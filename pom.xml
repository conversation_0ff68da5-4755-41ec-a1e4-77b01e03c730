<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.xiaomi.nr</groupId>
    <artifactId>promotion</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>promotion-api</module>
        <module>promotion-common</module>
        <module>promotion-service</module>
        <module>promotion-server</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.15</version>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>21</java.version>
        <junit.version>4.12</junit.version>
        <org.mi.thrift.version>0.9.2-mi-v1.4</org.mi.thrift.version>
        <org.springframework.version>5.3.29</org.springframework.version>
        <javax.el-api.version>3.0.0</javax.el-api.version>
        <org.apache.commons.pool2.version>2.11.1</org.apache.commons.pool2.version>
        <org.apache.commons.text.version>1.10.0</org.apache.commons.text.version>
        <org.apache.commons.logging.version>1.1.3</org.apache.commons.logging.version>
        <com.google.guava.version>25.1-jre</com.google.guava.version>
        <com.xiaomi.common.utils.version>2.8.15</com.xiaomi.common.utils.version>
        <org.xerial.snappy.version>1.1.2</org.xerial.snappy.version>
        <com.xiaomi.youpin.infra-rpc.version>2.0.1-CNZONE-SNAPSHOT</com.xiaomi.youpin.infra-rpc.version>
        <com.xiaomi.keycenter.util.version>3.5.3</com.xiaomi.keycenter.util.version>
        <mysql.connector.version>5.1.35</mysql.connector.version>
        <org.mybatis.version>3.5.3</org.mybatis.version>
        <org.mybatis.spring.boot.version>1.2.0</org.mybatis.spring.boot.version>
        <com.alibaba.druid.version>1.1.10</com.alibaba.druid.version>
        <com.alibaba.druid.spring.boot.starter.verion>1.1.10</com.alibaba.druid.spring.boot.starter.verion>
        <com.alibaba.spring.version>1.0.11</com.alibaba.spring.version>
        <com.alibaba.dubbo.nacos.version>1.2.1-mone-v1-SNAPSHOT</com.alibaba.dubbo.nacos.version>
        <com.alibaba.nacos.version>1.1.1</com.alibaba.nacos.version>
        <com.alibaba.nacos.client.version>2.1.2-XIAOMI</com.alibaba.nacos.client.version>
        <org.apache.httpcomponents.version>4.5.5</org.apache.httpcomponents.version>
        <org.apache.dubbo.version>2.7.12-mone-v23-SNAPSHOT</org.apache.dubbo.version>
        <com.xiaomi.nr.aop-utils.version>1.0-SNAPSHOT</com.xiaomi.nr.aop-utils.version>
        <com.xiaomi.retail.user-tag.version>1.0-SNAPSHOT</com.xiaomi.retail.user-tag.version>
        <com.xiaomi.goods.gis.version>1.0.46-SNAPSHOT</com.xiaomi.goods.gis.version>
        <com.xiaomi.nr.order.version>1.0.0-SNAPSHOT</com.xiaomi.nr.order.version>
        <com.xiaomi.nr.coupon.version>1.2.1-SNAPSHOT</com.xiaomi.nr.coupon.version>
        <com.xiaomi.nr.phoenix.version>2.3.1-SNAPSHOT</com.xiaomi.nr.phoenix.version>
        <com.xiaomi.nr.recycle.version>1.0-SNAPSHOT</com.xiaomi.nr.recycle.version>
        <com.xiaomi.mone.dubbo-docs-core.version>2.7.12-mone-v8-SNAPSHOT</com.xiaomi.mone.dubbo-docs-core.version>
        <com.xiaomi.nr.ecard.version>1.0.0-SNAPSHOT</com.xiaomi.nr.ecard.version>
        <com.xiaomi.youpin.aries.version>1.1.1-SNAPSHOT</com.xiaomi.youpin.aries.version>
        <rocketmq.spring.boot.starter.version>2.2.0-mdh2.2.2-RELEASE</rocketmq.spring.boot.starter.version>
        <com.xiaomi.mit.version>1.1.8</com.xiaomi.mit.version>
        <com.xiaomi.nr.md.promotion.admin.version>3.0.1-SNAPSHOT</com.xiaomi.nr.md.promotion.admin.version>
        <org.projectlombok.version>1.18.30</org.projectlombok.version>
        <com.xiaomi.nr.mro.policy.api.version>1.1.1-SNAPSHOT</com.xiaomi.nr.mro.policy.api.version>
        <com.xiaomi.nr.promotion.api.version>1.21.32-SNAPSHOT</com.xiaomi.nr.promotion.api.version>
        <car.iccc.user.permit.version>1.0.0</car.iccc.user.permit.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.xiaomi.nr</groupId>
                <artifactId>promotion-api</artifactId>
                <version>${com.xiaomi.nr.promotion.api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${org.projectlombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${org.springframework.version}</version>
            </dependency>

            <!--   Dubbo  RPC   -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${org.apache.dubbo.version}</version>
            </dependency>

            <!--     Thrift  RPC     -->
            <dependency>
                <groupId>org.mi</groupId>
                <artifactId>thrift</artifactId>
                <version>${org.mi.thrift.version}</version>
            </dependency>

            <!--   Dao Druid Mybatis  -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${com.alibaba.druid.spring.boot.starter.verion}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${org.mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${org.mybatis.spring.boot.version}</version>
            </dependency>

            <!--    公共依赖库       -->
            <dependency>
                <groupId>javax.el</groupId>
                <artifactId>javax.el-api</artifactId>
                <version>${javax.el-api.version}</version>
            </dependency>

            <dependency>
                <artifactId>youpin-infra-rpc</artifactId>
                <groupId>com.xiaomi.youpin</groupId>
                <version>${com.xiaomi.youpin.infra-rpc.version}</version>
            </dependency>

            <dependency>
                <artifactId>keycenter-agent-client</artifactId>
                <groupId>com.xiaomi</groupId>
                <version>${com.xiaomi.keycenter.util.version}</version>
            </dependency>

            <dependency>
                <artifactId>snappy-java</artifactId>
                <groupId>org.xerial.snappy</groupId>
                <version>${org.xerial.snappy.version}</version>
            </dependency>

            <dependency>
                <artifactId>xiaomi-common-utils</artifactId>
                <groupId>com.xiaomi</groupId>
                <version>${com.xiaomi.common.utils.version}</version>
            </dependency>

            <dependency>
                <artifactId>commons-logging</artifactId>
                <groupId>commons-logging</groupId>
                <version>${org.apache.commons.logging.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${org.apache.commons.pool2.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${org.apache.commons.text.version}</version>
            </dependency>

            <dependency>
                <artifactId>guava</artifactId>
                <groupId>com.google.guava</groupId>
                <version>${com.google.guava.version}</version>
            </dependency>

            <!-- 集成youpin log annotation utils -->
            <dependency>
                <groupId>com.xiaomi.nr</groupId>
                <artifactId>aop-utils</artifactId>
                <version>${com.xiaomi.nr.aop-utils.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xiaomi.youpin</groupId>
                <artifactId>log</artifactId>
                <version>${com.xiaomi.youpin.log.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xiaomi.youpin</groupId>
                <artifactId>annotation</artifactId>
                <version>${com.xiaomi.youpin.annotation.version}</version>
            </dependency>

            <!--     HttpClient    -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${org.apache.httpcomponents.version}</version>
            </dependency>

            <!--阿里巴巴nacos-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dubbo-registry-nacos</artifactId>
                <version>${com.alibaba.dubbo.nacos.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${com.alibaba.nacos.client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${com.alibaba.spring.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-spring-context</artifactId>
                <version>${com.alibaba.nacos.version}</version>
            </dependency>

            <!-- MQ服务 -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.spring.boot.starter.version}</version>
            </dependency>
            <!-- 人群服务 -->
            <dependency>
                <artifactId>retail-data-user-tag-service-rpc-api</artifactId>
                <groupId>com.xiaomi.retail</groupId>
                <version>${com.xiaomi.retail.user-tag.version}</version>
            </dependency>

            <!-- gis 服务      -->
            <dependency>
                <artifactId>gis-api</artifactId>
                <groupId>com.xiaomi.goods</groupId>
                <version>${com.xiaomi.goods.gis.version}</version>
            </dependency>

            <!--gms-->
            <dependency>
                <groupId>com.xiaomi.goods</groupId>
                <artifactId>gms-api</artifactId>
                <version>${com.xiaomi.goods.gms.version}</version>
            </dependency>

            <!--   order 服务     -->
            <dependency>
                <groupId>com.xiaomi.nr</groupId>
                <artifactId>order-api</artifactId>
                <version>${com.xiaomi.nr.order.version}</version>
            </dependency>

            <!-- coupon服务 -->
            <dependency>
                <artifactId>coupon-api</artifactId>
                <groupId>com.xiaomi.nr</groupId>
                <version>${com.xiaomi.nr.coupon.version}</version>
            </dependency>

            <!-- miapi -->
            <dependency>
                <groupId>com.xiaomi.mone</groupId>
                <artifactId>dubbo-docs-core</artifactId>
                <version>${com.xiaomi.mone.dubbo-docs-core.version}</version>
            </dependency>

            <!-- phoenix服务 -->
            <dependency>
                <groupId>com.xiaomi.nr</groupId>
                <artifactId>phoenix-api</artifactId>
                <version>${com.xiaomi.nr.phoenix.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 换新中台  -->
            <dependency>
                <groupId>com.xiaomi.nr</groupId>
                <artifactId>recycle-api</artifactId>
                <version>${com.xiaomi.nr.recycle.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--  新后台admin  -->
            <dependency>
                <groupId>com.xiaomi.nr.md.promotion.admin</groupId>
                <artifactId>md-promotion-admin-api</artifactId>
                <version>${com.xiaomi.nr.md.promotion.admin.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- ecard服务 -->
            <dependency>
                <artifactId>ecard-api</artifactId>
                <groupId>com.xiaomi.nr</groupId>
                <version>${com.xiaomi.nr.ecard.version}</version>
            </dependency>

            <!-- aries服务 -->
            <dependency>
                <artifactId>aries-client</artifactId>
                <groupId>com.xiaomi.youpin</groupId>
                <version>${com.xiaomi.youpin.aries.version}</version>
            </dependency>
            <!--   mit     -->
            <dependency>
                <groupId>com.xiaomi.mit</groupId>
                <artifactId>mit-starter</artifactId>
                <version>${com.xiaomi.mit.version}</version>
            </dependency>
            <!--  维保策略系统   -->
            <dependency>
                <groupId>com.xiaomi.nr</groupId>
                <artifactId>mro-policy-api</artifactId>
                <version>${com.xiaomi.nr.mro.policy.api.version}</version>
            </dependency>

            <!--  车主标签系统   -->
            <dependency>
                <groupId>com.mi.car.iccc</groupId>
                <artifactId>iccc-user-permit-common</artifactId>
                <version>${car.iccc.user.permit.version}</version>
            </dependency>

            <!--  iauth校验   -->
            <dependency>
                <groupId>com.xiaomi</groupId>
                <artifactId>xiaomi-iauth-java-sdk</artifactId>
                <version>2.5.9</version>
            </dependency>

            <!-- hera指标数据上报  -->
            <dependency>
                <groupId>run.mone</groupId>
                <artifactId>hera-metrics-sdk</artifactId>
                <version>1.0.7-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <compilerVersion>${java.version}</compilerVersion>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <fork>true</fork>
                    <verbose>true</verbose>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--扫描业务代码里面的synchronized关键字,synchronized会导致协程不能被卸载 -->
<!--            <plugin>-->
<!--                <groupId>com.xiaomi.mone</groupId>-->
<!--                <artifactId>jdk-feature-maven-plugin</artifactId>-->
<!--                <version>1.0.0-turbo-SNAPSHOT</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <phase>validate</phase>-->
<!--                        <goals>-->
<!--                            <goal>jdk</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.12</version>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>
                        @{argLine}
                        --add-opens=java.base/java.time=ALL-UNNAMED
                        --add-opens=java.base/java.lang=ALL-UNNAMED
                        --add-opens=java.base/java.util=ALL-UNNAMED
                        --add-opens=java.base/java.math=ALL-UNNAMED
                        --add-opens=java.base/sun.reflect=ALL-UNNAMED
                        --add-opens=com.alibaba/spring.context=ALL-UNNAMED
                        --add-exports=java.base/sun.reflect.annotation=ALL-UNNAMED
                        --add-exports=java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
                    </argLine>
                    <includes>
                        <include>**/xiaomi/nr/promotion/v2/**/*.java</include>
                    </includes>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-release-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-snapshot-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual</url>
        </snapshotRepository>
    </distributionManagement>
</project>
