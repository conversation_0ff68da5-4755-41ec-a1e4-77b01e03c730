package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/3/19
 */
@Data
@ToString
public class PolicyNewGroup implements Serializable {
    private static final long serialVersionUID = -7627574619821054559L;

    /**
     * sku
     */
    private Long sku = 0L;
    
    /**
     * ssu id
     */
    private Long ssuId = 0L;

    /**
     * 市场价
     */
    private Long marketPrice = 0L;

    /**
     * 加价购金额，赠品为0
     */
    private Long cartPrice = 0L;

    /**
     * 赠品是否有货，1-是，2-否
     */
    private Integer isInStock = 1;

    /**
     * 是否有活动库存
     */
    private Integer actNumLimit;

    /**
     * 赠品基数
     */
    private Long baseNum = 1L;

    /**
     * 从品描述
     */
    private String descRule;
}
