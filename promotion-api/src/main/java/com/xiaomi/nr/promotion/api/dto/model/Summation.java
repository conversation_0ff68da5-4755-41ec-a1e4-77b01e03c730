package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 价格计算
 *
 * <AUTHOR>
 * @date 2021/3/18
 */
@Data
@ToString
public class Summation implements Serializable {
    private static final long serialVersionUID = 6921231721974000530L;
    /**
     * 商品总数量
     */
    private Long totalCount = 0L;

    /**
     * 商品总价格
     */
    private Long totalAmount = 0L;

    /**
     * 商品总减免金额
     */
    private Long reduceAmount = 0L;

    /**
     * 总减免明细,act => price,coupon => price
     */
    private Map<String, Long> reduceList = new HashMap<>();

    /**
     * 运费减免明细
     */
    private Map<String, ExpressDetail> reduceExpressDetail = new HashMap<>();

    /**
     * 总计商品需付款金额
     */
    private Long finalAmount = 0L;

    /**
     * 红包金额
     */
    private Long hasRedPacket = 0L;

    /**
     * 可用积分数量
     */
    private Long validUsePointCount = 0L;
    /**
     * 用户可用积分总数量
     */
    private Long canUsePointCount;
    /**
     * 用户不可用积分说明文案
     */
    private String cannotUsePointMsg;
}
