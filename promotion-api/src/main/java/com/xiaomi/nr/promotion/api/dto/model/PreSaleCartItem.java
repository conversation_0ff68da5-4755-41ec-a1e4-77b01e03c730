package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;


/**
 * 预售商品信息
 */
@Data
public class PreSaleCartItem implements Serializable {
    private static final long serialVersionUID = -2093708569005312308L;

    /**
     * 预售模式
     */
    private String saleMode;

    /**
     * 商品类型: 单品（“sku"）, 套装（"packageId"）
     */
    private String goodLevel;

    /**
     * 预售价
     */
    private Long preSalePrice;

    /**
     * 划线价
     */
    private Long marketPrice;

    /**
     * 套装组<组id，划线价>
     */
    private Map<Integer, Long> groups;
}
