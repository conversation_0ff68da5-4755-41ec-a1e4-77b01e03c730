package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import com.xiaomi.nr.promotion.api.dto.model.InstallmentGoodItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 获取有效的分期活动参数
 *
 * <AUTHOR>
 * @data 2024/09/30
 */
@Data
public class GetValidInstallmentRequest implements Serializable {
    private static final long serialVersionUID = 6504906025570925907L;

    @ApiDocClassDefine(value = "installmentType", required = false, description = "分期类型")
    private Integer installmentType;

    @ApiDocClassDefine(value = "orgCode", required = false, description = "门店ID")
    private String orgCode;

    @ApiDocClassDefine(value = "channelId", required = false, description = "渠道ID")
    private Integer channelId;

    @ApiDocClassDefine(value = "startTime", required = false, description = "活动开始时间（秒）")
    private Long startTime;

    @ApiDocClassDefine(value = "endTime", required = false, description = "活动结束时间（秒）")
    private Long endTime;

    @ApiDocClassDefine(value = "goodsList", required = true, description = "商品列表")
    private List<InstallmentGoodItem> goodsList;

    @ApiDocClassDefine(value = "isPerfectMatchGoods", required = true, description = "是否完全匹配商品")
    private Boolean isPerfectMatchGoods = true;

    @ApiDocClassDefine(value = "amount", required = true, description = "金额（分），用于判断金额范围")
    private Long amount;

    @ApiDocClassDefine(value = "isCheckAmountScope", required = true, description = "是否校验金额范围")
    private Boolean isCheckAmountScope = true;
}
