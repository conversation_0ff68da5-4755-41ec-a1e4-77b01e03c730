package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import com.xiaomi.nr.promotion.api.dto.model.InstallmentGoodItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 根据条件校验是否能参与分期活动参数
 *
 * <AUTHOR>
 * @data 2024/09/30
 */
@Data
public class InstallmentConditionCheckRequest implements Serializable {
    private static final long serialVersionUID = 2426640140641798471L;

    @ApiDocClassDefine(value = "actId", required = false, description = "分期活动ID")
    private Long actId;

    @ApiDocClassDefine(value = "installmentType", required = false, description = "分期类型")
    private Integer installmentType;

    @ApiDocClassDefine(value = "orgCode", required = false, description = "门店ID")
    private String orgCode;

    @ApiDocClassDefine(value = "channelId", required = false, description = "渠道ID")
    private Integer channelId;

    @ApiDocClassDefine(value = "goodsList", required = true, description = "商品列表")
    private List<InstallmentGoodItem> goodsList;

    @ApiDocClassDefine(value = "isPerfectMatchGoods", required = true, description = "是否完全匹配商品")
    private Boolean isPerfectMatchGoods;

    @ApiDocClassDefine(value = "amount", required = true, description = "金额（分），用于判断金额范围")
    private Long amount;

    @ApiDocClassDefine(value = "installmentTime", required = true, description = "分期期数")
    private Integer installmentTime;
}
