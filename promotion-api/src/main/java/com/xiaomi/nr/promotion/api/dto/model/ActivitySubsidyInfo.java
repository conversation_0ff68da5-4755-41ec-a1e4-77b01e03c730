package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 活动信息
 *
 * <AUTHOR>
 * @date 2021/6/11
 */
@Data
public class ActivitySubsidyInfo implements Serializable {
    private static final long serialVersionUID = 1535203439016987000L;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 活动类型.  8: 加价购， 9:赠品，  20: 直降
     */
    private Integer typeId;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 活动渠道
     */
    private List<Integer> channel;

    /**
     * 用户范围 UserScopeEnum
     */
    private String userScope;

    /**
     * 贴息方
     */
    private String interestBearer;
    /**
     * 预算
     */
    private String budgetApplyNo;

    /**
     * 预算行
     */
    private Long lineNum;

}
