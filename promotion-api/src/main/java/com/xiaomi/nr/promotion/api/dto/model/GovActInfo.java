package com.xiaomi.nr.promotion.api.dto.model;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import com.xiaomi.nr.promotion.api.dto.enums.FetchTypeEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class GovActInfo implements Serializable {

    private static final long serialVersionUID = 4597832324483363209L;

    /**
     * 活动id
     */
    @ApiDocClassDefine(value = "id", description = "活动id")
    private Long id;

    /**
     * 国补模式,枚举 com.xiaomi.nr.promotion.api.dto.enums.SubsidyModeEnum
     */
    @ApiDocClassDefine(value = "subsidyMode", description = "国补模式")
    private Integer subsidyMode;

    /**
     * 活动会场URL，极简模式存在
     */
    @ApiDocClassDefine(value = "activityUrl", description = "活动会场url")
    private String activityUrl;

    /**
     * 补贴上报城市
     */
    @ApiDocClassDefine(value = "reportCity", description = "补贴上报城市")
    private Integer reportCity;

    /**
     * 国补优惠比例：15%，不存在则返回0
     */
    @ApiDocClassDefine(value = "reduceDiscount", description = "优惠比例")
    private Long reduceDiscount;

    /**
     * 最大优惠金额：单位分，不存在则返回0
     */
    @ApiDocClassDefine(value = "maxReduce", description = "最大优惠金额")
    private Long maxReduce;

    /**
     * 品类编码
     */
    @ApiDocClassDefine(value = "cateCode", description = "品类编码")
    private String cateCode;

    /**
     * 领取方式{@link FetchTypeEnum}
     */
    @ApiDocClassDefine(value = "fetchType", description = "领取方式")
    private Integer fetchType;

    /**
     * 使用指南
     */
    @ApiDocClassDefine(value = "usageGuide", description = "使用指南")
    private String usageGuide;

    /**
     * 使用指南图片URL
     */
    @ApiDocClassDefine(value = "usageGuideImgUrl", description = "使用指南图片URL")
    private String usageGuideImgUrl;

}
