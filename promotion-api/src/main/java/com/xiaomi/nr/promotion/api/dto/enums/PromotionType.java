package com.xiaomi.nr.promotion.api.dto.enums;

import com.google.common.collect.ImmutableMap;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by wa<PERSON><PERSON>yi on 2022/3/31
 */
public enum PromotionType {

    /**
     * 未知活动
     */
    UNKNOWN(-1, "未知"),

    /**
     * 现金券
     */
    COUPON_CASH(1, "现金券"),
    /**
     * 满减活动
     */
    REDUCE(5, "满减"),
    /**
     * "折扣活动"
     */
    DISCOUNT(7, "折扣"),
    /**
     * "加价购活动"
     */
    BARGAIN(8, "加价购"),
    /**
     * "赠品活动"
     */
    GIFT(9, "赠完即止"),
    /**
     * "包邮活动"
     */
    POST_FREE(10, "包邮"),
    /**
     * "直降活动"
     */
    ONSALE(20, "直降"),
    /**
     * "买赠活动"
     */
    BUY_GIFT(21, "买赠"),
    /**
     * "门店价"
     */
    STORE_PRICE(22, "门店价"),
    /**
     * "合约机"
     */
    CONTRACT_PHONE(23, "合约机"),
    /**
     * "下单立减"
     */
    BUY_REDUCE(27, "下单立减"),
    /**
     * "选装基金"
     */
    RANGE_REDUCE(28, "选装基金"),
    /**
     * "置换补贴"
     */
    EXCHANGE_SUBSIDY(29, "置换补贴"),
    /**
     * 订单立减
     */
    ORDER_REDUCE(31, "订单立减"),
    /**
     * "24年北京大家电以旧换新-购新补贴"
     */
    NEW_PURCHASE_SUBSIDY(40,"购新补贴"),
    /**
     * "24年北京大家电以旧换新-换新补贴"
     */
    UPGRADE_PURCHASE_SUBSIDY(41,"换新补贴"),
    /**
     * 团购价格折扣
     */
    B2T_VIP_DISCOUNT(70, "团购价格折扣"),
    /**
     * 阶梯价
     */
    B2T_STEP_PRICE(71, "阶梯价"),
    /**
     * 提货底价
     */
    B2T_CHANNEL_PRICE(72, "提货底价"),
    /**
     * 美团红包
     */
    MT_RED_PACKET(800, "美团红包"),

    /**
     * 优惠券
     */
    COUPON(999, "优惠券"),

    /**
     * 汽车售后工时去重
     */
    UN_DUPLICATED_WORK_HOUR(2001, "汽车售后工时去重"),

    /**
     * 积分
     */
    POINT(2002, "积分"),

    ;

    /**
     * 活动类型值
     */
    private final int value;


    /**
     * 活动类型的名称
     */
    private final String name;


    PromotionType(int value, String name) {
        this.value = value;
        this.name = name;
    }


    /**
     * 京东订单来源
     */
    private static final int ORDER_FROM_JD = 7;

    /**
     * 美团订单来源
     */
    private static final int ORDER_FROM_MT = 6;


    private static final Map<Integer, Map<String, PromotionType>> EXTERNAL_MATCH_MAP = ImmutableMap.of(
            ORDER_FROM_JD, new ImmutableMap.Builder<String, PromotionType>()
                    .put("JD-O-3", COUPON)
                    .put("20", ONSALE)
                    .build(),
            ORDER_FROM_MT, new ImmutableMap.Builder<String, PromotionType>()
                    .put("9", MT_RED_PACKET)
                    .put("117", COUPON)
                    .build()

    );

    public int getValue() {
        return value;
    }

    /**
     * 根据订单渠道和渠道匹配外部优惠券
     *
     * @param orderFrom
     * @param externalCouponCode
     * @return
     */
    public static int matchExternalPromotion(Integer orderFrom, String externalCouponCode) {
        if (orderFrom == null || externalCouponCode == null) {
            return UNKNOWN.getValue();
        }
        if (EXTERNAL_MATCH_MAP.containsKey(orderFrom)) {
            Map<String, PromotionType> couponTypeMap = EXTERNAL_MATCH_MAP.get(orderFrom);
            return couponTypeMap.getOrDefault(externalCouponCode, UNKNOWN).getValue();
        } else {
            return UNKNOWN.getValue();
        }

    }

    public static List<Integer> getAllPromotionCode(){
        List<Integer> res=new ArrayList<>();
        for (PromotionType type:PromotionType.values()){
            res.add(type.getValue());
        }
        return res;
    }

}
