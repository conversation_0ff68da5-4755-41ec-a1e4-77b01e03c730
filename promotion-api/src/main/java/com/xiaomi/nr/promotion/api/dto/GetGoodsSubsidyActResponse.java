package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import com.xiaomi.nr.promotion.api.dto.model.PromotionIndexDto;
import com.xiaomi.nr.promotion.api.dto.model.PurchaseActivityInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： Pancras
 * @date： 2024/9/18
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Data
public class GetGoodsSubsidyActResponse implements Serializable {
    
    private static final long serialVersionUID = -4947593503928097188L;
    
    @ApiDocClassDefine(value = "InvalidCode", required = true, description = "0 可用,1 门店不可用,2 商品不可用,3 活动已过期, 4 收货地址不可用，存在收货地址为当前省的活动 5 收货地址不可用，不存在收货地址为当前省的活动")
    private Integer invalidCode;
    
    @ApiDocClassDefine(value = "activityInfo", required = true, description = "活动信息")
    private PurchaseActivityInfo activityInfo;
    
    /**
     * 促销索引。key:ssuId, value: 促销索引列表
     */
    @ApiDocClassDefine(value = "promotionIndexMap", required = true, description = "促销索引.key:ssuId, value: 促销索引列表")
    private Map<Long, List<PromotionIndexDto>> promotionIndexMap;
    
    /**
     * 促销信息。key: promotionId, value: 促销活动信息
     */
    @ApiDocClassDefine(value = "promotionInfoMap", required = true, description = "促销信息.key: promotionId, value: 促销活动信息")
    private Map<Long, PurchaseActivityInfo> promotionInfoMap;

    /**
     * sku是否有可参加的国补活动
     */
    @ApiDocClassDefine(value = "skuCanJoin", required = true, description = "sku是否有可参加的国补活动")
    private Integer skuCanJoin;

    /**
     * 不可用的活动配置的标签
     */
    @ApiDocClassDefine(value = "activityInvalidTag", required = true, description = "不可用的活动配置的标签")
    private String activityInvalidTag;
}
