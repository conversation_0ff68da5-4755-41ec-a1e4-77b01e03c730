package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 商品数量限制规则
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Data
@ToString
public class NumLimitRule implements Serializable {
    private static final long serialVersionUID = 955862614053091604L;

    /**
     * 每单限购数
     */

    private Long orderLimit = 0L;

    /**
     * 每人限购数
     */
    private Long personLimit = 0L;

    /**
     * 每天每个门店限量
     */
    private Long dayLimitOne = 0L;

    /**
     * 每天全部门店限量
     */
    private Long dayLimitAll = 0L;

    /**
     * 活动期间每个门店限量
     */
    private Long activityLimitOne = 0L;

    /**
     * 活动期间全部门店限量
     */
    private Long activityLimitAll = 0L;

    /**
     * 每人每店限购数
     */
    private Long personLimitOne = 0L;
}
