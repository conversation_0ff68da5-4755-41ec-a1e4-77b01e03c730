package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class OrderExtention implements Serializable {
    private static final long serialVersionUID = -2267719205383025550L;
    /**
     * 礼品卡列表
     */
    private List<Ecard> ecardList = new ArrayList<>();

    /**
     * 三方优惠资格码
     */
    private Map<String, QualifyDetailInfo> qualifyDetailInfoMap;

}
