package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.*;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 结算结果响应：结算页结算返回结果
 *
 * <AUTHOR>
 */
@Data
@ToString
public class CheckoutPromotionV2Response implements Serializable {

    private static final long serialVersionUID = -8891912768967440812L;

    /**
     * 商品列表
     */
    private List<CartItem> cartList = new ArrayList<>();

    /**
     * 活动信息
     */
    private List<PromotionInfo> promotions = new ArrayList<>();

    /**
     * 价格计算
     */
    private Summation summation;

    /**
     * 礼品卡信息
     */
    private List<Ecard> ecardList;

    /**
     * 邮费减免详情
     */
    private Map<String, String> exInfo = new HashMap<>();

    /**
     * 券列表
     */
    private List<Coupon> couponList;

    /**
     * 是否有北京优惠券
     */
    private Boolean hasBeijingcoupon = false;
}
