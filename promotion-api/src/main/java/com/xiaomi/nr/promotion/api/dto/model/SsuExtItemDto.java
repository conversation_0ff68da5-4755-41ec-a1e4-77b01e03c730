package com.xiaomi.nr.promotion.api.dto.model;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 2024/3/2
 */
@Data
public class SsuExtItemDto implements Serializable {
    
    private static final long serialVersionUID = 963431395660721022L;
    
    @ApiDocClassDefine(value = "count", description = "数量")
    private Integer count;

    @ApiDocClassDefine(value = "subBizType", description = "类型")
    private Integer subBizType;
}
