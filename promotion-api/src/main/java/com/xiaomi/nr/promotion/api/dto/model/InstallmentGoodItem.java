package com.xiaomi.nr.promotion.api.dto.model;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * 分期活动商品item
 */
@Data
public class InstallmentGoodItem implements Serializable {

    private static final long serialVersionUID = 602264134755361688L;

    @ApiDocClassDefine(value = "id", required = true, description = "sku或套装ID")
    private Long id;

    @ApiDocClassDefine(value = "type", required = true, description = "商品类型")
    private Integer type;
}
