package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 优先级策略
 *
 * <AUTHOR>
 * @date 2021/3/19
 */
@Data
@ToString
public class PolicyNewLevel implements Serializable {
    private static final long serialVersionUID = 5874584894997685748L;

    /**
     * 组信息
     */
    private List<PolicyNewFillGoodsGroup> includedGoodsGroup = new ArrayList<>();

    /**
     * 规则
     */
    private PolicyNewRule rule = new PolicyNewRule();
}
