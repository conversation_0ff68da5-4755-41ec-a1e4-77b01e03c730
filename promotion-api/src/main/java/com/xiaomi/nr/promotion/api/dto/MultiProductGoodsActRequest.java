package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import com.xiaomi.nr.promotion.api.dto.model.MultiGoodItem;
import com.xiaomi.nr.promotion.api.dto.model.carshop.Region;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MultiProductGoodsActRequest implements Serializable {

    private static final long serialVersionUID = -7339619518406231839L;

    @ApiDocClassDefine(value = "goodsList", required = true, description = "商品请求列表")
    private List<MultiGoodItem> goodsList;

    /**
     * 1、传入空List，代表查询全量优惠类型 2、传入指定promotionType，查询指定优惠类型 3、传入null, 兼容第一期需求，只返回选装基金
     */
    @ApiDocClassDefine(value = "promotionTypeList", required = true, description = "促销类型请求列表")
    private List<Integer> promotionTypeList;

    /**
     * 渠道，com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum
     */
    @ApiDocClassDefine(value = "channel", required = true, description = "渠道")
    private Integer channel;

    /**
     * 地址信息
     */
    @ApiDocClassDefine(value = "region", required = false, description = "地址信息")
    private Region region;

    /**
     * vip级别
     */
    @ApiDocClassDefine(value = "vipLevel", required = false, description = "vip级别")
    private Integer vipLevel;

}
