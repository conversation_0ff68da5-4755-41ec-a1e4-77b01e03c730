package com.xiaomi.nr.promotion.api.dto.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by xqz on 2025/1/7
 * 用户类型枚举
 */
@Getter
public enum UserScopeEnum {

    ALL(0,"all","全部用户"),

    RE_PURCHASE(1, "re_purchase","复购用户"),

    MI_CAR_REPLACEMENT(2, "mi_car_replacement","本品置换"),

    OTHER_CAR_REPLACEMENT(3, "other_car_replacement","非本品置换")

    ;

    private final int code;
    private final String key;
    private final String desc;

    UserScopeEnum(int code, String key, String desc) {
        this.code = code;
        this.key = key;
        this.desc = desc;
    }

    public static UserScopeEnum findByValue(int value) {
        UserScopeEnum[] values = UserScopeEnum.values();
        for (UserScopeEnum actTypeEnum : values) {
            if (value == actTypeEnum.code) {
                return actTypeEnum;
            }
        }
        return null;
    }

    public static String findKeyByValue(int value) {
        UserScopeEnum[] values = UserScopeEnum.values();
        for (UserScopeEnum actTypeEnum : values) {
            if (value == actTypeEnum.code) {
                return actTypeEnum.getKey();
            }
        }
        return "";
    }
    public static Integer findValueByKey(String key) {
        UserScopeEnum[] values = UserScopeEnum.values();
        for (UserScopeEnum actTypeEnum : values) {
            if (StringUtils.equals(key,actTypeEnum.key)) {
                return actTypeEnum.getCode();
            }
        }
        return null;
    }
}
