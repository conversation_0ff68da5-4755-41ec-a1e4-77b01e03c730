package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 活动详情
 *
 * <AUTHOR>
 * @date 2022/12/5
 */
@Data
public class ProductActJoinInfo implements Serializable {

    private static final long serialVersionUID = -5081376963237582284L;

    /**
     * 活动ID
     */
    private Long promotionId;

    /**
     * 类型
     */
    private Integer promotionType;

    /**
     * 名称
     */
    private String promotionName;

    /**
     * 活动开始时间(秒）
     */
    private Integer joinTimes;

    /**
     * 优惠次数限制
     */
    private Integer userLimitNum;

    /**
     * 有效订单列表
     */
    private List<JoinedOrderInfoDto> joinedOrderInfoList;
}
