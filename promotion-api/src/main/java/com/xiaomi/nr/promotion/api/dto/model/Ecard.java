package com.xiaomi.nr.promotion.api.dto.model;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 礼品卡信息
 *
 * <AUTHOR>
 * @date 2021/3/19
 */
@Data
@ToString
public class Ecard implements Serializable {
    private static final long serialVersionUID = -6536476733284297200L;

    /**
     * 礼品卡id
     */
    private Long eCardId = 0L;

    /**
     * 原来的余额
     */
    private Long balanceOld = 0L;

    /**
     * 使用后的余额
     */
    private Long balanceNew = 0L;


    private Long income = 0L;

    /**
     * type id, e.g. 9
     */
    private Long typeId = 0L;
    /**
     * e.g. "ecard"
     */
    @SerializedName("type_code")
    private String typeCode = "";

    /**
     * 购物车中可以使用礼品卡的商品总金额，参加完优惠以后的（名字是玉增想的）＿
     */
    private Long ecardGoodsAmount = 0L;

    /**
     * 单卡在各小单扣费详情列表
     */
    private List<EcardConsumeItem> consumeList;

    /**
     * 是否开发票
     */
    private Boolean invoice;
}
