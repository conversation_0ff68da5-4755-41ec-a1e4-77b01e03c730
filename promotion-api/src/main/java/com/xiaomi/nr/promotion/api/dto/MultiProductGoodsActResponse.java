package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.ProductPriceInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionIndexDto;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfoDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class MultiProductGoodsActResponse implements Serializable {

    private static final long serialVersionUID = 6195637594170429587L;
    /**
     * 促销索引。key:ssuId, value: 促销索引列表
     */
    private Map<Long, List<PromotionIndexDto>> promotionIndexMap;

    /**
     * 促销信息。key: promotionId, value: 促销活动信息
     */
    private Map<Long, PromotionInfoDTO> promotionInfoMap;

    /**
     * 促销商品价格。 key:ssuId, value: 促销价格信息
     */
    private Map<Long, ProductPriceInfo>  promotionPriceDTOMap;

}
