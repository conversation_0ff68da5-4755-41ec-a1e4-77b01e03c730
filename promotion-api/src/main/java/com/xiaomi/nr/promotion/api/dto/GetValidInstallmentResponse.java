package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import com.xiaomi.nr.promotion.api.dto.model.InstallmentDetail;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 获取有效的分期活动返回
 *
 * <AUTHOR>
 * @data 2024/09/30
 */
@Data
public class GetValidInstallmentResponse implements Serializable {
    private static final long serialVersionUID = 6504906025570925101L;

    @ApiDocClassDefine(value = "installmentList", required = true, description = "分期活动信息列表（无序）")
    private List<InstallmentDetail> installmentList;
}
