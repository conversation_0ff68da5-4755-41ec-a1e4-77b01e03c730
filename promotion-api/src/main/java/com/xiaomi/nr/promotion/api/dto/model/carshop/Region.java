package com.xiaomi.nr.promotion.api.dto.model.carshop;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/8/12
 */
@Data
public class Region implements Serializable {

    private static final long serialVersionUID = -8654795554791602104L;

    @ApiDocClassDefine(
            value = "province",
            required = true,
            description = "省的编号",
            defaultValue = "0",
            example = "2"
    )
    private int province;
    @ApiDocClassDefine(
            value = "city",
            required = true,
            description = "城市的编号",
            defaultValue = "0",
            example = "36"
    )
    private int city;
    @ApiDocClassDefine(
            value = "district",
            required = true,
            description = "街道的编号",
            defaultValue = "0",
            example = "378"
    )
    private int district;
    @ApiDocClassDefine(
            value = "area",
            required = true,
            description = "区域的编号",
            defaultValue = "0",
            example = "213142"
    )
    private int area;
}
