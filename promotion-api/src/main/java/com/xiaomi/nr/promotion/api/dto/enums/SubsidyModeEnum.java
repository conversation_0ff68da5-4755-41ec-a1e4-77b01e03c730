package com.xiaomi.nr.promotion.api.dto.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/4
 * @Description 国补模式
 */
@Getter
@AllArgsConstructor
public enum SubsidyModeEnum {
    BEIJING_MODE(1, "政府模式",1),
    GUANGDONG_MODE(2, "银商模式" ,2),
    JIJIAN_MODE(3, "极简模式",3 ),
    ;


    public final int value;
    public final String desc;
    public final int sort;

    public static int getSortByValue(int value) {
        for (SubsidyModeEnum subsidyModeEnum: SubsidyModeEnum.values()) {
            if (subsidyModeEnum.getValue() == value) {
                return subsidyModeEnum.getSort();
            }
        }
        return Integer.MAX_VALUE;
    }

}
