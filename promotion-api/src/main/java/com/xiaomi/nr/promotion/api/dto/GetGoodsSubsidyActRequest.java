package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import com.xiaomi.nr.promotion.api.dto.model.carshop.Region;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： Pancras
 * @date： 2024/9/18
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Data
public class GetGoodsSubsidyActRequest implements Serializable {
    
    private static final long serialVersionUID = -364849032005755240L;

    @ApiDocClassDefine(value = "channel", required = true, description = "渠道，默认为2")
    private Integer channel;

    @ApiDocClassDefine(value = "region", required = true, description = "region")
    private Region region;
    
    @ApiDocClassDefine(value = "orgCode", required = true, description = "门店")
    private String orgCode;
    
    @ApiDocClassDefine(value = "skuList", required = true, description = "sku列表")
    private List<Long> skuList;

    /**
     * 调用三方促销查询资格时需要传递，线下不需要传
     */
    @ApiDocClassDefine(value = "personalInfo", description = "四要素加密信息")
    private String personalInfo;
}
