package com.xiaomi.nr.promotion.api.dto.enums;

import lombok.Getter;

public enum CrowdEnum {

    ALL("all", "全量人群"),
    STUDENT("student", "学生"),
    F_MEMBER("f_member", "F会员");

    /**
     * 人群编码
     */
    @Getter
    private final String code;

    /**
     * 促销人群名称
     */
    @Getter
    private final String name;

    CrowdEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
