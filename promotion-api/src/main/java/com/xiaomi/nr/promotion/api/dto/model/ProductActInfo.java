package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 活动信息
 *
 * <AUTHOR>
 * @date 2021/6/11
 */
@Data
public class ProductActInfo implements Serializable {
    private static final long serialVersionUID = 1535203439016987000L;

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动类型.  8: 加价购， 9:赠品，  20: 直降
     */
    private Integer type;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动开始时间（秒）
     */
    private Long unixStartTime;

    /**
     * 活动结束时间
     */
    private Long unixEndTime;

    /**
     * 政策列表
     */
    private List<Policy> policys;

    /**
     * 渠道列表
     */
    private List<Integer> channels;

    /**
     * 门店区域范围
     */
    private List<String> selectOrgList;

    /**
     * 端列表
     */
    private List<String> selectClientList;

    /**
     * 直降信息. 当type为直降时候存在
     */
    private ProductActOnsaleGoods onsaleGoods;

    /**
     * 赠品信息. 当type为赠品时候存在
     */
    @Deprecated
    private ProductActGoods giftGoods;

    /**
     * 赠品信息. 当type为赠品时候存在
     */
    private List<ProductActGoods> giftGoodsV2;

    /**
     * 加价购信息. 当type为加价购时候存在
     */
    private ProductActGoods bargainGoods;
}
