package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.StoreActPriceInfo;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;

/**
 * 商品门店下商品活动价(直降 ｜门店价）响应
 *
 * <AUTHOR>
 * @date 2021/11/11
 */
@Data
@ToString
public class GetStoreGoodsActPriceResponse implements Serializable {
    private static final long serialVersionUID = -5473730261181838397L;

    /**
     * 活动价
     * key: sku or packageId.  val: 活动优惠价(分）
     */
    private Map<String, StoreActPriceInfo> actPriceMap;
}
