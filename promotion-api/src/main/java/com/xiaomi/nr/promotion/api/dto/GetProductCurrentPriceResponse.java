package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 获取商品的当前价格返回值
 *
 * <AUTHOR>
 */
@Data
public class GetProductCurrentPriceResponse implements Serializable {
    private static final long serialVersionUID = 3985537602386438441L;

    /**
     * 购物车列表
     */
    private List<CartItem> cartList;

    /**
     * 活动信息
     */
    private List<PromotionInfo> promotions;

    /**
     * 扩展信息
     */
    private Map<String, String> exInfo;
}
