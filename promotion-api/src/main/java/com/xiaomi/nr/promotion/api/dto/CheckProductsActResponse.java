package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.CheckGoodsItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 加购时校验商品参加的活动是否有效返回结果
 *
 * <AUTHOR>
 */
@Data
public class CheckProductsActResponse implements Serializable {
    private static final long serialVersionUID = 7734563020565115475L;
    /**
     * 不合法商品列表, extend中为invalid reason
     */
    private List<CheckGoodsItem> invalidGoodsList;
}
