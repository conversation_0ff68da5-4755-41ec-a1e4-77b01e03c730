package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import com.xiaomi.nr.promotion.api.dto.model.carshop.Region;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GetGovernmentSubsidyRequest implements Serializable {

    private static final long serialVersionUID = 1078235399818301851L;

    /**
     * 渠道，com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum
     */
    @ApiDocClassDefine(value = "channel", required = true, description = "渠道")
    private Integer channel;

    @ApiDocClassDefine(value = "id", required = true, description = "商品id")
    private Long id;


    @ApiDocClassDefine(value = "region", required = true, description = "地址")
    private Region region;
}
