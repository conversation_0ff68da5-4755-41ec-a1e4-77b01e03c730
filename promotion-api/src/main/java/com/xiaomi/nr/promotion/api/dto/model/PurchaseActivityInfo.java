package com.xiaomi.nr.promotion.api.dto.model;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import com.xiaomi.nr.promotion.api.dto.model.carshop.Region;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： Pancras
 * @date： 2024/9/18
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Data
public class PurchaseActivityInfo implements Serializable {
    
    private static final long serialVersionUID = -3233402946056926752L;
    
    
    @ApiDocClassDefine(value = "actId", required = true, description = "活动Id")
    private Long actId;
    
    @ApiDocClassDefine(value = "actName", required = true, description = "活动名称")
    private String actName;
    
    @ApiDocClassDefine(value = "actType", required = true, description = "活动类型")
    private Integer actType;
    
    @ApiDocClassDefine(value = "startTime", required = true, description = "活动开始时间")
    private Long startTime;
    
    @ApiDocClassDefine(value = "endTime", required = true, description = "活动结束时间")
    private Long endTime;
    
    @ApiDocClassDefine(value = "reduceDiscount", required = true, description = "折扣:90=9折")
    private Long reduceDiscount;
    
    @ApiDocClassDefine(value = "maxReduce", required = true, description = "最大优惠额度:单位：分")
    private Long maxReduce;

    @ApiDocClassDefine(value = "cateCode", required = true, description = "品类编码")
    private String cateCode;

    @ApiDocClassDefine(value = "cateLevel", required = true, description = "品类能效")
    private String cateLevel;

    @ApiDocClassDefine(value = "activityRegion", required = true, description = "地区列表，省级")
    private List<Region> activityRegion;

    @ApiDocClassDefine(value = "skuList", required = true, description = "sku列表")
    private List<Long> skuList;

    @ApiDocClassDefine(value = "groupMap", required = true, description = "groupMap")
    private Map<Long, String> groupMap;

    @ApiDocClassDefine(value = "orgList", required = true, description = "门店列表")
    private List<String> orgList;

    @ApiDocClassDefine(value = "channel", required = true, description = "渠道")
    private List<Integer> channel;

    @ApiDocClassDefine(value = "paymentAccess", required = true, description = "支付对接方式，1-零售通仅录单，2-小U支付")
    private Integer paymentAccess;

    @ApiDocClassDefine(value = "invoiceRule", required = true, description = "开票规则，1-国补金额优惠开票，2-国补金额优惠不开票")
    private Integer invoiceRule;

    @ApiDocClassDefine(value = "reportCity", required = true, description = "补贴上报城市")
    private Integer reportCity;

    @ApiDocClassDefine(value = "tag", required = true, description = "展示标签")
    private String tag;

    @ApiDocClassDefine(value = "registrationEntity", required = true, description = "活动报名主体")
    private String registrationEntity;

    @ApiDocClassDefine(value = "skuCateLevel", required = true, description = "sku对应能效等级")
    private Map<Long, String> skuCateLevel;

    @ApiDocClassDefine(value = "subsidyMode", required = true, description = "国补模式: 1-北京模式 2-广东模式 3-极简模式")
    private Integer subsidyMode;

    @ApiDocClassDefine(value = "brandMap", required = true, description = "家电品牌集合")
    private Map<Long, String> brandMap;

    @ApiDocClassDefine(value = "itemNmeMap", required = true, description = "商品名称集合")
    private Map<Long, String> itemNameMap;

    @ApiDocClassDefine(value = "specModelMap", required = true, description = "规格型号集合")
    private Map<Long, String> specModelMap;


    @ApiDocClassDefine(value = "brand", required = true, description = "家电品牌")
    private String brand;

    @ApiDocClassDefine(value = "itemNme", required = true, description = "商品名称")
    private String itemName;

    @ApiDocClassDefine(value = "specModel", required = true, description = "规格型号")
    private String specModel;

    @ApiDocClassDefine(value = "fetchType", required = true, description = "领券方式")
    private Integer fetchType;

    @ApiDocClassDefine(value = "usageGuide", required = true, description = "使用指南")
    private String usageGuide;

    @ApiDocClassDefine(value = "usageGuideImgUrl", required = true, description = "使用指南图片url")
    private String usageGuideImgUrl;

    /**
     * 极简是云闪付h5
     * 广东模式是小米活动页连接
     */
    @ApiDocClassDefine(value = "activityUrl", required = true, description = "活动会场URL")
    private String activityUrl;

    @ApiDocClassDefine(value = "skuDiscountCode", required = true, description = "sku对应的优惠码")
    private Map<Long, String> skuDiscountCode;

    @ApiDocClassDefine(value = "skuCategoryName", required = true, description = "sku对应的品类名称")
    private Map<Long, String> skuCategoryName;

    @ApiDocClassDefine(value = "sku69Code", required = true, description = "sku对应的69码，一品多码逗号隔开")
    private Map<Long, String> sku69Code;

    @ApiDocClassDefine(value = "actPayTag", required = true, description = "活动tag,广东模式配置“云闪付”")
    private String actPayTag;

    @ApiDocClassDefine(value = "invoiceCompanyId", required = true, description = "发票主体id")
    private Integer invoiceCompanyId;

    @ApiDocClassDefine(value = "skuUnionPayActivityId", required = true, description = "云闪付活动Id")
    private Map<Long, String> skuUnionPayActivityId;

    @ApiDocClassDefine(value = "checkSnMap", required = true, description = "O2O国补校验SN")
    private Map<Long, Boolean> checkSnMap;

    @ApiDocClassDefine(value = "paymentMode", required = true, description = "支付方式")
    private String paymentMode;

    @ApiDocClassDefine(value = "extInfo", required = true, description = "扩展字段")
    private String extInfo;

}
