package com.xiaomi.nr.promotion.api.dto.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/11/27 15:11
 */
@Getter
@AllArgsConstructor
public enum SubsidyActivityCodeEnum {
    /**
     * 0:有效
     */
    VALID(0),

    /**
     * 1:门店不可用
     */
    ORG_UNUSABLE(1),

    /**
     * 2:商品不可用
     */
    GOODS_UNUSABLE(2),

    /**
     * 3:活动已过期
     */
    ACT_EXPIRE(3),

    /**
     * 4:收货地址不可用，存在收货地址为当前省的活动（当前市不可用）
     */
    CITY_UNUSABLE(4),

    /**
     * 5:收货地址不可用，不存在收货地址为当前省的活动（当前省不可用）
     */
    PROVINCE_UNUSABLE(5),

    /**
     * 6:有资格，但是和活动无法匹配
     */
    ACTIVITY_NULL(6),

    /**
     * 7:无资格，也没有命中极简活动
     */
    QUALIFY_ACTIVITY_NULL(7),

    /**
     * 8:无活动匹配
     */
    NO_ACTIVITY_MATCH(8),


    ;

    private final int code;
}
