package com.xiaomi.nr.promotion.api.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;

/**
 * 商品可以参加的活动优惠价响应
 *
 * <AUTHOR>
 * @date 2021/6/21
 */
@Data
@ToString
public class GetProductActPriceResponse implements Serializable {
    private static final long serialVersionUID = 1118013190828458841L;

    /**
     * 活动价
     * key: sku or packageId.  val: 活动优惠价(分）
     */
    private Map<String, Long> actPriceMap;
}
