package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import com.xiaomi.nr.promotion.api.dto.model.GovActInfo;
import com.xiaomi.nr.promotion.api.dto.model.QualificationInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/21 10:16
 */
@Data
public class QueryProductLayerSubsidyQualificationResp implements Serializable {
    private static final long serialVersionUID = 6704104186031495115L;

    /**
     * 是否命中国补
     */
    @ApiDocClassDefine(value = "hasGovAct", description = "是否命中国补")
    private Boolean hasGovAct;

    /**
     * 命中国补活动
     */
    @ApiDocClassDefine(value = "govActInfo", description = "命中国补活动")
    private GovActInfo govActInfo;

    /**
     * 资格详情
     */
    @ApiDocClassDefine(value = "qualificationInfo", description = "资格详情")
    private QualificationInfo qualificationInfo;
}
