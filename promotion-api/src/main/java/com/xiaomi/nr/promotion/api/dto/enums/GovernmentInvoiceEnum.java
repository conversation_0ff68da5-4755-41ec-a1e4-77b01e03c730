package com.xiaomi.nr.promotion.api.dto.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 开票规则
 * @date 2024/12/19 14:08
 */
@Getter
@AllArgsConstructor
public enum GovernmentInvoiceEnum {
    GOVERNMENT_DISCOUNT_INVOICED(1, "国补金额优惠开票"),
    GOVERNMENT_DISCOUNT_NOT_INVOICED(2, "国补金额优惠不开票"),
    ;

    public final int value;
    public final String desc;
}
