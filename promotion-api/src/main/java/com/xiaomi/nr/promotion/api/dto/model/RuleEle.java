package com.xiaomi.nr.promotion.api.dto.model;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 规则
 *
 * <AUTHOR>
 * @date 2021/3/18
 */
@Data
@ToString
public class RuleEle implements Serializable {
    private static final long serialVersionUID = -4844515691504545399L;

    /**
     * 可以被抵扣的列表，抵扣券，可以同时具有包邮属性
     */
    private List<String> targetGoods = Lists.newArrayList();

    /**
     * 减免的钱，现金券，可以同时具有包邮属性
     */
    private Long reduceMoney = 0L;

    /**
     * 打的折扣，折扣券<9折就是90>，可以同时具有包邮属性
     */
    private Long reduceDiscount = 0L;

    /**
     * 减免的邮费，部分抵邮券
     */
    private Long reduceExpress = 0L;

    /**
     * 包邮属性，现金/折扣/抵扣券可以使用，1为包邮
     */
    private Integer postFree = 0;

    /**
     * 折扣活动、折扣券的最大减免价格，如果为0，是不设上限
     */
    private Long maxPrice = 0L;

    /**
     * 可以作为赠品的sku列表
     */
    private List<String> giftSkuList = new ArrayList<>();
}
