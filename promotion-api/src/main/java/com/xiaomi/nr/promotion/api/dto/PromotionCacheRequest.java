package com.xiaomi.nr.promotion.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PromotionCacheRequest implements Serializable {
    private static final long serialVersionUID = -7813075425142157361L;

    private Integer bizPlatform;

    private List<Long> activityIds;

    private List<String> goodsIds;

    private List<Integer> channelIds;

    private List<Integer> departmentIds;

    private List<String> orgCodes;

    private FilterParam filterParam;

    @Data
    public static class FilterParam implements Serializable {
        private static final long serialVersionUID = 4337031605958507817L;

        private boolean queryActivityIndex = false;

        private boolean queryActivityInfo = false;

        private boolean queryGoodsInfo = false;

        private boolean queryChannelsInfo = false;

        private boolean queryDepartmentInfo = false;

        private boolean queryOrgCodesInfo = false;

        private boolean queryCommonActivityInfo = false;

        private boolean querySortedActivityInfo = false;
    }
}
