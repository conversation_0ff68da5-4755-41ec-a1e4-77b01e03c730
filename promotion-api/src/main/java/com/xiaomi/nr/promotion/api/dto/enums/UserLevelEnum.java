package com.xiaomi.nr.promotion.api.dto.enums;

import java.io.Serializable;
import java.util.Objects;

/**
 * 用户等级
 *
 * <AUTHOR>
 * @date 2023/2/15
 */
public enum UserLevelEnum implements Serializable {
    /**
     * NORMAL
     */
    NORMAL("normal"),
    /**
     * VIP
     */
    VIP("vip"),
    /**
     * SVIP
     */
    SVIP("svip");

    /**
     * 用户等级
     */
    private final String level;

    UserLevelEnum(String level) {
        this.level = level;
    }

    /**
     * 获取用户等级
     *
     * @param level 等级
     * @return UserLevelEnum
     */
    public static UserLevelEnum getByLevel(String level) {
        for (UserLevelEnum levelEnum : UserLevelEnum.values()) {
            if (Objects.equals(levelEnum.level, level)) {
                return levelEnum;
            }
        }
        return null;
    }

    public String getLevel() {
        return level;
    }
}
