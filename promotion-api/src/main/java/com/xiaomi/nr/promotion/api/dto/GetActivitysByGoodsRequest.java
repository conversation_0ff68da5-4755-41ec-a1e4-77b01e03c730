package com.xiaomi.nr.promotion.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 获取商品有效活动列表
 */
@Data
public class GetActivitysByGoodsRequest implements Serializable {
    private static final long serialVersionUID = -5913748240918080330L;

    /**
     * sku或套装列表. 必须
     */
    private List<String> skuPackageList;
    /**
     * 应用id
     */
    private Long clientId;
    /**
     * 活动类型 26-换新立减
     */
    private int activityType;
    /**
     * 渠道 1.小米商城 2.直营店 3.专卖店 4.小米授权店
     */
    private int channel;

}
