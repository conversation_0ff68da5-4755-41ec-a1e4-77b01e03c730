package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/22 19:11
 */
@Data
public class ServicePackDeductedInfo implements Serializable {

    /**
     * ssuId
     */
    private Long ssuId;

    /**
     * 购物车下标
     */
    private Integer inCartIndex;

    /**
     * ssuType: 0-单品ssu, 1-套装ssu，2-主附品ssu
     */
    private Integer ssuType;
    /**
     *  商品业务子类型，13-工时商品，14-配件商品
     */
    private Integer bizSubType;

    /**
     * 优惠数量
     */
    private Integer num;

    /**
     * 减免金额
     */
    private Long reduceAmount;
}
