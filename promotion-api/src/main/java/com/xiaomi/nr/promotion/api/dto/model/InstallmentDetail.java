package com.xiaomi.nr.promotion.api.dto.model;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 分期活动信息
 *
 * <AUTHOR>
 * @data 2024/09/30
 */
@Data
public class InstallmentDetail implements Serializable {
    private static final long serialVersionUID = 2309906025570925907L;

    @ApiDocClassDefine(value = "id", required = true, description = "分期活动ID")
    private Long id;

    @ApiDocClassDefine(value = "name", required = true, description = "活动名称")
    private String name;

    @ApiDocClassDefine(value = "startTime", required = true, description = "活动开始时间（秒）")
    private Long startTime;

    @ApiDocClassDefine(value = "endTime", required = true, description = "活动结束时间（秒）")
    private Long endTime;

    @ApiDocClassDefine(value = "status", required = true, description = "活动状态")
    private Integer status;

    @ApiDocClassDefine(value = "channels", required = true, description = "渠道ID")
    private List<Integer> channels;

    @ApiDocClassDefine(value = "installmentType", required = true, description = "分期类型")
    private Integer installmentType;

    @ApiDocClassDefine(value = "installmentTimes", required = true, description = "分期期数")
    private List<Integer> installmentTimes;

    @ApiDocClassDefine(value = "minAmount", required = true, description = "可参与活动的最小金额（分）")
    private Long minAmount;

    @ApiDocClassDefine(value = "maxAmount", required = true, description = "可参与活动的最大金额（分）")
    private Long maxAmount;

    @ApiDocClassDefine(value = "interestPayer", required = true, description = "息费承担方")
    private Integer interestPayer;

    @ApiDocClassDefine(value = "goodsList", required = true, description = "商品列表（针对参数中商品列表，过滤出可参与此活动的商品列表，无序）")
    private List<InstallmentGoodItem> goodsList;
}
