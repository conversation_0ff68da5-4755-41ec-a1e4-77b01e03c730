package com.xiaomi.nr.promotion.api.service;

import com.xiaomi.nr.promotion.api.dto.ManualQueryResourceResponse;
import com.xiaomi.nr.promotion.api.dto.ManualResourceRequest;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

/**
 * <AUTHOR>
 * @Date 2024/5/26
 */
public interface ManualOperationDubboService {

    /**
     * 手动退款资源
     *
     * @param request 手动资源请求对象，包含需要处理的SPU组和身份证信息
     * @return 处理结果，成功时返回相应的消息，失败时返回异常信息
     * @throws BizError 业务异常
     */
    Result<String> manualRefundResource(ManualResourceRequest request) throws BizError;

    /**
     * 手动查询资源
     *
     * @param request 手动资源请求对象，包含身份证信息和SPU组信息
     * @return 包含总限额和SPU组限额映射的响应结果
     */
    Result<ManualQueryResourceResponse> manualQueryResource(ManualResourceRequest request);
}
