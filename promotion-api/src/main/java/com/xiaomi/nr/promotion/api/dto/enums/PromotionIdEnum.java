package com.xiaomi.nr.promotion.api.dto.enums;

import lombok.Getter;

/**
 * 虚拟优惠ID，要求在全局优惠ID唯一
 *
 * <AUTHOR>
 * @date 2024/1/17
 */
@Getter
public enum PromotionIdEnum {

    UN_DUPLICATED_WORK_HOUR(100000000000001L, "汽车售后工时去重"),

    POINT(100000000000002L, "积分"),

    ;

    private final Long promotionId;

    private final String name;

    PromotionIdEnum(Long promotionId, String name) {
        this.promotionId = promotionId;
        this.name = name;
    }

}
