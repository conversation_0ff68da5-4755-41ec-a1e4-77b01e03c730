package com.xiaomi.nr.promotion.api.dto;


import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 2022/5/19
 */
@Data
public class CartPromotionRequest implements Serializable {


    /**
     * 购物车商品列表
     */
    @ApiDocClassDefine(value = "cartList", required = true, description = "购物车商品列表")
    private List<CartItem> cartList;

    /**
     * 渠道
     * 1：小米商城
     * 2：米家-直营店
     * 3：米家-专卖店
     * 4:  授权店
     * 20:  团购- 直客
     * 21:   团购- 经销商
     */
    @ApiDocClassDefine(value = "channel", required = true, description = "渠道")
    private Integer channel;

    /**
     * 应用id
     * 必须传【数据库存整数】
     */
    @ApiDocClassDefine(value = "clientId", required = true, description = "应用id")
    private Long clientId = 0L;

    /**
     * 用户id
     * submit必须传【数据库存整数】
     */
    @ApiDocClassDefine(value = "userId", required = true, description = "用户id")
    private Long userId = 0L;

    /**
     * 优惠券id值
     * 用逗号分开，couponCodes和couponIds两个至少传一个【数据库存整数】;
     */
    @ApiDocClassDefine(value = "couponIds", required = true, description = "优惠券id值")
    private List<Long> couponIds;



    /**
     * 未参加的加价购数量
     */
    @ApiDocClassDefine(value = "机构id", required = true, description = "未参加的加价购数量")
    private Long bargainSize = 0L;


    /**
     * 机构id（线下门店传）
     */
    @ApiDocClassDefine(value = "orgCode", required = true, description = "机构id")
    private String orgCode = "";

    /**
     * uid，mid不传，为手机号则传"mobile"
     */
    @ApiDocClassDefine(value = "uidType", required = true, description = "uid，mid不传，为手机号则传\"mobile")
    private String uidType = "";

    /**
     * 业务身份
     * 米网-"mishop" 云店-"estore" 电视商城-"tvshop"
     */
    @ApiDocClassDefine(value = "globalBusinessPartner", required = true, description = "业务身份")
    private String globalBusinessPartner = "";

    /**
     * 用户是否是F会员，1-是，2-否
     */
    @ApiDocClassDefine(value = "userIsFriend", required = true, description = "用户是否是F会员，1-是，2-否")
    private Integer userIsFriend;

    /**
     * 是否获取优惠券列表
     */
    @ApiDocClassDefine(value = "getCouponList", required = true, description = "是否获取优惠券列表")
    private Boolean getCouponList = true;

    /**
     * 优惠劵列表展示 0-只展示可用，1-都展示
     */
    @ApiDocClassDefine(value = "showType", required = true, description = "优惠劵列表展示 0-只展示可用，1-都展示")
    private Long showType = 1L;

    /**
     * 是否需要选择券
     */
    @ApiDocClassDefine(value = "useDefaultCoupon", required = true, description = "是否需要选择券")
    private Boolean useDefaultCoupon = true;

    /**
     * 用户等级, normal,VIP，SVIP
     */
    @ApiDocClassDefine(value = "userLevel", required = true, description = "用户等级")
    private String userLevel;


    /**
     * 订单已使用的优惠券
     */
    @ApiDocClassDefine(value = "submitType", description = "已使用优惠券")
    private Long usedCouponId;

    /**
     * 订单提交类型
     */
    @ApiDocClassDefine(value = "submitType", required = true, description = "提交类型")
    private Integer submitType = 0;

    /**
     * 汽车vid
     */
    @ApiDocClassDefine(value = "vid", required = true, description = "汽车vid")
    private String vid;

    /**
     * 下单时间
     */
    @ApiDocClassDefine(value = "orderTime", required = true, description = "下单时间")
    private Long orderTime;
}
