package com.xiaomi.nr.promotion.api.dto.model;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/21 10:22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QualificationInfo implements Serializable {
    private static final long serialVersionUID = 4888226952654065421L;

    /**
     * regionId
     */
    @ApiDocClassDefine(value = "regionId", description = "regionId")
    private Integer regionId;

    /**
     * 活动编码
     */
    @ApiDocClassDefine(value = "activityCategory", description = "活动编码")
    private Integer activityCategory;

    /**
     * 品类编码
     */
    @ApiDocClassDefine(value = "cateCode", description = "品类编码")
    private String cateCode;

    /**
     * 品类名称
     */
    @ApiDocClassDefine(value = "cateName", description = "品类名称")
    private String cateName;

    /**
     * 图片地址
     */
    @ApiDocClassDefine(value = "imgUrl", description = "图片名称url")
    private String imgUrl;

    /**
     * 状态
     */
    @ApiDocClassDefine(value = "statusCode", description = "状态")
    private Integer statusCode;

    /**
     * 状态名称
     */
    @ApiDocClassDefine(value = "statusDesc", description = "状态名称")
    private String statusDesc;

    /**
     * 支付方式
     */
    @ApiDocClassDefine(value = "paymentMode", description = "支付方式")
    private String paymentMode;
}
