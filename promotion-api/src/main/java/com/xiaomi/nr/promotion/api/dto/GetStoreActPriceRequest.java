package com.xiaomi.nr.promotion.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 商品门店活动价(直降 ｜门店价）请求
 *
 * <AUTHOR>
 * @date 2021/11/11
 */
@Data
public class GetStoreActPriceRequest implements Serializable {
    private static final long serialVersionUID = 7658234844244168323L;
    /**
     * sku 或 packageId 列表. 【必须】
     */
    private List<String> skuPackageList;

    /**
     * 机构Code【必须】
     */
    private String orgCode;
}
