package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 券数据
 *
 * <AUTHOR>
 * @date 2022/3/3
 */
@Data
public class Coupon implements Serializable {
    /**
     * 优惠券ID
     */
    private Long id;

    /**
     * 优惠券类型id
     */
    private Long couponTypeId;

    /**
     * 优惠券类型
     */
    private Integer type;

    /**
     * 优惠券状态
     */
    private String stat;

    /**
     * 劵开始时间
     */
    private Long startTime;

    /**
     * 劵结束时间
     */
    private Long endTime;

    /**
     * 劵类型名称
     */
    private String couponTypeName;

    /**
     * 劵类型使用范围描述
     */
    private String couponTypeRangeDesc;

    /**
     * 劵类型使用渠道描述
     */
    private String useChannelDesc;

    /**
     * 门槛类型
     * 1满元
     * 2满件
     * 3每满元
     * 4每满件
     */
    private Integer bottomType;

    /**
     * 门槛金额
     */
    private Integer bottomPrice;

    /**
     * 门槛数量
     */
    private Integer bottomCount;

    /**
     * 是否包邮，1-不包邮，2-包邮
     */
    private Integer postFree;

    /**
     * 调价商品是否可用，1-不可用，2-可用
     */
    private Integer checkPrice;

    /**
     * 部分套装商品是否可用劵，1-不可用，2-可用
     */
    @Deprecated
    private Integer checkPackage;

    /**
     * 劵是否可用，0-不可用，1-可用
     */
    private Integer allow;

    /**
     * 优惠券的面值，“100.00”：代表满*减100元，“8.5”：代表满*打8.5折，空：代表是抵扣券的
     */
    private String showTitle;

    /**
     * 优惠券的面额单位（现金券的单位为“元”，折扣券的单位为“折”，抵扣券的单位为空）
     */
    private String showUnit;

    /**
     * 应用id列表
     */
    @Deprecated
    private String clientIds;

    /**
     * 劵类型code，“cash”：现金券
     */
    private String typeCode;

    /**
     * 是否可分享，1-可分享，2-不可分享
     */
    private Long isShare;

    /**
     * 扩展信息
     */
    private Map<String, String> exInfo;

    /**
     * 劵可用区域Id限制列表
     */
    private List<String> limitUseRegion;

    /**
     * 标签
     */
    private List<String> tags;

    /**
     * 可用劵商品总额
     */
    private Long validGoodsPrice;

    /**
     * 发放渠道
     */
    private String sendChannel;

    /**
     * 使用渠道
     */
    private String useChannel;

    /**
     * 优惠券类型，区分包邮券和普通券
     */
    private Integer couponType;

    /**
     * 优惠券分组
     */
    private String couponGroupNo;

    /**
     * 是否被选中
     */
    private boolean checked;

    /**
     * 实际减免金额
     */
    private long realReduce;

    /**
     * 年度类型：1-单年度；2-双年度
     */
    private Integer annualType;

    /**
     * 券上配置商品的详情
     */
    private Map<Long, SsuExtItemDto> couponSsuExtInfo;

    /**
     * @ref com.xiaomi.nr.promotion.enums.CouponServiceTypeEnum
     */
    private Integer couponServiceType;

    /*
     * 结算阶段 1 定金 2 尾款
     */
    private Integer checkoutStage;

    /**
     * 被选中的组套Id
     */
    private String selectedServicePackId;

    /**
     * 可用组套信息
     */
    private List<ServicePackCheckoutCouponItem> availableServicePackItems;


    public void updateInvalidInfo(long code, String reason, String keyReason) {
        this.setAllow(BooleanUtils.toInteger(false));
        exInfo.put("reason_unusable", reason);
        exInfo.put("code_unusable", String.valueOf(code));
        exInfo.put("keydata_unusable", keyReason);
    }

    public void updateValidInfo(long validGoodsPrice, BigDecimal reduceMoneyYuan, long reduceMoney) {
        this.setAllow(BooleanUtils.toInteger(true));
        this.setValidGoodsPrice(validGoodsPrice);
        exInfo.put("real_reduce", reduceMoneyYuan.toString());
        realReduce = reduceMoney;
    }
}
