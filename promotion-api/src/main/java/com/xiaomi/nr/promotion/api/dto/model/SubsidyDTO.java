package com.xiaomi.nr.promotion.api.dto.model;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 金融贴息活动详情
 *
 * <AUTHOR>
 * @date 20251/6
 */
@Data
public class SubsidyDTO implements Serializable{

    private static final long serialVersionUID = -1803251174200427940L;

    @ApiDocClassDefine(value = "id", required = true, description = "分期活动ID")
    private Long id;

    @ApiDocClassDefine(value = "name", required = true, description = "活动名称")
    private String name;

    @ApiDocClassDefine(value = "startTime", required = true, description = "活动开始时间（秒）")
    private Long startTime;

    @ApiDocClassDefine(value = "endTime", required = true, description = "活动结束时间（秒）")
    private Long endTime;

    @ApiDocClassDefine(value = "typeId", required = true, description = "活动类型id")
    private Integer typeId;

    @ApiDocClassDefine(value = "status", required = true, description = "活动状态")
    private Integer status;

    @ApiDocClassDefine(value = "channels", required = true, description = "渠道ID")
    private List<Integer> channels;

    @ApiDocClassDefine(value = "userScope", required = true, description = "用户类型")
    private String userScope;

    @ApiDocClassDefine(value = "orderStartTime", required = true, description = "订单开始时间")
    private Long orderStartTime;

    @ApiDocClassDefine(value = "orderEndTime", required = true, description = "订单结束时间")
    private Long orderEndTime;

    @ApiDocClassDefine(value = "budgetApplyNo", required = true, description = "预算单号")
    private String budgetApplyNo;

    @ApiDocClassDefine(value = "lineNum", required = true, description = "预算行号")
    private Long lineNum;

    @ApiDocClassDefine(value = "interestBearer", required = true, description = "贴息方")
    private Integer interestBearer;


}
