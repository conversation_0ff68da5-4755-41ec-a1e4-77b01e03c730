package com.xiaomi.nr.promotion.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: <EMAIL>
 * @time: 2025/4/8 15:38
 * @description: 三方优惠-资格扩展信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetQualificationSubsidyExtInfoResp implements Serializable {

    private static final long serialVersionUID = -7803915465328757798L;
    /**
     * 支付宝国补渠道扩展信息
     */
    private QualificationAliPayExtendInfoResp aliPayExtInfo;

    @Data
    public static class QualificationAliPayExtendInfoResp implements Serializable {
        private static final long serialVersionUID = 1083404191989362608L;
        /**
         * 业务场景
         */
        private String policyDiscountScene;
        /**
         * 资格券模板id
         */
        private String qualificationTemplate;

        /**
         * 商品类⽬id
         */
        private String qualificationCateId;

        /**
         * 资格券实例ID
         */
        private String qualificationVoucherId;

        /**
         * 能效等级
         */
        private String energyLevel;
    }
}