package com.xiaomi.nr.promotion.api.dto.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/8 16:03
 */
public enum PromotionTag {

    ALL(0, "所有人均可享受的活动"),
    CAR_OWNER_REDUCE(1, "车主专享立减的活动"),
    LOCK_ORDER_OWNER_REDUCE(2, "准车主专享立减的活动"),
    ALL_CAR_OWNER_REDUCE(3, "车主立减的活动"),
    ;
    /**
     * 活动类型值
     */
    @Getter
    private final int value;


    /**
     * 活动类型的名称
     */
    @Getter
    private final String name;


    PromotionTag(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static boolean needCarOwnerInfo(PromotionTag tag) {
        if (tag == null) {
            return false;
        }
        return tag != ALL;
    }


}
