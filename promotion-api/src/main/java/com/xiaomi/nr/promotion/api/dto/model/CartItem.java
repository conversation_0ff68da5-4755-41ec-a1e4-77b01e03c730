package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 购物车单个条目明细
 *
 * <AUTHOR>
 * @date 2021/3/18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CartItem extends OrderCartItem implements Serializable {
    private static final long serialVersionUID = -2093708569005354308L;

    /**
     * 扣减明细，传入为空，返回不为空，值>=0 , act_XXX:XXX , coupon_XXX:XXX
     */
    private Map<String, Long> reduceList = new HashMap<>();

    /**
     * 活动扣减分摊，key: reduceList中的key，即【活动类型_活动id】; value: 活动分摊
     */
    private Map<String,List<ReduceShareInfo>> reduceShareMap = new HashMap<>();

    /**
     * 优惠详情
     */
    private List<ReduceDetailItem> reduceItemList = new ArrayList<>();

    /**
     * 购物车套装子级列表 返回不为空
     */
    private List<CartItemChild> childs = new ArrayList<>();

    /**
     * 主附品强绑定标记
     */
    private Boolean bindMainAccessory;

    /**
     * 直降优惠的金额
     */
    private Long onsaleReduce = 0L;

    /**
     * 门店价优惠的金额
     */
    private Long storepriceReduce = 0L;

    /**
     * 优惠明细（最细粒度的明细）
     */
    private Map<String, List<ReduceDetail>> reduceDetailList = new HashMap<>();

    /**
     * 购物车结算价
     */
    private Long checkoutPrice = 0L;

    /**
     * 购物车子项列表
     */
    private List<SubItem> subItemList;

}
