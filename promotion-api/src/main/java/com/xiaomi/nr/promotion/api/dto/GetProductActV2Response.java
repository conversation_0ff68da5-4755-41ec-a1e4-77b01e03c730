package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.ProductActItemDto;
import com.xiaomi.nr.promotion.api.dto.model.ProductRespActGoodsItemDto;
import com.xiaomi.nr.promotion.api.dto.model.ProductValidActItemDto;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 产品站返回值
 *
 * <AUTHOR>
 * @date 2023/4/17
 */
@Data
@ToString
public class GetProductActV2Response implements Serializable {

    private static final long serialVersionUID = 4500216512595053749L;

    /**
     * 商品可参与的活动ID列表
     * 1. 针对的是传入的sku或套装ID
     * 2. 基础排序后的结果
     * 3. 不可参与的活动则不返回
     */
    private Map<ProductRespActGoodsItemDto, List<ProductValidActItemDto>> validPromotions;

    /**
     * 活动信息map，活动ID=>活动信息
     */
    private Map<String, ProductActItemDto> promotions;

}
