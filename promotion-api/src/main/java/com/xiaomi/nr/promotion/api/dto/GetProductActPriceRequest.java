package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.PreSaleCartItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 商品可以参加的活动优惠价请求
 *
 * <AUTHOR>
 * @date 2021/6/21
 */
@Data
public class GetProductActPriceRequest implements Serializable {
    private static final long serialVersionUID = 7658234844244168323L;
    /**
     * sku 或 packageId 列表. 【必须】
     */
    private List<String> skuPackageList;

    /**
     * 走预售模式的商品<sku、预售商品信息>
     */
    private Map<String, PreSaleCartItem> preSaleSkuPackageList;

    /**
     * 应用id【数据库存整数】
     */
    private Long clientId;

    /**
     * 机构Code
     */
    private String orgCode;

    /**
     * 促销渠道 1：小米商城 2：小米直营店 3：小米专卖店 （暂不支持授权店）
     */
    private Integer channel;
}
