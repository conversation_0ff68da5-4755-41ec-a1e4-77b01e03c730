package com.xiaomi.nr.promotion.api.dto.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by xqz on 2025/1/7
 * 用户类型枚举
 */
@Getter
public enum InterestBearerEnum {

    XIAO_MI(1,"<PERSON><PERSON>","小米"),

    ;

    private final int code;
    private final String key;
    private final String desc;

    InterestBearerEnum(int code, String key, String desc) {
        this.code = code;
        this.key = key;
        this.desc = desc;
    }

    public static InterestBearerEnum findByValue(int value) {
        InterestBearerEnum[] values = InterestBearerEnum.values();
        for (InterestBearerEnum actTypeEnum : values) {
            if (value == actTypeEnum.code) {
                return actTypeEnum;
            }
        }
        return null;
    }

    public static String findKeyByValue(int value) {
        InterestBearerEnum[] values = InterestBearerEnum.values();
        for (InterestBearerEnum actTypeEnum : values) {
            if (value == actTypeEnum.code) {
                return actTypeEnum.getKey();
            }
        }
        return "";
    }
    public static Integer findValueByKey(String key) {
        InterestBearerEnum[] values = InterestBearerEnum.values();
        for (InterestBearerEnum actTypeEnum : values) {
            if (StringUtils.equals(key,actTypeEnum.key)) {
                return actTypeEnum.getCode();
            }
        }
        return null;
    }
}
