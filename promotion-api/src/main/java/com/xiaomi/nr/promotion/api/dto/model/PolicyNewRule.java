package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 规则
 *
 * <AUTHOR>
 * @date 2021/3/19
 */
@Data
@ToString
public class PolicyNewRule implements Serializable {
    private static final long serialVersionUID = -8462772315693690067L;

    /**
     * 配置url
     */
    private String configUrl;

    /**
     * 是否忽略库存
     */
    private Integer ignoreStock;
    
    /**
     * 赠品的信息
     */
    private PolicyNewGoods giftGoods = new PolicyNewGoods();

    /**
     * 加价购的信息
     */
    private PolicyNewGoods barginGoods = new PolicyNewGoods();
}
