package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 邮费减免详情
 *
 * <AUTHOR>
 * @date 2021/3/18
 */
@Data
@ToString
public class ExpressDetail implements Serializable {
    private static final long serialVersionUID = -1803251174200427940L;

    /**
     * 价格
     */
    private Long price = 0L;

    /**
     * 购物项列表
     */
    private List<String> items = new ArrayList<>();


    /**
     * 运费券Id
     */
    private Long couponId = 0L;
}
