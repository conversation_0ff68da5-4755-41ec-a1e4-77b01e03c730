package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.ProductPayInfo;
import com.xiaomi.nr.promotion.api.dto.model.ProductPriceInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 结算结果响应：购物车、结算请求返回值
 *
 * <AUTHOR>
 * @date 2021/3/18
 */
@Data
@ToString
public class GetProductActResponse implements Serializable {
    private static final long serialVersionUID = 1118013190828458841L;

    /**
     * 价格信息
     */
    private ProductPriceInfo price;

    /**
     * 活动信息
     */
    private List<PromotionInfo> promotions;

    /**
     * 支付信息
     */
    private ProductPayInfo pays;

    /**
     * 门店活动信息
     */
    private List<PromotionInfo> storePromotions;
}
