package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class Order implements Serializable {
    private static final long serialVersionUID = 6109534779954143972L;
    /**
     * 用户id
     */
    private Long userId = 0L;

    /**
     * 订单id
     */
    private Long orderId = 0L;

    /**
     * 原价减去优惠的
     */
    private Long goodsAmount = 0L;

    /**
     * 渠道 暂时保留
     */
    private String channel = "";

    /**
     * 邮费减免详情
     */
    private Map<String, ExpressDetail> reduceExpressDetail = new HashMap<>();

}
