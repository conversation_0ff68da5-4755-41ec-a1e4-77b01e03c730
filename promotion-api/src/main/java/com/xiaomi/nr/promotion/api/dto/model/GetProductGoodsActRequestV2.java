package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 单品可以参加的活动请求参数
 *
 * <AUTHOR>
 * @date 2021/6/11
 */
@Data
public class GetProductGoodsActRequestV2 implements Serializable{
    private static final long serialVersionUID = 7658234844244168323L;
    /**
     * sku 列表. 必须
     */
    private List<String> skuPackageList;

    /**
     * 活动类型. 选择
     */
    private List<Integer> actTypeList;

    /**
     * 根据活动渠道查询
     */
    private List<Integer> channel;

}
