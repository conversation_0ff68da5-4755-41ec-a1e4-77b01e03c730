package com.xiaomi.nr.promotion.api.service;

import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.youpin.infra.rpc.Result;

public interface PromotionAddDubboService {

    /**
     * 查询汽车优惠预算信息
     * @param request 请求
     * @return 优惠预算信息
     */
    Result<PromotionBudgetResponse> queryCarPromotionBudgetInfo(PromotionBudgetRequest request);

    /**
     * 追加优惠下单接口
     * @param request 请求
     * @return 是否成功
     */
    Result<CheckoutPromotionAddResponse> checkoutPromotion(CheckoutPromotionAddRequest request);

    /**
     * 追加优惠提交接口
     * @param request 请求
     * @return 优惠明细
     */
    Result<SubmitPromotionAddResponse> submitPromotion(SubmitPromotionAddRequest request);

    /**
     * 追加优惠回滚接口
     * @param request 请求
     * @return 是否成功
     */
    Result<RollbackPromotionAddResponse> rollbackPromotion(RollbackPromotionAddRequest request);

    /**
     * 改配场景追加优惠全量取消接口
     * @param request 请求
     * @return 是否成功
     */
    Result<CancelPromotionAddResponse> cancelPromotion(CancelPromotionAddRequest request);
}
