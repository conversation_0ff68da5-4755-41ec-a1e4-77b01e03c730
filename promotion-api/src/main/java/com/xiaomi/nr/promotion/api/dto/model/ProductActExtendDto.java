package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 产品站-入参商品信息
 *
 * <AUTHOR>
 * @date 2023/8/09
 */
@Data
public class ProductActExtendDto implements Serializable {

    private static final long serialVersionUID = 1167084987363268877L;

    /**
     * 是否包邮 true 是  false 否
     */
    private Boolean postFree;

    /**
     * 是否显示凑单
     */
    private Boolean showAddOnItem;

    /**
     * 是否在单品页强制推荐加价购
     */
    private Boolean recommendForceAddPrice;

    /**
     * 是否为VIP活动（指定人群的则为true）
     */
    private Boolean vipAct;

    /**
     * 是否为F会员专属赠品
     */
    private Boolean fMember;

    /**
     * 是否支持查看适用商品
     */
    private Boolean showUsableGoods;

    /**
     * 是否忽视赠品库存
     */
    private Boolean ignoreStock;

}
