package com.xiaomi.nr.promotion.api.dto;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 结算V2请求参数：结算页结算请求
 *
 * <AUTHOR>
 */
@Data
@ToString
public class CheckoutPromotionV2Request extends CheckoutPromotionRequest implements Serializable {

    /**
     * 是否获取优惠券列表
     */

    private Boolean getCouponList = false;


    /**
     * 优惠劵列表展示 0-只展示可用，1-都展示
     */
    private Long showType = 1L;

    /**
     * 是否需要选择券
     */
    private Boolean useDefaultCoupon = true;


}

