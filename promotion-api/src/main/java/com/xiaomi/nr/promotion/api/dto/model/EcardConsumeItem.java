package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 单卡在各小单扣费详情
 *
 * <AUTHOR>
 * @date 2021/3/18
 */
@Data
@ToString
public class EcardConsumeItem implements Serializable {
    private static final long serialVersionUID = 3885353102147055379L;

    /**
     * 小单号
     */
    private Long sOrderId = 0L;

    /**
     * 该礼品卡上所扣金额（分）
     */
    private Long money = 0L;
}
