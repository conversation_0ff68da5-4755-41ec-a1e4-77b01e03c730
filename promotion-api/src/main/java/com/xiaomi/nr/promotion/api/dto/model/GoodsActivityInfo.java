package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class GoodsActivityInfo implements Serializable {

    /**
     * 活动id
     */
    private long id;
    /**
     * 活动类型
     */
    private int type;
    /**
     * 活动名称
     */
    private String name;
    /**
     * 活动开始时间 秒
     */
    private long unixStartTime;
    /**
     * 活动结束时间 秒
     */
    private long unixEndTime;
    /**
     * 活动立减信息
     */
    private RenewReduceGoods renewReduceGoods;


}
