package com.xiaomi.nr.promotion.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class PromotionCacheResponse implements Serializable {
    private static final long serialVersionUID = 5963569547318316272L;

    private Map<Long, String> actIndexList;

    private Map<Long, String> actsList;

    private Map<String, List<Long>> sku2ActMapForTool;

    private Map<Integer, List<Long>> channel2actsMapForTool;

    private Map<Integer, List<Long>> department2actsMapForTool;

    private Map<String, List<Long>> orgCode2actsMapForTool;

    private List<Long> commonActListForTool;

    private List<Long> sortActListForTool;
}
