package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 商品对象
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
@Data
public class GoodsDto implements Serializable{
    private static final long serialVersionUID = -1803251174200427940L;

    /**
     * SKU 或者 套餐CID
     */
    private Long id;

    /**
     * 属性
     */
    private String level;

    /**
     * SSU ID
     */
    private Long ssuId;

    /**
     * 套装子级列表,目前仅用于
     */
    private List<CartItemChild> childs = new ArrayList<>();

    /**
     * 主附品强绑定标记
     */
    private Boolean bindMainAccessory;

    /**
     * 商品类型
     * 1:  主推品
     * 2:  常规品
     */
    private Integer goodsType;

    /**
     * 售价
     */
    private Long price;

    /**
     * 商品所属部门
     * 1:  销一
     * 2:  销二
     * 3:  销三
     */
    private Integer department;
}
