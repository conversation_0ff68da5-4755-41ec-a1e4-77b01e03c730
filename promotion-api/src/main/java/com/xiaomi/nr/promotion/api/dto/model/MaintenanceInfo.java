package com.xiaomi.nr.promotion.api.dto.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： duan<PERSON>gqi
 * @date： 2024/5/9
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaintenanceInfo implements Serializable {

    private static final long serialVersionUID = -9140595287754789648L;
    /**
     * 付费类型：自费/保内/理赔/内部结算
     */
    private Integer payType;
    /**
     * 工时单价
     */
    private Long unitPrice;
    /**
     * 工时(小时)
     */
    private BigDecimal workHour;

    /**
     * 标准面
     * 维保业务场景 - 抵扣券 - 漆面修复券 使用
     * 实际标准面 * 10 = workHourStandardPage
     */
    private Integer workHourStandardPage;

    /**
     * 是否可以参加调价
     */
    private boolean canAdjustPrice = false;

    /**
     * 不可以用的售后服务券类型
     */
    private List<Long> cannotUseCouponServiceTypes = new ArrayList<>();
}
