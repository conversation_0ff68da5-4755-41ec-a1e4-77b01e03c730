package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.GoodsDto;
import com.xiaomi.nr.promotion.api.dto.model.PreSaleCartItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 商品可以参加的活动优惠价请求
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
@Data
public class GetProductActPriceV2Request implements Serializable {
    private static final long serialVersionUID = 7658234844244168323L;
    /**
     * sku 或 packageId 列表. 【必须】
     */
    private List<GoodsDto> skuPackageList;

    /**
     * 走预售模式的商品<商品Id、预售商品信息>
     */
    private Map<String, PreSaleCartItem> preSaleSkuPackageList;

    /**
     * 促销渠道 1：小米商城 2：小米直营店 3：小米专卖店 4: 授权店
     */
    private Integer channel;

    /**
     * 应用id【数据库存整数】
     */
    private Long clientId;

    /**
     * 门店Code
     */
    private String orgCode;
}
