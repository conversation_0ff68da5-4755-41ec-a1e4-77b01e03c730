package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 该礼品卡上所扣金额
 *
 * <AUTHOR>
 * @date 2021/3/18
 */
@Data
@ToString
public class EcardConsumeItemList implements Serializable {
    private static final long serialVersionUID = 1013136747097028227L;

    /**
     * 单卡在各小单扣费详情列表
     */
    private List<EcardConsumeItem> consumeList = new ArrayList<>();

    /**
     * 该礼品卡扣的总额
     */
    private Long totalAmount = 0L;
}
