package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailAddItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CheckoutPromotionAddRequest implements Serializable {

    private static final long serialVersionUID = 4007090310500153473L;

    private Long orderId;

    private Long orderPrice;

    private Long userId;

    private List<ReduceDetailAddItem> promotionInfoList;

}
