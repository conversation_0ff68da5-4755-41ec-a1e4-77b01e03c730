package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 活动价信息
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
@Data
public class GoodsPriceDto implements Serializable {
    private static final long serialVersionUID = 3823168753227770115L;

    /**
     * SKU 或者 套餐ID
     */
    private Long id;

    /**
     * 'sku' 或 'package'
     */
    private String level;

    /**
     * 直降价格(分)
     */
    private Long lowerPrice;

    /**
     * 套装组直降价，key：组ID val：直降价（分）
     */
    private Map<Integer, Long> lowerPriceGroups;
}
