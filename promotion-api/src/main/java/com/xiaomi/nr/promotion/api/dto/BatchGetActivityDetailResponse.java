package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.ActivityDetailDto;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;

/**
 * 活动详情detail响应
 *
 * <AUTHOR>
 * @date 2022/12/5
 */
@Data
@ToString
public class BatchGetActivityDetailResponse implements Serializable {
    private static final long serialVersionUID = 8467053773432680731L;

    /**
     * 活动详情 key activityId, value: detail
     */
    private Map<Long, ActivityDetailDto> activityMap;
}
