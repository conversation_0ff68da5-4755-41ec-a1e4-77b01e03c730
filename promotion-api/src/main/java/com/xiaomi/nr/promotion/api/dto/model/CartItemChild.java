package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 购物车套装子级
 *
 * <AUTHOR>
 * @date 2021/3/18
 */
@Data
@ToString
public class CartItemChild implements Serializable {
    private static final long serialVersionUID = 600114265281751516L;
    /**
     * 商品sku
     */
    private String sku = "";

    /**
     * SSU_ID
     */
    private Long ssuId;

    /**
     * 套装内售价
     */
    private Long sellPrice = 0L;

    /**
     * 套装内商品数量
     */
    private Integer count = 0;

    /**
     * 门店使用
     */
    private String unitId = "";

    /**
     * 直降预售使用，直降定金分摊
     */
    private Long onSaleBookingPrice = 0L;

    /**
     * 直降使用，直降后价格
     */
    private Long lowerPrice = 0L;

    /**
     * 原销售价
     */
    private Long originalSellPrice = 0L;

    /**
     * cartPrice
     */
    private Long cartPrice = 0L;

    /**
     * 购物车结算价
     */
    private Long checkoutPrice = 0L;

    /**
     * 套装内子商品直降优惠的金额
     */
    private Long onsaleReduce = 0L;

    /**
     * 套装内子商品门店价优惠的金额
     */
    private Long storepriceReduce = 0L;

    /**
     * 组ID
     */
    private Integer groupId;
}
