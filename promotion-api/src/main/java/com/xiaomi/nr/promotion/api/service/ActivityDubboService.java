package com.xiaomi.nr.promotion.api.service;

import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.nr.promotion.api.dto.GetGovernmentSubsidyRequest;
import com.xiaomi.nr.promotion.api.dto.model.GetProductGoodsActRequestV2;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * 活动相关接口
 * <p>
 * 核心能力：
 * 1. 获取商品可参加进行中活动
 * 2. 获取商品可参加即将开始活动
 * 3. 获取商品优惠价（直降）
 * 4. 获取商品优惠价（直降）详情列表
 * 5. 商城产品站接口
 * 6. 获取商品门店活动价 (直降 | 门店价）
 * 7. 获取门店商品活动价 (直降 | 门店价）
 * <p>
 * 具体文档接口(<a href="https://xiaomi.f.mioffice.cn/docs/dock4zPMX9tfRYRNH5NHjTfXFHe#">...</a>)
 *
 * <AUTHOR>
 * @date 2021-05-13
 */
public interface ActivityDubboService {

    /**
     * 产品站批量获取可参加进行中的活动
     * @param request 请求参数
     * @return 响应
     */
    Result<MultiProductGoodsActResponse> getMultiProductGoodsAct(MultiProductGoodsActRequest request);

    /**
     * 根据商品sku以及地址，返回是否参与国补活动及国补信息
     * @param request
     * @return
     */
    Result<GovernmentSubsidyActResponse> queryGovernmentSubsidyAct(GetGovernmentSubsidyRequest request);

    /**
     * 获取商品可参加进行中活动
     *
     * @param request 请求参数
     * @return 响应
     */
    Result<GetProductGoodsActResponse> getProductGoodsAct(GetProductGoodsActRequest request);

    /**
     * 获取商品可参加进行中活动
     *
     * @param request 请求参数
     * @return 响应
     */
    Result<GetProductGoodsActResponse> getProductGoodsActV2(GetProductGoodsActRequestV2 request);

    /**
     * 获取商品可参加即将开始活动
     *
     * @param request 请求参数
     * @return 响应
     */
    Result<GetPreProductGoodsActResponse> getPreProductGoodsAct(GetPreProductGoodsActRequest request);

    /**
     * 获取商品活动优惠价（直降）
     *
     * @param request 请求参数
     * @return 响应
     */
    Result<GetProductActPriceResponse> getProductActPrice(GetProductActPriceRequest request);

    /**
     * 获取商品活动优惠价V2（直降）
     *
     * @param request 请求参数
     * @return 响应
     */
    Result<GetProductActPriceV2Response> getProductActPriceV2(GetProductActPriceV2Request request);

    /**
     * 获取商品活动优惠价
     *
     * @param request 请求参数
     * @return 响应
     */
    Result<GetGoodsActPriceResponse> getGoodsActPrice(GetGoodsActPriceRequest request);

    /**
     * 获取商品活动优惠价（直降）详情
     *
     * @param request 请求参数
     * @return 响应
     */
    Result<GetProductActPriceDetailResponse> getProductActPriceDetail(GetProductActPriceDetailRequest request);

    /**
     * 商城产品站活动接口（这是单商品的，升级版的为getProductActV2）
     *
     * @param request 请求参数
     * @return 响应
     */
    Result<GetProductActResponse> getProductAct(GetProductActRequest request);

    /**
     * 商城产品站单品页活动接口（批量查商品，getProductAct 的升级版）
     *
     * @param request 请求参数
     * @return 响应
     */
    Result<GetProductActV2Response> getProductActV2(GetProductActV2Request request);

    /**
     * 获取活动详情
     *
     * @param request 请求参数
     * @return 响应
     */
    Result<GetActivityDetailResponse> getActivityDetail(GetActivityDetailRequest request);

    /**
     * 批量获取活动详情
     *
     * @param request 请求参数
     * @return 响应
     * */
    Result<BatchGetActivityDetailResponse> batchGetActivityDetail(BatchGetActivityDetailRequest request);

    /**
     * 获取活动详情
     *
     * @param request 请求参数
     * @return 响应
     */
    Result<GetActivityAreaDetailResponse> getActivityAreaDetail(GetActivityAreaDetailRequest request);

    /**
     * 获取商品门店活动价 (直降 | 门店价）
     *
     * @param request 请求参数
     * @return 响应
     */
    Result<GetStoreActPriceResponse> getStoreActPrice(GetStoreActPriceRequest request);

    /**
     * 获取门店商品活动价 (直降 | 门店价）
     * <p>
     * 注：全量获取门店下已经配置了活动价（直降｜门店价）的商品价
     * </p>
     *
     * @param request 请求参数
     * @return 响应
     */
    Result<GetStoreGoodsActPriceResponse> getStoreGoodsActPrice(GetStoreGoodsActPriceRequest request);

    /**
     * 获取商品有效活动列表 caller 换新中台
     */
    Result<GetActivitysByGoodsResponse> getActivitysByGoods(GetActivitysByGoodsRequest request);

    /**
     * 加购时校验商品参加活动的有效性
     *
     * @param request 请求参数
     * @return 响应
     */
    Result<CheckProductsActResponse> checkProductsAct(CheckProductsActRequest request);
}
