package com.xiaomi.nr.promotion.api.dto.enums;

/**
 * 商品等级
 *
 * <AUTHOR>
 * @date 2024/10/09
 */
public enum GoodsLevelEnum {

    /**
     * 单品SKU
     */
    SKU(1, "SKU"),

    /**
     * 套装ID
     */
    PACKAGE_ID(3, "套装ID"),

    /**
     * 新套装ID
     */
    SSU_ID(5, "新套装ID"),

    ;

    /**
     * 值
     */
    private final int value;

    /**
     * 名称
     */
    private final String name;

    GoodsLevelEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

}
