package com.xiaomi.nr.promotion.api.dto.enums;

import java.io.Serializable;

/**
 * 价格来源枚举
 * <AUTHOR>
 * @date 2021/11/15
 */
public enum SourceTypeEnum implements Serializable {
    /**
     * 直降
     */
    ONSALE(20),
    /**
     * 门店价
     */
    STORE_PRICE(22);

    private final Integer type;

    SourceTypeEnum(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }
}
