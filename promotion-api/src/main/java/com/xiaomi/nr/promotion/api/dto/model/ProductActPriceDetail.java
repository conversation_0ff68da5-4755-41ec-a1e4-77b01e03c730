package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 活动优惠价详情
 *
 * <AUTHOR>
 * @date 2021/7/21
 */
@Data
public class ProductActPriceDetail implements Serializable {
    private static final long serialVersionUID = -6966714684127678694L;

    /**
     * 活动ID
     */
    private Long actId;

    /**
     * 活动价（分）
     */
    private Long price;

    /**
     * 1: 仅线上 2:仅线下 3：线上线下
     */
    private Integer offline;

    /**
     * 门店范围
     */
    private Integer orgScope;

    /**
     * 指定门店或区域ID
     */
    private List<String> selectOrgCodes;

    /**
     * 能参与的客户端列表
     */
    private List<String> selectClients;

    /**
     * 渠道列表
     */
    private List<Integer> channels;
}
