package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.ProductActGoodsItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 产品站可以参加的活动request
 *
 * <AUTHOR>
 * @date 2023/4/17
 */
@Data
public class GetProductActV2Request implements Serializable {

    private static final long serialVersionUID = -3250453652460185765L;

    /**
     * 应用id
     * 必须传【数据库存整数】
     */
    private Long clientId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 活动密码
     */
    private String accessCode;

    /**
     * 订单来源（交易定义的，对应活动后台配置规则里的订单来源），比如 常态销售common，常态大秒bigtap
     */
    private String saleSource;

    /**
     * 门店Code
     */
    private String orgCode;

    /**
     * 省ID since买赠
     */
    private Integer provinceId;

    /**
     * 市ID since买赠
     */
    private Integer cityId;

    /**
     * 区ID since买赠
     */
    private Integer districtId;

    /**
     * 镇ID
     */
    private Integer areaId;

    /**
     * 配送方式 1-现场购 2-物流
     */
    private Integer shipmentType;

    /**
     * 用户是否为F会员，true-是，false-否
     */
    private Boolean userIsFriend;

    /**
     * 商品列表
     */
    private List<ProductActGoodsItem> goodsList;

}
