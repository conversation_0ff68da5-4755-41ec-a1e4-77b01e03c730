package com.xiaomi.nr.promotion.api.dto.model;

import com.xiaomi.nr.promotion.api.dto.enums.SourceTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 门店价格详情
 *
 * <AUTHOR>
 * @date 2021/11/11
 */
@Data
public class StoreActPriceInfo implements Serializable {
    private static final long serialVersionUID = 8919798044360401021L;
    /**
     * 来源类型 20：直降 22：门店价
     */
    private SourceTypeEnum sourceType;

    /**
     * 活动价格
     */
    private Long actPrice;
}
