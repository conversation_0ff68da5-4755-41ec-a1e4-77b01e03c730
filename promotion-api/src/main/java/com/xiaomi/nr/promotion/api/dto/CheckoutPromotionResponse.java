package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.*;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 结算结果响应：购物车、结算请求返回值
 *
 * <AUTHOR>
 * @date 2021/3/18
 */
@Data
@ToString
public class CheckoutPromotionResponse implements Serializable {
    private static final long serialVersionUID = 349982009804127653L;
    /**
     * 详情
     */
    private String detail = "";

    /**
     * 商品列表
     */
    private List<CartItem> cartList = new ArrayList<>();

    /**
     * 活动信息
     */
    private List<PromotionInfo> promotions = new ArrayList<>();

    /**
     * 价格计算
     */
    private Summation summation;

    /**
     * 礼品卡信息
     */
    private List<Ecard> ecardList;

    /**
     * 券列表
     * 气门芯需求新增
     */
    private List<Coupon> couponList;

    /**
     * 邮费减免详情
     */
    private Map<String, String> exInfo = new HashMap<>();

    /**
     * 是否有北京优惠券
     */
    private Boolean hasBeijingcoupon = false;

    //*************下单checkout需返回***********
    /**
     * 订单优惠
     */
    private Order order;

    /**
     * 订单item优惠详情
     */
    private List<OrderItem> orderItems = new ArrayList<>();

    /**
     * 订单扩展信息
     */
    private OrderExtention orderExtention;

    /**
     * 红包可使用金额
     */
    private Long hasRedpacket = 0L;
}
