package com.xiaomi.nr.promotion.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 单品可以参加的活动请求参数
 *
 * <AUTHOR>
 * @date 2021/6/11
 */
@Data
public class GetProductGoodsActRequest implements Serializable{
    private static final long serialVersionUID = 7658234844244168323L;
    /**
     * sku 列表. 必须
     */
    private List<String> skuList;

    /**
     * ClientID. 选择，线上必须
     */
    private Long clientId;

    /**
     * 机构码. 选择，门店必须
     */
    private String orgCode;

    /**
     * 活动类型. 选择
     */
    private Integer activityType;

    /**
     * 促销渠道 1：小米商城 2：小米直营店 3：小米专卖店 4：小米授权店
     */
    private Integer channel;

    /**
     * 用户是否是F会员，1-是，2-否
     */
    private Integer userIsFriend;

    /**
     * 是否查询北京以旧换新补贴优惠
     */
    private Boolean queryPurchaseSubsidy = false;
}
