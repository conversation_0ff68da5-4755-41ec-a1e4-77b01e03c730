package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.ProductActPriceDetail;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 商品可以参加的活动优惠价响应
 *
 * <AUTHOR>
 * @date 2021/6/21
 */
@Data
@ToString
public class GetProductActPriceDetailResponse implements Serializable {
    private static final long serialVersionUID = 1118013190828458841L;

    /**
     * 活动价详情列表. key: skuPackage val: priceDetailList
     */
    private Map<String, List<ProductActPriceDetail>> actPriceDetailMap;
}
