package com.xiaomi.nr.promotion.api.dto.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @author： <EMAIL>
 * @time： 2025/4/8 15:53
 * @description：支付宝国补能效等级映射关系
 */
@Getter
@AllArgsConstructor
public enum AliPayGovEnergyLevel {
    GRADE_ONE("level_1", "gradeOne"),
    GRADE_TWO("level_2", "gradeTwo"),
    ;
    /**
     * 小米商城国补活动能效等级
     */
    private String xmShopEnergyLevel;
    /**
     * 支付宝国补能效等级
     */
    private String aliPayGovEnergyLevel;

    /**
     * 根据小米商城活动能效等级，映射支付宝国补能效等级
     *
     * @param xmShopEnergyLevel 小米商城活动能效等级
     * @return
     */
    public static AliPayGovEnergyLevel valueOfXmShopEnergyLevel(String xmShopEnergyLevel) {
        if (StringUtils.isBlank(xmShopEnergyLevel)) {
            return GRADE_TWO;
        }
        for (AliPayGovEnergyLevel value : values()) {
            if (value.getXmShopEnergyLevel().equals(xmShopEnergyLevel)) {
                return value;
            }
        }
        return GRADE_TWO;
    }
}
