package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.api.dto.model.Ecard;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.SummaryPriceInfo;
import com.xiaomi.nr.promotion.api.dto.model.Summation;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by wang<PERSON>yi on 2022/5/19
 */
@Data
public class CartPromotionResponse implements Serializable {
    /**
     * 详情
     */
    private String detail = "";

    /**
     * 商品列表
     */
    private List<CartItem> cartList = new ArrayList<>();

    /**
     * 活动信息
     */
    private List<PromotionInfo> promotions = new ArrayList<>();

    /**
     * 价格计算
     */
    private Summation summation = new Summation();

    /**
     * 礼品卡信息
     */
    private List<Ecard> ecardList = new ArrayList<>();

    /**
     * 邮费减免详情
     */
    private Map<String, String> exInfo = new HashMap<>();

    /**
     * 是否有北京优惠券
     */
    private Boolean hasBeijingcoupon = false;


    /**
     * 券列表
     */
    private List<Coupon> couponList = new ArrayList<>();

    /**
     * 购物车价格信息
     */
    private SummaryPriceInfo summaryPriceInfo = new SummaryPriceInfo();

}
