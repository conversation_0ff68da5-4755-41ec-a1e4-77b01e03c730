package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 直降限购规则信息
 *
 * <AUTHOR>
 * @date 2021/7/1
 */
@Data
public class OnsaleLimitRule implements Serializable {
    private static final long serialVersionUID = 3059289295080964621L;

    /**
     * 每人限量
     */
    private Long personLimit;

    /**
     * 每天每个门店限量
     */
    private Long dayLimitOne;

    /**
     * 每天全部门店总量
     */
    private Long dayLimitAll;

    /**
     * 活动期间每个门店限量
     */
    private Long activityLimitOne;

    /**
     * 活动期间全部门店限量
     */
    private Long activityLimitAll;
}
