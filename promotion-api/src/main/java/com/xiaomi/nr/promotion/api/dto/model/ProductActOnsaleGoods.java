package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 直降信息
 *
 * <AUTHOR>
 * @date 2021/6/11
 */
@Data
public class ProductActOnsaleGoods implements Serializable {
    private static final long serialVersionUID = 1535203439016987000L;

    /**
     * sku 或者package
     */
    private Long skuPackage;

    /**
     * "sku" 或者 "package"
     */
    private String level;

    /**
     * 直降后价格（分）
     */
    private Long lowerPrice;

    /**
     * sku 或者package的活动库存剩余数
     */
    private Integer limitNum;

    /**
     * 是否限制商品数量 （0-否 1-是 ）(线下特有)
     */
    private Integer isNumLimit;

    /**
     * 活动期间每人限购数
     */
    private Integer personLimit;

    /**
     * 限购规则
     */
    private OnsaleLimitRule limitRule;
}
