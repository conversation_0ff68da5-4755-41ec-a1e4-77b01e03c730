package com.xiaomi.nr.promotion.api.dto.model;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.enums.SkuCanJoinEnum;
import com.xiaomi.nr.promotion.api.dto.enums.SubsidyActivityCodeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/27 16:27
 */
@Data
public class SubsidyActivityContext {
    private Integer invalidCode = SubsidyActivityCodeEnum.VALID.getCode();

    private List<Long> activityIds = Lists.newArrayList();

    private PurchaseActivityInfo activityInfo;

    private Map<Long, List<PromotionIndexDto>> promotionIndexMap = Maps.newHashMap();

    private Map<Long, PurchaseActivityInfo> promotionInfoMap = Maps.newHashMap();

    private Integer skuCanJoin = SkuCanJoinEnum.NO_ACTIVITY.getCode();

    private String activityInvalidTag = StringUtils.EMPTY;

}
