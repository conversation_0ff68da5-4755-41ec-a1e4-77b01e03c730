package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import com.xiaomi.nr.promotion.api.dto.model.InstallmentDetail;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 根据条件校验是否能参与分期活动返回
 *
 * <AUTHOR>
 * @data 2024/09/30
 */
@Data
public class InstallmentConditionCheckResponse implements Serializable {
    private static final long serialVersionUID = 6504906025570925228L;

    @ApiDocClassDefine(value = "result", required = true, description = "是否符合条件")
    private Boolean result;

    @ApiDocClassDefine(value = "reason", required = false, description = "不符合条件的具体原因（非用户展示文案）")
    private String reason;

    @ApiDocClassDefine(value = "installmentList", required = true, description = "分期活动信息列表（无序）")
    private List<InstallmentDetail> installmentList;
}
