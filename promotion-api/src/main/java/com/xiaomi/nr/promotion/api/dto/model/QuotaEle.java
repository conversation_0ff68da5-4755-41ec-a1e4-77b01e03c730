package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 满足的配额
 *
 * <AUTHOR>
 * @date 2021/3/18
 */
@Data
@ToString
public class QuotaEle implements Serializable {
    private static final long serialVersionUID = -1780735111708352367L;

    /**
     * 满足的个数条件
     */
    private Integer count = 0;

    /**
     * 满足的金额条件
     */
    private Long money = 0L;

    /**
     * 类型，0-限额，1-限件，2-限额且限件,3-每满额，4-每满件，
     */
    private Integer type = 0;

    /**
     * 描述
     */
    private String desc;
}
