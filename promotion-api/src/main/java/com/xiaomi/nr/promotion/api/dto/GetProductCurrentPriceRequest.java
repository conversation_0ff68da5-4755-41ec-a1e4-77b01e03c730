package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.RedPacketUsedItem;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 获取商品的当前价格请求参数
 *
 * <AUTHOR>
 */
@Data
public class GetProductCurrentPriceRequest implements Serializable {
    private static final long serialVersionUID = 9114915284040091888L;

    /**
     * 用户id
     */
    private Long userId = 0L;

    /**
     * 应用Id
     */
    private Long clientId = 0L;

    /**
     * 购物车列表
     */
    private List<CartItem> cartList = new ArrayList<>();

    /**
     * 无码劵id
     */
    private Long couponId = 0L;

    /**
     * 无码劵id列表
     */
    private List<Long> couponIds;

    /**
     * 有码劵劵码
     */
    private String couponCode = "";

    /**
     * 使用的红包列表
     */
    private List<RedPacketUsedItem> redPacketUsedList = new ArrayList<>();

    /**
     * 订单id
     */
    private Long orderId;

}
