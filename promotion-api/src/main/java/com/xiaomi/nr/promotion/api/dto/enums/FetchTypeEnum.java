package com.xiaomi.nr.promotion.api.dto.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 领券方式
 *
 * <AUTHOR>
 * @date 2025/3/21 10:03
 */
@Getter
@AllArgsConstructor
public enum FetchTypeEnum {
    /**
     * 1-站内
     */
    IN_STATION_FETCH(1, 1, 2),

    /**
     * 2-站外
     */
    OUTSIDE_STATION_FETCH(2, 2, 3),

    /**
     * 3-无需领券
     */
    NOT_REQUIRED_FETCH(3, 3, 1);

    private final int value;
    private final int priority;
    private final int reorderPriority;

    public static int getPriorityByValue(int value) {
        for (FetchTypeEnum fetchTypeEnum : FetchTypeEnum.values()) {
            if (fetchTypeEnum.getValue() == value) {
                return fetchTypeEnum.getPriority();
            }
        }
        return Integer.MAX_VALUE;
    }

    public static int getReorderPriorityByValue(int value) {
        for (FetchTypeEnum fetchTypeEnum : FetchTypeEnum.values()) {
            if (fetchTypeEnum.getValue() == value) {
                return fetchTypeEnum.getReorderPriority();
            }
        }
        return Integer.MAX_VALUE;
    }
}
