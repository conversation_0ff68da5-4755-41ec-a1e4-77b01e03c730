package com.xiaomi.nr.promotion.api.dto.model.promotionprice;

import lombok.Data;

import java.io.Serializable;

/**
 * 折扣规则
 *
 * <AUTHOR>
 * @date 2023/2/22
 */
@Data
public class B2tVipDiscountRule implements Serializable {
    private static final long serialVersionUID = -7157436141219994922L;
    /**
     * 折扣信息
     */
    private DiscountRate discountRate;

    private GoodsDiscountRate goodsDiscountRate;

    @Data
    public static class GoodsDiscountRate {
        /** 非主推品折扣 */
        private Integer normalRate = 100;
        /** 主推品折扣 */
        private Integer recomRate;
    }
}
