package com.xiaomi.nr.promotion.api.service;

import com.xiaomi.nr.promotion.api.dto.GetGoodsSubsidyActRequest;
import com.xiaomi.nr.promotion.api.dto.GetGoodsSubsidyActResponse;
import com.xiaomi.nr.promotion.api.dto.GetValidInstallmentRequest;
import com.xiaomi.nr.promotion.api.dto.GetValidInstallmentResponse;
import com.xiaomi.nr.promotion.api.dto.InstallmentConditionCheckRequest;
import com.xiaomi.nr.promotion.api.dto.InstallmentConditionCheckResponse;
import com.xiaomi.nr.promotion.api.dto.QueryProductLayerSubsidyQualificationReq;
import com.xiaomi.nr.promotion.api.dto.QueryProductLayerSubsidyQualificationResp;
import com.xiaomi.youpin.infra.rpc.Result;

import java.util.List;

public interface ActivityGoodsConfigDubboService {

    /**
     * 分期免息和买赠二选一 生效商品判断
     *
     * @param productIdList 商品id列表
     * @return 其中处于生效的商品id
     */
    Result<List<Long>> installmentBuyGiftValidProduct(List<Long> productIdList);

    /**
     * 国补以旧换新活动接口
     *
     * @param request
     * @return
     */
    Result<GetGoodsSubsidyActResponse> getGoodsSubsidyAct(GetGoodsSubsidyActRequest request);

    /**
     * 湖北以旧换新活动接口
     *
     * @param request
     * @return
     */
    Result<GetGoodsSubsidyActResponse> getGoodsSubsidyActList(GetGoodsSubsidyActRequest request);

    /**
     * 根据条件获取有效的分期活动信息
     *
     * @param request 参数
     * @return Result
     */
    Result<GetValidInstallmentResponse> getValidInstallment(GetValidInstallmentRequest request);

    /**
     * 根据条件校验是否能参与有效分期活动
     *
     * @param request 参数
     * @return Result
     */
    Result<InstallmentConditionCheckResponse> installmentConditionCheck(InstallmentConditionCheckRequest request);


    /**
     * 查询产品站弹层补贴资格
     *
     * @param req 查询产品站弹层补贴资格的请求参数
     * @return 查询产品站弹层补贴资格的响应结果
     */
    Result<QueryProductLayerSubsidyQualificationResp> queryProductLayerSubsidyQualification(QueryProductLayerSubsidyQualificationReq req);
}
