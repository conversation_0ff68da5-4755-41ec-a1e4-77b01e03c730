package com.xiaomi.nr.promotion.api.service;

import com.xiaomi.nr.promotion.api.dto.GetValidSubsidyRequest;
import com.xiaomi.nr.promotion.api.dto.GetValidSubsidyResponse;
import com.xiaomi.youpin.infra.rpc.Result;

import java.util.List;
import java.util.Map;

public interface ActivityGoodsConfigDubboService {

    /**
     * 分期免息和买赠二选一 生效商品判断
     *
     * @param productIdList 商品id列表
     * @return 其中处于生效的商品id
     */
    Result<List<Long>> installmentBuyGiftValidProduct(List<Long> productIdList);



    /**
     * 根据条件获取有效的金融贴息活动信息
     *
     * @param request 参数
     * @return Result
     */
    Result<GetValidSubsidyResponse> getValidFinancialSubsidy(GetValidSubsidyRequest request);
}
