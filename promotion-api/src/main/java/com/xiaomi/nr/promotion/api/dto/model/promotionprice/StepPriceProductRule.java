package com.xiaomi.nr.promotion.api.dto.model.promotionprice;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 阶梯价规则
 *
 * <AUTHOR>
 * @date 2023/2/22
 */
@Data
public class StepPriceProductRule implements Serializable {
    private static final long serialVersionUID = -7157436141219994922L;
    /**
     * 返利底价，单位：分
     */
    private Long cashBackPrice;
    /**
     * 规则类型，STEP_PRICE（20）-阶梯价
     */
    private int ruleType;
    /**
     * 规格
     */
    private List<Specification> specificationList;
}
