package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.GoodsDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 获取优惠价请求
 *
 * <AUTHOR>
 * @date 2023/2/9
 */
@Data
public class GetPromotionPriceRequest implements Serializable {
    private static final long serialVersionUID = 7658234844244168323L;
    /**
     * 商品信息，最多50
     */
    private List<GoodsDto> goodsList;

    /**
     * 渠道
     */
    private Integer channel;

    /**
     * 用户等级, normal,VIP，SVIP
     */
    private String userLevel;

    /**
     * 门店编号
     */
    private String orgCode;

    /**
     * 下单时间
     */
    private Long orderTime;

    /**
     * 下单类型 0常态、1改配、2现车
     */
    private Integer submitType;
}
