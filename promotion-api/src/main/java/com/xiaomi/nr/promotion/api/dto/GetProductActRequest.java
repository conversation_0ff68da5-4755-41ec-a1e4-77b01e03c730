package com.xiaomi.nr.promotion.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 品页可以参加的活动request
 *
 * <AUTHOR>
 * @date 2021/5/12
 */
@Data
public class GetProductActRequest implements Serializable {
    private static final long serialVersionUID = 7658234844244168323L;

    /**
     * 应用id
     * 必须传【数据库存整数】
     */
    private Long clientId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 套装id
     */
    private String commodityId;

    /**
     * ssuType,新商品模式。ssuType=1代表新套装
     */
    private Integer ssuType;

    /**
     * sku
     */
    private String sku;

    /**
     * 活动密码
     */
    private String accessCode;

    /**
     * 订单来源，比如 常态销售common，常态大秒bigtap
     */
    private String saleSource;

    /**
     * 售价
     */
    private Long price;

    /**
     * 门店Code
     */
    private String orgCode;

    /**
     * 省ID since买赠
     */
    private Integer provinceId;

    /**
     * 市ID since买赠
     */
    private Integer cityId;

    /**
     * 区ID since买赠
     */
    private Integer districtId;

    /**
     * 镇ID
     */
    private Integer areaId;

    /**
     * 配送方式 1-现场购 2-物流
     */
    private Integer shipmentType;

    /**
     * 用户是否是F会员，1-是，2-否
     */
    private Integer userIsFriend;

    /**
     * 接口版本，内部使用，用于判断接口来源于getProductAct=1, getProductActV2=2
     */
    private Integer interfaceVersion;

}
