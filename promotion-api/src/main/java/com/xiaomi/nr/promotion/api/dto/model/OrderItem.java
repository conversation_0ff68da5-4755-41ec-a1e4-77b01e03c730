package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Data
@ToString
public class OrderItem implements Serializable {
    private static final long serialVersionUID = 321645882233666986L;
    /**
     * 用户id
     */
    private Long userId = 0L;
    /**
     * 订单id
     */
    private Long orderId = 0L;
    /**
     * 订单条目
     */
    private String itemId = "";
    /**
     * sku
     */
    private String sku = "";
    /**
     * 套装id
     */
    private String packageId = "";
    /**
     * 销售价
     */
    private Long standardPrice = 0L;
    /**
     * 加购价
     */
    private Long cartPrice = 0L;
    /**
     * 最终价
     */
    private Long finalPrice = 0L;
    /**
     * sku数量
     */
    private Integer skuCount = 0;
    /**
     * 原购物车item id
     */
    private String oriItemId = "";
    /**
     * 优惠扣减明细
     */
    private Map<String, List<ReduceDetail>> orderItemReduceList = new HashMap<>();
    /**
     * 优惠信息（非促销活动）
     */
    private List<PreferentialInfo> preferentialInfos = new ArrayList<>();

    /**
     * 门店使用，套装内子商品的id
     */
    private String unitId = "";

}
