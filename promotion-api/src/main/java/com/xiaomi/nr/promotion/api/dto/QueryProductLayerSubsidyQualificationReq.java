package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import com.xiaomi.nr.promotion.api.dto.model.carshop.Region;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/21 10:16
 */
@Data
public class QueryProductLayerSubsidyQualificationReq implements Serializable {
    private static final long serialVersionUID = 5068888549989198739L;

    /**
     * mid
     */
    @ApiDocClassDefine(value = "mid", required = true, description = "mid")
    private Long mid;

    /**
     * 商品 id
     */
    @ApiDocClassDefine(value = "id", required = true, description = "商品id")
    private Long id;

    /**
     * 收货地址
     */
    @ApiDocClassDefine(value = "region", required = true, description = "收货地址")
    private Region region;

    /**
     * 渠道
     */
    @ApiDocClassDefine(value = "channel", required = true, description = "渠道")
    private Integer channel;
}
