package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 产品站-入参商品信息
 *
 * <AUTHOR>
 * @date 2023/8/09
 */
@Data
public class ProductActGoodsItem implements Serializable {

    private static final long serialVersionUID = 319283193288324937L;

    /**
     * SKU或套装ID
     */
    private Long id;

    /**
     * 商品品级 package:套装ID sku:SKU
     */
    private String level;

    /**
     * ssuType,新商品模式。ssuType=1代表新套装
     */
    private Integer ssuType;

    /**
     * 销售价（单位分）
     */
    private Long salePrice;

    /**
     * 是否为虚拟商品
     */
    private Boolean virtual;

    /**
     * 商品销售模式，比如定金预售对应的标识
     */
    private String saleMode;

    /**
     * 商家类型，比如小米自营商品/POP对应的标识
     */
    private Integer businessType;
}
