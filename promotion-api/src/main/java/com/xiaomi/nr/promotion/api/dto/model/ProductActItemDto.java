package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 产品站-可用活动信息
 *
 * <AUTHOR>
 * @date 2023/8/09
 */
@Data
public class ProductActItemDto implements Serializable {

    private static final long serialVersionUID = 1088034827693330029L;

    /**
     * 活动ID
     */
    private String promotionId;

    /**
     * 活动类型
     */
    private String type;

    /**
     * 活动类型码
     */
    private String typeCode;

    /**
     * 活动分类
     */
    private String typeInfo;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 可用渠道分组（MISHOP：小米线上商城, MIHOME：小米线下门店）
     */
    private List<String> availableChannel;

    /**
     * 活动文案短规则，单品页优惠栏文案
     */
    private String descRuleIndex;

    /**
     * 活动规则文案，弹窗内文案（赠品的需要从policyNew内取）
     */
    private List<String> descRule;

    /**
     * 政策（满减、满折、包邮、下单立减）
     */
    private List<Policy> policys;

    /**
     * 政策（满赠、买赠、加价购）
     */
    private PolicyNew policyNew;

    /**
     * 扩展信息
     */
    private ProductActExtendDto extend;

}
