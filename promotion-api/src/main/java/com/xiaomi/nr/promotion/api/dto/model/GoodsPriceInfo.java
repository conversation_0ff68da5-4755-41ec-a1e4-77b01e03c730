package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 活动价信息
 *
 * <AUTHOR>
 * @date 2022/5/18
 */
@Data
public class GoodsPriceInfo implements Serializable {
    private static final long serialVersionUID = 3823168753227770115L;

    /**
     * 渠道范围：
     * 1:  商城 2: 直营 3:  专卖 4: 授权
     * 5: 部分门店
     */
    private Integer channelScope;

    /**
     * 门店列表
     */
    private List<String> selectOrgList;

    /**
     * 来源活动类型枚举： 20: 直降价  22: 门店价
     */
    private Integer actType;

    /**
     * 活动价
     */
    private Long actPrice;
}
