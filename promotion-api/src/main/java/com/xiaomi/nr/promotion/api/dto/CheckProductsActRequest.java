package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.CheckGoodsItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 加购时校验商品参加的活动是否有效请求参数
 *
 * <AUTHOR>
 */
@Data
public class CheckProductsActRequest implements Serializable {
    private static final long serialVersionUID = 1485303907574053192L;
    /**
     * 门店Code
     */
    private String orgCode;
    /**
     * 主品列表
     */
    private List<CheckGoodsItem> mainGoodsList;
    /**
     * 从品列表（赠品或者加价购商品）
     */
    private List<CheckGoodsItem> attachedGoodsList;
    /**
     * 活动id
     */
    private Long promotionId;
}
