package com.xiaomi.nr.promotion.api.service;

import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.youpin.infra.rpc.Result;

/**
 * 优惠中心主服务，提供中台用户端的优惠计算能力
 * <p>
 * 核心能力：
 * 1. 为购物车/结算页提供金额计算
 * 2. 商品金额计算
 *
 * <AUTHOR>
 * @date 2021/3/22
 */
public interface PromotionDubboService {

    /**
     * 优惠计算服务
     * 请求来源分别来自购物车列表、结算页
     * <p>
     * 购物车请求(cart list)
     * 刷新购物车、勾选购物车商品等操作会调用结算接口，重新计算优惠 以及 购物车商品可用优惠信息
     * <p>
     * - 结算页请求（checkout）
     * 购物车列表和订单结算页会调用接口计算优惠金额
     * 两个场景调用的区别：购物车列表请求参数不会传入与红包、优惠券、礼品卡优惠相关的参数
     * <p>
     * - 提交订单（submit）
     * 在结算页支付时，由订单调用
     * <p>
     * 其他信息：
     * 结算请求结构和返回结构，以及它们是如何返回的，请在对应的类中查看
     * {@link CheckoutPromotionRequest}
     * {@link CheckoutPromotionResponse}
     *
     * @param request 结算请求参数
     * @return 结算返回数据
     */
    Result<CheckoutPromotionResponse> checkoutPromotion(CheckoutPromotionRequest request);

    /**
     * 提交优惠结算
     * 仅订单服务有权调用
     * notes: 调用前需要先进行结算调用 {@link #checkoutPromotion}，进行资源占用
     * <p>
     * - 提交订单（submit）
     * 在结算页支付时，由订单调用。订单调用此接口重新结算优惠信息，优惠会锁定订单对应的优惠资源
     * 包括 限时购商品限购数量，优惠券，活动库存
     *
     * @param request 提交请求参数
     * @return 响应
     */
    Result<SubmitPromotionResponse> submitPromotion(SubmitPromotionRequest request);

    /**
     * 订单服务失败回滚优惠结算接口
     * 仅订单服务可用
     * <p>
     * - 占用资源回滚
     * 在流程异常或者资源释放时调用
     *
     * @param request 回滚请求参数
     * @return 响应
     */
    Result<RollbackPromotionResponse> rollbackPromotion(RollbackPromotionRequest request);

    /**
     * 订单服务失败回滚优惠结算接口
     *
     * <p>
     * - 占用资源回滚（部分）
     * 在流程异常或者资源释放时调用
     *
     * @param request 回滚请求参数
     * @return 响应
     */
    Result<PartRollbackPromotionResponse> partRollbackPromotion(PartRollbackPromotionRequest request);

    /**
     * 优惠结算服务V2
     * <p>
     * - 结算页请求（checkout）
     * 购物车列表和订单结算页会调用接口计算优惠金额
     * 两个场景调用的区别：购物车列表请求参数不会传入与红包、优惠券、礼品卡优惠相关的参数
     * <p>
     * 其他信息：
     * 结算请求结构和返回结构，以及它们是如何返回的，请在对应的类中查看
     * {@link CheckoutPromotionV2Request}
     * {@link CheckoutPromotionV2Response}
     *
     * @param request 结算请求参数
     * @return 结算返回数据
     */
    Result<CheckoutPromotionV2Response> checkoutPromotionV2(CheckoutPromotionV2Request request);

    /**
     * 购物车服务
     * <p>
     * 购物车请求(cart list)
     * 刷新购物车、勾选购物车商品等操作会调用结算接口，重新计算优惠 以及 购物车商品可用优惠信息
     * <p>
     * 其他信息：
     * 结算请求结构和返回结构，以及它们是如何返回的，请在对应的类中查看
     * {@link CartPromotionRequest}
     * {@link CartPromotionResponse}
     *
     * @param request 购物车请求参数
     * @return 结算返回数据
     */
    Result<CartPromotionResponse> cartCheckoutPromotion(CartPromotionRequest request);

    /**
     * 获取商品当前价格接口
     * 价保服务使用，用于对比商品下单时价格和当前价格
     * 优惠劵和红包加入金额计算
     *
     * @param request 获取商品的当前价格请求参数
     * @return 响应
     */
    Result<GetProductCurrentPriceResponse> getProductCurrentPrice(GetProductCurrentPriceRequest request);

    /**
     * 获取优惠价
     *
     * @param request 请求对象
     * @return 价格信息响应
     */
    Result<GetPromotionPriceResponse> getPromotionPrice(GetPromotionPriceRequest request);

    /**
     * 购物车分组结算服务
     * 按照分组规则结算购物车中的商品，
     * 返回值中仅包含商品维度的分摊信息，不返回订单维度的信息，如优惠券列表和结算统计信息
     * {@link CartPromotionRequest}
     * {@link CartPromotionResponse}
     *
     * @param request 购物车请求参数
     * @return 结算返回数据
     */
    Result<CartPromotionResponse> cartCheckoutPromotionByGroup(CartPromotionRequest request);
}
