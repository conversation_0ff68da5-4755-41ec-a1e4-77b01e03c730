package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by wangweiyi on 2022/5/19
 */
@Data
public class SummaryPriceInfo implements Serializable {
    /**
     * 商品总价格
     */
    private Long totalAmount = 0L;

    /**
     * 商品总减免金额
     */
    private Long reduceAmount = 0L;

    /**
     * 活动优惠明细
     */
    private Map<Integer, Long> promotionReduceMap = new HashMap<>();

}
