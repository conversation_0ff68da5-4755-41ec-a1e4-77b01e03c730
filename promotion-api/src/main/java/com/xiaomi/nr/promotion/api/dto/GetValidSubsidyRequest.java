package com.xiaomi.nr.promotion.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 单品可以参加的活动请求参数
 *
 * <AUTHOR>
 * @date 2021/6/11
 */
@Data
public class GetValidSubsidyRequest implements Serializable{
    private static final long serialVersionUID = 7658234844244168323L;
    /**
     * 活动渠道
     */
    private Integer channel;

    /**
     * 活动类型. 选择
     */
    private Integer activityType;

    /**
     * 用户标识
     */
    private List<String> userScope;


    /**
     * 大定订单时间
     */
    private Long orderTime;
    /**
     * 车型版本ssuId
     */
    private Long ssuId;

}
