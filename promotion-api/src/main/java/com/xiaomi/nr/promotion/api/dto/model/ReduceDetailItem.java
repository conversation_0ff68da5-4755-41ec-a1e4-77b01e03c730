package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 优惠活动信息
 *
 * <AUTHOR>
 * @date 2023/3/21
 */
@Data
@Accessors(chain = true)
public class ReduceDetailItem implements Serializable {
    private static final long serialVersionUID = 3307697382387614042L;
    /**
     * 优惠id
     */
    private Long promotionId;

    /**
     * 优惠类型
     */
    private Integer promotionType;

    /**
     * SSU_ID
     */
    private Long ssuId;

    /**
     * 总优惠(分）
     */
    private Long reduce;

    /**
     * 单件优惠金额（分）
     */
    private Long reduceSingle;

    /**
     * 扩展信息 json
     */
    private String extend;
    /**
     * BR单据
     */
    private String budgetApplyNo;

    /**
     * BR行号
     */
    private Long lineNum;
}
