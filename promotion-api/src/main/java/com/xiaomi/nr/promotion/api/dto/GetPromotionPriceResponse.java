package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.PromotionPriceDTO;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;

/**
 * 获取优惠价信息
 *
 * <AUTHOR>
 * @date 2023/2/9
 */
@Data
@ToString
public class GetPromotionPriceResponse implements Serializable {
    private static final long serialVersionUID = 1118013190828458841L;

    /**
     * 优惠价信息
     *
     * key: SSU_ID
     */
    private Map<Long, PromotionPriceDTO> priceMap;
}
