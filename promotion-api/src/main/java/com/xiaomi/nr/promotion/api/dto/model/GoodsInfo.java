package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 优惠商品信息
 *
 * <AUTHOR>
 * @date 2021/6/11
 */
@Data
public class GoodsInfo implements Serializable {
    private static final long serialVersionUID = 3890637664124313531L;

    /**
     * sku
     */
    private String sku;

    /**
     * sku
     */
    private Long goodsId;

    /**
     * 优惠价（分）
     */
    private Long cartPrice;

    /**
     * 配送方式 1-现场购 2-物流
     */
    private Integer shipmentType;

    /**
     * 标价（分）
     */
    private Long marketPrice;

    /**
     * 赠品是否有活动数量，1-是，2-否
     */
    private Integer actNumLimit;

    /**
     * 赠品基数 ( 买赠赠品存在）
     */
    private Long baseNum;

    /**
     * 剩余数量
     */
    private Long remainActNum;

    /**
     * 商品优惠描述
     */
    private String descRule;
}
