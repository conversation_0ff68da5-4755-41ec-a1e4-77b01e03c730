package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2024/12/19 14:17
 */
@Data
public class GovernmentActivityDto {
    /**
     * 优惠金额
     */
    private Long reduceMoney;
    /**
     * 资格品类信息,KEY：itemId,主副品强绑定，key取主品itemId
     */
    private Map<String,QualifyDetailInfo> qualifyDetailInfoMap;
    /**
     * 能效等级
     */
    private String cateLevel;
    /**
     *   国补模式：1-政府模式 2-银联模式 3-极简模式
     */
    private Integer subsidyMode;
    /**
     *   上报城市ID
     */
    private Integer reportCity;
    /**
     * 上报城市标签(xx以旧换新)
     */
    private String reportTag;
    /**
     *  开票规则： 1-国补金额优惠开票，2-国补金额优惠不开票
     */
    private Integer invoiceRule;
    /**
     *  开票主体：(深圳小米景明)
     */
    private Integer invoiceCompanyId;
}
