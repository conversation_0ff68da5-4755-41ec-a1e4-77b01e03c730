package com.xiaomi.nr.promotion.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 单品可以参加即将开始活动请求参数
 *
 * <AUTHOR>
 * @date 2021/10/29
 */
@Data
public class GetPreProductGoodsActRequest implements Serializable {
    private static final long serialVersionUID = 7658234844244168323L;
    /**
     * sku 列表. 必须
     */
    private List<String> skuPackageList;

    /**
     * ClientID. 选择，线上必须
     */
    private Long clientId;

    /**
     * 机构码. 选择，门店必须
     */
    private String orgCode;

    /**
     * 活动类型. 选择
     */
    private Integer activityType;
}
