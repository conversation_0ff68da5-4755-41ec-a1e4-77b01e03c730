package com.xiaomi.nr.promotion.api.service;

import com.xiaomi.nr.promotion.api.dto.PromotionCacheRequest;
import com.xiaomi.nr.promotion.api.dto.PromotionCacheResponse;
import com.xiaomi.youpin.infra.rpc.Result;

public interface PromotionCacheDubboService {

    /**
     * 手动刷新本机缓存
     * @return
     * @throws Exception
     */
    Result<String> refreshLocalCache(PromotionCacheRequest request) throws Exception;

    /**
     * 手动刷新本机缓存
     *
     * @return
     * @throws Exception
     */
    Result<PromotionCacheResponse> searchLocalCache(PromotionCacheRequest request) throws Exception;
}
