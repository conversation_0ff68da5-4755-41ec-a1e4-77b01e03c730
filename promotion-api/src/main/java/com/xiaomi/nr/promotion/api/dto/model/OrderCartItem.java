package com.xiaomi.nr.promotion.api.dto.model;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * submit拆分为sku，订单级别的item
 *
 * <AUTHOR>
 */
@Data
public class OrderCartItem implements Serializable {
    private static final long serialVersionUID = 3823168753227770115L;
    /**
     * 购物车每个条目唯一id
     */
    private String itemId = "";

    /**
     * 商品sku
     */
    private String sku = "";

    /**
     * 套装id
     */
    private String packageId = "";

    /**
     * SSU
     */
    private Long ssuId;

    /**
     * ssuType: 0-单品ssu, 1-套装ssu，2-主附品ssu
     */
    private Integer ssuType;

    /**
     * 商品业务子类型，13-工时商品，14-配件商品
     */
    private Integer bizSubType;
    /**
     * 付费类型：自费/保内/理赔/内部结算
     * @deprecated
     */
    @Deprecated
    private Integer payType;
    /**
     * 工时单价
     * @deprecated
     */
    @Deprecated
    private Long unitPrice;
    /**
     * 工时(小时)
     * @deprecated
     */
    @Deprecated
    private BigDecimal workHour;
    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品数量 必须>=0
     */
    private Integer count = 0;

    /**
     * 销售价 必须>=0
     */
    private Long standardPrice = 0L;

    /**
     * 购物车价 必须>=0，只有直降和门店价可以变动
     */
    private Long cartPrice = 0L;

    /**
     * 定金金额
     */
    private Long prePrice = 0L;

    /**
     * 总减免金额，不包含直降优惠的金额（返回参数使用） 必须>=0,
     */
    private Long reduceAmount = 0L;

    /**
     * 加入购物车来源 ( F码、大秒、加价购、0元分期、赠品、秒杀、礼品卡、保险、购买资格、预售、众筹、合约机）对应Karos的购物车gettype，目前只用了bargain、gift
     */
    private String source = "";

    /**
     * 加价购、赠品活动id
     */
    private String sourceCode = "";

    /**
     * 主附品强绑定，主品/附品参加的直降活动id, key：子品序号， value:活动ID
     */
    private Map<Integer, String> onSalePromotionIdMap = new HashMap<>();

    /**
     * 扩展，json
     */
    private String properties = "";

    /**
     * 父商品item id
     */
    private String parentItemId = "";

    /**
     * 更新时间
     */
    private Long updateTime = 0L;

    /**
     * 显示类型(f码、秒杀、0元分期、加价购、赠品、礼品卡)
     */
    private Integer displayType = 0;

    /**
     * 原购物车itemId
     */
    private String oriItemId = "";

    /**
     * 可参加活动code
     */
    private List<String> accessCode = new ArrayList<>();

    /**
     * true表示不可以参加活动
     */
    private Boolean cannotJoinAct = false;

    /**
     * true表示不可以用优惠券
     */
    private Boolean cannotUseCoupon = false;

    /**
     * 市场价
     */
    private Long marketPrice = 0L;

    /**
     * true表示不可以使用礼品卡
     */
    private Boolean cannotUseEcard = false;

    /**
     * item当前最多可用礼品卡金额(注:乘count之后的总值
     */
    private Long maxUseEcardAmount = 0L;

    /**
     * 不可以参加的活动类型
     */
    private List<Long> cannotJoinActTypes = new ArrayList<>();

    /**
     * 不可以用的劵类型
     */
    private List<Long> cannotUseCouponTypes = new ArrayList<>();

    /**
     * 对应Karos的购物车source
     */
    private String saleSource = "";

    /**
     * 对应Karos的购物车source
     */
    private List<String> saleSources = new ArrayList<>();

    /**
     * 该物品是否可以使用红包
     */
    private Boolean cannotUseRedPacket = false;

    /**
     * 赠品或加价购商品对应组id
     */
    private Long groupId = 0L;

    /**
     * 直降定金金额
     */
    private Long onSaleBookingPrice = 0L;

    /**
     * 是否参与直降
     */
    private boolean joinOnsale = false;

    /**
     * 参与改价的活动类型ID, 0代表未参与改价活动
     */
    private Integer changePriceActType = 0;

    /**
     * 原始购物车价
     */
    private Long originalCartPrice = 0L;

    /**
     * 优惠明细
     */
    private Map<String, List<ReduceDetail>> orderItemReduceList = new HashMap<>();

    /**
     * 优惠信息（非促销活动）
     */
    private List<PreferentialInfo> preferentialInfos = new ArrayList<>();

    /**
     * 门店使用，套装内子商品的id
     */
    private String unitId = "";

    /**
     * 商品类型: 1: 主推品 2：常规品
     */
    private Integer goodsType;

    /**
     * 商品所属部门
     * 1:   销一
     * 2:  销二
     * 3:  销三
     */
    private Integer department;

    /**
     * 该物品是否不可以使用汽车积分
     */
    private Boolean cannotUsePoint = false;


    /**
     * 是否可以参加调价
     * @deprecated
     */
    @Deprecated
    private boolean canAdjustPrice = false;

    /**
     * 维保相关信息
     * 维保业务场景 - 工时去重&维保抵扣券 使用
     */
    private MaintenanceInfo maintenanceInfo;

    @ApiDocClassDefine(
            value = "selected",
            required = false,
            description = "是否选中，影响最终checkoutPrice价格",
            defaultValue = "false",
            example = "false"
    )
    private Boolean selected;

    /**
     * 组套Id
     */
    private String servicePackId;

    @ApiDocClassDefine(
            value = "isDepositDiff",
            required = false,
            description = "是否补差价",
            defaultValue = "false",
            example = "false"
    )
    private Boolean isDepositDiff;
}
