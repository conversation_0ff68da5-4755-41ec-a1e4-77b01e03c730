package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 活动策略
 *
 * <AUTHOR>
 * @date 2021/3/19
 */
@Data
@ToString
public class PolicyNew implements Serializable {
    private static final long serialVersionUID = -3465355811534786764L;

    /**
     * 各优先级的政策
     */
    private List<PolicyNewLevel> policy = new ArrayList<>();

    /**
     * 活动的类型
     */
    private Long type = 0L;
}
