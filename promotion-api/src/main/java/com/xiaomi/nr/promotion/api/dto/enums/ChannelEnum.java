package com.xiaomi.nr.promotion.api.dto.enums;

import com.google.common.collect.ImmutableSet;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 渠道枚举
 *
 * <AUTHOR>
 * @date 2023/2/22
 */
public enum ChannelEnum {
    /**
     * 小米商城
     */
    MISHOP(1, "小米商城"),
    /**
     * 直营店
     */
    DIRECT(2, "直营店"),
    /**
     * 专卖店
     */
    SPECIALTY(3, "专卖店"),
    /**
     * 授权店
     */
    AUTHORIZED(4, "授权店"),
    /**
     * 京东到家-直营
     */
    JD_HOME_DIRECT(5, "京东到家-直营"),
    /**
     * 京东到家-专卖
     */
    JD_HOME_SPECIALTY(6, "京东到家-专卖"),
    /**
     * 京东到家-授权
     */
    JD_HOME_AUTHORIZED(7, "京东到家-授权"),
    /**
     * 有品
     */
    YOUPIN(10, "有品"),

    /**
     * 汽车整车
     *
     */
    CAR_VEHICLE(11, "汽车整车"),

    /**
     * 汽车维保售后
     *
     */
    CAR_MAINTENANCE_REPAIR(12,"汽车维保售后"),

    /**
     * 汽车商城
     */
    CAR_SHOP(13, "汽车商城"),

    /**
     * 团购-直客
     */
    B2T_C_CUSTOMER(20, "团购-直客"),
    /**
     * 团购-经销商
     */
    B2T_GOV_BIG_CUSTOMER(21, "团购-经销商-政企"),

    B2T_MIJIA_BIG_CUSTOMER(22,"团购-经销商-米家"),

    /**
     * 饿了么-直营
     */
    ELM_DIRECT(51, "饿了么-直营"),

    /**
     * 饿了么-专卖
     */
    ELM_SPECIALTY(52, "饿了么-专卖"),

    /**
     * 饿了么-授权
     */
    ELM_AUTHORIZED(53, "饿了么-授权"),

    /**
     * 美团-直营
     */
    MT_DIRECT(54, "美团-直营"),

    /**
     * 美团-专卖
     */
    MT_SPECIALTY(55,"美团-专卖"),

    /**
     * 美团-授权
     */
    MT_AUTHORIZED(56, "美团-授权"),

    /**
     * 抖音-直营
     */
    DY_DIRECT(57, "抖音-直营"),

    /**
     * 抖音-专卖
     */
    DY_SPECIALTY(58, "抖音-专卖"),
    ;

    private static final Set<Integer> THIRD_BUY_GIFT_CHANNELS = ImmutableSet.of(
            ChannelEnum.JD_HOME_AUTHORIZED.getValue(),
            ChannelEnum.JD_HOME_DIRECT.getValue(),
            ChannelEnum.JD_HOME_SPECIALTY.getValue(),
            ChannelEnum.ELM_AUTHORIZED.getValue(),
            ChannelEnum.ELM_DIRECT.getValue(),
            ChannelEnum.ELM_SPECIALTY.getValue(),
            ChannelEnum.MT_AUTHORIZED.getValue(),
            ChannelEnum.MT_DIRECT.getValue(),
            ChannelEnum.MT_SPECIALTY.getValue(),
            ChannelEnum.DY_DIRECT.getValue(),
            ChannelEnum.DY_SPECIALTY.getValue()
    );

    private final int value;

    private final String name;
    private static final Map<Integer, ChannelEnum> MAPPING_VALUE = new HashMap<>();

    static {
        ChannelEnum[] var0 = values();
        int var1 = var0.length;

        for (int var2 = 0; var2 < var1; ++var2) {
            ChannelEnum e = var0[var2];
            MAPPING_VALUE.put(e.value, e);
        }

    }

    ChannelEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static ChannelEnum getByValue(int value) {
        return MAPPING_VALUE.get(value);
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    /**
     * 是否是三方渠道
     * @param channel
     * @return
     */
    public static boolean isThirdChannel(Integer channel) {
        if (channel == null) {
            return false;
        }
        return THIRD_BUY_GIFT_CHANNELS.contains(channel);
    }
}
