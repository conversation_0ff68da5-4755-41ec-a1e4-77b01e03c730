package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.GoodsPriceDto;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 商品可以参加的活动优惠价响应
 *
 * <AUTHOR>
 * @date 2022/10/20
 */
@Data
@ToString
public class GetProductActPriceV2Response implements Serializable {
    private static final long serialVersionUID = 1118013190828458841L;

    /**
     * 活动价
     */
    private Map<Long, GoodsPriceDto> goodsPriceMap;
}
