package com.xiaomi.nr.promotion.api.dto;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.model.ActivitySubsidyInfo;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 金融贴息可参与的活动
 *
 * <AUTHOR>
 * @date 2025年1月7日19:22:52
 */
@Data
public class GetValidSubsidyResponse implements Serializable {
    private static final long serialVersionUID = 1118013190828458841L;

    /**
     * 可参与的活动list
     */
    private List<ActivitySubsidyInfo> activityList = Lists.newArrayList();
}
