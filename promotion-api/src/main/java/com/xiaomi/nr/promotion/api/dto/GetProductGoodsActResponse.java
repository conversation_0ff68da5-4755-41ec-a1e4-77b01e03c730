package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 单品可以参加的活动响应
 *
 * <AUTHOR>
 * @date 2021/6/11
 */
@Data
public class GetProductGoodsActResponse implements Serializable {
    private static final long serialVersionUID = 1118013190828458841L;

    /**
     * sku对应可参加活动列表
     * key: sku，对应参数中sku  val: 可参加活动列表
     */
    private Map<String, List<ProductActInfo>> skuActMap;
}
