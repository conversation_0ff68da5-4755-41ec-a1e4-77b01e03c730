package com.xiaomi.nr.promotion.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 加购时校验商品参加的活动是否有效请求参数
 *
 * <AUTHOR>
 */
@Data
public class ProductActJoinInfoRequest implements Serializable {
    private static final long serialVersionUID = -9119317907583350103L;
    /**
     * 门店Code
     */
    private Long mid;
    /**
     * 主品列表
     */
    private String vid;
    /**
     * 活动id
     */
    private List<Long> promotionIds;
}
