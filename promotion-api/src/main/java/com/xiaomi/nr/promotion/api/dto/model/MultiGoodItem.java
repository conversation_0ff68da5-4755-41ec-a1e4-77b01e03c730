package com.xiaomi.nr.promotion.api.dto.model;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MultiGoodItem implements Serializable {

    private static final long serialVersionUID = 504464134755361688L;

    @ApiDocClassDefine(value = "ssuId", required = true, description = "ssuId")
    private Long ssuId;

    @ApiDocClassDefine(value = "ssuType", required = true, description = "ssuType")
    private Integer ssuType;

    @ApiDocClassDefine(value = "marketPrice", required = true, description = "marketPrice")
    private Long marketPrice;

    @ApiDocClassDefine(value = "ssuTag", required = true, description = "ssuTag")
    private List<String> ssuTag;
}
