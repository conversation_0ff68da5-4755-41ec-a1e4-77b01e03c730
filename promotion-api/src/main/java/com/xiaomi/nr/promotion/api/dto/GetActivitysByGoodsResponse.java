package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.GoodsActivityInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 获取商品有效活动列表
 */
@Data
public class GetActivitysByGoodsResponse implements Serializable {
    private static final long serialVersionUID = -5473730261181838397L;

    /**
     * 活动列表 key-sku或者套装id
     */
    private Map<String,List<GoodsActivityInfo>> activitys;
}
