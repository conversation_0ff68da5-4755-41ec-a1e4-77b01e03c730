package com.xiaomi.nr.promotion.api.dto;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.EcardConsumeItemList;
import com.xiaomi.nr.promotion.api.dto.model.ThirdPromotion;
import com.xiaomi.nr.promotion.api.dto.model.carshop.Region;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 购物车、结算请求参数
 *
 * <AUTHOR>
 * @date 2021/3/16
 */
@Data
@ToString
public class CheckoutPromotionRequest implements Serializable {
    private static final long serialVersionUID = 4007090310500153474L;
    /**
     * 请求来源
     * 购物车/结算页 1-checkout
     * 订单提交 2-submit
     */
    private Integer sourceApi;

    /**
     * 购物车商品列表
     */
    private List<CartItem> cartList;

    /**
     * 渠道
     * 1：小米商城
     * 2：米家-直营店
     * 3：米家-专卖店
     * 4:  授权店
     * 20:  团购- 直客
     * 21:   团购- 经销商
     */
    private Integer channel;

    /**
     * 应用id
     * 必须传【数据库存整数】
     */
    private Long clientId = 0L;

    /**
     * 用户id
     * submit必须传【数据库存整数】
     */
    private Long userId = 0L;

    /**
     * 优惠券id值
     * 用逗号分开，couponCodes和couponIds两个至少传一个【数据库存整数】;
     */

    private List<Long> couponIds;

    /**
     * 明码优惠券
     * 多个用逗号分开，couponIds和couponCodes两个至少传一个
     */
    private List<String> couponCodes;

    /**
     * 礼品卡
     */
    private List<String> ecardIds;

    /**
     * submit接口必须传入orderid，checkout可以不传【数据库存整数】
     */
    private Long orderId = 0L;

    /**
     * 提单时间
     */
    private Long orderTime;

    /**
     * 未参加的加价购数量
     */
    private Long bargainSize = 0L;

    /**
     * submit的时候,是否需要落库,如是true 则不落库,如果是false,则落库
     */
    private Boolean noSaveDbSubmit = false;

    /**
     * 礼品卡使用小单号扣费详情
     */
    private Map<String, EcardConsumeItemList> ecardConsumeDetail;

    /**
     * 购物车能否使用红包
     */
    private Boolean useRedPacket = false;

    /**
     * 是否计算红包，默认不处理
     */
    private Boolean calculateRedpacket = false;

    /**
     * 是否使用北京优惠券
     */
    private Boolean useBeijingcoupon = false;

    /**
     * 机构id（线下门店传）
     */
    private String orgCode = "";

    /**
     * uid，mid不传，为手机号则传"mobile"
     */
    private String uidType = "";

    /**
     * 区域，城市id
     */
    private Long cityId = 0L;

    /**
     * 购物模式
     * 1-物流，2-现场购，3-物流和现场购混合
     */
    private Long shoppingMode = 0L;

    /**
     * 履约方式
     */
    private Integer shipmentId = -1;

    /**
     * 运费
     */
    private Long shipmentExpense = 0L;

    /**
     * 业务身份
     * 米网-"mishop" 云店-"estore" 电视商城-"tvshop"
     */
    private String globalBusinessPartner = "";

    /**
     * 是否来自价保
     */
    private Boolean fromPriceProtect = false;

    /**
     * 联通华盛用户余额
     */
    private Long unicomHsBalance;
    /**
     * 联通华盛用户付款码
     */
    private String payBarCode;

    /**
     * 用户是否是F会员，1-是，2-否
     */
    @Deprecated
    private Integer userIsFriend;
    /**
     * 三方优惠信息
     */
    private List<ThirdPromotion> thirdPromotions;

    /**
     * 用户等级, normal,VIP，SVIP
     */
    private String userLevel;

    /**
     * 是否使用积分，true:是 false:否
     */
    private Boolean usePoint = false;

    /**
     * 积分可抵扣金额（分，结算页展示给用户的那个金额）
     */
    private Long pointReduceAmount = 0L;

    /**
     * 订单已使用的优惠券
     */
    private Long usedCouponId;

    /**
     * 订单提交类型
     * @see com.xiaomi.nr.promotion.api.dto.enums.SubmitTypeEnum
     */
    private Integer submitType = 0;

    /**
     * 汽车vid
     */
    private String vid;

    /**
     * 用户身份证
     */
    private String idCard;
    /**
     * 能否使用北京以旧换新补贴优惠
     */
    private Boolean usePurchaseSubsidy = false;
    /**
     * 收货地址
     */
    private Region region;

    /**
     * 四要素加密信息
     */
    private String personalInfo;

    /**
     * 查询用户是否为F会员
     */
    private Boolean fMemberFlag = false;

    /**
     * 查询用户是否为学生
     */
    private Boolean studentFlag = false;

    /**
     * 查询用户是否属于海葵id 人群包
     */
    private List<String> haikuiIds = Lists.newArrayList();

    /**
     * 查询用户是否属于画像服务 人群包
     */
    private List<String> ruleIds = Lists.newArrayList();
}
