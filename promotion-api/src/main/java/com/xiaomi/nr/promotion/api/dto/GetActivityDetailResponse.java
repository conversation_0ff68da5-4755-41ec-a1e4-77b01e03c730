package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.*;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 活动详情detail响应
 *
 * <AUTHOR>
 * @date 2021/11/23
 */
@Data
@ToString
public class GetActivityDetailResponse implements Serializable {
    private static final long serialVersionUID = 8467053773432680731L;

    /**
     * 活动ID
     */
    private  Long id;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 活动开始时间(秒）
     */
    private Long unixStartTime;

    /**
     * 活动结束时间(秒）
     */
    private Long unixEndTime;

    /**
     * 条件列表
     */
    private List<Policy> policys;

    /**
     * 活动主品列表
     */
    private List<GoodsItem> goodsItemList;

    /**
     * 直降信息. 当type为直降时候存在
     */
    private List<ProductActOnsaleGoods> onsaleGoods;

    /**
     * 赠品信息. 当type为赠品时候存在
     */
    private ProductActGoods giftGoods;

    /**
     * 赠品信息. 当type为赠品时候存在
     */
    private List<ProductActGoods> giftGoodsV2;

    /**
     * 加价购信息. 当type为加价购时候存在
     */
    private ProductActGoods bargainGoods;

    /**
     * 活动规则文案
     */
    private List<String> descRule;

    /**
     * 活动规则(最优阶梯） 短描述规则
     */
    private String descRuleIndex;

    /**
     * 扣减商品
     */
    private List<ReduceGoodsInfo> reduceGoods;

}
