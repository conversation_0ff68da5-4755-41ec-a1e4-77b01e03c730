package com.xiaomi.nr.promotion.api.dto;

import com.xiaomi.nr.promotion.api.dto.model.GoodsPriceInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 商品可以参加的活动优惠价响应
 *
 * <AUTHOR>
 * @date 2022/5/18
 */
@Data
public class GetGoodsActPriceResponse implements Serializable {
    private static final long serialVersionUID = 1118013190828458841L;

    /**
     * 活动价
     * key: sku or packageId.  val: 活动优惠价(分）
     */
    private Map<String, List<GoodsPriceInfo>> actPriceMap;
}
