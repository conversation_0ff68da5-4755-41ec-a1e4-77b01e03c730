package com.xiaomi.nr.promotion.api.dto.model;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 优惠活动信息
 *
 * <AUTHOR>
 * @date 2023/2/14
 */
@Data
public class PromotionInfoDTO implements Serializable {
    private static final long serialVersionUID = 3371139019872495134L;

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动类型：71: 阶梯价 70: 折扣
     */
    private Integer promotionType;

    /**
     * 活动名称
     */
    private String promotionName;

    /**
     * 促销文案
     */
    private String promotionText;

    /**
     * 开始时间（秒）
     */
    private Long startTime;

    /**
     * 结束时间 （秒）
     */
    private Long endTime;

    /**
     * 活动规则
     */
    private String rule;
    
    /**
     * 买赠&加价购 扩展字段
     */
    private PolicyNew policyNew;

    @ApiDocClassDefine(
            value = "stockMap",
            required = false,
            description = "库存map，具有主从关系组合"
    )
    private Map<Long, Map<Long, Boolean>> stockMap;

    /**
     * 优惠后价格
     */
    private Long promotionPrice;
}
