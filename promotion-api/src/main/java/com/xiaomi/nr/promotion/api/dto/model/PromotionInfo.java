package com.xiaomi.nr.promotion.api.dto.model;

import com.google.common.collect.Lists;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 活动信息
 *
 * <AUTHOR>
 * @date 2021/3/18
 */
@Data
@ToString
public class PromotionInfo implements Serializable {
    private static final long serialVersionUID = -6966714684127678694L;

    /**
     * 活动ID
     */
    private String promotionId = "";

    /**
     * 图标
     */
    private String icon = "";

    /**
     * 活动类型，比如加价购活动填8
     */
    private String type = "";

    /**
     * 活动分类，比如"加价购活动"
     */
    private String typeInfo = "";

    /**
     * 比如"sale"
     */
    private String typeCode = "";

    /**
     * 活动name
     */
    private String title = "";

    private String limit = "";

    /**
     * 活动扩展信息,对于加价购 返回可以选择的商品和最多可以赠送的最大个数 + 还可以送的
     */
    private String extend = "";

    /**
     * 该活动是否已经参加
     */
    private Integer joined = 0;

    /**
     * 是否为强父子关系，在活动缓存中可以取到
     */
    private Integer isOnlyGoods = 0;

    /**
     * 导致该活动可以参加的条件item组
     */
    private List<String> parentItemId = new ArrayList<>();

    /**
     * 该活动作用于哪些item组
     */
    private List<String> joinedItemId = new ArrayList<>();

    /**
     * 包邮，1-包邮，2-非包邮
     */
    private Integer postfree = 0;

    /**
     * 是否满件包邮
     */
    private Boolean postfreeIsnum = false;

    /**
     * 还差N件包邮
     */
    private Integer needsNum4PostFree = 0;

    /**
     * 满几件包邮
     */
    private Integer postfreeNum = 0;

    private Long urlSetting = 0L;

    private String urlContent = "";

    private String descTitle = "";

    private String descUrl = "";

    /**
     * 满足的配额,增加活动满件满多少金额限件限额等信息
     */
    private List<QuotaEle> quotaEles = new ArrayList<>();

    /**
     * 完整的活动政策标签
     */
    protected List<String> descPolicy = new ArrayList<>();

    /**
     * 活动规则文案
     */
    private List<String> descRule;

    /**
     * 活动规则(最优阶梯） 短描述规则
     */
    private String descRuleIndex;

    /**
     * 描述短名称这样
     */
    private String descShortName = "";

    /**
     * 是否显示凑单
     */
    private Boolean descIsShowAddOnItem = false;

    /**
     * 实施的政策
     */
    private List<Policy> policys = new ArrayList<>();

    private Integer isRecommendForceAddPrice = 0;

    /**
     * 融合版新的政策
     */
    private PolicyNew policyNew;

    /**
     * 每组最大的参与次数
     */
    private Integer joinCounts = 0;

    /**
     * 商品数量限制规则，如果是0则代表不限制（如果没有限制，则没有此字段）
     */
    private NumLimitRule numLimitRule;

    /**
     * 活动是否互斥 0-否 1-是
     */
    private Integer activityMutexLimit = 0;

    /**
     * 互斥活动优先级（活动id，优先级是倒序）
     */
    private List<String> activityMutex = new ArrayList<>();

    /**
     * 频次限制，1没有限制，2本次活动仅能参加一次，3每天可以参加一次
     */
    private Integer frequent = 0;

    /**
     * 活动库存
     */
    private Long totalLimitNum = 0L;

    /**
     * 1：线上 2：线下 3：都支持
     */
    private Integer offline;

    /**
     * 是否F会员专属赠品 1-是，2-否
     */
    private Integer isFMember;

    /**
     * 活动结束时间戳（秒）
     */
    private Long endTime;

    @ApiDocClassDefine(value = "canSelect", required = false, description = "是否可选，用于选择活动")
    private Boolean canSelect = false;

    @ApiDocClassDefine(value = "checked", required = false, description = "是否选中")
    private Boolean checked = false;

    @ApiDocClassDefine(value = "validGoodsList", required = false, description = "商品白名单")
    private List<Long> validGoodsList = Lists.newArrayList();

    @ApiDocClassDefine(value = "inValidGoodsList", required = false, description = "商品黑名单")
    private List<Long> inValidGoodsList = Lists.newArrayList();

    @ApiDocClassDefine(
            value = "stockMap",
            required = false,
            description = "库存map，具有主从关系组合"
    )
    private Map<Long, Map<Long, Boolean>> stockMap;

    /**
     * 促销活动标签
     */
    private List<Integer> promotionTags;
}
