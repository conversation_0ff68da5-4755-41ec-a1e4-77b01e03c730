package com.xiaomi.nr.promotion.api.dto.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 券信息
 *
 * <AUTHOR>
 * @date 2021/5/6
 */
@Data
public class CouponInfo implements Serializable {
    private static final long serialVersionUID = 6414164484436140272L;
    /**
     * 无码
     */
    private Long couponId = 0L;
    /**
     * 有码（新旧）
     */
    private String couponCode = "";
    /**
     * 是否为线下,实际上没用核销时取得是
     */
    private Integer offline = 0;
    /**
     * 券状态
     */
    @Deprecated
    private String stat = "";
    /**
     * 是指生成券的模板比如2334这种
     */
    @Deprecated
    private String typ = "";
    /**
     * 券减免的钱
     */
    private Long reduceMoney = 0L;
    /**
     * 券减免的邮费
     */
    private Long reduceExpress = 0L;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 优惠券类型 @see
     */
    private Integer couponType;
}
