<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>promotion</artifactId>
        <groupId>com.xiaomi.nr</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>promotion-api</artifactId>
    <version>${com.xiaomi.nr.promotion.api.version}</version>


    <properties>
        <com.xiaomi.youpin.infra-rpc.version>2.0.0-CNZONE-SNAPSHOT</com.xiaomi.youpin.infra-rpc.version>
        <com.xiaomi.youpin.coloregg.version>1.4-SNAPSHOT</com.xiaomi.youpin.coloregg.version>
        <com.xiaomi.youpin.aop.version>1.4-SNAPSHOT</com.xiaomi.youpin.aop.version>
        <com.xiaomi.youpin.common.version>1.7-SNAPSHOT</com.xiaomi.youpin.common.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>youpin-infra-rpc</artifactId>
            <version>${com.xiaomi.youpin.infra-rpc.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>coloregg</artifactId>
            <version>${com.xiaomi.youpin.coloregg.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>aop</artifactId>
            <version>${com.xiaomi.youpin.aop.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>common</artifactId>
            <version>${com.xiaomi.youpin.common.version}</version>
        </dependency>

        <!-- miapi -->
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>dubbo-docs-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>org.apache.dubbo</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <compilerVersion>1.8</compilerVersion>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.xiaomi.mone</groupId>
                <artifactId>jdk-feature-maven-plugin</artifactId>
                <version>1.0.0-turbo-SNAPSHOT</version>
                <executions>
                    <execution>
                        <phase>none</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>