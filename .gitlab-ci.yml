image: cr.d.xiaomi.net/cicd/maven-jdk1.8:openjdk
variables:
  MAVEN_OPTS: -Dmaven.repo.local=.m2/repository
include:
  - project: mit/infra/ci-templates
    ref: master
    file: maven/scan.gitlab-ci.yml
cache:
  paths:
    - .m2/repository/
stages:
  - scan   # 执行sonar扫描
  - build
build:
  stage: build
  script:
    - mvn clean package -U -DskipTests
  artifacts:
    paths:
      - promotion-api/target/classes/
      - promotion-common/target/classes/
      - promotion-server/target/classes/
      - promotion-service/target/classes/