package com.xiaomi.nr.promotion.model.common;

import com.google.gson.annotations.SerializedName;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.B2tStepPriceRule;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.StepPriceProductRule;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 促销拓展信息
 *
 * <AUTHOR>
 * @date 2021/3/29
 */
@Data
public class PromotionExtend {
    /**
     * skuMap
     */
    @SerializedName("list")
    private Map<String, String> list;

    /**
     * skuList
     */
    @SerializedName("sku_list")
    private List<String> skuList;

    /**
     * 当前次数
     */
    @SerializedName("current_count")
    private Integer curCount = 0;

    /**
     * 当前每组次数
     */
    @SerializedName("group_current_count")
    private List<GroupCurCount> groupCurCount;

    /**
     * 最大次数
     */
    @SerializedName("max_count")
    private Integer maxCount = 0;

    /**
     * 购物车价格
     */
    @SerializedName("cart_price")
    private Long cartPrice = 0L;

    /**
     * 选择类型
     */
    @SerializedName("select_type")
    private String selectType = "";

    /**
     * 是否忽略库存
     */
    @SerializedName("ignore_stock")
    private Integer ignoreStock = 0;

    /**
     * 退款时赠品、加价购价值
     */
    @SerializedName("refund_deduction")
    private Long refundValue = 0L;

    /**
     * 1:强制顺序挂靠主商品
     */
    @SerializedName("force_parent")
    private Integer forceParent = 0;

    /**
     * 活动码
     */
    @SerializedName("access_code")
    private String accessCode;

    /**
     * 活动组信息
     */
    @SerializedName("skugroups_list")
    private List<SkuGroupBean> skuGroupsList;

    /**
     * 直降的扩展信息
     */
    @SerializedName("onsale_extend")
    private OnsaleExtendInfo onsaleExtend;

    /**
     * 立减的扩展信息
     */
    @SerializedName("reduce_extend")
    private ReduceExtendInfo reduceExtend;

    /**
     * 指定门店降价的扩展信息
     */
    @SerializedName("partonsale_extend")
    private PartOnsaleExtend partOnsaleExtend;

    /**
     * 阶梯价拓展
     */
    @SerializedName("stepprice_extend")
    private List<StepPriceProductRule> steppriceExtend;
    /**
     * 渠道价拓展
     */
    @SerializedName("channelprice_extend")
    private List<StepPriceProductRule> channelpriceExtend;

    /**
     * 盲订定金商品sku
     */
    @SerializedName("blindBookingSku")
    private Long blindBookingSku;
}
