package com.xiaomi.nr.promotion.annotation.load;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotatedTypeMetadata;

import java.util.List;

@Slf4j
public class PromotionLoadCondition implements Condition {

    /**
     * 数据列表：
     * 1. 环境配置列表（baseline）
     * 2. 注释配置列表（match）
     *
     * 逻辑判断：
     * 1. 环境配置列表必须有，没有则所有数据不加载
     * 2. 注释配置列表可以没有，没有则跳过校验
     * 3. 数据同时存在，则做匹配，匹配成功加载，匹配失败不加载
     *
     * @param context
     * @param metadata
     * @return
     */
    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        // check annotation
        AnnotationAttributes attributes = AnnotationAttributes.fromMap(
                metadata.getAnnotationAttributes(PromotionCondition.class.getName())
        );
        if (attributes == null) {
            log.warn("promotion condition annotation is not enabled");
            return false;
        }
        String[] bizList = attributes.getStringArray("value");
        if (bizList == null || bizList.length == 0) {
            log.warn("promotion condition annotation config is empty");
            return true;
        }


        // check environment
        Environment environment = context.getEnvironment();
        // 获取当前激活的业务配置
        String property = environment.getProperty(PromotionLoadConstants.PROFILES_KEY);
        if (property == null || property.isEmpty()) {
            log.warn("promotion profiles is not configured");
            return false;
        }
        List<String> actBizList = List.of(property.split(","));

        // match biz
        for (String s : bizList) {
            if (actBizList.contains(s)) {
                return true;
            }
        }

        return false;
    }
}
