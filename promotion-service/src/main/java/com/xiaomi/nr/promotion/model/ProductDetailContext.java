package com.xiaomi.nr.promotion.model;

import com.xiaomi.nr.promotion.api.dto.model.ProductPriceInfo;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 计算所有价格的上下文
 */
@Data
public class ProductDetailContext {

    /**
     * 售卖渠道
     */
    private String channel;

    /**
     * 会员标识
     */
    private Integer vipLevel;

    /**
     * 外部资源
     */
    private Map<ResourceExtType, ExternalDataProvider<?>> externalDataMap = new HashMap<>();

    /**
     * 促销商品价格。 key:ssuId, value: 促销价格信息
     */
    private Map<Long, ProductPriceInfo> promotionPriceMap = new HashMap<>();

}
