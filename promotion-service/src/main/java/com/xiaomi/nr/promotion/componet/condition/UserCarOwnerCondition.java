package com.xiaomi.nr.promotion.componet.condition;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.activity.pool.CarPromotionInstancePool;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.enums.PromotionTag;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.enums.UserCarPermitEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyReducePromotionConfig;
import com.xiaomi.nr.promotion.resource.external.CarUserPermitExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.util.CarShopBuyReduceUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/10 21:00
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class UserCarOwnerCondition extends Condition {

    private PromotionToolType promotionType;

    private PromotionTag promotionTag;


    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        // 如果不是车商城则，不用走这个活动
        if (request.getChannel() != ChannelEnum.CAR_SHOP.getValue()) {
            return true;
        }
        if (promotionType != PromotionToolType.BUY_REDUCE) {
            return true;
        }
        UserCarPermitEnum userPermit = getUserPermitFromContext(context);
        return CarShopBuyReduceUtil.scopeMatch(promotionTag, userPermit);
    }

    private UserCarPermitEnum getUserPermitFromContext(LocalContext context) {
        Map<ResourceExtType, ExternalDataProvider<?>> externalDataMap = context.getExternalDataMap();
        CarUserPermitExternalProvider provider =
                (CarUserPermitExternalProvider) externalDataMap.get(ResourceExtType.USER_CAR_PERMIT);
        try {
            return provider.getData();
        } catch (BizError e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof BuyReducePromotionConfig)) {
            return;
        }
        BuyReducePromotionConfig buyReducePromotionConfig = (BuyReducePromotionConfig) config;
        this.promotionType = config.getPromotionType();
        this.promotionTag = buyReducePromotionConfig.getActivityCarTag();
    }
}
