package com.xiaomi.nr.promotion.mq.consumer;

import cn.hutool.core.collection.CollectionUtil;
import com.xiaomi.hera.trace.annotation.Trace;
import com.xiaomi.nr.promotion.constant.MqConstant;
import com.xiaomi.nr.promotion.enums.OrderItemTypeEnum;
import com.xiaomi.nr.promotion.mq.consumer.entity.*;
import com.xiaomi.nr.promotion.resource.impl.ResourceManager;
import com.xiaomi.nr.promotion.resource.model.OrderDataInfo;
import com.xiaomi.nr.promotion.resource.model.ResourceManageContext;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.model.ReturnType;
import com.xiaomi.nr.promotion.rpc.order.OrderQueryServiceProxy;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 订单消息
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(value="rocketmq.topic.order.carLife.enabled", havingValue="true")
@RocketMQMessageListener(
        topic = "${rocketmq.topic.order.carLife}",
        selectorExpression = "OrderClosed || OrderRefund",
        nameServer = "${rocketmq.order.name-server}",
        accessKey = "${rocketmq.access-key}",
        secretKey = "${rocketmq.secret-key}",
        consumerGroup = "${rocketmq.topic.order.carLife.consumerGroup}")
public class OrderCarLifeMessageListener implements RocketMQListener<String> {

    @Autowired
    private ResourceManager resourceManager;
    @Autowired
    private OrderQueryServiceProxy orderQueryServiceProxy;

    @Override
    @Trace
    public void onMessage(String s) {
        long startTime = System.currentTimeMillis();
        log.info("OrderCarLifeMessageListener receive startTime:{} msg:{}", startTime, s);
        try {
            OrderMessage closeMessage = GsonUtil.fromJson(s, OrderMessage.class);
            if (closeMessage == null) {
                log.error("OrderCarLifeMessageListener data error. message:{}", s);
                return;
            }
            String event = closeMessage.getEvent();
            // 关单
            if (Objects.equals(MqConstant.TOPIC_ORDER_CLOSED, event)) {
                onHandleCloseMessage(s);
                // 退款
            } else if (Objects.equals(MqConstant.TOPIC_ORDER_REFUND, event)) {
                onHandleRefundMessage(s);
            }
        } catch (Throwable e) {
            log.error("OrderCarLifeMessageListener error. message:{}", s, e);
            throw new RuntimeException(e);
        } finally {
            log.info("OrderCarLifeMessageListener receive ws:{} msg:{}", System.currentTimeMillis() - startTime, s);
        }
    }

    /**
     * 对SA订单进行关闭，通过资源进行判断
     * 如果为可关闭的订单，则通过resourceManager进行资源关闭
     *
     * @param s 订单关闭消息
     */
    private void onHandleCloseMessage(String s) throws BizError {
        OrderMessage closeMessage = GsonUtil.fromJson(s, OrderMessage.class);
        if (closeMessage == null) {
            log.error("OrderCarLifeMessageListener data error. message:{}", s);
            return;
        }
        String saOrderId = closeMessage.getIndex();
        saOrderId = StringUtils.replace(saOrderId, "SA", "");
        if (StringUtils.isEmpty(saOrderId) || !StringUtils.isNumeric(saOrderId)) {
            log.warn("OrderMessageListener not support data. message:{} saOrderId:{}", s, saOrderId);
            return;
        }
        long orderId = Long.parseLong(saOrderId);
        ResourceManageContext context = ResourceManageContext.fromMessage(orderId);
        resourceManager.rollback(context);
    }

    /**
     * 如果大单下所有子单关闭了，则可以关闭
     * - 当前只退券和红包
     *
     * @param s 订单关闭消息
     */
    private void onHandleRefundMessage(String s) throws BizError {
        OrderRefundMessage refundMessage = GsonUtil.fromJson(s, OrderRefundMessage.class);
        if (refundMessage == null) {
            log.error("OrderCarLifeMessageListener data error. message:{}", s);
            return;
        }
        String body = refundMessage.getBody();
        OrderRefundBody refundBody = GsonUtil.fromJson(body, OrderRefundBody.class);
        if (refundBody == null || refundBody.getOrderData() == null) {
            log.warn("OrderCarLifeMessageListener body is null . message:{}", s);
            return;
        }
        OrderData orderData = refundBody.getOrderData();

        long orderId = Long.parseLong(orderData.getOrgOrderId());
        ResourceManageContext context = ResourceManageContext.fromMessage(orderId);
        List  resourceTypes = new ArrayList();
        if (Objects.equals(orderData.getOrderType(), 2)) {//退积分
            log.warn("OrderCarLifeMessageListener orderType is 2 message:{}", s);
            OrderDataInfo orderDataInfo = OrderDataInfo.builder()
                    .orderId(orderId)
                    .refundNo(Long.parseLong(refundMessage.getOdOrderId()))
                    .returnPointAmount(orderData.getIntegralAmount().multiply(new BigDecimal("100")).intValue())
                    .returnType(ReturnType.RETURN).build();

            resourceTypes.add(ResourceType.POINT);
            context.setOrderData(orderDataInfo);

        }

        // 售前退资源
        if (Objects.equals(orderData.getOrgOrderId(), refundMessage.getOdOrderId())) {
            log.warn("OrderCarLifeMessageListener onHandleRefundMessage odOrderId equal to orgOrderId. message:{}", s);
            resourceTypes.add(ResourceType.COUPON);
            resourceTypes.add(ResourceType.USER_JOIN_ACT_NUM);
        } else {

            // 售后退资源
            List<ItemDetail> itemDetail = refundBody.getItemDetail();
            if (CollectionUtil.isNotEmpty(itemDetail)) {
                boolean valid = itemDetail.stream().allMatch(detail -> (OrderItemTypeEnum.isValidOrderItemType(detail.getItemType())));
                if (valid) {
                    log.info("OrderCarLifeMessageListener onHandleRefundMessage has virtual goods. message:{}", s);
                    resourceTypes.add(ResourceType.COUPON);
                    resourceTypes.add(ResourceType.USER_JOIN_ACT_NUM);
                }
            }
        }


        if (resourceTypes.size() == 0) {
            return;
        }
        context.setResourceTypes(resourceTypes);
        resourceManager.partRollback(context);
    }
}
