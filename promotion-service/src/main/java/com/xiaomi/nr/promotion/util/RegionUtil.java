package com.xiaomi.nr.promotion.util;

import com.xiaomi.nr.promotion.api.dto.model.carshop.Region;

import java.util.List;

public class RegionUtil {

    /**
     * 检查目标收货地址是否有效
     *
     * 1、province匹配
     * 2、city = 0时，返回true，否则继续匹配city
     *
     * @param target 目标区域
     * @param source 源区域列表
     * @return 如果目标区域与源区域列表中的任何一个匹配，则返回true；否则返回false
     */
    public static boolean isAnyMatch(Region target, List<Region> source) {
        for (Region region : source) {
            if (region.getProvince() == null || region.getCity() == null || region.getDistrict() == null || region.getArea() == null) {
                return false;
            }
            if (region.getProvince() != 0 && region.getProvince().equals(target.getProvince())) {
                if (region.getCity() == 0 || region.getCity().equals(target.getCity())) {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean isAddressMatch(Region target, List<com.xiaomi.nr.md.promotion.admin.api.dto.activity.Region> source) {
        for (com.xiaomi.nr.md.promotion.admin.api.dto.activity.Region region : source) {
            if (region.getProvince() == null || region.getCity() == null || region.getDistrict() == null || region.getArea() == null) {
                return false;
            }
            if (region.getProvince() != 0 && region.getProvince().equals(target.getProvince())) {
                if (region.getCity() == 0 || region.getCity().equals(target.getCity())) {
                    return true;
                }
            }
        }
        return false;
    }
}
