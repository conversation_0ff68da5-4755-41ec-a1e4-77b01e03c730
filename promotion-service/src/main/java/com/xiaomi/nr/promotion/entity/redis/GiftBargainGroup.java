package com.xiaomi.nr.promotion.entity.redis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * 赠品加价购组
 *
 * <AUTHOR>
 * @date 2021/5/14
 */
@Data
public class GiftBargainGroup implements Serializable {
    private static final long serialVersionUID = 136141474356126904L;

    /**
     * sku
     */
    @SerializedName("sku")
    private Long sku = 0L;
    
    /**
     * ssuId
     */
    private Long ssuId;

    /**
     * GID
     */
    @SerializedName("goods_id")
    private Long goodsId;

    /**
     * 标价
     */
    @SerializedName("market_price")
    private Long marketPrice = 0L;

    /**
     * 购物车价
     */
    @SerializedName("cart_price")
    private Long cartPrice = 0L;

    /**
     * 配送方式 1-现场购 2-物流
     */
    @SerializedName("shipment_type")
    private Integer shipmentType = 0;

    /**
     * 退款时赠品、加价购价值
     */
    @SerializedName("refund_deduction")
    private Long refundValue = 0L;

    /**
     * 赠品数量（since 买赠）
     */
    @SerializedName("gift_limit_num")
    private Long giftLimitNum;

    /**
     * 加价购数量（since 加价购）
     */
    @SerializedName("bargain_limit_num")
    private Long bargainLimitNum;

    /**
     * 赠品基数（since 买赠）
     */
    @SerializedName("gift_base_num")
    private Long giftBaseNum = 1L;

    /**
     * 规则描述
     */
    private String descRule;
}
