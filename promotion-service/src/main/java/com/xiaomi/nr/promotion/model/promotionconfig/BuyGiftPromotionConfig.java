package com.xiaomi.nr.promotion.model.promotionconfig;

import com.xiaomi.nr.promotion.entity.redis.FillGoodsGroup;
import com.xiaomi.nr.promotion.entity.redis.Goods;
import lombok.ToString;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 买赠配置
 *
 * <AUTHOR>
 * @date 2021/5/31
 */
@ToString
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class BuyGiftPromotionConfig extends MultiPromotionConfig {
    /**
     * 主商品列表
     */
    private FillGoodsGroup includeGoodsGroup;

    /**
     * 赠品信息
     */
    private Goods giftGoods;

    /**
     * 是否F会员专属赠品 1-是，2-否
     */
    private Integer isFMember;

    /**
     * 配置URL
     */
    private String configUrl;

    public FillGoodsGroup getIncludeGoodsGroup() {
        return includeGoodsGroup;
    }

    public void setIncludeGoodsGroup(FillGoodsGroup includeGoodsGroup) {
        this.includeGoodsGroup = includeGoodsGroup;
    }

    public Goods getGiftGoods() {
        return giftGoods;
    }

    public void setGiftGoods(Goods giftGoods) {
        this.giftGoods = giftGoods;
    }

    public Integer getIsFMember() {
        return isFMember;
    }

    public void setIsFMember(Integer isFMember) {
        this.isFMember = isFMember;
    }

    public String getConfigUrl() {
        return configUrl;
    }

    public void setConfigUrl(String configUrl) {
        this.configUrl = configUrl;
    }
}
