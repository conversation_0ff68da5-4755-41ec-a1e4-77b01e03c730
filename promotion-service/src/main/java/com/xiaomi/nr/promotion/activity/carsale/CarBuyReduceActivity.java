package com.xiaomi.nr.promotion.activity.carsale;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.activity.AbstractActivityTool;
import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.GetProductActRequest;
import com.xiaomi.nr.promotion.api.dto.MultiProductGoodsActRequest;
import com.xiaomi.nr.promotion.api.dto.enums.PromotionTag;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.componet.action.carsale.CarBuyReduceAction;
import com.xiaomi.nr.promotion.componet.condition.carsale.CarBuyReduceCondition;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.ProductDetailContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.ActRespConverter;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.GoodsReduceInfo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/17 21:20
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class CarBuyReduceActivity extends AbstractActivityTool implements ActivityTool {

    private Map<String, GoodsReduceInfo> reduceInfoMap;

    @Setter
    @Getter
    private PromotionTag promotionTag;



    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine.condition(CarBuyReduceCondition.class)
                .action(CarBuyReduceAction.class);
    }

    /**
     * 构建优惠信息
     *
     * @param context 上下文
     * @return 优惠信息
     */
    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) {
        PromotionInfo promotionInfo = buildDefaultPromotionInfo(context);
        promotionInfo.setPromotionTags(Lists.newArrayList(promotionTag.getValue()));
        return promotionInfo;
    }

    @Override
    public List<CheckGoodsItem> checkProductsAct(CheckProductsActRequest request, Map<String, GoodsHierarchy> goodsHierarchyMap) throws BizError {
        return null;
    }

    /**
     * 获取产品站信息
     *
     * @param clientId       应用ID
     * @param orgCode        门店Code
     * @param skuPackageList 活动类别
     */
    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) {
        // 不支持透出
        return Collections.emptyMap();
    }

    /**
     * 获取活动详情
     *
     * @return 活动详情
     */
    @Override
    public ActivityDetail getActivityDetail() {
        List<ReduceGoodsInfo> reduceGoods = reduceInfoMap.values().stream()
                .map(ActRespConverter::convertReduceInfo)
                .toList();
        ActivityDetail detail = getBasicActivityDetail();
        detail.setReduceGoods(reduceGoods);
        return detail;
    }

    /**
     * 获取产品站活动信息：获取PromotionInfo
     *
     * @param request   产品站信息
     * @param hierarchy 商品层级关系
     * @param isOrgTool 是否来源门店工具
     * @return 促销信息
     */
    @Override
    public PromotionInfo getProductAct(GetProductActRequest request, GoodsHierarchy hierarchy, boolean isOrgTool) throws BizError {
        return null;
    }

    @Override
    public PromotionInfoDTO getMultiProductAct(MultiGoodItem goodItem, MultiProductGoodsActRequest request,
                                               ProductDetailContext context) throws BizError {
//        GoodsReduceInfo reduceInfo = reduceInfoMap.get(goodItem.getSsuId().toString());
//        if (Objects.isNull(reduceInfo)) {
//            return null;
//        }
//        PromotionInfoDTO promotionInfo = initPromotionInfo();
//        promotionInfo.setPromotionText(ActivityTypeEnum.BUY_REDUCE.getName());
//        Map<Long, ProductPriceInfo> promotionPriceMap = context.getPromotionPriceMap();
//        ProductPriceInfo productPriceInfo =
//                promotionPriceMap.computeIfAbsent(goodItem.getSsuId(), k -> new ProductPriceInfo());
//        long totalPrice = Optional.ofNullable(goodItem.getMarketPrice()).orElse(0L);
//        long actPrice = totalPrice - reduceInfo.getReducePrice() < 0 ? 0 : totalPrice - reduceInfo.getReducePrice();
//        productPriceInfo.setActPrice(actPrice);
//        promotionInfo.setPromotionPrice(actPrice);
//        promotionInfo.setReducePrice(reduceInfo.getReducePrice());
//        promotionInfo.setPromotionTag(Lists.newArrayList(promotionTag.getValue()));
        return null;
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof BuyReducePromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        BuyReducePromotionConfig promotionConfig = (BuyReducePromotionConfig) config;
        this.reduceInfoMap = promotionConfig.getBuyReduceInfoMap();
        this.promotionTag = promotionConfig.getActivityCarTag();

        return true;
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.BUY_REDUCE;
    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR;
    }
}
