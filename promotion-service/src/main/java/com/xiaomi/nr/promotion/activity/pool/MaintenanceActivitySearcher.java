package com.xiaomi.nr.promotion.activity.pool;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-01-03 16:33
*/
@Slf4j
@Component
public class MaintenanceActivitySearcher extends AbstractActivitySearcher {

    @Autowired(required = false)
    private CarPromotionInstancePool carPromotionInstancePool;

    @Override
    public List<Long> doSearchIndex(ActSearchParam actSearchParam) {
        List<ActSearchParam.GoodsInSearch> goodsList = actSearchParam.getGoodsList();
        if (CollectionUtils.isEmpty(goodsList)) {
            return Collections.emptyList();
        }
        List<String> goodsIdList = goodsList.stream()
                .map(goods -> String.valueOf(goods.getSsuId()))
                .collect(Collectors.toList());

        // 获取skuPackage可参加活动
        Set<Long> actIdInGoods = carPromotionInstancePool.getCurrentActIds(goodsIdList);

        // 获取渠道可参加活动
        List<Long> actIdInChannel = carPromotionInstancePool.getCurrentChannelActIds(Lists.newArrayList(actSearchParam.getChannel()));

        // 做交集
        actIdInGoods.retainAll(actIdInChannel);

        // 根据有序活动基线做交集，保证获取活动的有序性
        List<Long> sortedActIds = carPromotionInstancePool.getSortedActIds(new ArrayList<>(actIdInGoods));
        return new ArrayList<>(sortedActIds);
    }

    @Override
    public List<ActivityTool> doSearchAct(List<Long> actIds) {
        return carPromotionInstancePool.getCurrentTools(actIds);
    }

    @Override
    public ActivityTool doSearchAct(Long actId) {
        return carPromotionInstancePool.getCurrentTool(actId);
    }

    @Override
    public List<ActivityTool> doFilter(ActSearchParam actSearchParam, List<ActivityTool> activityTools) {
        return activityTools.stream()
                .filter(tool -> checkTime(tool, Boolean.FALSE))
                .filter(activityTool -> activityTool.getBizPlatform() == getBizPlatform())
                .collect(Collectors.toList());
    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.MAINTENANCE_REPAIR;
    }

    public List<ActSearchParam.GoodsInSearch> createSearchGoods(List<CartItem> cartItemList) {
        List<ActSearchParam.GoodsInSearch> resultList = new ArrayList<>();

        for (CartItem item : cartItemList) {
            ActSearchParam.GoodsInSearch goodsInSearch = new ActSearchParam.GoodsInSearch();
            goodsInSearch.setSsuId(item.getSsuId());
            resultList.add(goodsInSearch);
        }
        return resultList;
    }
}
