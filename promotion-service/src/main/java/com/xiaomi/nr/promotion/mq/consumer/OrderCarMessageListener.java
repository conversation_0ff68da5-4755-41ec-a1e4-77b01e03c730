package com.xiaomi.nr.promotion.mq.consumer;

import com.xiaomi.nr.promotion.mq.consumer.entity.OrderMessage;
import com.xiaomi.nr.promotion.resource.impl.ResourceManager;
import com.xiaomi.nr.promotion.resource.model.ResourceManageContext;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 车销售 - 订单消息
 *
 * <AUTHOR>
 * @date 2024/2/27
 */
@Slf4j
@Component
@ConditionalOnProperty(value="rocketmq.topic.order.car.enabled", havingValue="true")
@RocketMQMessageListener(
        topic = "${rocketmq.topic.order.car}",
        selectorExpression = "OrderClosed || OrderPreLockedCancel || OrderLockedCancel",
        nameServer = "${rocketmq.order.name-server}",
        accessKey = "${rocketmq.access-key}",
        secretKey = "${rocketmq.secret-key}",
        consumerGroup = "${rocketmq.topic.order.car.consumerGroup}")
public class OrderCarMessageListener implements RocketMQListener<String> {

    @Autowired
    private ResourceManager resourceManager;

    @Override
    public void onMessage(String s) {
        long startTime = System.currentTimeMillis();
        log.info("OrderCarMessageListener receive startTime:{} msg:{}", startTime, s);
        try {
            OrderMessage closeMessage = GsonUtil.fromJson(s, OrderMessage.class);
            if (closeMessage == null) {
                log.error("OrderCarMessageListener data error. message:{}", s);
                return;
            }
            onRefundHandle(s);
        } catch (Throwable e) {
            log.error("OrderCarMessageListener error. message:{}", s, e);
            throw new RuntimeException(e);
        } finally {
            log.info("OrderCarMessageListener receive ws:{} msg:{}", System.currentTimeMillis() - startTime, s);
        }
    }

    /**
     * 对SA订单进行关单，进行资源退还
     * - 目前只支持退无码券，以后有其他优惠且需要退还的，再加上
     *
     * @param s 订单取消消息
     */
    private void onRefundHandle(String s) throws BizError {
        OrderMessage closeMessage = GsonUtil.fromJson(s, OrderMessage.class);
        if (closeMessage == null) {
            log.error("OrderCarMessageListener data error. message:{}", s);
            return;
        }

        String saOrderId = closeMessage.getSaOrderId();
        saOrderId = StringUtils.replace(saOrderId, "SA", "");
        if (StringUtils.isEmpty(saOrderId) || !StringUtils.isNumeric(saOrderId)) {
            log.warn("OrderCarMessageListener not support data. message:{} saOrderId:{}", s, saOrderId);
            return;
        }

        long orderId = Long.parseLong(saOrderId);
        ResourceManageContext context = ResourceManageContext.fromMessage(orderId);
        context.setResourceTypes(List.of(ResourceType.COUPON));
        resourceManager.refund(context);
    }

}
