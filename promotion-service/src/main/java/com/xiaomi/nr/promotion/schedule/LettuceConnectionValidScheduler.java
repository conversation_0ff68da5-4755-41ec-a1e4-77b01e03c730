package com.xiaomi.nr.promotion.schedule;

import com.xiaomi.nr.promotion.util.RedisClusterAutoSwitchHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * lettuce链接有效性校验定时任务
 *
 * <AUTHOR>
 * @date 2022/12/12
 **/
@Component
@Slf4j
public class LettuceConnectionValidScheduler {

//    @Autowired
//    @Qualifier("stringPromotionRedisTemplate")
//    private RedisTemplate<String, String> stringPromotionRedisTemplate;
    @Autowired
    @Qualifier("stringActRedisTemplate")
    private RedisTemplate<String, String> stringActRedisTemplate;
//    @Autowired
//    @Qualifier("stringActCacheRedisTemplate")
//    private RedisTemplate<String, String> stringActCacheRedisTemplate;
    @Autowired
    @Qualifier("stringCouponRedisTemplate")
    private RedisTemplate<String, String> stringCouponRedisTemplate;
    @Autowired
    @Qualifier("stringRedpacketRedisTemplate")
    private RedisTemplate<String, String> stringRedpacketRedisTemplate;

    /**
     * 缓存操作
     */
    @Autowired
    private RedisClusterAutoSwitchHelper switchHelper;

//    @Scheduled(cron = "0/5 * * * * ?")
    public void stringPromotionRedisConnectionFactoryTask() {
        // RedisConnectionFactory connectionFactory = stringPromotionRedisTemplate.getConnectionFactory();
        RedisConnectionFactory connectionFactory = switchHelper.getRedisTemplate().getConnectionFactory();
        if (connectionFactory instanceof LettuceConnectionFactory) {
            LettuceConnectionFactory c = (LettuceConnectionFactory) connectionFactory;
            c.validateConnection();
        }
    }

//    @Scheduled(cron = "0/5 * * * * ?")
    public void stringActRedisConnectionFactoryTask() {
        RedisConnectionFactory connectionFactory = stringActRedisTemplate.getConnectionFactory();
        if (connectionFactory instanceof LettuceConnectionFactory) {
            LettuceConnectionFactory c = (LettuceConnectionFactory) connectionFactory;
            c.validateConnection();
        }
    }

//    @Scheduled(cron = "0/5 * * * * ?")
    public void stringActCacheRedisConnectionFactoryTask() {
        // RedisConnectionFactory connectionFactory = stringActCacheRedisTemplate.getConnectionFactory();
        RedisConnectionFactory connectionFactory = switchHelper.getRedisTemplate().getConnectionFactory();
        if (connectionFactory instanceof LettuceConnectionFactory) {
            LettuceConnectionFactory c = (LettuceConnectionFactory) connectionFactory;
            c.validateConnection();
        }
    }

//    @Scheduled(cron = "0/5 * * * * ?")
    public void stringCouponRedisConnectionFactoryTask() {
        RedisConnectionFactory connectionFactory = stringCouponRedisTemplate.getConnectionFactory();
        if (connectionFactory instanceof LettuceConnectionFactory) {
            LettuceConnectionFactory c = (LettuceConnectionFactory) connectionFactory;
            c.validateConnection();
        }
    }


//    @Scheduled(cron = "0/5 * * * * ?")
    public void stringRedpacketRedisConnectionFactoryTask() {
        RedisConnectionFactory connectionFactory = stringRedpacketRedisTemplate.getConnectionFactory();
        if (connectionFactory instanceof LettuceConnectionFactory) {
            LettuceConnectionFactory c = (LettuceConnectionFactory) connectionFactory;
            c.validateConnection();
        }
    }
}
