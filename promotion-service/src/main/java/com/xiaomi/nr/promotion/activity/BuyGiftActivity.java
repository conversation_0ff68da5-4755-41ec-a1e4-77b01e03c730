package com.xiaomi.nr.promotion.activity;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.GetProductActRequest;
import com.xiaomi.nr.promotion.api.dto.enums.CrowdEnum;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.componet.action.BuyGiftAction;
import com.xiaomi.nr.promotion.componet.condition.*;
import com.xiaomi.nr.promotion.componet.preparation.GlobalExcludePreparation;
import com.xiaomi.nr.promotion.componet.preparation.GoodsHierarchyPreparation;
import com.xiaomi.nr.promotion.componet.preparation.OrgInfoPreparation;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.ProductActGoodsProvider;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.SkuGroup;
import com.xiaomi.nr.promotion.entity.redis.*;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.enums.BooleanV2Enum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.ActPromExtend;
import com.xiaomi.nr.promotion.model.common.ActRespConverter;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyGiftPromotionConfig;
import com.xiaomi.nr.promotion.tool.ConditionCheckTool;
import com.xiaomi.nr.promotion.util.CompareHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 买赠活动
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class BuyGiftActivity extends AbstractBuyGfitActivity implements ActivityTool, ProductActGoodsProvider {
    
    /**
     * 是否F会员专属赠品 1-是，2-否
     */
    private Integer isFMember;

    /**
     * 人群列表，枚举见@CrowdEnum
     */
    private List<String> crowdList;


    @Autowired
    private ConditionCheckTool conditionCheckTool;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine
                .conditionPreparation(GoodsHierarchyPreparation.class)
                .conditionPreparation(GlobalExcludePreparation.class)
                .conditionPreparation(OrgInfoPreparation.class)
                .condition(OrgCondition.class)
                .condition(ChannelCondition.class)
                .condition(ClientCondition.class)
                .condition(CrowdCondition.class)
                .condition(BuyGiftBaseNumCondition.class)
                .condition(GiftBargainCondition.class)
                .condition(UserGroupCondition.class)
                .condition(OfflineJoinLimitCondition.class)
                .condition(BuyGiftNumCondition.class)
                .action(BuyGiftAction.class);
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.BUY_GIFT;
    }

    /**
     * 构建购物车优惠数据
     *
     * @param context 上下文
     * @return 优惠促销信息
     */
    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) {
        NumLimitRule numLimitRule = ActRespConverter.convert(this.numLimitRule);
        PolicyNew policyNew = getPolicyNew(includeGoodsGroups, giftGoods.getSkuGroupList(), null, false);

        PromotionInfo promotionInfo = buildDefaultPromotionInfo(context);
        promotionInfo.setFrequent(null);
        promotionInfo.setTotalLimitNum(actLimitNum);
        promotionInfo.setActivityMutexLimit(BooleanEnum.NO.getValue());
        promotionInfo.setActivityMutex(Collections.emptyList());
        promotionInfo.setNumLimitRule(numLimitRule);
        promotionInfo.setPolicyNew(policyNew);
        return promotionInfo;
    }

    /**
     * 获取产品站信息
     *
     * @param clientId       应用ID
     * @param orgCode        门店Code
     * @param skuPackageList 活动类别
     */
    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        if (!checkProductGoodsActCondition(clientId, orgCode)) {
            log.debug("buyGift condition is not match. actId:{} clientId:{} orgCode:{}", id, clientId, orgCode);
            return Collections.emptyMap();
        }
        List<String> joinedSkuPackageList = Lists.newArrayList(includeSkuPackages);
        joinedSkuPackageList.retainAll(skuPackageList);
        if (CollectionUtils.isEmpty(joinedSkuPackageList)) {
            log.debug("buyGift joinedSkuPackageList retain empty. actId:{} clientId:{} orgCode:{}", id, clientId, orgCode);
            return Collections.emptyMap();
        }

        List<SkuGroup> skuGroupList = giftGoods.getSkuGroupList();
        ProductActGoods getGiftGoodsInfo = doConvertActGoods(skuGroupList, includeGoodsGroups, Boolean.TRUE);
        if (getGiftGoodsInfo == null) {
            log.warn("buyGift getGiftGoodsInfo null. actId:{}", id);
            return Collections.emptyMap();
        }
        ProductActInfo productActInfo = new ProductActInfo();
        productActInfo.setType(type.getValue());
        productActInfo.setId(id);
        productActInfo.setName(name);
        productActInfo.setChannels(channels);
        productActInfo.setSelectClientList(selectClientList);
        productActInfo.setSelectOrgList(selectOrgList);
        productActInfo.setUnixStartTime(getUnixStartTime());
        productActInfo.setUnixEndTime(getUnixEndTime());
        productActInfo.setGiftGoods(getGiftGoodsInfo);
        productActInfo.setGiftGoodsV2(Collections.singletonList(getGiftGoodsInfo));
        return joinedSkuPackageList.stream().collect(Collectors.toMap(skuPackage -> skuPackage, skuPackage -> productActInfo));
    }

    /**
     * 获取活动详情
     *
     * @return 活动详情
     */
    @Override
    public ActivityDetail getActivityDetail() {
        ActivityDetail detail = getBasicActivityDetail();
        detail.setGiftGoods(giftGoods);
        detail.setDescRule(descRule);
        detail.setIncludeGoodsGroups(includeGoodsGroups);
        return detail;
    }

    /**
     * 获取产品站活动优惠信息
     *
     * @param request   产品站信息
     * @param hierarchy 商品层级关系
     * @param isOrgTool 是否来自活动工具
     * @return 优惠数据
     */
    @Override
    public PromotionInfo getProductAct(GetProductActRequest request, GoodsHierarchy hierarchy, boolean isOrgTool) throws BizError {
        if (!checkProductActCondition(request, isOrgTool)) {
            log.info("buygift condition is not match. id:{}，request:{}", id,request);
            return null;
        }
        // 检查F会员专属
        Integer judgeFMember=crowdList.contains(CrowdEnum.F_MEMBER.getCode())?1:2;
        // interfaceVersion==1代表老接口能力，保持不变
        if (!conditionCheckTool.checkFmember(judgeFMember, request.getUserIsFriend()) && request.getInterfaceVersion()==1) {
            return null;
        }
        // PC服务端调用交易、交易调用促销老的产品站接口interfaceVersion==1，不展示学生人群的买赠活动
        if (request.getInterfaceVersion()==1&&crowdList.contains(CrowdEnum.STUDENT.getCode())){
            return null;
        }

        // 构建PolicyNew
        PolicyNew policyNew = buildPolicyNewForGiftAct(request, giftGoods, isOrgTool, includeGoodsGroups, hierarchy);
        if (policyNew == null) {
            log.info("buy gift policyNew is null. actId:{}, request:{}, isOrgTool:{}", id, request, isOrgTool);
            return null;
        }
        List<SkuGroup> skuGroupList = giftGoods.getSkuGroupList();
        Integer ignoreStock = giftGoods.getIgnoreStock();
        ActPromExtend extend = buildNewPromotionExtend(skuGroupList, includeGoodsGroups, ignoreStock);
        if (extend != null) {
            extend.setMaxCount(1L);
        }
        PromotionInfo promotionInfo = getDefaultProductAct();
        promotionInfo.setExtend(GsonUtil.toJson(extend));
        promotionInfo.setQuotaEles(getNewQuotaEleList(includeGoodsGroups));
        promotionInfo.setPolicys(null);
        promotionInfo.setPolicyNew(policyNew);
        promotionInfo.setIsFMember(judgeFMember);
        if (request.getInterfaceVersion()==2) {
            promotionInfo.setCrowdList(crowdList);
        }
        return promotionInfo;
    }

    /**
     * 获取政策
     *
     * @param stockMap 库存信息
     * @return 政策
     */
    private PolicyNew getPolicyNew(List<FillGoodsGroup> includeGoodsGroups, Map<Long, Long> stockMap) {
        if (CollectionUtils.isEmpty(includeGoodsGroups)) {
            return null;
        }
        List<PolicyNewSkuGroup> skuGroupsList = getNewSkuGroupList(stockMap);
        if (CollectionUtils.isEmpty(skuGroupsList)) {
            return null;
        }
        PolicyNewGoods newGoods = new PolicyNewGoods();
        newGoods.setSkuGroupsList(skuGroupsList);

        PolicyNewRule newRule = new PolicyNewRule();
        newRule.setGiftGoods(newGoods);

        List<PolicyNewFillGoodsGroup> includedGoodsGroup = includeGoodsGroups.stream().map(fillGoodsGroup -> {
            PolicyNewFillGoodsGroup goodsGroup = new PolicyNewFillGoodsGroup();
            goodsGroup.setQuota(ActRespConverter.convert(fillGoodsGroup.getQuota()));
            return goodsGroup;
        }).collect(Collectors.toList());

        PolicyNewLevel policyItem = new PolicyNewLevel();
        policyItem.setIncludedGoodsGroup(includedGoodsGroup);
        policyItem.setRule(newRule);

        PolicyNew policyNew = new PolicyNew();
        policyNew.setPolicy(Lists.newArrayList(policyItem));
        policyNew.setType((long) type.getValue());
        return policyNew;
    }

    /**
     * 获取赠品信息
     *
     * @param stockMap 库存Map
     * @return SKU信息
     */
    private List<PolicyNewSkuGroup> getNewSkuGroupList(Map<Long, Long> stockMap) {
        List<SkuGroup> skuGroupsList = giftGoods.getSkuGroupList();
        if (CollectionUtils.isEmpty(skuGroupsList)) {
            return Collections.emptyList();
        }
        return skuGroupsList.stream().map(item -> convertGoodsItem(item, stockMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * gift goods
     *
     * @return ProductActGoods
     */
    @Override
    public ProductActGoods getAdditionalGoods() {
        return doConvertActGoods(giftGoods.getSkuGroupList(), includeGoodsGroups, Boolean.TRUE);
    }

    private PolicyNewSkuGroup convertGoodsItem(SkuGroup skuGroups, Map<Long, Long> stockMap) {
        List<PolicyNewGroup> groupList = skuGroups.getListInfo().stream()
                .map(skuGroup -> convert(skuGroups.getGroupId(), skuGroup, stockMap)).collect(Collectors.toList());
        boolean noneInStock = groupList.stream().noneMatch(item -> Objects.equals(BooleanV2Enum.YES.getValue(), item.getIsInStock()));
        if (noneInStock) {
            return null;
        }

        PolicyNewSkuGroup group = new PolicyNewSkuGroup();
        group.setGroupId(skuGroups.getGroupId());
        group.setListInfo(groupList);
        return group;
    }

    private PolicyNewGroup convert(Long groupId, GiftBargainGroup giftGroup, Map<Long, Long> stockMap) {
        Long stockNum = stockMap.get(giftGroup.getGoodsId());
        int isInStock = stockNum != null && (stockNum / giftGroup.getGiftBaseNum()) > 0 ?
                BooleanV2Enum.YES.getValue() : BooleanV2Enum.NO.getValue();
        // 忽略库存
        if (BooleanEnum.isYes(giftGoods.getIgnoreStock())) {
            isInStock = BooleanV2Enum.YES.getValue();
        }
        boolean check = conditionCheckTool.checkActGiftLimit(giftGroup.getGiftLimitNum(), giftGroup.getGiftBaseNum(),
                id, groupId, giftGroup.getSku());
        if (!check) {
            isInStock = BooleanV2Enum.NO.getValue();
        }

        PolicyNewGroup newGroup = new PolicyNewGroup();
        newGroup.setSku(giftGroup.getSku());
        newGroup.setCartPrice(giftGroup.getCartPrice());
        newGroup.setMarketPrice(giftGroup.getMarketPrice());
        newGroup.setIsInStock(isInStock);
        newGroup.setBaseNum(giftGroup.getGiftBaseNum());
        return newGroup;
    }

    /**
     * 校验商品参与活动的有效性
     *
     * @param request 商品信息
     * @param goodsHierarchyMap 商品层级关系map
     * @return 不合法的商品列表
     * @throws BizError 业务异常
     */
    @Override
    public List<CheckGoodsItem> checkProductsAct(CheckProductsActRequest request, Map<String, GoodsHierarchy> goodsHierarchyMap) throws BizError {
        List<CheckGoodsItem> invalidGoodsList = new ArrayList<>();
        // 校验商品是否在活动配置中
        checkIncludeGoods(request.getMainGoodsList(), includeGoodsGroups, goodsHierarchyMap, request.getAttachedGoodsList(), giftGoods.getSkuGroupList(), invalidGoodsList);
        return invalidGoodsList;
    }



    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof BuyGiftPromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        BuyGiftPromotionConfig promotionConfig = (BuyGiftPromotionConfig) config;
        this.giftGoods = promotionConfig.getGiftGoods();
        this.includeGoodsGroups = Collections.singletonList(promotionConfig.getIncludeGoodsGroup());
        this.isFMember = promotionConfig.getIsFMember();
        this.crowdList=promotionConfig.getCrowdList();
        return true;
    }
}
