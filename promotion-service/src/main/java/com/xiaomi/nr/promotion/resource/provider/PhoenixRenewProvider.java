package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.rpc.recycle.OfflineOrderServiceProxy;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 三方优惠以旧换新优惠
 *
 * <AUTHOR>
 * @date 2022/10/10
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class PhoenixRenewProvider implements ResourceProvider<PhoenixRenewProvider.ResContent> {
    private ResourceObject<PhoenixRenewProvider.ResContent> resourceObject;
    @Autowired
    private OfflineOrderServiceProxy offlineOrderServiceProxy;

    @Override
    public ResourceObject<PhoenixRenewProvider.ResContent> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<PhoenixRenewProvider.ResContent> object) {
        this.resourceObject = object;
    }

    @Override
    public void lock() throws BizError {
        ResContent content = resourceObject.getContent();
        offlineOrderServiceProxy.verifyRecycleOrder(content.userId, resourceObject.getOrderId(), content.renewOrderId,
                content.oldPhoneAmount, content.subsidyAmount);
        log.info("verifyRecycleOrder ok. resourceObject:{}", resourceObject);
    }

    @Override
    public void consume() throws BizError {
    }

    @Override
    public void rollback() throws BizError {
        ResContent content = resourceObject.getContent();
        offlineOrderServiceProxy.rollbackVerifyRecycleOrder(content.userId, resourceObject.getOrderId(), content.renewOrderId,
                content.oldPhoneAmount, content.subsidyAmount);
        log.info("rollbackVerifyRecycleOrder ok. resourceObject:{}", resourceObject);
    }

    @Override
    public String conflictText() {
        return null;
    }

    @Data
    public static class ResContent {
        /**
         * 用户ID
         */
        private Long userId;
        /**
         * 回收单号
         */
        private Long renewOrderId;
        /**
         * 旧机补贴金额消耗
         */
        private Long subsidyAmount;
        /**
         * 旧机抵扣金额消耗
         */
        private Long oldPhoneAmount;
    }
}
