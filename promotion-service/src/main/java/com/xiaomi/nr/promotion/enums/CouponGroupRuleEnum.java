package com.xiaomi.nr.promotion.enums;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

/**
 * @<PERSON> duan<PERSON>gqi
 * @Date 2024/3/4
 */
@Getter
@AllArgsConstructor
public enum CouponGroupRuleEnum {
    
    /**
     * 互斥
     */
    EXCLUSION(1),
    
    /**
     * 叠加
     */
    INCLUSION(2),
    
    ;
    
    private final Integer code;
    
    private static final Map<Integer, CouponGroupRuleEnum> COUPON_GROUP_RULE_ENUM_MAP = Maps.newHashMap();
    
    static {
        COUPON_GROUP_RULE_ENUM_MAP.put(EXCLUSION.getCode(), EXCLUSION);
        COUPON_GROUP_RULE_ENUM_MAP.put(INCLUSION.getCode(), INCLUSION);
    }
    
    public static CouponGroupRuleEnum getEnumByCode(Integer code) {
        return COUPON_GROUP_RULE_ENUM_MAP.get(code);
    }
    
}
