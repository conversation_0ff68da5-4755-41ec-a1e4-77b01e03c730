package com.xiaomi.nr.promotion.enums;

public enum ActivityStockEnum {

    ONSALE(1, "onsale"),
    ONSALE_USER(2, "onsale_user"),
    ONSALE_MOBILE_USER(3, "onsale_mobile_user"),
    ONSALE_STORE(4, "onsale_store"),
    ONSALE_STORE_DALIY(5, "onsale_store_daliy"),
    ONSALE_ALL_STORE(6, "onsale_all_store"),
    ONSALE_ALL_STORE_DAILY(7, "onsale_all_store_daily"),
    BARGAIN(8, "bargain"),
    GIFT(9, "gift"),
    STORE(10, "store"),
    STORE_DAILY(11, "store_daily"),
    ALL_STORE(12, "all_store"),
    ALL_STORE_DAILY(13, "all_store_daily"),
    PERSON(14, "person"),
    PERSON_DAILY(15, "person_daily"),
    U_TYPE_PERSON(16, "u_type_person"),
    U_TYPE_PERSON_DAILY(17, "u_type_person_daily"),
    ONLINE(18, "online")

    ;

    /**
     * 库存类型值
     */
    private final int value;

    /**
     * 活动类型的名称
     */
    private final String name;

    ActivityStockEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
