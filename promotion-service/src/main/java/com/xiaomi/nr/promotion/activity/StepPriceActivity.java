package com.xiaomi.nr.promotion.activity;

import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.model.GoodsDto;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionPriceDTO;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.Specification;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.StepPriceProductRule;
import com.xiaomi.nr.promotion.componet.action.StepPriceAction;
import com.xiaomi.nr.promotion.componet.condition.StepPriceCondition;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.PromotionPriceNormProvider;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.StepPricePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * 阶梯价活动
 *
 * <AUTHOR>
 * @date 2023/2/9
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class StepPriceActivity extends AbstractActivityTool implements ActivityTool, PromotionPriceNormProvider {

    /**
     * 阶梯信息
     * key: SSU ID val:ActPriceInfo
     */
    private Map<Long, ActPriceInfo> stepPriceInfoMap;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine
                .condition(StepPriceCondition.class)
                .action(StepPriceAction.class);
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.B2T_STEP_PRICE;
    }

    /**
     * 构建优惠信息
     *
     * @param context 上下文
     * @return 优惠信息
     */
    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) throws BizError {
        return super.buildDefaultPromotionInfo(context);
    }

    /**
     * 获取活动详情
     *
     * @return 活动详情
     */
    @Override
    public ActivityDetail getActivityDetail() {
        return super.getBasicActivityDetail();
    }

    /**
     * 获取优惠价格
     *
     * @param goodsList     详情信息
     * @param contextParams 上下文参数
     * @return key: SSU val: 价格信息
     */
    @Override
    public Map<Long, PromotionPriceDTO> getGoodsPromotionPrice(List<GoodsDto> goodsList, Map<String, String> contextParams) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return Collections.emptyMap();
        }
        Map<Long, PromotionPriceDTO> priceDtoMap = Maps.newHashMap();
        for (GoodsDto goodsDto : goodsList) {
            handlePrice(goodsDto, priceDtoMap);
        }
        return priceDtoMap;
    }

    private void handlePrice(GoodsDto goodsDto, Map<Long, PromotionPriceDTO> priceDtoMap) {
        ActPriceInfo priceInfo = stepPriceInfoMap.get(goodsDto.getSsuId());
        if (priceInfo == null) {
            return;
        }
        List<Specification> specificationList = priceInfo.getSpecificationList();
        if (CollectionUtils.isEmpty(specificationList)) {
            return;
        }
        // 如果有一件的阶梯价，返回一件的阶梯价，没有一件的阶梯价，返回原价
        Specification specification = specificationList.stream()
                .filter(spec -> spec.getThreshold() <= 1).findAny().orElse(null);

        long finalPrice = goodsDto.getPrice();
        if (specification != null) {
            finalPrice = specification.getPrice();
        }
        // 确定价格
        if (goodsDto.getPrice() != null) {
            finalPrice = Math.min(finalPrice, goodsDto.getPrice());
        }
        StepPriceProductRule productRule = new StepPriceProductRule();
        productRule.setRuleType(priceInfo.getRuleType());
        productRule.setCashBackPrice(priceInfo.getCashBackPrice());
        productRule.setSpecificationList(specificationList);

        // 包装结果
        PromotionPriceDTO priceDTO = buildPromotionPrice(GsonUtil.toJson(productRule), finalPrice);
        priceDtoMap.put(goodsDto.getSsuId(), priceDTO);
        // 替换价格
        goodsDto.setPrice(finalPrice);
    }

    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        return Collections.emptyMap();
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof StepPricePromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        StepPricePromotionConfig promotionConfig = (StepPricePromotionConfig) config;
        this.stepPriceInfoMap = promotionConfig.getStepPriceInfoMap();
        return true;
    }
}
