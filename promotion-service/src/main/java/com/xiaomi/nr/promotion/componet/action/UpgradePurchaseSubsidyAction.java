package com.xiaomi.nr.promotion.componet.action;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.UpgradePurchaseSubsidyPromotionConfig;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.IdKeyHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;



@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class UpgradePurchaseSubsidyAction extends AbstractAction {

    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 优惠类型
     */
    private PromotionToolType promotionType;
    /**
     * 折扣打的折扣，9折就是90
     */
    private long reduceDiscount = 0;
    /**
     * 折扣最高可以减免的钱
     */
    private long maxReducePrice = 0;
    /**
     * sku-品类
     */
    private Map<String, Integer> goodsSpuGroupMap;

    @Autowired
    private CheckoutCartTool checkoutCartTool;

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (MapUtil.isEmpty(goodsSpuGroupMap)) {
            log.warn("UpgradePurchaseSubsidyAction goodsSpuGroupMap is empty. actId:{} uid:{}", promotion, request.getUserId());
            return;
        }
        List<CartItem> cartItemList = request.getSourceApi() == SourceApi.SUBMIT ? context.getCarts() : request.getCartList();
        // 获取此次参加活动的商品
        List<GoodsIndex> indexList = context.getGoodIndex();

        // 改价，并且获取参与活动的itemId
        List<String> joinedItemIdList = changePriceAndGetJoinedItemIdList(request,cartItemList, indexList);
        if (joinedItemIdList.isEmpty()) {
            return;
        }
        // 构造promotionInfo
        PromotionInfo promotionInfo = new PromotionInfo();
        promotionInfo.setPromotionId(promotionId.toString());
        promotionInfo.setType(String.valueOf(promotionType.getTypeId()));
        promotionInfo.setJoinedItemId(joinedItemIdList);
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());

    }

    private List<String> changePriceAndGetJoinedItemIdList(CheckoutPromotionRequest request, List<CartItem> cartItemList, List<GoodsIndex> indexList) {
        List<Pair<Long, String>> curPriceItemIdPairList = new ArrayList<>();
        // 门店强绑定商品单独处理
        List<CartItem> orgBindItemList = new ArrayList<>();
        for (CartItem cartItem : cartItemList) {
            boolean match = indexList.stream().anyMatch(item -> item.getItemId().equals(cartItem.getItemId()));
            if (!match) {
                continue;
            }
            String skuPackage = CartHelper.getSkuPackage(cartItem);
            Integer spuGroup = goodsSpuGroupMap.get(skuPackage);
            if (spuGroup == null) {
                log.warn("UpgradePurchaseSubsidyPromotionConfig goodsSpuGroupMap not contain skuPackage:{}. actId:{} uid:{}", skuPackage, promotionId, request.getUserId());
                continue;
            }
            // 当前优惠后的价格
            long curPrice = getCurPrice(cartItem);
            if (curPrice <= 0) {
                log.warn("UpgradePurchaseSubsidyPromotionConfig promotion price error. skuPackage:{}. actId:{} uid:{} curPrice:{}", skuPackage, promotionId, request.getUserId(), curPrice);
                continue;
            }
            // 实际减免金额
            long reducePrice = calculateReduceMoney(curPrice, reduceDiscount, maxReducePrice);
            if (reducePrice==0){
                continue;
            }
            // 门店强绑定需要单独处理
            if (!StringUtils.isEmpty(request.getOrgCode()) && SourceEnum.isBind(cartItem.getSource())) {
                orgBindItemList.add(cartItem);
                continue;
            }
            updateCartItemReduceInfo(cartItem, reducePrice, curPrice, curPriceItemIdPairList);
        }

        if (!orgBindItemList.isEmpty()) {
            // 处理门店强绑定
            changOrgBindPrice(orgBindItemList, request, curPriceItemIdPairList);
        }

        // 按照优惠后价格降序排列
        curPriceItemIdPairList.sort((o1, o2) -> o2.getKey().compareTo(o1.getKey()));
        List<String> priceItemIdList = new ArrayList<>();
        curPriceItemIdPairList.forEach(price -> {
            priceItemIdList.add(price.getValue());
        });
        return priceItemIdList;
    }

    private void changOrgBindPrice(List<CartItem> orgBindItemList, CheckoutPromotionRequest request, List<Pair<Long, String>> curPriceItemIdPairList) {
        // 找到成对的强绑定商品
        List<Pair<CartItem, CartItem>> bindItemPairList = getBindItemPairList(orgBindItemList);
        for (Pair<CartItem, CartItem> pair : bindItemPairList) {
            CartItem parentItem = pair.getKey();
            CartItem childItem = pair.getValue();
            // 当前优惠后的价格
            long parentCurPrice = getCurPrice(parentItem);
            long childCurPrice = getCurPrice(childItem);
            long totalCurPrice = parentCurPrice + childCurPrice;
            // 折扣后的价格
            long totalReducePrice = calculateReduceMoney(totalCurPrice, reduceDiscount, maxReducePrice);
            // 分摊
            long parentReducePrice = Math.min(Math.floorDiv(totalReducePrice * parentCurPrice, totalCurPrice), parentCurPrice);
            long childReducePrice = totalReducePrice - parentReducePrice;
            // 更新父品优惠信息
            updateCartItemReduceInfo(parentItem, parentReducePrice, parentCurPrice, curPriceItemIdPairList);
            // 更新子品优惠信息
            updateCartItemReduceInfo(childItem, childReducePrice, childCurPrice, curPriceItemIdPairList);
        }
    }

    private void updateCartItemReduceInfo(CartItem cartItem, long reducePrice, long curPrice, List<Pair<Long, String>> curPriceItemIdPairList) {
        String idKey = IdKeyHelper.getGeneralActIdKey(promotionId);
        // 更新购物车优惠扣减：reduceList/reduceDetailList/reduceAmount
        checkoutCartTool.updateCartsItemReduce(cartItem, reducePrice, idKey, promotionType.getTypeId(), promotionId);
        curPriceItemIdPairList.add(Pair.of(curPrice, cartItem.getItemId()));
    }

    private List<Pair<CartItem, CartItem>> getBindItemPairList(List<CartItem> orgBindItemList) {
        List<Pair<CartItem, CartItem>> bindItemPairList = new ArrayList<>();
        List<CartItem> parentItemList = new ArrayList<>();
        Map<String, CartItem> parentItemIdCartItemMap = new HashMap<>();
        for (CartItem item : orgBindItemList) {
            if (StringUtils.isEmpty(item.getParentItemId())) {
                parentItemList.add(item);
            } else {
                parentItemIdCartItemMap.put(item.getParentItemId(), item);
            }
        }
        for (CartItem parentItem : parentItemList) {
            CartItem childItem = parentItemIdCartItemMap.get(parentItem.getItemId());
            if (childItem == null) {
                log.warn("getBindItemPairList error, parentItem not contain childItem, parentItem:{}", parentItem);
                continue;
            }
            bindItemPairList.add(Pair.of(parentItem, childItem));
        }
        return bindItemPairList;
    }

    /**
     * 获取优惠后的价格（排除购新补贴）
     * @param cartItem 购物车
     * @return 优惠后价格
     */
    private Long getCurPrice(CartItem cartItem){
        long curPrice = cartItem.getCartPrice() * cartItem.getCount() - cartItem.getReduceAmount();
        Map<String, List<ReduceDetail>> reduceDetailList = cartItem.getReduceDetailList();
        if (reduceDetailList!=null && reduceDetailList.containsKey(PromotionConstant.NEW_PURCHASE_SUBSIDY_KEY)){
            // 排除购新补贴
            List<ReduceDetail> subsidyReduceDetailList = reduceDetailList.get(PromotionConstant.NEW_PURCHASE_SUBSIDY_KEY);
            long newPurchaseSubsidyReducePrice = subsidyReduceDetailList.stream().mapToLong(ReduceDetail::getAmount).sum();
            curPrice = curPrice+newPurchaseSubsidyReducePrice;
        }
        return curPrice;
    }

    private long calculateReduceMoney(long totalPrice, long reduceDiscount, long maxReducePrice) {
        //打折后的价格
        long reducePrice = totalPrice * reduceDiscount / 100;
        reducePrice = Math.min(reducePrice, totalPrice);
        //最高可以减免的钱
        if (maxReducePrice > 0) {
            reducePrice = Math.min(maxReducePrice, reducePrice);
        }
        return reducePrice;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof UpgradePurchaseSubsidyPromotionConfig)) {
            log.error("config is not instanceof UpgradePurchaseSubsidyPromotionConfig. class:{}", config.getName());
            return;
        }
        UpgradePurchaseSubsidyPromotionConfig promotionConfig = (UpgradePurchaseSubsidyPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        this.reduceDiscount = promotionConfig.getReduceDiscount();
        this.maxReducePrice = promotionConfig.getMaxReducePrice();
        this.goodsSpuGroupMap = promotionConfig.getGoodsSpuGroupMap();
    }
}
