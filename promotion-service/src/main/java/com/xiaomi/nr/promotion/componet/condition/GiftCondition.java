package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.entity.redis.*;
import com.xiaomi.nr.promotion.enums.PolicyQuotaTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.*;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.GiftPromotionConfig;
import com.xiaomi.nr.promotion.tool.ConditionCheckTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 满赠条件
 *
 * <AUTHOR>
 * @date 2022/7/18
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class GiftCondition extends AbstractGiftCondition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 活动类型
     */
    private PromotionToolType promotionType;
    /**
     * 是否允许套装内商品参加
     */
    private boolean checkPackage;
    /**
     * 销售来源
     */
    private List<String> saleSources;
    /**
     * 活动密码
     */
    private String accessCode;
    /**
     * 条件阶梯
     */
    private List<QuotaLevel> levelList;

    /**
     * - 获取符合条件组的商品组. 可能有多组， 是一个且的关系
     * - 计算每组可得赠品 加价购数量，并且获取最小值
     * - 获取可参与的主商品
     *
     * @param request 请求参数
     * @param context 活动内上下文
     * @return true/false
     */
    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) {
        Long userId = request.getUserId();
        if (CollectionUtils.isEmpty(levelList)) {
            log.error("gift levelList is not empty. uid:{} actId:{}", userId, promotionId);
            return false;
        }
        List<CartItem> cartList = request.getCartList();
        boolean online = StringUtils.isEmpty(request.getOrgCode());

        // 阶梯匹配, 从前往后, 阶梯高的在前
        LevelMatchContext matchContext = levelList.stream()
                .map(level -> matchQuotaLevel(level, cartList, userId, online, context))
                .filter(Objects::nonNull).findAny().orElse(null);
        if (matchContext == null) {
            log.info("gift carts none match quote level. uid:{} actId:{}", userId, promotionId);
            return false;
        }

        context.setFillTimes((int) matchContext.getFillTimes());
        context.setGoodIndex(matchContext.getIndexList());
        context.setQuotaLevel(matchContext.getQuotaLevel());
        return true;
    }

    private LevelMatchContext matchQuotaLevel(QuotaLevel level, List<CartItem> cartList, Long userId, boolean online, LocalContext context) {
        // 赠品组活动库存检查
        Goods giftGoods = level.getGiftGoods();
        boolean giftLimitCheck = checkGiftLimit(giftGoods.getSkuGroupList(), promotionId);
        if (!giftLimitCheck) {
            log.info("gift goods limit all used. uid:{} actId:{}", userId, promotionId);
            return null;
        }

        // 获取符合条件组的商品组 && 合并fillGoodsListGroup
        List<FillGoodsGroup> goodsGroups = level.getIncludeGoodsGroups();
        List<List<GoodsIndex>> indexListGroup = doGoodsListGroupMatch(goodsGroups, cartList, context, userId, online,
                promotionId, promotionType, checkPackage, saleSources, accessCode);
        List<GoodsIndex> indexList = mergeListGroup(indexListGroup);
        if (CollectionUtils.isEmpty(indexList)) {
            log.debug("condition is not satisfied. fillGoodsList is empty. actId:{} uid:{}", promotionId, userId);
            return null;
        }

        // 统计累加符合商品，计算叠加的金额或者件数
        List<ValidCondition> staticGroupList = doStaticGroups(goodsGroups, indexListGroup, cartList);
        // 计算次数， 并获得最小值，多个条件为且的关系，遍历判断条件是否满足金额或者件数，其中一个不满足则整体不满足，直接返回
        long fillTimes = calculateFillTimes(goodsGroups, staticGroupList, userId);
        if (fillTimes <= 0L) {
            return null;
        }

        // 匹配结果
        LevelMatchContext matchContext = new LevelMatchContext();
        matchContext.setQuotaLevel(level);
        matchContext.setFillTimes(fillTimes);
        matchContext.setIndexList(indexList);
        return matchContext;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof GiftPromotionConfig)) {
            log.error("config is not instanceof MultiPromotionConfig. config:{}", config);
            return;
        }
        GiftPromotionConfig promotionConfig = (GiftPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        this.checkPackage = promotionConfig.isCheckPackage();
        this.saleSources = promotionConfig.getSaleSources();
        this.accessCode = promotionConfig.getAccessCode();
        this.levelList = promotionConfig.getLevelList();
    }
}
