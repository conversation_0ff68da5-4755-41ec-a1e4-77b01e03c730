package com.xiaomi.nr.promotion.resource.external;

/**
 * 券类型信息资源
 *
 * <AUTHOR>
 * @date 2021/4/28
 */
//@Deprecated
//@Component
//@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
//public class CouponExternalProvider extends ExternalDataProvider<List<CouponOwnedInfo>> {
//    /**
//     * 券类型信息数据
//     */
//    private ListenableFuture<List<CouponOwnedInfo>> future;
//
//    @Autowired
//    @Qualifier("couponInfoServiceRemoteImpl")
//    private CouponInfoService couponInfoService;
//
//    @Override
//    protected boolean switchOn() {
//        return Boolean.TRUE;
//    }
//
//    @Override
//    protected void doPrepare(CheckoutPromotionRequest request) {
//        Long uid = request.getUserId();
//        List<String> codes = request.getCouponCodes();
//        List<Long> ids = request.getCouponIds();
//        //  券码
//        if (CollectionUtils.isNotEmpty(codes)) {
//            this.future = couponInfoService.getByCodeAsyncNew(request);
//            // 券ID
//        } else if (CollectionUtils.isNotEmpty(ids)) {
//            this.future = couponInfoService.batchGetByIdAsync(ids, uid);
//        }
//    }
//
//    @Override
//    protected long getTimeoutMills() {
//        return DEFAULT_TIMEOUT;
//    }
//
//    @Override
//    public ListenableFuture<List<CouponOwnedInfo>> getFuture() {
//        return future;
//    }
//
//    @Override
//    public ResourceExtType getResourceExtType() {
//        return ResourceExtType.COUPON;
//    }
//}
