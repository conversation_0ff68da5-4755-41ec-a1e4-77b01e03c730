package com.xiaomi.nr.promotion.enums;

/**
 * 券类型
 *
 * <AUTHOR>
 * @date 2021/4/22
 */
public enum CouponTypeEnum {
    /**
     * 现金券
     */
    CASH(1),
    /**
     * 折扣券
     */
    DISCOUNT(2),
    /**
     * 商品抵扣券
     */
    DEDUCT(3),
    /**
     * 快递券/部分免邮券
     */
    PART_POSTFREE(4);

    private final int type;

    CouponTypeEnum(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    /**
     * 获取券类型
     * @param val
     * @return
     */
    public static CouponTypeEnum get(int val) {
        CouponTypeEnum[] values = CouponTypeEnum.values();
        for (CouponTypeEnum typeEnum : values) {
            if (val == typeEnum.getType()) {
                return typeEnum;
            }
        }
        return null;
    }
}
