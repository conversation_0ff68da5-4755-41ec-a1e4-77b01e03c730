package com.xiaomi.nr.promotion.componet.action.carshop;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.CartItemChild;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.componet.action.AbstractOnsaleAction;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.OnsaleJoin;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2024/8/13
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarShopOnsaleAction extends AbstractOnsaleAction {
    @Override
    public String getJoinGoods(CartItem cartItem) {
        return String.valueOf(cartItem.getSsuId());
    }

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context)
            throws BizError {
        
        if (MapUtil.isEmpty(onsaleInfoMap)) {
            log.error("onsaleInfoMap is empty. actId:{} uid:{}", promotion, request.getUserId());
            return;
        }

        // 获取此次参加活动的商品
        List<GoodsIndex> indexList = context.getGoodIndex();
        List<CartItem> cartList = request.getCartList();

        // 计算参加商品的优惠
        Map<String, OnsaleCalcInfo> joinMap = calculateOnsaleJoinMap(cartList, indexList);

        for (CartItem cartItem : cartList) {

            if (!joinMap.containsKey(cartItem.getItemId())) {
                continue;
            }
            // 获取可以参加的直降活动(onSaleInfo == nil代表没获取到)
            OnsaleCalcInfo onSaleInfo = joinMap.get(cartItem.getItemId());
            if (onSaleInfo == null) {
                log.error("onSaleInfo is null. cart:{}", cartItem);
                continue;
            }

            // 直降后的价格不能小于等于0
            long salePrice = onSaleInfo.getOnsalePrice();
            if (salePrice <= 0L) {
                log.warn("onSaleInfo lowerPrice <= 0, onSaleInfo:{} cart:{} ", onSaleInfo, cartItem);
                continue;
            }

            // 当前直降幅度小于上次的,不改价，给用户最优的价格
            if (cartItem.isJoinOnsale() && cartItem.getCartPrice() <= salePrice) {
                continue;
            }

            // 处理直降互斥
            List<ReduceDetailItem> reduceItemList = Optional.ofNullable(cartItem.getReduceItemList()).orElse(Lists.newArrayList());
            reduceItemList.removeIf(reduceItem -> Objects.equals(reduceItem.getPromotionType(), promotionType.getTypeId()));

            Map<Long, Long> childPriceMap = null;
            if (CartHelper.isPackage(cartItem)) {
                childPriceMap = new HashMap<>(cartItem.getChilds().size());
                calChildPrice(cartItem, salePrice, childPriceMap);
            }

            //商品改价
            cartItem.setCartPrice(salePrice);
            cartItem.setCheckoutPrice(salePrice);

            //生成优惠分摊
            calReduceItem(cartItem, joinMap, childPriceMap);
        }

        // 改价
        //changeCartPrice(cartList, joinMap);

        // 生成优惠分摊
        //calcReduceItem(cartList, joinMap);

        // 设置结果
        Map<String, OnsaleJoin> onsaleJoinMap = convertToOldOnaleJoinMap(joinMap);
        setResult(context, cartList, promotion, onsaleJoinMap);

    }

    private void calReduceItem(CartItem cartItem, Map<String, OnsaleCalcInfo> joinMap, Map<Long, Long> childPriceMap) {
        // 处理直降互斥
        List<ReduceDetailItem> reduceItemList = Optional.ofNullable(cartItem.getReduceItemList()).orElse(Lists.newArrayList());
        reduceItemList.removeIf(reduceItem -> Objects.equals(reduceItem.getPromotionType(), promotionType.getTypeId()));

        // 生成抵扣信息
        String productId = getJoinGoods(cartItem);
        ActPriceInfo onSaleInfo = onsaleInfoMap.get(productId);

        if (!CartHelper.isPackage(cartItem)) {
            OnsaleCalcInfo onsaleCalcInfo = joinMap.get(cartItem.getItemId());
            ReduceDetailItem detailItem = initReduceDetailItem(promotionId, promotionType, cartItem.getSsuId(),
                    onsaleCalcInfo.getBeforeOnsalePrice(), onsaleCalcInfo.getOnsalePrice(), cartItem.getCount());

            if (onSaleInfo != null) {
                detailItem.setBudgetApplyNo(onSaleInfo.getBudgetApplyNo());
                detailItem.setLineNum(onSaleInfo.getLineNum());
            }

            reduceItemList.add(detailItem);
        } else {
            if (childPriceMap == null) {
                childPriceMap = new HashMap<>(cartItem.getChilds().size());
            }
            for (CartItemChild child : cartItem.getChilds()) {
                ReduceDetailItem detailItem = new ReduceDetailItem();
                detailItem.setPromotionId(promotionId);
                detailItem.setPromotionType(promotionType.getTypeId());
                detailItem.setSsuId(child.getSsuId());
                detailItem.setReduce(childPriceMap.get(child.getSsuId()) * cartItem.getCount());
                detailItem.setReduceSingle(child.getOnsaleReduce());

                if (onSaleInfo != null) {
                    detailItem.setBudgetApplyNo(onSaleInfo.getBudgetApplyNo());
                    detailItem.setLineNum(onSaleInfo.getLineNum());
                }

                reduceItemList.add(detailItem);
            }
        }

        cartItem.setReduceItemList(reduceItemList);
    }

    /**
     * 转换成ssu维度的计数
     *
     * @param joinMap
     * @return
     */
    private Map<String, OnsaleJoin> convertToOldOnaleJoinMap(Map<String, OnsaleCalcInfo> joinMap) {
        Map<String, OnsaleJoin> oldJoinMap = new HashMap<>();
        joinMap.forEach((itemId, onsaleCalcInfo) -> {
            String ssuId = String.valueOf(onsaleCalcInfo.getProductId());
            if (oldJoinMap.containsKey(ssuId)) {
                //累加参加个数
                OnsaleJoin onsaleJoin = oldJoinMap.get(ssuId);
                int newCount = onsaleJoin.getJoinCounts() + onsaleCalcInfo.getJoinCounts();
                onsaleJoin.setJoinCounts(newCount);
            } else {

                OnsaleJoin onSaleJoin = new OnsaleJoin();
                onSaleJoin.setJoinCounts(onsaleCalcInfo.getJoinCounts());
                onSaleJoin.setLimitRule(onsaleCalcInfo.getLimitRule());
                onSaleJoin.setSkuOrPackage(onsaleCalcInfo.getProductId());
                oldJoinMap.put(ssuId, onSaleJoin);
            }

        });
        return oldJoinMap;
    }

    /**
     * 直降分摊
     *
     * @param cartItemList
     * @param joinMap
     */
    private void calcReduceItem(List<CartItem> cartItemList, Map<String, OnsaleCalcInfo> joinMap) {
        for (CartItem item : cartItemList) {

            String productId = getJoinGoods(item);
            if (!joinMap.containsKey(item.getItemId())) {
                continue;
            }

            // 处理直降互斥
            List<ReduceDetailItem> reduceItemList = Optional.ofNullable(item.getReduceItemList()).orElse(Lists.newArrayList());
            reduceItemList.removeIf(reduceItem -> Objects.equals(reduceItem.getPromotionType(), promotionType.getTypeId()));

            // 生成抵扣信息
            OnsaleCalcInfo onsaleCalcInfo = joinMap.get(item.getItemId());
            ReduceDetailItem detailItem = initReduceDetailItem(promotionId, promotionType, item.getSsuId(),
                    onsaleCalcInfo.getBeforeOnsalePrice(), onsaleCalcInfo.getOnsalePrice(), item.getCount());

            // 预算信息填充
            ActPriceInfo onSaleInfo = onsaleInfoMap.get(productId);
            if (onSaleInfo != null) {
                detailItem.setBudgetApplyNo(onSaleInfo.getBudgetApplyNo());
                detailItem.setLineNum(onSaleInfo.getLineNum());
            }
            reduceItemList.add(detailItem);
            item.setReduceItemList(reduceItemList);
        }
    }

    /**
     * 促销价修改
     *
     * @param cartItemList
     * @param joinMap
     */
    private void changeCartPrice(List<CartItem> cartItemList, Map<String, OnsaleCalcInfo> joinMap) {
        for (CartItem cartItem : cartItemList) {
            if (!joinMap.containsKey(cartItem.getItemId())) {
                continue;
            }
            cartItem.setCartPrice(joinMap.get(cartItem.getItemId()).getOnsalePrice());
            cartItem.setCheckoutPrice(joinMap.get(cartItem.getItemId()).getOnsalePrice());
        }
    }

    /**
     * 计算套装子品直降价：前n-1个子品按比例向下取整，最后一个子品用总直降减前N-1个子品的直降
     *
     * @param cartItem
     * @param onSalePrice
     */
    private void calChildPrice(CartItem cartItem, long onSalePrice, Map<Long, Long> childPriceMap) {
        long totalSellPrice = cartItem.getStandardPrice();

        List<CartItemChild> childList = new ArrayList<>(cartItem.getChilds());
        childList.sort(Comparator.comparing(CartHelper::getChildOriginPrice).reversed());

        int size = childList.size();

        long totalChildOnSalePrice = 0;
        for (int index = 0; index < size - 1; index++) {
            CartItemChild child = childList.get(index);

            long childOnSalePrice = Math.floorDiv(onSalePrice * child.getOriginalSellPrice(), totalSellPrice);
            child.setSellPrice(childOnSalePrice);
            child.setLowerPrice(childOnSalePrice);
            child.setOnsaleReduce(child.getOriginalSellPrice() - childOnSalePrice);
            totalChildOnSalePrice += child.getSellPrice() * child.getCount();

            childPriceMap.put(child.getSsuId(), child.getOnsaleReduce() * child.getCount());
        }

        CartItemChild lastChild = childList.getLast();
        long lastChildPrice = CartHelper.getChildOriginPrice(lastChild);
        long lastChildOnSalePrice = onSalePrice - totalChildOnSalePrice;
        long singleChildOnSalePrice = Math.ceilDiv(lastChildOnSalePrice, lastChild.getCount());
        lastChild.setSellPrice(singleChildOnSalePrice);
        lastChild.setLowerPrice(singleChildOnSalePrice);
        lastChild.setOnsaleReduce(lastChild.getOriginalSellPrice() - singleChildOnSalePrice);
        childPriceMap.put(lastChild.getSsuId(), lastChildPrice - lastChildOnSalePrice);

        // 调整最后一个商品价格低于reduce的情况
        long adjustedSellPrice = lastChildOnSalePrice - lastChildPrice;
        if (adjustedSellPrice > 0) {
            // 更新第一个商品的价格和onsaleReduce
            CartItemChild firstChild = childList.getFirst();
            long adjustedPrice = Math.ceilDiv(adjustedSellPrice, lastChild.getCount());
            long adjustedFirstChildSellPrice = firstChild.getSellPrice() + adjustedPrice;
            firstChild.setSellPrice(adjustedFirstChildSellPrice);
            firstChild.setLowerPrice(adjustedFirstChildSellPrice);
            firstChild.setOnsaleReduce(firstChild.getOriginalSellPrice() - adjustedFirstChildSellPrice);
            childPriceMap.put(firstChild.getSsuId(), childPriceMap.get(firstChild.getSsuId()) - adjustedPrice);

            // 更新最后一个商品的价格和onsaleReduce
            lastChild.setSellPrice(lastChild.getOriginalSellPrice());
            lastChild.setLowerPrice(lastChild.getOriginalSellPrice());
            lastChild.setOnsaleReduce(0L);

            childPriceMap.put(lastChild.getSsuId(), 0L);
        }

    }

    /**
     * 生成参加活动商品的直降信息
     *
     * @param cartList
     * @param indexList
     * @return key:购物车itemId
     */
    private Map<String, OnsaleCalcInfo> calculateOnsaleJoinMap(List<CartItem> cartList, List<GoodsIndex> indexList) {
        final Map<String, OnsaleCalcInfo> joinMap = new HashMap<>();
        for (CartItem item : cartList) {
            // 判断当前商品是否可以参加直降
            boolean canJoin = canJoinOnSaleAct(item, indexList);
            if (!canJoin) {
                continue;
            }
            
            // 获取可以参加的直降活动(onSaleInfo == nil代表没获取到)
            String productId = getJoinGoods(item);
            ActPriceInfo onSaleInfo = onsaleInfoMap.get(productId);
            if (onSaleInfo == null) {
                log.error("onSaleInfo is null. cart:{}", item);
                continue;
            }

            // 直降后的价格不能小于等于0
            Long onsalePrice = onSaleInfo.getPrice();
            if (onsalePrice < 0L) {
                log.warn("onSaleInfo lowerPrice < 0, onSaleInfo:{} cart:{} ", onSaleInfo, item);
                continue;
            }

            // 排除其他直降的最终金额
            Long curPrice = CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList(), promotionType);
            if (curPrice < onsalePrice) {
                log.warn("onSaleInfo lowerPrice >= curPrice, onSaleInfo:{} cart:{} ", onSaleInfo, item);
                continue;
            }


            // 记录 OnsaleCalcInfo
            OnsaleCalcInfo onsaleCalcInfo = new OnsaleCalcInfo();
            onsaleCalcInfo.setCartItemId(item.getItemId());
            onsaleCalcInfo.setProductId(item.getSsuId());
            onsaleCalcInfo.setBeforeOnsalePrice(curPrice);
            onsaleCalcInfo.setOnsalePrice(onsalePrice);
            onsaleCalcInfo.setJoinCounts(item.getCount());
            onsaleCalcInfo.setLimitRule(onSaleInfo.getLimitRule());
            joinMap.put(item.getItemId(), onsaleCalcInfo);

        }
        return joinMap;
    }
}
