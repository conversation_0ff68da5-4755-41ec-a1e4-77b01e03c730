package com.xiaomi.nr.promotion.domain.activity.facade;

import com.xiaomi.nr.promotion.activity.pool.ActSearchParam;
import com.xiaomi.nr.promotion.activity.pool.MaintenanceActivitySearcher;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MaintenanceActivityFacade {

    @Autowired
    private MaintenanceActivitySearcher maintenanceActivitySearcher;

    /**
     * 活动检索
     * @param domainCheckoutContext 促销领域上下文
     * @return 可参与活动工具
     */
    public List<ActivityTool> activitySearch(DomainCheckoutContext domainCheckoutContext) {
        ActSearchParam param = new ActSearchParam()
                .setChannel(domainCheckoutContext.getRequest().getChannel())
                .setGoodsList(maintenanceActivitySearcher.createSearchGoods(domainCheckoutContext.getRequest().getCartList()));
        return maintenanceActivitySearcher.searchCarActivity(param);
    }
}
