package com.xiaomi.nr.promotion.domain.coupon.service.mishop;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.constant.CouponConstant;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.service.base.type.AbstractCouponTool;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import com.xiaomi.nr.promotion.enums.OrderShipmentIdEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 折扣券
 *
 * <AUTHOR>
 * @date 2022/3/3
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class DiscountCoupon extends AbstractCouponTool {

    @Autowired
    private CheckoutCartTool checkoutCartTool;

    @Override
    public Long getCouponId() {
        return id;
    }

    @Override
    public Coupon generateCartCoupon() {
        return initCoupon();
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.COUPON_DISCOUNT;
    }

    @Override
    public CouponTypeEnum getCouponType() {
        return CouponTypeEnum.DISCOUNT;
    }


    @Override
    public CouponCheckoutResult checkoutCoupon(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        // 筛选满足商品列表
        List<CartItem> cartList = request.getCartList();
        List<GoodsIndex> indexList = matchCartList(cartList, context, joinGoods, (long) getCouponType().getType());

        // 计算符合总价
        ValidGoods validGoods = CartHelper.buildValidGoods(cartList, indexList);

        // 判断
        CouponCheckoutResult result = new CouponCheckoutResult();
        Pair<Boolean, String> pair = isSatisfiedQuota(validGoods);
        long cartTotalPrice = CartHelper.getTotalPrice(cartList);
        // 构建结果
        // 条件满足
        if (pair.getLeft()) {
            result.setCouponId(this.id);
            if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                result.setCouponCode(checkoutCoupon.getCouponCode());
            }
            result.setAllow(true);
            result.setValidGoodsPrice(validGoods.getValidPrice());
            result.setValidGoods(indexList);
            // 计算减免金额
            Long reduceMoney = getReduce(cartTotalPrice, validGoods);
            result.setReduceAmount(reduceMoney);
        } else {
            result.setAllow(false);
            result.setCouponId(this.id);
            if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                result.setCouponCode(checkoutCoupon.getCouponCode());
            }
            result.setUnusableReason(CouponConstant.NOT_SATISFY_USAGE_CONDITION);
            result.setUnusableCode(ErrCode.ERR_COUPON_MONEY_NUM.getCode());
            result.setKeyDataUnusable(pair.getRight());
        }
        return result;
    }

    long getReduce(long cartTotalPrice, ValidGoods validGoods) {
        long discount = checkoutCoupon.getPromotionValue();
        // 打折后的价钱
        long newPrice = discount * validGoods.getValidPrice() / 100;

        // 优惠金额
        long reduceMoney = validGoods.getValidPrice() - newPrice;
        reduceMoney = Math.min(reduceMoney, validGoods.getValidPrice());

        //折后最高可以减免的钱
        long maxPrice = checkoutCoupon.getMaxReduce();
        if (maxPrice != 0) {
            reduceMoney = Math.min(maxPrice, reduceMoney);
        }
        // 减的金额需小于当前总价，消除0元订单
        if (reduceMoney >= 1 && reduceMoney == validGoods.getValidPrice() && cartTotalPrice == validGoods.getValidPrice()) {
            reduceMoney -= 1L;
        }
        log.debug("reduce {}, maxPrice {} discount {}", reduceMoney, maxPrice, discount);
        return reduceMoney;
    }

    @Override
    public void updateCartsReduce(CheckoutPromotionRequest request, CheckoutContext context, CouponCheckoutResult result) {
        List<CartItem> cartList = request.getCartList();
        //处理商品分摊
        updateItemReduce(cartList, result);

        //处理邮费
        if (request.getShipmentId() != OrderShipmentIdEnum.FLASH_POST.val) {
            updateExpressInfo(context, result);
        }
        log.info("discountCoupon updateCartsReduce. cartList:{}, result:{}", GsonUtil.toJson(request.getCartList()), GsonUtil.toJson(result));

        //更新context
        updateCommonInfo(request, context);
    }


}
