package com.xiaomi.nr.promotion.mq.producer;

import com.xiaomi.nr.promotion.mq.producer.entity.CarActivityUseMessage;
import com.xiaomi.nr.promotion.mq.producer.template.CarActivityUseRocketMQTemplate;
import com.xiaomi.nr.promotion.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author: zhang<PERSON>wei6
 * @date: 2025/1/7 15:07
 * @description:
 */
@Slf4j
@Component
public class CarActivityUseProducer extends BaseProducer implements Producer<CarActivityUseMessage> {

    @Autowired
    public CarActivityUseProducer(CarActivityUseRocketMQTemplate carActivityUseRocketMQTemplate) {
        super.setRocketMqTemplate(carActivityUseRocketMQTemplate);
    }

    @Value("${mq.topic.carActivity}")
    private String topic;

    @Value("${mq.topic.carActivity.tag}")
    private String tag;

    @Override
    public void sendMessage(CarActivityUseMessage message) {
        SendResult sendResult = super.sendMessageAndRetry(topic, tag, message, 1);
        log.info("send result:{}", GsonUtil.toJson(sendResult));
    }
}
