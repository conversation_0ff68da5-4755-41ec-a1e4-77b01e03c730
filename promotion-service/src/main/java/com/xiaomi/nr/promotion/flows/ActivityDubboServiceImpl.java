package com.xiaomi.nr.promotion.flows;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.promotion.annotation.log.Log;
import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.nr.promotion.api.dto.model.GetProductGoodsActRequestV2;
import com.xiaomi.nr.promotion.api.service.ActivityDubboService;
import com.xiaomi.nr.promotion.domain.activity.service.common.ProductActivityService;
import com.xiaomi.nr.promotion.domain.activity.service.maintenance.PromotionUsedCountQueryService;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.RequestParamValidator;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/**
 * 活动相关接口
 * <p>
 * 核心能力：
 * 1. 获取商品可参加进行中活动
 * 2. 获取商品可参加即将开始活动
 * 3. 获取商品优惠价（直降）
 * 4. 获取商品优惠价（直降）详情列表
 * 5. 商城产品站接口
 * 8. 获取活动详情
 * </p>
 * 具体文档接口(<a href="https://xiaomi.f.mioffice.cn/docs/dock4zPMX9tfRYRNH5NHjTfXFHe#">...</a>)
 *
 * <AUTHOR>
 * @date 2021-05-13
 */
@Slf4j
@Service(timeout = 1000, group = "${dubbo.group}", version = "1.0")
@ApiModule(value = "优惠活动服务", apiInterface = ActivityDubboService.class)
public class ActivityDubboServiceImpl implements ActivityDubboService {

    @Autowired
    private ProductActivityService productActivityService;

    @Autowired
    private PromotionUsedCountQueryService promotionUsedCountQueryService;


    @Override
    @ApiDoc("产品站批量获取可参加进行中的活动")
    @Log(name = "ActivityDubboService#getMultiProductGoodsAct")
    public Result<MultiProductGoodsActResponse> getMultiProductGoodsAct(MultiProductGoodsActRequest request) {
        try {
            MultiProductGoodsActResponse response = productActivityService.getMultiProductGoodsAct(request);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on getMultiProductGoodsAct. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on getMultiProductGoodsAct. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 获取单品页可以参加的活动
     *
     * @param request 请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("pos产品站获取商品可参加进行中的活动")
    @Log(name = "ActivityDubboService#getProductGoodsAct")
    public Result<GetProductGoodsActResponse> getProductGoodsAct(GetProductGoodsActRequest request) {
        try {
            GetProductGoodsActResponse response = productActivityService.getProductGoodsAct(request);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on getProductGoodsAct. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on getProductGoodsAct. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 获取商品可参加进行中活动
     *
     * @param request 请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("获取商品可参加进行中活动V2")
    @Log(name = "ActivityDubboService#getProductGoodsActV2")
    public Result<GetProductGoodsActResponse> getProductGoodsActV2(GetProductGoodsActRequestV2 request) {
        try {
            GetProductGoodsActResponse response = productActivityService.getProductGoodsActV2(request);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on getProductGoodsActV2. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on getProductGoodsActV2. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 获取商品可参加即将开始活动
     *
     * @param request 请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("获取商品可参加即将开始的活动")
    @Log(name = "ActivityDubboService#getPreProductGoodsAct")
    public Result<GetPreProductGoodsActResponse> getPreProductGoodsAct(GetPreProductGoodsActRequest request) {
        try {
            GetPreProductGoodsActResponse response = productActivityService.getPreProductGoodsAct(request);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on getPreProductGoodsAct. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on getPreProductGoodsAct. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 获取商品活动优惠价（直降）
     *
     * @param request 请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("获取商品活动价（直降）")
    @Log(name = "ActivityDubboService#getProductActPrice")
    public Result<GetProductActPriceResponse> getProductActPrice(GetProductActPriceRequest request) {
        try {
            GetProductActPriceResponse response = productActivityService.getProductActPrice(request);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on getProductActPrice. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on getProductActPrice. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 获取商品活动优惠价V2（直降）
     *
     * @param request 请求参数
     * @return 响应
     */
    @ApiDoc("获取商品活动价V2（直降）")
    @Override
    @Log(name = "ActivityDubboService#getProductActPriceV2")
    public Result<GetProductActPriceV2Response> getProductActPriceV2(GetProductActPriceV2Request request) {
        try {
            GetProductActPriceV2Response response = productActivityService.getProductActPrice(request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("Exception on getProductActPriceV2. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 获取商品活动优惠价-电子价签专用
     *
     * @param request 请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("获取商品活动优惠价-电子价签专用")
    @Log(name = "ActivityDubboService#getGoodsActPrice")
    public Result<GetGoodsActPriceResponse> getGoodsActPrice(GetGoodsActPriceRequest request) {
        try {
            GetGoodsActPriceResponse response = productActivityService.getGoodsActPrice(request);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on getGoodsActPrice. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on getGoodsActPrice. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 获取商品活动价详情列表
     *
     * @param request 请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("获取商品活动价（直降）详情")
    @Log(name = "ActivityDubboService#getProductActPriceDetail")
    public Result<GetProductActPriceDetailResponse> getProductActPriceDetail(GetProductActPriceDetailRequest request) {
        try {
            GetProductActPriceDetailResponse response = productActivityService.getProductActPriceDetail(request);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on getProductActPriceDetail. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on getProductActPriceDetail. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 获取单品页可以参加的活动（这是单商品的，升级版的为getProductActV2）
     *
     * @param request 请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("商城产品站获取商品可以参加的活动")
    @Log(name = "ActivityDubboService#getProductAct")
    public Result<GetProductActResponse> getProductAct(GetProductActRequest request) {
        try {
            GetProductActResponse response = productActivityService.getProductAct(request);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on getProductAct. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on getProductAct. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 获取活动详情
     *
     * @param request 请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("获取活动详情")
    @Log(name = "ActivityDubboService#getActivityDetail")
    public Result<GetActivityDetailResponse> getActivityDetail(GetActivityDetailRequest request) {
        try {
            GetActivityDetailResponse response = productActivityService.getActivityDetail(request);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on getActivityDetail. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on getActivityDetail. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 批量获取活动详情
     *
     * @param request 请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("批量获取活动详情")
    @Log(name = "ActivityDubboService#batchGetActivityDetail")
    public Result<BatchGetActivityDetailResponse> batchGetActivityDetail(BatchGetActivityDetailRequest request) {
        try {
            BatchGetActivityDetailResponse response = productActivityService.batchGetActivityDetail(request);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on batchGetActivityDetail. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on batchGetActivityDetail. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 获取活动详情
     *
     * @param request 请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("获取区域活动详情")
    @Log(name = "ActivityDubboService#getActivityAreaDetail")
    public Result<GetActivityAreaDetailResponse> getActivityAreaDetail(GetActivityAreaDetailRequest request) {
        try {
            GetActivityAreaDetailResponse response = productActivityService.getActivityAreaDetail(request);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on getActivityAreaDetail. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on getActivityAreaDetail. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 获取商品门店活动价 (直降 | 门店价）
     *
     * @param request 请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("获取商品在门店的活动价（直降 | 门店价）")
    @Log(name = "ActivityDubboService#getStoreActPrice")
    public Result<GetStoreActPriceResponse> getStoreActPrice(GetStoreActPriceRequest request) {
        try {
            GetStoreActPriceResponse response = productActivityService.getStoreActPrice(request);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on getStoreActPrice. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on getStoreActPrice. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 获取门店商品活动价 (直降 | 门店价）
     * <p>
     * 注：全量获取门店下已经配置了活动价（直降｜门店价）的商品价
     * </p>
     *
     * @param request 请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("获取门店下商品活动价 (直降 | 门店价）")
    @Log(name = "ActivityDubboService#getStoreGoodsActPrice")
    public Result<GetStoreGoodsActPriceResponse> getStoreGoodsActPrice(GetStoreGoodsActPriceRequest request) {
        try {
            GetStoreGoodsActPriceResponse response = productActivityService.getStoreGoodsActPrice(request);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on getStoreActPrice. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on getStoreActPrice. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 获取商品有效活动列表 caller 换新中台
     */
    @Override
    @ApiDoc("空调换新立减-查询商品可参加的活动")
    @Log(name = "ActivityDubboService#getActivitysByGoods")
    public Result<GetActivitysByGoodsResponse> getActivitysByGoods(GetActivitysByGoodsRequest request) {
        try {
            GetActivitysByGoodsResponse response = productActivityService.getActivitysByGoods(request);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on getActivitysByGoods. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on getActivitysByGoods. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 加购时校验商品参加活动的有效性
     *
     * @param request 请求参数
     * @return 响应
     */
    @Override
    @Log(name = "ActivityDubboService#checkProductsAct")
    public Result<CheckProductsActResponse> checkProductsAct(CheckProductsActRequest request) {
        try {
            CheckProductsActResponse response = productActivityService.checkProductsAct(request);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on checkProductsAct. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on checkProductsAct. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    @Override
    @ApiDoc("会员权益查询参与活动次数")
    @Log(name = "ActivityDubboService#getProductActJoinInfo")
    public Result<ProductActJoinInfoResponse> getProductActJoinInfo(ProductActJoinInfoRequest request) {
        try {
            RequestParamValidator.valid(request);
            return Result.success(promotionUsedCountQueryService.queryUsedCount(request));
        } catch (Exception e) {
            log.error("Exception on getProductActJoinInfo. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 商城产品站单品页可参与活动接口（限流方法）
     *
     * @param request 请求参数
     * @return 响应
     */
    public Result<GetProductActV2Response> getProductActV2SentinelDowngrade(GetProductActV2Request request) {
        GetProductActV2Response response = new GetProductActV2Response();
        response.setValidPromotions(Collections.emptyMap());
        response.setPromotions(Collections.emptyMap());
        log.info("getProductActV2SentinelDowngrade, response:{}",  GsonUtil.toJson(response));
        return Result.success(response);
    }

    /**
     * 商城产品站单品页可参与活动接口（批量查商品，getProductAct 的升级版）
     *
     * @param request 请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("商城产品站获取商品可以参加的活动")
    @SentinelResource(value="com.xiaomi.nr.promotion.flows.ActivityDubboServiceImpl.getProductActV2", fallback="getProductActV2SentinelDowngrade")
    @Log(name = "ActivityDubboService#getProductActV2")
    public Result<GetProductActV2Response> getProductActV2(GetProductActV2Request request) {
        long startTime = System.currentTimeMillis();
        try {
            GetProductActV2Response response = productActivityService.getProductActV2(request);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on getProductActV2. costTime:{}ms, request:{}", System.currentTimeMillis() - startTime, request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on getProductActV2. costTime:{}ms, request:{}", System.currentTimeMillis() - startTime, request, e);
            return Result.fromException(e);
        }
    }



}
