package com.xiaomi.nr.promotion.domain.subsidyactivity.service.common.imp;

import com.xiaomi.nr.phoenix.api.dto.response.tradeInfo.TradeInfoQueryListResp;
import com.xiaomi.nr.promotion.domain.subsidyactivity.service.common.QualificationInfoService;
import com.xiaomi.nr.promotion.rpc.phoenix.SubsidyPhoenixProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;

import javax.annotation.Resource;

/**
 * 礼品卡信息服务
 *
 * <AUTHOR>
 * @date 2021/5/7 2:26 下午
 */
@Slf4j
@Service
public class QualificationInfoServiceImpl implements QualificationInfoService {

    @Resource
    private SubsidyPhoenixProxy subsidyPhoenixProxy;

    /**
     * 异步获取三方优惠资格码列表
     *
     * @param userId 用户ID
     * @return 礼品卡信息列表
     */
    @Async("phoenixTaskExecutor")
    @Override
    public ListenableFuture<TradeInfoQueryListResp> getQualificationInfoAsync(Long userId) {
        long startTime = System.currentTimeMillis();
        try {
            TradeInfoQueryListResp resp = subsidyPhoenixProxy.queryListForCoupon(userId);
            return AsyncResult.forValue(resp);
        } catch (Exception e) {
            log.error("get subsidyPhoenixProxy getQualificationInfo async error. uid:{}, ws:{}", userId, System.currentTimeMillis() - startTime, e);
            return AsyncResult.forExecutionException(e);
        }
    }

}
