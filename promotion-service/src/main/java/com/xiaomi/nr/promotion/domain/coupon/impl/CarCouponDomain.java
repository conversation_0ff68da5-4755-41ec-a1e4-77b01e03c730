package com.xiaomi.nr.promotion.domain.coupon.impl;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Request;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.domain.coupon.AbstractCouponDomain;
import com.xiaomi.nr.promotion.domain.coupon.facade.CarCouponFacade;
import com.xiaomi.nr.promotion.domain.coupon.facade.CouponFacade;
import com.xiaomi.nr.promotion.domain.coupon.facade.CouponFacadeInterFace;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.service.base.processor.CouponProcessorInterface;
import com.xiaomi.nr.promotion.engine.CouponTool;
import com.xiaomi.nr.promotion.entity.redis.OrgInfo;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.CouponGeneralType;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.nr.promotion.resource.external.OrgInfoExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CarCouponDomain extends AbstractCouponDomain {

    @Autowired
    @Qualifier("carCouponFacade")
    protected CarCouponFacade carCouponFacade;
    
    @Override
    protected CouponFacadeInterFace getCouponFacde() {
        return carCouponFacade;
    }
    
    @Override
    public void checkout(DomainCheckoutContext domainCheckoutContext) throws Exception {
        FromInterfaceEnum fromInterface = domainCheckoutContext.getFromInterface();
        CheckoutPromotionRequest request = domainCheckoutContext.getRequest();
        CheckoutPromotionV2Request requestV2 = domainCheckoutContext.getRequestV2();
        CheckoutPromotionResponse response = domainCheckoutContext.getResponse();
        CheckoutContext checkoutContext = domainCheckoutContext.getContext();
        // 下单逻辑
        if (fromInterface.equals(FromInterfaceEnum.CHECKOUT_ORDER)){
            carCouponFacade.checkoutForSubmit(request, response, checkoutContext);
        }
        // 结算页逻辑
        if (fromInterface.equals(FromInterfaceEnum.CHECKOUT_PAGE)){
            if (CollectionUtils.isNotEmpty(requestV2.getCouponCodes())) {
                carCouponFacade.checkoutForSubmit(requestV2, response, checkoutContext);
            } else {
                carCouponFacade.checkoutForCouponList(requestV2, response, checkoutContext);
            }
        }

        // 加购
        if (fromInterface.equals(FromInterfaceEnum.CHECKOUT_CART)){
            if (domainCheckoutContext.getCartRequest().getGetCouponList()) {
                carCouponFacade.checkoutForCouponList(requestV2, response, checkoutContext);
            }
        }

    }




}
