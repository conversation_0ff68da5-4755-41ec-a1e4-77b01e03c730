package com.xiaomi.nr.promotion.constant;

import java.util.Arrays;
import java.util.List;

/**
 * 礼品卡常量
 *
 * <AUTHOR>
 * @date 2022-06-06
 */
public class EcardConstant {

    /**
     * 北京消费券
     */
    public final static int TYPE_ID_BEIJING_COUPON = 28;

    /**
     * 2022年北京消费劵
     */
    public final static int TYPE_ID_2022BEIJING_COUPON = 45;

    /**
     * 北京消费券限制使用次数
     */
    public final static int USED_COUNT_LIMIT_BEIJING_COUPON = 10;

    /**
     * 门店回收礼品卡
     */
    public static final List<Long> STORE_RECYCLE_TYPES = Arrays.asList(47L,48L,49L,50L,55L,56L);

    /**
     * 线下均可以混用的回收礼品卡
     */
    public static final List<Long> OFFLINE_MIXED_RECYCLE_TYPES = Arrays.asList(51L, 57L, 58L);
}
