package com.xiaomi.nr.promotion.domain.phoenix.impl;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.domain.phoenix.AbstractPhoenixDomain;
import com.xiaomi.nr.promotion.domain.phoenix.facade.MiShopPhoenixFacade;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import com.xiaomi.nr.promotion.domain.phoenix.service.mishop.PhoenixService;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MiShopPhoenixDomain extends AbstractPhoenixDomain {

    @Autowired
    private MiShopPhoenixFacade miShopPhoenixFacade;

    @Override
    public void checkout(DomainCheckoutContext domainCheckoutContext) throws Exception {
        // 加购不过
        if (domainCheckoutContext.getFromInterface().equals(FromInterfaceEnum.CHECKOUT_CART)){
            return;
        }
        miShopPhoenixFacade.checkout(domainCheckoutContext);
    }
}
