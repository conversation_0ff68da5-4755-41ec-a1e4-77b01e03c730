package com.xiaomi.nr.promotion.model.promotionconfig;

import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import lombok.ToString;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 满折配置
 *
 * <AUTHOR>
 * @date 2021/5/31
 */
@ToString
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarShopVipDiscountPromotionConfig extends MultiPromotionConfig {
    /**
     * 折扣阶梯
     */
    private List<QuotaLevel> levelList;

    private Integer vipLevel;

    private Integer userJoinNumLimit;

    public List<QuotaLevel> getLevelList() {
        return levelList;
    }

    public void setLevelList(List<QuotaLevel> levelList) {
        this.levelList = levelList;
    }

    public Integer getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(Integer vipLevel) {
        this.vipLevel = vipLevel;
    }

    public Integer getUserJoinNumLimit() {
        return userJoinNumLimit;
    }

    public void setUserJoinNumLimit(Integer userJoinNumLimit) {
        this.userJoinNumLimit = userJoinNumLimit;
    }
}
