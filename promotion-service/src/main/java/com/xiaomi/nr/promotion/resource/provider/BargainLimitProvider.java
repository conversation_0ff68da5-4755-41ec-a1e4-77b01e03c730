package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.disruptor.ActStockActionEnum;
import com.xiaomi.nr.promotion.disruptor.ActStockManager;
import com.xiaomi.nr.promotion.disruptor.StockRecord;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 加价购数量资源
 *
 * <AUTHOR>
 * @date 2021/9/16
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class BargainLimitProvider implements ResourceProvider<BargainLimitProvider.ResContent> {
    private ResourceObject<BargainLimitProvider.ResContent> resourceObject;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Autowired
    private ActStockManager actStockManager;

    @Autowired
    private NacosConfig nacosConfig;

    @Override
    public ResourceObject<BargainLimitProvider.ResContent> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<BargainLimitProvider.ResContent> object) {
        this.resourceObject = object;
    }

    @Override
    public void lock() throws BizError {
        Map<Long, Map<String, Integer>> countMap = resourceObject.getContent().countMap;
        log.info("lock act bargain limit resource. orderId:{}, promotionId:{} countMap:{}", resourceObject.getOrderId(), resourceObject.getPromotionId(), countMap);
        if (MapUtils.isEmpty(countMap)) {
            log.warn("countMap is empty. resourceObject:{}", resourceObject);
            return;
        }
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("BargainLimitProvider.lock(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        doLock();
        log.info("lock act bargain limit ok. resourceObject:{}", resourceObject);
        actStockManager.publishEvent(() -> stockManage(ActStockActionEnum.COMMIT));
    }

    public void doLock() {
        Long promotionId = resourceObject.getPromotionId();
        Map<Long, Map<String, Integer>> countMap = resourceObject.getContent().countMap;
        countMap.forEach((groupId, numMap) -> {
            if (MapUtils.isEmpty(numMap)) {
                return;
            }
            numMap.forEach((sku, num) -> activityRedisDao.incrActBargainLimitNum(promotionId, sku, groupId, num));
        });
    }

    @Override
    public void consume() {
        log.info("consume act bargain limit resource. resourceObject{}", resourceObject);
    }

    @Override
    public void rollback() throws BizError {
        log.info("rollback act limit resource. resourceObject:{}", resourceObject);
        log.info("rollback act limit ok. resourceObject:{}", resourceObject);
    }

    @Override
    public String conflictText() {
        return "处理加价购数失败";
    }

    private StockRecord stockManage(ActStockActionEnum actionEnum) {
        return new StockRecord().setActivityId(getResource().getPromotionId())
                .setResourceType(getResource().getResourceType().getValue())
                .setAction(actionEnum.getCode())
                .setAdditionalCountMap(getResource().getContent().getCountMap())
                .setDateTimeMills(System.currentTimeMillis());
    }

    @Data
    public static class ResContent {
        /**
         * 扣减数组： key: groupId, val:(key: sku val:count)
         */
        private Map<Long, Map<String, Integer>> countMap;
    }
}
