package com.xiaomi.nr.promotion.dao.redis.impl;

import java.util.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.dao.redis.ActUserLimitPo;
import com.xiaomi.nr.promotion.domain.redpackage.model.RedPacket;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.util.RedisClusterAutoSwitchHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xiaomi.nr.promotion.constant.CacheKeyConstant;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.entity.redis.ActivityInfo;
import com.xiaomi.nr.promotion.util.DateTimeUtil;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.StringUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 活动redis 缓存操作对象
 *
 * <AUTHOR>
 * @date 2021/3/30
 */
@Component
@Slf4j
public class ActivityRedisDaoImpl implements ActivityRedisDao {
    /**
     * 门店对应活动列表LocalKey前缀
     */
    private static final String PREFIX_KEY_ORG_ACT_LIST = "act_id_list_orgcode_";
    /**
     * 应用对应活动列表LocalKey前缀
     */
    private static final String PREFIX_KEY_CLIENT_ACT_LIST = "act_id_list_clientid_";
    /**
     * 应用对应活动列表LocalKey前缀
     */
    private static final String PREFIX_KEY_CHANNEL_ACT_LIST = "act_id_list_channel_";
    /**
     * 对应活动列表本地缓存最大数量
     */
    private static final Long ACT_ID_LIST_MAX_CACHE_SIZE = 40000L;
    /**
     * 对应活动列表本地缓存时间
     */
    private static final Long ACT_ID_LIST_MAX_ALIVE_TTL = 30L;
    /**
     * 门店，应用对应活动ID列表缓存
     */
    private static final Cache<String, List<Long>> ACT_ID_LIST_CACHE = CacheBuilder.newBuilder()
            .maximumSize(ACT_ID_LIST_MAX_CACHE_SIZE)
            .expireAfterWrite(ACT_ID_LIST_MAX_ALIVE_TTL, TimeUnit.SECONDS)
            .build();

    /**
     * 缓存操作对象
     */
//    @Autowired
//    @Qualifier("stringPromotionRedisTemplate")
//    private RedisTemplate<String, String> redisTemplate;

    /**
     * act缓存操作对象
     */
    @Autowired
    @Qualifier("stringActRedisTemplate")
    private RedisTemplate<String, String> actRedisTemplate;

//    @Autowired
//    @Qualifier("stringActCacheRedisTemplate")
//    private RedisTemplate<String, String> actCacheRedisTemplate;

    /**
     * 缓存操作
     */
    @Autowired
    private RedisClusterAutoSwitchHelper switchHelper;

    @Autowired
    private NacosConfig nacosConfig;

    //language=Lua
    private static final String CONSUME_SCRIPT =
            "local keyLength = #KEYS\n" +
                    "for i = 1, keyLength do\n" +
                    "    local soldCnt = tonumber(redis.call('get',KEYS[i]))\n" +
                    "    local targetCnt = tonumber(ARGV[2*i])\n" +
                    "    local limitCnt = tonumber(ARGV[2*i-1])\n" +
                    "    if soldCnt == nil then\n" +
                    "        soldCnt=0\n" +
                    "    end\n" +
                    "    if soldCnt+targetCnt>limitCnt then\n" +
                    "        return \"fail\"\n" +
                    "    end\n" +
                    "end\n" +
                    "for i = 1, keyLength do\n" +
                    "    local soldCnt = tonumber(redis.call('get',KEYS[i]))\n" +
                    "    local targetCnt = tonumber(ARGV[2*i])\n" +
                    "    local limitCnt = tonumber(ARGV[2*i-1])\n" +
                    "    if soldCnt == nil then\n" +
                    "        soldCnt=0\n" +
                    "    end\n" +
                    "    local newSoldCnt=soldCnt+targetCnt\n" +
                    "    redis.call('set',KEYS[i],newSoldCnt)\n" +
                    "end\n" +
                    "return \"success\"";


    //language=Lua
    private static final String ROLLBACK_SCRIPT = "local keyLength = #KEYS\n" +
            "\n" +
            "for i = 1, keyLength do\n" +
            "    local soldCnt = tonumber(redis.call('get',KEYS[i]))\n" +
            "    local targetCnt = tonumber(ARGV[2*i])\n" +
            "    local limitCnt = tonumber(ARGV[2*i-1])\n" +
            "    local newSoldCnt=soldCnt - targetCnt\n" +
            "    if newSoldCnt < 0 then\n" +
            "        newSoldCnt = 0\n" +
            "    end\n" +
            "    redis.call('set',KEYS[i],newSoldCnt)\n" +
            "end\n" +
            "return \"success\"";

    private static final String SCRIPT_SUCCESS_CODE = "success";
    private static final String SCRIPT_FAIL_CODE = "fail";

    /**
     * 根据活动ID获取活动实体
     *
     * @param actId 活动ID
     * @return 缓存活动实体
     */
    @Override
    public ActivityInfo getActivityById(Long actId) {
        // ValueOperations<String, String> operations = actCacheRedisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT, actId.toString());
        String actJson = operations.get(key);
        return GsonUtil.fromJson(actJson, ActivityInfo.class);
    }

    /**
     * 根据部门Code获取活动ID列表
     *
     * @param orgCode 部门Code
     * @return 活动ID列表
     */
    @Override
    public List<Long> listActivityIdByOrgCode(String orgCode) {
        String localCacheKey = PREFIX_KEY_ORG_ACT_LIST + orgCode;
        List<Long> actIdList = ACT_ID_LIST_CACHE.getIfPresent(localCacheKey);
        if (actIdList != null) {
            return actIdList;
        }

        // ValueOperations<String, String> operations = actCacheRedisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_ID_LIST_ORGCODE, orgCode);
        String list = operations.get(key);
        actIdList = splitAndConvertIdList(list);
        ACT_ID_LIST_CACHE.put(localCacheKey, actIdList);
        return actIdList;
    }

    /**
     * 根据ClientId获取活动ID列表
     *
     * @param clientId 客户端ID
     * @return 活动ID列表
     */
    @Override
    public List<Long> listActivityIdByClientId(Long clientId) {
        String localCacheKey = PREFIX_KEY_CLIENT_ACT_LIST + clientId;
        List<Long> actIdList = ACT_ID_LIST_CACHE.getIfPresent(localCacheKey);
        if (actIdList != null) {
            return actIdList;
        }

        // ValueOperations<String, String> operations = actCacheRedisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_CLIENTID, clientId.toString());
        String list = operations.get(key);
        actIdList = splitAndConvertIdList(list);
        ACT_ID_LIST_CACHE.put(localCacheKey, actIdList);
        return actIdList;
    }

    private List<Long> splitAndConvertIdList(String list) {
        if (StringUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        String[] idStrList = StringUtils.split(list, ',');
        List<Long> res = new ArrayList<>();
        for (String idStr : idStrList) {
            res.add(Long.valueOf(idStr));
        }
        return res;
    }

    /**
     * 活动期间每人每天总量(Utype)
     *
     * @param actId     活动ID
     * @param uidType   用户类型
     * @param userId    用户ID
     * @param dateMills 13位时间戳
     * @return Num
     */
    @Override
    public Integer getActUtypePersonDayLimitNum(Long actId, String uidType, Long userId, Long dateMills) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_UTYPE_PERSON_DAY, String.valueOf(actId), String.valueOf(uidType), String.valueOf(userId), date);
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 活动期间每人活动总量(Utype)
     *
     * @param actId   活动ID
     * @param uidType 用户类型
     * @param userId  用户ID
     * @return Num
     */
    @Override
    public Integer getActUtypePersonLimitNum(Long actId, String uidType, Long userId) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_UTYPE_PERSON, String.valueOf(actId), String.valueOf(uidType), String.valueOf(userId));
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 活动期间每人每天总量
     *
     * @param actId     活动ID
     * @param userId    用户ID
     * @param dateMills 13位时间戳
     * @return Num
     */
    @Override
    public Integer getActPersonDayLimitNum(Long actId, Long userId, Long dateMills) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_PERSON_DAY, String.valueOf(actId), String.valueOf(userId), date);
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 活动期间每人活动总量
     *
     * @param actId  活动ID
     * @param userId 用户ID
     * @return Num
     */
    @Override
    public Integer getActPersonLimitNum(Long actId, Long userId) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_PERSON, String.valueOf(actId), String.valueOf(userId));
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 活动期间每人每店活动总量
     *
     * @param actId   活动Id
     * @param userId  用户Id（mid）
     * @param orgCode 门店Code
     * @return Num
     */
    @Override
    public Integer getActPersonStoreLimitNum(Long actId, Long userId, String orgCode) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_PERSON_STORE_LIMIT, String.valueOf(actId), String.valueOf(userId), orgCode);
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 活动期间每人每店活动总量(Utype)
     *
     * @param actId   活动Id
     * @param userId  用户Id（手机号）
     * @param orgCode 门店Code
     * @return Num
     */
    @Override
    public Integer getActUtypePersonStoreLimitNum(Long actId, Long userId, String orgCode) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_PERSON_MOBILE_STORE_LIMIT, String.valueOf(actId), String.valueOf(userId), orgCode);
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 扣减每人（mid）每个门店活动数量
     *
     * @param actId   活动id
     * @param userId  小米ID
     * @param orgCode 门店id
     * @param count   数量
     * @return 回滚次数
     */
    @Override
    public Long incrActPersonStoreUserIdLimitNum(Long actId, Long userId, String orgCode, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_PERSON_STORE_LIMIT, String.valueOf(actId), String.valueOf(userId), orgCode);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    /**
     * 回滚每人（mid）每个门店活动数量
     *
     * @param actId   活动id
     * @param userId  小米ID
     * @param orgCode 门店id
     * @param count   数量
     * @return 回滚次数
     */
    @Override
    public Long decrActPersonStoreUserIdLimitNum(Long actId, Long userId, String orgCode, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_PERSON_STORE_LIMIT, String.valueOf(actId), String.valueOf(userId), orgCode);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    /**
     * 扣减每人（手机号）每个门店活动数量
     *
     * @param actId   活动id
     * @param mobile  手机号
     * @param orgCode 门店id
     * @param count   数量
     * @return 回滚次数
     */
    @Override
    public Long incrActPersonStoreMobileLimitNum(Long actId, Long mobile, String orgCode, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_PERSON_MOBILE_STORE_LIMIT, String.valueOf(actId), String.valueOf(mobile), orgCode);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    /**
     * 回滚每人（手机号）每个门店活动数量
     *
     * @param actId   活动id
     * @param mobile  手机号
     * @param orgCode 门店id
     * @param count   数量
     * @return 回滚次数
     */
    @Override
    public Long decrActPersonStoreMobileLimitNum(Long actId, Long mobile, String orgCode, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_PERSON_MOBILE_STORE_LIMIT, String.valueOf(actId), String.valueOf(mobile), orgCode);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    /**
     * 活动期间全部门店活动每天总量
     *
     * @param actId     活动ID
     * @param dateMills 13位时间戳
     * @return Num
     */
    @Override
    public Integer getActAllStoreDayLimitNum(Long actId, Long dateMills) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_ALL_STORE_DAY, String.valueOf(actId), date);
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 活动期间全部门店活动总量
     *
     * @param actId 活动ID
     * @return Num
     */
    @Override
    public Integer getActAllStoreLimitNum(Long actId) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_ALL_STORE, String.valueOf(actId));
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 活动期间每个门店活动每天总量
     *
     * @param orgCode   门店Code
     * @param actId     活动ID
     * @param dateMills 13位时间戳
     * @return Num
     */
    @Override
    public Integer getActStoreDayLimitNum(Long actId, String orgCode, Long dateMills) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_STORE_DAY, orgCode, String.valueOf(actId), date);
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 直降活动mid每人参与次数key直降
     *
     * @param actId      活动ID
     * @param userId     用户ID
     * @param skuPackage sku/packageId
     * @return 参与次数
     */
    @Override
    public Integer getOnsaleUserLimitNum(Long actId, Long userId, String skuPackage) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_USER_USED, String.valueOf(actId),
                String.valueOf(userId), skuPackage);
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 直降活动手机号每人参与次数
     *
     * @param actId      活动ID
     * @param userId     用户ID
     * @param skuPackage sku/packageId
     * @return 参与次数
     */
    @Override
    public Integer getOnsaleMobileUserLimitNum(Long actId, Long userId, String skuPackage) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_USER_MOBILE_USED, String.valueOf(actId),
                String.valueOf(userId), skuPackage);
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 直降活动每天每个门店参与次数
     *
     * @param actId      活动ID
     * @param orgCode    门店Code
     * @param dateMills  13位时间戳
     * @param skuPackage sku/packageId
     * @return 参与次数
     */
    @Override
    public Integer getOnsaleStoreDayLimitNum(Long actId, String orgCode, Long dateMills, String skuPackage) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_DAY_ONE_USED, String.valueOf(actId),
                orgCode, date, skuPackage);
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 直降活动每天全国门店参与次数
     *
     * @param actId      活动ID
     * @param dateMills  13位时间戳
     * @param skuPackage sku/packageId
     * @return 参与次数
     */
    @Override
    public Integer getOnsaleAllStoreDayLimitNum(Long actId, Long dateMills, String skuPackage) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_DAY_ALL_USED, String.valueOf(actId),
                date, skuPackage);
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 直降活动期间每个门店参与次数key
     *
     * @param actId      活动ID
     * @param orgCode    门店Code
     * @param skuPackage sku/packageId
     * @return 参与次数
     */
    @Override
    public Integer getOnsaleStoreLimitNum(Long actId, String orgCode, String skuPackage) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_ACT_ONE_USED, String.valueOf(actId),
                orgCode, skuPackage);
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 直降活动期间全国门店参与次数key
     *
     * @param actId      活动ID
     * @param skuPackage sku/packageId
     * @return 参与次数
     */
    @Override
    public Integer getOnsaleAllStoreLimitNum(Long actId, String skuPackage) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_ACT_ALL_USED, String.valueOf(actId),
                skuPackage);
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 活动期间每个门店活动总量
     *
     * @param orgCode 门店Code
     * @param actId   活动ID
     * @return Num
     */
    @Override
    public Integer getActStoreLimitNum(Long actId, String orgCode) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_STORE, orgCode, String.valueOf(actId));
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 活动期间活动总量
     *
     * @param actId 活动ID
     * @return Num
     */
    @Override
    public Integer getActStatusLimitNum(Long actId) {
        HashOperations<String, String, String> operations = actRedisTemplate.opsForHash();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_STATUS, String.valueOf(actId));
        String num = operations.get(key, CacheKeyConstant.FILED_ACT_STATUS_JOINED_NUM);
        if (num == null) {
            return 0;
        }
        return Integer.valueOf(num);
    }

    /**
     * 是否存在用户参加过活动
     *
     * @param actId  活动ID
     * @param userId 用户ID
     * @return true/flse
     */
    @Override
    public Boolean isExistUserActDailyRecord(Long actId, Long userId, Long dateMills) {
        HashOperations<String, String, String> operations = actRedisTemplate.opsForHash();
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_DAILY_RECORD, String.valueOf(actId), date);
        String field = StringUtil.formatContent(CacheKeyConstant.FILED_ACT_DAILY_RECORD_UID, String.valueOf(userId));
        return operations.hasKey(key, field);
    }

    /**
     * 是否存在用户参加过活动总共
     *
     * @param actId  活动ID
     * @param userId 用户ID
     * @return true/flse
     */
    @Override
    public Boolean isExistUserActTotalRecord(Long actId, Long userId) {
        HashOperations<String, String, Integer> operations = actRedisTemplate.opsForHash();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_TOTAL_RECORD, String.valueOf(actId));
        String field = StringUtil.formatContent(CacheKeyConstant.FILED_ACT_TOTAL_RECORD_UID, String.valueOf(userId));
        return operations.hasKey(key, field);
    }

    /**
     * 获取买赠活动次数
     *
     * @param actId   活动次数
     * @param sku     SKU
     * @param groupId 组ID
     * @return 数量
     */
    @Override
    public Integer getActBuyGiftLimitNum(Long actId, String sku, Long groupId) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_BUYGIFT_LIMIT, String.valueOf(actId),
                sku, String.valueOf(groupId));
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 获取24年北京以旧换新补贴活动参加次数
     *
     * @param idCard 身份证
     * @return 数量
     */
    @Override
    public Integer getActPurchaseSubsidyLimitTotal(String idCard) {
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_PURCHASE_SUBSIDY_LIMIT_TOTAL, idCard);
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    @Override
    public Map<Integer, Integer> scanActPurchaseSubsidyLimitGroup(String idCard) {
        Map<Integer, Integer> spuGroupMap = Maps.newHashMap();
        Cursor<String> cursor = null;
        try {

            String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_PURCHASE_SUBSIDY_LIMIT_TOTAL, idCard);
            cursor = switchHelper.getRedisTemplate().opsForSet().scan(key, ScanOptions.scanOptions().count(100).build());
            long currentTime = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
            while (cursor.hasNext()) {
                byte[] value = cursor.next().getBytes();
                Integer limit = GsonUtil.fromJson(new String(value), Integer.class);
                spuGroupMap.put(limit, limit);
            }
        } finally {
            // 关闭cursor
            cursor.close();
        }
        return spuGroupMap;
    }

    /**
     * 获取24年北京以旧换新补贴活动参加次数
     *
     * @param idCard   身份证
     * @param spuGroup 品类
     * @return 数量
     */
    @Override
    public Integer getActPurchaseSubsidyLimitGroup(String idCard, Integer spuGroup) {
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_PURCHASE_SUBSIDY_LIMIT_GROUP, idCard, String.valueOf(spuGroup));
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 获取加价购数量
     *
     * @param actId   活动次数
     * @param sku     SKU
     * @param groupId 组ID
     * @return 数量
     */
    @Override
    public Integer getActBargainLimitNum(Long actId, String sku, Long groupId) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_BARGAIN_LIMIT, String.valueOf(actId),
                sku, String.valueOf(groupId));
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 获取商品活动库存数据
     *
     * @param actId 活动Id
     * @param sku   SKU
     * @return 数量
     */
    @Override
    public Integer getActGoodsLimitNum(Long actId, String sku) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_GOODS_LIMIT, String.valueOf(actId), sku);
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 获取商品每个门店活动库存
     *
     * @param actId      活动id
     * @param skuPackage sku/packageId
     * @param orgCode    门店id
     * @return 数量
     */
    @Override
    public Integer getActGoodsStoreLimitNum(Long actId, String skuPackage, String orgCode) {
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_GOODS_STORE_LIMIT, String.valueOf(actId), skuPackage, orgCode);
        String limitNum = operations.get(key);
        if (limitNum == null) {
            return 0;
        }
        return Integer.parseInt(limitNum);
    }

    /**
     * 批量获取商品每个门店活动库存
     *
     * @param orgCode     门店id
     * @param actId       活动id
     * @param skuPackages sku/packageId
     * @return 数量
     */
    @Override
    public Map<String, Long> batchGetActGoodsStoreLimitNum(String orgCode, Long actId, List<String> skuPackages) {
        if (CollectionUtils.isEmpty(skuPackages)) {
            return MapUtils.EMPTY_MAP;
        }
        List<String> keys = new ArrayList<>();
        skuPackages.forEach(skuPackage -> {
            String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_GOODS_STORE_LIMIT, String.valueOf(actId), skuPackage, orgCode);
            keys.add(key);
        });

        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        List<String> limits = operations.multiGet(keys);

        Map<String, Long> result = new HashMap<>(skuPackages.size());
        for (int i = 0; i < skuPackages.size(); i++) {
            if (limits == null || limits.size() == 0 || limits.get(i) == null) {
                result.put(skuPackages.get(i), 0L);
                continue;
            }
            String limit = limits.get(i);
            result.put(skuPackages.get(i), Long.parseLong(limit));
        }
        return result;
    }

    /**
     * 扣减商品每个门店活动库存
     *
     * @param actId      活动id
     * @param skuPackage sku/packageId
     * @param orgCode    门店id
     * @param count      数量
     * @return 回滚次数
     */
    @Override
    public Long incrActGoodsStoreLimitNum(Long actId, String skuPackage, String orgCode, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_GOODS_STORE_LIMIT, String.valueOf(actId), skuPackage, orgCode);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    /**
     * 回滚商品每个门店活动库存
     *
     * @param actId      活动id
     * @param skuPackage sku/packageId
     * @param orgCode    门店id
     * @param count      数量
     * @return 回滚次数
     */
    @Override
    public Long decrActGoodsStoreLimitNum(Long actId, String skuPackage, String orgCode, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_GOODS_STORE_LIMIT, String.valueOf(actId), skuPackage, orgCode);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }


    @Override
    public Long incrActStatusLimitNum(Long actId, Long orderId, Integer count, Long limitNum) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_STATUS, String.valueOf(actId));
        String field = CacheKeyConstant.FILED_ACT_STATUS_JOINED_NUM;
        HashOperations<String, String, Long> operations = actRedisTemplate.opsForHash();
        return operations.increment(key, field, count);
    }

    /**
     * 回滚活动参与次数
     *
     * @param actId   活动ID
     * @param orderId 订单ID
     * @param count   次数
     * @return 回滚次数
     */
    @Override
    public Long decrActStatusLimitNum(Long actId, Long orderId, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_STATUS, String.valueOf(actId));
        String field = CacheKeyConstant.FILED_ACT_STATUS_JOINED_NUM;
        HashOperations<String, String, Long> operations = actRedisTemplate.opsForHash();
        return operations.increment(key, field, -count);
    }

    /**
     * 插入存在用户参加过活动每天
     *
     * @param actId      活动ID
     * @param userId     用户ID
     * @param dateMills  13位时间戳
     * @param orderId    订单ID
     * @param expireTime 过期时间 > 0 为有效 （秒）
     */
    @Override
    public void setUserActDailyRecord(Long actId, Long userId, Long dateMills, Long orderId, Long expireTime) throws BizError {
        // 写开关关闭: 阻断缓存写
        if (!nacosConfig.isWriteSwitch()) {
            log.error("ActivityRedisDaoImpl.setUserActDailyRecord(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_DAILY_RECORD, String.valueOf(actId), date);
        String field = StringUtil.formatContent(CacheKeyConstant.FILED_ACT_DAILY_RECORD_UID, String.valueOf(userId));
        HashOperations<String, String, String> operations = actRedisTemplate.opsForHash();
        operations.put(key, field, String.valueOf(orderId));
        if (expireTime != null && expireTime > 0) {
            // redisTemplate.expireAt(key, new Date(expireTime * 1000));
            switchHelper.getRedisTemplate().expireAt(key, new Date(expireTime * 1000));
        }
    }

    /**
     * 删除用户参加过活动每天
     *
     * @param actId     活动ID
     * @param userId    用户ID
     * @param dateMills 13位时间戳
     * @return 影响数量
     */
    @Override
    public Long delUserActDailyRecord(Long actId, Long userId, Long dateMills) {
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_DAILY_RECORD, String.valueOf(actId), date);
        String field = StringUtil.formatContent(CacheKeyConstant.FILED_ACT_DAILY_RECORD_UID, String.valueOf(userId));
        HashOperations<String, String, String> operations = actRedisTemplate.opsForHash();
        return operations.delete(key, field);
    }

    /**
     * 插入存在用户参加过活动总共
     *
     * @param actId      活动ID
     * @param userId     用户ID
     * @param orderId    订单ID
     * @param expireTime 过期时间 > 0 为有效 （秒）
     */
    @Override
    public void setUserActTotalRecord(Long actId, Long userId, Long orderId, Long expireTime) throws BizError {
        if (!nacosConfig.isWriteSwitch()) {
            log.error("ActivityRedisDaoImpl.setUserActTotalRecord(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_TOTAL_RECORD, String.valueOf(actId));
        String field = StringUtil.formatContent(CacheKeyConstant.FILED_ACT_TOTAL_RECORD_UID, String.valueOf(userId));
        HashOperations<String, String, String> operations = actRedisTemplate.opsForHash();
        operations.put(key, field, String.valueOf(orderId));
        if (expireTime != null && expireTime > 0) {
            // redisTemplate.delete(key);
            switchHelper.getRedisTemplate().delete(key);
        }
    }

    /**
     * 删除存在用户参加过活动总共
     *
     * @param actId  活动ID
     * @param userId 用户ID
     * @return 影响数量
     */
    @Override
    public Long delUserActTotalRecord(Long actId, Long userId) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_TOTAL_RECORD, String.valueOf(actId));
        String field = StringUtil.formatContent(CacheKeyConstant.FILED_ACT_TOTAL_RECORD_UID, String.valueOf(userId));
        HashOperations<String, String, String> operations = actRedisTemplate.opsForHash();
        return operations.delete(key, field);
    }

    @Override
    public Long incrActPersonDayLimitNum(Long actId, Long userId, Long dateMills, Integer count, Long limitNum) {
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_PERSON_DAY, String.valueOf(actId),
                String.valueOf(userId), date);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    @Override
    public Long decrActPersonDayLimitNum(Long actId, Long userId, Long dateMills, Integer count) {
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_PERSON_DAY, String.valueOf(actId),
                String.valueOf(userId), date);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    @Override
    public Long incrActPersonLimitNum(Long actId, Long userId, Integer count, Long limitNum) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_PERSON, String.valueOf(actId),
                String.valueOf(userId));
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    @Override
    public Long decrActPersonLimitNum(Long actId, Long userId, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_PERSON, String.valueOf(actId),
                String.valueOf(userId));
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    @Override
    public Long incrActUtypePersonDayLimitNum(Long actId, String uidType, Long userId, Long dateMills, Integer count, Long limitNum) {
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_UTYPE_PERSON_DAY, uidType, String.valueOf(actId),
                String.valueOf(userId), date);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    @Override
    public Long decrActUtypePersonDayLimitNum(Long actId, String uidType, Long userId, Long dateMills, Integer count) {
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_UTYPE_PERSON_DAY, uidType, String.valueOf(actId),
                String.valueOf(userId), date);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    @Override
    public Long incrActUtypePersonLimitNum(Long actId, String uidType, Long userId, Integer count, Long limitNum) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_UTYPE_PERSON, uidType, String.valueOf(actId),
                String.valueOf(userId));
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    @Override
    public Long decrActUtypePersonLimitNum(Long actId, String uidType, Long userId, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_UTYPE_PERSON, uidType, String.valueOf(actId),
                String.valueOf(userId));
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    @Override
    public Long incrActAllStoreDayLimitNum(Long actId, Long dateMills, Integer count, Long limitNum) {
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_ALL_STORE_DAY, String.valueOf(actId), date);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    @Override
    public Long decrActAllStoreDayLimitNum(Long actId, Long dateMills, Integer count) {
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_ALL_STORE_DAY, String.valueOf(actId), date);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    @Override
    public Long incrActAllStoreLimitNum(Long actId, Integer count, Long limitNum) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_ALL_STORE, String.valueOf(actId));
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    @Override
    public Long decrActAllStoreLimitNum(Long actId, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_ALL_STORE, String.valueOf(actId));
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    @Override
    public Long incrActStoreDayLimitNum(Long actId, String orgCode, Long dateMills, Integer count, Long limitNum) {
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_STORE_DAY, orgCode, String.valueOf(actId), date);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    @Override
    public Long decrActStoreDayLimitNum(Long actId, String orgCode, Long dateMills, Integer count) {
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_STORE_DAY, orgCode, String.valueOf(actId), date);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    @Override
    public Long incrActStoreLimitNum(Long actId, String orgCode, Integer count, Long limitNum) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_STORE, orgCode, String.valueOf(actId));
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    @Override
    public Long decrActStoreLimitNum(Long actId, String orgCode, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_NUM_STORE, orgCode, String.valueOf(actId));
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    /**
     * 增加直降活动mid每人参与次数
     *
     * @param actId      活动ID
     * @param userId     用户ID
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @param limitNum   限制数量
     * @return NUM
     */
    @Override
    public Long incrOnsaleUserLimitNum(Long actId, Long userId, String skuPackage, Integer count, Long limitNum) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_USER_USED, String.valueOf(actId),
                String.valueOf(userId), skuPackage);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    /**
     * 减少直降活动mid每人参与次数
     *
     * @param actId      活动ID
     * @param userId     用户ID
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @return NUM
     */
    @Override
    public Long decrOnsaleUserLimitNum(Long actId, Long userId, String skuPackage, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_USER_USED, String.valueOf(actId),
                String.valueOf(userId), skuPackage);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    /**
     * 增加直降活动手机号每人参与次数
     *
     * @param actId      活动ID
     * @param userId     用户ID
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @param limitNum   限制数量
     * @return 参与次数
     */
    @Override
    public Long incrOnsaleMobileUserLimitNum(Long actId, Long userId, String skuPackage, Integer count, Long limitNum) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_USER_MOBILE_USED, String.valueOf(actId),
                String.valueOf(userId), skuPackage);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    /**
     * 减少直降活动手机号每人参与次数
     *
     * @param actId      活动ID
     * @param userId     用户ID
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @return 参与次数
     */
    @Override
    public Long decrOnsaleMobileUserLimitNum(Long actId, Long userId, String skuPackage, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_USER_MOBILE_USED, String.valueOf(actId),
                String.valueOf(userId), skuPackage);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    /**
     * 增加直降活动每天每个门店参与次数
     *
     * @param actId      活动ID
     * @param orgCode    门店Code
     * @param dateMills  13位时间戳
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @param limitNum   限制数量
     * @return 参与次数
     */
    @Override
    public Long incrOnsaleStoreDayLimitNum(Long actId, String orgCode, Long dateMills, String skuPackage,
                                           Integer count, Long limitNum) {
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_DAY_ONE_USED, String.valueOf(actId),
                orgCode, date, skuPackage);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    /**
     * 减少直降活动每天每个门店参与次数
     *
     * @param actId      活动ID
     * @param orgCode    门店Code
     * @param dateMills  13位时间戳
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @return 参与次数
     */
    @Override
    public Long decrOnsaleStoreDayLimitNum(Long actId, String orgCode, Long dateMills, String skuPackage, Integer count) {
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_DAY_ONE_USED, String.valueOf(actId),
                orgCode, date, skuPackage);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    /**
     * 增加直降活动每天全国门店参与次数
     *
     * @param actId      活动ID
     * @param dateMills  13位时间戳
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @param limitNum   限制数量
     * @return 参与次数
     */
    @Override
    public Long incrOnsaleAllStoreDayLimitNum(Long actId, Long dateMills, String skuPackage, Integer count, Long limitNum) {
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_DAY_ALL_USED, String.valueOf(actId),
                date, skuPackage);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    /**
     * 减少直降活动每天全国门店参与次数
     *
     * @param actId      活动ID
     * @param dateMills  13位时间戳
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @return 参与次数
     */
    @Override
    public Long decrOnsaleAllStoreDayLimitNum(Long actId, Long dateMills, String skuPackage, Integer count) {
        String date = DateTimeUtil.formatByPattern(dateMills, "yyyy-MM-dd");
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_DAY_ALL_USED, String.valueOf(actId),
                date, skuPackage);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    /**
     * 增加直降活动期间每个门店参与次数key
     *
     * @param actId      活动ID
     * @param orgCode    门店Code
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @param limitNum   限制数量
     * @return 参与次数
     */
    @Override
    public Long incrOnsaleStoreLimitNum(Long actId, String orgCode, String skuPackage, Integer count, Long limitNum) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_ACT_ONE_USED, String.valueOf(actId),
                orgCode, skuPackage);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    /**
     * 减少直降活动期间每个门店参与次数key
     *
     * @param actId      活动ID
     * @param orgCode    门店Code
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @return 参与次数
     */
    @Override
    public Long decrOnsaleStoreLimitNum(Long actId, String orgCode, String skuPackage, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_ACT_ONE_USED, String.valueOf(actId),
                orgCode, skuPackage);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    /**
     * 增加直降活动期间全国门店参与次数key
     *
     * @param actId      活动ID
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @param limitNum   限制数量
     * @return 参与次数
     */
    @Override
    public Long incrOnsaleAllStoreLimitNum(Long actId, String skuPackage, Integer count, Long limitNum) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_ACT_ALL_USED, String.valueOf(actId),
                skuPackage);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    /**
     * 减少直降活动期间全国门店参与次数key
     *
     * @param actId      活动ID
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @return 参与次数
     */
    @Override
    public Long decrOnsaleAllStoreLimitNum(Long actId, String skuPackage, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ONSALE_LIMIT_ACT_ALL_USED, String.valueOf(actId),
                skuPackage);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    /**
     * 赠加买赠活动次数
     *
     * @param actId   活动次数
     * @param sku     SKU
     * @param groupId 组ID
     * @param count   数量
     * @return 数量
     */
    @Override
    public Long incrActGiftLimitNum(Long actId, String sku, Long groupId, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_BUYGIFT_LIMIT, String.valueOf(actId), sku, String.valueOf(groupId));
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    /**
     * 减少买赠活动次数
     *
     * @param actId   活动次数
     * @param sku     SKU
     * @param groupId 组ID
     * @param count   数量
     * @return 数量
     */
    @Override
    public Long decrActGiftLimitNum(Long actId, String sku, Long groupId, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_BUYGIFT_LIMIT, String.valueOf(actId), sku, String.valueOf(groupId));
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    /**
     * 赠加加价购活动次数
     *
     * @param actId   活动次数
     * @param sku     SKU
     * @param groupId 组ID
     * @param count   数量
     * @return 数量
     */
    @Override
    public Long incrActBargainLimitNum(Long actId, String sku, Long groupId, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_BARGAIN_LIMIT, String.valueOf(actId), sku, String.valueOf(groupId));
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    /**
     * 减少加价购活动次数
     *
     * @param actId   活动次数
     * @param sku     SKU
     * @param groupId 组ID
     * @param count   数量
     * @return 数量
     */
    @Override
    public Long decrActBargainLimitNum(Long actId, String sku, Long groupId, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_BARGAIN_LIMIT, String.valueOf(actId), sku, String.valueOf(groupId));
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

    /**
     * 赠加活动主品活动次数
     *
     * @param actId 活动次数
     * @param sku   SKU
     * @param count 数量
     * @return 数量
     */
    @Override
    public Long incrActGoodsLimitNum(Long actId, String sku, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_GOODS_LIMIT, String.valueOf(actId), sku);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, count);
    }

    /**
     * 减少活动主品活动次数
     *
     * @param actId 活动次数
     * @param sku   SKU
     * @param count 数量
     * @return 数量
     */
    @Override
    public Long decrActGoodsLimitNum(Long actId, String sku, Integer count) {
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_GOODS_LIMIT, String.valueOf(actId), sku);
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        return operations.increment(key, -count);
    }

//    @Override
//    public void batchConsumeSubsidyLimit(List<ActUserLimitPo> actUserLimitPoList) throws BizError {
//        String result = executeSubsidyScript(actUserLimitPoList, CONSUME_SCRIPT);
//        if (!Objects.equals(SCRIPT_SUCCESS_CODE, result)) {
//            throw ExceptionHelper.create(ErrCode.CODE_ACT_USER_RECORD_INSERT_FAIL, "用户限购扣减失败");
//        }
//    }
//
//
//    @Override
//    public void batchRollbackSubsidyLimit(List<ActUserLimitPo> actUserLimitPoList) throws BizError {
//        String result = executeSubsidyScript(actUserLimitPoList, ROLLBACK_SCRIPT);
//        if (!Objects.equals(SCRIPT_SUCCESS_CODE, result)) {
//            throw ExceptionHelper.create(ErrCode.CODE_ACT_USER_RECORD_INSERT_FAIL, "用户限购归还失败");
//        }
//    }

    public void batchConsumeSubsidyStockV2(List<ActUserLimitPo> actUserLimitPoList) throws BizError {
        if (CollectionUtils.isEmpty(actUserLimitPoList)) {
            return;
        }

        List<ActUserLimitPo> successList = new ArrayList<>();

        //扣减。统计结果
        for (ActUserLimitPo actStockPo : actUserLimitPoList) {
            Long current = null;
            try {
                current = switchHelper.getOperations().increment(actStockPo.getLockKey(), actStockPo.getTarget());
                Long newCurrent = NumberUtils.toLong(switchHelper.getOperations().get(actStockPo.getLockKey()));
                log.info("newPurchaseSubsidyLimitInfo lock ,lockKey:{}, current:{}, newCurrent:{}", actStockPo.getLockKey(), current, newCurrent);
                if (Objects.isNull(current) || current < newCurrent) {
                    current = newCurrent;
                }
            } catch (Exception e) {
                log.error("batchConsumeSubsidyStockV2 increment error. data:{}", GsonUtil.toJson(actStockPo), e);
                rollbackSuccessList(successList);
                throw e;

            }

            if (current <= actStockPo.getLimit()) {
                successList.add(actStockPo);
            } else {
                rollbackSuccessList(successList);
                throw ExceptionHelper.create(ErrCode.CODE_ACT_USER_RECORD_INSERT_FAIL, "用户限购扣减失败");

            }

        }
    }

    private void rollbackSuccessList(List<ActUserLimitPo> successList) throws BizError {
        //rollback
        for (ActUserLimitPo success : successList) {
            Long decrement = switchHelper.getOperations().decrement(success.getLockKey(), success.getTarget());
            log.info("newPurchaseSubsidyLimitInfo lock rollback ,decrement:{}, ActUserLimitPo:{}", decrement, success);

        }
    }

    /**
     * 批量回滚补贴限制
     *
     * @param actUserLimitPoList 用户限制信息列表
     * @throws BizError 业务异常
     */
    public void batchRollbackSubsidyLimitV2(List<ActUserLimitPo> actUserLimitPoList) throws BizError {
        if (CollectionUtils.isEmpty(actUserLimitPoList)) {
            return;
        }

        //扣减。统计结果
        for (ActUserLimitPo actStockPo : actUserLimitPoList) {
            Long current = switchHelper.getOperations().decrement(actStockPo.getLockKey(), actStockPo.getTarget());


            Long newCurrent = NumberUtils.toLong(switchHelper.getOperations().get(actStockPo.getLockKey()));
            log.info("newPurchaseSubsidyLimitInfo rollback ,lockKey:{}, current:{}, newCurrent:{}", actStockPo.getLockKey(), current, newCurrent);


            if (newCurrent < 0) {
                switchHelper.getOperations().set(actStockPo.getLockKey(), "0");
            }

        }
    }

    private String executeSubsidyScript(List<ActUserLimitPo> actUserLimitPoList, String rollbackScript) throws BizError {
        try {
            DefaultRedisScript<String> redisScript = new DefaultRedisScript<>(rollbackScript, String.class);
            List<String> keys = new ArrayList<>();
            List<String> args = new ArrayList<>();
            for (ActUserLimitPo actUserLimitPo : actUserLimitPoList) {
                keys.add(actUserLimitPo.getLockKey());
                args.add(String.valueOf(actUserLimitPo.getLimit()));
                args.add(String.valueOf(actUserLimitPo.getTarget()));
            }
            return switchHelper.getRedisTemplate().execute(redisScript, keys, args.toArray());
        } catch (Exception e) {
            log.error("executeSubsidyScript error.", e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "操作用户限购失败");
        }

    }
}
