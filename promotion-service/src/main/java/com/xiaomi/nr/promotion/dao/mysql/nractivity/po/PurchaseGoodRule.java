package com.xiaomi.nr.promotion.dao.mysql.nractivity.po;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/11/26 17:04
 */
@Data
public class PurchaseGoodRule implements Serializable {
    private static final long serialVersionUID = -1578554485062257423L;

    /**
     * 商品对应能效等级
     */
    private String cateLevel;

    /**
     * 极简模式下：商品优惠码
     */
    private String discountCode;

    /**
     * 极简模式下：商品所属品类名称
     */
    private String categoryName;

    /**
     * 极简模式下：商品69码，一品多码，逗号隔开
     */
    private String code69;

    /**
     * 极简模式下：商品自定义品类Code
     */
    private String customCateCode;

    /**
     * 极简模式下：云闪付活动ID
     */
    private String unionPayActivityId;

    /**
     * 广东模式下：商品名称
     */
    private String itemName;

    /**
     * 广东模式下：家电品牌
     */
    private String brand;

    /**
     * 广东模式下：规格型号
     */
    private String specModel;

    /**
     * O2O国补校验SN
     */
    private Boolean checkSn;

}
