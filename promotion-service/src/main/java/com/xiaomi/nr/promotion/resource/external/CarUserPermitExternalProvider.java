package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.nr.promotion.activity.pool.ActSearchParam;
import com.xiaomi.nr.promotion.activity.pool.CarShopActivitySearcher;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.UserCarPermitEnum;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.rpc.carpermit.UserCarPermitProxyService;
import com.xiaomi.nr.promotion.util.CarShopBuyReduceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.List;
import java.util.Objects;

/**
 * 车主身份请求
 *
 * <AUTHOR>
 * @date 2025/5/10 14:05
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class CarUserPermitExternalProvider extends ExternalDataProvider<UserCarPermitEnum> {

    @Autowired
    private UserCarPermitProxyService userCarPermitProxyService;

    @Autowired
    private CarShopActivitySearcher carShopActivitySearcher;

    private ListenableFuture<UserCarPermitEnum> future;

    @Override
    protected boolean switchOn() {
        return true;
    }

    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        if (!Objects.equals(request.getChannel(), ChannelEnum.CAR_SHOP.getValue())) {
            return;
        }
        if (request.getUserId() == null || request.getUserId() <= 0) {
            return;
        }
        ActSearchParam param = new ActSearchParam()
                .setChannel(request.getChannel())
                .setGoodsList(carShopActivitySearcher.createSearchGoods(request.getCartList()));
        List<ActivityTool> activityTools = carShopActivitySearcher.searchCarActivity(param);
        ActivityTool carReduceActivity = ListUtils.emptyIfNull(activityTools)
                .stream()
                .filter(activityTool -> activityTool.getType() == PromotionToolType.BUY_REDUCE)
                .findFirst()
                .orElse(null);
        if (Objects.isNull(carReduceActivity)) {
            return;
        }
        boolean needCarOwnerInfo = CarShopBuyReduceUtil.needCarOwnerInfo(carReduceActivity);
        if (!needCarOwnerInfo) {
            return;
        }
        this.future = userCarPermitProxyService.queryUserPermitAsync(request.getUserId());
    }

    @Override
    protected long getTimeoutMills() {
        return 1000;
    }

    @Override
    protected ListenableFuture<UserCarPermitEnum> getFuture() {
        return Objects.isNull(future) ? AsyncResult.forValue(UserCarPermitEnum.UNKNOWN) : future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.USER_CAR_PERMIT;
    }
}
