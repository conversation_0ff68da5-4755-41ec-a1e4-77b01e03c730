package com.xiaomi.nr.promotion.engine;

import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.CheckProductsActResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.GetProductActRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;
import java.util.Map;

/**
 * 促销工具类--用于缓存池
 *
 * <AUTHOR>
 * @date 2021/3/24
 */
public interface ActivityTool extends PromotionTool, BizPlatformComponent{
    /**
     * 活动ID
     *
     * @return ID
     */
    long getId();

    /**
     * Load信息到本地缓存
     *
     * @param config 配置
     * @return true/false
     * @throws BizError 业务异常
     */
    boolean load(AbstractPromotionConfig config) throws BizError;

    /**
     * 获取开始时间
     *
     * @return 10位时间戳
     */
    long getUnixStartTime();

    /**
     * 获取结束时间
     *
     * @return 10位时间戳
     */
    long getUnixEndTime();

    /**
     * 优惠信息输出
     *
     * @return 获取优惠信息
     */
    ActivityDetail getActivityDetail();

    /**
     * 获取产品站信息
     *
     * @param request   产品站信息
     * @param hierarchy 商品层级关系
     * @param isOrgTool 是否只检查门店
     * @return 优惠信息
     * @throws BizError 业务异常
     */
    default PromotionInfo getProductAct(GetProductActRequest request, GoodsHierarchy hierarchy, boolean isOrgTool) throws BizError {
        return null;
    }

    /**
     * 批量获取产品站信息
     *
     * @param goodItem  产品站信息
     * @return 优惠信息
     * @throws BizError 业务异常
     */
    default PromotionInfoDTO getMultiProductAct(MultiGoodItem goodItem) throws BizError {
        return null;
    }


    /**
     * 构建优惠信息
     *
     * @param context 上下文
     * @return 优惠信息
     * @throws BizError 业务异常
     */
    PromotionInfo buildCartPromotionInfo(LocalContext context) throws BizError;

    /**
     * 校验商品是否满足活动
     *
     * @param request 商品信息
     * @param goodsHierarchyMap 商品层级关系map
     * @return 不合法的商品列表
     * @throws BizError 业务异常
     */
    default List<CheckGoodsItem> checkProductsAct(CheckProductsActRequest request, Map<String, GoodsHierarchy> goodsHierarchyMap) throws BizError {
        return null;
    }

    AbstractPromotionConfig getPromotionConfig();
}
