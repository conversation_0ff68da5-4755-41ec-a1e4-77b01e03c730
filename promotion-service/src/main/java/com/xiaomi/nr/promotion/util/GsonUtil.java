package com.xiaomi.nr.promotion.util;

import com.google.gson.*;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * gson 序列化反序列化工具类
 *
 * <AUTHOR>
 * @date 2021/3/24
 */
@Slf4j
public class GsonUtil {

    private static final Gson GSON;

    static {
        GSON = new Gson();
    }

    /**
     * 将object对象转成json字符串
     *
     * @param object 具体对象
     * @return json字符串
     */
    public static String toJson(Object object) {
        if (GSON == null) {
            return null;
        }
        return GSON.toJson(object);
    }

    /**
     * 将json字符串转成泛型bean
     *
     * @param json json字符串
     * @param cls  需转化的类型
     * @return T类型对象
     */
    public static <T> T fromJson(String json, Class<T> cls) {
        if (GSON == null || json == null) {
            return null;
        }
        T t = null;
        try {
            t = GSON.fromJson(json, cls);
        } catch (JsonSyntaxException e) {
            log.error("gson from json to object err.", e);
        }
        return t;
    }

    /**
     * 将json反序列化转成list
     *
     * @param json json 字符串
     * @param cls  需要转化为列表类型的class
     * @param <T>  元素类型
     * @return T类型对象列表
     */
    public static <T> List<T> fromListJson(String json, Class<T> cls) {
        if (GSON == null || json == null) {
            return null;
        }
        List<T> list = new ArrayList<>();
        try {
            JsonElement jsonElement = JsonParser.parseString(json);
            if (jsonElement == null) {
                return null;
            }
            JsonArray array = jsonElement.getAsJsonArray();
            for (final JsonElement elem : array) {
                list.add(GSON.fromJson(elem, cls));
            }
        } catch (JsonSyntaxException e) {
            log.error("gson from json to list err.", e);
        }
        return list;
    }

    /**
     * 将json 反序列化成Map
     *
     * @param json json字符串
     * @return map对象
     */
    public static <K, V> Map<K, V> fromMapJson(String json, Type type) {
        if (GSON == null || json == null) {
            return null;
        }
        Map<K, V> map = null;
        try {
            map = GSON.fromJson(json, type);
        } catch (Exception e) {
            log.error("gson from map to json err.", e);
        }
        return map;
    }
}
