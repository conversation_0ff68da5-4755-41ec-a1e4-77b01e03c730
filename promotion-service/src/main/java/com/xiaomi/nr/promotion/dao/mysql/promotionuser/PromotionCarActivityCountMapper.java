package com.xiaomi.nr.promotion.dao.mysql.promotionuser;

import com.xiaomi.nr.promotion.entity.mysql.promotionuser.CarActivityCount;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description 整车参与活动记录表
 * <AUTHOR>
 * @date 2025-01-06 17:38
*/
@Repository
public interface PromotionCarActivityCountMapper {

    /**
     * 插入整车参与活动记录
     * @param carActivityCount 用户参与活动记录
     * @return 插入记录数
     */
    @Insert("INSERT INTO " +
            "promotion_car_activity_count " +
            "   (vid, promotion_id, num, extend, create_time, update_time) " +
            "VALUES " +
            "   (#{vid}, #{promotionId}, #{num}, #{extend}, #{createTime}, #{updateTime})")
    Integer insert(CarActivityCount carActivityCount);

    /**
     * 根据vid和活动id获取用户参与活动记录
     * @param vid
     * @param promotionId 活动id
     * @return 用户参与活动记录
     */
    @Select("select * from promotion_car_activity_count where vid = #{vid} and promotion_id = #{promotionId} " +
            "order by id desc limit 1")
    CarActivityCount getByVidAndPromotionId(@Param("vid") String vid, @Param("promotionId") Long promotionId);

    /**
     * 更新vId参与活动次数
     * @param vid
     * @param promotionId 活动ID
     * @param newNum 新的次数
     * @param oldNum 旧的次数
     * @param updateTime 更新时间
     * @return 更新记录数
     */
    @Update("update promotion_car_activity_count " +
            "set " +
            "   num = #{newNum}, extend = #{extend}, update_time = #{updateTime} " +
            "where vid = #{vid} and promotion_id = #{promotionId} and num = #{oldNum}")
    Integer updateCount(@Param("vid") String vid, @Param("promotionId") Long promotionId,
                        @Param("newNum") Integer newNum, @Param("oldNum") Integer oldNum, @Param("extend") String extend, @Param("updateTime") Long updateTime);


    List<CarActivityCount> getByVidAndPromotionIds(@Param("vid") String vid,
                                                   @Param("promotionIds") List<Long> promotionIds);
}
