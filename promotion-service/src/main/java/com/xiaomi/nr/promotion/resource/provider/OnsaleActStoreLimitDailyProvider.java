package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 直降每个门店每天参与活动次数
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OnsaleActStoreLimitDailyProvider implements ResourceProvider<OnsaleActStoreLimitDailyProvider.ActStoreDayLimit> {
    /**
     * 线上用户参与活动记录
     */
    private ResourceObject<ActStoreDayLimit> resourceObject;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Autowired
    private NacosConfig nacosConfig;

    @Override
    public ResourceObject<ActStoreDayLimit> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<ActStoreDayLimit> object) {
        this.resourceObject = object;
    }

    /**
     * 扣减库存
     */
    @Override
    public void lock() throws BizError {
        log.info("lock onsale act store daily limit resource. {}", resourceObject);
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("OnsaleActStoreLimitDailyProvider.lock(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        activityRedisDao.incrOnsaleStoreDayLimitNum(resourceObject.getContent().getActId(),
                resourceObject.getContent().getOrgCode(),
                resourceObject.getContent().getDateTimeMills(),
                resourceObject.getContent().getSkuPackage(),
                resourceObject.getContent().getCount(),
                resourceObject.getContent().getLimitNum());
        log.info("lock onsale act store daily limit resource ok. {}", resourceObject);
    }

    @Override
    public void consume() {
        log.info("consume onsale act store daily  resource. {}", resourceObject);
    }

    @Override
    public void rollback() throws BizError {
        log.info("rollback onsale act store daily limit resource. {}", resourceObject);
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("OnsaleActStoreLimitDailyProvider.rollback(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        activityRedisDao.decrOnsaleStoreDayLimitNum(resourceObject.getContent().getActId(),
                resourceObject.getContent().getOrgCode(),
                resourceObject.getContent().getDateTimeMills(),
                resourceObject.getContent().getSkuPackage(),
                resourceObject.getContent().getCount());
        log.info("rollback onsale act store daily limit resource ok. {}", resourceObject);
    }

    @Override
    public String conflictText() {
        return "减限购失败";
    }

    /**
     * 活动记录
     */
    @Data
    public static class ActStoreDayLimit {
        /**
         * 门店Code
         */
        private String orgCode;
        /**
         * 活动ID
         */
        private Long actId;
        /**
         * 时间(毫秒）
         */
        private Long dateTimeMills;
        /**
         * sku/package
         */
        private String skuPackage;
        /**
         * 扣减数量
         */
        private Integer count;
        /**
         * 过期时间 (秒）
         */
        private Long limitNum;
    }
}
