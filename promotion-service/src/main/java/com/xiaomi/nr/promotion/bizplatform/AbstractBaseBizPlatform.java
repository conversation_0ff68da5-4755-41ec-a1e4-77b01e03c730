package com.xiaomi.nr.promotion.bizplatform;

import com.xiaomi.nr.promotion.api.dto.CartPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Response;
import com.xiaomi.nr.promotion.domain.BaseDomain;
import com.xiaomi.nr.promotion.domain.PromotionDomainFactory;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.nr.promotion.tool.CheckoutResponseTool;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public abstract class AbstractBaseBizPlatform implements BaseBizPlatform, InitializingBean {

    @Autowired
    private CheckoutResponseTool checkoutResponseTool;

    @Autowired
    private PromotionDomainFactory promotionDomainFactory;


    public abstract BaseDomainList checkBaseDomainList();
    
    @Override
    public void checkout(DomainCheckoutContext domainCheckoutContext) throws Exception {
        BaseDomainList BaseDomainList = checkBaseDomainList();
        for (BaseDomain domain:BaseDomainList.domainList){
            domain.checkout(domainCheckoutContext);
        }

    }

    public void originalGenerateResponse(DomainCheckoutContext domainCheckoutContext) throws BizError {
        CheckoutPromotionRequest request = domainCheckoutContext.getRequest();
        CheckoutPromotionResponse response = domainCheckoutContext.getResponse();
        FromInterfaceEnum fromInterface = domainCheckoutContext.getFromInterface();
        CheckoutContext checkoutContext = domainCheckoutContext.getContext();
        CheckoutPromotionV2Response responseV2 = domainCheckoutContext.getResponseV2();
        CartPromotionResponse cartResponse = domainCheckoutContext.getCartResponse();
        if (fromInterface.equals(FromInterfaceEnum.CHECKOUT_ORDER)){
            checkoutResponseTool.convertCheckoutContextToResponse(checkoutContext, request, response);
        }else if (fromInterface.equals(FromInterfaceEnum.CHECKOUT_PAGE)){
            checkoutResponseTool.convertCheckoutContextToResponseV2(checkoutContext, request, responseV2);
        }else {
            checkoutResponseTool.convertCheckoutContextToCartResponse(checkoutContext, request, cartResponse);
        }
    }



    /**
     * 实例化资源列表
     * @return 实体
     */
    public BaseDomainList instanceBaseDomainList() {
        return new BaseDomainList();
    }

    public class BaseDomainList{

        private List<BaseDomain> domainList;

        public BaseDomainList addResourceList(Class<? extends BaseDomain> clazz){
            BaseDomain resource = promotionDomainFactory.getResource(clazz);
            if (this.domainList == null) {
                this.domainList = new ArrayList<>();
            }
            this.domainList.add(resource);
            return this;
        }

    }

}

