package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.concurrent.TimeUnit;

/**
 * 外部数据资源基类
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
@Slf4j
public abstract class ExternalDataProvider<T> {
    /**
     * 默认超时时间
     */
    protected static final long DEFAULT_TIMEOUT = 5000L;

    /**
     * 外部请求开关
     *
     * @return true/false
     */
    protected abstract boolean switchOn();

    /**
     * 初始化异步请求
     *
     * @param request 结算请求对象
     */
    protected abstract void doPrepare(CheckoutPromotionRequest request);

    /**
     * 超时时间
     *
     * @return 超时时间
     */
    protected abstract long getTimeoutMills();

    /**
     * 获取数据
     *
     * @return 数据future对象
     */
    protected abstract ListenableFuture<T> getFuture();

    /**
     * 外部资源类型
     *
     * @return ResourceExtType
     */
    public abstract ResourceExtType getResourceExtType();

    /**
     * 发起异步请求，暴露给外面
     *
     * @param request 结算请求对象
     */
    public void prepare(CheckoutPromotionRequest request) {
        boolean resourceSwitchOn = switchOn();
        if (resourceSwitchOn) {
            doPrepare(request);
            log.debug("external data:{} data prepare. request:{} ", getResourceExtType().getName(), request);
        } else {
            log.debug("external data:{} switch off.  request:{} ", getResourceExtType().getName(), request);
        }
    }

    /**
     * 获取数据结果
     *
     * @return 数据结果
     * @throws BizError 业务异常
     */
    public T getData() throws BizError {
        try {
            ListenableFuture<T> future = getFuture();
            if (future == null) {
                log.warn("future is null. data: {}", getResourceExtType().getName());
                return null;
            }
            return future.get(getTimeoutMills(), TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("externalData:{} error. timeout:{} error:{}", getTimeoutMills(), getResourceExtType().getName(), e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, getResourceExtType().name() + "外部资源请求失败", e);
        }
    }
}
