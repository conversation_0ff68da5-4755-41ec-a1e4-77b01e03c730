package com.xiaomi.nr.promotion.model;


import com.xiaomi.nr.phoenix.api.dto.response.tradeInfo.TradeQualificationInfo;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.domain.redpackage.model.RedPacket;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import com.xiaomi.nr.promotion.model.common.BasicInfo;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import lombok.Data;

import java.util.*;

/**
 * 上下文
 *
 * <AUTHOR>
 * @date 2021/3/16
 */
@Data
public class CheckoutContext {

    /**
     * 业务场景
     */
    private BizPlatformEnum bizPlatform;

    /**
     * 接口来zx源
     */
    private FromInterfaceEnum fromInterface;

    /**
     * 资源处理类接口集
     */
    private List<ResourceProvider<?>> resourceHandlers = new ArrayList<>();

    /**
     * 外部资源
     */
    private Map<ResourceExtType, ExternalDataProvider<?>> externalDataMap = new HashMap<>();

    // --------------------- 活动部分 --------------
    /**
     * 活动信息
     */
    private List<PromotionInfo> promotion = new ArrayList<>();

    /**
     * 包邮信息，包括产生包邮作用的ID,接口返回结果使用
     */
    private List<Express> express = new ArrayList<>();

    /**
     * 最终时间
     */
    private List<Long> finTime = new ArrayList<>();

    // --------------------- 券部分 --------------

    /**
     * 基本信息
     */
    private BasicInfo baseInfo;

    /**
     * 购物车信息
     */
    private List<CartItem> carts;

    /**
     * 券信息,兼容旧逻辑
     */
    @Deprecated
    private String couponBaseInfo = "";

    /**
     * 券信息
     */
    private String couponBaseInfoList = "";

    /**
     * 券错误信息
     */
    private String couponBaseInfoInvalid = "";


    /**
     * 三方优惠错误信息
     */
    private String phoenixBaseInfoInvalid = "";

    /**
     * 券名字
     */
    @Deprecated
    private String couponName;

    /**
     * 券类型
     */
    @Deprecated
    private Integer couponType;

    /**
     * 运费券名字
     */
    private String shipmentCouponName;

    /**
     * 运费券类型
     */
    private Integer shipmentCouponType;

    /**
     * 券限制使用区域-区域消费券
     */
    private String couponUseRegion;

    /**
     * 劵列表
     */
    private List<Coupon> couponList;

    /**
     * 劵不可用原因
     */
    private String couponInvalidReason = "";
    // --------------------- 红包部分 --------------
    /**
     * 红包可使用金额
     */
    private Long hasRedpacket = 0L;

    /**
     * 购物车使用的红包列表
     */
    private List<RedPacket> usedRedpacketList = new ArrayList<>();

    /**
     * 红包错误信息
     */
    private String redPacketBaseInfoInvalid = "";

    // --------------------- 积分部分 --------------
    /**
     * 用户有效积分总数量（账户内的有效积分）
     */
    private Long userValidPointCount;

    // --------------------- 礼品卡部分 --------------
    /**
     * 是否线上
     */
    private Integer isOnline;

    /**
     * 礼品卡基本信息字符串
     */
    private String eCardBaseInfo = "";

    /**
     * 可用礼品卡的商品的itemID, notes:这里空时候特殊处理,和pulse先保持一致
     */
    private String cardFullGoods = "null";

    /**
     * 礼品卡列表
     */
    private List<Ecard> eCardInfoList;

    /**
     * 礼品卡错误信息
     */
    private String ecardBaseInfoInvalid = "";
    /**
     * 北京以旧换新加购数量导致不可用优惠错误提示
     */
    private String purchaseSubsidyInvalid = "";

    /**
     * 是否有北京消费券
     */
    private boolean hasFulFillBeijingCoupon;


    // --------------------- 三方优惠部分 --------------

    /**
     * 购物车可用的三方优惠资格码列表 key:spuGroup value:QualificationInfo
     */
    private Map<String, TradeQualificationInfo> usedQualifyMap = new HashMap<>();

    /**
     * 购物车可用的三方优惠资格码列表
     */
    private Map<String, TradeQualificationInfo> cartItemQualifyMap = new HashMap<>();

    // --------------------- 下单部分 --------------
    /**
     * sku级别item
     */
    private List<OrderCartItem> orderCartItems = new ArrayList<>();
    /**
     * 订单sku item列表
     */
    private List<OrderItem> orderItems = new ArrayList<>();

    // --------------------- 其他 --------------
    /**
     * 是否是价保，true-是，false-否
     */
    private Boolean isProtectPrice = false;
    /**
     * 订单号
     */
    private Long orderId = 0L;
    /**
     * SSU信息结算
     */
    private Map<Long, Set<Long>> goodsActIdsMap = new HashMap<>();

    /**
     * 获取资源对象 列表
     *
     * @return 资源列表
     */
    public List<ResourceProvider<?>> getResourceHandlers() {
        return this.resourceHandlers;
    }

    /**
     * 商品是否强绑定
     */
    private Boolean isBind = false;

    /**
     * 参加活动类型
     */
    private List<Integer> joinActivityType;

    /**
     * 增加资源
     *
     * @param resourceHandlers 资源处理列表
     */
    public void appendResourceHandler(List<ResourceProvider<?>> resourceHandlers) {
        this.resourceHandlers.addAll(resourceHandlers);
    }

    /**
     * 添加包邮信息
     *
     * @param expressInfo 包邮信息
     */
    public void appendExpress(Express expressInfo) {
        express.add(expressInfo);
    }
}
