package com.xiaomi.nr.promotion.domain.activity.service.common.impl;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xiaomi.nr.coupon.api.dto.coupon.ProductUsableCouponResponse;
import com.xiaomi.nr.promotion.activity.PartOnsaleActivity;
import com.xiaomi.nr.promotion.activity.pool.*;
import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.nr.promotion.api.dto.enums.PromotionType;
import com.xiaomi.nr.promotion.api.dto.enums.SourceTypeEnum;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.constant.ApplicationConstant;
import com.xiaomi.nr.promotion.dao.redis.GoodsRedisDao;
import com.xiaomi.nr.promotion.domain.activity.service.common.ProductActivityService;
import com.xiaomi.nr.promotion.domain.coupon.facade.CarShopCouponFacade;
import com.xiaomi.nr.promotion.domain.coupon.facade.CouponFacade;
import com.xiaomi.nr.promotion.engine.*;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.enums.*;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.ProductDetailContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.ActPromExtend;
import com.xiaomi.nr.promotion.model.common.ActRespConverter;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.ExternalProviderFactory;
import com.xiaomi.nr.promotion.resource.external.GoodsStockExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.tool.OnsaleMutextTool;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.RequestParamValidator;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * 产品活动接口实现
 *
 * <AUTHOR>
 * @date 2021/6/15
 */
@Service
@Slf4j
public class ProductActivityServiceImpl implements ProductActivityService {
    @Autowired
    private ActivityPool activityPool;
    @Autowired
    private GoodsRedisDao goodsRedisDao;
    @Autowired
    private OnsaleMutextTool onsaleMutextTool;
    @Autowired
    private CarActivitySearcher carActivitySearcher;

    @Autowired
    private CarShopActivitySearcher carShopActivitySearcher;

    @Autowired(required = false)
    private PromotionInstancePool promotionInstancePool;

    @Resource
    private NacosConfig nacosConfig;

    @Autowired
    @Qualifier("commonAsyncTaskExecutor")
    private Executor commonAsyncTaskExecutor;

    @Autowired
    private ExternalProviderFactory externalProviderFactory;


    /**
     * 获取多商品活动信息
     *
     * @param request 包含商品列表和渠道等信息的请求对象
     * @return 包含活动索引和活动信息的响应对象
     * @throws BizError 业务异常
     */
    @Override
    public MultiProductGoodsActResponse getMultiProductGoodsAct(MultiProductGoodsActRequest request) throws BizError {
        // 1、参数校验
        checkMultiProductGoodsActRequest(request);

        ProductDetailContext context  = new ProductDetailContext();

        prepareExternalData(request, context);

        // 2、遍历商品列表
        Map<Long, List<PromotionIndexDto>> promotionIndexMap = new HashMap<>();
        Map<Long, PromotionInfoDTO> promotionInfoMap = new HashMap<>();
        for (MultiGoodItem goodItem : request.getGoodsList()) {
            // 3、活动检索
            List<ActSearchParam.GoodsInSearch> resultList = new ArrayList<>();
            ActSearchParam.GoodsInSearch search = new ActSearchParam.GoodsInSearch();
            search.setSsuId(goodItem.getSsuId());
            resultList.add(search);
            ActSearchParam param = new ActSearchParam()
                    .setChannel(request.getChannel())
                    .setGoodsList(resultList);
            List<ActivityTool> activityTools = chooseSearcherByChannel(request.getChannel()).searchCarActivity(param);
             for (ActivityTool tool : activityTools) {
                // 4、获取活动优惠信息
                PromotionInfoDTO promotionInfo = tool.getMultiProductAct(goodItem, request, context);
                if (promotionInfo == null) {
                    continue;
                }
                // 5、活动过滤
                if (!request.getPromotionTypeList().isEmpty() && !request.getPromotionTypeList().contains(promotionInfo.getPromotionType())) {
                    continue;
                }

                promotionInfoMap.put(promotionInfo.getId(), promotionInfo);
                // 6、构建索引
                List<PromotionIndexDto> promotionIndexList = promotionIndexMap.getOrDefault(goodItem.getSsuId(), new ArrayList<>());
                PromotionIndexDto promotionIndexDto = new PromotionIndexDto();
                promotionIndexDto.setPromotionId(promotionInfo.getId());
                promotionIndexList.add(promotionIndexDto);
                promotionIndexMap.put(goodItem.getSsuId(), promotionIndexList);
            }
        }

        // 7、返回结果
        MultiProductGoodsActResponse response = new MultiProductGoodsActResponse();
        response.setPromotionIndexMap(promotionIndexMap);
        response.setPromotionInfoMap(promotionInfoMap);
        response.setPromotionPriceDTOMap(context.getPromotionPriceMap());
        return response;
    }

    private AbstractActivitySearcher chooseSearcherByChannel(Integer channel) {
        if (Objects.equals(channel, com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum.CAR_VEHICLE.getValue())) {
            return carActivitySearcher;
        } else if (Objects.equals(channel, com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum.CAR_SHOP.getValue())) {
            return carShopActivitySearcher;
        } else {
            return null;
        }
    }

    /**
     * 进行外部资源的加载准备
     *
     * @param request       请求对象
     * @param context       上下文
     */
    private void prepareExternalData(MultiProductGoodsActRequest request, ProductDetailContext context) {
        BizPlatformEnum bizPlatformEnum = BizPlatformEnum.findByChannel(request.getChannel());
        List<ExternalDataProvider<?>> providers = externalProviderFactory.getProductDetailExternalProviders(bizPlatformEnum);
        Map<ResourceExtType, ExternalDataProvider<?>> map = new HashMap<>(providers.size());
        for (ExternalDataProvider<?> provider : providers) {
            provider.productDetailPrepare(request);
            map.put(provider.getResourceExtType(), provider);
        }
        context.setExternalDataMap(map);
    }

    private void checkMultiProductGoodsActRequest(MultiProductGoodsActRequest request) throws BizError {
        if (request.getChannel() != com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum.CAR_VEHICLE.getValue()
                && request.getChannel() != com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum.CAR_SHOP.getValue()) {
            log.error("param channel is error. request:{}", request);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "channel 只支持汽车和车商城渠道");
        }
        if (request.getGoodsList() == null) {
            log.error("param goodsList is error. request:{}", request);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "goodsList 不能为空");
        }
        if (request.getGoodsList().size() > 100) {
            log.error("param goodsList is error. request:{}", request);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "goodsList 最大数量为100");
        }
        for (MultiGoodItem item : request.getGoodsList()) {
            if (item.getSsuId() == null || item.getSsuId() < 0) {
                log.error("param item is error. request:{}", item);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "ssuId 非法");
            }
        }
        // 兼容第一期需求，导购不传入PromotionTypeList，只给返回选装基金，后续导购若有需求想要获取其它优惠类型，再修改导购代码传入导购不传入PromotionTypeList
        if (request.getPromotionTypeList() == null) {
            request.setPromotionTypeList(List.of(PromotionType.RANGE_REDUCE.getValue()));
        }
    }

    /**
     * 获取产品站活动接口：pos2.0
     *
     * @param request 请求参数
     * @return 响应参数
     * @throws BizError 业务异常
     */
    @Override
    public GetProductGoodsActResponse getProductGoodsAct(GetProductGoodsActRequest request) throws BizError {
        if (request.getSkuList() == null) {
            log.warn("param skuList is null. request:{}", request);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "skuList 参数不能为空");
        }
        // 获取可参加活动
        List<String> skuPackageList = request.getSkuList();
        Long clientId = request.getClientId();
        String orgCode = request.getOrgCode();
        List<ActivityTool> toolList = activityPool.getCurrent(orgCode, clientId, skuPackageList);
        // 如果有类型 和 渠道, 过滤类型和渠道
        Integer activityType = request.getActivityType();
        if (activityType != null) {
            toolList = toolList.stream().filter(tool -> Objects.equals(tool.getType().getTypeId(), activityType)).collect(Collectors.toList());
        }
        Integer channel = request.getChannel();
        if (channel != null) {
            toolList = toolList.stream().filter(tool -> filterChannel(tool, channel)).collect(Collectors.toList());
        }

        // subsity
        if (nacosConfig.isQueryOldSubsity() && Objects.nonNull(request.getQueryPurchaseSubsidy()) && request.getQueryPurchaseSubsidy()) {
            List<ActivityTool> subsityActivityTool = promotionInstancePool.activitySearcher(request.getChannel(),
                    request.getOrgCode(), request.getSkuList());
            toolList.addAll(subsityActivityTool);
        }

        // 合并SkuPackage活动数据
        Map<String, List<ProductActInfo>> skuPackageActMap = mergeProductActMap(clientId, orgCode, skuPackageList, toolList);
        // 处理直降互斥, 取价格最低
        skuPackageActMap.forEach((skuPackage, actInfoList) -> onsaleMutextTool.handleOnsaleMutex(actInfoList));
        GetProductGoodsActResponse response = new GetProductGoodsActResponse();
        response.setSkuActMap(skuPackageActMap);
        return response;
    }

    /**
     * 获取商品活动信息
     *
     * @param request 包含商品SKU包列表、渠道列表和活动类型列表的请求对象
     * @return 包含商品活动信息的响应对象
     * @throws BizError 当请求参数skuPackageList为空时抛出业务错误
     */
    @Override
    public GetProductGoodsActResponse getProductGoodsActV2(GetProductGoodsActRequestV2 request) throws BizError {
        if (request.getSkuPackageList() == null) {
            log.warn("param skuPackageList is null. request:{}", request);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "skuPackageList 参数不能为空");
        }
        // 获取可参加活动
        List<String> skuPackageList = request.getSkuPackageList();
        List<Integer> channel = request.getChannel();
        List<ActivityTool> toolList = activityPool.getCurrentV2(null, null, skuPackageList, channel);
        List<Integer> actTypeList = request.getActTypeList();
        if (CollectionUtils.isNotEmpty(actTypeList)) {
            toolList = toolList.stream().filter(tool -> actTypeList.contains(tool.getType().getTypeId())).collect(Collectors.toList());
        }
        // 合并SkuPackage活动数据
        Map<String, List<ProductActInfo>> skuPackageActMap = mergeProductActMap(null, null, skuPackageList, toolList);
        // 处理直降互斥, 取价格最低
        skuPackageActMap.forEach((skuPackage, actInfoList) -> onsaleMutextTool.handleOnsaleMutex(actInfoList));
        GetProductGoodsActResponse response = new GetProductGoodsActResponse();
        response.setSkuActMap(skuPackageActMap);
        return response;
    }

    /**
     * 获取商品可参加即将开始活动
     *
     * @param request 请求参数
     * @return 响应
     * @throws BizError 业务异常
     */
    @Override
    public GetPreProductGoodsActResponse getPreProductGoodsAct(GetPreProductGoodsActRequest request) throws BizError {
        if (request.getSkuPackageList() == null) {
            log.warn("param skuPackageList is null. request:{}", GsonUtil.toJson(request));
            throw ExceptionHelper.create(GeneralCodes.ParamError, "skuPackageList 参数不能为空");
        }
        // 获取可参加活动
        List<String> skuPackageList = request.getSkuPackageList();
        Long clientId = request.getClientId();
        String orgCode = request.getOrgCode();
        List<ActivityTool> toolList = activityPool.getPreview(orgCode, clientId, skuPackageList);

        // 如果有类型和渠道, 过滤类型和渠道
        Integer activityType = request.getActivityType();
        if (activityType != null) {
            toolList = toolList.stream().filter(tool -> Objects.equals(tool.getType().getTypeId(), activityType)).collect(Collectors.toList());
        }

        // 获取数据
        Map<String, List<ProductActInfo>> skuPackageActMap = mergeProductActMap(clientId, orgCode, skuPackageList, toolList);

        GetPreProductGoodsActResponse response = new GetPreProductGoodsActResponse();
        response.setSkuPackageActMap(skuPackageActMap);
        return response;
    }

    /**
     * 获取商品活动价
     * 现阶段只有直降
     *
     * @param request 请求参数
     * @return 价格Map
     */
    @Override
    public GetProductActPriceResponse getProductActPrice(GetProductActPriceRequest request) throws BizError {
        if (request.getSkuPackageList() == null) {
            log.warn("param skuPackageList is null. request:{}", GsonUtil.toJson(request));
            throw ExceptionHelper.create(GeneralCodes.ParamError, "skuPackageList 参数不能为空");
        }
        // 获取可参加活动
        List<String> skuPackageList = request.getSkuPackageList();
        String orgCode = request.getOrgCode();
        Long clientId = request.getClientId();
        List<ActivityTool> toolList = activityPool.getCurrent(orgCode, clientId, skuPackageList);

        // 按渠道过滤
        Integer channel = request.getChannel();
        if (channel != null) {
            toolList = toolList.stream().filter(tool -> filterChannel(tool, channel)).collect(Collectors.toList());
        }

        // 获取活动优惠价
        Map<String, Long> promotionPriceMap = Maps.newHashMap();
        for (ActivityTool activityTool : toolList) {
            if (!(activityTool instanceof PromotionPriceProvider)) {
                continue;
            }
            PromotionPriceProvider priceProvider = (PromotionPriceProvider) activityTool;
            Map<String, Long> actPriceMap = priceProvider.getPromotionPrice(skuPackageList, orgCode);
            actPriceMap.forEach((skuPackage, price) -> promotionPriceMap.merge(skuPackage, price, Math::min));
        }

        GetProductActPriceResponse response = new GetProductActPriceResponse();
        response.setActPriceMap(promotionPriceMap);
        return response;
    }

    /**
     * 获取商品活动价详情列表
     * 现阶段只有直降
     *
     * @param request 请求参数
     * @return 价格Map
     * @throws BizError 业务异常
     */
    @Override
    public GetProductActPriceDetailResponse getProductActPriceDetail(GetProductActPriceDetailRequest request) throws BizError {
        if (request.getSkuPackageList() == null) {
            log.warn("param skuPackageList is null. request:{}", GsonUtil.toJson(request));
            throw ExceptionHelper.create(GeneralCodes.ParamError, "skuPackageList 参数不能为空");
        }
        // 获取可参加活动
        List<String> skuPackageList = request.getSkuPackageList();
        List<ActivityTool> toolList = activityPool.getCurrent(null, null, skuPackageList);

        // 获取活动优惠价
        final Map<String, List<ProductActPriceDetail>> promotionPriceDetailMap = new HashMap<>();
        for (ActivityTool activityTool : toolList) {
            if (!(activityTool instanceof PromotionPriceProvider)) {
                continue;
            }
            PromotionPriceProvider priceProvider = (PromotionPriceProvider) activityTool;
            Map<String, Long> actPriceMap = priceProvider.getPromotionPrice(skuPackageList, null);
            handleActPriceMap(actPriceMap, promotionPriceDetailMap, activityTool);
        }

        // 包装响应
        GetProductActPriceDetailResponse response = new GetProductActPriceDetailResponse();
        response.setActPriceDetailMap(promotionPriceDetailMap);
        return response;
    }

    /**
     * 获取产品站活动
     *
     * @param request 请求参数
     * @return 活动列表信息
     * @throws BizError 业务异常
     */
    @Override
    public GetProductActResponse getProductAct(GetProductActRequest request) throws BizError {
        String sku = request.getSku();
        String commodityId = request.getCommodityId();
        if (StringUtils.isEmpty(commodityId) && StringUtils.isEmpty(sku)) {
            log.warn("param both commodityId and sku is null. commodityId:{} sku:{}", commodityId, request.getSku());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "CommodityID或Sku不能为空");
        }
        if (request.getClientId() <= 0L) {
            log.warn("param clientId <=0. clientId:{}", request.getClientId());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "clientId不能为空");
        }
        // 获取商品层级信息
        GoodsHierarchy hierarchy = getGoodsHierarchy(request.getSku(), commodityId);
        if (hierarchy == null) {
            log.warn("get goods hierarchy fail. sku:{}, commodityId:{}", request.getSku(), request.getCommodityId());
            throw ExceptionHelper.create(GeneralCodes.NotFound, "获取商品信息失败");
        }

        // 线上活动
        String skuPackage = StringUtils.isNotBlank(sku) ? sku : commodityId;
        List<String> skuPackageList = Collections.singletonList(skuPackage);
        List<ActivityTool> clientToolList = activityPool.getCurrent(null, request.getClientId(), skuPackageList);
        // 门店活动
        List<ActivityTool> orgToolList = Collections.emptyList();
        if (StringUtils.isNotBlank(request.getOrgCode())) {
            orgToolList = activityPool.getCurrent(request.getOrgCode(), request.getClientId(), skuPackageList);
        }

        List<PromotionInfo> shopPromotionList = handleToolList(clientToolList, request, hierarchy, Boolean.FALSE);
        List<PromotionInfo> storePromotionList = handleToolList(orgToolList, request, hierarchy, Boolean.TRUE);

        GetProductActResponse response = new GetProductActResponse();
        response.setPrice(new ProductPriceInfo());
        response.setPromotions(shopPromotionList);
        response.setStorePromotions(storePromotionList);
        return response;
    }

    /**
     * 获取门店商品活动价 (直降 | 门店价）
     * <p>
     * 注：全量获取门店下已经配置了活动价（直降｜门店价）的商品价
     *
     * @param request 请求参数
     * @return 响应
     * @throws BizError 业务异常
     */
    @Override
    public GetStoreActPriceResponse getStoreActPrice(GetStoreActPriceRequest request) throws BizError {
        if (request.getSkuPackageList() == null || StringUtils.isBlank(request.getOrgCode())) {
            log.warn("param skuPackageList is null. request:{}", GsonUtil.toJson(request));
            throw ExceptionHelper.create(GeneralCodes.ParamError, "skuPackageList和orgCode参数不能为空");
        }

        // 获取可参加活动
        String orgCode = request.getOrgCode();
        List<String> skuPackageList = request.getSkuPackageList();
        List<ActivityTool> toolList = activityPool.getCurrent(orgCode, null, skuPackageList);

        // 合并直降价（取最低） 和 门店价（互斥）
        Map<String, Long> onsalePriceMap = Maps.newHashMap();
        Map<String, Long> storePriceMap = Maps.newHashMap();
        for (ActivityTool activityTool : toolList) {
            mergeActPriceMap(skuPackageList, orgCode, activityTool, onsalePriceMap, storePriceMap);
        }

        Set<String> skuPackageSet = Sets.newHashSet(onsalePriceMap.keySet());
        skuPackageSet.addAll(storePriceMap.keySet());
        // 处理门店价 和 直降价 互斥， 门店价优先
        Map<String, StoreActPriceInfo> actPriceMap = skuPackageSet.stream()
                .collect(Collectors.toMap(skuPackage -> skuPackage, skuPackage -> transferActPriceInfo(skuPackage, onsalePriceMap, storePriceMap), (val1, val2) -> val2));

        GetStoreActPriceResponse response = new GetStoreActPriceResponse();
        response.setActPriceMap(actPriceMap);
        return response;
    }

    /**
     * 获取门店商品活动价 (直降 | 门店价）
     * <p>
     * 注：全量获取门店下已经配置了活动价（直降｜门店价）的商品价
     * </p>
     *
     * @param request 请求参数
     * @return 响应
     * @throws BizError 业务异常
     */
    @Override
    public GetStoreGoodsActPriceResponse getStoreGoodsActPrice(GetStoreGoodsActPriceRequest request) throws BizError {
        if (StringUtils.isBlank(request.getOrgCode())) {
            log.warn("param orgCode is null. request:{}", GsonUtil.toJson(request));
            throw ExceptionHelper.create(GeneralCodes.ParamError, "orgCode参数不能为空");
        }

        // 获取可参加活动
        String orgCode = request.getOrgCode();
        List<ActivityTool> toolList = activityPool.getCurrent(orgCode);

        // 合并直降价（取最低） 和 门店价（互斥）
        Map<String, Long> onsalePriceMap = Maps.newHashMap();
        Map<String, Long> storePriceMap = Maps.newHashMap();
        for (ActivityTool activityTool : toolList) {
            ActivityDetail detail = activityTool.getActivityDetail();
            if (detail == null || CollectionUtils.isEmpty(detail.getIncludeSkuPackages())) {
                continue;
            }
            List<String> skuPackageList = Lists.newArrayList(detail.getIncludeSkuPackages());
            mergeActPriceMap(skuPackageList, orgCode, activityTool, onsalePriceMap, storePriceMap);
        }

        Set<String> skuPackageSet = Sets.newHashSet(onsalePriceMap.keySet());
        skuPackageSet.addAll(storePriceMap.keySet());
        // 处理门店价 和 直降价 互斥， 门店价优先
        Map<String, StoreActPriceInfo> actPriceMap = skuPackageSet.stream()
                .collect(Collectors.toMap(skuPackage -> skuPackage, skuPackage -> transferActPriceInfo(skuPackage, onsalePriceMap, storePriceMap), (val1, val2) -> val2));

        GetStoreGoodsActPriceResponse response = new GetStoreGoodsActPriceResponse();
        response.setActPriceMap(actPriceMap);
        return response;
    }

    /**
     * 根据商品获取活动信息
     *
     * @param request 包含商品列表、活动类型和渠道的请求对象
     * @return 包含活动信息的响应对象
     * @throws BizError 如果请求参数无效则抛出业务异常
     */
    @Override
    public GetActivitysByGoodsResponse getActivitysByGoods(GetActivitysByGoodsRequest request) throws BizError {
        if (CollectionUtils.isEmpty(request.getSkuPackageList())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "商品列表不能为空");
        }
        if (ActivityTypeEnum.getByValue(request.getActivityType()) == null) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "活动类型不能为空");
        }
        if (request.getChannel() < 0) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "渠道不能为空");
        }
        // 获取可参加活动
        List<String> skuPackageList = request.getSkuPackageList();
        Long clientId = request.getClientId();
        List<ActivityTool> toolList = activityPool.getCurrent("", clientId, skuPackageList);

        // 如果有类型 和 渠道, 过滤类型和渠道
        Integer activityType = request.getActivityType();
        toolList = toolList.stream().filter(tool -> Objects.equals(tool.getType().getTypeId(), activityType)).collect(Collectors.toList());
        Integer channel = request.getChannel();
        toolList = toolList.stream().filter(tool -> filterChannel(tool, channel)).collect(Collectors.toList());


        //转化数据
        Map<String, List<GoodsActivityInfo>> activitys = mergeGoodsValidActMap(clientId, null, skuPackageList, toolList);

        GetActivitysByGoodsResponse response = new GetActivitysByGoodsResponse();
        response.setActivitys(activitys);
        return response;
    }

    /**
     * 合并Tool列表中支持的ProductActInfoList
     *
     * @param clientId       应用ID
     * @param orgCode        门店Code
     * @param skuPackageList SKU 或 Package
     * @param toolList       工具列表
     * @return 接口异常
     * @throws BizError 业务异常
     */
    private Map<String, List<GoodsActivityInfo>> mergeGoodsValidActMap(Long clientId, String orgCode, List<String> skuPackageList, List<ActivityTool> toolList) throws BizError {
        // 初始化Map
        Map<String, List<GoodsActivityInfo>> skuPackageActMap = skuPackageList.stream()
                .collect(Collectors.toMap(skuPackage -> skuPackage, skuPackage -> Lists.newArrayList(), (list1, list2) -> list1));
        // 从tool 中获取活动优惠详情
        for (ActivityTool tool : toolList) {
            if (!(tool instanceof GoodsValidActProvider)) {
                continue;
            }
            Map<String, GoodsActivityInfo> skuActMap = ((GoodsValidActProvider) tool).getGoodsValidAct(clientId, orgCode, skuPackageList);
            if (skuActMap == null) {
                continue;
            }
            skuActMap.forEach((skuPackage, actInfo) -> {
                List<GoodsActivityInfo> tools = skuPackageActMap.getOrDefault(skuPackage, Lists.newArrayList());
                tools.add(actInfo);
                skuPackageActMap.put(skuPackage, tools);
            });
        }
        return skuPackageActMap;
    }

    /**
     * 获取活动售价
     *
     * @param request 请求对象
     * @return 活动价格数据
     */
    @Override
    public GetGoodsActPriceResponse getGoodsActPrice(GetGoodsActPriceRequest request) throws BizError {
        if (CollectionUtils.isEmpty(request.getSkuPackageList())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "商品列表不能为空");
        }
        List<String> skuPackageList = request.getSkuPackageList();
        if (skuPackageList.size() > ApplicationConstant.REQUEST_BATCH_LIMIT) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "批量请求请不要超过" + ApplicationConstant.REQUEST_BATCH_LIMIT);
        }
        List<ActivityTool> toolList = activityPool.getCurrent(null, null, skuPackageList);
        Map<String, List<GoodsPriceInfo>> actPriceMap = Maps.newHashMap();
        for (ActivityTool activityTool : toolList) {
            if (!(activityTool instanceof GoodsActPriceProvider)) {
                continue;
            }
            GoodsActPriceProvider priceProvider = (GoodsActPriceProvider) activityTool;
            Map<String, List<GoodsPriceInfo>> priceMap = priceProvider.getGoodsActPrice(skuPackageList);
            priceMap.forEach((skuPackage, goodPriceInfoList) -> goodPriceInfoList.forEach(priceInfo -> {
                handleValidActPrice(actPriceMap, skuPackage, priceInfo);
            }));
        }

        GetGoodsActPriceResponse response = new GetGoodsActPriceResponse();
        response.setActPriceMap(actPriceMap);
        return response;
    }

    /**
     * 合并处理价格
     *
     * @param actPriceMap 汇总价格Map
     * @param priceInfo   价格Map
     */
    private void handleValidActPrice(Map<String, List<GoodsPriceInfo>> actPriceMap, String skuPackage, GoodsPriceInfo priceInfo) {
        List<GoodsPriceInfo> priceInfoList = actPriceMap.getOrDefault(skuPackage, Lists.newArrayList());
        actPriceMap.put(skuPackage, priceInfoList);
        // 为空则加入当前
        if (CollectionUtils.isEmpty(priceInfoList)) {
            priceInfoList.add(priceInfo);
            return;
        }
        // 处理非部门门店的
        if (!ChannelScopeEnum.PART_STORE.getValue().equals(priceInfo.getChannelScope())) {
            GoodsPriceInfo goodsPriceInfo = priceInfoList.stream()
                    .filter(info -> Objects.equals(info.getChannelScope(), priceInfo.getChannelScope()))
                    .findAny().orElse(null);
            // 没有发现就直接加入
            if (goodsPriceInfo == null) {
                priceInfoList.add(priceInfo);
                return;
            }
            // 渠道价格更低，选择更低的
            if (priceInfo.getActPrice() < goodsPriceInfo.getActPrice()) {
                priceInfoList.remove(goodsPriceInfo);
                priceInfoList.add(priceInfo);
            }
            return;
        }
        // 来源门店价
        if (Objects.equals(ActivityTypeEnum.STORE_PRICE.getValue(), priceInfo.getActType())) {
            priceInfoList.add(priceInfo);
            return;
        }
        // 部分门店
        priceInfoList.add(priceInfo);
    }


    /**
     * 加购时校验商品参加活动的有效性
     *
     * @param request 请求参数
     * @return 响应
     * @throws BizError 业务异常
     */
    @Override
    public CheckProductsActResponse checkProductsAct(CheckProductsActRequest request) throws BizError {
        // 入参校验
        RequestParamValidator.valid(request);
        // 获取可参加活动工具
        String orgCode = request.getOrgCode();
        ActivityTool tool = activityPool.getCurrentByActId(orgCode, request.getPromotionId());
        if (tool == null) {
            throw ExceptionHelper.create(ErrCode.ERR_INVALID_ACT_TIME, "活动未在有效期内");
        }
        Map<String, GoodsHierarchy> goodsHierarchyMap = getGoodsHierarchyMap(request.getMainGoodsList());
        CheckProductsActResponse response = new CheckProductsActResponse();
        // 具体校验逻辑
        List<CheckGoodsItem> invalidGoods = tool.checkProductsAct(request, goodsHierarchyMap);
        if (CollectionUtils.isNotEmpty(invalidGoods)) {
            response.setInvalidGoodsList(invalidGoods);
        }

        return response;
    }

    /**
     * 获取商品活动价V2
     * 现阶段只有直降
     *
     * @param request 请求参数
     * @return 价格Map
     * @throws BizError 业务异常
     */
    @Override
    public GetProductActPriceV2Response getProductActPrice(GetProductActPriceV2Request request) throws BizError {
        if (request.getSkuPackageList() == null) {
            log.warn("param skuPackageList is empty.");
            throw ExceptionHelper.create(GeneralCodes.ParamError, "skuPackageList不能为空");
        }
        if (request.getSkuPackageList().size() > ApplicationConstant.REQUEST_BATCH_LIMIT) {
            log.warn("param skuPackageList length is over 100.");
            throw ExceptionHelper.create(GeneralCodes.ParamError, "批量请求请不要超过" + ApplicationConstant.REQUEST_BATCH_LIMIT);
        }

        // 获取可参加活动
        String orgCode = request.getOrgCode();
        Long clientId = request.getClientId();
        List<GoodsDto> goodsList = request.getSkuPackageList();
        List<String> skuPackageList = goodsList.stream().map(GoodsDto::getId).map(String::valueOf).collect(Collectors.toList());
        List<ActivityTool> toolList = activityPool.getCurrentV2(orgCode, clientId, skuPackageList, Collections.singletonList(request.getChannel()));

        // 定义比较器
        BiFunction<GoodsPriceDto, GoodsPriceDto, GoodsPriceDto> priceRemapping = (priceData, otherPriceData) -> {
            if (otherPriceData.getLowerPrice() < priceData.getLowerPrice()) {
                return otherPriceData;
            }
            return priceData;
        };
        // 获取活动优惠价
        Map<Long, GoodsPriceDto> goodsPriceMap = Maps.newHashMap();
        for (ActivityTool activityTool : toolList) {
            if (!(activityTool instanceof PromotionPriceV2Provider)) {
                continue;
            }
            PromotionPriceV2Provider priceProvider = (PromotionPriceV2Provider) activityTool;
            Map<Long, GoodsPriceDto> actPriceMap = priceProvider.getPromotionPrice(goodsList);
            actPriceMap.forEach((skuPackage, price) -> goodsPriceMap.merge(skuPackage, price, priceRemapping));
        }
        GetProductActPriceV2Response response = new GetProductActPriceV2Response();
        response.setGoodsPriceMap(goodsPriceMap);
        return response;
    }

    /**
     * 批量获取活动详情
     *
     * @param request 请求对象
     * @return 活动详情
     * @throws BizError 业务异常
     */
    @Override
    public BatchGetActivityDetailResponse batchGetActivityDetail(BatchGetActivityDetailRequest request) throws BizError {
        if (CollectionUtils.isEmpty(request.getActivityIds())) {
            log.warn("param activityIds is empty. request:{}", request);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "参数activityIds不能为空");
        }
        if (request.getActivityIds().size() > ApplicationConstant.REQUEST_BATCH_ACT_LIMIT) {
            log.warn("param activityIds length is over {}. request:{}", ApplicationConstant.REQUEST_BATCH_ACT_LIMIT, request);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "参数activityIds数量不能大于20");
        }
        Map<Long, ActivityDetailDto> detailDtoMap = Maps.newConcurrentMap();
        // 批量获取可参加活动
        CompletableFuture<?>[] futureList = request.getActivityIds().stream()
                .map(actId -> CompletableFuture.runAsync(() -> {
                    try {
                        // 获取可参加活动
                        ActivityTool tool = activityPool.getPromotionById(actId);
                        if (tool == null) {
                            log.warn("activity id not found. activityId:{}", actId);
                            throw ExceptionHelper.create(GeneralCodes.NotFound, "活动不存在或已结束");
                        }
                        ActivityDetail activityDetail = tool.getActivityDetail();
                        if (activityDetail == null) {
                            log.warn("activity detail is null. activityId:{}", actId);
                            throw ExceptionHelper.create(GeneralCodes.NotFound, "活动详情不存在");
                        }
                        ActivityDetailDto detailDto = getActivityDetailDto(tool, activityDetail);
                        detailDtoMap.put(actId, detailDto);
                    } catch (BizError e) {
                        log.warn("get activity error. actId:{}", actId, e);
                    }
                }, commonAsyncTaskExecutor))
                .toArray(CompletableFuture[]::new);
        CompletableFuture.allOf(futureList).join();

        BatchGetActivityDetailResponse response = new BatchGetActivityDetailResponse();
        response.setActivityMap(detailDtoMap);
        return response;
    }

    /**
     * 获取活动详情
     *
     * @param request 请求参数
     * @return 活动详情
     * @throws BizError 业务异常
     */
    @Override
    public GetActivityDetailResponse getActivityDetail(GetActivityDetailRequest request) throws BizError {
        if (request.getActivityId() == null) {
            log.warn("param activityId is null. request:{}", request);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "activityId不能为空");
        }
        if (request.getChannel() == null || com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum.getByValue(request.getChannel()) == null) {
            request.setChannel(com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum.MISHOP.getValue());
        }
        Long activityId = request.getActivityId();
        String orgCode = request.getOrgCode();
        // 获取可参加活动
        ActivityTool tool;
        if (com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum.MISHOP.getValue() == request.getChannel()) {
            tool = activityPool.getPromotionById(activityId);
        } else {
            AbstractActivitySearcher activitySearcher = chooseSearcherByChannel(request.getChannel());
            if (activitySearcher == null) {
                log.warn("param channel is invalid. request:{}", request);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "非法的渠道");
            }
            tool = activitySearcher.getPromotionById(activityId);
        }
        if (tool == null) {
            log.warn("activity id not found. activityId:{}", activityId);
            throw ExceptionHelper.create(GeneralCodes.NotFound, "活动不存在或已结束");
        }
        ActivityDetail activityDetail = tool.getActivityDetail();
        if (activityDetail == null) {
            log.warn("activity detail is null. activityId:{}", activityId);
            throw ExceptionHelper.create(GeneralCodes.NotFound, "活动详情不存在");
        }
        if (tool instanceof PartOnsaleActivity && StringUtils.isEmpty(orgCode)) {
            log.warn("param orgCode is null, activityId:{}", activityId);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "orgCode不能为空");
        }
        //指定门店，且传入门店不在活动可用门店范围内，则返回null
        if (tool instanceof PartOnsaleActivity) {
            if (CollectionUtils.isEmpty(activityDetail.getSelectOrgCodes())
                    || !activityDetail.getSelectOrgCodes().contains(orgCode)) {
                return null;
            }
        }
        ActivityDetailDto detailDto = getActivityDetailDto(tool, activityDetail);
        if (detailDto == null) {
            log.warn("activity not found. request:{}", request);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "活动信息不存在");
        }
        GetActivityDetailResponse response = new GetActivityDetailResponse();
        response.setId(detailDto.getId());
        response.setType(detailDto.getType());
        response.setPolicys(detailDto.getPolicys());
        response.setUnixStartTime(detailDto.getUnixStartTime());
        response.setUnixEndTime(detailDto.getUnixEndTime());
        response.setGoodsItemList(detailDto.getGoodsItemList());
        response.setOnsaleGoods(detailDto.getOnsaleGoods());
        response.setDescRule(detailDto.getDescRule());
        response.setDescRuleIndex(detailDto.getDescRuleIndex());
        response.setGiftGoods(detailDto.getGiftGoods());
        response.setGiftGoodsV2(detailDto.getGiftGoodsV2());
        response.setBargainGoods(detailDto.getBargainGoods());
        return response;
    }

    /**
     * 获取活动详情数据传输对象
     *
     * @param tool           活动工具对象
     * @param activityDetail 活动详情对象
     * @return 活动详情数据传输对象
     * @throws BizError 业务异常
     */
    private ActivityDetailDto getActivityDetailDto(ActivityTool tool, ActivityDetail activityDetail) throws BizError {
        // 包装可用商品
        List<GoodsItem> itemList = Optional.ofNullable(activityDetail.getIncludeSkuPackages()).orElse(Collections.emptySet()).stream()
                .map(this::transferGoodsItem).collect(Collectors.toList());
        List<ProductActOnsaleGoods> onsaleGoods = convertOnsaleGoods(activityDetail.getPriceInfoMap());

        ActivityDetailDto detailDto = new ActivityDetailDto();
        detailDto.setId(tool.getId());
        detailDto.setType(tool.getType().getTypeId());
        detailDto.setName(activityDetail.getName());
        detailDto.setPolicys(activityDetail.getPolicys());
        detailDto.setUnixStartTime(tool.getUnixStartTime());
        detailDto.setUnixEndTime(tool.getUnixEndTime());
        detailDto.setGoodsItemList(itemList);
        detailDto.setOnsaleGoods(onsaleGoods);
        detailDto.setDescRule(activityDetail.getDescRule());
        detailDto.setDescRuleIndex(activityDetail.getDescRuleIndex());
        detailDto.setReduceGoods(activityDetail.getReduceGoods());

        ProductActGoods actGoods = null;
        List<ProductActGoods> actGoodsV2s = null;
        if (tool instanceof ProductActGoodsProvider) {
            actGoods = ((ProductActGoodsProvider) tool).getAdditionalGoods();
        }
        if (tool instanceof GiftActGoodsProvider) {
            actGoodsV2s = ((GiftActGoodsProvider) tool).getAdditionalGoodsV2();
        }
        if (tool.getType() == PromotionToolType.GIFT || tool.getType() == PromotionToolType.BUY_GIFT) {
            detailDto.setGiftGoods(actGoods);
            detailDto.setGiftGoodsV2(actGoodsV2s);
        }
        if (tool.getType() == PromotionToolType.BARGAIN) {
            detailDto.setBargainGoods(actGoods);
        }
        return detailDto;
    }

    /**
     * 获取活动详情  根据是否传入区域信息，获取库存信息
     *
     * @param request 请求参数
     * @return 活动详情
     * @throws BizError 业务异常
     */
    @Override
    public GetActivityAreaDetailResponse getActivityAreaDetail(GetActivityAreaDetailRequest request) throws BizError {

        // 获取可参加活动
        ActivityTool tool = activityPool.getPromotionById(request.getActivityId());
        if (tool == null) {
            log.warn("activity id not found. activityId:{}", request.getActivityId());
            throw ExceptionHelper.create(GeneralCodes.NotFound, "活动不存在或已结束");
        }
        ActivityDetail activityDetail = tool.getActivityDetail();
        if (activityDetail == null) {
            log.warn("activity detail is null. activityId:{}", request.getActivityId());
            throw ExceptionHelper.create(GeneralCodes.NotFound, "活动详情不存在");
        }
        // 包装可用商品
        List<GoodsItem> itemList = Optional.ofNullable(activityDetail.getIncludeSkuPackages()).orElse(Collections.emptySet())
                .stream().map(this::transferGoodsItem).collect(Collectors.toList());

        ProductActGoods actGoods = null;
        List<ProductActGoods> actGoodsV2s = null;
        if (tool instanceof ProductActGoodsProvider) {
            actGoods = ((ProductActGoodsProvider) tool).getAdditionalGoods();
        }
        if (tool instanceof GiftActGoodsProvider) {
            actGoodsV2s = ((GiftActGoodsProvider) tool).getAdditionalGoodsV2();
            ((GiftActGoodsProvider) tool).setGiftStockByArea(actGoodsV2s, request.getAreaId(), request.getCityId(), request.getProvinceId(), request.getDistrictId());

        }
        List<ProductActOnsaleGoods> onsaleGoods = convertOnsaleGoods(activityDetail.getPriceInfoMap());

        GetActivityAreaDetailResponse response = new GetActivityAreaDetailResponse();
        response.setId(tool.getId());
        response.setType(tool.getType().getTypeId());
        response.setPolicys(activityDetail.getPolicys());
        response.setUnixStartTime(tool.getUnixStartTime());
        response.setUnixEndTime(tool.getUnixEndTime());
        response.setGoodsItemList(itemList);
        response.setOnsaleGoods(onsaleGoods);
        response.setDescRule(activityDetail.getDescRule());
        response.setDescRuleIndex(activityDetail.getDescRuleIndex());
        if (tool.getType() == PromotionToolType.GIFT || tool.getType() == PromotionToolType.BUY_GIFT) {
            response.setGiftGoods(actGoods);
            response.setGiftGoodsV2(actGoodsV2s);
        }
        if (tool.getType() == PromotionToolType.BUY_GIFT && actGoods != null) {
            response.setGiftGoodsV2(Collections.singletonList(actGoods));
        }
        if (tool.getType() == PromotionToolType.BARGAIN) {
            response.setBargainGoods(actGoods);
        }
        return response;
    }


    /**
     * 将价格信息映射转换为促销商品列表
     *
     * @param priceInfoMap 包含价格信息的映射
     * @return 促销商品列表，如果输入映射为空则返回null
     */
    private List<ProductActOnsaleGoods> convertOnsaleGoods(Map<String, ActPriceInfo> priceInfoMap) {
        if (MapUtils.isEmpty(priceInfoMap)) {
            return null;
        }
        return Optional.of(priceInfoMap.values()).orElse(Collections.emptyList()).stream()
                .map(priceInfo -> {
                    ProductActOnsaleGoods onsaleGoods = new ProductActOnsaleGoods();
                    onsaleGoods.setLevel(priceInfo.getLevel());
                    onsaleGoods.setSkuPackage(priceInfo.getSkuPackage());
                    onsaleGoods.setLowerPrice(priceInfo.getPrice());
                    onsaleGoods.setLimitRule(ActRespConverter.convert(priceInfo.getLimitRule()));
                    return onsaleGoods;
                }).collect(Collectors.toList());
    }

    /**
     * 过滤渠道，检查是否为支持的渠道
     *
     * @param tool    活动工具
     * @param channel 渠道
     * @return 是否能参与 TRUE/FALSE
     */
    private boolean filterChannel(ActivityTool tool, Integer channel) {
        ActivityDetail activityDetail = tool.getActivityDetail();
        if (activityDetail == null) {
            return false;
        }

        OnOffLineEnum offline = activityDetail.getOffline();
        OrgScopeEnum orgScope = activityDetail.getOrgScope();
        if (Objects.equals(ChannelEnum.MI_SHOP.getId(), channel) && offline != OnOffLineEnum.ONLINE && offline != OnOffLineEnum.ONOFFLINE) {
            return false;
        }
        if (Objects.equals(ChannelEnum.DIRECT.getId(), channel)
                && ((offline != OnOffLineEnum.OFFLINE && offline != OnOffLineEnum.ONOFFLINE) || (orgScope != OrgScopeEnum.ORG_ALL_STORE && orgScope != OrgScopeEnum.ORG_ALL_DIRECT_STORE))) {
            return false;
        }
        if (Objects.equals(ChannelEnum.SPECIALTY.getId(), channel)
                && ((offline != OnOffLineEnum.OFFLINE && offline != OnOffLineEnum.ONOFFLINE) || (orgScope != OrgScopeEnum.ORG_ALL_STORE && orgScope != OrgScopeEnum.ORG_ALL_SPECIALTY_STORE))) {
            return false;
        }
        if (Objects.equals(ChannelEnum.AUTHORIZED.getId(), channel)
                && ((offline != OnOffLineEnum.OFFLINE && offline != OnOffLineEnum.ONOFFLINE) || orgScope != OrgScopeEnum.ORG_ALL_AUTHORIZED_STORE)) {
            return false;
        }
        return true;
    }

    /**
     * 合并Tool列表中支持的ProductActInfoList
     *
     * @param clientId       应用ID
     * @param orgCode        门店Code
     * @param skuPackageList SKU 或 Package
     * @param toolList       工具列表
     * @return 接口异常
     * @throws BizError 业务异常
     */
    private Map<String, List<ProductActInfo>> mergeProductActMap(Long clientId, String orgCode, List<String> skuPackageList, List<ActivityTool> toolList) throws BizError {
        // 初始化Map
        Map<String, List<ProductActInfo>> skuPackageActMap = skuPackageList.stream()
                .collect(Collectors.toMap(skuPackage -> skuPackage, skuPackage -> Lists.newArrayList(), (list1, list2) -> list1));
        // 从tool 中获取活动优惠详情
        for (ActivityTool tool : toolList) {
            if (!(tool instanceof ProductGoodsActProvider)) {
                continue;
            }
            Map<String, ProductActInfo> skuActMap = ((ProductGoodsActProvider) tool).getProductGoodsAct(clientId, orgCode, skuPackageList);
            if (skuActMap == null) {
                continue;
            }
            skuActMap.forEach((skuPackage, actInfo) -> {
                List<ProductActInfo> tools = skuPackageActMap.getOrDefault(skuPackage, Lists.newArrayList());
                tools.add(actInfo);
                skuPackageActMap.put(skuPackage, tools);
            });
        }
        // 排序 1：按类型 2：开始时间
        skuPackageActMap.forEach((skuPackage, actInfoList) ->
                actInfoList.sort(Comparator.comparing(ProductActInfo::getType).thenComparing(ProductActInfo::getUnixStartTime)));
        return skuPackageActMap;
    }

    /**
     * 处理活动优惠PromotionInfo
     *
     * @param toolList  活动工具列表
     * @param request   请求对象
     * @param hierarchy 商品层级
     * @param orgTool   是否只检查门店
     * @return Promotion列表
     * @throws BizError 业务异常
     */
    private List<PromotionInfo> handleToolList(List<ActivityTool> toolList, GetProductActRequest request,
                                               GoodsHierarchy hierarchy, boolean orgTool) throws BizError {
        if (CollectionUtils.isEmpty(toolList)) {
            return Collections.emptyList();
        }
        // 获取活动优惠
        List<PromotionInfo> promotionList = Lists.newArrayList();
        for (ActivityTool tool : toolList) {
            PromotionInfo promotionInfo = tool.getProductAct(request, hierarchy, orgTool);
            if (promotionInfo == null) {
                continue;
            }
            promotionList.add(promotionInfo);
        }
        // 处理直降
        onsaleMutextTool.handlePromtionOnsaleMutex(promotionList);
        return promotionList;
    }

    /**
     * 获取商品信息
     *
     * @param sku         SKU
     * @param commodityId PackageId
     * @return 商品层级
     */
    private GoodsHierarchy getGoodsHierarchy(String sku, String commodityId) {
        if (StringUtils.isBlank(sku)) {
            return goodsRedisDao.getHierarchyByCommodityId(commodityId);
        } else {
            return goodsRedisDao.getHierarchyBySku(sku);
        }
    }

    /**
     * 获取商品信息map
     *
     * @param checkGoodsItems 商品列表
     * @return 商品信息map
     * @throws BizError 业务异常
     */
    private Map<String, GoodsHierarchy> getGoodsHierarchyMap(List<CheckGoodsItem> checkGoodsItems) throws BizError {
        Map<String, GoodsHierarchy> hierarchyMap = new HashMap<>(checkGoodsItems.size());
        for (CheckGoodsItem goodsItem : checkGoodsItems) {
            Long sku = goodsItem.getSku();
            Long packageId = goodsItem.getPackageId();
            GoodsHierarchy goodsHierarchy = getGoodsHierarchy(String.valueOf(sku), String.valueOf(packageId));
            if (goodsHierarchy == null) {
                log.warn("getHierarchyBySkuPkg fail. skuPkg:{}", goodsItem);
                throw ExceptionHelper.create(ErrCode.ERR_UNKNOWN_GOODS, "skuPkg: " + goodsItem);
            }
            if (sku == null || sku == 0L) {
                hierarchyMap.put(String.valueOf(packageId), goodsHierarchy);
            } else {
                hierarchyMap.put(String.valueOf(sku), goodsHierarchy);
            }
        }
        return hierarchyMap;
    }

    /**
     * 处理活动价详情Map
     *
     * @param actPriceMap    活动价
     * @param priceDetailMap 活动价列表详情
     * @param activityTool   活动Tool
     */
    private void handleActPriceMap(Map<String, Long> actPriceMap, Map<String, List<ProductActPriceDetail>> priceDetailMap, ActivityTool activityTool) {
        if (MapUtils.isEmpty(actPriceMap)) {
            return;
        }
        // 迭代添加
        for (Map.Entry<String, Long> actPriceEntry : actPriceMap.entrySet()) {
            String skuPackage = actPriceEntry.getKey();
            Long price = actPriceEntry.getValue();
            List<ProductActPriceDetail> detailList = priceDetailMap.getOrDefault(skuPackage, new ArrayList<>());

            ActivityDetail activityDetail = activityTool.getActivityDetail();
            if (activityDetail == null) {
                continue;
            }
            OnOffLineEnum offLineEnum = activityDetail.getOffline();
            OrgScopeEnum orgScopeEnum = activityDetail.getOrgScope();
            Integer orgScope = orgScopeEnum != null ? orgScopeEnum.getOrgScope() : null;

            ProductActPriceDetail detail = new ProductActPriceDetail();
            detail.setActId(activityTool.getId());
            detail.setPrice(price);
            detail.setOffline(offLineEnum.getValue());
            detail.setChannels(activityDetail.getChannels());

            detail.setOrgScope(orgScope);
            detail.setSelectOrgCodes(activityDetail.getSelectOrgCodes());
            detail.setSelectClients(activityDetail.getSelectClients());
            detailList.add(detail);
            priceDetailMap.put(skuPackage, detailList);
        }
    }

    private GoodsItem transferGoodsItem(String skuPackage) {
        GoodsItem item = new GoodsItem();
        item.setSkuPackage(skuPackage);
        item.setLevel(GoodsLevelEnum.getLevel(skuPackage).getLevel());
        return item;
    }

    /**
     * 合并价格信息
     *
     * @param skuPackageList sku or pacakgeId 列表
     * @param orgCode        门店Code
     * @param tool           促销工具
     * @param onsalePriceMap 直降价格Map
     * @param storePriceMap  门店价Map
     * @throws BizError 业务异常
     */
    private void mergeActPriceMap(List<String> skuPackageList, String orgCode, ActivityTool tool, Map<String, Long> onsalePriceMap, Map<String, Long> storePriceMap) throws BizError {
        if (tool instanceof PromotionPriceProvider) {
            PromotionPriceProvider priceProvider = (PromotionPriceProvider) tool;
            Map<String, Long> actPriceMap = priceProvider.getPromotionPrice(skuPackageList, orgCode);
            actPriceMap.forEach((skuPackage, price) -> onsalePriceMap.merge(skuPackage, price, Math::min));
        }
        if (tool instanceof StoreActPriceProvider) {
            StoreActPriceProvider priceProvider = (StoreActPriceProvider) tool;
            Map<String, Long> actPriceMap = priceProvider.getStoreActPrice(skuPackageList, orgCode);
            actPriceMap.forEach((skuPackage, price) -> storePriceMap.merge(skuPackage, price, Math::min));
        }
    }

    /**
     * 转化价格详情
     *
     * @param skuPackage     sku Or package
     * @param onsalePriceMap 直降详情列表
     * @param storePriceMap  门店价格详情
     * @return 包装详情
     */
    private StoreActPriceInfo transferActPriceInfo(String skuPackage, Map<String, Long> onsalePriceMap, Map<String, Long> storePriceMap) {
        Long lowerPrice = onsalePriceMap.get(skuPackage);
        Long storePrice = storePriceMap.get(skuPackage);

        Long actPrice = lowerPrice;
        SourceTypeEnum sourceType = SourceTypeEnum.ONSALE;
        if (storePrice != null) {
            actPrice = storePriceMap.get(skuPackage);
            sourceType = SourceTypeEnum.STORE_PRICE;
        }
        StoreActPriceInfo info = new StoreActPriceInfo();
        info.setActPrice(actPrice);
        info.setSourceType(sourceType);
        return info;
    }

    /**
     * 获取商城产品站活动（新版，支持批量商品，getProductAct 的升级版）
     *
     * @param request 请求参数
     * @return 活动列表信息
     * @throws BizError 业务异常
     */
    @Override
    public GetProductActV2Response getProductActV2(GetProductActV2Request request) throws BizError {
        checkGoodsValidActParams(request);
        request.setGoodsList(distinctGoods(request.getGoodsList()));

        //可参与的活动
        Map<String, ProductActItemDto> allProductActs = new HashMap<>();
        Map<String, PromotionInfo> allPromotions = new HashMap<>();
        Map<ProductActGoodsItem, List<String>> goodsActIdMap = new HashMap<>();
        for (ProductActGoodsItem g : request.getGoodsList()) {
            try {
                if (!isValidGoods(g)) {
                    continue;
                }
                GetProductActRequest req = makeGetProductActRequest(request, g);
                GetProductActResponse resp = getProductActV2ByOneGoods(req);
                List<PromotionInfo> shopPromotions = resp.getPromotions();
                List<PromotionInfo> storePromotions = resp.getStorePromotions();

                List<String> goodsActIds = new ArrayList<>();
                fillProductData(shopPromotions, allProductActs, allPromotions, goodsActIds);
                fillProductData(storePromotions, allProductActs, allPromotions, goodsActIds);
                goodsActIdMap.put(g, goodsActIds);
            } catch (Exception e) {
                log.warn("getProductActV2, get one goods fail, id:{} level:{}, errMsg:{}, err:", g.getId(), g.getLevel(), e.getMessage(), e);
            }
        }

        List<PromotionInfo> allPromotionList = new ArrayList<>(allPromotions.values());

        //排序
        List<PromotionInfo> promotionsSorted = promotionsSort(allPromotionList);

        //构建结果
        Map<ProductRespActGoodsItemDto, List<ProductValidActItemDto>> validPromotions = new HashMap<>();
        for (Map.Entry<ProductActGoodsItem, List<String>> item : goodsActIdMap.entrySet()) {
            ProductRespActGoodsItemDto g = convertGoodsItemToProductDto(item.getKey());
            List<String> actIds = item.getValue();
            for (PromotionInfo act : promotionsSorted) {
                if (actIds.contains(act.getPromotionId())) {
                    if (!validPromotions.containsKey(g)) {
                        validPromotions.put(g, Lists.newArrayList());
                    }
                    ProductValidActItemDto actDto = new ProductValidActItemDto();
                    actDto.setPromotionId(act.getPromotionId());
                    validPromotions.get(g).add(actDto);
                }
            }
        }

        //返回
        GetProductActV2Response response = new GetProductActV2Response();
        response.setValidPromotions(Collections.emptyMap());
        response.setPromotions(Collections.emptyMap());
        response.setValidPromotions(validPromotions);
        response.setPromotions(allProductActs);
        return response;
    }

    /**
     * 商品可参与活动参数校验
     *
     * @param request GetProductActV2Request
     * @throws BizError .
     */
    private void checkGoodsValidActParams(GetProductActV2Request request) throws BizError {
        if (Objects.isNull(request.getClientId()) || request.getClientId() <= 0L) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "clientId不能为空");
        }
        if (Strings.isNullOrEmpty(request.getSaleSource())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "订单来源不能为空");
        }
        if (Objects.isNull(request.getShipmentType())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "配送方式不能为空");
        }
        if (Objects.isNull(request.getUserIsFriend())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "用户是否为F会员不能为空");
        }
        if (CollectionUtils.isEmpty(request.getGoodsList())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "商品列表不能为空");
        }
        if (request.getGoodsList().size() > 50L) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "商品不能超过50个");
        }
        for (ProductActGoodsItem goodsInfo : request.getGoodsList()) {
            if (Objects.isNull(goodsInfo) || Objects.isNull(goodsInfo.getId())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品sku或套装ID不能都为空");
            }
            if (Strings.isNullOrEmpty(goodsInfo.getLevel())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品品级不能都为空");
            }
            if (goodsInfo.getId() <= 0L) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品sku或套装ID必须有一个大于0");
            }
            if (!GoodsLevelEnum.SKU.getLevel().equals(goodsInfo.getLevel()) && !GoodsLevelEnum.PACKAGE.getLevel().equals(goodsInfo.getLevel())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品品级只能为sku或package");
            }
            if (Objects.isNull(goodsInfo.getSalePrice()) || goodsInfo.getSalePrice() < 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品销售价不能为空");
            }
            if (Objects.isNull(goodsInfo.getVirtual())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传是否为虚拟商品不能为空");
            }
            if (Strings.isNullOrEmpty(goodsInfo.getSaleMode())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品销售模式不能为空");
            }
            if (Objects.isNull(goodsInfo.getBusinessType()) || goodsInfo.getBusinessType() <= 0) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "所传商品类型必须大于0");
            }
        }
    }

    /**
     * 检查是否为可用商品
     *
     * @param g 商品
     * @return boolean
     */
    private boolean isValidGoods(ProductActGoodsItem g) {
        //销售模式
        if (Objects.nonNull(g.getSaleMode())) {
            if (!checkSaleMode(g.getSaleMode())) {
                return false;
            }
        }

        //商家类型，第三方商品过滤（只有自营和采销可参与活动,有品和POP不能参与活动）
        if (Objects.nonNull(g.getBusinessType())) {
            if (!checkBusinessType(g.getBusinessType())) {
                return false;
            }
        }

        //0元商品不可参与活动
        if (Objects.nonNull(g.getSalePrice())) {
            if (g.getSalePrice() <= 0L) {
                return false;
            }
        }

        //虚拟商品不可参与活动
        if (Objects.nonNull(g.getVirtual())) {
            if (g.getVirtual()) {
                return false;
            }
        }

        return true;
    }

    /**
     * 不展示活动的销售模式，比如盲售
     *
     * @param saleMode 销售模式
     * @return boolean
     */
    private boolean checkSaleMode(String saleMode) {
        List<String> sm = Arrays.asList(
                SaleModeEnum.STANDARD.getValue(),
                SaleModeEnum.PRESALES.getValue()
        );
        return sm.contains(saleMode);
    }

    /**
     * 第三方商品不可参与活动（比如有品商品、POP商品）
     *
     * @param businessType 商家类型
     * @return boolean
     */
    private boolean checkBusinessType(Integer businessType) {
        List<Integer> bs = Arrays.asList(
                GoodsBusinessTypeEnum.CN_ORDER.getValue(),
                GoodsBusinessTypeEnum.CN_FAMILYS.getValue(),
                GoodsBusinessTypeEnum.CN_YPORDER.getValue(),
                GoodsBusinessTypeEnum.CN_YP_VMI_SELF.getValue()
        );
        return bs.contains(businessType);
    }

    private void fillProductData(List<PromotionInfo> goodsActList, Map<String, ProductActItemDto> allProductActs, Map<String, PromotionInfo> allPromotions, List<String> goodsActIds) {
        //产品站只返回这几类活动
        List<String> respActType = Lists.newArrayList(
                String.valueOf(ActivityTypeEnum.REDUCE.getValue()),
                String.valueOf(ActivityTypeEnum.DISCOUNT.getValue()),
                String.valueOf(ActivityTypeEnum.BARGAIN.getValue()),
                String.valueOf(ActivityTypeEnum.GIFT.getValue()),
                String.valueOf(ActivityTypeEnum.POST_FREE.getValue()),
                String.valueOf(ActivityTypeEnum.BUY_GIFT.getValue()),
                String.valueOf(ActivityTypeEnum.BUY_REDUCE.getValue())
        );

        for (PromotionInfo act : goodsActList) {
            if (Objects.isNull(act) || StringUtils.isEmpty(act.getPromotionId())) {
                continue;
            }
            if (!respActType.contains(act.getType())) {
                continue;
            }
            if (!goodsActIds.contains(act.getPromotionId())) {
                goodsActIds.add(act.getPromotionId());
            }
            if (!allPromotions.containsKey(act.getPromotionId())) {
                allPromotions.put(act.getPromotionId(), act);
            }
            if (allProductActs.containsKey(act.getPromotionId())) {
                continue;
            }
            ProductActItemDto promotion = convertPromotionToProductDto(act);
            allProductActs.put(promotion.getPromotionId(), promotion);
        }
    }

    /**
     * 产品站2.0的活动基础排序（产品：郭岩慧+张从越）
     *
     * @param acts List<PromotionInfo>
     * @return List<PromotionInfo>
     */
    private List<PromotionInfo> promotionsSort(List<PromotionInfo> acts) {
        if (CollectionUtils.isEmpty(acts)) {
            return Collections.emptyList();
        }

        // 排序（注意c1、c2是倒序遍历的，也就是说c1是第二条数据，c2才是第一条数据）
        return acts.stream().sorted((c1, c2) -> {
            // 优惠类型：立减 > 满减 > 折扣 > 买赠 > 满赠 > 包邮
            int c1ActTypeWeight = getProductSortWeightByActType(c1.getType());
            int c2ActTypeWeight = getProductSortWeightByActType(c2.getType());
            if (c1ActTypeWeight > c2ActTypeWeight) {
                return -1;
            }
            if (c1ActTypeWeight < c2ActTypeWeight) {
                return 1;
            }

            // 门槛条件：无门槛 > 满件（由低到高）> 满额（由低到高）
            // 优惠力度：优惠金额由高到低，折扣力度由高到低
            //    1.立减：立减额度由高到低
            //    2.满减：
            //         1. 满件低到高，满件条件相同的减额高到低。
            //         2. 满元低到高，满元条件相同的减额高到低。
            //         3. 阶梯条件时，仅对比第一阶梯。
            //    3. 满折：
            //         1. 满件低到高，满件条件相同的折扣低到高。满件折扣相同时面额高到低。
            //         2. 满元低到高，满元条件相同的折扣低到高。满元折扣相同时面额高到低。
            //         3. 阶梯条件时，仅对比第一阶梯。

            // 下单立减
            if (c1.getType().equals(String.valueOf(ActivityTypeEnum.BUY_REDUCE.getValue()))) {
                if (c1.getDescRuleIndex().compareTo(c2.getDescRuleIndex()) < 0) {
                    return -1;
                }
                if (c1.getDescRuleIndex().compareTo(c2.getDescRuleIndex()) > 0) {
                    return 1;
                }
            }

            // 买赠、满赠
            if (checkGiftActCond(c1) && checkGiftActCond(c2)) {
                int sortVal = comparePromotionInfo(c1, c2);
                if (sortVal != 0) {
                    return sortVal;
                }
            }

            // 满减、满折、包邮
            if (checkReduceDistinctPostFreeActCond(c1) && checkReduceDistinctPostFreeActCond(c2)) {
                int sortVal = sortReduceDistinctPostFreeActCond(c1, c2);
                if (sortVal != 0) {
                    return sortVal;
                }
            }

            // 过期时间：过期早 > 过期晚
            if (c1.getEndTime() < c2.getEndTime()) {
                return -1;
            }
            if (c1.getEndTime() > c2.getEndTime()) {
                return 1;
            }

            // 兜底：按活动ID倒序
            return c1.getPromotionId().compareTo(c2.getPromotionId());
        }).collect(Collectors.toList());
    }

    /**
     * 根据促销信息的不同条件进行排序比较
     *
     * @param c1 促销信息1
     * @param c2 促销信息2
     * @return 比较结果，-1表示c1优先，1表示c2优先，0表示相等
     */
    private int sortReduceDistinctPostFreeActCond(PromotionInfo c1, PromotionInfo c2) {
        int c1PolicyLastIndex = c1.getPolicys().size() - 1;
        int c2PolicyLastIndex = c2.getPolicys().size() - 1;

        // 满件 > 满元
        int c1QuotaTypeWeight = getProductSortWeightByQuotaType(c1.getPolicys().get(c1PolicyLastIndex).getQuotas().get(0).getType());
        int c2QuotaTypeWeight = getProductSortWeightByQuotaType(c2.getPolicys().get(c2PolicyLastIndex).getQuotas().get(0).getType());
        if (c1QuotaTypeWeight > c2QuotaTypeWeight) {
            return -1;
        }
        if (c1QuotaTypeWeight < c2QuotaTypeWeight) {
            return 1;
        }

        // 满件：从小到大
        long c1QuotaCount = c1.getPolicys().get(c1PolicyLastIndex).getQuotas().get(0).getCount();
        long c2QuotaCount = c2.getPolicys().get(c2PolicyLastIndex).getQuotas().get(0).getCount();
        if (c1QuotaCount < c2QuotaCount) {
            return -1;
        }
        if (c1QuotaCount > c2QuotaCount) {
            return 1;
        }

        // 满元：从小到大
        long c1QuotaMoney = c1.getPolicys().get(c1PolicyLastIndex).getQuotas().get(0).getMoney();
        long c2QuotaMoney = c2.getPolicys().get(c2PolicyLastIndex).getQuotas().get(0).getMoney();
        if (c1QuotaMoney < c2QuotaMoney) {
            return -1;
        }
        if (c1QuotaMoney > c2QuotaMoney) {
            return 1;
        }

        // 满元/满件条件相同时，减额从高到低
        if (c1.getType().equals(String.valueOf(ActivityTypeEnum.REDUCE.getValue()))) {
            long c1ReduceMoney = Math.min(c1.getPolicys().get(c1PolicyLastIndex).getRule().getReduceMoney(), c1.getPolicys().get(c1PolicyLastIndex).getRule().getMaxPrice());
            long c2ReduceMoney = Math.min(c2.getPolicys().get(c2PolicyLastIndex).getRule().getReduceMoney(), c2.getPolicys().get(c2PolicyLastIndex).getRule().getMaxPrice());
            if (c1ReduceMoney > c2ReduceMoney) {
                return -1;
            }
            if (c1ReduceMoney < c2ReduceMoney) {
                return 1;
            }
        }

        // 满元/满件条件相同时，折扣从低到高
        if (c1.getType().equals(String.valueOf(ActivityTypeEnum.DISCOUNT.getValue()))) {
            long c1Distinct = c1.getPolicys().get(c1PolicyLastIndex).getRule().getReduceDiscount();
            long c2Distinct = c2.getPolicys().get(c2PolicyLastIndex).getRule().getReduceDiscount();
            if (c1Distinct < c2Distinct) {
                return -1;
            }
            if (c1Distinct > c2Distinct) {
                return 1;
            }
        }
        return 0;
    }

    /**
     * 比较两个促销信息的优先级
     *
     * @param c1 第一个促销信息
     * @param c2 第二个促销信息
     * @return 比较结果，-1表示c1优先级高于c2，1表示c1优先级低于c2，0表示两者优先级相同
     */
    private int comparePromotionInfo(PromotionInfo c1, PromotionInfo c2) {
        // 满件 > 满元
        int c1QuotaTypeWeight = getProductSortWeightByQuotaType(c1.getPolicyNew().getPolicy().get(0).getIncludedGoodsGroup().get(0).getQuota().getType());
        int c2QuotaTypeWeight = getProductSortWeightByQuotaType(c2.getPolicyNew().getPolicy().get(0).getIncludedGoodsGroup().get(0).getQuota().getType());
        if (c1QuotaTypeWeight > c2QuotaTypeWeight) {
            return -1;
        }
        if (c1QuotaTypeWeight < c2QuotaTypeWeight) {
            return 1;
        }

        // 满件：从小到大
        long c1QuotaCount = c1.getPolicyNew().getPolicy().get(0).getIncludedGoodsGroup().get(0).getQuota().getCount();
        long c2QuotaCount = c2.getPolicyNew().getPolicy().get(0).getIncludedGoodsGroup().get(0).getQuota().getCount();
        if (c1QuotaCount < c2QuotaCount) {
            return -1;
        }
        if (c1QuotaCount > c2QuotaCount) {
            return 1;
        }

        // 满元：从小到大
        long c1QuotaMoney = c1.getPolicyNew().getPolicy().get(0).getIncludedGoodsGroup().get(0).getQuota().getMoney();
        long c2QuotaMoney = c2.getPolicyNew().getPolicy().get(0).getIncludedGoodsGroup().get(0).getQuota().getMoney();
        if (c1QuotaMoney < c2QuotaMoney) {
            return -1;
        }
        if (c1QuotaMoney > c2QuotaMoney) {
            return 1;
        }

        // 每个赠品组最大市场价之总和：从大到小（优惠力度从大到小）
        long c1MaxMarketPrice = calcMaxMarketPrice(c1.getPolicyNew().getPolicy().get(0).getRule().getGiftGoods().getSkuGroupsList());
        long c2MaxMarketPrice = calcMaxMarketPrice(c2.getPolicyNew().getPolicy().get(0).getRule().getGiftGoods().getSkuGroupsList());
        if (c1MaxMarketPrice > c2MaxMarketPrice) {
            return -1;
        }
        if (c1MaxMarketPrice < c2MaxMarketPrice) {
            return 1;
        }
        return 0;
    }

    private boolean checkReduceDistinctPostFreeActCond(PromotionInfo p) {
        return (p.getType().equals(String.valueOf(ActivityTypeEnum.REDUCE.getValue()))
                || p.getType().equals(String.valueOf(ActivityTypeEnum.DISCOUNT.getValue()))
                || p.getType().equals(String.valueOf(ActivityTypeEnum.POST_FREE.getValue())))
                && CollectionUtils.isNotEmpty(p.getPolicys())
                && CollectionUtils.isNotEmpty(p.getPolicys().get(p.getPolicys().size() - 1).getQuotas())
                && Objects.nonNull(p.getPolicys().get(p.getPolicys().size() - 1).getQuotas().get(0))
                && Objects.nonNull(p.getPolicys().get(p.getPolicys().size() - 1).getRule());
    }

    private boolean checkGiftActCond(PromotionInfo p) {
        return (p.getType().equals(String.valueOf(ActivityTypeEnum.BUY_GIFT.getValue()))
                || p.getType().equals(String.valueOf(ActivityTypeEnum.GIFT.getValue())))
                && Objects.nonNull(p.getPolicyNew())
                && CollectionUtils.isNotEmpty(p.getPolicyNew().getPolicy())
                && CollectionUtils.isNotEmpty(p.getPolicyNew().getPolicy().get(0).getIncludedGoodsGroup())
                && Objects.nonNull(p.getPolicyNew().getPolicy().get(0).getIncludedGoodsGroup().get(0))
                && Objects.nonNull(p.getPolicyNew().getPolicy().get(0).getIncludedGoodsGroup().get(0).getQuota())
                && Objects.nonNull(p.getPolicyNew().getPolicy().get(0).getRule())
                && Objects.nonNull(p.getPolicyNew().getPolicy().get(0).getRule().getGiftGoods())
                && CollectionUtils.isNotEmpty(p.getPolicyNew().getPolicy().get(0).getRule().getGiftGoods().getSkuGroupsList());
    }

    /**
     * 每个赠品组最大市场价之总和
     *
     * @param p List
     * @return Long
     */
    private Long calcMaxMarketPrice(List<PolicyNewSkuGroup> p) {
        long result = 0L;
        for (PolicyNewSkuGroup group : p) {
            Optional<PolicyNewGroup> max = group.getListInfo().stream()
                    .filter(e -> Objects.nonNull(e.getMarketPrice()) && Objects.nonNull(e.getBaseNum()))
                    .max(Comparator.comparingLong(e -> e.getMarketPrice() * e.getBaseNum()));
            if (max.isPresent()) {
                result += max.get().getMarketPrice() * max.get().getBaseNum();
            }
        }

        return result;
    }

    /**
     * 获取QuotaType的排序权重（从大到小）
     * Quota类型，0-限额，1-限件，3-每满额，4-每满件
     *
     * @param type int
     * @return int
     */
    private int getProductSortWeightByQuotaType(int type) {
        Map<Integer, Integer> map = new HashMap<Integer, Integer>() {
            private static final long serialVersionUID = -1065569439603002931L;

            {
                put(PolicyQuotaTypeEnum.POLICY_QUOTA_PER_NUM.getType(), 4);
                put(PolicyQuotaTypeEnum.POLICY_QUOTA_NUM.getType(), 3);
                put(PolicyQuotaTypeEnum.POLICY_QUOTA_PER_MONEY.getType(), 2);
                put(PolicyQuotaTypeEnum.POLICY_QUOTA_MONEY.getType(), 1);
            }
        };
        if (map.containsKey(type)) {
            return map.get(type);
        }
        return 0;
    }

    /**
     * 获取优惠类型的排序权重（从大到小）
     * 优惠类型：立减  > 满减 > 折扣 > 买赠 > 满赠 > 包邮
     *
     * @param type String
     * @return int
     */
    private int getProductSortWeightByActType(String type) {
        Map<String, Integer> map = new HashMap<String, Integer>() {
            private static final long serialVersionUID = 7130991316813466538L;

            {
                put(String.valueOf(ActivityTypeEnum.BUY_REDUCE.getValue()), 6);
                put(String.valueOf(ActivityTypeEnum.REDUCE.getValue()), 5);
                put(String.valueOf(ActivityTypeEnum.DISCOUNT.getValue()), 4);
                put(String.valueOf(ActivityTypeEnum.BUY_GIFT.getValue()), 3);
                put(String.valueOf(ActivityTypeEnum.GIFT.getValue()), 2);
                put(String.valueOf(ActivityTypeEnum.POST_FREE.getValue()), 1);
            }
        };
        if (map.containsKey(type)) {
            return map.get(type);
        }
        return 0;
    }

    private ProductRespActGoodsItemDto convertGoodsItemToProductDto(ProductActGoodsItem req) {
        ProductRespActGoodsItemDto resp = new ProductRespActGoodsItemDto();
        resp.setId(req.getId());
        resp.setLevel(req.getLevel());
        return resp;
    }

    /**
     * 将促销信息转换为产品活动项DTO
     *
     * @param req 促销信息请求对象
     * @return 转换后的产品活动项DTO
     */
    private ProductActItemDto convertPromotionToProductDto(PromotionInfo req) {
        //是否忽视赠品库存
        boolean isIgnoreStock = false;
        //是否为VIP活动（指定人群的则为true）
        boolean isVipAct = false;
        //是否支持查看适用商品
        boolean isShowUsableGoods = false;
        if (StringUtils.isNotEmpty(req.getExtend())) {
            ActPromExtend extend = GsonUtil.fromJson(req.getExtend(), ActPromExtend.class);
            if (extend != null) {
                isIgnoreStock = BooleanV2Enum.isYes(extend.getIgnoreStock());
                isVipAct = !Objects.isNull(extend.getVipAct()) && extend.getVipAct();
                isShowUsableGoods = !Objects.isNull(extend.getShowUsableGoods()) && extend.getShowUsableGoods();
            }
        }

        ProductActExtendDto extend = new ProductActExtendDto();

        //是否包邮
        extend.setPostFree(!Objects.isNull(req.getPostfree()) && req.getPostfree().equals(PostFreeEnum.POST_FREE.getVal()));

        //是否显示凑单
        extend.setShowAddOnItem(!Objects.isNull(req.getDescIsShowAddOnItem()) && req.getDescIsShowAddOnItem());

        //是否在单品页强制推荐加价购
        extend.setRecommendForceAddPrice(!Objects.isNull(req.getIsRecommendForceAddPrice()) && req.getIsRecommendForceAddPrice().equals(RecommendForceAddPriceEnum.YES.getVal()));

        //是否为VIP活动（指定人群的则为true）
        extend.setVipAct(isVipAct);

        //是否为F会员专属赠品
        extend.setFMember(!Objects.isNull(req.getIsFMember()) && req.getIsFMember().equals(FMemberEnum.YES.getVal()));

        //是否支持查看适用商品
        extend.setShowUsableGoods(isShowUsableGoods);

        //是否忽视赠品库存
        extend.setIgnoreStock(isIgnoreStock);

        //可用渠道
        List<String> availableChannels = new ArrayList<>();
        if (OnOffLineEnum.isOnLine(req.getOffline())) {
            availableChannels.add(AvailableChannelEnum.MISHOP.getCode());
        } else if (OnOffLineEnum.isOffLine(req.getOffline())) {
            availableChannels.add(AvailableChannelEnum.MIHOME.getCode());
        } else if (OnOffLineEnum.iisOnLineAndOffLine(req.getOffline())) {
            availableChannels.add(AvailableChannelEnum.MISHOP.getCode());
            availableChannels.add(AvailableChannelEnum.MIHOME.getCode());
        }

        ProductActItemDto resp = new ProductActItemDto();
        resp.setPromotionId(req.getPromotionId());
        resp.setType(req.getType());
        resp.setTypeCode(req.getTypeCode());
        resp.setTypeInfo(req.getTypeInfo());
        resp.setName(req.getTitle());
        resp.setAvailableChannel(availableChannels);
        resp.setDescRuleIndex(req.getDescRuleIndex());
        resp.setDescRule(req.getDescRule());
        resp.setPolicys(req.getPolicys());
        resp.setPolicyNew(req.getPolicyNew());
        resp.setExtend(extend);
        return resp;
    }

    private GetProductActRequest makeGetProductActRequest(GetProductActV2Request req, ProductActGoodsItem goods) {
        GetProductActRequest resp = new GetProductActRequest();
        resp.setUserId(req.getUserId());
        resp.setClientId(req.getClientId());
        resp.setAccessCode(req.getAccessCode());
        resp.setSaleSource(req.getSaleSource());
        resp.setOrgCode(req.getOrgCode());
        resp.setProvinceId(Objects.nonNull(req.getProvinceId()) ? req.getProvinceId() : 0);
        resp.setCityId(Objects.nonNull(req.getCityId()) ? req.getCityId() : 0);
        resp.setDistrictId(Objects.nonNull(req.getDistrictId()) ? req.getDistrictId() : 0);
        resp.setAreaId(Objects.nonNull(req.getAreaId()) ? req.getAreaId() : 0);
        resp.setShipmentType(req.getShipmentType());
        resp.setUserIsFriend(Objects.nonNull(req.getUserIsFriend()) && req.getUserIsFriend() ? BooleanV2Enum.YES.getValue() : BooleanV2Enum.NO.getValue());
        if (CartItemLevelEnum.SKU.getLevel().equals(goods.getLevel())) {
            resp.setSku(goods.getId().toString());
        } else if (CartItemLevelEnum.PACKAGE.getLevel().equals(goods.getLevel())) {
            resp.setCommodityId(goods.getId().toString());
        }
        resp.setPrice(goods.getSalePrice());
        return resp;
    }

    private List<ProductActGoodsItem> distinctGoods(List<ProductActGoodsItem> goods) {
        if (CollectionUtils.isEmpty(goods)) {
            return Collections.emptyList();
        }
        return goods.stream().
                filter(e -> Objects.nonNull(e) && Objects.nonNull(e.getId()) && Objects.nonNull(e.getLevel()))
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(e -> e.getId() + "-" + e.getLevel()))), ArrayList::new));
    }

    /**
     * 根据商品信息获取产品活动信息
     *
     * @param request 包含商品信息的请求对象
     * @return 包含产品活动信息的响应对象
     * @throws BizError 如果参数无效或获取商品信息失败
     */
    private GetProductActResponse getProductActV2ByOneGoods(GetProductActRequest request) throws BizError {
        String sku = request.getSku();
        String commodityId = request.getCommodityId();
        if (StringUtils.isEmpty(commodityId) && StringUtils.isEmpty(sku)) {
            log.warn("getProductActV2, param both commodityId and sku is null. commodityId:{} sku:{}", commodityId, request.getSku());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "CommodityID或Sku不能为空");
        }
        if (request.getClientId() <= 0L) {
            log.warn("getProductActV2, param clientId <=0. clientId:{}", request.getClientId());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "clientId不能为空");
        }
        // 获取商品层级信息
        GoodsHierarchy hierarchy = getGoodsHierarchy(request.getSku(), commodityId);
        if (hierarchy == null) {
            log.warn("getProductActV2, get goods hierarchy fail. sku:{}, commodityId:{}", request.getSku(), request.getCommodityId());
            throw ExceptionHelper.create(GeneralCodes.NotFound, "获取商品信息失败");
        }

        // 线上活动
        String skuPackage = StringUtils.isNotBlank(sku) ? sku : commodityId;
        List<String> skuPackageList = Collections.singletonList(skuPackage);
        List<ActivityTool> clientToolList = activityPool.getCurrent(null, request.getClientId(), skuPackageList);

        // 门店活动
        List<ActivityTool> orgToolList = Collections.emptyList();
        if (StringUtils.isNotBlank(request.getOrgCode())) {
            orgToolList = activityPool.getCurrent(request.getOrgCode(), request.getClientId(), skuPackageList);
        }

        GetProductActResponse response = new GetProductActResponse();
        response.setPromotions(handleToolList(clientToolList, request, hierarchy, Boolean.FALSE));
        response.setStorePromotions(handleToolList(orgToolList, request, hierarchy, Boolean.TRUE));
        return response;
    }

}
