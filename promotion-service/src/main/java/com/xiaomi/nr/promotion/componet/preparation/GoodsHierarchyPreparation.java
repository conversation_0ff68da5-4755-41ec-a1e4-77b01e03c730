package com.xiaomi.nr.promotion.componet.preparation;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.engine.componet.ConditionPreparation;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.GoodsHierarchyExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;

/**
 * 购物车里所有商品条件准备
 * 主要为准备购物车里所有商品的category id, product id, commodity id等信息
 *
 * <AUTHOR>
 * @date 2021/3/26
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class GoodsHierarchyPreparation extends ConditionPreparation {

    @Override
    public void prepare(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        Map<ResourceExtType, ExternalDataProvider<?>> providerMap = Optional.ofNullable(context.getExternalDataMap())
                .orElse(Collections.emptyMap());
        context.setGoodsHierarchyMap(getGoodsHierarchy(providerMap));
    }

    private Map<String, GoodsHierarchy> getGoodsHierarchy(Map<ResourceExtType, ExternalDataProvider<?>> providerMap) throws BizError {
        GoodsHierarchyExternalProvider provider = (GoodsHierarchyExternalProvider) providerMap.get(ResourceExtType.GOODS_HIERARCHY);
        return null == provider ? Collections.emptyMap() : provider.getData();
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
    }
}
