package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MultiPromotionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Optional;

/**
 * 线下活动限制条件
 *
 * <AUTHOR>
 * @date 2021/6/1
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class OfflineJoinLimitCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 是否限制商品数量
     */
    private boolean numLimit;
    /**
     * 活动限制规则
     */
    private ActNumLimitRule numLimitRule;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) {
        // 线上或者 不检查活动限制
        if (StringUtils.isBlank(request.getOrgCode()) || !numLimit) {
            return true;
        }

        Long uid = request.getUserId();
        long maxCount = Optional.ofNullable(context.getFillTimes()).orElse(Integer.MAX_VALUE);

        // 校验每单限制次数
        if (numLimitRule.getOrderLimit() != 0L) {
            maxCount = Math.min(maxCount, numLimitRule.getOrderLimit());
            if (maxCount < 1L) {
                log.debug("condition is not satisfied. act maxCount<1. actId:{},uid:{},maxCount:{},orderLimit:{}", promotionId, uid, maxCount, numLimitRule.getOrderLimit());
                return false;
            }
        }

        // 校验每人每天参与次数, 用户id（mid、手机号）为0时不校验与用户相关的次数
        if (uid != 0L && numLimitRule.getPersonLimitDay() != 0L) {
            Long remain = getPersonDayLimitRemain(request, numLimitRule.getPersonLimitDay());
            maxCount = Math.min(maxCount, remain);
            if (maxCount < 1L) {
                log.debug("condition is not satisfied. act maxCount<1. actId:{},uid:{},maxCount:{},personLimitDay:{},remain:{}", promotionId, uid, maxCount, numLimitRule.getPersonLimitDay(), remain);
                return false;
            }
        }

        // 校验每人活动期间参与次数
        if (uid != 0L && numLimitRule.getPersonLimit() != 0L) {
            Long remain = getPersonLimitRemain(request, numLimitRule.getPersonLimit());
            maxCount = Math.min(maxCount, remain);
            if (maxCount < 1L) {
                log.debug("condition is not satisfied. act maxCount<1. actId:{},uid:{},maxCount:{},personLimit:{},remain:{}", promotionId, uid, maxCount, numLimitRule.getPersonLimit(), remain);
                return false;
            }
        }

        // 校验每天全国门店活动总量
        if (numLimitRule.getDayLimitAll() != 0L) {
            Long remain = getAllDayLimitRemain(numLimitRule.getDayLimitAll());
            maxCount = Math.min(maxCount, remain);
            if (maxCount < 1L) {
                log.debug("condition is not satisfied. act maxCount<1. actId:{},uid:{},maxCount:{},limitAllDay:{},remain:{}", promotionId, uid, maxCount, numLimitRule.getDayLimitAll(), remain);
                return false;
            }
        }

        // 校验每天每个门店活动总量
        if (numLimitRule.getDayLimitOne() != 0L) {
            Long remain = getDayOneLimitRemain(request, numLimitRule.getDayLimitOne());
            maxCount = Math.min(maxCount, remain);
            if (maxCount < 1L) {
                log.debug("condition is not satisfied. act maxCount<1. actId:{},uid:{},maxCount:{},limitOneDay:{},remain:{}", promotionId, uid, maxCount, numLimitRule.getDayLimitOne(), remain);
                return false;
            }
        }

        // 校验活动期间全国门店活动总量
        if (numLimitRule.getActivityLimitAll() != 0L) {
            Long remain = getAllLimitRemain(numLimitRule.getActivityLimitAll());
            maxCount = Math.min(maxCount, remain);
            if (maxCount < 1L) {
                log.debug("condition is not satisfied. act maxCount<1. actId:{},uid:{},maxCount:{},limitAll:{},remain:{}", promotionId, uid, maxCount, numLimitRule.getActivityLimitAll(), remain);
                return false;
            }
        }

        // 校验活动期间每个门店活动总量
        if (numLimitRule.getActivityLimitOne() != 0L) {
            Long remain = getOneLimitRemain(request, numLimitRule.getActivityLimitOne());
            maxCount = Math.min(maxCount, remain);
            if (maxCount < 1L) {
                log.debug("condition is not satisfied. act maxCount<1. actId:{},uid:{},maxCount:{},limitOne:{},numJoin:{}", promotionId, uid, maxCount, numLimitRule.getActivityLimitOne(), remain);
                return false;
            }
        }
        context.setFillTimes((int) maxCount);
        return true;
    }

    private Long getPersonDayLimitRemain(CheckoutPromotionRequest request, Long limit) {
        Integer numJoin;
        long now = Instant.now().toEpochMilli();
        if (StringUtils.isNotBlank(request.getUidType())) {
            numJoin = activityRedisDao.getActUtypePersonDayLimitNum(promotionId, request.getUidType(), request.getUserId(), now);
        } else {
            numJoin = activityRedisDao.getActPersonDayLimitNum(promotionId, request.getUserId(), now);
        }
        return limit - numJoin;
    }

    private Long getPersonLimitRemain(CheckoutPromotionRequest request, Long limit) {
        Integer numJoin;
        if (StringUtils.isNotBlank(request.getUidType())) {
            numJoin = activityRedisDao.getActUtypePersonLimitNum(promotionId, request.getUidType(), request.getUserId());
        } else {
            numJoin = activityRedisDao.getActPersonLimitNum(promotionId, request.getUserId());
        }
        return limit - numJoin;
    }

    private Long getAllDayLimitRemain(Long limit) {
        long now = Instant.now().toEpochMilli();
        Integer numJoin = activityRedisDao.getActAllStoreDayLimitNum(promotionId, now);
        return limit - numJoin;
    }

    private Long getAllLimitRemain(Long limit) {
        Integer numJoin = activityRedisDao.getActAllStoreLimitNum(promotionId);
        return limit - numJoin;
    }

    private Long getDayOneLimitRemain(CheckoutPromotionRequest request, Long limit) {
        long now = Instant.now().toEpochMilli();
        Integer numJoin = activityRedisDao.getActStoreDayLimitNum(promotionId, request.getOrgCode(), now);
        return limit - numJoin;
    }

    private Long getOneLimitRemain(CheckoutPromotionRequest request, Long limit) {
        Integer numJoin = activityRedisDao.getActStoreLimitNum(promotionId, request.getOrgCode());
        return limit - numJoin;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof MultiPromotionConfig)) {
            log.error("config is not instanceof MultiPromotionConfig. config:{}", config);
            return;
        }
        MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.numLimitRule = promotionConfig.getNumLimitRule();
        this.numLimit = promotionConfig.isNumLimit();
    }
}
