package com.xiaomi.nr.promotion.domain.coupon.service.maintenance;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.api.dto.model.DeductCouponExtendDto;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.api.dto.model.SsuExtItemDto;
import com.xiaomi.nr.promotion.domain.coupon.model.*;
import com.xiaomi.nr.promotion.domain.coupon.service.base.type.AbstractDeductCoupon;
import com.xiaomi.nr.promotion.enums.*;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.xiaomi.nr.promotion.error.ClientSideErr.COMMON_SYSTEM_ERR_MSG;

/**
 * <AUTHOR>
 * @Date 2024/3/2
 */
@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class MaintenanceDeductCoupon extends AbstractDeductCoupon {

    private CouponServiceTypeEnum serviceType;

    private String couponGroupNo;

    private Integer maxLimit;

    private Integer annualType;

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.MAINTENANCE_REPAIR;
    }

    @Override
    public Coupon generateCartCoupon() {
        Coupon coupon = initCoupon();
        coupon.setCouponSsuExtInfo(buildRangeGoods());
        coupon.setCouponServiceType(checkoutCoupon.getServiceType());
        coupon.setAnnualType(annualType);
        return coupon;
    }



    protected Map<Long, SsuExtItemDto> buildRangeGoods() {
        Map<Long, SsuExtItemDto> extItemDtoMap = Maps.newHashMap();
        for (Map.Entry<Long, CouponRangeGoodsDO> info : checkoutCoupon.getCouponRangeGoodsList()
                .entrySet()) {
            MaintenanceCouponRangeGoodsDO rangeGoods = (MaintenanceCouponRangeGoodsDO) info.getValue();
            SsuExtItemDto ssuExtItemDto = new SsuExtItemDto();
            ssuExtItemDto.setCount(rangeGoods.getCount());
            ssuExtItemDto.setSubBizType(rangeGoods.getBizSubTypeEnum().getCode());
            extItemDtoMap.put(info.getKey(), ssuExtItemDto);
        }
        return extItemDtoMap;
    }
    
    @Override
    public boolean load(CheckoutCoupon checkoutCoupon) throws BizError {
        boolean success =  super.load(checkoutCoupon);

        // 加载serviceType
        serviceType = CouponServiceTypeEnum.valueOf(checkoutCoupon.getServiceType());
        if (Objects.isNull(serviceType)) {
            success = false;
        }

        // 加载年度类型
        annualType = checkoutCoupon.getAnnualType();

        // 加载抵扣规则
        CouponDeductRuleEnum couponRuleEnum = CouponDeductRuleEnum.getEnumByCode(checkoutCoupon.getDeductRule());
        if (Objects.nonNull(couponRuleEnum)) {
            this.conditionList.add(couponRuleEnum.getConditionByEnum());
        } else {
            success = false;
        }

        // 券组
        this.couponGroupNo = checkoutCoupon.getCouponGroupNo();

        // 使用券数量限制
        this.maxLimit = CouponGroupMutualRuleEnum.calcMaxCountByCode(checkoutCoupon.getMutualRule());

        // 按照服务类型加载可用商品列表
        this.validCouponRangeGoodsList = this.buildCouponItemInfoByServiceType();

        return success;
    }

    private List<CouponRangeGoodsDO> buildCouponItemInfoByServiceType() {
        List<CouponRangeGoodsDO> rangeGoodsList = Lists.newArrayList();

        List<Long> validGoodsList = checkoutCoupon.getValidGoodsList();
        Map<Long, CouponRangeGoodsDO> couponRangeGoodsList = checkoutCoupon.getCouponRangeGoodsList();
        if (CollectionUtils.isEmpty(validGoodsList)) {
            return rangeGoodsList;
        }

        for (Long ssuId : validGoodsList) {
            CouponRangeGoodsDO rangeGoodsDO = couponRangeGoodsList.get(ssuId);
            if (!(rangeGoodsDO instanceof MaintenanceCouponRangeGoodsDO)) {
                log.error("MaintenanceDeductCoupon.buildCouponItemInfoByServiceType warn. rangeGoodsDO is not an instance of MaintenanceCouponRangeGoodsDO");
                continue;
            }
            MaintenanceCouponRangeGoodsDO rangeGoods = (MaintenanceCouponRangeGoodsDO) rangeGoodsDO;
            if (Objects.nonNull(rangeGoods)) {
                rangeGoodsList.add(rangeGoods);
            }
        }

        return rangeGoodsList;
    }

    @Override
    public CouponCheckoutResult checkoutCoupon(CouponCheckoutContext context) throws BizError {
        CouponCheckoutResult result = new CouponCheckoutResult();
        List<CartItem> cartList = context.getCartItemList();

        // 最大使用数量校验
        Integer count = context.getCounterMap().getOrDefault(couponGroupNo, 0);
        if (count >= maxLimit) {
            result.setCouponId(id);
            if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                result.setCouponCode(checkoutCoupon.getCouponCode());
            }
            result.setAllow(false);
            result.setUnusableReason("未满足优惠券使用条件");
            result.setUnusableCode(ErrCode.ERR_COUPON_MONEY_NUM.getCode());
            result.setKeyDataUnusable("该类券使用超过上限");
            return result;
        }

        // 券校验
        List<GoodsIndex> goodsList = matchCartList(cartList);
        Map<Long, ValidGoods> validGoodsMap = buildValidGoods(cartList, goodsList);

        List<Integer> indexList = goodsList.stream().map(GoodsIndex::getIndex).collect(Collectors.toList());

        // 是否满足券的抵扣规则
        Pair<Boolean, String> satisfiedResult = isSatisfiedByCouponRule(validGoodsMap);

        if (!satisfiedResult.getLeft()) {
            result.setCouponId(id);
            if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                result.setCouponCode(checkoutCoupon.getCouponCode());
            }
            result.setAllow(false);
            result.setUnusableReason("未满足优惠券使用条件");
            result.setUnusableCode(ErrCode.ERR_COUPON_MONEY_NUM.getCode());
            result.setKeyDataUnusable(satisfiedResult.getRight());
            return result;
        }

        //获取哪些商品参加抵扣
        Map<Long, List<DeductedInfo>> deductedInfoMap = getDeductedInfoList(cartList, indexList);

        long deductMoney = 0L;
        List<DeductedInfo> couponDeductInfo = Lists.newArrayList();
        // 根据类型计算抵扣
        if (Objects.equals(serviceType, CouponServiceTypeEnum.BASIC_MAINTENANCE)) {
            deductMoney = calcBasicMaintenanceDeduct(cartList, deductedInfoMap, couponDeductInfo);
        } else if (Objects.equals(serviceType, CouponServiceTypeEnum.PAINT_REPAIR)) {
            deductMoney = calcPaintRepairDeduct(cartList, deductedInfoMap, couponDeductInfo);
        } else if (Objects.equals(serviceType, CouponServiceTypeEnum.REPAIR_TAIR)) {
            deductMoney = calcRepairTairDeduct(cartList, deductedInfoMap, couponDeductInfo);
        } else if (Objects.equals(serviceType, CouponServiceTypeEnum.NEED_MAINTENANCE)) {
            // 按需保养券计算抵扣逻辑与基础保养券相同
            deductMoney = calcBasicMaintenanceDeduct(cartList, deductedInfoMap, couponDeductInfo);
        } else if (Objects.equals(serviceType, CouponServiceTypeEnum.CONSUMABLES)) {
            deductMoney = calcConsumablesDeduct(cartList, deductedInfoMap, couponDeductInfo);
        }

        result.setCouponId(id);
        if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
            result.setCouponCode(checkoutCoupon.getCouponCode());
        }
        result.setAllow(true);
        result.setReduceAmount(deductMoney);
        result.setDeductedInfoList(couponDeductInfo);
        result.setValidGoods(goodsList);
        result.setValidGoodsPrice(validGoodsMap.values().stream().mapToLong(ValidGoods::getValidPrice).sum());
        return result;
    }

    /**
     * 补胎券计算
     * @param cartList
     * @param deductedInfoMap
     * @param couponDeductInfo
     * @return
     */
    private long calcRepairTairDeduct(List<CartItem> cartList, Map<Long, List<DeductedInfo>> deductedInfoMap, List<DeductedInfo> couponDeductInfo) {
        long deductMoney = 0L;

        List<DeductedInfo> deductList = deductedInfoMap.values().stream().flatMap(List::stream).toList();

        for (DeductedInfo info : deductList) {
            CartItemForCoupon item = (CartItemForCoupon) cartList.get(info.getIndex().getInCarts());

            // calc current
            int curDeductNum = item.getCount();
            long curDeductMoney = info.getGoodPrice() * curDeductNum;

            // calc final
            deductMoney = deductMoney + curDeductMoney;

            // add list
            MaintenanceDeductedInfo maintenanceDeductedInfo = new MaintenanceDeductedInfo(info.getIndex().getInCarts(), info.getGoodPrice());
            maintenanceDeductedInfo.setNum(curDeductNum);
            maintenanceDeductedInfo.setDeductPrice(curDeductMoney);
            couponDeductInfo.add(maintenanceDeductedInfo);
        }
        return deductMoney;
    }

    /**
     * 漆面修复计算
     * @param cartList
     * @param deductedInfoMap
     * @param couponDeductInfo
     * @return
     */
    private long calcPaintRepairDeduct(List<CartItem> cartList, Map<Long, List<DeductedInfo>> deductedInfoMap, List<DeductedInfo> couponDeductInfo) {
        long deductMoney = 0L;

        List<DeductedInfo> deductList = deductedInfoMap.values().stream().flatMap(List::stream).sorted((info1, info2) -> {
            if (Objects.equals(info1.getPriority(), info2.getPriority())) {
                if (Objects.equals(((MaintenanceDeductedInfo) info1).getPriceForPerPage(), ((MaintenanceDeductedInfo) info2).getPriceForPerPage())) {
                    return - (((MaintenanceDeductedInfo) info1).getNum() - ((MaintenanceDeductedInfo) info2).getNum());
                } else {
                    return - (int) (((MaintenanceDeductedInfo) info1).getPriceForPerPage() - ((MaintenanceDeductedInfo) info2).getPriceForPerPage());
                }
            } else {
                return - (info1.getPriority() - info2.getPriority());
            }
        }).toList();

        int deductPage = 0;
        for (DeductedInfo info : deductList) {
            if (deductPage < checkoutCoupon.getWorkHourStandardPage()) {
                CartItemForCoupon item = (CartItemForCoupon) cartList.get(info.getIndex().getInCarts());

                // calc current
                int currDeductPage;
                long curDeductMoney;
                if ((checkoutCoupon.getWorkHourStandardPage() - deductPage) >= item.getRemainStandardPage()) {
                    // 券可以完全抵扣该商品，则抵扣金额 = 当前金额
                    currDeductPage = item.getRemainStandardPage();
                    curDeductMoney = CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList());
                } else {
                    // 券不可以完全抵扣该商品，则抵扣金额 = 当前金额 * 数量 * 抵扣面数 / 总面数
                    currDeductPage = checkoutCoupon.getWorkHourStandardPage() - deductPage;
                    curDeductMoney = Math.floorDiv(info.getGoodPrice() * item.getCount() * currDeductPage, item.getMaintenanceInfo().getWorkHourStandardPage());
                }

                // calc final
                deductPage = deductPage + currDeductPage;
                deductMoney = deductMoney + curDeductMoney;

                MaintenanceDeductedInfo deductedInfo = new MaintenanceDeductedInfo(info.getIndex().getInCarts(), info.getGoodPrice());
                deductedInfo.setNum(currDeductPage);
                deductedInfo.setDeductPrice(curDeductMoney);
                couponDeductInfo.add(deductedInfo);
            }
        }

        return deductMoney;
    }

    /**
     * 基础保养计算
     * @param cartList
     * @param deductedInfoMap
     * @param couponDeductInfo
     * @return
     */
    private long calcBasicMaintenanceDeduct(List<CartItem> cartList, Map<Long, List<DeductedInfo>> deductedInfoMap, List<DeductedInfo> couponDeductInfo) {
        long deductMoney = 0L;
        for (CouponRangeGoodsDO rangeGoodsDO : this.validCouponRangeGoodsList) {
            MaintenanceCouponRangeGoodsDO rangeGoods = (MaintenanceCouponRangeGoodsDO) rangeGoodsDO;

            List<DeductedInfo> deductedInfos = deductedInfoMap.get(rangeGoodsDO.getSsuId());
            //计算抵扣金额
            if (CollectionUtils.isNotEmpty(deductedInfos)) {
                deductedInfos.sort((info1, info2) -> {
                    if (Objects.equals(info1.getPriority(), info2.getPriority())) {
                        return (int) (info1.getGoodPrice() - info2.getGoodPrice());
                    } else {
                        return - (info1.getPriority() - info2.getPriority());
                    }
                });

                // 抵扣数量的累加
                int nums = 0;
                for (DeductedInfo info : deductedInfos) {
                    if (nums < rangeGoods.getCount()) {
                        CartItem item = cartList.get(info.getIndex().getInCarts());
                        // calc current
                        int curDeductNum = Math.min((rangeGoods.getCount() - nums), item.getCount());
                        long curDeductMoney = info.getGoodPrice() * curDeductNum;

                        // calc final
                        deductMoney += curDeductMoney;
                        nums += curDeductNum;

                        MaintenanceDeductedInfo maintenanceDeductedInfo = new MaintenanceDeductedInfo(
                                info.getIndex().getInCarts(), info.getGoodPrice());
                        maintenanceDeductedInfo.setNum(curDeductNum);
                        maintenanceDeductedInfo.setDeductPrice(curDeductMoney);
                        couponDeductInfo.add(maintenanceDeductedInfo);
                    }
                }
            }
        }

        return deductMoney;
    }

    /**
     * 耗材券抵扣计算
     * @param cartList
     * @param deductedInfoMap
     * @param couponDeductInfo
     * @return
     */
    private long calcConsumablesDeduct(List<CartItem> cartList, Map<Long, List<DeductedInfo>> deductedInfoMap, List<DeductedInfo> couponDeductInfo) {
        long deductMoney = 0L;
        for (CouponRangeGoodsDO rangeGoodsDO : this.validCouponRangeGoodsList) {
            MaintenanceCouponRangeGoodsDO rangeGoods = (MaintenanceCouponRangeGoodsDO) rangeGoodsDO;

            List<DeductedInfo> deductedInfos = deductedInfoMap.get(rangeGoodsDO.getSsuId());
            //计算抵扣金额
            if (CollectionUtils.isNotEmpty(deductedInfos)) {
                deductedInfos.sort((info1, info2) -> {
                    if (Objects.equals(info1.getPriority(), info2.getPriority())) {
                        return (int) (info1.getGoodPrice() - info2.getGoodPrice());
                    } else {
                        return - (info1.getPriority() - info2.getPriority());
                    }
                });

                // 抵扣数量的累加
                int nums = 0;
                for (DeductedInfo info : deductedInfos) {
                    if (nums < rangeGoods.getCount()) {
                        CartItemForCoupon item = (CartItemForCoupon) cartList.get(info.getIndex().getInCarts());
                        // calc current
                        int curDeductNum = Math.min((rangeGoods.getCount() - nums), item.getRemainCount());
                        long curDeductMoney = info.getGoodPrice() * curDeductNum;

                        // calc final
                        deductMoney += curDeductMoney;
                        nums += curDeductNum;

                        MaintenanceDeductedInfo maintenanceDeductedInfo = new MaintenanceDeductedInfo(
                                info.getIndex().getInCarts(), info.getGoodPrice());
                        maintenanceDeductedInfo.setNum(curDeductNum);
                        maintenanceDeductedInfo.setDeductPrice(curDeductMoney);
                        couponDeductInfo.add(maintenanceDeductedInfo);
                    }
                }
            }
        }

        return deductMoney;
    }

    /**
     * 构成validGoodMap   查看整体购物车ssu数量，适用于item拆分情况
     * key  --->    ssuId
     * value    --->    ValidGoods
     * @param cartList
     * @param goodsList
     * @return
     */
    private Map<Long, ValidGoods> buildValidGoods(List<CartItem> cartList, List<GoodsIndex> goodsList) {
        Map<Long, ValidGoods> validGoodsMap = Maps.newHashMap();
        for (GoodsIndex goodsIndex : goodsList) {
            Integer index = goodsIndex.getIndex();
            if (index >=  cartList.size()) {
                continue;
            }
            CartItemForCoupon item = (CartItemForCoupon) cartList.get(index);
            Long ssuId = item.getSsuId();
            ValidGoods validGoods = validGoodsMap.getOrDefault(ssuId, new ValidGoods());

            // validNum
            Long validNum = validGoods.getValidNum() + calcValidNumByServiceType(item);

            // validPrice
            Long validPrice = validGoods.getValidPrice() + calcValidPriceByServiceType(item);

            validGoods.setValidNum(validNum);
            validGoods.setValidPrice(validPrice);
            validGoodsMap.put(item.getSsuId(), validGoods);
        }
        
        
        return validGoodsMap;
    }

    /**
     * 计算当前价格
     * @param item
     * @return
     */
    private Long calcValidPriceByServiceType(CartItemForCoupon item) {
        if (Objects.equals(serviceType, CouponServiceTypeEnum.PAINT_REPAIR) || Objects.equals(serviceType, CouponServiceTypeEnum.CONSUMABLES)) {
            return item.getCartPriceBeforeCoupon();
        } else {
            return CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList());
        }
    }
    
    /**
     * 计算当前数量
     * @param item
     * @return
     */
    private Long calcValidNumByServiceType(CartItemForCoupon item) {
        if (Objects.equals(serviceType, CouponServiceTypeEnum.PAINT_REPAIR)) {
            return (long) item.getRemainStandardPage();
        } else if (Objects.equals(serviceType, CouponServiceTypeEnum.CONSUMABLES)) {
            return item.getRemainCount().longValue();

        } else {
            return (long) item.getCount();
        }
    }
    
    
    protected List<GoodsIndex> matchCartList(List<CartItem> cartList) throws BizError {
        final Long couponTypeId = (long) getCouponType().getType();

        // 查找符合包含条件的购物车item列表
        List<GoodsIndex> indexList = new ArrayList<>();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItemForCoupon item = (CartItemForCoupon) cartList.get(idx);
            
            if (SourceEnum.isGiftBargain(item.getSource())) {
                continue;
            }
            //处理当前item是否可参加活动
            boolean itemQualify = CartHelper.isCouponQualifyItem(item, couponTypeId, Long.valueOf(serviceType.getCode()));
            if (!itemQualify) {
                log.info("coupon:{} matchCartList itemQualify item:{}", getCouponId(), GsonUtil.toJson(item));
                continue;
            }
            
            
            boolean isMatched = doMatch(item);
            if (!isMatched) {
                log.info("coupon:{} matchCartList isMatched item:{}", getCouponId(), GsonUtil.toJson(item));
                
                continue;
            }
            indexList.add(new GoodsIndex(item.getItemId(), idx));
        }
        return indexList;
    }
    
    private boolean doMatch(CartItemForCoupon item) {
        boolean isMatch = Boolean.FALSE;
        
        for (CouponRangeGoodsDO rangeGoodsDO : this.validCouponRangeGoodsList) {
            MaintenanceCouponRangeGoodsDO rangeGoods = (MaintenanceCouponRangeGoodsDO) rangeGoodsDO;
            if (Objects.equals(rangeGoods.getSsuId(), item.getSsuId())) {
                // bizSubType不为null
                if (Objects.isNull(item.getBizSubType())
                        || !Objects.equals(item.getBizSubType(), rangeGoods.getBizSubTypeEnum().getCode())) {
                    return isMatch;
                }
                // 匹配bizSubType
                if (!Objects.equals(item.getBizSubType(), BizSubTypeEnum.CAR_WORK_HOUR.getCode())
                        && !Objects.equals(item.getBizSubType(), BizSubTypeEnum.CAR_PARTS.getCode())) {
                    return isMatch;
                }
                // 工时和配件为自费才能使用
                if (Objects.isNull(item.getMaintenanceInfo().getPayType())
                        || !Objects.equals(item.getMaintenanceInfo().getPayType(), 1)) {
                    return isMatch;
                }
                // 如果本张券为漆面修复， 则标准面不能为空或者0
                if (Objects.equals(serviceType, CouponServiceTypeEnum.PAINT_REPAIR)
                        && (Objects.isNull(item.getMaintenanceInfo().getWorkHourStandardPage())
                        || Objects.equals(item.getMaintenanceInfo().getWorkHourStandardPage(), 0)
                        || Objects.equals(item.getRemainStandardPage(), 0))) {
                    return isMatch;
                }
                // 如果本张券为耗材券，剩余数量为空或者0
                if (Objects.equals(serviceType, CouponServiceTypeEnum.CONSUMABLES)
                        && (Objects.isNull(item.getRemainCount()) || Objects.equals(item.getRemainCount(), 0))) {
                    return isMatch;
                }
                // 按需维保券、基础维保券和补胎券：同一ssu上只能使用一张券
                if (item.isUsedNeedMaintenance()) {
                    return isMatch;
                }
                if (item.isUsedBasicMaintenance()) {
                    return isMatch;
                }
                if (item.isUsedTireRepair()) {
                    return isMatch;
                }
                // 漆面修复券：同一ssu上能用多张券
                if (item.isUsedPaintRepair() && !Objects.equals(serviceType, CouponServiceTypeEnum.PAINT_REPAIR)) {
                    return isMatch;
                }
                // 耗材券: 同一ssu上能用多张券
                if (item.isUsedConsumables() && !Objects.equals(serviceType, CouponServiceTypeEnum.CONSUMABLES)) {
                    return isMatch;
                }
                isMatch = Boolean.TRUE;
            }
        }
        return isMatch;
    }

    private Map<Long, List<DeductedInfo>> getDeductedInfoList(List<CartItem> cartList, List<Integer> indexes) throws BizError {
        if (CollectionUtils.isEmpty(indexes)) {
            return Maps.newHashMap();
        }
        
        
        Map<Long, List<DeductedInfo>> deductedGoodsInfo = Maps.newHashMap();
        for (Integer index : indexes) {
            if (index >= cartList.size()) {
                log.error("UpdateCartsReduceDeductCoupon {}th cart is wrong", index);
                throw ExceptionHelper.create(ErrCode.ERR_FUNC_INPUT, COMMON_SYSTEM_ERR_MSG);
            }
            CartItemForCoupon cartItem = (CartItemForCoupon) cartList.get(index);
            
            //当前价格已经为０，不抵扣
            long curPrice = calcValidPriceByServiceType(cartItem);
            if (curPrice <= 0) {
                continue;
            }
            
            MaintenanceDeductedInfo deductedGood = new MaintenanceDeductedInfo(index, curPrice);
            deductedGood.setPriority(cartItem.getMaintenanceInfo().isCanAdjustPrice());

            if (Objects.equals(serviceType, CouponServiceTypeEnum.PAINT_REPAIR)) {
                deductedGood.setNum(Math.min(checkoutCoupon.getWorkHourStandardPage(), cartItem.getRemainStandardPage()));
                deductedGood.setPriceForPerPage(Math.floorDiv(curPrice, cartItem.getRemainStandardPage()));
            }

            if (Objects.equals(serviceType, CouponServiceTypeEnum.CONSUMABLES)) {
                if (cartItem.getRemainCount() == 0) {
                    continue;
                }
                CouponRangeGoodsDO rangeGoodsDO = checkoutCoupon.getCouponRangeGoodsList().get(cartItem.getSsuId());
                MaintenanceCouponRangeGoodsDO rangeGoods = (MaintenanceCouponRangeGoodsDO) rangeGoodsDO;
                deductedGood.setNum(Math.min(rangeGoods.getCount(), cartItem.getRemainCount()));
            }

            List<DeductedInfo> deductedInfos = deductedGoodsInfo.getOrDefault(cartItem.getSsuId(), new ArrayList<>());
            deductedInfos.add(deductedGood);
            deductedGoodsInfo.put(cartItem.getSsuId(), deductedInfos);
        }
        return deductedGoodsInfo;
    }
    
    
    
    @Override
    public void updateCartsReduce(CheckoutPromotionRequest request, CheckoutContext context, CouponCheckoutResult result) {
        //处理商品分摊
        updateItemReduce(request.getCartList(), result);

        //更新context
        updateCommonInfo(request, context);
        
    }

    @Override
    public void updateItemReduce(List<CartItem> cartList, CouponCheckoutResult result) {
        List<DeductedInfo> deductedInfoList = result.getDeductedInfoList();
        
        for (DeductedInfo info : deductedInfoList) {
            MaintenanceDeductedInfo deductedInfo = (MaintenanceDeductedInfo) info;
            // 获取购物车item
            Integer index = deductedInfo.getIndex().getInCarts();
            CartItemForCoupon cartItem = (CartItemForCoupon) cartList.get(index);
            if (cartItem == null) {
                log.error("in updateCartsReduceDeduct cart is nil, id:{}", info.getIndex());
                return;
            }

            //        // 抵扣劵的优惠明细
            ReduceDetailItem reduceDetailItem = new ReduceDetailItem();
            reduceDetailItem.setPromotionId(getCouponId());
            reduceDetailItem.setPromotionType(getCouponType().getType());
            reduceDetailItem.setReduce(deductedInfo.getDeductPrice());
            if (Objects.equals(serviceType, CouponServiceTypeEnum.CONSUMABLES)) {
                reduceDetailItem.setReduceSingle(deductedInfo.getDeductPrice() / deductedInfo.getNum());
            } else {
                reduceDetailItem.setReduceSingle(deductedInfo.getDeductPrice() / cartItem.getCount());
            }
            reduceDetailItem.setSsuId(cartItem.getSsuId());
            DeductCouponExtendDto extendDto = new DeductCouponExtendDto();
            extendDto.setGroupNo(checkoutCoupon.getCouponGroupNo());
            extendDto.setUsedNum(deductedInfo.getNum());
            extendDto.setServiceType(serviceType.getCode());
            reduceDetailItem.setExtend(GsonUtil.toJson(extendDto));
            cartItem.getReduceItemList().add(reduceDetailItem);
            cartItem.useCouponTag(serviceType);

            if (Objects.equals(serviceType, CouponServiceTypeEnum.PAINT_REPAIR)) {
                cartItem.setRemainStandardPage(cartItem.getRemainStandardPage() - deductedInfo.getNum());
            }

            if (Objects.equals(serviceType, CouponServiceTypeEnum.CONSUMABLES)) {
                cartItem.setRemainCount(cartItem.getRemainCount() - deductedInfo.getNum());
            }
            
        }
        
    }

    @Override
    public void updateItemReduce(CouponCheckoutContext context, CouponCheckoutResult result) {
        List<CartItem> cartList = context.getCartItemList();
        List<DeductedInfo> deductedInfoList = result.getDeductedInfoList();

        for (DeductedInfo info : deductedInfoList) {
            MaintenanceDeductedInfo deductedInfo = (MaintenanceDeductedInfo) info;
            // 获取购物车item
            Integer index = deductedInfo.getIndex().getInCarts();
            CartItemForCoupon cartItem = (CartItemForCoupon) cartList.get(index);
            if (cartItem == null) {
                log.error("in updateCartsReduceDeduct cart is nil, id:{}", info.getIndex());
                return;
            }

            //        // 抵扣劵的优惠明细
            ReduceDetailItem reduceDetailItem = new ReduceDetailItem();
            reduceDetailItem.setPromotionId(getCouponId());
            reduceDetailItem.setPromotionType(getCouponType().getType());
            reduceDetailItem.setReduce(deductedInfo.getDeductPrice());
            if (Objects.equals(serviceType, CouponServiceTypeEnum.CONSUMABLES)) {;
                reduceDetailItem.setReduceSingle(deductedInfo.getDeductPrice() / deductedInfo.getNum());
            } else {
                reduceDetailItem.setReduceSingle(deductedInfo.getDeductPrice() / cartItem.getCount());
            }
            reduceDetailItem.setSsuId(cartItem.getSsuId());
            DeductCouponExtendDto extendDto = new DeductCouponExtendDto();
            extendDto.setGroupNo(checkoutCoupon.getCouponGroupNo());
            extendDto.setUsedNum(deductedInfo.getNum());
            extendDto.setServiceType(serviceType.getCode());
            reduceDetailItem.setExtend(GsonUtil.toJson(extendDto));
            cartItem.getReduceItemList().add(reduceDetailItem);
            cartItem.useCouponTag(serviceType);

            if (Objects.equals(serviceType, CouponServiceTypeEnum.PAINT_REPAIR)) {
                cartItem.setRemainStandardPage(cartItem.getRemainStandardPage() - deductedInfo.getNum());
            }

            if (Objects.equals(serviceType, CouponServiceTypeEnum.CONSUMABLES)) {
                cartItem.setRemainCount(cartItem.getRemainCount() - deductedInfo.getNum());
            }

            // 按需保养券和基础保养券在工单维度互斥
            if (Objects.equals(serviceType, CouponServiceTypeEnum.NEED_MAINTENANCE)) {
                List<Long> cannotUseCouponServiceTypes = Optional.ofNullable(cartItem.getMaintenanceInfo().getCannotUseCouponServiceTypes()).orElse(new ArrayList<>());
                if (CollectionUtils.isEmpty(cannotUseCouponServiceTypes) || !cannotUseCouponServiceTypes.contains(serviceType.getCode().longValue())) {
                    cannotUseCouponServiceTypes.add(CouponServiceTypeEnum.BASIC_MAINTENANCE.getCode().longValue());
                }
                cartItem.getMaintenanceInfo().setCannotUseCouponServiceTypes(cannotUseCouponServiceTypes);
            }

            Integer count = context.getCounterMap().getOrDefault(this.couponGroupNo, 0);
            context.getCounterMap().put(this.couponGroupNo, ++count);

        }

    }

    private Pair<Integer, Long> calcReduceByServiceType(CartItemForCoupon cartItem, MaintenanceDeductedInfo deductedInfo) {
        int reduceNum = 0;
        long reduceMoney = 0L;
        switch (serviceType) {
            case BASIC_MAINTENANCE:
                reduceNum = Math.min(cartItem.getCount(), deductedInfo.getNum());
                reduceMoney = deductedInfo.getGoodPrice() * reduceNum;
                break;
            case PAINT_REPAIR:
                reduceNum = Math.min(cartItem.getMaintenanceInfo().getWorkHourStandardPage(), deductedInfo.getNum());
                reduceMoney = deductedInfo.getGoodPrice() * cartItem.getCount() * reduceNum / cartItem.getMaintenanceInfo().getWorkHourStandardPage();
                break;
            case REPAIR_TAIR:
                reduceNum = Math.min(cartItem.getCount(), deductedInfo.getNum());
                reduceMoney = deductedInfo.getGoodPrice() * reduceNum;
                break;
        }

        return Pair.of(reduceNum, reduceMoney);
    }
}
