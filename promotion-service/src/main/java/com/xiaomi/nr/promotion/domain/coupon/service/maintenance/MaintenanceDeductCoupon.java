package com.xiaomi.nr.promotion.domain.coupon.service.maintenance;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.api.dto.model.DeductCouponExtendDto;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.api.dto.model.SsuExtItemDto;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponRangeGoodsDO;
import com.xiaomi.nr.promotion.domain.coupon.model.DeductedInfo;
import com.xiaomi.nr.promotion.domain.coupon.model.MaintenanceCouponRangeGoodsDO;
import com.xiaomi.nr.promotion.domain.coupon.model.MaintenanceDeductedInfo;
import com.xiaomi.nr.promotion.domain.coupon.service.base.type.AbstractDeductCoupon;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.BizSubTypeEnum;
import com.xiaomi.nr.promotion.enums.CouponRuleEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.xiaomi.nr.promotion.error.ClientSideErr.COMMON_SYSTEM_ERR_MSG;

/**
 * <AUTHOR>
 * @Date 2024/3/2
 */
@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class MaintenanceDeductCoupon extends AbstractDeductCoupon {

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.MAINTENANCE_REPAIR;
    }

    @Override
    public Coupon generateCartCoupon() {
        Coupon coupon = initCoupon();
        coupon.setCouponSsuExtInfo(buildRangeGoods());
        return coupon;
    }



    protected Map<Long, SsuExtItemDto> buildRangeGoods() {
        Map<Long, SsuExtItemDto> extItemDtoMap = Maps.newHashMap();
        for (Map.Entry<Long, CouponRangeGoodsDO> info : checkoutCoupon.getCouponRangeGoodsList()
                .entrySet()) {
            MaintenanceCouponRangeGoodsDO rangeGoods = (MaintenanceCouponRangeGoodsDO) info.getValue();
            SsuExtItemDto ssuExtItemDto = new SsuExtItemDto();
            ssuExtItemDto.setCount(rangeGoods.getCount());
            ssuExtItemDto.setSubBizType(rangeGoods.getBizSubTypeEnum().getCode());
            extItemDtoMap.put(info.getKey(), ssuExtItemDto);
        }
        return extItemDtoMap;
    }
    
    @Override
    public boolean load(CheckoutCoupon checkoutCoupon) throws BizError {
        boolean success =  super.load(checkoutCoupon);
        
        CouponRuleEnum couponRuleEnum = CouponRuleEnum.getEnumByCode(checkoutCoupon.getDeductRule());
        if (Objects.nonNull(couponRuleEnum)) {
            this.conditionList.add(couponRuleEnum.getConditionByEnum());
        } else {
            success = false;
        }
        
        this.validCouponRangeGoodsList = buildCouponItemInfo(checkoutCoupon.getValidGoodsList(), checkoutCoupon.getCouponRangeGoodsList());

        return success;
    }
    
    private List<CouponRangeGoodsDO> buildCouponItemInfo(List<Long> validGoodsList,
                                                         Map<Long, CouponRangeGoodsDO> couponRangeGoodsMap) {
        List<CouponRangeGoodsDO> rangeGoodsList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(validGoodsList)) {
            return rangeGoodsList;
        }
        
        for (Long ssuId : validGoodsList) {
            MaintenanceCouponRangeGoodsDO rangeGoods = (MaintenanceCouponRangeGoodsDO) couponRangeGoodsMap.get(ssuId);
            if (Objects.nonNull(rangeGoods)) {
                rangeGoodsList.add(rangeGoods);
            }
        }
        
        return rangeGoodsList;
    }
    
    
    @Override
    public CouponCheckoutResult checkoutCoupon(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        List<CartItem> cartList = request.getCartList();
        CouponCheckoutResult result = new CouponCheckoutResult();

        // 券校验
        List<GoodsIndex> goodsList = matchCartList(cartList, context, joinGoods, (long) getCouponType().getType());
        Map<Long, ValidGoods> validGoodsMap = buildValidGoods(cartList, goodsList);
        
        List<Integer> indexList = goodsList.stream().map(GoodsIndex::getIndex).collect(Collectors.toList());
        
        
        Pair<Boolean, String> satisfiedResult = isSatisfiedByCouponRule(validGoodsMap);
        
        if (!satisfiedResult.getLeft()) {
            result.setAllow(false);
            result.setUnusableReason("未满足优惠券使用条件");
            result.setUnusableCode(ErrCode.ERR_COUPON_MONEY_NUM.getCode());
            result.setKeyDataUnusable(satisfiedResult.getRight());
            return result;
        }
        
        if (satisfiedResult.getLeft()) {
            //获取哪些商品参加抵扣
            Map<Long, List<DeductedInfo>> deductedInfoList = getDeductedInfoList(cartList, indexList);
            
            long deductMoney = 0L;
            List<DeductedInfo> couponDeductInfo = Lists.newArrayList();
            for (CouponRangeGoodsDO rangeGoodsDO : this.validCouponRangeGoodsList) {
                MaintenanceCouponRangeGoodsDO rangeGoods = (MaintenanceCouponRangeGoodsDO) rangeGoodsDO;
                
                List<DeductedInfo> deductedInfos = deductedInfoList.get(rangeGoods.getSsuId());
                //计算抵扣金额
                if (CollectionUtils.isNotEmpty(deductedInfos)) {
                    deductedInfos.sort((info1, info2) -> {
                        if (Objects.equals(info1.getPriority(), info2.getPriority())) {
                            return (int) (info1.getGoodPrice() - info2.getGoodPrice());
                        } else {
                            return info1.getPriority() - info2.getPriority();
                        }
                    });
                    
                    // 抵扣数量的累加
                    int nums = 0;
                    for (DeductedInfo info : deductedInfos) {
                        if (nums < rangeGoods.getCount()) {
                            CartItem item = cartList.get(info.getIndex().getInCarts());
                            // calc current
                            int curDeductNum = Math.min((rangeGoods.getCount() - nums), item.getCount());
                            long curDeductMoney = info.getGoodPrice() * curDeductNum;
                            
                            // calc final
                            deductMoney += curDeductMoney;
                            nums += curDeductNum;
                            
                            MaintenanceDeductedInfo maintenanceDeductedInfo = new MaintenanceDeductedInfo(
                                    info.getIndex().getInCarts(), info.getGoodPrice());
                            maintenanceDeductedInfo.setNum(curDeductNum);
                            couponDeductInfo.add(maintenanceDeductedInfo);
                        }
                    }
                } else {
                    result.setCouponId(id);
                    if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                        result.setCouponCode(checkoutCoupon.getCouponCode());
                    }
                    result.setAllow(false);
                    result.setUnusableReason("未满足优惠券使用条件");
                    result.setUnusableCode(ErrCode.ERR_COUPON_MONEY_NUM.getCode());
                    result.setKeyDataUnusable(satisfiedResult.getRight());
                    return result;
                }
            }
            
            result.setCouponId(id);
            if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                result.setCouponCode(checkoutCoupon.getCouponCode());
            }
            result.setAllow(true);
            result.setReduceAmount(deductMoney);
            result.setDeductedInfoList(couponDeductInfo);
            result.setValidGoods(goodsList);
            result.setValidGoodsPrice(validGoodsMap.values().stream().mapToLong(ValidGoods::getValidPrice).sum());
            return result;
            
        }
        
        return result;
    }


    
    /**
     * 构成validGoodMap   查看整体购物车ssu数量，适用于item拆分情况
     * key  --->    ssuId
     * value    --->    ValidGoods
     * @param cartList
     * @param goodsList
     * @return
     */
    private Map<Long, ValidGoods> buildValidGoods(List<CartItem> cartList, List<GoodsIndex> goodsList) {
        Map<Long, ValidGoods> validGoodsMap = Maps.newHashMap();
        for (GoodsIndex goodsIndex : goodsList) {
            Integer index = goodsIndex.getIndex();
            if (index >=  cartList.size()) {
                continue;
            }
            CartItem item = cartList.get(index);
            Long ssuId = item.getSsuId();
            ValidGoods validGoods = validGoodsMap.getOrDefault(ssuId, new ValidGoods());
            Long validNum = item.getCount().longValue() + validGoods.getValidNum();
            Long validPrice = CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList()) * item.getCount() + validGoods.getValidPrice();
            validGoods.setValidNum(validNum);
            validGoods.setValidPrice(validPrice);
            validGoodsMap.put(item.getSsuId(), validGoods);
        }
        
        
        return validGoodsMap;
    }
    
    
    protected List<GoodsIndex> matchCartList(List<CartItem> cartList, CheckoutContext context, CompareItem joinGoods,
            Long couponTypeId) throws BizError {
        
        // 查找符合包含条件的购物车item列表
        List<GoodsIndex> indexList = new ArrayList<>();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            
            if (SourceEnum.isGiftBargain(item.getSource())) {
                continue;
            }
            //处理当前item是否可参加活动
            boolean itemQualify = CartHelper.isCouponQualifyItem(item, couponTypeId);
            if (!itemQualify) {
                log.info("coupon:{} matchCartList itemQualify item:{}", getCouponId(), GsonUtil.toJson(item));
                continue;
            }
            
            
            boolean isMatched = doMatch(item);
            if (!isMatched) {
                log.info("coupon:{} matchCartList isMatched item:{}", getCouponId(), GsonUtil.toJson(item));
                
                continue;
            }
            indexList.add(new GoodsIndex(item.getItemId(), idx));
        }
        return indexList;
    }
    
    private boolean doMatch(CartItem item) {
        boolean isMatch = Boolean.FALSE;
        
        for (CouponRangeGoodsDO rangeGoodsDO : this.validCouponRangeGoodsList) {
            MaintenanceCouponRangeGoodsDO rangeGoods = (MaintenanceCouponRangeGoodsDO) rangeGoodsDO;
            if (Objects.equals(rangeGoods.getSsuId(), item.getSsuId())) {
                // bizSubType不为null
                if (Objects.isNull(item.getBizSubType()) || !Objects.equals(item.getBizSubType(), rangeGoods.getBizSubTypeEnum().getCode())) {
                    return isMatch;
                }
                // 匹配bizSubType
                if (!Objects.equals(item.getBizSubType(), BizSubTypeEnum.CAR_WORK_HOUR.getCode()) && !Objects.equals(item.getBizSubType(), BizSubTypeEnum.CAR_PARTS.getCode())) {
                    return isMatch;
                }
                // 工时和配件为自费才能使用
                if (!(Objects.nonNull(item.getPayType()) && Objects.equals(item.getPayType(), 1))) {
                    return isMatch;
                }
                isMatch = Boolean.TRUE;
            }
        }
        return isMatch;
    }

    private Map<Long, List<DeductedInfo>> getDeductedInfoList(List<CartItem> cartList, List<Integer> indexes) throws BizError {
        if (CollectionUtils.isEmpty(indexes)) {
            return Maps.newHashMap();
        }
        
        
        Map<Long, List<DeductedInfo>> deductedGoodsInfo = Maps.newHashMap();
        for (Integer index : indexes) {
            if (index >= cartList.size()) {
                log.error("UpdateCartsReduceDeductCoupon {}th cart is wrong", index);
                throw ExceptionHelper.create(ErrCode.ERR_FUNC_INPUT, COMMON_SYSTEM_ERR_MSG);
            }
            CartItem cartItem = cartList.get(index);
            
            //当前价格已经为０，不抵扣
            long curPrice = CartHelper.itemCurPrice(cartItem.getOriginalCartPrice(), cartItem.getReduceItemList());
            if (curPrice <= 0) {
                continue;
            }
            
            DeductedInfo deductedGood = new DeductedInfo(index, curPrice);
            if (cartItem.isCanAdjustPrice()) {
                deductedGood.setPriority(0);
            } else {
                deductedGood.setPriority(Integer.MAX_VALUE);
            }

            List<DeductedInfo> deductedInfos = deductedGoodsInfo.getOrDefault(cartItem.getSsuId(), new ArrayList<>());
            deductedInfos.add(deductedGood);
            deductedGoodsInfo.put(cartItem.getSsuId(), deductedInfos);
        }
        return deductedGoodsInfo;
    }
    
    
    
    @Override
    public void updateCartsReduce(CheckoutPromotionRequest request, CheckoutContext context, CouponCheckoutResult result) {
        //处理商品分摊
        updateItemReduce(request.getCartList(), result);

        //更新context
        updateCommonInfo(request, context);
        
    }
    
    @Override
    public void updateItemReduce(List<CartItem> cartList, CouponCheckoutResult result) {
        List<DeductedInfo> deductedInfoList = result.getDeductedInfoList();
        
        for (DeductedInfo info : deductedInfoList) {
            MaintenanceDeductedInfo deductedInfo = (MaintenanceDeductedInfo) info;
            // 获取购物车item
            Integer index = deductedInfo.getIndex().getInCarts();
            CartItem cartItem = cartList.get(index);
            if (cartItem == null) {
                log.error("in updateCartsReduceDeduct cart is nil, id:{}", info.getIndex());
                return;
            }
            
            // 处理分摊
            int reduceNum = Math.min(cartItem.getCount(), deductedInfo.getNum());
            long reduceMoney = deductedInfo.getGoodPrice() * reduceNum;
            
            //        // 抵扣劵的优惠明细
            ReduceDetailItem reduceDetailItem = new ReduceDetailItem();
            reduceDetailItem.setPromotionId(getCouponId());
            reduceDetailItem.setPromotionType(getCouponType().getType());
            reduceDetailItem.setReduce(reduceMoney);
            reduceDetailItem.setReduceSingle(deductedInfo.getGoodPrice());
            reduceDetailItem.setSsuId(cartItem.getSsuId());
            DeductCouponExtendDto extendDto = new DeductCouponExtendDto();
            extendDto.setGroupNo(checkoutCoupon.getCouponGroupNo());
            extendDto.setUsedNum(reduceNum);
            reduceDetailItem.setExtend(GsonUtil.toJson(extendDto));
            cartItem.getReduceItemList().add(reduceDetailItem);
            
        }
        
    }
}
