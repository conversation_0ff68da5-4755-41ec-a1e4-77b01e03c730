package com.xiaomi.nr.promotion.rpc.carpermit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mi.car.iccc.user.permit.common.model.UserPermitDetail;
import com.mi.car.iccc.user.permit.common.model.UserPermitResponse;
import com.mi.car.iccc.user.permit.common.model.api.IcccCommonCode;
import com.xiaomi.iauth.java.sdk.app.IAuthAppSDKTool;
import com.xiaomi.iauth.java.sdk.common.IAuthTokenInfo;
import com.xiaomi.iauth.java.sdk.configuration.IAuthConfiguration;
import com.xiaomi.iauth.java.sdk.constants.IAuthConstants;
import com.xiaomi.keycenter.okhttp3.HttpUrl;
import com.xiaomi.keycenter.okhttp3.OkHttpClient;
import com.xiaomi.keycenter.okhttp3.Request;
import com.xiaomi.keycenter.okhttp3.Response;
import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.enums.UserCarPermitEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

/**
 * 车主身份服务代理proxy
 *
 * <AUTHOR>
 * @date 2025/5/10 12:56
 */
@Slf4j
@Service
public class UserCarPermitProxyService {


    @Value("${user.permit.url}")
    private String urlPrefix;

    @Autowired
    private NacosConfig nacosConfig;

    /**
     * 获取用户的车主身份
     *
     * @param mid
     * @return
     */
    @Async("checkoutAsyncTaskExecutor")
    public ListenableFuture<UserCarPermitEnum> queryUserPermitAsync(Long mid) {
        try {
            UserCarPermitEnum userCarPermit = queryUserPermit(mid);
            return AsyncResult.forValue(userCarPermit);
        } catch (Exception e) {
            log.error("获取车主身份失败,userId:{},ex:", mid, e);
            return AsyncResult.forExecutionException(e);
        }
    }


    /**
     * 获取用户的车主身份
     *
     * @param mid
     * @return
     */
    public UserCarPermitEnum queryUserPermit(Long mid) {
        UserPermitResponse response = queryCarPermit(mid);
        boolean owner = isOwner(response);
        boolean undeliveredOwner = isUndeliveredOwner(response);
        if (owner) {
            return undeliveredOwner ? UserCarPermitEnum.OWNER_AND_UNDELIVERED_OWNER :
                    UserCarPermitEnum.OWNER;
        }
        return undeliveredOwner ? UserCarPermitEnum.UNDELIVERED_OWNER : UserCarPermitEnum.NONE;
    }

    /**
     * 远程调用获取用户身份
     *
     * @param mid
     * @return
     */
    public UserPermitResponse queryCarPermit(Long mid) {
        if (Objects.isNull(mid) || mid <= 0) {
            log.warn("query user car permit illegal");
            return null;
        }
        OkHttpClient client = new OkHttpClient();
        HttpUrl.Builder urlBuilder = HttpUrl.parse(urlPrefix + "/permit/getUserPermitData").newBuilder();
        urlBuilder.addQueryParameter("mid", String.valueOf(mid));
        // 打开iauth鉴权
        if (nacosConfig.isIauthSwitch()) {
            try {
                IAuthConfiguration instance = IAuthConfiguration.getInstance();
                IAuthTokenInfo info = IAuthAppSDKTool.getInstance().getIAuthToken("iccc-user-permit", false);
                String tokenStr = info.getToken();
                long appId = instance.getAppId();
                urlBuilder.addQueryParameter(IAuthConstants.APP_ID, String.valueOf(appId));
                urlBuilder.addQueryParameter(IAuthConstants.TOKEN, tokenStr);
            } catch (Exception e) {
                log.error("get token from iauth error. configuration is not correctly set", e);
                throw new RuntimeException(e);
            }
        }
        Request request = new Request.Builder().url(urlBuilder.toString()).build();
        UserPermitResponse ret = null;
        try {
            Response response = client.newCall(request).execute();
            JSONObject jsonobject = JSON.parseObject(response.body().string());
            log.info("获取车主身份, request:{}, response:{}", mid, jsonobject.toString());
            if (IcccCommonCode.SUCCESS.getCode() != jsonobject.getIntValue("code")) {
                throw ExceptionHelper.create(ErrCode.ERR_ECARD_UPDATEBALANCE, "获取车主身份失败");
            }
            ret = jsonobject.getObject("data", UserPermitResponse.class);
        } catch (Exception e) {
            log.error("获取车主身份异常, request:{}", mid, e);
            throw new RuntimeException("获取车主身份异常");
        }
        return ret;
    }

    /**
     * 判断是不是车主
     *
     * @param response
     * @return
     */
    private boolean isOwner(UserPermitResponse response) {
        boolean su7Owner = isOwner(response, UserPermitResponse::getSu7);
        boolean su7UltraOwner = isOwner(response, UserPermitResponse::getSu7Ultra);
        boolean yu7Owner = isOwner(response, UserPermitResponse::getYu7);
        return su7Owner || su7UltraOwner || yu7Owner;
    }

    /**
     * 判断是不是准车主
     *
     * @param response
     * @return
     */
    private boolean isUndeliveredOwner(UserPermitResponse response) {
        boolean su7UndeliveredOwner = isUndeliveredOwner(response, UserPermitResponse::getSu7);
        boolean su7UndeliveredUltraOwner = isUndeliveredOwner(response, UserPermitResponse::getSu7Ultra);
        boolean yu7UndeliveredOwner = isUndeliveredOwner(response, UserPermitResponse::getYu7);
        return su7UndeliveredOwner || su7UndeliveredUltraOwner || yu7UndeliveredOwner;
    }

    private boolean isOwner(UserPermitResponse response,
                           Function<UserPermitResponse, UserPermitDetail> detailFunc) {
        return Optional.ofNullable(response)
                .map(detailFunc)
                .map(UserPermitDetail::isOwner)
                .orElse(false);
    }



    private boolean isUndeliveredOwner(UserPermitResponse response,
                                      Function<UserPermitResponse, UserPermitDetail> detailFunc) {
        return Optional.ofNullable(response)
                .map(detailFunc)
                .map(UserPermitDetail::isHasUndeliveredCar)
                .orElse(false);
    }
}
