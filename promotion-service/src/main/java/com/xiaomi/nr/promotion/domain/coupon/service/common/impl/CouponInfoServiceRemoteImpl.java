package com.xiaomi.nr.promotion.domain.coupon.service.common.impl;

import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.coupon.api.dto.coupon.CouponBaseInfo;
import com.xiaomi.nr.coupon.api.dto.coupon.GetUsedCouponRequest;
import com.xiaomi.nr.coupon.api.dto.coupon.GetUsedCouponResponse;
import com.xiaomi.nr.coupon.api.dto.coupon.checkout.GoodsInfo;
import com.xiaomi.nr.coupon.api.dto.trade.*;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.enums.SubmitTypeEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.domain.coupon.model.*;
import com.xiaomi.nr.promotion.domain.coupon.service.common.CouponInfoService;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.BizSubTypeEnum;
import com.xiaomi.nr.promotion.enums.CouponPromotionType;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceStatus;
import com.xiaomi.nr.promotion.resource.provider.CouponProvider;
import com.xiaomi.nr.promotion.rpc.coupon.CouponServiceProxy;
import com.xiaomi.nr.promotion.rpc.coupon.CouponTradeServiceProxy;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.*;

/**
 * Created by wangweiyi on 2023/1/13
 */
@Slf4j
@Service
public class CouponInfoServiceRemoteImpl implements CouponInfoService {

    @Autowired
    private CouponTradeServiceProxy couponTradeServiceProxy;

    @Autowired
    private CouponServiceProxy couponServiceProxy;


    @Override
    public ListenableFuture<CouponOwnedInfo> getByCodeAsync(String code, Long uid) {
        return null;
    }

    @Override
    public ListenableFuture<List<CouponOwnedInfo>> getByCodeAsyncNew(String code, Long uid) {
        return null;
    }

    @Override
    public ListenableFuture<List<CouponOwnedInfo>> getByCodeAsyncNew(CheckoutPromotionRequest request) {
        return null;
    }


    @Override
    public ListenableFuture<CouponOwnedInfo> getByIdAsync(Long cid, Long uid) {
        return null;
    }

    @Override
    public ListenableFuture<List<CouponOwnedInfo>> batchGetByIdAsync(List<Long> cid, Long uid) {
        return null;
    }

    @Override
    @Async("couponAsyncTaskExecutor")
    public ListenableFuture<List<CheckoutCoupon>> getCouponForCheckout(CheckoutPromotionRequest request, Integer orgType, boolean getCouponList) {
        GetCheckoutCouponListV2Request checkoutCouponListRequest = initCheckoutCouponListV2Request(request, orgType, getCouponList);

        try {

            GetCheckoutCouponListV2Response checkoutCouponListV2 = couponTradeServiceProxy.getCheckoutCouponListV2(checkoutCouponListRequest);
            List<CheckoutCoupon> checkoutCouponList = extractCheckoutCoupons(checkoutCouponListV2);
            return AsyncResult.forValue(checkoutCouponList);
        } catch (Exception e) {
            return AsyncResult.forExecutionException(e);
        }
    }

    private GetCheckoutCouponListV2Request initCheckoutCouponListV2Request(CheckoutPromotionRequest request, Integer orgType, boolean getCouponList) {
        GetCheckoutCouponListV2Request checkoutCouponListRequest = new GetCheckoutCouponListV2Request();
        // 获取用户优惠劵列表（可用和不可用）
        List<GoodsInfo> goodsInfos = getUsableGoods(request.getCartList(), request.getChannel(), request.getFromInterface());

        if (!getCouponList) {
            checkoutCouponListRequest.setCouponIds(request.getCouponIds());
            if (CollectionUtils.isNotEmpty(request.getCouponCodes())) {
                checkoutCouponListRequest.setCouponCode(request.getCouponCodes().get(0));
            }
        }
        checkoutCouponListRequest.setClientId(request.getClientId());
        checkoutCouponListRequest.setUserId(request.getUserId());
        checkoutCouponListRequest.setOrgCode(request.getOrgCode());
        checkoutCouponListRequest.setOrgType(orgType);
        checkoutCouponListRequest.setCityId(request.getCityId());
        checkoutCouponListRequest.setShoppingMode(request.getShoppingMode().intValue());
        checkoutCouponListRequest.setSkuPackageList(goodsInfos);
        checkoutCouponListRequest.setShipmentId(request.getShipmentId());
        checkoutCouponListRequest.setSubmitType(request.getSubmitType());
        checkoutCouponListRequest.setUsedCoupon(request.getUsedCouponId());
        checkoutCouponListRequest.setVid(request.getVid());
        AfterSaleFilterParam filterParam = new AfterSaleFilterParam();
        filterParam.setOrderType(request.getWorkOrderType());
        checkoutCouponListRequest.setAfterSaleFilterParam(filterParam);
        BizPlatformEnum bizPlatformEnum = BizPlatformEnum.findByChannel(request.getChannel());
        checkoutCouponListRequest.setBizPlatform(Lists.newArrayList(bizPlatformEnum.getValue()));

        // 兑换场景只需要礼品兑换券
        if (Boolean.TRUE.equals(request.getIsExchange())) {
            checkoutCouponListRequest.setPromotionTypeList(Lists.newArrayList(CouponPromotionType.Gift.getCode()));
        }

        return checkoutCouponListRequest;
    }

    @Override
    @Async("couponAsyncTaskExecutor")
    public ListenableFuture<RemoteCouponResponse> getCouponAndGroupForCheckout(CheckoutPromotionRequest request, Integer orgType, boolean getCouponList) {
        GetCheckoutCouponListV2Request checkoutCouponListRequest = initCheckoutCouponListV2Request(request, orgType, getCouponList);

        try {

            GetCheckoutCouponListV2Response checkoutCouponListV2 = couponTradeServiceProxy.getCheckoutCouponListV2(checkoutCouponListRequest);
            RemoteCouponResponse remoteCouponResponse = new RemoteCouponResponse();




            //TODO Mock数据
            Map<Long, CheckoutCouponInfo> noCodeCoupons = checkoutCouponListV2.getNoCodeCoupons() == null? new HashMap<>(): checkoutCouponListV2.getNoCodeCoupons();
//            noCodeCoupons.putAll(getMockCoupon(request));
            checkoutCouponListV2.setNoCodeCoupons(noCodeCoupons);

            Map<String, CouponGroupInfo> couponGroupInfoMap = checkoutCouponListV2.getCouponGroupInfoMap() == null? new HashMap<>(): checkoutCouponListV2.getCouponGroupInfoMap();
//            couponGroupInfoMap.putAll(getMockCouponGroup());
            checkoutCouponListV2.setCouponGroupInfoMap(couponGroupInfoMap);




            List<CheckoutCoupon> checkoutCouponList = extractCheckoutCoupons(checkoutCouponListV2);
            Map<String, CouponGroupInfoDO> groupInfoMap = convertCouponGroupMap(checkoutCouponListV2);
            remoteCouponResponse.setCouponGroupInfoMap(groupInfoMap);
            remoteCouponResponse.setCheckoutCouponList(checkoutCouponList);
            return AsyncResult.forValue(remoteCouponResponse);
        } catch (Exception e) {
            return AsyncResult.forExecutionException(e);
        }
    }


    private Map<String, CouponGroupInfoDO> convertCouponGroupMap(GetCheckoutCouponListV2Response checkoutCouponListV2) {
        Map<String, CouponGroupInfoDO> groupInfoMap = new HashMap<>();
        Map<String, CouponGroupInfo> couponGroupInfoMap = checkoutCouponListV2.getCouponGroupInfoMap();
        if (couponGroupInfoMap != null) {
            for (Map.Entry<String, CouponGroupInfo> entry : couponGroupInfoMap.entrySet()) {
                CouponGroupInfoDO couponGroupInfoDo = new CouponGroupInfoDO();
                CouponGroupInfo ruleDto = entry.getValue();
                if (ruleDto == null) {
                    continue;
                }
                couponGroupInfoDo.setDeductRule(ruleDto.getDeductRule());
                couponGroupInfoDo.setGroupNo(ruleDto.getCouponGroupNo());
                couponGroupInfoDo.setMutualRule(ruleDto.getMutualRule());
                couponGroupInfoDo.setSupportOrderType(ruleDto.getSupportOrderType());
                groupInfoMap.put(entry.getKey(), couponGroupInfoDo);
            }
        }
        return groupInfoMap;
    }

    private List<CheckoutCoupon> extractCheckoutCoupons(GetCheckoutCouponListV2Response checkoutCouponListV2) {
        List<CheckoutCoupon> checkoutCouponList = new ArrayList<>();

        if (checkoutCouponListV2.getCodeCoupons() != null) {

            checkoutCouponListV2.getCodeCoupons().forEach((couponCode, coupon) -> {
                if (coupon.getCouponBaseInfo() != null) {

                    CheckoutCoupon codeCoupon = convertCheckoutCoupon(coupon);
                    codeCoupon.setCouponCode(couponCode);

                    checkoutCouponList.add(codeCoupon);
                }
            });
        }
        if (checkoutCouponListV2.getNoCodeCoupons() != null) {

            checkoutCouponListV2.getNoCodeCoupons().forEach((couponId, coupon) -> {
                if (coupon.getCouponBaseInfo() != null) {
                    checkoutCouponList.add(convertCheckoutCoupon(coupon));
                }
            });
        }
        return checkoutCouponList;
    }

    @Override
    //@Async("couponAsyncTaskExecutor")
    public ListenableFuture<List<CheckoutCoupon>> getCouponForProtectPrice(CheckoutPromotionRequest request, Integer orgType, boolean getCouponList) {
        GetUsedCouponRequest getUsedCouponRequest = new GetUsedCouponRequest();
        List<GoodsInfo> goodsInfos = getUsableGoods(request.getCartList(), request.getChannel(), request.getFromInterface());

        getUsedCouponRequest.setCouponIds(request.getCouponIds());
        if (CollectionUtils.isNotEmpty(request.getCouponCodes())) {
            getUsedCouponRequest.setCouponCode(request.getCouponCodes().get(0));
        }
        getUsedCouponRequest.setSkuPackageList(goodsInfos);
        getUsedCouponRequest.setUserId(request.getUserId());
        getUsedCouponRequest.setClientId(request.getClientId());
        getUsedCouponRequest.setOrgCode(request.getOrgCode());
        getUsedCouponRequest.setOrgType(orgType);
        //getUsedCouponRequest.setShoppingMode(request.getShoppingMode().intValue());
        getUsedCouponRequest.setSkuPackageList(goodsInfos);
        getUsedCouponRequest.setShipmentId(request.getShipmentId());

        try {

            GetUsedCouponResponse response = couponServiceProxy.getUsedCoupons(getUsedCouponRequest);
            List<CheckoutCoupon> checkoutCouponList = new ArrayList<>();

            if (response.getCodeCoupons() != null) {

                response.getCodeCoupons().forEach((couponCode, coupon) ->{
                    if (coupon.getCouponBaseInfo() != null){

                        CheckoutCoupon codeCoupon = convertCheckoutCoupon(coupon);
                        codeCoupon.setCouponCode(couponCode);

                        checkoutCouponList.add(codeCoupon);
                    }
                });
            }
            if (response.getNoCodeCoupons() != null) {

                response.getNoCodeCoupons().forEach((couponId, coupon) ->{
                    if (coupon.getCouponBaseInfo() != null) {
                        checkoutCouponList.add(convertCheckoutCoupon(coupon));
                    }
                });
            }            return AsyncResult.forValue(checkoutCouponList);
        } catch (Exception e) {
            return AsyncResult.forExecutionException(e);
        }
    }

    /**
     * 筛选可用优惠劵的商品
     * @param cartItems 购物车列表
     * @return 可用优惠劵的商品列表
     */
    protected List<GoodsInfo> getUsableGoods(List<CartItem> cartItems, Integer channel, Integer fromInterface) {
        List<GoodsInfo> goodsInfos = new ArrayList<>();
        Map<String, GoodsInfo> goodsInfoMap = Maps.newHashMap();
        for (CartItem item  : cartItems) {
            if (item.getCannotUseCoupon()) {
                continue;
            }

            //车商城加购页未选中商品跳过
            if (channel != null && Objects.equals(ChannelEnum.CAR_SHOP.getValue(), channel) && fromInterface != null && Objects.equals(FromInterfaceEnum.CHECKOUT_CART.getValue(), fromInterface)) {
                Boolean selected = item.getSelected();
                if (Objects.isNull(selected) || Objects.equals(selected, Boolean.FALSE)) {
                    continue;
                }
            }

            // 整车售后不为自费的商品跳过
            if (Objects.equals(channel, ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue())) {
                if (Objects.isNull(item.getMaintenanceInfo()) || !Objects.equals(item.getMaintenanceInfo().getPayType(), 1)) {
                    continue;
                }
            }

//            GoodsInfo goodsInfo = new GoodsInfo();
            if (item.getSsuId() != null) {
                GoodsInfo goodsInfo = goodsInfoMap.getOrDefault(item.getSsuId().toString(), new GoodsInfo());
                goodsInfo.setLevel("ssu");
                goodsInfo.setId(item.getSsuId());
                if (Objects.equals(item.getBizSubType(), BizSubTypeEnum.CAR_WORK_HOUR.getCode())) {

                    goodsInfo.setTag("labor");
                    goodsInfo.setCount(NumberUtil.add(item.getCount(), goodsInfo.getCount()).intValue());
                } else if (Objects.equals(item.getBizSubType(), BizSubTypeEnum.CAR_PARTS.getCode())) {
                    goodsInfo.setTag("part");
                    goodsInfo.setCount(NumberUtil.add(item.getCount(), goodsInfo.getCount()).intValue());
                }
                goodsInfoMap.put(item.getSsuId().toString(), goodsInfo);

            } else if (StringUtils.isNotEmpty(item.getSku())) {
                GoodsInfo goodsInfo = goodsInfoMap.getOrDefault(item.getSku(), new GoodsInfo());
                goodsInfo.setLevel("sku");
                goodsInfo.setId(Long.valueOf(item.getSku()));
                goodsInfoMap.put(item.getSku(), goodsInfo);

            } else if (StringUtils.isNotEmpty(item.getPackageId())) {
                GoodsInfo goodsInfo = goodsInfoMap.getOrDefault(item.getPackageId(), new GoodsInfo());
                goodsInfo.setLevel("package");
                goodsInfo.setId(Long.valueOf(item.getPackageId()));
                goodsInfoMap.put(item.getPackageId(), goodsInfo);

            }
        }
        goodsInfos.addAll(goodsInfoMap.values());
        return Lists.newArrayList(goodsInfoMap.values());
    }

    private CheckoutCoupon convertCheckoutCoupon(CheckoutCouponInfo remoteCoupon) {

        CheckoutCoupon checkoutCoupon = new CheckoutCoupon();


        checkoutCoupon.setValidCode(remoteCoupon.getValidCode());
        checkoutCoupon.setInvalidReason(remoteCoupon.getInvalidReason());
        checkoutCoupon.setInvalidData(remoteCoupon.getInvalidData());
        checkoutCoupon.setValidSkuList(remoteCoupon.getValidSkuList());
        checkoutCoupon.setValidPackageList(remoteCoupon.getValidPackageList());
        checkoutCoupon.setCouponGroupNo(remoteCoupon.getCouponGroupNo());

        checkoutCoupon.setValidGoodsList(remoteCoupon.getValidSsuList());

        CouponBaseInfo baseInfo = remoteCoupon.getCouponBaseInfo();
        checkoutCoupon.setCouponId(baseInfo.getCouponId());
        checkoutCoupon.setCouponName(baseInfo.getCouponName());
        checkoutCoupon.setConfigId(baseInfo.getConfigId());
        checkoutCoupon.setStatus(baseInfo.getStatus());
        checkoutCoupon.setStartTime(baseInfo.getStartTime());
        checkoutCoupon.setEndTime(baseInfo.getEndTime());
        checkoutCoupon.setCouponType(baseInfo.getCouponType());
        checkoutCoupon.setCouponRangeDesc(baseInfo.getCouponRangeDesc());
        checkoutCoupon.setCouponRuleDesc(baseInfo.getCouponRuleDesc());


        checkoutCoupon.setPromotionType(baseInfo.getPromotionType());
        checkoutCoupon.setPromotionValue(baseInfo.getPromotionValue());
        checkoutCoupon.setMaxReduce(baseInfo.getMaxReduce() == null? null: baseInfo.getMaxReduce().longValue());
        checkoutCoupon.setUseChannelDesc(baseInfo.getUseChannelDesc());
        checkoutCoupon.setBottomType(baseInfo.getBottomType());
        checkoutCoupon.setBottomPrice(baseInfo.getBottomPrice());
        checkoutCoupon.setBottomCount(baseInfo.getBottomCount());
        checkoutCoupon.setPostageFree(baseInfo.getPostFree());
        checkoutCoupon.setShare(baseInfo.getShare());
        checkoutCoupon.setType(baseInfo.getType());
        checkoutCoupon.setTypeCode(baseInfo.getTypeCode());
        checkoutCoupon.setLimitUseRegion(baseInfo.getLimitUseRegion());
        checkoutCoupon.setSendChannel(baseInfo.getSendChannel());
        checkoutCoupon.setUseChannel(baseInfo.getUseChannel());
        checkoutCoupon.setBudgetApplyNo(baseInfo.getBudgetApplyNo());
        checkoutCoupon.setLineNum(baseInfo.getLineNum());
        checkoutCoupon.setServiceType(baseInfo.getServiceScene());
        checkoutCoupon.setWorkHourStandardPage(baseInfo.getWorkHourStandardPage());
        Map<Long, CouponRangeGoodsDO> couponItemExtendInfoMap = convertCouponItemExtendInfo(remoteCoupon.getCouponSsuExtInfo());
        checkoutCoupon.setCouponRangeGoodsList(couponItemExtendInfoMap);
        checkoutCoupon.setTags(remoteCoupon.getCouponBaseInfo().getTags());
        Integer annualType = Optional.ofNullable(baseInfo.getAnnualType()).orElse(0);
        checkoutCoupon.setAnnualType(annualType);
        checkoutCoupon.setCheckoutStage(baseInfo.getCheckoutStage());
        return checkoutCoupon;

    }

    private Map<Long, CouponRangeGoodsDO> convertCouponItemExtendInfo(Map<Long, SsuExtItemDto> ssuExtItemDtoMap) {
        Map<Long, CouponRangeGoodsDO> couponItemExtendInfoMap = new HashMap<>();
        if (ssuExtItemDtoMap != null) {
            ssuExtItemDtoMap.forEach((ssuId, ssuExtInfo) -> {
                MaintenanceCouponRangeGoodsDO couponItemExtendInfo = new MaintenanceCouponRangeGoodsDO();
                couponItemExtendInfo.setSsuId(ssuId);
                if (ssuExtInfo.isLaborSsu()) {

                    couponItemExtendInfo.setBizSubTypeEnum(BizSubTypeEnum.CAR_WORK_HOUR);
                } else if (ssuExtInfo.isPartSsu()) {
                    couponItemExtendInfo.setBizSubTypeEnum(BizSubTypeEnum.CAR_PARTS);
                } else {
                    return;
                }
                couponItemExtendInfo.setCount(ssuExtInfo.getCount());
                couponItemExtendInfoMap.put(ssuId, couponItemExtendInfo);
            });
        }
        return couponItemExtendInfoMap;
    }

    @Override
    public boolean lockCoupon(ResourceObject<CouponProvider.ResContent> resourceObject) throws BizError {
        LockCouponRequest request = new LockCouponRequest();
        CouponProvider.MultiResContent content = (CouponProvider.MultiResContent) resourceObject.getContent();
        request.setClientId(content.getClientId());
        request.setUserId(content.getUserId());
        request.setOrderId(resourceObject.getOrderId());
        request.setOrgCode(content.getOrgCode());
        request.setOffline(content.getOffline());
        request.setCouponItems(convertCouponItem(content.getCouponItems()));
        request.setSubmitType(content.getSubmitType());
        request.setUsedCoupon(content.getUsedCouponId());
        request.setBizPlatform(content.getBizPlatform());
        request.setVid(content.getVid());

        couponTradeServiceProxy.lockCoupon(request);

        return true;
    }

    private List<CouponLockItem> convertCouponItem(List<CouponProvider.CouponItem> couponItemList) {
        List<CouponLockItem> couponLockItemList = new ArrayList<>();
        for (CouponProvider.CouponItem couponItem : couponItemList) {
            CouponLockItem couponLockItem = new CouponLockItem();
            couponLockItem.setCouponCode(couponItem.getCode());
            couponLockItem.setCouponId(couponItem.getId());
            couponLockItem.setReduceExpress(couponItem.getReduceExpress());
            couponLockItem.setReplaceMoney(couponItem.getReplaceMoney());
            couponLockItemList.add(couponLockItem);
        }
        return couponLockItemList;
    }

    @Override
    public boolean consumeCoupon(ResourceObject<CouponProvider.ResContent> resourceObject) throws BizError {
        ConsumeCouponRequest request = new ConsumeCouponRequest();
        CouponProvider.MultiResContent content = (CouponProvider.MultiResContent) resourceObject.getContent();
        request.setClientId(content.getClientId());
        request.setUserId(content.getUserId());
        request.setOrderId(resourceObject.getOrderId());
        request.setOffline(content.getOffline());
        request.setSubmitType(content.getSubmitType());
        request.setUsedCoupon(content.getUsedCouponId());

        List<Long> couponIds = new ArrayList<>();
        String couponCode = null;
        for (CouponProvider.CouponItem couponItem : content.getCouponItems()) {
            if (couponItem.getCode() != null) {
                couponCode = couponItem.getCode();
            }
            if (couponItem.getId() != null) {
                couponIds.add(couponItem.getId());
            }

        }
        request.setCouponIds(couponIds);
        request.setCouponCode(couponCode);
        request.setBizPlatform(content.getBizPlatform());
        request.setVid(content.getVid());
        couponTradeServiceProxy.consumeCoupon(request);

        return true;
    }

    @Override
    public boolean rollbackCoupon(ResourceObject<CouponProvider.ResContent> resourceObject) throws BizError {
        RollbackCouponRequest request = new RollbackCouponRequest();

        CouponProvider.MultiResContent content = (CouponProvider.MultiResContent) resourceObject.getContent();

        request.setClientId(content.getClientId());
        request.setUserId(content.getUserId());
        request.setOrderId(resourceObject.getOrderId());
        request.setOffline(content.getOffline());
        request.setSubmitType(content.getSubmitType());
        request.setUsedCoupon(content.getUsedCouponId());

        List<Long> couponIds = new ArrayList<>();
        String couponCode = null;
        for (CouponProvider.CouponItem couponItem : content.getCouponItems()) {
            if (couponItem.getCode() != null) {
                couponCode = couponItem.getCode();
            }
            if (couponItem.getId() != null) {
                couponIds.add(couponItem.getId());
            }

        }
        request.setCouponIds(couponIds);
        request.setCouponCode(couponCode);
        request.setBizPlatform(content.getBizPlatform());
        request.setVid(content.getVid());
        couponTradeServiceProxy.rollbackCoupon(request);
        return true;
    }

    @Override
    public boolean refundCoupon(int resourceStatus, ResourceObject<CouponProvider.ResContent> resourceObject) throws BizError {
        CouponProvider.MultiResContent content = (CouponProvider.MultiResContent) resourceObject.getContent();
        if (Objects.isNull(content)) {
            log.error("refundCoupon resource error. resourceStatus:{}, resourceObject:{}", resourceStatus, resourceObject);
            return false;
        }

        List<Long> couponIds = new ArrayList<>();
        String couponCode = null;

        for (CouponProvider.CouponItem couponItem : content.getCouponItems()) {
            if (Strings.isNotBlank(couponItem.getCode())) {
                couponCode = couponItem.getCode();
                continue;
            }

            if (couponItem.getId() == null || couponItem.getId() <= 0L) {
                continue;
            }

            if (SubmitTypeEnum.NORMAL.getCode() == content.getSubmitType()) {
                couponIds.add(couponItem.getId());
                continue;
            }

            if (SubmitTypeEnum.REPLACE.getCode() == content.getSubmitType()) {
                if (resourceStatus == ResourceStatus.COMMITED.getValue()) {
                    couponIds.add(couponItem.getId());
                } else if (resourceStatus == ResourceStatus.ROLLBACKED.getValue()) {
                    couponIds.add(content.getUsedCouponId());
                }
            }
        }

        RollbackCouponRequest request = new RollbackCouponRequest();
        request.setClientId(content.getClientId());
        request.setUserId(content.getUserId());
        request.setOrderId(resourceObject.getOrderId());
        request.setOffline(content.getOffline());
        request.setSubmitType(0);
        request.setCouponIds(couponIds);
        request.setCouponCode(couponCode);
        request.setBizPlatform(content.getBizPlatform());
        couponTradeServiceProxy.rollbackCoupon(request);
        return true;
    }
}
