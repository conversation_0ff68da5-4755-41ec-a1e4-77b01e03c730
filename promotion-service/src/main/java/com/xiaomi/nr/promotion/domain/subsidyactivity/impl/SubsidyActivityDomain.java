package com.xiaomi.nr.promotion.domain.subsidyactivity.impl;

import com.xiaomi.nr.promotion.domain.subsidyactivity.AbstractSubsidyActivityDomain;
import com.xiaomi.nr.promotion.domain.subsidyactivity.facade.SubsidyActivityFacade;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class SubsidyActivityDomain extends AbstractSubsidyActivityDomain {

    @Autowired
    private SubsidyActivityFacade facade;

    @Override
    public void checkout(DomainCheckoutContext domainCheckoutContext) throws Exception {
        // 加购不过
        if (domainCheckoutContext.getFromInterface().equals(FromInterfaceEnum.CHECKOUT_CART)){
            return;
        }
        facade.checkout(domainCheckoutContext);
    }


}