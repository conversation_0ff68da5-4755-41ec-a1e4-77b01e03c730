package com.xiaomi.nr.promotion.model.promotionconfig;

import com.xiaomi.nr.promotion.enums.OnOffLineEnum;
import com.xiaomi.nr.promotion.enums.OrgScopeEnum;
import com.xiaomi.nr.promotion.enums.UserGroupActionEnum;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ConfigMetaInfo;

import java.time.LocalTime;
import java.util.List;
import java.util.Set;

/**
 * 抽象活动配置类
 *
 * <AUTHOR>
 * @date 2021/3/16
 */
public interface AbstractPromotionConfig {
    /**
     * 活动ID
     *
     * @return 活动ID
     */
    Long getPromotionId();

    /**
     * 设置活动ID
     *
     * @param promotionId 活动ID
     */
    void setPromotionId(Long promotionId);

    /**
     * 获取活动列表
     *
     * @return 活动列表
     */
    PromotionToolType getPromotionType();

    /**
     * 设置活动类型
     *
     * @param promotionType 活动类型
     */
    void setPromotionType(PromotionToolType promotionType);

    /**
     * 获取活动名称
     *
     * @return 名称
     */
    String getName();

    /**
     * 设置活动名称
     *
     * @param name 名称
     */
    void setName(String name);

    /**
     * 获取开始时间
     *
     * @return 活动开始时间 （秒）
     */
    long getUnixStartTime();

    /**
     * 设置开始时间
     *
     * @param unixStartTime 开始时间
     */
    void setUnixStartTime(long unixStartTime);

    /**
     * 获取结束时间
     *
     * @return 结束时间（秒）
     */
    long getUnixEndTime();

    /**
     * 设置活动结束时间
     *
     * @param unixEndTime 结束时间
     */
    void setUnixEndTime(long unixEndTime);

    /**
     * 是否周期性
     *
     * @return true/false
     */
    boolean isDaily();

    /**
     * 设置是否是周期性的活动
     *
     * @param daily 是否周期
     */
    void setDaily(boolean daily);

    /**
     * 获取
     *
     * @return HH:mm
     */
    LocalTime getDailyStartTime();

    /**
     * 设置周期开始时间
     *
     * @param dailyStartTime 周期开始时间
     */
    void setDailyStartTime(LocalTime dailyStartTime);

    /**
     * 获取周期结束时间
     *
     * @return HH:mm
     */
    LocalTime getDailyEndTime();

    /**
     * 设置周期结束时间
     *
     * @param dailyEndTime 周期结束时间
     */
    void setDailyEndTime(LocalTime dailyEndTime);

    /**
     * 获取活动范围 线上/线下/线上+线下
     *
     * @return 范围
     */
    OnOffLineEnum getOffline();

    /**
     * 设置活动范围 线上/线下/线上+线下
     *
     * @param offline 范围
     */
    void setOffline(OnOffLineEnum offline);

    /**
     * 获取门店范围
     *
     * @return 获取门店范围
     */
    OrgScopeEnum getOrgScope();

    /**
     * 设置门店范围
     *
     * @param orgScope 门店范围
     */
    void setOrgScope(OrgScopeEnum orgScope);

    /**
     * 获取门店列表
     *
     * @return 门店列表
     */
    List<String> getSelectOrgCodes();

    /**
     * 设置门店列表
     *
     * @param selectOrgCodes 门店列表
     */
    void setSelectOrgCodes(List<String> selectOrgCodes);

    /**
     * 获取支持的应用端
     *
     * @return 端列表
     */
    List<String> getSelectClients();

    /**
     * 设置支持的应用端
     *
     * @param selectClients 端列表
     */
    void setSelectClients(List<String> selectClients);

    /**
     * 支持的获取人群范围
     *
     * @return 人群动作
     */
    UserGroupActionEnum getGroupAction();

    /**
     * 设置支持人群范围
     *
     * @param groupAction 人群动作范围
     */
    void setGroupAction(UserGroupActionEnum groupAction);

    /**
     * 获取支持的人群
     *
     * @return 人群列表
     */
    List<String> getSelectGroups();

    /**
     * 设置人群
     *
     * @param selectGroups 获取人群
     */
    void setSelectGroups(List<String> selectGroups);

    /**
     * 获取可参加的商品信息
     *
     * @return includeSkuPackages
     */
    Set<String> getIncludeSkuPackages();

    /**
     * 设置可参加的商品信息
     *
     * @param includeSkuPackages 商品列表
     */
    void setIncludeSkuPackages(Set<String> includeSkuPackages);

    /**
     * 获取元数据
     *
     * @return 元数据
     */
    ConfigMetaInfo getMetaInfo();

    /**
     * 设置元数据
     *
     * @param metaInfo 元数据
     */
    void setMetaInfo(ConfigMetaInfo metaInfo);

    /**
     * 获取渠道
     *
     * @return 渠道列表
     */
    List<Integer> getChannels();

    /**
     * 设置渠道
     *
     * @param channels 渠道列表
     */
    void setChannels(List<Integer> channels);


    /**
     * 获取文案列表
     *
     * @return descRule
     */
    List<String> getDescRule();

    /**
     * 设置文案列表
     *
     * @param descRule descRule
     */
    void setDescRule(List<String> descRule);

    /**
     * 获取短规则
     *
     * @return 短规则
     */
    String getDescRuleIndex();

    /**
     * 设置短规则
     *
     * @param descRuleIndex 短规则
     */
    void setDescRuleIndex(String descRuleIndex);

    /**
     * 设置渠道范围
     *
     * @param channel 渠道范围
     */
    void setChannel(List<Integer> channel);

    /**
     * 获取渠道范围
     * @return 渠道范围
     */
    List<Integer> getChannel();

    /**
     * 设置活动版本
     *
     * @param version 活动版本
     */
    void setVersion(Integer version);

    /**
     * 获取活动版本
     *
     * @return 活动版本
     */
    Integer getVersion();

    /**
     * 设置活动版本
     *
     * @param priority 活动版本
     */
    void setPriority(Integer priority);

    /**
     * 获取活动版本
     *
     * @return 活动版本
     */
    Integer getPriority();

}
