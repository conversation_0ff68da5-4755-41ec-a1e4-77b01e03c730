package com.xiaomi.nr.promotion.entity.redis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 规则
 *
 * <AUTHOR>
 * @date 2021/5/14
 */
@Data
public class RuleEle implements Serializable {
    private static final long serialVersionUID = -8264519699282487668L;
    /**
     * 减免的钱，现金券，可以同时具有包邮属性
     */
    @SerializedName("reduce_money")
    private Long reduceMoney = 0L;

    /**
     * 打的折扣，折扣券<9折就是90>，可以同时具有包邮属性
     */
    @SerializedName("reduce_discount")
    private Long reduceDiscount = 0L;

    /**
     * 减免的邮费，部分抵邮券
     */
    @SerializedName("reduce_express")
    private Long reduceExpress = 0L;

    /**
     * 包邮属性，现金/折扣/抵扣券可以使用，1为包邮
     */
    @SerializedName("postfree")
    private Integer postFree = 0;

    /**
     * 减最低几件，减最低几件的活动
     */
    @SerializedName("reduce_num")
    private Integer reduceNum = 0;

    /**
     * 赠品的信息
     */
    @SerializedName("gift_goods")
    private Goods giftGoods;

    /**
     * 加价购的信息
     */
    @SerializedName("bargain_goods")
    private Goods bargainGoods;

    /**
     * 折扣券的最大减免价格，如果为0，是不设上限
     */
    @SerializedName("max_price")
    private Long maxPrice = 0L;

    /**
     * 可以被抵扣的列表，抵扣券，可以同时具有包邮属性
     */
    @SerializedName("target_goods")
    private Map<String, String> deductedGoods;

    /**
     * 抵扣劵金额，抵扣后商品的价格（N元劵包含之前的一分钱抵扣劵）
     */
    @SerializedName("nyuan_price")
    private Long NYuanPrice = 0L;
}
