package com.xiaomi.nr.promotion.resource;


import com.xiaomi.nr.promotion.mq.consumer.entity.OrderData;
import com.xiaomi.nr.promotion.resource.model.OrderDataInfo;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

/**
 * 资源处理类接口
 * 实现此接口的类,
 *
 * <AUTHOR>
 * @date 2021/
 */
public interface ResourceProvider<ContentType> {

    /**
     * 获取provider的资源
     *
     * @return 资源对象
     */
    ResourceObject<ContentType> getResource();

    /**
     * 将对应资源初始化到provider，一个provider使用唯一一个资源对象
     *
     * @param object 资源对象
     */
    void initResource(ResourceObject<ContentType> object);

    /**
     * 将对应资源初始化到provider，一个provider使用唯一一个资源对象
     * @param object 资源对象
     * @param orderData 订单信息对象
     */
    default void initResource(ResourceObject<ContentType> object, OrderDataInfo orderData){}

    /**
     * 锁定本资源时需要进行的操作
     *
     * @throws BizError 业务异常
     */
    void lock() throws BizError;

    /**
     * 资源对象需要进行消耗时进行的操作
     *
     * @throws BizError 业务异常
     */
    void consume() throws BizError;

    /**
     * 资源对象回滚时进行的操作
     *
     * @throws BizError 业务异常
     */
    void rollback() throws BizError;

    /**
     * 资源退还
     *
     * @throws BizError 业务异常
     */
    default void refund(int resourceStatus) throws BizError {}

    /**
     * 发生资源抢占冲突时（resourceId+resourceType冲突），给出的文案
     *
     * @return 文案
     */
    String conflictText();
}
