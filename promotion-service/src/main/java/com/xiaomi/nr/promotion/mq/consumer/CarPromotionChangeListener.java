package com.xiaomi.nr.promotion.mq.consumer;

import com.xiaomi.nr.promotion.activity.pool.CarPromotionInstancePool;
import com.xiaomi.nr.promotion.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@ConditionalOnExpression(value = "'${biz.area.id.list}'.contains('CAR')")
@RocketMQMessageListener(
        topic = "${rocketmq.topic.promotion.notify.broadcast}",
        messageModel = MessageModel.BROADCASTING,
        nameServer = "${rocketmq.name-server}",
        accessKey = "${rocketmq.access-key}",
        secretKey = "${rocketmq.secret-key}",
        consumerGroup = "${rocketmq.consumer.group.promotion.notify.broadcast}")
public class CarPromotionChangeListener implements RocketMQListener<String> {

    @Autowired(required = false)
    private CarPromotionInstancePool carPromotionInstancePool;



    @Override
    public void onMessage(String message) {
        log.info("CarPromotionChangeListener receive msg:{}", message);
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("CarPromotionChangeListener error. message is empty");
                return;
            }
            MessageContent messageContent = GsonUtil.fromJson(message, MessageContent.class);
            if (messageContent == null) {
                log.error("CarPromotionChangeListener error. messageContent is null, message:{}", message);
                return;
            }
            MessageBody body = messageContent.getBody();
            if (body == null) {
                log.error("CarPromotionChangeListener error. body is null, message:{}", message);
                return;
            }
            Long sequenceId = body.getSequenceId();
            if (sequenceId == null || sequenceId <= 0) {
                log.error("CarPromotionChangeListener error. sequenceId is error, message:{}", message);
                return;
            }
            carPromotionInstancePool.updateCacheTask(sequenceId);
        } catch (Exception e) {
            log.error("CarPromotionChangeListener error. message:{}", message, e);
        }

    }

}
