package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.phoenix.api.dto.request.qualifycode.TradeInQualifyDetailReq;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.rpc.phoenix.SubsidyPhoenixProxy;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/21 15:36
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SubsidyPhoenixProvider implements ResourceProvider<SubsidyPhoenixProvider.ResContent> {
    private ResourceObject<SubsidyPhoenixProvider.ResContent> resourceObject;

    @Autowired
    private SubsidyPhoenixProxy subsidyPhoenixProxy;

    /**
     * 获取provider的资源
     *
     * @return 资源对象
     */
    @Override
    public ResourceObject<ResContent> getResource() {
        return resourceObject;
    }

    /**
     * 将对应资源初始化到provider，一个provider使用唯一一个资源对象
     *
     * @param object 资源对象
     */
    @Override
    public void initResource(ResourceObject<ResContent> object) {
        this.resourceObject = object;
    }

    /**
     * 锁定本资源时需要进行的操作
     *
     * @throws BizError 业务异常
     */
    @Override
    public void lock() throws BizError {
        log.info("lock SubsidyPhoenix begin, resource = {}", resourceObject);

        String orderId = resourceObject.getContent().getOrderId();
        Long userId = resourceObject.getContent().getUserId();
        List<TradeInQualifyDetailReq> qualifyDetailReqList = resourceObject.getContent().getQualifyDetailReqList();

        // 调用三方优惠，核销资格码
        String result = subsidyPhoenixProxy.writeOffByOrder(userId, orderId, qualifyDetailReqList);
        log.info("lock SubsidyPhoenix finished, resource = {}, result = {}", resourceObject, result);
    }

    /**
     * 资源对象需要进行消耗时进行的操作
     *
     * @throws BizError 业务异常
     */
    @Override
    public void consume() throws BizError {
        log.info("consume SubsidyPhoenix resource. {}", GsonUtil.toJson(resourceObject));
    }

    /**
     * 资源对象回滚时进行的操作
     *
     * @throws BizError 业务异常
     */
    @Override
    public void rollback() throws BizError {
        log.info("rollback SubsidyPhoenix begin, resource = {}", resourceObject);

        String orderId = resourceObject.getContent().getOrderId();
        Long userId = resourceObject.getContent().getUserId();
        List<TradeInQualifyDetailReq> qualifyDetailReqList = resourceObject.getContent().getQualifyDetailReqList();

        // 调用三方优惠，回滚资格码
        String result = subsidyPhoenixProxy.rollbackWriteOff(userId, orderId, qualifyDetailReqList);
        log.info("rollback SubsidyPhoenix finished, resource = {}, result = {}", resourceObject, result);
    }

    /**
     * 发生资源抢占冲突时（resourceId+resourceType冲突），给出的文案
     *
     * @return 文案
     */
    @Override
    public String conflictText() {
        return "处理换新补贴失败";
    }

    @Data
    public static class ResContent {
        /**
         * 订单号
         */
        private String orderId;

        /**
         * 用户账号
         */
        private Long userId;

        /**
         * 资格码核销/回滚接口入参
         */
        private List<TradeInQualifyDetailReq> qualifyDetailReqList;
    }

}
