package com.xiaomi.nr.promotion.mq.consumer;

import com.xiaomi.nr.promotion.constant.MqConstant;
import com.xiaomi.nr.promotion.mq.consumer.entity.OrderData;
import com.xiaomi.nr.promotion.mq.consumer.entity.OrderMessage;
import com.xiaomi.nr.promotion.mq.consumer.entity.OrderRefundBody;
import com.xiaomi.nr.promotion.mq.consumer.entity.OrderRefundMessage;
import com.xiaomi.nr.promotion.resource.impl.ResourceManager;
import com.xiaomi.nr.promotion.resource.model.ResourceManageContext;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 售后服务 - 工单退款消息
 *
 * <AUTHOR>
 * @date 2024/5/23
 */
@Slf4j
@Component
@ConditionalOnProperty(value="rocketmq.topic.order.afterSale.enabled", havingValue="true")
@RocketMQMessageListener(
        topic = "${rocketmq.topic.order.afterSale}",
        selectorExpression = "OrderClosed || OrderRefund",
        nameServer = "${rocketmq.order.name-server}",
        accessKey = "${rocketmq.access-key}",
        secretKey = "${rocketmq.secret-key}",
        consumerGroup = "${rocketmq.topic.order.afterSale.consumerGroup}")
public class OrderAfterSaleMessageListener implements RocketMQListener<String> {

    @Autowired
    private ResourceManager resourceManager;

    private final static String TAG_ORDER_REFUND = "orderRefund";

    @Override
    public void onMessage(String s) {
        long startTime = System.currentTimeMillis();
        log.info("OrderAfterSaleMessageListener receive startTime:{} msg:{}", startTime, s);
        try {
            OrderMessage orderMessage = GsonUtil.fromJson(s, OrderMessage.class);
            if (orderMessage == null) {
                log.error("OrderAfterSaleMessageListener data error. message:{}", s);
                return;
            }

            String event = orderMessage.getEvent();
            if (Objects.equals(MqConstant.TOPIC_ORDER_CLOSED, event)) {
                // 关单
                onHandleCloseMessage(orderMessage);
            } else if (Objects.equals(MqConstant.TOPIC_ORDER_REFUND, event)) {
                // 退款
                onHandleRefundMessage(s);
            }
        } catch (Throwable e) {
            log.error("OrderAfterSaleMessageListener error. message:{}", s, e);
            throw new RuntimeException(e);
        } finally {
            log.info("OrderAfterSaleMessageListener receive ws:{} msg:{}", System.currentTimeMillis() - startTime, s);
        }
    }

    /**
     * 关单处理逻辑
     * @param closeMessage 关单消息
     */
    private void onHandleCloseMessage(OrderMessage closeMessage) throws BizError {
        String orderIdStr = closeMessage.getOdOrderId();
        if (StringUtils.isEmpty(orderIdStr) || !StringUtils.isNumeric(orderIdStr)) {
            log.error("OrderAfterSaleMessageListener 关单订单号不符合要求. message:{} orderIdStr:{}", GsonUtil.toJson(closeMessage), orderIdStr);
            return;
        }

        long orderId = Long.parseLong(orderIdStr);
        if(orderId <= 0L) {
            log.error("OrderAfterSaleMessageListener 关单订单号不符合要求. message:{} orderIdStr:{}, orderId:{}", GsonUtil.toJson(closeMessage), orderIdStr, orderId);
            return;
        }

        ResourceManageContext context = ResourceManageContext.fromMessage(orderId);
        resourceManager.rollback(context);
    }

    /**
     * 目前只支持退无码券，以后有其他优惠且需要退还的，再加上
     *
     * @param s 工单退款消息
     */
    private void onHandleRefundMessage(String s) throws BizError {
        OrderRefundMessage refundMessage = GsonUtil.fromJson(s, OrderRefundMessage.class);
        if (refundMessage == null) {
            log.error("OrderAfterSaleMessageListener data error. message:{}", s);
            return;
        }

        String body = refundMessage.getBody();
        OrderRefundBody refundBody = GsonUtil.fromJson(body, OrderRefundBody.class);
        if (Objects.isNull(refundBody) || Objects.isNull(refundBody.getOrderData())) {
            log.warn("OrderAfterSaleMessageListener body is null . message:{}", GsonUtil.toJson(refundMessage));
            return;
        }

        OrderData orderData = refundBody.getOrderData();
        if (CollectionUtils.isEmpty(orderData.getOrderTags()) || !orderData.getOrderTags().contains(TAG_ORDER_REFUND)) {
            log.info("OrderAfterSaleMessageListener 非整单退款，不退优惠券 . message:{}", GsonUtil.toJson(refundMessage));
            return;
        }

        long orderId = Long.parseLong(orderData.getOrgOrderId());
        ResourceManageContext context = ResourceManageContext.fromMessage(orderId);
        context.setResourceTypes(List.of(ResourceType.COUPON, ResourceType.USER_JOIN_ACT_NUM, ResourceType.VID_JOIN_ACT_LIMIT));
        resourceManager.partRollback(context);
    }

}
