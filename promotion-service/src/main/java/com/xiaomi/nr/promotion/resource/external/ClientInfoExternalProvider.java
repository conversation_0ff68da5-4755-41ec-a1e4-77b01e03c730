package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.entity.redis.ClientStc;
import com.xiaomi.nr.promotion.domain.activity.service.common.ClientInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

/**
 * <AUTHOR>
 * @date 2021/5/17
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class ClientInfoExternalProvider extends ExternalDataProvider<ClientStc> {

    /**
     * Client信息数据
     */
    private ListenableFuture<ClientStc> future;

    @Autowired
    private ClientInfoService clientInfoService;

    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        Long clientId = request.getClientId();
        if (clientId != null && clientId != 0) {
            future = clientInfoService.getClientInfoAsync(clientId);
        }
    }

    @Override
    protected long getTimeoutMills() {
        return DEFAULT_TIMEOUT;
    }

    @Override
    public ListenableFuture<ClientStc> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.CLIENT_INFO;
    }
}
