package com.xiaomi.nr.promotion.bizplatform.impl;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.bizplatform.AbstractBaseBizPlatform;
import com.xiaomi.nr.promotion.domain.activity.impl.CarParallelExportActivityDomain;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.OrderTypeEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.nr.promotion.model.OrderPromotionDetailModel;
import com.xiaomi.nr.promotion.model.ProductShare;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.provider.CarOrderPromotionProvider;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class CarParallelBizPlatform extends AbstractBaseBizPlatform {

    @Autowired
    private ResourceProviderFactory resourceProviderFactory;

    private BaseDomainList baseDomainList;

    @Override
    public BaseDomainList checkBaseDomainList() {
        return baseDomainList;
    }

    @Override
    public BizPlatformEnum getBiz() {
        return BizPlatformEnum.CAR_PARALLEL;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        baseDomainList=super.instanceBaseDomainList();
        baseDomainList.addResourceList(CarParallelExportActivityDomain.class);
    }

    @Override
    public void generateResponse(DomainCheckoutContext domainCheckoutContext) throws BizError {
        super.originalGenerateResponse(domainCheckoutContext);
    }

    @Override
    public void addResource(DomainCheckoutContext domainCheckoutContext) throws BizError {
        CheckoutContext context = domainCheckoutContext.getContext();
        CheckoutPromotionRequest request = domainCheckoutContext.getRequest();
        List<ResourceProvider<?>> resourceProviders = initCarOrderPromotionResource(request);
        context.getResourceHandlers().addAll(resourceProviders);
    }

    private List<ResourceProvider<?>> initCarOrderPromotionResource(CheckoutPromotionRequest request) throws BizError {
        List<ResourceProvider<?>> providerList = new ArrayList<>();
        Long orderId = request.getOrderId();
        Long userId = request.getUserId();

        // 汽车订单优惠
        CarOrderPromotionProvider.OrderPromotionDetailResource carOrderBenefitResource=new CarOrderPromotionProvider.OrderPromotionDetailResource();
        carOrderBenefitResource.setOrderId(orderId);
        carOrderBenefitResource.setOrderScene(BizPlatformEnum.CAR_PARALLEL.getValue());
        carOrderBenefitResource.setOrderType(OrderTypeEnum.NORMAL_ORDER.getOrderType());
        carOrderBenefitResource.setUserId(userId);
        List<CarOrderPromotionProvider.OrderPromotionDetailPo> promotionDetailList=new ArrayList<>();
        carOrderBenefitResource.setPromotionDetailList(promotionDetailList);
        // 分摊明细, 以promotionId+promotionType维度聚合
        List<CartItem> cartList = request.getCartList();
        Map<String,OrderPromotionDetailModel> promotionDetailMap=new HashMap<>();
        for (CartItem cartItem:cartList){
            List<ReduceDetailItem> reduceItemList = cartItem.getReduceItemList();
            if (reduceItemList==null|| reduceItemList.isEmpty()){
                continue;
            }
            for (ReduceDetailItem reduceItem:cartItem.getReduceItemList()){
                String key=reduceItem.getPromotionId()+"-"+reduceItem.getPromotionType();
                OrderPromotionDetailModel promotionDetail=promotionDetailMap.getOrDefault(key,new OrderPromotionDetailModel());

                promotionDetail.setPromotionId(reduceItem.getPromotionId());
                promotionDetail.setPromotionType(reduceItem.getPromotionType());
                promotionDetail.setBudgetApplyNo(reduceItem.getBudgetApplyNo());
                promotionDetail.setLineNum(reduceItem.getLineNum());
                long reduce = promotionDetail.getReduce() == null ? 0 : promotionDetail.getReduce();
                if (reduceItem.getReduce()!=null){
                    promotionDetail.setReduce(reduce+reduceItem.getReduce());
                }
                // 产品维度分摊
                List<ProductShare> productShareList = promotionDetail.getProductShareList();
                if (productShareList==null){
                    productShareList=new ArrayList<>();
                }
                ProductShare productShare=new ProductShare();
                productShare.setProductId(cartItem.getSsuId());
                productShare.setCount(cartItem.getCount());
                productShare.setReduce(reduceItem.getReduce());
                productShareList.add(productShare);

                promotionDetail.setProductShareList(productShareList);
                promotionDetailMap.put(key,promotionDetail);
            }
        }
        promotionDetailMap.forEach((k,v)->{
            CarOrderPromotionProvider.OrderPromotionDetailPo po=new CarOrderPromotionProvider.OrderPromotionDetailPo();
            po.setPromotionId(v.getPromotionId());
            po.setPromotionType(v.getPromotionType());
            po.setPromotionDetail(GsonUtil.toJson(v));
            promotionDetailList.add(po);
        });
//        if (promotionDetailList.isEmpty()){
//            return providerList;
//        }
        // 空优惠也记录
        carOrderBenefitResource.setPromotionDetailList(promotionDetailList);
        ResourceObject<CarOrderPromotionProvider.OrderPromotionDetailResource> resourceObject = buildCarOrderBenefitResourceObject(orderId, userId, carOrderBenefitResource);
        CarOrderPromotionProvider benefitProvider = (CarOrderPromotionProvider) resourceProviderFactory.getProvider(ResourceType.CAR_ORDER_BENEFIT);
        benefitProvider.initResource(resourceObject);
        log.debug("car order benefit provider:{}", benefitProvider);
        providerList.add(benefitProvider);
        return providerList;
    }


    private ResourceObject<CarOrderPromotionProvider.OrderPromotionDetailResource> buildCarOrderBenefitResourceObject(Long orderId, Long userId, CarOrderPromotionProvider.OrderPromotionDetailResource orderBenefit) {
        ResourceObject<CarOrderPromotionProvider.OrderPromotionDetailResource> resourceObject = new ResourceObject<>();
        resourceObject.setContent(orderBenefit);
        resourceObject.setPid(0L);
        resourceObject.setPromotionId(orderId);
        resourceObject.setOrderId(orderId);
        resourceObject.setResourceType(ResourceType.CAR_ORDER_BENEFIT);
        resourceObject.setResourceId(String.format("%s_%s", orderId, userId));
        return resourceObject;
    }
}
