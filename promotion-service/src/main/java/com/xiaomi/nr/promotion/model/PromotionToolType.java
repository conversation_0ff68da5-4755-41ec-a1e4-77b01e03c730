package com.xiaomi.nr.promotion.model;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import com.xiaomi.nr.promotion.enums.PhoenixTypeEnum;

import java.util.List;

/**
 * 组件内使用的活动类型值，同时用来定义权重
 * 优惠券枚举值范围：2xx
 *
 * <AUTHOR>
 * @date 2021/3/16
 */
public enum PromotionToolType {
    /**
     * 直降
     */
    ONSALE(ActivityTypeEnum.ONSALE.getValue()),
    /**
     * 赠品活动
     */
    GIFT(ActivityTypeEnum.GIFT.getValue()),
    /**
     * 加价购活动
     */
    BARGAIN(ActivityTypeEnum.BARGAIN.getValue()),
    /**
     * 包邮活动
     */
    POST_FREE(ActivityTypeEnum.POST_FREE.getValue()),
    /**
     * 满减活动
     */
    REDUCE(ActivityTypeEnum.REDUCE.getValue()),
    /**
     * 范围立减活动
     */
    RANGE_REDUCE(ActivityTypeEnum.RANGE_REDUCE.getValue()),
    /**
     * 置换补贴
     */
    EXCHANGE_SUBSIDY(ActivityTypeEnum.EXCHANGE_SUBSIDY.getValue()),
    /**
     * 订单立减
     */
    ORDER_REDUCE(ActivityTypeEnum.ORDER_REDUCE.getValue()),
    /**
     * 折扣活动
     */
    DISCOUNT(ActivityTypeEnum.DISCOUNT.getValue()),
    /**
     * 买赠活动
     */
    BUY_GIFT(ActivityTypeEnum.BUY_GIFT.getValue()),
    /**
     * 门店价
     */
    STORE_PRICE(ActivityTypeEnum.STORE_PRICE.getValue()),
    /**
     * 换新立减
     */
    RENEW_REDUCE(ActivityTypeEnum.RENEW_REDUCE.getValue()),

    /**
     * 指定门店降价
     */
    PARTONSALE(ActivityTypeEnum.PARTONSALE.getValue()),
    /**
     * 下单立减
     */
    BUY_REDUCE(ActivityTypeEnum.BUY_REDUCE.getValue()),

    /**
     * 会员折扣
     */
    CAR_SHOP_VIP(ActivityTypeEnum.CAR_SHOP_VIP.getValue()),

    // ------------------------- 华丽分割线 售后活动 ---------------------
    /**
     * 维保折扣活动
     */
    MAINTENANCE_REPAIR_DISCOUNT(ActivityTypeEnum.MAINTENANCE_REPAIR_DISCOUNT.getValue()),

    /**
     * 工项免费活动
     */
    MAINTENANCE_ITEM_FREE(ActivityTypeEnum.MAINTENANCE_ITEM_FREE.getValue()),

    // ------------------------- 华丽分割线 团购活动 ---------------------
    /**
     * 团购价格折扣
     */
    B2T_VIP_DISCOUNT(ActivityTypeEnum.B2T_VIP_DISCOUNT.getValue()),
    /**
     * 阶梯价
     */
    B2T_STEP_PRICE(ActivityTypeEnum.B2T_STEP_PRICE.getValue()),
    /**
     * 提货底价
     */
    B2T_CHANNEL_PRICE(ActivityTypeEnum.B2T_CHANNEL_PRICE.getValue()),
    // ------------------------- 华丽分割线 券 ---------------------
    /**
     * 现金券
     */
    COUPON_CASH(CouponTypeEnum.CASH.getType()),
    /**
     * 折扣券
     */
    COUPON_DISCOUNT(CouponTypeEnum.DISCOUNT.getType()),
    /**
     * 抵扣券
     */
    COUPON_DEDUCT(CouponTypeEnum.DEDUCT.getType()),

    /**
     * 礼品兑换券
     */
    COUPON_GIFT(CouponTypeEnum.GIFT.getType()),

    // ------------------------- 华丽分割线 三方优惠 ---------------------
    /**
     * 华盛联通三方优惠
     */
    PHOENIX_HUASHENG(PhoenixTypeEnum.HUASHENG.getType()),
    /**
     * 三方优惠-联通入驻
     */
    PHOENIX_UNICOM(PhoenixTypeEnum.UNICOM.getType()),
    /**
     * 三方优惠-电信入驻
     */
    PHOENIX_TELECOM(PhoenixTypeEnum.TELECOM.getType()),
    /**
     * 三方优惠-移动入驻
     */
    PHOENIX_MOBILE(PhoenixTypeEnum.MOBILE.getType()),
    /**
     * 以旧换新使用
     */
    PHOENIX_RENEW(PhoenixTypeEnum.RENEW_DEDUCT.getType()),
    /**
     * "24年北京大家电以旧换新-购新补贴"
     */
    NEW_PURCHASE_SUBSIDY(ActivityTypeEnum.NEW_PURCHASE_SUBSIDY.getValue()),
    /**
     * "24年北京大家电以旧换新-换新补贴"
     */
    UPGRADE_PURCHASE_SUBSIDY(ActivityTypeEnum.UPGRADE_PURCHASE_SUBSIDY.getValue());


    private final int typeId;

    PromotionToolType(int typeId) {
        this.typeId = typeId;
    }

    public static final List<PromotionToolType> purchaseToolList = Lists.newArrayList(PromotionToolType.NEW_PURCHASE_SUBSIDY, PromotionToolType.UPGRADE_PURCHASE_SUBSIDY);
    
    /**
     * 根据类型ID 获取对象
     *
     * @param typeId 类型ID
     * @return PromotionToolType
     */
    public static PromotionToolType fromTypeId(int typeId) {
        for (PromotionToolType promotionToolType : PromotionToolType.values()) {
            if (promotionToolType.typeId == typeId) {
                return promotionToolType;
            }
        }
        return null;
    }

    /**
     * 是否支持价格保的类型
     *
     * @param toolType 类型
     * @return 是否支持
     */
    public static boolean isSupportProtectPrice(PromotionToolType toolType) {
        return toolType == PromotionToolType.ONSALE || toolType == PromotionToolType.REDUCE || toolType == PromotionToolType.DISCOUNT || toolType == PromotionToolType.BUY_REDUCE;
    }

    public int getTypeId() {
        return typeId;
    }
}
