package com.xiaomi.nr.promotion.schedule;

import com.xiaomi.hera.trace.annotation.Trace;
import com.xiaomi.nr.promotion.activity.pool.PurchaseActivityPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： Pancras
 * @date： 2024/9/18
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Component
@Slf4j
public class PurchaseActivityRefreshTask implements InitializingBean {
    
    @Autowired
    private PurchaseActivityPool purchaseActivityPool;
    
    
    @Trace
    @Scheduled(fixedDelay = 1000 * 10, initialDelay = 1000 * 30)
    public void refreshPurchaseCache() {
        long startTime = System.currentTimeMillis();
        try {
            purchaseActivityPool.refreshPurchaseCache();
            log.info("refreshPurchaseCache success ws={}", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("refreshPurchaseCache err:{}", e.getMessage());
        }
    }
    
    @Override
    public void afterPropertiesSet() throws Exception {
        purchaseActivityPool.refreshPurchaseCache();
    }
}
