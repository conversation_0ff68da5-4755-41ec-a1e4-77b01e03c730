package com.xiaomi.nr.promotion.schedule;

import com.xiaomi.hera.trace.annotation.Trace;
import com.xiaomi.nr.promotion.activity.pool.PromotionInstancePool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 活动缓存定时刷新任务
 *
 * <AUTHOR>
 * @date 2021/5/17
 */
@Component
@Slf4j
@ConditionalOnExpression(value = "'${biz.area.id.list}'.contains('MISHOP')")
public class ActivityCacheRefreshTask {

    @Autowired(required = false)
    private PromotionInstancePool promotionInstancePool;

    /**
     * 重新构建活动缓存，延迟1分钟后每1分钟构建一次
     */
    @Trace
    @Scheduled(fixedDelay = 1000 * 60, initialDelay = 1000 * 60)
    public void rebuildActPoolCache() {
        long startTime = System.currentTimeMillis();
        try {
            promotionInstancePool.rebuildCacheTask();
            log.info("rebuildActPoolCache success ws={}", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("rebuildActPoolCache err:{}", e.getMessage());
        }
    }
}
