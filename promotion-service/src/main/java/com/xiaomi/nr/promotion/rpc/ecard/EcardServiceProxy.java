package com.xiaomi.nr.promotion.rpc.ecard;

import com.xiaomi.nr.ecard.api.dto.recycle.ReturnEcardNotifyMarioRequest;
import com.xiaomi.nr.ecard.api.service.EcardDubboService;
import com.xiaomi.nr.promotion.config.yaml.PromotionTotalZkConfig;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 礼品卡服务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class EcardServiceProxy {
    @Reference(interfaceClass = EcardDubboService.class, version = "${ecard.dubbo.version}", timeout = 2000, group = "${ecard.dubbo.group}")
    private EcardDubboService ecardDubboService;

    @Autowired
    private PromotionTotalZkConfig promotionTotalZkConfig;

    /**
     * 退还礼品卡（信用换新）消息推mario
     *
     * @param uid      用户id
     * @param orderIds 订单号列表
     */
    public void sendEcardMsgToMario(Long uid, List<Long> orderIds) {
        // 如果服务降级
        if (promotionTotalZkConfig.isEcardReqFallback()) {
            log.info("service ecard fallback switch on. ");
            return;
        }

        ReturnEcardNotifyMarioRequest request = new ReturnEcardNotifyMarioRequest();
        request.setUid(uid);
        request.setOrderIds(orderIds);
        Result<Void> result;

        long startTime = System.currentTimeMillis();
        try {
            result = ecardDubboService.orderCloseCreditRecycleNotifyMario(request);
        } catch (Exception e) {
            log.error("invoke rpc orderCloseCreditRecycleNotifyMario error. request:{}, err", request, e);
            return;
        }

        // 处理响应
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc orderCloseCreditRecycleNotifyMario succ. request:{}, result:{}, ws:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), System.currentTimeMillis() - startTime);
            return;
        }
        log.info("invoke rpc orderCloseCreditRecycleNotifyMario fail. request:{}, code:{} message:{}, ws:{}", request, result.getCode(), result.getMessage(), System.currentTimeMillis() - startTime);
    }
}
