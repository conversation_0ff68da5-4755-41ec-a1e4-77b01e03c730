package com.xiaomi.nr.promotion.domain.coupon.service.base.condition;

import com.xiaomi.nr.promotion.domain.coupon.model.CouponRangeGoodsDO;
import com.xiaomi.nr.promotion.model.common.ValidGoods;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/3/11
 */
public class HalfDeductCondition implements CouponCondition{
    
    @Override
    public boolean isSatisfied(Map<Long, ValidGoods> validGoodsMap,
            List<CouponRangeGoodsDO> couponRangeGoodsList) {
        return false;
    }
}
