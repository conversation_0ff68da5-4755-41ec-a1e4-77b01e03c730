package com.xiaomi.nr.promotion.componet.action;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.enums.ActFrequencyEnum;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.DiscountPromotionConfig;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.IdKeyHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 折扣活动：计算扣减金额和分摊
 *
 * <AUTHOR>
 * @date 2021/4/9
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class DiscountAction extends AbstractAction {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 频次限制 1不限制 2整个活动一次 3每天一次
     */
    private ActFrequencyEnum frequency;
    /**
     * 活动总数
     */
    private long actLimitNum;
    /**
     * 活动限制. 数据值：0-不限制， 没有限制就没有对应字段
     */
    private ActNumLimitRule numLimitRule;

    @Autowired
    private CheckoutCartTool checkoutCartTool;

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        QuotaLevel level = context.getQuotaLevel();
        List<GoodsIndex> indexList = context.getGoodIndex();
        if (CollectionUtils.isEmpty(indexList) || level == null) {
            log.error("discount context indexList is empty or level is null. actId:{}, uid:{} indexList:{}", promotionId, request.getUserId(), indexList);
            return;
        }
        List<CartItem> cartList = request.getCartList();
        ValidGoods validGoods = CartHelper.buildValidGoods(cartList, indexList);

        // 计算实际总折扣金额
        long reduceDiscount = level.getReduceDiscount();
        long maxReducePrice = level.getMaxReducePrice();
        long realTotalReduceMoney = calculateDiscountMoney(validGoods.getValidPrice(), reduceDiscount, maxReducePrice);

        // 处理分摊，分摊购物车减免金额
        String idKey = IdKeyHelper.getGeneralActIdKey(promotionId);
        List<Integer> cartIndexList = getCartIndexList(indexList);
        checkoutCartTool.divideCartsReduce(realTotalReduceMoney, cartIndexList, cartList, idKey, ActivityTypeEnum.DISCOUNT.getValue(), promotionId);

        // 组织优惠信息promotionInfo，填充到context
        setResult(context, cartList, promotion);

        // 初始化资源
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            initResource(request, promotionId, 1, context, actLimitNum, frequency, numLimitRule);
        }
    }

    private long calculateDiscountMoney(long totalPrice, long reduceDiscount, long maxReducePrice) {
        //打折后的价格
        long newPrice = totalPrice * reduceDiscount / 100;
        long reduceMoney = totalPrice - newPrice;
        reduceMoney = Math.min(reduceMoney, totalPrice);

        //最高可以减免的钱
        if (maxReducePrice > 0) {
            reduceMoney = Math.min(maxReducePrice, reduceMoney);
        }
        return reduceMoney;
    }

    private void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool) throws BizError {
        List<GoodsIndex> indexList = context.getGoodIndex();
        List<String> joinedItemIdList = CartHelper.getJoinedItemIdList(indexList, cartList, promotionId);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setParentItemId(CartHelper.getParentItemIdList(indexList));
        promotionInfo.setJoinedItemId(joinedItemIdList);
        promotionInfo.setExtend(Strings.EMPTY);
        promotionInfo.setJoined(CollectionUtils.isNotEmpty(joinedItemIdList) ? 1 : 0);

        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof DiscountPromotionConfig)) {
            return;
        }
        DiscountPromotionConfig promotionConfig = (DiscountPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.frequency = promotionConfig.getFrequency();
        this.actLimitNum = promotionConfig.getActLimitNum();
        this.numLimitRule = promotionConfig.getNumLimitRule();
    }
}
