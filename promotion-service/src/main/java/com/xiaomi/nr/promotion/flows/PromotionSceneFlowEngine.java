package com.xiaomi.nr.promotion.flows;

import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.ScopeTypeEnum;
import com.xiaomi.nr.promotion.api.dto.enums.GoodsTypeEnum;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.xiaomi.nr.promotion.model.PromotionToolType.*;

/**
 * <AUTHOR>
 * @date 2023/2/22
 */
@Component
public class PromotionSceneFlowEngine {

    /**
     * 确定flow
     *
     * @param toolList   活动工具
     * @param channel    渠道
     * @param goodsType  商品类型
     * @param department 部门
     * @return 能参加的活动
     */
    public List<ActivityTool> filterSceneAct(List<ActivityTool> toolList, Integer channel, Integer goodsType, Integer department) {
        // 确定场景
        SceneEnum scene = getScene(channel, goodsType, department);
        // 执行类型过滤
        toolList = toolList.stream()
                .filter(tool -> scene.toolTypes.contains(tool.getType())).collect(Collectors.toList());
        // 排序
        toolList.sort(Comparator.comparing(config ->
                Optional.ofNullable(ActivityTypeEnum.getByValue(config.getType().getTypeId()))
                        .map(ActivityTypeEnum::getWeight).orElse(0)));
        Collections.reverse(toolList);

        // 如果叠加，则直接返回
        if (RelationEnum.AND == scene.relation) {
            return toolList;
        }
        // 确定优先级类型
        Set<PromotionToolType> toolTypes = toolList.stream().map(ActivityTool::getType).collect(Collectors.toSet());
        PromotionToolType finalType = null;
        for (PromotionToolType toolType : scene.toolTypes) {
            if (toolTypes.contains(toolType)) {
                finalType = toolType;
                break;
            }
        }
        PromotionToolType finalType2 = finalType;
        toolList = toolList.stream()
                .filter(tool -> Objects.equals(tool.getType(), finalType2)).collect(Collectors.toList());
        return toolList;
    }

    private SceneEnum getScene(Integer channel, Integer goodsType, Integer department) {
        if (!Objects.equals(ChannelEnum.B2T_C_CUSTOMER.getValue(), channel)
                && !Objects.equals(ChannelEnum.B2T_GOV_BIG_CUSTOMER.getValue(), channel)
                && !Objects.equals(ChannelEnum.B2T_MIJIA_BIG_CUSTOMER.getValue(), channel)) {
            return SceneEnum.NORM;
        }

        // 经销商米家& 销一
        if (Objects.equals(ChannelEnum.B2T_MIJIA_BIG_CUSTOMER.getValue(), channel)
                && Objects.equals(ScopeTypeEnum.ProductDepartment.S1.value, department)) {
            return SceneEnum.BIG_MIJIA_S1;
        }
        // 经销商米家& 销二
        if (Objects.equals(ChannelEnum.B2T_MIJIA_BIG_CUSTOMER.getValue(), channel)
                && Objects.equals(ScopeTypeEnum.ProductDepartment.S2.value, department)) {
            return SceneEnum.BIG_MIJIA_S2;
        }
        // 经销商米家& 销三
        if (Objects.equals(ChannelEnum.B2T_MIJIA_BIG_CUSTOMER.getValue(), channel)
                && Objects.equals(ScopeTypeEnum.ProductDepartment.S3.value, department)) {
            return SceneEnum.BIG_MIJIA_S3;
        }

        // 经销商政企& 销一
        if (Objects.equals(com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum.B2T_GOV_BIG_CUSTOMER.getValue(), channel)
                && Objects.equals(ScopeTypeEnum.ProductDepartment.S1.value, department)) {
            return SceneEnum.BIG_C_S1;
        }
        // 经销商政企& 销二
        if (Objects.equals(ChannelEnum.B2T_GOV_BIG_CUSTOMER.getValue(), channel)
                && Objects.equals(ScopeTypeEnum.ProductDepartment.S2.value, department)) {
            return SceneEnum.BIG_C_S2;
        }
        // 经销商政企& 销三
        if (Objects.equals(ChannelEnum.B2T_GOV_BIG_CUSTOMER.getValue(), channel)
                && Objects.equals(ScopeTypeEnum.ProductDepartment.S3.value, department)) {
            return SceneEnum.BIG_C_S3;
        }

        // 直客 & 销一
        if (Objects.equals(ChannelEnum.B2T_C_CUSTOMER.getValue(), channel)
                && Objects.equals(ScopeTypeEnum.ProductDepartment.S1.value, department)) {
            return SceneEnum.C_S1;
        }
        // 直客 & 销二
        if (Objects.equals(ChannelEnum.B2T_C_CUSTOMER.getValue(), channel)
                && Objects.equals(ScopeTypeEnum.ProductDepartment.S2.value, department)) {
            return SceneEnum.C_S2;
        }
        // 直客 & 销三 && 常规品
        if (Objects.equals(ChannelEnum.B2T_C_CUSTOMER.getValue(), channel)
                && Objects.equals(ScopeTypeEnum.ProductDepartment.S3.value, department)
                && Objects.equals(GoodsTypeEnum.NORM.getValue(), goodsType)) {
            return SceneEnum.C_S3_NORM;
        }
        // 直客 & 销三 && 主推品
        if (Objects.equals(ChannelEnum.B2T_C_CUSTOMER.getValue(), channel)
                && Objects.equals(ScopeTypeEnum.ProductDepartment.S3.value, department)
                && Objects.equals(GoodsTypeEnum.RECOM.getValue(), goodsType)) {
            return SceneEnum.C_S3;
        }
        return SceneEnum.NORM;
    }

    public enum RelationEnum {
        /**
         * 关系：互斥
         */
        OR(),
        /**
         * 叠加
         */
        AND();

        RelationEnum() {
        }
    }

    public enum SceneEnum {
        /**
         * 经销商-政企&销一
         */
        BIG_C_S1(Arrays.asList(B2T_CHANNEL_PRICE, ONSALE), RelationEnum.OR),
        /**
         * 经销商-政企&销二
         */
        BIG_C_S2(Arrays.asList(B2T_STEP_PRICE, ONSALE), RelationEnum.OR),
        /**
         * 经销商-政企&销三
         */
        BIG_C_S3(Arrays.asList(B2T_CHANNEL_PRICE, ONSALE), RelationEnum.OR),

        /**
         * 经销商-米家&销一
         */
        BIG_MIJIA_S1(Arrays.asList(ONSALE), RelationEnum.AND),
        /**
         * 经销商-米家&销二
         */
        BIG_MIJIA_S2(Arrays.asList(ONSALE), RelationEnum.AND),
        /**
         * 经销商-米家&销一
         */
        BIG_MIJIA_S3(Arrays.asList(B2T_VIP_DISCOUNT, ONSALE), RelationEnum.AND),
        /**
         * 直客&销一
         */
        C_S1(Arrays.asList(ONSALE, B2T_VIP_DISCOUNT), RelationEnum.AND),
        /**
         * 直客&销二
         */
        C_S2(Arrays.asList(B2T_STEP_PRICE, ONSALE), RelationEnum.AND),
        /**
         * 直客&销三&主推
         */
        C_S3(Arrays.asList(B2T_CHANNEL_PRICE, ONSALE), RelationEnum.OR),
        /**
         * 直客&销三&常规
         */
        C_S3_NORM(Arrays.asList(ONSALE, B2T_VIP_DISCOUNT), RelationEnum.AND),
        /**
         * 通用
         */
        NORM(Arrays.asList(STORE_PRICE, ONSALE), RelationEnum.OR);

        /**
         * 优惠类型
         */
        private final List<PromotionToolType> toolTypes;
        /**
         * 关系
         */
        private final RelationEnum relation;

        SceneEnum(List<PromotionToolType> toolTypes, RelationEnum relation) {
            this.toolTypes = toolTypes;
            this.relation = relation;
        }
    }
}
