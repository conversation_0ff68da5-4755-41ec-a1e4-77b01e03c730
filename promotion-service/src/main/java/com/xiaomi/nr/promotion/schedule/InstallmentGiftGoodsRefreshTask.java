package com.xiaomi.nr.promotion.schedule;

import com.xiaomi.hera.trace.annotation.Trace;
import com.xiaomi.nr.promotion.annotation.load.PromotionCondition;
import com.xiaomi.nr.promotion.annotation.load.PromotionLoadConstants;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.xiaomi.nr.promotion.activity.pool.InstallmentGiftGoodsPool;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
@PromotionCondition(value = {PromotionLoadConstants.MI_SHOP_BIZ})
public class InstallmentGiftGoodsRefreshTask implements InitializingBean {

    @Autowired
    private InstallmentGiftGoodsPool installmentGiftGoodsPool;

    @Trace
    @Scheduled(fixedDelay = 1000 * 60, initialDelay = 1000 * 60)
    public void reBuildValidProductCache() {
        long startTime = System.currentTimeMillis();
        try {
            installmentGiftGoodsPool.reBuildValidProductCache();
            log.info("reBuildValidProductCache success ws={}", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("reBuildValidProductCache err:{}", e.getMessage());
        }
    }


	@Override
	public void afterPropertiesSet() throws Exception {
		installmentGiftGoodsPool.reBuildValidProductCache();
	}
}
