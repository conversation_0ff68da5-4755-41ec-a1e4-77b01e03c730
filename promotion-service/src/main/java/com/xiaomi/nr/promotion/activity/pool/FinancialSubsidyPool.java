package com.xiaomi.nr.promotion.activity.pool;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.xiaomi.nr.promotion.api.dto.model.SubsidyDTO;
import com.xiaomi.nr.promotion.dao.mysql.mdpromotion.TInstallmentGiftGoodsMapper;
import com.xiaomi.nr.promotion.dao.mysql.mdpromotion.TInstallmentGiftGoodsPo;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.NrActGoodsMapper;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.NrActScopeMapper;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.NrActivityMapper;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.FinancialSubsidyExtInfo;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.NrActGoodsPo;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.NrActivityPo;
import com.xiaomi.nr.promotion.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FinancialSubsidyPool {

    /**
     * 本地锁
     */
    private final Lock lock = new ReentrantLock();
    private Map<Long, List<Long>> SSU_ACTIVITY_CACHE_MAP = new ConcurrentHashMap<>();
    private Map<Long, SubsidyDTO> ACTIVITY_DTO_CACHE_MAP = new ConcurrentHashMap<>();

    // 金融贴息活动类型
    public static final Integer type = 12;

    @Resource
    private NrActivityMapper nrActivityMapper;

    @Resource
    private NrActGoodsMapper nrActGoodsMapper;


    public void reBuildValidSubsidyCache() {
        Map<Long, List<Long>> ssuActivityMap = new ConcurrentHashMap<>();
        Map<Long, SubsidyDTO> activityDTOMap = new ConcurrentHashMap<>();
        long nowTime = System.currentTimeMillis() / 1000;
        List<NrActivityPo> nrActivityPoList = nrActivityMapper.queryValidListByType(type,nowTime);
        log.info("reBuildValidSubsidyCache new size={}, old size={}", nrActivityPoList.size(), ACTIVITY_DTO_CACHE_MAP.size());
        if (CollectionUtils.isEmpty(nrActivityPoList)) {
            log.warn("reBuildValidSubsidyCache, act is empty, type:{}, nowTime:{}", type, nowTime);
            // 更新缓存
            rebuildMapCache(activityDTOMap, ssuActivityMap);
            return;
        }

        //构建商品对应的缓存
        List<Long> actIds = nrActivityPoList.stream().map(NrActivityPo::getId).collect(Collectors.toList());

        // 商品列表
        List<NrActGoodsPo> nrActGoodsPos = nrActGoodsMapper.queryListByType(actIds);
        if (CollectionUtils.isEmpty(nrActGoodsPos)) {
            log.warn("reBuildValidSubsidyCache, goods is empty, actIds:{}", actIds);
            // 更新缓存
            rebuildMapCache(activityDTOMap, ssuActivityMap);
            return;
        }
        log.info("reBuildValidSubsidyCache, queryListByType, nrActGoodsPos:{}", GsonUtil.toJson(nrActGoodsPos));

        // 商品map
        ssuActivityMap = nrActGoodsPos.stream().collect(Collectors.groupingBy(
                NrActGoodsPo::getProductId, // key: productId
                        Collectors.mapping(NrActGoodsPo::getActivityId, Collectors.toList()) // value: activityId
                ));
        log.info("reBuildValidSubsidyCache, buildSsuActivityMap, ssuActivityMap:{}", GsonUtil.toJson(ssuActivityMap));

        //可以过滤一下没有商品的活动，因为为了确保接口稳定，异常商品数据被排除，如果没有商品的活动也需要排除

        // 活动map
        Map<Long, SubsidyDTO> actMap = nrActivityPoList.stream().map(FinancialSubsidyPool::convertPo2DTO).collect(Collectors.toMap(SubsidyDTO::getId, Function.identity(), (x, y) -> x));
        log.info("reBuildValidSubsidyCache, buildActMapp, actMap:{}", GsonUtil.toJson(actMap));

        rebuildMapCache(actMap,ssuActivityMap);

    }

    public static SubsidyDTO convertPo2DTO(NrActivityPo nrActivityPo){
        log.info("reBuildValidSubsidyCache, reBuildValidSubsidyCache.convertPo2DTO, nrActivityPo:{}", GsonUtil.toJson(nrActivityPo));
        SubsidyDTO subsidyDTO = new SubsidyDTO();
        subsidyDTO.setId(nrActivityPo.getId());
        subsidyDTO.setName(nrActivityPo.getName());
        subsidyDTO.setChannels(Arrays.stream(nrActivityPo.getChannel().split(","))
                .map(String::trim)
                .filter(NumberUtils::isParsable)
                .map(s -> Integer.parseInt(s.trim()))
                .collect(Collectors.toList()));
        subsidyDTO.setStartTime(nrActivityPo.getStartTime());
        subsidyDTO.setEndTime(nrActivityPo.getEndTime());
        subsidyDTO.setStatus(nrActivityPo.getStatus());
        subsidyDTO.setTypeId(nrActivityPo.getTypeId());
        // 活动扩展信息
        FinancialSubsidyExtInfo ext = GsonUtil.fromJson(nrActivityPo.getExtInfo(), FinancialSubsidyExtInfo.class);
        log.info("reBuildValidSubsidyCache, reBuildValidSubsidyCache.convertPo2DTO, ext:{}", GsonUtil.toJson(ext));
        if(Objects.isNull(ext)){
            return subsidyDTO;
        }
        subsidyDTO.setBudgetApplyNo(ext.getBudgetApplyNo());
        subsidyDTO.setLineNum(ext.getLineNum());
        subsidyDTO.setOrderStartTime(ext.getOrderStartTime());
        subsidyDTO.setOrderEndTime(ext.getOrderEndTime());
        subsidyDTO.setInterestBearer(ext.getInterestBearer());
        subsidyDTO.setUserScope(ext.getUserScope());
        log.info("reBuildValidSubsidyCache, reBuildValidSubsidyCache.convertPo2DTO, subsidyDTO:{}", GsonUtil.toJson(subsidyDTO));
        return subsidyDTO;
    }

    /**
     * 重置本地map缓存
     *
     * @param actInfoMap    活动Id - 具体活动信息
     * @param goodsActIdMap 活动ssu - 活动id信息
     */
    private void rebuildMapCache(Map<Long, SubsidyDTO> actInfoMap,  Map<Long, List<Long>> goodsActIdMap) {
        log.info("reBuildValidSubsidyCache, rebuildMapCache.args, actInfoMap:{}, goodsActIdMap:{}", GsonUtil.toJson(actInfoMap), GsonUtil.toJson(goodsActIdMap));
        try {
            lock.lock();
            log.info("reBuildValidSubsidyCache, rebuildMapCache.beforeChange, SSU_ACTIVITY_CACHE_MAP:{}, ACTIVITY_DTO_CACHE_MAP：{}", GsonUtil.toJson(SSU_ACTIVITY_CACHE_MAP), GsonUtil.toJson(ACTIVITY_DTO_CACHE_MAP));
            SSU_ACTIVITY_CACHE_MAP = goodsActIdMap;
            ACTIVITY_DTO_CACHE_MAP = actInfoMap;
            log.info("reBuildValidSubsidyCache, rebuildMapCache.endChange, SSU_ACTIVITY_CACHE_MAP:{}, ACTIVITY_DTO_CACHE_MAP:{}", GsonUtil.toJson(SSU_ACTIVITY_CACHE_MAP), GsonUtil.toJson(ACTIVITY_DTO_CACHE_MAP));
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取ssuId可参与的活动列表
     *
     * @param ssuId 车版本号
     * @return List
     */
    public List<Long> getSsuCanJoinActId(Long ssuId) {
        if (Objects.isNull(ssuId)) {
            return Collections.emptyList();
        }

        return SSU_ACTIVITY_CACHE_MAP.getOrDefault(ssuId, Collections.emptyList());
    }
    /**
     * 根据actId获取活动详情
     *
     * @param actId 活动id
     * @return SubsidyDTO
     */
    public SubsidyDTO getActDTOByActId(Long actId) {
        return ACTIVITY_DTO_CACHE_MAP.get(actId);
    }
}
