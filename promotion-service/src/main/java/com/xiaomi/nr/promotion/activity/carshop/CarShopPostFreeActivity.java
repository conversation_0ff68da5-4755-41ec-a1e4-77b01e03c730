package com.xiaomi.nr.promotion.activity.carshop;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.activity.AbstractActivityTool;
import com.xiaomi.nr.promotion.api.dto.MultiProductGoodsActRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.componet.action.carshop.CarShopPostFreeAction;
import com.xiaomi.nr.promotion.componet.condition.carshop.CarShopPostFreeCondition;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.ProductDetailContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.PostFreePromotionConfig;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @author: zhangliwei6
 * @date: 2025/5/8 19:37
 * @description:
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class CarShopPostFreeActivity extends AbstractActivityTool implements ActivityTool {

    private String descRuleIndex;

    /**
     * 参与商品信息
     */
    private Set<Long> ssuIds;

    /**
     * 条件阶梯
     */
    private List<QuotaLevel> levelList;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine.condition(CarShopPostFreeCondition.class).action(CarShopPostFreeAction.class);
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof PostFreePromotionConfig promotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        this.descRuleIndex = promotionConfig.getDescRuleIndex();
        if (promotionConfig.getJoinGoods() == null) {
            log.error("CarShopPostFreeActivity load failed, goods is empty");
            return false;
        }
        List<Long> ssuIdList = Optional.ofNullable(promotionConfig.getJoinGoods())
                .map(CompareItem::getSsuId)
                .orElse(Collections.emptyList());
        this.ssuIds = new HashSet<>(ssuIdList);
        this.levelList = promotionConfig.getLevelList();
        return true;
    }

    @Override
    public ActivityDetail getActivityDetail() {
        ActivityDetail activityDetail = getBasicActivityDetail();
        Policy policy = new Policy();
        policy.setQuotas(Lists.newArrayList(buildQuotaEle()));
        activityDetail.setPolicys(Lists.newArrayList(policy));
        return activityDetail;
    }

    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) throws BizError {
        PromotionInfo promotionInfo = buildDefaultPromotionInfo(context);
        promotionInfo.setDescRuleIndex(descRuleIndex);
        return promotionInfo;
    }

    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        return Map.of();
    }

    @Override
    public PromotionInfoDTO getMultiProductAct(MultiGoodItem goodItem, MultiProductGoodsActRequest request, ProductDetailContext context) throws BizError {
        if (checkProduct(goodItem)) {
            PromotionInfoDTO promotionInfo = initPromotionInfo();
            promotionInfo.setPromotionText(this.descRuleIndex);
            PolicyNew policyNew = new PolicyNew();
            promotionInfo.setPolicyNew(policyNew);

            PolicyNewLevel policyNewLevel = new PolicyNewLevel();
            policyNew.setPolicy(Lists.newArrayList(policyNewLevel));

            PolicyNewFillGoodsGroup policyNewFillGoodsGroup = new PolicyNewFillGoodsGroup();
            policyNewFillGoodsGroup.setQuota(buildQuotaEle());
            policyNewLevel.setIncludedGoodsGroup(Lists.newArrayList(policyNewFillGoodsGroup));
            return promotionInfo;
        }
        return null;
    }

    private boolean checkProduct(MultiGoodItem goodItem) {
        return ssuIds.contains(goodItem.getSsuId());
    }

    private QuotaEle buildQuotaEle() {
        com.xiaomi.nr.promotion.entity.redis.QuotaEle quotaEleConfig = levelList.getFirst().getQuotas().getFirst();
        QuotaEle quotaEle = new QuotaEle();
        quotaEle.setType(quotaEleConfig.getType());
        quotaEle.setCount(quotaEleConfig.getCount());
        return quotaEle;
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.POST_FREE;
    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR_SHOP;
    }
}
