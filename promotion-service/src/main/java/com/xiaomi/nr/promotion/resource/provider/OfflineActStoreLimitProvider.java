package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.disruptor.ActStockActionEnum;
import com.xiaomi.nr.promotion.disruptor.ActStockManager;
import com.xiaomi.nr.promotion.disruptor.StockRecord;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 线下每个门店参与活动次数
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OfflineActStoreLimitProvider implements ResourceProvider<OfflineActStoreLimitProvider.ActStoreLimit> {
    /**
     * 线上用户参与活动记录
     */
    private ResourceObject<OfflineActStoreLimitProvider.ActStoreLimit> resourceObject;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Autowired
    private ActStockManager actStockManager;

    @Autowired
    private NacosConfig nacosConfig;

    @Override
    public ResourceObject<OfflineActStoreLimitProvider.ActStoreLimit> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<OfflineActStoreLimitProvider.ActStoreLimit> object) {
        this.resourceObject = object;
    }

    /**
     * 扣减库存
     */
    @Override
    public void lock() throws BizError {
        log.info("lock act store limit resource. {}", resourceObject);
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("OfflineActStoreLimitProvider.lock(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        activityRedisDao.incrActStoreLimitNum(resourceObject.getContent().getActId(),
                resourceObject.getContent().getOrgCode(),
                resourceObject.getContent().getCount(),
                resourceObject.getContent().getLimitNum());
        log.info("lock act store limit resource ok. {}", resourceObject);
        actStockManager.publishEvent(() -> stockManage(ActStockActionEnum.COMMIT));
    }

    @Override
    public void consume() {
        log.info("consume act store limit resource. {}", resourceObject);
    }

    @Override
    public void rollback() throws BizError {
        log.info("rollback act store limit resource. {}", resourceObject);
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("OfflineActStoreLimitProvider.rollback(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        activityRedisDao.decrActStoreLimitNum(resourceObject.getContent().getActId(),
                resourceObject.getContent().getOrgCode(),
                resourceObject.getContent().getCount());
        log.info("rollback act store limit resource ok. {}", resourceObject);
        actStockManager.publishEvent(() -> stockManage(ActStockActionEnum.ROLLBACK));
    }

    @Override
    public String conflictText() {
        return "减限购失败";
    }

    private StockRecord stockManage(ActStockActionEnum actionEnum) {
        Long actId = resourceObject.getContent().getActId();
        String orgCode =  resourceObject.getContent().getOrgCode();
        Long actLimitNum = resourceObject.getContent().getLimitNum();
        Integer limitNum = activityRedisDao.getActStoreLimitNum(actId, orgCode);
        if (actLimitNum == null || actLimitNum == 0L ||
                (ActStockActionEnum.COMMIT == actionEnum && limitNum < actLimitNum)) {
            return null;
        }
        return new StockRecord().setActivityId(actId)
                .setResourceType(getResource().getResourceType().getValue())
                .setAction(actionEnum.getCode())
                .setOrgCode(orgCode)
                .setDateTimeMills(System.currentTimeMillis());
    }

    /**
     * 活动记录
     */
    @Data
    public static class ActStoreLimit {
        /**
         * 门店Code
         */
        private String orgCode;
        /**
         * 活动ID
         */
        private Long actId;
        /**
         * 时间(毫秒）
         */
        private Integer count;
        /**
         * 过期时间 (秒）
         */
        private Long limitNum;
    }
}
