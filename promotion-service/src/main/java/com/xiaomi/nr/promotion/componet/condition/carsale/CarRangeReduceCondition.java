package com.xiaomi.nr.promotion.componet.condition.carsale;

import com.google.common.collect.Lists;
import com.xiaomi.nr.md.promotion.admin.api.constant.TradeType;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.SubmitTypeEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.CarRangeReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.BenefitInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 范围立减条件判断
 *
 * <AUTHOR>
 * @date 2023/11/26 17:51
 **/
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarRangeReduceCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;

    /**
     * 活动类型
     */
    private PromotionToolType promotionType;

    /**
     * 该活动所属车型
     */
    private List<Long> belongCarEdition;

    /**
     * 交易类型
     */
    protected Integer tradeType;

    /**
     * 权益信息
     */
    protected BenefitInfo benefitInfo;


    /**
     * 检查交易类型是否符合条件
     *
     * @param request 结账促销请求对象
     * @return 如果交易类型不符合条件则返回true，否则返回false
     */
    private boolean tradeTypeMatchCheck(CheckoutPromotionRequest request) {

        Long orderTime = request.getOrderTime();

        int submitType = Optional.ofNullable(request.getSubmitType()).orElse(TradeType.NORMAL.getCode());

        if (SubmitTypeEnum.REPLACE.getCode() == tradeType) {
            if (SubmitTypeEnum.REPLACE.getCode() != submitType) {
                return false;
            }
            if (orderTime == null) {
                log.error("CarRangeReduceCondition orderTime is null. actId:{} oid:{}, orderTime:{}", promotionId, request.getOrderId(), orderTime);
                return false;
            }
            if (benefitInfo.getStartTime() > orderTime || benefitInfo.getEndTime() < orderTime) {
                log.error("CarRangeReduceCondition orderTime check is false. actId:{} oid:{}, orderTime:{}", promotionId, request.getOrderId(), orderTime);
                return false;
            }
        } else {
            if (SubmitTypeEnum.REPLACE.getCode() == submitType && orderTime != null) {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (CollectionUtils.isEmpty(belongCarEdition)) {
            log.error("belongCarEdition is empty. actId:{} uid:{}", promotionId, request.getUserId());
            return false;
        }

        // 交易类型校验
        if (!tradeTypeMatchCheck(request)) {
            return false;
        }

        List<CartItem> cartList = request.getCartList();
        // 筛选商品数据
        List<GoodsIndex> indexList = goodsActMatch(cartList);

        // 没有符合的商品，不满足活动
        if (CollectionUtils.isEmpty(indexList)) {
            return false;
        }
        context.setGoodIndex(indexList);

        return true;
    }

    /**
     * 筛选商品数据
     *
     * @param cartList
     * @return
     */
    private List<GoodsIndex> goodsActMatch(List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(cartList)) {
            return Collections.emptyList();
        }
        List<GoodsIndex> indexList = Lists.newArrayList();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            if (item.getSsuId() != null && item.getSsuId() != 0L) {
                String ssuId = item.getSsuId().toString();
                // 是否匹配
                boolean contains = belongCarEdition.contains(Long.valueOf(ssuId));
                if (!contains) {
                    continue;
                }
                // 规则判断
                boolean itemCheck = CartHelper.checkItemActQualifyCommon(item, promotionType.getTypeId());
                if (!itemCheck) {
                    continue;
                }

                indexList.add(new GoodsIndex(item.getItemId(), idx));
            }
        }
        return indexList;
    }

    /**
     * 加载活动配置信息
     *
     * @param config 活动配置
     */
    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof CarRangeReducePromotionConfig)) {
            log.error("config is not instanceof CarRangeReduceCondition. config:{}", config);
            return;
        }
        CarRangeReducePromotionConfig rangeReduceConfig = (CarRangeReducePromotionConfig) config;
        this.promotionId = rangeReduceConfig.getPromotionId();
        this.promotionType = rangeReduceConfig.getPromotionType();
        this.belongCarEdition = rangeReduceConfig.getJoinGoods().getSsuId();
        this.tradeType = rangeReduceConfig.getTradeType();
        this.benefitInfo = rangeReduceConfig.getBenefitInfo();
    }
}
