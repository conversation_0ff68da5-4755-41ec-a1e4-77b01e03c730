package com.xiaomi.nr.promotion.dao.mysql.nractivity.po;

import lombok.Data;

import java.util.List;

/**
 * Created by luobaohui on 202r/10/9
 */
@Data
public class InstallmentExtInfo {
    /**
     * 分期期数
     */
    private List<Integer> installmentTimes;

    /**
     * 分期类型
     */
    private Integer installmentType;

    /**
     * 息费承担方
     */
    private Integer interestPayer;

    /**
     * 金额范围-开始
     */
    private Long minActivityAmount;

    /**
     * 金额范围-结束
     */
    private Long maxActivityAmount;

    /**
     * 门店范围：是否所有门店都能参与 0-所有门店 1-指定门店
     */
    private Integer storeRange;
}
