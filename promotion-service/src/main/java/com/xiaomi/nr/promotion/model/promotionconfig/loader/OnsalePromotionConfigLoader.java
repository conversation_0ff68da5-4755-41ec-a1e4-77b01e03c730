package com.xiaomi.nr.promotion.model.promotionconfig.loader;

import com.xiaomi.nr.md.promotion.admin.api.constant.ChannelEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.ProductIdTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ProductPolicy;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.OnsaleRule;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.RangeReduceRule;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.specification.BenefitSpecification;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.specification.BrBudgetSpecification;
import com.xiaomi.nr.promotion.entity.redis.*;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.OnsalePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.model.promotionconfig.common.BenefitInfo;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ChildPriceInfo;
import com.xiaomi.nr.promotion.util.ChannelsHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 直降配置数据加载
 *
 * <AUTHOR>
 * @date 2021/6/3
 */
@Slf4j
@Component
public class OnsalePromotionConfigLoader implements PromotionConfigLoader {


    @Override
    public boolean support(AbstractPromotionConfig promotionConfig) {
        if (promotionConfig instanceof OnsalePromotionConfig) {
            return true;
        }
        return false;
    }

    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityInfo activityInfo) throws BizError {
        if (activityInfo == null || promotionConfig == null) {
            log.error("[OnsalePromotionConfigLoader] activityInfo is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "activityInfo is null");
        }
        TypeBase typeBase = activityInfo.getBasetype();
        Condition condition = activityInfo.getCondition();
        Policy policy = activityInfo.getPolicy();
        if (typeBase == null || condition == null || policy == null) {
            log.error("[OnsalePromotionConfigLoader] baseType or policy or policy is null. actInfo:{}", activityInfo);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "baseType or policy or policy is null");
        }
        Long id = typeBase.getId();
        OnsalePromotionConfig onsaleConfig = (OnsalePromotionConfig) promotionConfig;
        if (CollectionUtils.isEmpty(policy.getOnsale())) {
            log.error("policy onsale is empty. actId:{} policy:{} ", id, policy);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "policy onsale is empty");
        }
        List<OnsaleInfo> onsaleInfoList = Optional.ofNullable(policy.getOnsale()).orElse(Collections.emptyList());
        Map<String, ActPriceInfo> onsaleInfoMap = onsaleInfoList.stream()
                .collect(Collectors.toMap(info -> String.valueOf(info.getSkuPackage()), this::convertPriceInfo, (va1, va2) -> va1));
        onsaleConfig.setOnsaleInfoMap(onsaleInfoMap);

        Set<String> skuPackageSet = new HashSet<>();
        for (OnsaleInfo onsaleInfo : policy.getOnsale()) {
            skuPackageSet.add(String.valueOf(onsaleInfo.getSkuPackage()));
        }
        onsaleConfig.setIncludeSkuPackages(skuPackageSet);

        // 如果有专卖店，多加两个渠道
        List<Integer> channels = Optional.ofNullable(onsaleConfig.getChannels()).orElse(ChannelsHelper.convertChannels(condition));

        if (channels.contains(ChannelEnum.SPECIALTY.value)) {
            channels.add(ChannelEnum.B2T_C_CUSTOMER.value);
            channels.add(ChannelEnum.B2T_GOV_BIG_CUSTOMER.value);
            channels.add(ChannelEnum.B2T_MIJIA_BIG_CUSTOMER.value);
        }
        onsaleConfig.setChannels(channels);
    }

    private ActPriceInfo convertPriceInfo(OnsaleInfo onsaleInfo) {
        List<List<ChildPriceInfo>> childPriceList = Optional.ofNullable(onsaleInfo.getLowPriceList())
                .orElse(Collections.emptyList()).stream().map(list -> list.stream().map(this::convertPriceInfo).collect(Collectors.toList()))
                .collect(Collectors.toList());
        ActPriceInfo info = new ActPriceInfo();
        info.setSkuPackage(onsaleInfo.getSkuPackage());
        info.setLevel(onsaleInfo.getLevel());
        info.setPrice(onsaleInfo.getLowerPrice());
        info.setChildPriceList(childPriceList);
        info.setIsLimit(onsaleInfo.getIsLimit());
        info.setLimitRule(onsaleInfo.getLimitRule());
        info.setChildGroupPriceMap(onsaleInfo.getLowerPriceGroups());
        return info;
    }

    private ChildPriceInfo convertPriceInfo(BatchInfo info) {
        ChildPriceInfo childInfo = new ChildPriceInfo();
        childInfo.setSku(info.getSku());
        childInfo.setPrice(info.getLowerPrice());
        return childInfo;
    }

    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityConfig activityConfig) throws BizError {
        OnsalePromotionConfig onsaleConfig = (OnsalePromotionConfig) promotionConfig;
        //按渠道过滤
        Map<String, ActPriceInfo> onsaleInfoMap = new HashMap<>();
        Set<String> skuPackageSet = new HashSet<>();

        for (ProductPolicy productPolicy : activityConfig.getProductPolicyList()) {
            if (productPolicy.getProductIdType() != ProductIdTypeEnum.SSU.code) {
                continue;
            }
            Long productId = productPolicy.getProductId();
            ActPriceInfo actPriceInfo = new ActPriceInfo();
            actPriceInfo.setSkuPackage(productId);
            actPriceInfo.setPrice(productPolicy.getPromotionPrice());
            actPriceInfo.setSsuId(productId);


            LimitRule limitRule = new LimitRule();
            actPriceInfo.setLimitRule(limitRule);
            actPriceInfo.setIsLimit(BooleanEnum.NO.getValue());

            // 财务信息
            String rule = activityConfig.getRule();
            OnsaleRule onsaleRule = GsonUtil.fromJson(rule, OnsaleRule.class);
            if (onsaleRule != null) {
                BrBudgetSpecification specification = onsaleRule.getBudgetSpecification();
                if (specification != null) {
                    actPriceInfo.setBudgetApplyNo(specification.getBudgetApplyNo());
                    actPriceInfo.setLineNum(specification.getLineNum());
                }
            }
            onsaleInfoMap.put(String.valueOf(productId), actPriceInfo);

            skuPackageSet.add(String.valueOf(productId));
        }

        String rule = activityConfig.getRule();
        OnsaleRule onsaleRule = GsonUtil.fromJson(rule, OnsaleRule.class);

        BenefitInfo benefitInfo = Optional.ofNullable(onsaleRule)
                .map(OnsaleRule::getBenefitSpecification)
                .map(specification -> {
                    BenefitInfo info = new BenefitInfo();
                    info.setStartTime(specification.getStartTime());
                    info.setEndTime(specification.getEndTime());
                    return info;
                })
                .orElse(null);

        onsaleConfig.setOnsaleInfoMap(onsaleInfoMap);
        onsaleConfig.setIncludeSkuPackages(skuPackageSet);
        onsaleConfig.setTradeType(Optional.ofNullable(activityConfig.getTradeType()).orElse(0));
        onsaleConfig.setBenefitInfo(benefitInfo);
    }

}
