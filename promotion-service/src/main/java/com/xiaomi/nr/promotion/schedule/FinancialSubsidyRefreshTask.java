package com.xiaomi.nr.promotion.schedule;

import com.xiaomi.hera.trace.annotation.Trace;
import com.xiaomi.nr.promotion.activity.pool.FinancialSubsidyPool;
import com.xiaomi.nr.promotion.activity.pool.InstallmentGiftGoodsPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class FinancialSubsidyRefreshTask implements InitializingBean {

    @Autowired
    private FinancialSubsidyPool financialSubsidyPool;

    @Trace
    @Scheduled(fixedDelay = 1000 * 60, initialDelay = 1000 * 60)
    public void reBuildValidProductCache() {
        long startTime = System.currentTimeMillis();
        try {
            financialSubsidyPool.reBuildValidSubsidyCache();
            log.info("reBuildValidProductCache success ws={}", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("reBuildValidProductCache err:{}", e.getMessage());
        }
    }


	@Override
	public void afterPropertiesSet() throws Exception {
        financialSubsidyPool.reBuildValidSubsidyCache();
	}
}
