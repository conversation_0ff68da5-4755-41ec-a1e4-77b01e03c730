package com.xiaomi.nr.promotion.activity;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.GetProductActRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.componet.action.RenewReduceAction;
import com.xiaomi.nr.promotion.componet.condition.RenewReduceCondition;
import com.xiaomi.nr.promotion.componet.preparation.GlobalExcludePreparation;
import com.xiaomi.nr.promotion.componet.preparation.GoodsHierarchyPreparation;
import com.xiaomi.nr.promotion.componet.preparation.RecycleOrderPreparation;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.dao.redis.GlobalConfigRedisDao;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.GoodsValidActProvider;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.RenewReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.GoodsReduceInfo;
import com.xiaomi.nr.promotion.domain.activity.service.common.GoodsService;
import com.xiaomi.nr.promotion.util.CompareHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * 换新立减活动
 *
 * <AUTHOR>
 * @date 2021/05/09
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class RenewReduceActivity extends AbstractActivityTool implements ActivityTool,GoodsValidActProvider {
    /**
     * 换新立减信息
     * key: skuPackage val:GoodsReduceInfo
     */
    private Map<String, GoodsReduceInfo> renewReduceInfoMap;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private GlobalConfigRedisDao globalConfigRedisDao;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine
                .conditionPreparation(GoodsHierarchyPreparation.class)
                .conditionPreparation(GlobalExcludePreparation.class)
                .conditionPreparation(RecycleOrderPreparation.class)
                .condition(RenewReduceCondition.class)
                .action(RenewReduceAction.class);
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.RENEW_REDUCE;
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof RenewReducePromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        RenewReducePromotionConfig promotionConfig = (RenewReducePromotionConfig) config;
        this.renewReduceInfoMap = promotionConfig.getRenewReduceInfoMap();
        return true;
    }

    /**
     * 构建优惠信息
     *
     * @param context 上下文
     * @return 优惠信息
     */
    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) {
        return buildDefaultPromotionInfo(context);
    }

    @Override
    public List<CheckGoodsItem> checkProductsAct(CheckProductsActRequest request, Map<String, GoodsHierarchy> goodsHierarchyMap) throws BizError {
        return null;
    }

    /**
     * 获取产品站信息
     *
     * @param clientId       应用ID
     * @param orgCode        门店Code
     * @param skuPackageList 活动类别
     */
    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) {
        // 不支持透出
        return Collections.emptyMap();
    }

    /**
     * 获取活动详情
     *
     * @return 活动详情
     */
    @Override
    public ActivityDetail getActivityDetail() {
        return super.getBasicActivityDetail();
    }


    /**
     * 获取产品站活动信息：获取PromotionInfo
     *
     * @param request   产品站信息
     * @param hierarchy 商品层级关系
     * @param isOrgTool 是否来源门店工具
     * @return 促销信息
     */
    @Override
    public PromotionInfo getProductAct(GetProductActRequest request, GoodsHierarchy hierarchy, boolean isOrgTool) {
        // 不支持透出
        return null;
    }


    /**
     * 转换商品的可用活动
     * @param clientId       应用ID
     * @param orgCode        门店Code
     * @param skuPackageList 活动类别
     * @return
     * @throws BizError
     */
    @Override
    public Map<String, GoodsActivityInfo> getGoodsValidAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        if (!checkProductGoodsActCondition(clientId, orgCode)) {
            log.debug("RenewReduceActivity getGoodsValidAct condition is not match. actId:{} clientId:{} orgCode:{}", id, clientId, orgCode);
            return Collections.emptyMap();
        }

        List<String> joinedSkuPackageList = Lists.newArrayList(includeSkuPackages);
        joinedSkuPackageList.retainAll(skuPackageList);
        if (CollectionUtils.isEmpty(joinedSkuPackageList)) {
            return Collections.emptyMap();
        }

        //全局黑名单排除
        CompareItem globalInExclude = globalConfigRedisDao.getGlobalInExclude();
        CompareItem globalActInExclude = globalConfigRedisDao.getGlobalActInExclude();

        Map<String, GoodsActivityInfo> actMap = Maps.newHashMap();
        for (String skuPackage : joinedSkuPackageList) {
            GoodsReduceInfo goodsReduceInfo = renewReduceInfoMap.get(skuPackage);
            if (goodsReduceInfo == null) {
                continue;
            }

            GoodsHierarchy goodsHierarchy =goodsService.getHierarchyBySku(skuPackage);
            //是否满足全局排除
            if (CompareHelper.isFillInclude(goodsHierarchy, globalInExclude, true)) {
                log.warn("RenewReduceActivity getGoodsValidAct promotionId:{},skuPackage:{},globalInExclude",id,skuPackage);
                continue;
            }
            // 满足全局活动排除的
            if (CompareHelper.isFillInclude(goodsHierarchy, globalActInExclude, true)) {
                log.warn("RenewReduceActivity getGoodsValidAct promotionId:{},skuPackage:{},globalActInExclude",id,skuPackage);
                continue;
            }

            // 检查活动库存
            if (goodsReduceInfo.getLimitNum() > 0) {
                Integer usedNum = activityRedisDao.getActGoodsLimitNum(id, skuPackage);
                if (usedNum >= goodsReduceInfo.getLimitNum()) {
                    log.warn("RenewReduceActivity getGoodsValidAct promotionId:{},skuPackage:{},no stock",id,skuPackage);
                    continue;
                }
            }


            GoodsActivityInfo goodsActivityInfo = new GoodsActivityInfo();
            goodsActivityInfo.setType(type.getValue());
            goodsActivityInfo.setId(id);
            goodsActivityInfo.setName(name);
            goodsActivityInfo.setUnixStartTime(getUnixStartTime());
            goodsActivityInfo.setUnixEndTime(getUnixEndTime());

            RenewReduceGoods renewReduceGoods =new RenewReduceGoods();
            renewReduceGoods.setReduceAmount(goodsReduceInfo.getReduceAmount());
            goodsActivityInfo.setRenewReduceGoods(renewReduceGoods);
            actMap.put(skuPackage, goodsActivityInfo);
        }
        return actMap;

    }
}
