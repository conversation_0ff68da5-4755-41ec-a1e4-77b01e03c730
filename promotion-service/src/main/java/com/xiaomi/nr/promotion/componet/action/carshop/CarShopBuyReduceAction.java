package com.xiaomi.nr.promotion.componet.action.carshop;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.componet.action.AbstractAction;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActFrequencyEnum;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.PromotionExtend;
import com.xiaomi.nr.promotion.model.common.ReduceExtendInfo;
import com.xiaomi.nr.promotion.model.common.ReduceJoin;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.GoodsReduceInfo;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.model.ReturnStatus;
import com.xiaomi.nr.promotion.resource.provider.CommonActPersonLimitProvider;
import com.xiaomi.nr.promotion.resource.provider.GoodsLimitResourceProvider;
import com.xiaomi.nr.promotion.tool.CheckoutCartToolV2;
import com.xiaomi.nr.promotion.tool.PromotionDescRuleTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 车商城立减活动
 *
 * <AUTHOR>
 * @date 2025/5/19 19:32
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarShopBuyReduceAction extends AbstractAction {

    private Long promotionId;

    private Integer limitType;

    private Integer limitCount;

    private Map<String, GoodsReduceInfo> buyReduceMap;

    @Autowired
    private PromotionDescRuleTool promotionDescRuleTool;

    @Autowired
    private CheckoutCartToolV2 checkoutCartToolV2;


    @Autowired
    private ResourceProviderFactory resourceProviderFactory;

    @Override
    public void execute(ActivityTool activityTool, CheckoutPromotionRequest request, LocalContext context)
            throws BizError {
        if (CollectionUtils.isEmpty(context.getGoodIndex())) {
            log.error("buyReduce goodsIndex is empty. actId:{} uid:{}", promotionId, request.getUserId());
            return;
        }
        List<CartItem> cartList = request.getCartList();
        List<GoodsIndex> goodIndex = context.getGoodIndex();
        List<CartItem> fillCartList = CartHelper.getIndexCartList(cartList, goodIndex);

        // 商品改价
        changePrice(fillCartList, buyReduceMap);
        // 处理响应promotionInfo
        setResult(context, activityTool, fillCartList);
        // 初始化资源
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            List<String> skuPackageList =
                    CartHelper.getSkuPackageList(fillCartList).stream().distinct().collect(Collectors.toList());
            initReduceResourceResource(request, promotionId, skuPackageList, context);
        }
    }

    private void changePrice(List<CartItem> fillCartList, Map<String, GoodsReduceInfo> buyReduceMap) throws BizError {
        for (CartItem item : fillCartList) {
            String skuPackage = CartHelper.getSkuPackage(item);
            GoodsReduceInfo reduceInfo = buyReduceMap.get(skuPackage);
            if (reduceInfo == null) {
                continue;
            }
            // 改价
            doChangeCartPrice(item, reduceInfo);
        }
    }

    private void doChangeCartPrice(CartItem item, GoodsReduceInfo reduceInfo) throws BizError {
        Long reduceAmount = reduceInfo.getReduceAmount();
        if (reduceAmount <= 0L) {
            log.warn("buyReduce reduceAmount <= 0, actId:{} reduceInfo:{} cart:{} ", promotionId, reduceInfo, item);
            return;
        }
        long curPrice = CartHelper.itemCurPrice(item);
        // 如果超出购物车本身金额，则保留一分钱
        long reduceActual = reduceAmount * item.getCount();
        if (curPrice <= reduceActual) {
            reduceActual = curPrice - PromotionConstant.CART_PRICE_REMAIN;
        }
        // 分摊购物车减免金额
        List<CartItem> fillCartList = Collections.singletonList(item);
        checkoutCartToolV2.divideCartsReduce(reduceActual, Collections.singletonList(0), fillCartList,
                ActivityTypeEnum.BUY_REDUCE.getValue(), promotionId);
        item.getReduceItemList()
                .stream()
                .filter(Objects::nonNull)
                .filter(reduceItem -> Objects.equals(reduceItem.getPromotionId(), promotionId))
                .forEach(reduceItem -> {
                    reduceItem.setBudgetApplyNo(reduceInfo.getBudgetApplyNo());
                    reduceItem.setLineNum(reduceInfo.getLineNum());
                });
    }

    /**
     * 初始化立减
     *
     * @param request 请求对象
     * @param activityId 活动ID
     * @param skuPackageList sku/package
     * @param context 上下文
     * @throws BizError 业务异常
     */
    protected void initReduceResourceResource(CheckoutPromotionRequest request, Long activityId,
                                              List<String> skuPackageList, LocalContext context) throws BizError {
        // 增加商品库存
        GoodsLimitResourceProvider.ResContent resContent = new GoodsLimitResourceProvider.ResContent();
        resContent.setSkuPackageList(skuPackageList);
        resContent.setBuyReduceInfoMap(buyReduceMap);
        ResourceObject<GoodsLimitResourceProvider.ResContent>
                resContentResourceObject =
                buildLimitResource(ResourceType.GOODS_ACT_LIMIT, resContent, activityId, request.getOrderId());
        resContentResourceObject.setReturnStatus(ReturnStatus.CANCEL_RETURN.getValue());
        GoodsLimitResourceProvider reduceResourceProvider =
                (GoodsLimitResourceProvider) resourceProviderFactory.getProvider(ResourceType.GOODS_ACT_LIMIT);
        reduceResourceProvider.initResource(resContentResourceObject);
        context.addProvider(reduceResourceProvider);
        // 增加活动库存
        if (!Objects.equals(ActFrequencyEnum.NONE.getValue(), limitType)) {
            CommonActPersonLimitProvider.ActPersonLimit personLimit = buildActPersonLimit(activityId, request.getUserId(), 1, Optional.ofNullable(limitCount).map(Long::valueOf).orElse(0L));
            ResourceObject<CommonActPersonLimitProvider.ActPersonLimit> resourceObject = buildLimitResource(ResourceType.COMMON_ACT_PERSON_LIMIT, personLimit, activityId,
                            request.getOrderId());
            CommonActPersonLimitProvider resourcePersonProvider =
                    (CommonActPersonLimitProvider) resourceProviderFactory.getProvider(
                            ResourceType.COMMON_ACT_PERSON_LIMIT);
            resourcePersonProvider.initResource(resourceObject);
            context.addProvider(resourcePersonProvider);
        }
    }

    private void setResult(LocalContext context, ActivityTool tool, List<CartItem> fillCartList) throws BizError {
        // 参与Item
        List<String> itemList = fillCartList.stream().map(CartItem::getItemId).collect(Collectors.toList());
        // 生成拓展信息
        String extend = generateActExpandInfo(fillCartList);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setJoinedItemId(itemList);
        promotionInfo.setParentItemId(itemList);
        promotionInfo.setExtend(extend);
        promotionInfo.setJoined(BooleanEnum.YES.getValue());

        // 设置上下文结果
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    private String generateActExpandInfo(List<CartItem> fillCartList) {
        List<ReduceJoin> joinList = fillCartList.stream().map(this::initReduceJoin)
                .filter(Objects::nonNull).collect(Collectors.toList());

        ReduceExtendInfo reduceExtendInfo = new ReduceExtendInfo();
        reduceExtendInfo.setJoinExtend(joinList);

        PromotionExtend extend = new PromotionExtend();
        extend.setReduceExtend(reduceExtendInfo);
        return GsonUtil.toJson(extend);
    }

    private ReduceJoin initReduceJoin(CartItem item) {
        GoodsReduceInfo reduceInfo = buyReduceMap.get(CartHelper.getSkuPackage(item));
        if (reduceInfo == null) {
            return null;
        }
        String ruleDesc = promotionDescRuleTool.generateBuyReduceItemRule(reduceInfo.getReduceAmount());
        ReduceJoin reduceJoin = new ReduceJoin();
        reduceJoin.setSkuOrPackage(reduceInfo.getSkuPackage());
        reduceJoin.setLevel(reduceInfo.getLevel());
        reduceJoin.setReduceAmount(reduceInfo.getReduceAmount());
        reduceJoin.setJoinCounts(item.getCount());
        reduceJoin.setRuleDesc(ruleDesc);
        reduceJoin.setLimitNum(reduceInfo.getLimitNum());
        return reduceJoin;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof BuyReducePromotionConfig)) {
            return;
        }
        BuyReducePromotionConfig promotionConfig = (BuyReducePromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.limitCount = promotionConfig.getLimitCount();
        this.limitType = promotionConfig.getLimitType();
        this.buyReduceMap = promotionConfig.getBuyReduceInfoMap();
    }
}
