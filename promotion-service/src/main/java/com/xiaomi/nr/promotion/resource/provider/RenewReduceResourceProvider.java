package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.util.IdKeyHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 换新资源立减
 *
 * <AUTHOR>
 * @date 2021/9/16
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class RenewReduceResourceProvider implements ResourceProvider<RenewReduceResourceProvider.ResContent> {
    private ResourceObject<RenewReduceResourceProvider.ResContent> resourceObject;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Autowired
    private NacosConfig nacosConfig;

    @Override
    public ResourceObject<RenewReduceResourceProvider.ResContent> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<RenewReduceResourceProvider.ResContent> object) {
        this.resourceObject = object;
    }

    @Override
    public void lock() throws BizError {
        List<String> skuPackageList = resourceObject.getContent().skuPackageList;
        String renewLevelKey = resourceObject.getContent().renewLevelKey;
        log.info("lock act goods limit resource. orderId:{}, promotionId:{} skuPackageList:{}", resourceObject.getOrderId(), resourceObject.getPromotionId(), skuPackageList);
        if (CollectionUtils.isEmpty(skuPackageList)) {
            log.warn("skuPackageList is empty. resourceObject:{}", resourceObject);
            return;
        }
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("RenewReduceResourceProvider.lock(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        skuPackageList.forEach(skuPackage -> {
            String key = IdKeyHelper.generalReduceKey(skuPackage, renewLevelKey);
            activityRedisDao.incrActGoodsLimitNum(resourceObject.getPromotionId(), key, 1);
        });
        log.info("lock act goods limit ok. resourceObject:{}", resourceObject);
    }

    @Override
    public void consume() {
        log.info("consume act bargain limit resource. resourceObject{}", resourceObject);
    }

    @Override
    public void rollback() throws BizError {
        List<String> skuPackageList = resourceObject.getContent().skuPackageList;
        String renewLevelKey = resourceObject.getContent().renewLevelKey;
        log.info("rollback act limit resource. resourceObject:{}", resourceObject);
        if (CollectionUtils.isEmpty(skuPackageList)) {
            log.warn("rollback skuPackageList is empty. resourceObject:{}", resourceObject);
            return;
        }
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("RenewReduceResourceProvider.rollback(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        skuPackageList.forEach(skuPackage -> {
            String key = IdKeyHelper.generalReduceKey(skuPackage, renewLevelKey);
            activityRedisDao.decrActGoodsLimitNum(resourceObject.getPromotionId(), key, 1);
        });
        log.info("rollback act limit ok. resourceObject:{}", resourceObject);
    }

    @Override
    public String conflictText() {
        return "处理加价购数失败";
    }

    @Data
    public static class ResContent {
        /**
         * 扣减次数商品
         */
        private List<String> skuPackageList;

        /**
         * 阶梯Key
         */
        private String renewLevelKey;
    }
}
