package com.xiaomi.nr.promotion.dao.mysql.promotionuser;

import com.xiaomi.nr.promotion.entity.mysql.promotionuser.PromotionResourceOperationLog;
import org.apache.ibatis.annotations.Insert;
import org.springframework.stereotype.Repository;

/**
 * 操作日志
 *
 * <AUTHOR>
 * @date 2021/4/21
 */
@Repository
public interface PromotionResourceOperationLogMapper {

    /**
     * 插入资源操作日志
     *
     * @param promotionResourceOperationLog 资源操作日志实体
     * @return 影响数量
     */
    @Insert("insert into promotion_resource_operation_log (order_id,transaction_id,uid,resource_context,operation_source,operation_type) " +
            "values (#{orderId},#{transactionId},#{uid},#{resourceContext},#{operationSource},#{operationType})")
    Integer insertOperationLog(PromotionResourceOperationLog promotionResourceOperationLog);
}
