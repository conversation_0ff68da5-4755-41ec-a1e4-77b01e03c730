package com.xiaomi.nr.promotion.schedule;

import com.xiaomi.hera.trace.annotation.Trace;
import com.xiaomi.nr.promotion.activity.pool.InstallmentActivityPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 分期活动定时任务
 *
 * <AUTHOR>
 * @date 2024/10/12
 */
@Component
@Slf4j
public class InstallmentActivityRefreshTask implements InitializingBean {
    
    @Resource
    private InstallmentActivityPool installmentActivityPool;
    
    
    @Trace
    @Scheduled(fixedDelay = 1000 * 60, initialDelay = 1000 * 60)
    public void refreshInstallmentCache() {
        long runStartTime = System.currentTimeMillis();
        try {
            log.info("refreshInstallmentCache, begin to rebuild activity cache on Scheduled");
            installmentActivityPool.refreshInstallmentCache();
            log.info("refreshInstallmentCache, end to rebuild activity cache on Scheduled, lostTime:{}ms", System.currentTimeMillis() - runStartTime);
        } catch (Exception e) {
            log.error("refreshPurchaseCache, error on Scheduled, lostTime:{}ms, err:{}", System.currentTimeMillis() - runStartTime, e.getMessage(), e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        long runStartTime = System.currentTimeMillis();
        try {
            log.info("refreshInstallmentCache, begin to rebuild activity cache on server Started");
            installmentActivityPool.refreshInstallmentCache();
            log.info("refreshInstallmentCache, end to rebuild activity cache on server Started, lostTime:{}ms", System.currentTimeMillis() - runStartTime);
        } catch (Exception e) {
            log.error("refreshPurchaseCache, error on server Started, lostTime:{}ms, err:{}", System.currentTimeMillis() - runStartTime, e.getMessage(), e);
        }
    }
}
