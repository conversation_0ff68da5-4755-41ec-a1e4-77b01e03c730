package com.xiaomi.nr.promotion.model.promotionconfig;

import com.xiaomi.nr.promotion.model.promotionconfig.common.GoodsReduceInfo;
import lombok.ToString;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 下单立减配置
 *
 * <AUTHOR>
 * @date 2022/07/04
 */
@ToString
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class BuyReducePromotionConfig extends MultiPromotionConfig {
    /**
     * 下单立减优惠信息
     * <p>
     * key: skuPackage val:GoodsReduceInfo
     */
    private Map<String, GoodsReduceInfo> buyReduceInfoMap;

    public Map<String, GoodsReduceInfo> getBuyReduceInfoMap() {
        return buyReduceInfoMap;
    }

    public void setBuyReduceInfoMap(Map<String, GoodsReduceInfo> buyReduceInfoMap) {
        this.buyReduceInfoMap = buyReduceInfoMap;
    }
}
