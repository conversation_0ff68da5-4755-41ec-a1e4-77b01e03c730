package com.xiaomi.nr.promotion.model.promotionconfig;

import com.xiaomi.nr.promotion.api.dto.enums.PromotionTag;
import com.xiaomi.nr.promotion.model.promotionconfig.common.GoodsReduceInfo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 下单立减配置
 *
 * <AUTHOR>
 * @date 2022/07/04
 */
@ToString
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class BuyReducePromotionConfig extends MultiPromotionConfig {
    /**
     * 下单立减优惠信息
     * <p>
     * key: skuPackage val:GoodsReduceInfo
     */
    @Setter
    @Getter
    private Map<String, GoodsReduceInfo> buyReduceInfoMap;

    @Setter
    @Getter
    private PromotionTag activityCarTag;

    @Setter
    @Getter
    private Integer limitCount;

    @Getter
    @Setter
    private Integer limitType;

    @Setter
    @Getter
    private boolean carShop = false;

}
