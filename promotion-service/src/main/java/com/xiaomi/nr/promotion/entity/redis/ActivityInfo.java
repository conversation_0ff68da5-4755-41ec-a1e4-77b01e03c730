package com.xiaomi.nr.promotion.entity.redis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * 活动缓存
 *
 * <AUTHOR>
 * @date 2021/5/14
 */
@Data
public class ActivityInfo implements Serializable {
    private static final long serialVersionUID = 3017381452721424363L;

    /**
     * 基本信息
     */
    private TypeBase basetype;

    /**
     * 条件信息
     */
    private Condition condition;

    /**
     * 政策信息
     */
    @SerializedName(value = "policy", alternate = {"policy_new"})
    private Policy policy;

    /**
     * 活动版本
     */
    private Integer version;
}
