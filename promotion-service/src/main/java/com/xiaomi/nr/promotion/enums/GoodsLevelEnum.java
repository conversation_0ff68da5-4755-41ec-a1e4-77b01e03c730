package com.xiaomi.nr.promotion.enums;

/**
 * 商品品级
 *
 * <AUTHOR>
 * @date 2021/11/23
 */
public enum GoodsLevelEnum {
    /**
     * 单品
     */
    GOODS("goods"),
    /**
     * SKU
     */
    SKU("sku"),
    /**
     * 套装
     */
    PACKAGE("package"),

    /**
     * ssu
     */
    SSU("ssu");

    private final String level;

    private static final int PACKAGE_ID_MIN_LENGTH = 10;
    /**
     *  获取级别
     * @param skuPackage  SKU 或 PACKAGE
     * @return enum
     */
    public static GoodsLevelEnum getLevel(String skuPackage) {
        if (skuPackage.length() >= PACKAGE_ID_MIN_LENGTH) {
            return PACKAGE;
        }
        return GOODS;
    }

    GoodsLevelEnum(String level) {
        this.level = level;
    }

    public String getLevel() {
        return level;
    }
}
