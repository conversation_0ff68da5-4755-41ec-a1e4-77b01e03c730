package com.xiaomi.nr.promotion.entity.redis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 换新立减 RenewReduceInfo
 *
 * <AUTHOR>
 * @date 2021/3/26
 * @see RenewReduceInfo
 */
@Data
@ToString
public class RenewReduceInfo implements Serializable {
    private static final long serialVersionUID = 355449167249845669L;
    /**
     * sku/套装id
     */
    @SerializedName("sku_package")
    private Long skuPackage = 0L;
    /**
     * sku或package
     */
    private String level = "";
    /**
     * 档位KEY
     */
    @SerializedName(value = "levelKey", alternate = "level_key")
    private String levelKey;
    /**
     * 直降后的价格（单位分）
     */
    @SerializedName(value = "reduceAmount", alternate = "reduce_amount")
    private Long reduceAmount;
    /**
     * 活动数量限制
     */
    @SerializedName(value = "limitNum", alternate = "limit_num")
    private Integer limitNum;
}
