package com.xiaomi.nr.promotion.componet.condition.carsale;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.SubmitTypeEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.OnsalePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.model.promotionconfig.common.BenefitInfo;
import com.xiaomi.nr.promotion.model.promotionconfig.common.MergedCartItem;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 直降条件判断
 *
 * <AUTHOR>
 * @date 2024/11/25
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarOnsaleCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 活动类型
     */
    private PromotionToolType promotionType;
    /**
     * 直降信息
     */
    private Map<String, ActPriceInfo> onsaleInfoMap;

    /**
     * 交易类型
     */
    protected Integer tradeType;

    /**
     * 权益信息
     */
    protected BenefitInfo benefitInfo;

    /**
     * - 遍历购物车,合并同一个sku或套装的item
     * - 过滤不能参加直降的. 查找是否找到对应的直降信息， 以及限购数是否符合
     * - 筛选购物车中可以参加直降的列表
     *
     * @param request 请求参数
     * @param context 活动内上下文
     * @return 是否满足
     */
    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) {
        Long uid = request.getUserId();
        if (MapUtil.isEmpty(onsaleInfoMap)) {
            log.error("onsaleInfoMap is empty. actId:{} uid:{}", promotionId, uid);
            return false;
        }

        // 交易类型校验
        if (!tradeTypeMatchCheck(request)) {
            return false;
        }

        List<CartItem> cartList = request.getCartList();

        // 合并同sku 或 packageId 商品. key: sku/packageId val: summarize
        Map<String, MergedCartItem> mergeCartItemMap = mergeCartList(cartList);

        // 过滤不可以参加活动的
        mergeCartItemMap = filterMergeCartItemMap(mergeCartItemMap);

        // 查找可以参加的购物车
        List<GoodsIndex> indexList = findFillGoodsList(cartList, mergeCartItemMap);

        // 没有符合的商品，不满足活动
        if (CollectionUtils.isEmpty(indexList)) {
            return false;
        }

        context.setGoodIndex(indexList);
        return true;
    }


    /**
     * 检查交易类型是否符合条件
     *
     * @param request 结账促销请求对象
     * @return 如果交易类型不符合条件则返回true，否则返回false
     */
    private boolean tradeTypeMatchCheck(CheckoutPromotionRequest request) {

        Long orderTime = request.getOrderTime();

        int submitType = Optional.ofNullable(request.getSubmitType()).orElse(0);

        if (SubmitTypeEnum.REPLACE.getCode() == tradeType) {
            if (SubmitTypeEnum.REPLACE.getCode() != submitType) {
                return false;
            }
            if (orderTime == null) {
                log.info("CarOnsaleCondition orderTime is null. actId:{} oid:{}, orderTime:{}", promotionId, request.getOrderId(), orderTime);
                return false;
            }
            if (benefitInfo.getStartTime() > orderTime || benefitInfo.getEndTime() < orderTime) {
                log.info("CarOnsaleCondition orderTime check is false. actId:{} oid:{}, orderTime:{}", promotionId, request.getOrderId(), orderTime);
                return false;
            }
        } else {
            if (SubmitTypeEnum.REPLACE.getCode() == submitType && orderTime != null) {
                return false;
            }
        }
        return true;
    }


    /**
     * 合并同SKU / PackageId 的商品和购物车
     *
     * @param cartList 购物车列表
     * @return key: skuPackage val: 合并后的cartItem(主要是数量合并）
     */
    private Map<String, MergedCartItem> mergeCartList(List<CartItem> cartList) {
        Map<String, MergedCartItem> mergeCartList = new HashMap<>(cartList.size());
        cartList.forEach(item -> handleCartItem(item, mergeCartList));
        return mergeCartList;
    }

    private void handleCartItem(CartItem item, Map<String, MergedCartItem> mergeCartList) {
        int activityType = promotionType.getTypeId();
        boolean itemQualify = CartHelper.checkItemActQualifyCommon(item, activityType);
        if (!itemQualify) {
            return;
        }
        boolean itemOnsaleQualify = CartHelper.isOnsaleActQualifyItem(item, null);
        if (!itemOnsaleQualify) {
            return;
        }
        ActPriceInfo onsaleInfo = onsaleInfoMap.get(CartHelper.getSkuPackageForOnsale(item));
        if (onsaleInfo == null || (onsaleInfo.getPrice() >= (item.getCartPrice() + item.getOnSaleBookingPrice()))) {
            return;
        }

        // 合并数量
        String skuPackage = CartHelper.getSkuPackageForOnsale(item);
        MergedCartItem mergeItem = mergeCartList.get(skuPackage);
        if (mergeItem != null) {
            mergeItem.setCounts(mergeItem.getCounts() + item.getCount());
            return;
        }
        mergeItem = new MergedCartItem();
        mergeItem.setSkuPackage(Long.valueOf(skuPackage));
        mergeItem.setCartPrice(item.getCartPrice());
        mergeItem.setCounts(item.getCount());
        mergeItem.setCanJoin(Boolean.FALSE);
        mergeItem.setBookingPrice(item.getOnSaleBookingPrice());
        mergeCartList.put(skuPackage, mergeItem);
    }

    /**
     * 过滤不合法的商品
     *
     * @param mergeCartMap 合并后的Map
     * @return 过滤后的Map
     */
    private Map<String, MergedCartItem> filterMergeCartItemMap(Map<String, MergedCartItem> mergeCartMap) {
        if (MapUtil.isEmpty(mergeCartMap)) {
            return Collections.emptyMap();
        }

        Map<String, MergedCartItem> newMap = new HashMap<>(mergeCartMap.size());
        mergeCartMap.forEach((skuPackage, mergedCartItem) -> {
            ActPriceInfo onsaleInfo = onsaleInfoMap.get(String.valueOf(mergedCartItem.getSkuPackage()));
            if (onsaleInfo == null) {
                return;
            }
            boolean matchQuota = isMatchQuota(onsaleInfo, mergedCartItem);
            if (!matchQuota) {
                return;
            }
            mergedCartItem.setCanJoin(Boolean.TRUE);
            newMap.put(skuPackage, mergedCartItem);
        });
        return newMap;
    }

    /**
     * 是否符合条件：价格， 门店限制
     *
     * @param onsale   直降信息
     * @param cartItem 购物车Item
     * @return true/false
     */
    private boolean isMatchQuota(ActPriceInfo onsale, MergedCartItem cartItem) {
        Long skuPackage = onsale.getSkuPackage();
        if (!Objects.equals(skuPackage, cartItem.getSkuPackage())) {
            return false;
        }
        // 校验价格，加购价格小于等于直降后价格则该商品不参与直降活动，定金预售需加上定金的价格
        if ((cartItem.getCartPrice() + cartItem.getBookingPrice()) <= onsale.getPrice()) {
            return false;
        }
        return true;
    }

    /**
     * 筛选出可以改价的item index List
     *
     * @param cartList         购物车列表
     * @param mergeCartItemMap 合并的Map
     * @return goodsIndList
     */
    private List<GoodsIndex> findFillGoodsList(List<CartItem> cartList, Map<String, MergedCartItem> mergeCartItemMap) {
        List<GoodsIndex> indexList = new ArrayList<>();
        // 遍历购物车，将满足的商品放入
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            int activityType = promotionType.getTypeId();
            boolean itemQualify = CartHelper.checkItemActQualifyCommon(item, activityType);
            if (!itemQualify) {
                continue;
            }
            boolean itemOnsaleQualify = CartHelper.isOnsaleActQualifyItem(item, null);
            if (!itemOnsaleQualify) {
                continue;
            }
            String skuPackage = CartHelper.getSkuPackageForOnsale(item);
            if (!mergeCartItemMap.containsKey(skuPackage)) {
                continue;
            }
            ActPriceInfo onsaleInfo = onsaleInfoMap.get(String.valueOf(skuPackage));
            if (onsaleInfo == null || (onsaleInfo.getPrice() >= (item.getCartPrice() + item.getOnSaleBookingPrice()))) {
                continue;
            }
            GoodsIndex goodsIndex = new GoodsIndex(item.getItemId(), idx);
            indexList.add(goodsIndex);
        }
        return indexList;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof OnsalePromotionConfig)) {
            log.error("config is not instanceof OnsalePromotionConfig. config:{}", config);
            return;
        }
        OnsalePromotionConfig onsaleConfig = (OnsalePromotionConfig) config;
        this.promotionId = onsaleConfig.getPromotionId();
        this.onsaleInfoMap = onsaleConfig.getOnsaleInfoMap();
        this.promotionType = onsaleConfig.getPromotionType();
        this.tradeType = onsaleConfig.getTradeType();
        this.benefitInfo = onsaleConfig.getBenefitInfo();
    }
}
