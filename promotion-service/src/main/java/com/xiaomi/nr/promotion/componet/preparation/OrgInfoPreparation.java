package com.xiaomi.nr.promotion.componet.preparation;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.engine.componet.ConditionPreparation;
import com.xiaomi.nr.promotion.entity.redis.OrgInfo;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.OrgInfoExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;

/**
 * 门店详情
 *
 * <AUTHOR>
 * @date 2021/4/1
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class OrgInfoPreparation extends ConditionPreparation {

    @Override
    public void prepare(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (StringUtils.isEmpty(request.getOrgCode())) {
            return;
        }
        Map<ResourceExtType, ExternalDataProvider<?>> providerMap = Optional.ofNullable(context.getExternalDataMap())
                .orElse(Collections.emptyMap());
        context.setOrgInfo(getOrgInfo(providerMap));
    }

    private OrgInfo getOrgInfo(Map<ResourceExtType, ExternalDataProvider<?>> providerMap) throws BizError {
        OrgInfoExternalProvider provider = (OrgInfoExternalProvider) providerMap.get(ResourceExtType.ORG_INFO);
        return null == provider ? null : provider.getData();
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
    }
}
