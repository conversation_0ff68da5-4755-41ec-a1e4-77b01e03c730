package com.xiaomi.nr.promotion.dao.redis;

import com.xiaomi.nr.promotion.entity.redis.ActivityInfo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;
import java.util.Map;

/**
 * 活动redis 缓存操作对象
 *
 * <AUTHOR>
 * @date 2021/3/30
 */
public interface ActivityRedisDao {

    /**
     * 根据部门Code获取活动ID列表
     *
     * @param orgCode 部门Code
     * @return 活动ID列表
     */
    List<Long> listActivityIdByOrgCode(String orgCode);

    /**
     * 根据ClientId获取活动ID列表
     *
     * @param clientId 客户端ID
     * @return 活动ID列表
     */
    List<Long> listActivityIdByClientId(Long clientId);

    /**
     * 活动期间每人每天总量(Utype)
     *
     * @param actId     活动ID
     * @param uidType   用户类型
     * @param userId    用户ID
     * @param dateMills 13位时间戳
     * @return Num
     */
    Integer getActUtypePersonDayLimitNum(Long actId, String uidType, Long userId, Long dateMills);

    /**
     * 活动期间每人活动总量(Utype)
     *
     * @param actId   活动ID
     * @param uidType 用户类型
     * @param userId  用户ID
     * @return Num
     */
    Integer getActUtypePersonLimitNum(Long actId, String uidType, Long userId);

    /**
     * 活动期间每人每天总量
     *
     * @param actId     活动ID
     * @param userId    用户ID
     * @param dateMills 13位时间戳
     * @return Num
     */
    Integer getActPersonDayLimitNum(Long actId, Long userId, Long dateMills);

    /**
     * 活动期间每人活动总量
     *
     * @param actId  活动ID
     * @param userId 用户ID
     * @return Num
     */
    Integer getActPersonLimitNum(Long actId, Long userId);


    /**
     * 活动期间每人每店活动总量
     *
     * @param actId 活动Id
     * @param userId 用户Id（mid）
     * @param orgCode   门店Code
     * @return Num
     */
    Integer getActPersonStoreLimitNum(Long actId, Long userId, String orgCode);

    /**
     * 活动期间每人每店活动总量
     *
     * @param actId   活动Id
     * @param userId  用户Id（手机号）
     * @param orgCode   门店Code
     * @return Num
     */
    Integer getActUtypePersonStoreLimitNum(Long actId, Long userId, String orgCode);

    /**
     * 扣减每人（mid）每个门店活动数量
     *
     * @param actId     活动id
     * @param userId    小米ID
     * @param orgCode   门店id
     * @param count     数量
     * @return 回滚次数
     */
    Long incrActPersonStoreUserIdLimitNum(Long actId, Long userId, String orgCode, Integer count);

    /**
     * 回滚每人（mid）每个门店活动数量
     *
     * @param actId     活动id
     * @param userId    小米ID
     * @param orgCode   门店id
     * @param count     数量
     * @return 回滚次数
     */
    Long decrActPersonStoreUserIdLimitNum(Long actId, Long userId, String orgCode, Integer count);

    /**
     * 扣减每人（手机号）每个门店活动数量
     *
     * @param actId     活动id
     * @param mobile    手机号
     * @param orgCode   门店id
     * @param count     数量
     * @return 回滚次数
     */
    Long incrActPersonStoreMobileLimitNum(Long actId, Long mobile, String orgCode, Integer count);

    /**
     * 回滚每人（手机号）每个门店活动数量
     *
     * @param actId     活动id
     * @param mobile    手机号
     * @param orgCode   门店id
     * @param count     数量
     * @return 回滚次数
     */
    Long decrActPersonStoreMobileLimitNum(Long actId, Long mobile, String orgCode, Integer count);

    /**
     * 活动期间全部门店活动每天总量
     *
     * @param actId     活动ID
     * @param dateMills 13位时间戳
     * @return Num
     */
    Integer getActAllStoreDayLimitNum(Long actId, Long dateMills);

    /**
     * 活动期间全部门店活动总量
     *
     * @param actId 活动ID
     * @return Num
     */
    Integer getActAllStoreLimitNum(Long actId);

    /**
     * 活动期间每个门店活动每天总量
     *
     * @param orgCode   门店Code
     * @param actId     活动ID
     * @param dateMills 13位时间戳
     * @return Num
     */
    Integer getActStoreDayLimitNum(Long actId, String orgCode, Long dateMills);

    /**
     * 直降活动mid每人参与次数key直降
     *
     * @param actId      活动ID
     * @param userId     用户ID
     * @param skuPackage sku/packageId
     * @return 参与次数
     */
    Integer getOnsaleUserLimitNum(Long actId, Long userId, String skuPackage);

    /**
     * 直降活动手机号每人参与次数
     *
     * @param actId      活动ID
     * @param userId     用户ID
     * @param skuPackage sku/packageId
     * @return 参与次数
     */
    Integer getOnsaleMobileUserLimitNum(Long actId, Long userId, String skuPackage);

    /**
     * 直降活动每天每个门店参与次数
     *
     * @param actId      活动ID
     * @param orgCode    门店Code
     * @param dateMills  13位时间戳
     * @param skuPackage sku/packageId
     * @return 参与次数
     */
    Integer getOnsaleStoreDayLimitNum(Long actId, String orgCode, Long dateMills, String skuPackage);

    /**
     * 直降活动每天全国门店参与次数
     *
     * @param actId      活动ID
     * @param dateMills  13位时间戳
     * @param skuPackage sku/packageId
     * @return 参与次数
     */
    Integer getOnsaleAllStoreDayLimitNum(Long actId, Long dateMills, String skuPackage);

    /**
     * 直降活动期间每个门店参与次数key
     *
     * @param actId      活动ID
     * @param orgCode    门店Code
     * @param skuPackage sku/packageId
     * @return 参与次数
     */
    Integer getOnsaleStoreLimitNum(Long actId, String orgCode, String skuPackage);

    /**
     * 直降活动期间全国门店参与次数key
     *
     * @param actId      活动ID
     * @param skuPackage sku/packageId
     * @return 参与次数
     */
    Integer getOnsaleAllStoreLimitNum(Long actId, String skuPackage);

    /**
     * 活动期间每个门店活动总量
     *
     * @param orgCode 门店Code
     * @param actId   活动ID
     * @return Num
     */
    Integer getActStoreLimitNum(Long actId, String orgCode);

    /**
     * 活动期间活动总量
     *
     * @param actId 活动ID
     * @return Num
     */
    Integer getActStatusLimitNum(Long actId);

    /**
     * 是否存在用户参加过活动每天
     *
     * @param actId     活动ID
     * @param userId    用户ID
     * @param dateMills 13位时间戳
     * @return true/false
     */
    Boolean isExistUserActDailyRecord(Long actId, Long userId, Long dateMills);

    /**
     * 是否存在用户参加过活动总共
     *
     * @param actId  活动ID
     * @param userId 用户ID
     * @return true/false
     */
    Boolean isExistUserActTotalRecord(Long actId, Long userId);

    /**
     * 获取买赠活动次数
     *
     * @param actId   活动次数
     * @param sku     SKU
     * @param groupId 组ID
     * @return 数量
     */
    Integer getActBuyGiftLimitNum(Long actId, String sku, Long groupId);

    /**
     * 获取24年北京以旧换新补贴活动参加次数
     *
     * @param idCard  身份证
     * @return 数量
     */
    Integer getActPurchaseSubsidyLimitTotal(String idCard);
    
    
    /**
     * 获取24年北京以旧换新补贴活动参加次数
     *
     * @param idCard  身份证
     * @return 数量
     */
    Map<Integer, Integer> scanActPurchaseSubsidyLimitGroup(String idCard);
    
    /**
     * 获取24年北京以旧换新补贴活动参加次数
     *
     * @param idCard  身份证
     * @param spuGroup  品类
     * @return 数量
     */
    Integer getActPurchaseSubsidyLimitGroup(String idCard, Integer spuGroup);

    /**
     * 获取加价购活动次数
     *
     * @param actId   活动次数
     * @param sku     SKU
     * @param groupId 组ID
     * @return 数量
     */
    Integer getActBargainLimitNum(Long actId, String sku, Long groupId);

    /**
     * 获取商品活动库存数据
     *
     * @param actId   活动id
     * @param sku     SKU
     * @return 数量
     */
    Integer getActGoodsLimitNum(Long actId, String sku);

    /**
     * 获取商品每个门店活动库存
     *
     * @param actId       活动id
     * @param skuPackage  sku/packageId
     * @param orgCode     门店id
     * @return 数量
     */
    Integer getActGoodsStoreLimitNum(Long actId, String skuPackage, String orgCode);

    /**
     * 批量获取商品每个门店活动库存
     * @param orgCode 门店id
     * @param actId 活动id
     * @param skuPackages sku/packageId
     * @return 数量
     */
    Map<String, Long> batchGetActGoodsStoreLimitNum(String orgCode, Long actId, List<String> skuPackages);

    /**
     * 扣减商品每个门店活动库存
     *
     * @param actId       活动id
     * @param skuPackage  sku/packageId
     * @param orgCode     门店id
     * @param count 数量
     * @return 回滚次数
     */
    Long incrActGoodsStoreLimitNum(Long actId, String skuPackage, String orgCode, Integer count);

    /**
     * 回滚商品每个门店活动库存
     *
     * @param actId       活动id
     * @param skuPackage  sku/packageId
     * @param orgCode     门店id
     * @param count 数量
     * @return 回滚次数
     */
    Long decrActGoodsStoreLimitNum(Long actId, String skuPackage, String orgCode, Integer count);


    // ------------------------ 订单相关 ---------------------------

    /**
     * 增加活动参与总次数
     *
     * @param actId    活动ID
     * @param orderId  订单ID
     * @param count    活动增长次数
     * @param limitNum 活动限制总次数
     * @return 增长次数
     */
    Long incrActStatusLimitNum(Long actId, Long orderId, Integer count, Long limitNum);

    /**
     * 回滚活动参与次数
     *
     * @param actId   活动ID
     * @param orderId 订单ID
     * @param count   次数
     * @return 回滚次数
     */
    Long decrActStatusLimitNum(Long actId, Long orderId, Integer count);

    /**
     * 插入存在用户参加过活动每天
     *
     * @param actId      活动ID
     * @param userId     用户ID
     * @param dateMills  13位时间戳
     * @param orderId    订单ID
     * @param expireTime 过期时间 > 0 为有效 （秒）
     */
    void setUserActDailyRecord(Long actId, Long userId, Long dateMills, Long orderId, Long expireTime) throws BizError;

    /**
     * 删除用户参加过活动每天
     *
     * @param actId     活动ID
     * @param userId    用户ID
     * @param dateMills 13位时间戳
     * @return 影响数量
     */
    Long delUserActDailyRecord(Long actId, Long userId, Long dateMills);

    /**
     * 插入存在用户参加过活动总共
     *
     * @param actId      活动ID
     * @param userId     用户ID
     * @param orderId    订单ID
     * @param expireTime 过期时间 > 0 为有效 （秒）
     */
    void setUserActTotalRecord(Long actId, Long userId, Long orderId, Long expireTime) throws BizError;

    /**
     * 删除存在用户参加过活动总共
     *
     * @param actId  活动ID
     * @param userId 用户ID
     * @return 影响数量
     */
    Long delUserActTotalRecord(Long actId, Long userId);

    /**
     * 增加活动期间每人每天总量
     *
     * @param actId     活动ID
     * @param userId    用户ID
     * @param dateMills 13位时间戳
     * @param count     数量
     * @param limitNum  限制数量
     * @return Num
     */
    Long incrActPersonDayLimitNum(Long actId, Long userId, Long dateMills, Integer count, Long limitNum);

    /**
     * 减少活动期间每人每天总量
     *
     * @param actId     活动ID
     * @param userId    用户ID
     * @param dateMills 13位时间戳
     * @param count     数量
     * @return Num
     */
    Long decrActPersonDayLimitNum(Long actId, Long userId, Long dateMills, Integer count);

    /**
     * 减少活动期间每人活动总量
     *
     * @param actId    活动ID
     * @param userId   用户ID
     * @param count    数量
     * @param limitNum 限制数量
     * @return Num
     */
    Long incrActPersonLimitNum(Long actId, Long userId, Integer count, Long limitNum);

    /**
     * 减少活动期间每人活动总量
     *
     * @param actId  活动ID
     * @param userId 用户ID
     * @param count  数量
     * @return Num
     */
    Long decrActPersonLimitNum(Long actId, Long userId, Integer count);

    /**
     * 增加活动期间每人每天总量(Utype)
     *
     * @param actId     活动ID
     * @param uidType   用户类型
     * @param userId    用户ID
     * @param dateMills 13位时间戳
     * @param count     数量
     * @param limitNum  限制数量
     * @return Num
     */
    Long incrActUtypePersonDayLimitNum(Long actId, String uidType, Long userId, Long dateMills, Integer count, Long limitNum);

    /**
     * 减少活动期间每人每天总量(Utype)
     *
     * @param actId     活动ID
     * @param uidType   用户类型
     * @param userId    用户ID
     * @param dateMills 13位时间戳
     * @param count     数量
     * @return Num
     */
    Long decrActUtypePersonDayLimitNum(Long actId, String uidType, Long userId, Long dateMills, Integer count);

    /**
     * 减少活动期间每人活动总量(Utype)
     *
     * @param actId    活动ID
     * @param uidType  用户类型
     * @param userId   用户ID
     * @param count    数量
     * @param limitNum 限制数量
     * @return Num
     */
    Long incrActUtypePersonLimitNum(Long actId, String uidType, Long userId, Integer count, Long limitNum);

    /**
     * 减少活动期间每人活动总量(Utype)
     *
     * @param actId   活动ID
     * @param uidType 用户类型
     * @param userId  用户ID
     * @param count   数量
     * @return Num
     */
    Long decrActUtypePersonLimitNum(Long actId, String uidType, Long userId, Integer count);

    /**
     * 增加活动期间全部门店活动每天总量
     *
     * @param actId     活动ID
     * @param dateMills 13位时间戳
     * @param count     变动数量
     * @param limitNum  限制数量
     * @return Num
     */
    Long incrActAllStoreDayLimitNum(Long actId, Long dateMills, Integer count, Long limitNum);

    /**
     * 减少增加活动期间全部门店活动每天总量
     *
     * @param actId     活动ID
     * @param dateMills 13位时间戳
     * @param count     变动数量
     * @return Num
     */
    Long decrActAllStoreDayLimitNum(Long actId, Long dateMills, Integer count);

    /**
     * 增加活动期间全部门店活动总量
     *
     * @param actId    活动ID
     * @param count    变动数量
     * @param limitNum 限制数量
     * @return Num
     */
    Long incrActAllStoreLimitNum(Long actId, Integer count, Long limitNum);

    /**
     * 减少活动期间全部门店活动总量
     *
     * @param actId 活动ID
     * @param count 变动数量
     * @return Num
     */
    Long decrActAllStoreLimitNum(Long actId, Integer count);

    /**
     * 增加活动期间每个门店活动每天总量
     *
     * @param actId     活动ID
     * @param orgCode   门店Code
     * @param dateMills 13位时间戳
     * @param count     变动数量
     * @param limitNum  限制数量
     * @return Num
     */
    Long incrActStoreDayLimitNum(Long actId, String orgCode, Long dateMills, Integer count, Long limitNum);

    /**
     * 减少活动期间每个门店活动每天总量
     *
     * @param actId     活动ID
     * @param orgCode   门店Code
     * @param dateMills 13位时间戳
     * @param count     变动数量
     * @return Num
     */
    Long decrActStoreDayLimitNum(Long actId, String orgCode, Long dateMills, Integer count);

    /**
     * 活动期间每个门店活动总量
     *
     * @param orgCode  门店Code
     * @param actId    活动ID
     * @param count    变动数量
     * @param limitNum 限制数量
     * @return Num
     */
    Long incrActStoreLimitNum(Long actId, String orgCode, Integer count, Long limitNum);

    /**
     * 活动期间每个门店活动总量
     *
     * @param orgCode 门店Code
     * @param actId   活动ID
     * @param count   变动数量
     * @return Num
     */
    Long decrActStoreLimitNum(Long actId, String orgCode, Integer count);

    /**
     * 增加直降活动mid每人参与次数key直降
     *
     * @param actId      活动ID
     * @param userId     用户ID
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @param limitNum   限制数量
     * @return NUM
     */
    Long incrOnsaleUserLimitNum(Long actId, Long userId, String skuPackage, Integer count, Long limitNum);

    /**
     * 减少直降活动mid每人参与次数key直降
     *
     * @param actId      活动ID
     * @param userId     用户ID
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @return NUM
     */
    Long decrOnsaleUserLimitNum(Long actId, Long userId, String skuPackage, Integer count);

    /**
     * 增加直降活动手机号每人参与次数
     *
     * @param actId      活动ID
     * @param userId     用户ID
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @param limitNum   限制数量
     * @return 参与次数
     */
    Long incrOnsaleMobileUserLimitNum(Long actId, Long userId, String skuPackage, Integer count, Long limitNum);

    /**
     * 减少直降活动手机号每人参与次数
     *
     * @param actId      活动ID
     * @param userId     用户ID
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @return 参与次数
     */
    Long decrOnsaleMobileUserLimitNum(Long actId, Long userId, String skuPackage, Integer count);

    /**
     * 增加直降活动每天每个门店参与次数
     *
     * @param actId      活动ID
     * @param orgCode    门店Code
     * @param dateMills  13位时间戳
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @param limitNum   限制数量
     * @return 参与次数
     */
    Long incrOnsaleStoreDayLimitNum(Long actId, String orgCode, Long dateMills, String skuPackage, Integer count, Long limitNum);

    /**
     * 减少直降活动每天每个门店参与次数
     *
     * @param actId      活动ID
     * @param orgCode    门店Code
     * @param dateMills  13位时间戳
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @return 参与次数
     */
    Long decrOnsaleStoreDayLimitNum(Long actId, String orgCode, Long dateMills, String skuPackage, Integer count);

    /**
     * 增加直降活动每天全国门店参与次数
     *
     * @param actId      活动ID
     * @param dateMills  13位时间戳
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @param limitNum   限制数量
     * @return 参与次数
     */
    Long incrOnsaleAllStoreDayLimitNum(Long actId, Long dateMills, String skuPackage, Integer count, Long limitNum);

    /**
     * 减少直降活动每天全国门店参与次数
     *
     * @param actId      活动ID
     * @param dateMills  13位时间戳
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @return 参与次数
     */
    Long decrOnsaleAllStoreDayLimitNum(Long actId, Long dateMills, String skuPackage, Integer count);

    /**
     * 增加直降活动期间每个门店参与次数key
     *
     * @param actId      活动ID
     * @param orgCode    门店Code
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @param limitNum   限制数量
     * @return 参与次数
     */
    Long incrOnsaleStoreLimitNum(Long actId, String orgCode, String skuPackage, Integer count, Long limitNum);

    /**
     * 减少直降活动期间每个门店参与次数key
     *
     * @param actId      活动ID
     * @param orgCode    门店Code
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @return 参与次数
     */
    Long decrOnsaleStoreLimitNum(Long actId, String orgCode, String skuPackage, Integer count);

    /**
     * 增加直降活动期间全国门店参与次数key
     *
     * @param actId      活动ID
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @param limitNum   限制数量
     * @return 参与次数
     */
    Long incrOnsaleAllStoreLimitNum(Long actId, String skuPackage, Integer count, Long limitNum);

    /**
     * 减少直降活动期间全国门店参与次数key
     *
     * @param actId      活动ID
     * @param skuPackage sku/packageId
     * @param count      变动数量
     * @return 参与次数
     */
    Long decrOnsaleAllStoreLimitNum(Long actId, String skuPackage, Integer count);

    /**
     * 赠加买赠活动次数
     *
     * @param actId   活动次数
     * @param sku     SKU
     * @param groupId 组ID
     * @param count   数量
     * @return 数量
     */
    Long incrActGiftLimitNum(Long actId, String sku, Long groupId, Integer count);

    /**
     * 减少买赠活动次数
     *
     * @param actId   活动次数
     * @param sku     SKU
     * @param groupId 组ID
     * @param count   数量
     * @return 数量
     */
    Long decrActGiftLimitNum(Long actId, String sku, Long groupId, Integer count);

    /**
     * 赠加加价购活动次数
     *
     * @param actId   活动次数
     * @param sku     SKU
     * @param groupId 组ID
     * @param count   数量
     * @return 数量
     */
    Long incrActBargainLimitNum(Long actId, String sku, Long groupId, Integer count);

    /**
     * 减少加价购活动次数
     *
     * @param actId   活动次数
     * @param sku     SKU
     * @param groupId 组ID
     * @param count   数量
     * @return 数量
     */
    Long decrActBargainLimitNum(Long actId, String sku, Long groupId, Integer count);

    /**
     * 赠加活动主品活动次数
     *
     * @param actId   活动次数
     * @param sku     SKU
     * @param count   数量
     * @return 数量
     */
    Long incrActGoodsLimitNum(Long actId, String sku, Integer count);

    /**
     * 减少活动主品活动次数
     *
     * @param actId   活动次数
     * @param sku     SKU
     * @param count   数量
     * @return 数量
     */
    Long decrActGoodsLimitNum(Long actId, String sku, Integer count);

//    void batchConsumeSubsidyLimit(List<ActUserLimitPo> actUserLimitPoList) throws BizError;

//    void batchRollbackSubsidyLimit(List<ActUserLimitPo> actUserLimitPoList) throws BizError;


    void batchConsumeSubsidyStockV2(List<ActUserLimitPo> actUserLimitPoList) throws BizError;

    void batchRollbackSubsidyLimitV2(List<ActUserLimitPo> actUserLimitPoList) throws BizError;


    List<ActivityInfo> multiGetActivityByIdList(List<Long> actIdList);
}
