package com.xiaomi.nr.promotion.domain.phoenix.service.base;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.provider.PhoenixHuashengProvider;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 三方优惠-联通华盛
 *
 * <AUTHOR>
 * @date 2022/8/1
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class PhoenixHuasheng extends AbstractPhoenixTool {
    @Autowired
    private CheckoutCartTool checkoutCartTool;
    @Autowired
    private ResourceProviderFactory resourceProviderFactory;

    /**
     * 条件是否满足:
     * <p>
     * 如果请求参数传了orgCode和barCode字段，则含义为需要尽心联通华盛结算
     *
     * @param request 请求参数
     * @param context 上下文
     * @return 是否满足 true/false
     * @throws BizError 异常情况业务异常
     */
    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        if (StringUtils.isEmpty(request.getOrgCode()) || request.getUnicomHsBalance() == null || request.getUnicomHsBalance() <= 0L) {
            return false;
        }
        return true;
    }

    /**
     * 优惠结算
     * 1. 获取券信息，获取失败则提示
     * 2. 进行金额计算，计算购物车总金额，如果大于券金额，则扣减全部优惠金额，如果小于券金额，则使用购物车金额
     * 3. 进行金额分摊
     * 4. 如果是submit 则进行金额冻结资源操作
     *
     * @param request         请求参数
     * @param checkoutContext 上下文
     * @throws BizError 业务异常
     */
    @Override
    public void doCheckout(CheckoutPromotionRequest request, CheckoutContext checkoutContext) throws BizError {
        // 优惠金额计算
        int balance = request.getUnicomHsBalance().intValue();
        List<CartItem> cartList = request.getSourceApi() == SourceApi.SUBMIT ? checkoutContext.getCarts() : request.getCartList();
        long reducePrice = calculateReducePrice(cartList, balance, checkoutContext);
        if (reducePrice == 0L) {
            log.warn("use phoenix huasheng coupon zero. userId:{} balance:{}", request.getUserId(), balance);
            return;
        }

        // 进行优惠分摊
        String idKey = PromotionConstant.CARTLIST_PHOENIX_PREFIX + getType().getTypeId();
        checkoutCartTool.divideCartsReduce(reducePrice, cartList, idKey, getType().getTypeId(), (long) getType().getTypeId());

        // 如果是submit 则进行资源构建
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            String goodsName = cartList.get(0).getGoodsName();
            initResource(request, checkoutContext, (int) reducePrice, goodsName);
        }
    }

    private void initResource(CheckoutPromotionRequest request, CheckoutContext checkoutContext, int amount, String goodsName) throws BizError {
        Long orderId = request.getOrderId();
        PhoenixHuashengProvider.ResContent resContent = buildResContent(request, amount, goodsName);
        ResourceObject<PhoenixHuashengProvider.ResContent> resourceObjectObject = buildResource(resContent, orderId);
        PhoenixHuashengProvider couponProvider = (PhoenixHuashengProvider) resourceProviderFactory.getProvider(ResourceType.PHOENIX_HUASHENG);
        couponProvider.initResource(resourceObjectObject);
        checkoutContext.getResourceHandlers().add(couponProvider);
    }

    private PhoenixHuashengProvider.ResContent buildResContent(CheckoutPromotionRequest request, int amount, String goodsName) {
        PhoenixHuashengProvider.ResContent resContent = new PhoenixHuashengProvider.ResContent();
        resContent.setUserId(request.getUserId());
        resContent.setOrderId(request.getOrderId());
        resContent.setOrderTime(request.getOrderTime());
        resContent.setOrgCode(request.getOrgCode());
        resContent.setPayBarCode(request.getPayBarCode());
        resContent.setGoodsName(goodsName);
        resContent.setAmount(amount);
        return resContent;
    }

    private ResourceObject<PhoenixHuashengProvider.ResContent> buildResource(PhoenixHuashengProvider.ResContent content, Long orderId) {
        ResourceObject<PhoenixHuashengProvider.ResContent> resourceObject = new ResourceObject<>();
        resourceObject.setContent(content);
        resourceObject.setResourceId(String.format("phoenix_%s_%s", PromotionToolType.PHOENIX_HUASHENG.getTypeId(), orderId));
        resourceObject.setResourceType(ResourceType.PHOENIX_HUASHENG);
        resourceObject.setPromotionId(-1L);
        resourceObject.setOrderId(orderId);
        resourceObject.setPid(-1L);
        return resourceObject;
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.PHOENIX_HUASHENG;
    }
}
