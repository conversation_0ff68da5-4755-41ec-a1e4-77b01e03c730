package com.xiaomi.nr.promotion.util;

import com.xiaomi.nr.promotion.api.dto.model.CouponInfo;
import com.xiaomi.nr.promotion.constant.CouponConstant;
import com.xiaomi.nr.promotion.enums.CouponCategoryEnum;
import com.xiaomi.nr.promotion.enums.CouponStatEnum;
import com.xiaomi.nr.promotion.model.common.BasicInfo;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

import static com.xiaomi.nr.promotion.util.CouponHelper.getCodeCouponType;

/**
 * 基本信息辅助类
 *
 * <AUTHOR>
 * @date 2021/5/6
 */
public class BasicInfoHelper {
    public static void setCpInfo(BasicInfo b, List<String> codes, Long couponId, int isOnline, Long typ,
                                 long reduceMoney, long reduceExpress) {
        if (b == null) {
            return;
        }
        int offline;
        String stat;
        String code = null;
        Long cid = 0L;
        // 如果是旧有码券
        if (CollectionUtils.isNotEmpty(codes) && codes.size() == 1
                && getCodeCouponType(codes.get(0)) == CouponCategoryEnum.OLDCODE.getType()) {
            offline = CouponConstant.COUPON_ONLINE_DB;
            stat = CouponStatEnum.ODL_LOCK.getVal();
            //如果是读取线下
            if (isOnline == CouponConstant.COUPON_OFFLINE_READ) {
                offline = CouponConstant.COUPON_OFFLINE_DB;
                stat = CouponStatEnum.ODL_USED.getVal();
            }
            code = codes.get(0);
            // 否则为明码券或者新有码券
        } else {
            //默认读取线上
            offline = CouponConstant.COUPON_ONLINE_DB;
            stat = CouponStatEnum.LOCKED.getVal();
            //如果是读取线下
            if (isOnline == CouponConstant.COUPON_OFFLINE_READ) {
                offline = CouponConstant.COUPON_OFFLINE_DB;
                stat = CouponStatEnum.USED.getVal();
            }
            //如果是新有码券
            if (CollectionUtils.isNotEmpty(codes) && codes.size() == 1) {
                code = codes.get(0);
                //否则是无码券
            } else {
                cid = couponId;
            }
        }

        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setOffline(offline);
        couponInfo.setStat(stat);
        couponInfo.setCouponCode(code);
        couponInfo.setCouponId(cid);
        couponInfo.setTyp(String.valueOf(typ));
        couponInfo.setReduceMoney(reduceMoney);
        couponInfo.setReduceExpress(reduceExpress);
        b.setCouponInfo(couponInfo);
    }

    public static void setShipmentCpInfo(BasicInfo b, Long couponId, Long typ, long reduceMoney) {
        if (b == null) {
            return;
        }
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setOffline(CouponConstant.COUPON_ONLINE_DB);
        couponInfo.setStat(CouponStatEnum.LOCKED.getVal());
        couponInfo.setCouponCode("");
        couponInfo.setCouponId(couponId);
        couponInfo.setTyp(String.valueOf(typ));
        couponInfo.setReduceMoney(0L);
        couponInfo.setReduceExpress(reduceMoney);
        b.setShipmentCouponInfo(couponInfo);
    }

    /**
     * 获取券状态
     *
     * @param b 基本信息
     * @return 状态
     */
    public static String cpStat(BasicInfo b) {
        if (b.getCouponInfo() != null) {
            return b.getCouponInfo().getStat();
        }
        return Strings.EMPTY;
    }

    /**
     * 获取劵类型
     *
     * @param b
     * @return
     */
    public static int getCouponCategoryType(BasicInfo b) {
        if (b.getCouponInfo() == null && b.getShipmentCouponInfo() == null) {
            return CouponConstant.IMPOSSIBLE_INT;
        }
        CouponInfo couponInfo = b.getCouponInfo();
        if (couponInfo != null) {
            // 无码劵
            if (couponInfo.getCouponId() > 0 && StringUtils.isBlank(couponInfo.getCouponCode())) {
                return CouponConstant.COUPON_CATEGORY_NOCODE;
            }
        }
        if (b.getShipmentCouponInfo() != null) {
            if (b.getShipmentCouponInfo().getCouponId() > 0) {
                return CouponConstant.COUPON_CATEGORY_NOCODE;
            }
        }

        // 有码劵
        if (couponInfo.getCouponId() <= 0 && !StringUtils.isBlank(couponInfo.getCouponCode())) {
            Integer codeType = getCodeCouponType(couponInfo.getCouponCode());
            // 如果是新版有码劵
            if (CouponCategoryEnum.isNewCodeCoupon(codeType)) {
                return CouponConstant.COUPON_CATEGORY_NEWCODE;
            }
            // 否则是旧版有码券
            return CouponConstant.COUPON_CATEGORY_OLDCODE;
        }

        return CouponConstant.IMPOSSIBLE_INT;
    }


}
