package com.xiaomi.nr.promotion.schedule;

import com.xiaomi.hera.trace.annotation.Trace;
import com.xiaomi.nr.infra.aop.clustertask.ClusterTask;
import com.xiaomi.nr.promotion.resource.impl.ResourceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 资源补偿任务
 *
 * <AUTHOR>
 * @date 2021/8/16
 */
@Component
@Slf4j
public class ResourceRemedialTask {
    /**
     * 资源补偿分布式锁
     */
    private final static String LOCK_RESOURCE_REMEDIAL = "/cnzone/config/nr-mid-platform/promotion/remedial_task_lock";

    @Autowired
    private ResourceManager resourceManager;

    /**
     * 每分钟调用补偿接口，根据订单状态提交或释放用户资产
     * 这里调度间隔时间，需要小于订单接口超时时间，避免错误回滚处于事务中的资源
     * <p>
     * 补偿操作查询 10s 之前锁定的资源列表，回滚之
     */
    @Trace
    @Scheduled(fixedDelay = 1000 * 10, initialDelay = 1000 * 10)
    @ClusterTask(value = "", zkLockPath = LOCK_RESOURCE_REMEDIAL)
    public void remedialResource() {
        long startTime = System.currentTimeMillis();
        log.info("remedialResource start. startTime={}", startTime);
        try {
            resourceManager.remedial();
            log.info("remedialResource end success . ws={}", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("remedialResource err.", e);
        }
    }
}
