package com.xiaomi.nr.promotion.resource.model;

import lombok.Data;
import lombok.ToString;

/**
 * 资源对象
 *
 * <AUTHOR>
 * @date 2021/4/21
 */
@Data
@ToString
public class ResourceObject<T> {
    /**
     * 商品Id
     */
    private Long pid;

    /**
     * 活动的Id,辅助作用
     */
    private Long promotionId;

    /**
     * 订单号
     */
    private Long orderId;

    /**
     * 分支事务id
     */
    private Long transactionId;

    /**
     * 优惠券，赠品，积分
     */
    private ResourceType resourceType;

    /**
     * 数据库唯一表示
     */
    private String resourceId;

    /**
     * 返还资源的状态
     */
    private int returnStatus;

    /**
     * 用于转成json,实例化对象
     */
    private T content;
}

