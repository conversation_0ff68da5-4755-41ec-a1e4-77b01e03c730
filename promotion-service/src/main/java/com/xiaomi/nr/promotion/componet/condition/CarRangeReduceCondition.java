package com.xiaomi.nr.promotion.componet.condition;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.CarRangeReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 范围立减条件判断
 *
 * <AUTHOR>
 * @date 2023/11/26 17:51
 **/
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarRangeReduceCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;

    /**
     * 活动类型
     */
    private PromotionToolType promotionType;

    private Map<String, ActPriceInfo> rangeReduceInfoMap;

    /**
     * 销售来源
     */
    private List<String> saleSources;

    private String rangeReduceRule;


    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (MapUtil.isEmpty(rangeReduceInfoMap)) {
            log.error("reduceInfoMap is empty. actId:{} uid:{}", promotionId, request.getUserId());
            return false;
        }
        List<CartItem> cartList = request.getCartList();
        // 筛选商品数据
        List<GoodsIndex> indexList = goodsActMatch(cartList);

        // 没有符合的商品，不满足活动
        if (CollectionUtils.isEmpty(indexList)) {
            return false;
        }
        context.setGoodIndex(indexList);

        return true;
    }

    /**
     * 筛选商品数据
     *
     * @param cartList
     * @return
     */
    private List<GoodsIndex> goodsActMatch(List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(cartList)) {
            return Collections.emptyList();
        }
        List<GoodsIndex> indexList = Lists.newArrayList();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            if (item.getSsuId() != null && item.getSsuId() != 0L) {
                String ssuId = item.getSsuId().toString();
                // 是否匹配
                ActPriceInfo reduceInfo = rangeReduceInfoMap.get(ssuId);
                if (reduceInfo == null) {
                    continue;
                }
                // 规则判断
                boolean itemCheck = checkRangeReduceItem(item, saleSources);
                if (!itemCheck) {
                    continue;
                }

                indexList.add(new GoodsIndex(item.getItemId(), idx));
            }
        }
        return indexList;
    }

    /**
     * 校验规则
     *
     * @param item 购物车item
     * @param saleSources 订单来源
     * @return
     */
    private boolean checkRangeReduceItem(CartItem item, List<String> saleSources) {
        boolean isMiShop = true;
        // 检查订单来源
        boolean quotaCheck = CartHelper.checkItemActQualify(item, promotionType.getTypeId(), isMiShop, saleSources, null);
        if (!quotaCheck) {
            return false;
        }
        return true;
    }

    /**
     * 加载活动配置信息
     *
     * @param config 活动配置
     */
    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof CarRangeReducePromotionConfig)) {
            log.error("config is not instanceof CarRangeReduceCondition. config:{}", config);
            return;
        }
        CarRangeReducePromotionConfig rangeReduceConfig = (CarRangeReducePromotionConfig) config;
        this.promotionId = rangeReduceConfig.getPromotionId();
        this.promotionType = rangeReduceConfig.getPromotionType();
        this.rangeReduceInfoMap = rangeReduceConfig.getRangeReduceInfoMap();
        this.rangeReduceRule = rangeReduceConfig.getRangeReduceRule();
        // this.saleSources = buyReduceConfig.getSaleSources();
    }
}
