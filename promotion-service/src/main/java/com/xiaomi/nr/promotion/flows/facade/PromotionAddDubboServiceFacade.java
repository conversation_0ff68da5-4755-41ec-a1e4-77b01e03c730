package com.xiaomi.nr.promotion.flows.facade;

import com.xiaomi.nr.promotion.activity.pool.PromotionInstancePool;
import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.nr.promotion.api.dto.enums.PromotionType;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailAddItem;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.OrderPromotionDetailMapper;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.OrderPromotionDetailStatusMapper;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.OrderPromotionDetail;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.OrderPromotionDetailStatus;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.OrderTypeEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.OrderPromotionDetailModel;
import com.xiaomi.nr.promotion.model.ProductShare;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.resource.model.OrderPromotionStatusEnum;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Component
public class PromotionAddDubboServiceFacade {

    @Autowired
    private OrderPromotionDetailMapper orderPromotionDetailMapper;

    @Autowired
    private OrderPromotionDetailStatusMapper orderPromotionDetailStatusMapper;

    @Autowired
    private PromotionInstancePool promotionInstancePool;

    public PromotionBudgetResponse queryCarPromotionBudgetInfo(PromotionBudgetRequest request) throws BizError {
        PromotionBudgetResponse response = new PromotionBudgetResponse();
        List<Integer> filterPromotionTypeList = Arrays.asList(PromotionType.ONSALE.getValue(), PromotionType.RANGE_REDUCE.getValue(),
                PromotionType.EXCHANGE_SUBSIDY.getValue(), PromotionType.COUPON_CASH.getValue());
        List<ReduceDetailAddItem> promotionInfoList = new ArrayList<>();
        List<OrderPromotionDetail> promotionDetailList = orderPromotionDetailMapper.getByOrderId(request.getOrderId());
        for (OrderPromotionDetail promotionDetail : promotionDetailList) {
            String promotionDetailStr = promotionDetail.getPromotionDetail();
            OrderPromotionDetailModel model = GsonUtil.fromJson(promotionDetailStr, OrderPromotionDetailModel.class);
            if (model == null) {
                throw ExceptionHelper.create(GeneralCodes.InternalError, "数据反序列化失败，orderId:" + request.getOrderId());
            }
            if (!filterPromotionTypeList.contains(model.getPromotionType())) {
                continue;
            }
            ReduceDetailAddItem item = new ReduceDetailAddItem();
            item.setPromotionId(model.getPromotionId());
            item.setPromotionType(model.getPromotionType());
            item.setReduce(model.getReduce());
            item.setBudgetApplyNo(model.getBudgetApplyNo());
            item.setLineNum(model.getLineNum());
            promotionInfoList.add(item);
        }
        response.setPromotionInfoList(promotionInfoList);
        return response;
    }

    @Transactional(transactionManager = "promotionUserConfigTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public CheckoutPromotionAddResponse checkoutPromotion(CheckoutPromotionAddRequest request) throws BizError {
        List<OrderPromotionDetailStatus> promotionDetailStatusList = new ArrayList<>();
        // 1、查询状态表快照
        List<OrderPromotionDetailStatus> lockList = orderPromotionDetailStatusMapper.getByOrderIdAndOrderTypeAndStatus(request.getOrderId(),
                OrderTypeEnum.ADD_ORDER.getOrderType(), OrderPromotionStatusEnum.LOCK.getValue());
        for (ReduceDetailAddItem item : request.getPromotionInfoList()) {
            // 校验：根据活动信息去缓存池中找对应的活动
            List<ActivityTool> activityToolList = promotionInstancePool.getCurrentTools(Collections.singletonList(item.getPromotionId()));
            if (activityToolList.isEmpty()) {
                log.error("PromotionAddDubboService.checkoutPromotion error, not exist promotionId:{} , orderId:{}", item.getPromotionId(), request.getOrderId());
                throw ExceptionHelper.create(ErrCode.PROMOTION_ACTIVITY_INVALID, "活动失效，promotionId:" + item.getPromotionId());
            }
            ActivityTool activityTool = activityToolList.getFirst();
            Map<String, ActPriceInfo> priceInfoMap = activityTool.getActivityDetail().getPriceInfoMap();
            ActPriceInfo actPriceInfo = priceInfoMap.get(item.getSsuId().toString());
            if (actPriceInfo == null || !actPriceInfo.getPrice().equals(item.getReduce())) {
                log.error("PromotionAddDubboService.checkoutPromotion error, Activity failure promotionId:{} , orderId:{}", item.getPromotionId(), request.getOrderId());
                throw ExceptionHelper.create(ErrCode.PROMOTION_ACTIVITY_INVALID, "活动价格不一致，promotionId:" + item.getPromotionId());
            }
            // 2、同类型活动互斥：如果已经存在锁定的优惠，拒绝再次锁定，防止重复锁定。
            Optional<OrderPromotionDetailStatus> any = lockList.stream().filter(f -> f.getPromotionType().equals(item.getPromotionType())).findAny();
            if (any.isPresent()) {
                log.error("checkoutPromotion error. orderId:{}, promotionId:{}", request.getOrderId(), item.getPromotionId());
                throw ExceptionHelper.create(GeneralCodes.InternalError, "存在已经锁定的追加优惠，禁止重复锁定");
            }

            // 构造优惠分摊模型
            OrderPromotionDetailModel promotionDetailModel = constructPromotionShareModel(item, actPriceInfo);
            // 构造优惠明细
            OrderPromotionDetailStatus detailStatus = new OrderPromotionDetailStatus();
            detailStatus.setOrderId(request.getOrderId());
            detailStatus.setOrderScene(BizPlatformEnum.CAR.getValue());
            detailStatus.setOrderType(OrderTypeEnum.ADD_ORDER.getOrderType());
            detailStatus.setPromotionId(item.getPromotionId());
            detailStatus.setPromotionType(item.getPromotionType());
            detailStatus.setPromotionDetail(GsonUtil.toJson(promotionDetailModel));
            detailStatus.setUserId(request.getUserId());
            detailStatus.setStatus(OrderPromotionStatusEnum.LOCK.getValue());
            promotionDetailStatusList.add(detailStatus);
        }
        // 3、锁定优惠数据新增
        orderPromotionDetailStatusMapper.insertList(promotionDetailStatusList);
        // 4、构造返回信息
        CheckoutPromotionAddResponse response = new CheckoutPromotionAddResponse();
        response.setResult(true);
        return response;
    }

    /**
     * 构造优惠分摊模型
     *
     * @param item         新增优惠
     * @param actPriceInfo 优惠明细
     * @return 分摊模型
     */
    private OrderPromotionDetailModel constructPromotionShareModel(ReduceDetailAddItem item, ActPriceInfo actPriceInfo) {
        OrderPromotionDetailModel model = new OrderPromotionDetailModel();
        model.setPromotionId(item.getPromotionId());
        model.setPromotionType(item.getPromotionType());
        model.setReduce(item.getReduce());
        model.setBudgetApplyNo(actPriceInfo.getBudgetApplyNo());
        model.setLineNum(actPriceInfo.getLineNum());
        List<ProductShare> productShareList = new ArrayList<>();
        model.setProductShareList(productShareList);
        ProductShare productShare = new ProductShare();
        productShare.setProductId(item.getSsuId());
        productShare.setCount(1);
        productShare.setReduce(item.getReduce());
        productShareList.add(productShare);
        return model;
    }

    @Transactional(transactionManager = "promotionUserConfigTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public SubmitPromotionAddResponse submitPromotion(SubmitPromotionAddRequest request) throws BizError {
        SubmitPromotionAddResponse response = new SubmitPromotionAddResponse();
        List<ReduceDetailAddItem> reduceItemList = new ArrayList<>();
        response.setReduceItemList(reduceItemList);
        List<ReduceDetailAddItem> promotionInfoList = request.getPromotionInfoList();
        // 目前每次请求只有1个活动，活动列表size为1，暂时无需考虑性能问题。后续若支持同时提交多个活动，需考虑性能优化
        for (ReduceDetailAddItem item : promotionInfoList) {
            // 异常处理：重复调用commit场景处理：订单调交易超时，交易调促销提交成功，订单会再次调用交易重试，此时应该抛异常-》活动已提交
            // 查询明细中是否存在生效的记录,改配会删除明细
            List<OrderPromotionDetail> promotionDetailList = orderPromotionDetailMapper.getByOrderIdAndPromotion(request.getOrderId(), OrderTypeEnum.ADD_ORDER.getOrderType(),
                    item.getPromotionId(), item.getPromotionType());
            // 查询最新状态数据是否为commit
            OrderPromotionDetailStatus recentStatus = orderPromotionDetailStatusMapper.getRecentStatus(request.getOrderId(), OrderTypeEnum.ADD_ORDER.getOrderType(),
                    item.getPromotionId(), item.getPromotionType());
            // 如果新状态数据为commit && 明细记录存在（未改配）-》》证明重复调用commit
            if (!promotionDetailList.isEmpty() && (recentStatus != null && recentStatus.getStatus() != null && recentStatus.getStatus().equals(OrderPromotionStatusEnum.COMMIT.getValue()))) {
                log.error("PromotionAddDubboService.submitPromotion error, already exist activity orderId:{}, item:{} ", request.getOrderId(), GsonUtil.toJson(item));
                throw ExceptionHelper.create(ErrCode.PROMOTION_ACTIVITY_COMMIT, "已经存在提交的活动，promotionId:" + item.getPromotionId());
            }
            // 查询锁定快照
            List<OrderPromotionDetailStatus> snapshotList = orderPromotionDetailStatusMapper.getByOrderAndPromotion(request.getOrderId(), OrderTypeEnum.ADD_ORDER.getOrderType(),
                    item.getPromotionId(), item.getPromotionType(), OrderPromotionStatusEnum.LOCK.getValue());
            if (snapshotList.isEmpty()) {
                // 异常处理：如果锁定快照没有数据，并且最新状态为回滚-》》 则证明此次提交属于提交重试
                if (recentStatus != null && recentStatus.getStatus() != null && recentStatus.getStatus().equals(OrderPromotionStatusEnum.ROLLBACK.getValue())) {
                    // 回滚快照更新入库
                    OrderPromotionDetail insertDetail = recentStatus.statusConvertDetail();
                    List<OrderPromotionDetail> insertList = new ArrayList<>();
                    insertList.add(insertDetail);
                    orderPromotionDetailMapper.insertList(insertList);
                    orderPromotionDetailStatusMapper.updateStatusByOrderAndPromotion(request.getOrderId(), OrderTypeEnum.ADD_ORDER.getOrderType(),
                            item.getPromotionId(), item.getPromotionType(), OrderPromotionStatusEnum.COMMIT.getValue(), OrderPromotionStatusEnum.ROLLBACK.getValue());
                    ReduceDetailAddItem reduceDetailAddItem = convertDetailToResponse(insertDetail);
                    reduceItemList.add(reduceDetailAddItem);
                } else {
                    // 如果快照没有数据，最新状态也不为回滚-》》 则证明提交的活动不存在，未锁定
                    log.error("PromotionAddDubboService.submitPromotion error, not exist activity orderId:{}, item:{} ", request.getOrderId(), GsonUtil.toJson(item));
                    throw ExceptionHelper.create(ErrCode.PROMOTION_ACTIVITY_INVALID, "提交的活动未锁定，promotionId:" + item.getPromotionId());
                }
            } else {
                // 存在锁定快照，直接更新入库
                OrderPromotionDetail insertDetail = snapshotList.getFirst().statusConvertDetail();
                List<OrderPromotionDetail> insertList = new ArrayList<>();
                insertList.add(insertDetail);
                orderPromotionDetailMapper.insertList(insertList);
                orderPromotionDetailStatusMapper.updateStatusByOrderAndPromotion(request.getOrderId(), OrderTypeEnum.ADD_ORDER.getOrderType(),
                        item.getPromotionId(), item.getPromotionType(), OrderPromotionStatusEnum.COMMIT.getValue(), OrderPromotionStatusEnum.LOCK.getValue());
                ReduceDetailAddItem reduceDetailAddItem = convertDetailToResponse(insertDetail);
                reduceItemList.add(reduceDetailAddItem);
            }
        }
        return response;
    }

    private ReduceDetailAddItem convertDetailToResponse(OrderPromotionDetail detail) throws BizError {
        String promotionDetail = detail.getPromotionDetail();
        OrderPromotionDetailModel model = GsonUtil.fromJson(promotionDetail, OrderPromotionDetailModel.class);
        if (model == null) {
            log.error("PromotionAddDubboService.submitPromotion.convertDetailToResponse error, promotion detail data convert fail , promotionDetail: " + promotionDetail);
            throw ExceptionHelper.create(ErrCode.PROMOTION_ACTIVITY_INVALID, "数据转换失败，itepromotionDetailm:" + promotionDetail);
        }
        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setPromotionId(model.getPromotionId());
        item.setPromotionType(model.getPromotionType());
        item.setReduce(model.getReduce());
        List<Long> productIdList = new ArrayList<>();
        model.getProductShareList().forEach(productShare -> {
            productIdList.add(productShare.getProductId());
        });
        item.setSsuId(productIdList.getFirst());
        return item;
    }

    @Transactional(transactionManager = "promotionUserConfigTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public RollbackPromotionAddResponse rollbackPromotion(RollbackPromotionAddRequest request) throws BizError {
        if (request.getOrderId() == null) {
            log.error("orderId is null. request:{}", GsonUtil.toJson(request));
            throw ExceptionHelper.create(GeneralCodes.ParamError, "订单ID为空");
        }
        long orderId = request.getOrderId();
        List<ReduceDetailAddItem> promotionInfoList = request.getPromotionInfoList();
        for (ReduceDetailAddItem item : promotionInfoList) {
            // 逆向调用促销驳回。LOCK->EXAMINE_REJECT
            if (request.getExamineReject() != null && request.getExamineReject()) {
                orderPromotionDetailStatusMapper.updateStatusByOrderAndPromotion(orderId, OrderTypeEnum.ADD_ORDER.getOrderType(),
                        item.getPromotionId(), item.getPromotionType(), OrderPromotionStatusEnum.EXAMINE_REJECT.getValue(), OrderPromotionStatusEnum.LOCK.getValue());
            }else {//交易调用促销回滚。COMMIT->ROLLBACK
                OrderPromotionDetailStatus recentStatus = orderPromotionDetailStatusMapper.getRecentStatus(request.getOrderId(), OrderTypeEnum.ADD_ORDER.getOrderType(),
                        item.getPromotionId(), item.getPromotionType());
                if (recentStatus!=null){
                    orderPromotionDetailStatusMapper.updateStatusByIdAndStatus(orderId, recentStatus.getId(),OrderPromotionStatusEnum.ROLLBACK.getValue(), OrderPromotionStatusEnum.COMMIT.getValue());
                }
            }
            // 删除历史明细
            orderPromotionDetailMapper.deleteByOrderAndPromotion(request.getOrderId(), OrderTypeEnum.ADD_ORDER.getOrderType(), item.getPromotionId(), item.getPromotionType());
        }
        RollbackPromotionAddResponse response = new RollbackPromotionAddResponse();
        response.setResult(true);
        return response;
    }


    @Transactional(transactionManager = "promotionUserConfigTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public CancelPromotionAddResponse cancelPromotion(CancelPromotionAddRequest request) throws BizError {
        // 改配删除所有追加优惠数据
        orderPromotionDetailMapper.deleteByOrderIdAndOrderType(request.getOrderId(), OrderTypeEnum.ADD_ORDER.getOrderType());
        CancelPromotionAddResponse response = new CancelPromotionAddResponse();
        response.setResult(true);
        return response;
    }

}
