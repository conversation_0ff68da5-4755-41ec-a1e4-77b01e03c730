package com.xiaomi.nr.promotion.entity.redis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * 礼品卡基本信息
 *
 * <AUTHOR>
 * 2021/5/8 11:14 上午
 */
@Data
public class EcardBaseType {
    private Long id = 0L;
    /**
     * e.g. "ecard"
     */
    @SerializedName("ecard_type")
    private String ecardType = "";

    /**
     * 小米网电子礼品卡20元
     */
    private String name = "";

    /**
     * 描述
     */
    private String desc = "";

    /**
     * 范围描述
     */
    @SerializedName("range_desc")
    private String rangeDesc = "";

    /**
     * 是否开发票
     */
    private Boolean invoice;
}
