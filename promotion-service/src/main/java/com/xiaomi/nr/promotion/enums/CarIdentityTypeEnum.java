package com.xiaomi.nr.promotion.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum CarIdentityTypeEnum {

    /**
     * 车商城会员
     */
    CAR_SHOP_VIP(1, 5),

    /**
     * 用车权益
     */
    USAGE_EQUITY(2, 6),

    ;

    private final int code;

    private final int priority;

    private static Map<Integer, CarIdentityTypeEnum> ENUM_MAP = new HashMap<>();

    static {
        ENUM_MAP.put(1, CarIdentityTypeEnum.CAR_SHOP_VIP);
        ENUM_MAP.put(2, CarIdentityTypeEnum.USAGE_EQUITY);
    }

    public static CarIdentityTypeEnum getEnumByCode(int code) {
        return ENUM_MAP.get(code);
    }
}
