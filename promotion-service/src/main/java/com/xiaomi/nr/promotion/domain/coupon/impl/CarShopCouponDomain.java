package com.xiaomi.nr.promotion.domain.coupon.impl;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Request;
import com.xiaomi.nr.promotion.domain.coupon.AbstractCouponDomain;
import com.xiaomi.nr.promotion.domain.coupon.facade.CarShopCouponFacade;
import com.xiaomi.nr.promotion.domain.coupon.facade.CouponFacadeInterFace;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CarShopCouponDomain extends AbstractCouponDomain {

    @Autowired
    @Qualifier("carShopCouponFacade")
    protected CarShopCouponFacade carShopCouponFacade;

    @Override
    protected CouponFacadeInterFace getCouponFacde() {
        return carShopCouponFacade;
    }

    /**
     * 处理结账逻辑，根据不同的接口类型执行相应的操作
     *
     * @param domainCheckoutContext 包含结账所需的上下文信息
     * @throws Exception 处理过程中可能抛出的异常
     */
    @Override
    public void checkout(DomainCheckoutContext domainCheckoutContext) throws Exception {
        FromInterfaceEnum fromInterface = domainCheckoutContext.getFromInterface();
        CheckoutPromotionRequest request = domainCheckoutContext.getRequest();
        CheckoutPromotionV2Request requestV2 = domainCheckoutContext.getRequestV2();
        CheckoutPromotionResponse response = domainCheckoutContext.getResponse();
        CheckoutContext checkoutContext = domainCheckoutContext.getContext();
        // 下单逻辑
        if (fromInterface.equals(FromInterfaceEnum.CHECKOUT_ORDER)) {
            carShopCouponFacade.checkoutForSubmit(request, response, checkoutContext);
        }
        // 结算页逻辑
        if (fromInterface.equals(FromInterfaceEnum.CHECKOUT_PAGE)) {
            if (CollectionUtils.isNotEmpty(requestV2.getCouponCodes())) {
                carShopCouponFacade.checkoutForSubmit(requestV2, response, checkoutContext);
            } else {
                carShopCouponFacade.checkoutForCouponList(requestV2, response, checkoutContext);
            }
        }

        // 加购
        if (fromInterface.equals(FromInterfaceEnum.CHECKOUT_CART)) {
            if (domainCheckoutContext.getCartRequest().getGetCouponList()) {
                carShopCouponFacade.checkoutForCouponList(requestV2, response, checkoutContext);
            }
        }

    }

}
