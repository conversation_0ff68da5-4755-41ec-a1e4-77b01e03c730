package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.ProductDepartmentEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.CartItemChild;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.entity.redis.LimitRule;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.OnsalePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.model.promotionconfig.common.MergedCartItem;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.ChannelsHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.*;

/**
 * 直降条件判断
 *
 * <AUTHOR>
 * @date 2021/3/22
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class OnsaleCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 活动类型
     */
    private PromotionToolType promotionType;
    /**
     * 直降信息
     */
    private Map<String, ActPriceInfo> onsaleInfoMap;
    /**
     * 销售来源
     */
    private List<String> saleSources;
    /**
     * 活动密码
     */
    private String accessCode;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    /**
     * - 遍历购物车,合并同一个sku或套装的item
     * - 过滤不能参加直降的. 查找是否找到对应的直降信息， 以及限购数是否符合
     * - 筛选购物车中可以参加直降的列表
     *
     * @param request 请求参数
     * @param context 活动内上下文
     * @return 是否满足
     */
    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) {
        Long uid = request.getUserId();
        if (MapUtil.isEmpty(onsaleInfoMap)) {
            log.error("onsaleInfoMap is empty. actId:{} uid:{}", promotionId, uid);
            return false;
        }

        List<CartItem> cartList = request.getCartList();
        String uidType = request.getUidType();
        String orgCode = request.getOrgCode();
        boolean online = StringUtils.isEmpty(orgCode);

        // 合并同sku 或 packageId 商品. key: sku/packageId val: summarize
        Map<String, MergedCartItem> mergeCartItemMap = mergeCartList(cartList, online, orgCode);

        // 过滤不可以参加活动的
        mergeCartItemMap = filterMergeCartItemMap(mergeCartItemMap, uid, uidType, orgCode);

        // 查找可以参加的购物车
        List<GoodsIndex> indexList = findFillGoodsList(cartList, mergeCartItemMap, online, orgCode);

        // 主附品强绑定的商品，其子节点的sku满足即可
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            if (ChannelsHelper.isBindMainAccessory(request.getChannel(),item.getDepartment(),item.getBindMainAccessory())) {
                for (CartItemChild child : item.getChilds()) {
                    boolean isExist = indexList.stream().anyMatch(p -> p.getItemId().equals(item.getItemId()));
                    if (onsaleInfoMap.containsKey(child.getSku())&&!isExist){
                        indexList.add(new GoodsIndex(item.getItemId(),idx));
                    }
                }
            }
        }

        // 没有符合的商品，不满足活动
        if (CollectionUtils.isEmpty(indexList)) {
            return false;
        }

        context.setGoodIndex(indexList);
        return true;
    }

    /**
     * 合并同SKU / PackageId 的商品和购物车
     *
     * @param cartList 购物车列表
     * @param online   是否线上
     * @return key: skuPackage val: 合并后的cartItem(主要是数量合并）
     */
    private Map<String, MergedCartItem> mergeCartList(List<CartItem> cartList, boolean online, String orgCode) {
        Map<String, MergedCartItem> mergeCartList = new HashMap<>(cartList.size());
        cartList.forEach(item -> handleCartItem(item, mergeCartList, online, orgCode));
        return mergeCartList;
    }

    private void handleCartItem(CartItem item, Map<String, MergedCartItem> mergeCartList, boolean online, String orgCode) {
        int activityType = promotionType.getTypeId();
        boolean itemQualify = CartHelper.checkItemActQualify(item, activityType, online, saleSources, accessCode);
        if (!itemQualify) {
            return;
        }
        boolean itemOnsaleQualify = CartHelper.isOnsaleActQualifyItem(item, orgCode);
        if (!itemOnsaleQualify) {
            return;
        }
        ActPriceInfo onsaleInfo = onsaleInfoMap.get(CartHelper.getSkuPackageForOnsale(item));
        if (onsaleInfo == null || (onsaleInfo.getPrice() >= (item.getCartPrice() + item.getOnSaleBookingPrice()))) {
            return;
        }

        // 合并数量
        String skuPackage = CartHelper.getSkuPackageForOnsale(item);
        MergedCartItem mergeItem = mergeCartList.get(skuPackage);
        if (mergeItem != null) {
            mergeItem.setCounts(mergeItem.getCounts() + item.getCount());
            return;
        }
        mergeItem = new MergedCartItem();
        mergeItem.setSkuPackage(Long.valueOf(skuPackage));
        mergeItem.setCartPrice(item.getCartPrice());
        mergeItem.setCounts(item.getCount());
        mergeItem.setCanJoin(Boolean.FALSE);
        mergeItem.setBookingPrice(item.getOnSaleBookingPrice());
        mergeCartList.put(skuPackage, mergeItem);
    }

    /**
     * 过滤不合法的商品
     *
     * @param mergeCartMap 合并后的Map
     * @param uid          用户ID
     * @param uidType      用户类型
     * @param orgCode      门店Code
     * @return 过滤后的Map
     */
    private Map<String, MergedCartItem> filterMergeCartItemMap(Map<String, MergedCartItem> mergeCartMap, Long uid,
                                                               String uidType, String orgCode) {
        if (MapUtil.isEmpty(mergeCartMap)) {
            return Collections.emptyMap();
        }

        Map<String, MergedCartItem> newMap = new HashMap<>(mergeCartMap.size());
        mergeCartMap.forEach((skuPackage, mergedCartItem) -> {
            ActPriceInfo onsaleInfo = onsaleInfoMap.get(String.valueOf(mergedCartItem.getSkuPackage()));
            if (onsaleInfo == null) {
                return;
            }
            boolean matchQuota = isMatchQuota(onsaleInfo, mergedCartItem, uid, uidType, orgCode);
            if (!matchQuota) {
                return;
            }
            mergedCartItem.setCanJoin(Boolean.TRUE);
            newMap.put(skuPackage, mergedCartItem);
        });
        return newMap;
    }

    /**
     * 是否符合条件：价格， 门店限制
     *
     * @param onsale   直降信息
     * @param cartItem 购物车Item
     * @param uid      用户ID
     * @param uidType  用户类型
     * @param orgCode  门店Code
     * @return true/false
     */
    private boolean isMatchQuota(ActPriceInfo onsale, MergedCartItem cartItem, Long uid, String uidType, String orgCode) {
        Long skuPackage = onsale.getSkuPackage();
        if (!Objects.equals(skuPackage, cartItem.getSkuPackage())) {
            return false;
        }
        // 校验价格，加购价格小于等于直降后价格则该商品不参与直降活动，定金预售需加上定金的价格
        if ((cartItem.getCartPrice() + cartItem.getBookingPrice()) <= onsale.getPrice()) {
            return false;
        }
        // 是否有限制规则
        if (BooleanEnum.isNo(onsale.getIsLimit())) {
            return true;
        }
        // 校验限购数
        LimitRule limitRule = onsale.getLimitRule();
        return checkOnsaleActLimit(cartItem.getCounts(), limitRule, uid, uidType, orgCode, skuPackage);
    }

    private boolean checkOnsaleActLimit(Integer count, LimitRule limitRule, Long uid, String uidType, String orgCode, Long skuPackage) {
        //校验每单限购数
        if (limitRule.getOrderLimitBuy() > 0 && count > limitRule.getOrderLimitBuy()) {
            return false;
        }

        //校验每人限购数
        if (uid > 0 && limitRule.getPersonLimit() > 0) {
            Integer usedNum;
            if (StringUtils.isNotEmpty(uidType)) {
                usedNum = activityRedisDao.getOnsaleMobileUserLimitNum(promotionId, uid, String.valueOf(skuPackage));
            } else {
                usedNum = activityRedisDao.getOnsaleUserLimitNum(promotionId, uid, String.valueOf(skuPackage));
            }
            if ((usedNum + count) > limitRule.getPersonLimit()) {
                return false;
            }
        }

        long timeNow = Instant.now().toEpochMilli();
        // 校验每天每个门店限量
        if (limitRule.getDayLimitOne() > 0) {
            Integer usedNum = activityRedisDao.getOnsaleStoreDayLimitNum(promotionId, orgCode, timeNow, String.valueOf(skuPackage));
            if ((usedNum + count) > limitRule.getDayLimitOne()) {
                return false;
            }
        }
        // 校验每天全部门店限量
        if (limitRule.getDayLimitAll() > 0) {
            Integer usedNum = activityRedisDao.getOnsaleAllStoreDayLimitNum(promotionId, timeNow, String.valueOf(skuPackage));
            if ((usedNum + count) > limitRule.getDayLimitAll()) {
                return false;
            }
        }
        // 校验活动期间每个门店限量
        if (limitRule.getActivityLimitOne() > 0) {
            Integer usedNum = activityRedisDao.getOnsaleStoreLimitNum(promotionId, orgCode, String.valueOf(skuPackage));
            if ((usedNum + count) > limitRule.getActivityLimitOne()) {
                return false;
            }
        }
        // 校验活动期间全部门店限量
        if (limitRule.getActivityLimitAll() > 0) {
            Integer usedNum = activityRedisDao.getOnsaleAllStoreLimitNum(promotionId, String.valueOf(skuPackage));
            if ((usedNum + count) > limitRule.getActivityLimitAll()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 筛选出可以改价的item index List
     *
     * @param cartList         购物车列表
     * @param mergeCartItemMap 合并的Map
     * @param online           是否线上
     * @return goodsIndList
     */
    private List<GoodsIndex> findFillGoodsList(List<CartItem> cartList, Map<String, MergedCartItem> mergeCartItemMap, boolean online, String orgCode) {
        List<GoodsIndex> indexList = new ArrayList<>();
        // 遍历购物车，将满足的商品放入
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            int activityType = promotionType.getTypeId();
            boolean itemQualify = CartHelper.checkItemActQualify(item, activityType, online, saleSources, accessCode);
            if (!itemQualify) {
                continue;
            }
            boolean itemOnsaleQualify = CartHelper.isOnsaleActQualifyItem(item, orgCode);
            if (!itemOnsaleQualify) {
                continue;
            }
            String skuPackage = CartHelper.getSkuPackageForOnsale(item);
            if (!mergeCartItemMap.containsKey(skuPackage)) {
                continue;
            }
            ActPriceInfo onsaleInfo = onsaleInfoMap.get(String.valueOf(skuPackage));
            if (onsaleInfo == null || (onsaleInfo.getPrice() >= (item.getCartPrice() + item.getOnSaleBookingPrice()))) {
                continue;
            }
            GoodsIndex goodsIndex = new GoodsIndex(item.getItemId(), idx);
            indexList.add(goodsIndex);
        }
        return indexList;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof OnsalePromotionConfig)) {
            log.error("config is not instanceof OnsalePromotionConfig. config:{}", config);
            return;
        }
        OnsalePromotionConfig onsaleConfig = (OnsalePromotionConfig) config;
        this.promotionId = onsaleConfig.getPromotionId();
        this.onsaleInfoMap = onsaleConfig.getOnsaleInfoMap();
        this.promotionType = onsaleConfig.getPromotionType();
        this.saleSources = onsaleConfig.getSaleSources();
        this.accessCode = onsaleConfig.getAccessCode();
    }
}
