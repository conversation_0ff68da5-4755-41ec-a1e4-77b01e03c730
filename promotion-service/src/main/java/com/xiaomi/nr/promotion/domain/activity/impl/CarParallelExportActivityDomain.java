package com.xiaomi.nr.promotion.domain.activity.impl;

import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.domain.activity.AbstractActivityDomain;
import com.xiaomi.nr.promotion.domain.activity.facade.CarParallelExportActivityFacade;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 汽车平行出口活动领域
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class CarParallelExportActivityDomain extends AbstractActivityDomain {

    @Autowired
    private CarParallelExportActivityFacade facade;

    @Autowired
    @Qualifier("checkoutAsyncTaskExecutor")
    private ThreadPoolTaskExecutor checkoutAsyncTaskExecutor;

    @Override
    public void checkout(DomainCheckoutContext domainCheckoutContext) throws BizError, ExecutionException, InterruptedException, TimeoutException {

        // 1、商品分组 初始化domainContext
        CheckoutContext checkoutContext = domainCheckoutContext.getContext();
        CheckoutPromotionRequest request = domainCheckoutContext.getRequest();
        Map<String, Set<String>> cartItemIdGroup = splitItemIdGroup(request.getCartList());
        Map<String, DomainCheckoutContext> domainContextMap = initDomainContext(cartItemIdGroup.keySet(), domainCheckoutContext);

        // 2、并发过促销活动
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        for (String parentItemId : cartItemIdGroup.keySet()) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 2.1、复制request
                    DomainCheckoutContext domainContext = domainContextMap.get(parentItemId);
                    domainContext.setRequest(copyRequest(request, cartItemIdGroup.get(parentItemId)));

                    // 2.2、搜索促销活动，进行结算
                    List<ActivityTool> activityTools = facade.activitySearch(domainContext);
                    super.executeActivity(domainContext, activityTools);
                } catch (BizError e) {
                    throw new BaseException(e.getCode(), e.getMsg());
                }
            }, checkoutAsyncTaskExecutor);
            futureList.add(future);
        }

        // 3、整合future，整体等待2s
        CompletableFuture<Void> allTasks = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        allTasks.get(2000, TimeUnit.MILLISECONDS);

        // 4、request中cartItem信息根据count修正（reduceItemList）
        Map<String, CartItem> calCartItemMap = new HashMap<>();
        for (DomainCheckoutContext domainContext : domainContextMap.values()) {
            List<CartItem> cartList = domainContext.getRequest().getCartList();
            for (CartItem cartItem : cartList) {
                calCartItemMap.put(cartItem.getItemId(), cartItem);
            }
        }
        for (CartItem cartItem : request.getCartList()) {
            CartItem calCartItem = calCartItemMap.get(cartItem.getItemId());
            cartItem.setCartPrice(calCartItem.getCartPrice());
            Integer count = cartItem.getCount();
            List<ReduceDetailItem> reduceItemList = calCartItem.getReduceItemList();
            for (ReduceDetailItem reduceDetailItem : reduceItemList) {
                reduceDetailItem.setReduce(reduceDetailItem.getReduceSingle() * count);
            }
            cartItem.setReduceItemList(reduceItemList);
        }

        // 5、promotionInfo去重
        List<PromotionInfo> originPromotion = domainCheckoutContext.getContext().getPromotion();
        Map<String, PromotionInfo> promotionInfoMap = new HashMap<>();
        for (DomainCheckoutContext curDomainContext : domainContextMap.values()) {
            CheckoutContext curCheckoutContext = curDomainContext.getContext();
            for (PromotionInfo promotion : curCheckoutContext.getPromotion()) {
                PromotionInfo oldPromotion = promotionInfoMap.get(promotion.getPromotionId());
                // 已有同一活动促销信息，进行合并joinedItemId、parentItemId
                if (Objects.nonNull(oldPromotion)) {
                    if (CollectionUtils.isNotEmpty(promotion.getJoinedItemId())) {
                        List<String> oldJoinedItemId = Optional.ofNullable(oldPromotion.getJoinedItemId()).orElse(new ArrayList<>());
                        oldJoinedItemId.addAll(promotion.getJoinedItemId());
                        oldPromotion.setJoinedItemId(oldJoinedItemId);
                    }
                    if (CollectionUtils.isNotEmpty(promotion.getParentItemId())) {
                        List<String> oldParentItemId = Optional.ofNullable(oldPromotion.getParentItemId()).orElse(new ArrayList<>());
                        oldParentItemId.addAll(promotion.getParentItemId());
                        oldPromotion.setParentItemId(oldParentItemId);
                    }
                } else {
                    promotionInfoMap.put(promotion.getPromotionId(), promotion);
                }
            }
        }
        originPromotion.addAll(promotionInfoMap.values());

        // 6、下单时对checkoutContext carts进行赋值
        if (Objects.equals(request.getSourceApi(), SourceApi.SUBMIT) && Objects.isNull(checkoutContext.getCarts())) {
            List<CartItem> simpleCarts = copyList(request.getCartList());
            checkoutContext.setCarts(simpleCarts);
        }
    }

    /**
     * 初始化
     *
     * @param itemIdSet
     * @param originDomainContext
     * @return
     */
    private Map<String, DomainCheckoutContext> initDomainContext(Set<String> itemIdSet, DomainCheckoutContext originDomainContext) {
        Map<String, DomainCheckoutContext> domainCheckoutContextMap = new HashMap<>();
        for (String itemId : itemIdSet) {
            DomainCheckoutContext curDomainContext = new DomainCheckoutContext();

            // checkoutContext拷贝
            CheckoutContext checkoutContext = originDomainContext.getContext();
            CheckoutContext curCheckoutContext = new CheckoutContext();
            curCheckoutContext.setBizPlatform(checkoutContext.getBizPlatform());
            curCheckoutContext.setFromInterface(checkoutContext.getFromInterface());
            curCheckoutContext.setExternalDataMap(checkoutContext.getExternalDataMap());
            curDomainContext.setContext(curCheckoutContext);

            domainCheckoutContextMap.put(itemId, curDomainContext);
        }

        return domainCheckoutContextMap;
    }

    /**
     * 复制并修改CheckoutPromotionRequest对象，将其购物车列表中的商品数量设置为1
     *
     * @param request   需要复制的CheckoutPromotionRequest对象
     * @param filterItemIdSet 包含需要保留的商品ID的集合
     */
    private CheckoutPromotionRequest copyRequest(CheckoutPromotionRequest request, Set<String> filterItemIdSet) {
        CheckoutPromotionRequest curRequest = SerializationUtils.clone(request);
        List<CartItem> curCartList = curRequest.getCartList().stream().filter(cartItem -> filterItemIdSet.contains(cartItem.getItemId()))
                .peek(cartItem -> cartItem.setCount(1))
                .collect(Collectors.toList());
        curRequest.setCartList(curCartList);
        return curRequest;
    }

    /**
     * cartList分组
     *
     * @param cartList cartList
     * @return 分组映射
     */
    private Map<String, Set<String>> splitItemIdGroup(List<CartItem> cartList) {
        // 分组映射：parentItemId -> cartList
        Map<String, Set<String>> groupMap = new LinkedHashMap<>();
        for (CartItem cartItem : cartList) {
            String itemId = cartItem.getItemId();
            String parentItemId = cartItem.getParentItemId();
            // parentItemId为空则为车型版本ssu，不为空则配置ssu
            String groupKey = StringUtils.isBlank(parentItemId) ? itemId : parentItemId;
            Set<String> itemIdSet = groupMap.getOrDefault(groupKey, new HashSet<>());
            itemIdSet.add(itemId);
            groupMap.put(groupKey, itemIdSet);
        }
        return groupMap;
    }

    private List<CartItem> copyList(List<CartItem> cartList) {
        List<CartItem> simCarts = new ArrayList<>();
        for (CartItem item : cartList) {
            CartItem newItem = SerializationUtils.clone(item);
            simCarts.add(newItem);
        }
        return simCarts;
    }
}
