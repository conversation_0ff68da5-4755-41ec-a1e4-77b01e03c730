package com.xiaomi.nr.promotion.domain.coupon.service.base.processor;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Request;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.api.dto.model.CouponInfo;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.engine.BizPlatformComponent;
import com.xiaomi.nr.promotion.engine.CouponTool;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.CouponGeneralType;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponOwnedInfo;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.resource.provider.CouponProvider;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;
import java.util.Map;
import java.util.TreeSet;

/**
 * Created by wangweiyi on 2023/1/10
 * 券结算流程
 */
public interface CouponProcessorInterface extends BizPlatformComponent {

    /**
     * 结算流程对应的券类型
     * @return
     */
    CouponGeneralType getGeneralType();



    List<CouponTool> loadCouponTools(List<CouponOwnedInfo> couponOwnedInfoList);

    /**
     * 结算流程券工具加载规则
     * @return
     */
    List<CouponTool> loadCouponToolsForCheckout(List<CheckoutCoupon> couponOwnedInfoList, BizPlatformEnum bizPlatform);

    /**
     * 券列表排序规则
     * @param checkoutResultMap
     * @return
     */
    TreeSet<Coupon> initSortCouponList(Map<Long, CouponCheckoutResult> checkoutResultMap);

    /**
     *
     * @param request
     * @param validCouponList
     * @param invalidCouponList
     * @param resultMap
     * @param context
     * @return
     * @throws BizError
     */
    Coupon setSelectCoupon(CheckoutPromotionV2Request request, TreeSet<Coupon> validCouponList, List<Coupon> invalidCouponList,  Map<Long, CouponCheckoutResult> resultMap, CheckoutContext context) throws BizError;

    /**
     * 初始化券资源
     * @param request
     * @param couponTool
     * @param result
     * @return
     * @throws BizError
     */
    CouponProvider initResource(CheckoutPromotionRequest request, CouponTool couponTool, CouponCheckoutResult result) throws BizError;

}

