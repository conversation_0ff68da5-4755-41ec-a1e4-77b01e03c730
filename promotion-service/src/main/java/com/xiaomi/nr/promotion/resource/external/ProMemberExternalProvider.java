package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.nr.promotion.activity.pool.ActivityPool;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.CrowdEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyGiftPromotionConfig;
import com.xiaomi.nr.promotion.rpc.promember.MemberApiProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.List;


@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class ProMemberExternalProvider extends ExternalDataProvider<Boolean> {

    private ListenableFuture<Boolean> future;

    @Autowired
    private MemberApiProxy memberApiProxy;

    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        if (request.getUserId() == null || request.getUserId() <= 0) {
            return;
        }
        future = memberApiProxy.checkIsMember(request.getUserId());
    }

    @Override
    protected long getTimeoutMills() {
        return 50;
    }

    @Override
    protected ListenableFuture<Boolean> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.PRO_MEMBER_RESULT;
    }
}
