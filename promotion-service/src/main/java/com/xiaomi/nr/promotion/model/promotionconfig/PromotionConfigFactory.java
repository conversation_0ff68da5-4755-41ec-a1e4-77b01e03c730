package com.xiaomi.nr.promotion.model.promotionconfig;

import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 活动配置工厂
 *
 * <AUTHOR>
 * @date 2021/6/1
 */
@Component
public class PromotionConfigFactory {
    @Autowired
    private ObjectFactory<GiftPromotionConfig> giftPromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<BargainPromotionConfig> bargainPromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<ReducePromotionConfig> reducePromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<DiscountPromotionConfig> discountPromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<OnsalePromotionConfig> onsalePromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<PostFreePromotionConfig> postFreePromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<BuyGiftPromotionConfig> buyGiftPromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<StorePricePromotionConfig> storePricePromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<RenewReducePromotionConfig> renewReducePromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<BuyReducePromotionConfig> buyReducePromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<PartOnsalePromotionConfig> partOnsalePromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<CrmChannelPricePromotionConfig> crmChannelPricePromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<StepPricePromotionConfig> stepPricePromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<B2tVipDiscountPromotionConfig> b2tVipDiscountPromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<CarRangeReducePromotionConfig> carRangeReducePromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<CarExchangeSubsidyConfig> carExchangeSubsidyConfigObjectFactory;
    @Autowired
    private ObjectFactory<CarOrderReduceConfig> carOrderReduceConfigObjectFactory;
    @Autowired
    private ObjectFactory<NewPurchaseSubsidyPromotionConfig> newPurchaseSubsidyPromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<UpgradePurchaseSubsidyPromotionConfig> upgradePurchaseSubsidyPromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<GovernmentSubsidyPromotionConfig> governmentSubsidyPromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<CarShopVipDiscountPromotionConfig> carShopVipDiscountPromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<MaintenanceDiscountPromotionConfig> maintenanceDiscountPromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<MaintenanceItemFreePromotionConfig> maintenanceItemFreePromotionConfigObjectFactory;

    /**
     * 获取配置对象
     *
     * @param promotionType 类型
     * @return 配置对象
     * @throws BizError 业务异常
     */
    public AbstractPromotionConfig getConfig(PromotionToolType promotionType) throws BizError {
        switch (promotionType) {
            case GIFT:
                return giftPromotionConfigObjectFactory.getObject();
            case BARGAIN:
                return bargainPromotionConfigObjectFactory.getObject();
            case ONSALE:
                return onsalePromotionConfigObjectFactory.getObject();
            case RANGE_REDUCE:
                return carRangeReducePromotionConfigObjectFactory.getObject();
            case EXCHANGE_SUBSIDY:
                return carExchangeSubsidyConfigObjectFactory.getObject();
            case ORDER_REDUCE:
                return carOrderReduceConfigObjectFactory.getObject();
            case REDUCE:
                return reducePromotionConfigObjectFactory.getObject();
            case DISCOUNT:
                return discountPromotionConfigObjectFactory.getObject();
            case POST_FREE:
                return postFreePromotionConfigObjectFactory.getObject();
            case BUY_GIFT:
                return buyGiftPromotionConfigObjectFactory.getObject();
            case STORE_PRICE:
                return storePricePromotionConfigObjectFactory.getObject();
            case RENEW_REDUCE:
                return renewReducePromotionConfigObjectFactory.getObject();
            case BUY_REDUCE:
                return buyReducePromotionConfigObjectFactory.getObject();
            case PARTONSALE:
                return partOnsalePromotionConfigObjectFactory.getObject();
            case B2T_VIP_DISCOUNT:
                return b2tVipDiscountPromotionConfigObjectFactory.getObject();
            case B2T_CHANNEL_PRICE:
                return crmChannelPricePromotionConfigObjectFactory.getObject();
            case B2T_STEP_PRICE:
                return stepPricePromotionConfigObjectFactory.getObject();
            case NEW_PURCHASE_SUBSIDY:
                return newPurchaseSubsidyPromotionConfigObjectFactory.getObject();
            case UPGRADE_PURCHASE_SUBSIDY:
                return upgradePurchaseSubsidyPromotionConfigObjectFactory.getObject();
            case GOVERNMENT_SUBSIDY:
                return governmentSubsidyPromotionConfigObjectFactory.getObject();
            case CAR_SHOP_VIP:
                return carShopVipDiscountPromotionConfigObjectFactory.getObject();
            case MAINTENANCE_REPAIR_DISCOUNT:
                return maintenanceDiscountPromotionConfigObjectFactory.getObject();
            case MAINTENANCE_ITEM_FREE:
                return maintenanceItemFreePromotionConfigObjectFactory.getObject();
            default:
                throw ExceptionHelper.create(GeneralCodes.InternalError, "Error PromotionToolType");
        }
    }
}
