package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetail;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.UpgradePurchaseSubsidyPromotionConfig;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class UpgradePurchaseSubsidyCondition extends AbstractGiftCondition {

    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 优惠类型
     */
    private PromotionToolType promotionType;
    /**
     * sku-品类
     */
    private Map<String, Integer> goodsSpuGroupMap;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        Long uid = request.getUserId();
        String idCard = request.getIdCard();
        if (MapUtil.isEmpty(goodsSpuGroupMap)) {
            log.warn("UpgradePurchaseSubsidyCondition goodsSpuGroupMap is empty. actId:{} uid:{}", promotionId, uid);
            return false;
        }
        // 有身份证&&前端勾选使用补贴才可享受换新补贴
        if (StringUtils.isEmpty(idCard)||!request.getUsePurchaseSubsidy()) {
            return false;
        }
        List<GoodsIndex> goodsIndexList=new ArrayList<>();
        List<CartItem> cartList = request.getSourceApi() == SourceApi.SUBMIT ? context.getCarts() : request.getCartList();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            // 1、赠品、加价购、不能参加的活动类型过滤
            boolean filterResult = CartHelper.checkItemActQualify(item, promotionType.getTypeId(), StringUtils.isEmpty(request.getOrgCode()), null, null);
            if (!filterResult) {
                continue;
            }
            // 2、没有参加过购新补贴活动的过滤，参加过购新补贴一定满足限购规则
            Map<String, List<ReduceDetail>> reduceDetailList = item.getReduceDetailList();
            if (reduceDetailList==null){
                continue;
            }
            if (!reduceDetailList.containsKey(PromotionConstant.NEW_PURCHASE_SUBSIDY_KEY)){
                continue;
            }
            // 3、活动池中不存在的商品过滤
            String skuPackage = CartHelper.getSkuPackage(item);
            Integer spuGroup = goodsSpuGroupMap.get(skuPackage);
            if (spuGroup == null) {
                continue;
            }
            goodsIndexList.add(new GoodsIndex(item.getItemId(),idx));
        }
        context.setGoodIndex(goodsIndexList);
        return !goodsIndexList.isEmpty();
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof UpgradePurchaseSubsidyPromotionConfig promotionConfig)) {
            log.error("config is not instanceof UpgradePurchaseSubsidyPromotionConfig. config:{}", config);
            return;
        }
        this.promotionId = promotionConfig.getPromotionId();
        this.goodsSpuGroupMap=promotionConfig.getGoodsSpuGroupMap();
        this.promotionType=promotionConfig.getPromotionType();
    }
}

