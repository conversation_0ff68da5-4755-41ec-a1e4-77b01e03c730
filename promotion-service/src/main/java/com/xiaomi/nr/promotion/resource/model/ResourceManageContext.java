package com.xiaomi.nr.promotion.resource.model;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.RollbackPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.SubmitPromotionRequest;
import com.xiaomi.nr.promotion.enums.TradeFromEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.mq.consumer.entity.OrderData;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 资源管理上下文
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
@Data
public class ResourceManageContext {
    /**
     * 订单ID
     */
    private long orderId;
    /**
     * 用户ID
     */
    private long uid;
    /**
     * 资源提供者列表
     */
    private List<ResourceProvider<?>> providers;
    /**
     * 操作来源
     */
    private ResourceOperationSource operationSource;
    /**
     * 资源状态
     */
    private ResourceStatus resourceStatus;

    /**
     * 交易来源
     */
    private TradeFromEnum tradeFrom;

    /**
     * 处理资源
     */
    private List<ResourceType> resourceTypes;

    /**
     * 订单消息信息
     */
    private OrderDataInfo orderData;

    /**
     * 下单类型
     */
    private Integer submitType;

    /**
     * 从请求结算上下文获取资源上下文
     *
     * @param request         请求参数
     * @param checkoutContext 结算上下文
     * @return ResourceManageContext资源管理上下文
     */
    public static ResourceManageContext fromCheckoutRequest(CheckoutPromotionRequest request, CheckoutContext checkoutContext) {
        TradeFromEnum trade = StringUtils.isNotBlank(request.getOrgCode())? TradeFromEnum.STORE : TradeFromEnum.SHOP;
        ResourceManageContext resourceManageContext = new ResourceManageContext();
        resourceManageContext.setProviders(checkoutContext.getResourceHandlers());
        resourceManageContext.setOrderId(request.getOrderId());
        resourceManageContext.setTradeFrom(trade);
        resourceManageContext.setUid(request.getUserId());
        resourceManageContext.setOperationSource(ResourceOperationSource.ORDER);
        resourceManageContext.setSubmitType(request.getSubmitType());
        return resourceManageContext;
    }

    /**
     * 从订单提交请求获取资源管理上下文
     *
     * @param request 订单提交请求参数
     * @return ResourceManageContext资源管理上下文
     */
    public static ResourceManageContext fromCommitRequest(SubmitPromotionRequest request) {
        ResourceManageContext resourceManageContext = new ResourceManageContext();
        resourceManageContext.setOrderId(request.getOrderId());
        resourceManageContext.setOperationSource(ResourceOperationSource.ORDER);
        resourceManageContext.setUid(0L);
        return resourceManageContext;
    }

    /**
     * 从订单回滚请求获取资源管理上下文
     *
     * @param request 回滚请求参数
     * @return ResourceManageContext资源管理上下文
     */
    public static ResourceManageContext fromRollbackRequest(RollbackPromotionRequest request) {
        ResourceManageContext resourceManageContext = new ResourceManageContext();
        resourceManageContext.setOrderId(request.getOrderId());
        resourceManageContext.setOperationSource(ResourceOperationSource.ORDER);
        resourceManageContext.setUid(0L);
        return resourceManageContext;
    }

    /**
     * 根据订单ID获取资源管理上下文
     *
     * @param orderId 订单ID
     * @return ResourceManageContext资源管理上下文
     */
    public static ResourceManageContext fromTask(long orderId) {
        ResourceManageContext resourceManageContext = new ResourceManageContext();
        resourceManageContext.setOrderId(orderId);
        resourceManageContext.setOperationSource(ResourceOperationSource.SCHEDULE);
        resourceManageContext.setUid(0L);
        return resourceManageContext;
    }

    /**
     * 根据订单ID获取资源管理上下文
     *
     * @param orderId 订单ID
     * @return ResourceManageContext资源管理上下文
     */
    public static ResourceManageContext fromMessage(long orderId) {
        ResourceManageContext resourceManageContext = new ResourceManageContext();
        resourceManageContext.setOrderId(orderId);
        resourceManageContext.setOperationSource(ResourceOperationSource.SCHEDULE);
        resourceManageContext.setUid(0L);
        return resourceManageContext;
    }
}
