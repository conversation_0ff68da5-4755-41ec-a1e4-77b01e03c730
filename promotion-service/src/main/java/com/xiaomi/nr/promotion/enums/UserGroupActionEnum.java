package com.xiaomi.nr.promotion.enums;

/**
 * 群组Group
 *
 * <AUTHOR>
 * @date 2021/4/14
 */
public enum UserGroupActionEnum {
    /**
     * 群组action为包含
     */
    INCLUDE(1),
    /**
     * 群组action为排除，目前已无排除
     */
    EXCLUDE(2);

    private final int val;

    UserGroupActionEnum(int val) {
        this.val = val;
    }

    public int getVal() {
        return val;
    }

    /**
     * 根据动作获取枚举
     *
     * @param action 动作
     * @return UserGroupActionEnum
     */
    public static UserGroupActionEnum getAction(int action) {
        UserGroupActionEnum[] values = UserGroupActionEnum.values();
        for (UserGroupActionEnum actionEnum : values) {
            if (action == actionEnum.getVal()) {
                return actionEnum;
            }
        }
        return null;
    }
}
