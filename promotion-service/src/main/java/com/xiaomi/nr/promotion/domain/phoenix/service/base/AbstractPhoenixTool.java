package com.xiaomi.nr.promotion.domain.phoenix.service.base;

import com.xiaomi.nr.phoenix.api.dto.store.CheckOperatorPackageResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.api.dto.model.ThirdPromotion;
import com.xiaomi.nr.promotion.engine.PhoenixTool;
import com.xiaomi.nr.promotion.enums.BusinessTypeEnum;
import com.xiaomi.nr.promotion.enums.PhoenixCheckResultCodeEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.provider.PhoenixOperatorProvider;
import com.xiaomi.nr.promotion.rpc.phoenix.PhoenixOperatorServiceProxy;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * 三方优惠-抽象
 *
 * <AUTHOR>
 * @date 2022/9/20
 */
public abstract class AbstractPhoenixTool implements PhoenixTool {

    @Autowired
    protected ResourceProviderFactory resourceProviderFactory;

    @Autowired
    private PhoenixOperatorServiceProxy phoenixOperatorServiceProxy;

    /**
     * 检查套装
     *
     * @param thirdPromotion 三方数据
     * @param request        请求
     * @param context        上下问
     * @return 是否符合
     * @throws BizError 异常
     */
    protected boolean checkoutPhoenixPackage(ThirdPromotion thirdPromotion, CheckoutPromotionRequest request, CheckoutContext context, int discount) throws BizError {
        Integer type = thirdPromotion.getBusinessType();
        String orgCode = request.getOrgCode();
        String payBarCode = request.getPayBarCode();
        CheckOperatorPackageResponse response = phoenixOperatorServiceProxy.checkOperatorPackage(type, orgCode, payBarCode, thirdPromotion.getParams());
        if (Objects.equals(response.getResultCode().getCode(), PhoenixCheckResultCodeEnum.INVALID.getCode())) {
            String message = "400030970:选择的套餐不存在或已被修改。请重新选择";
            throw ExceptionHelper.create(ErrCode.ERR_PHOENIX_PACKAGE, message);
        }
        if (Objects.equals(response.getResultCode().getCode(), PhoenixCheckResultCodeEnum.USED.getCode())) {
            String message = "400030972:该运营商编号已被使用，请确认后重新输入。SA单号为:" + response.getOrderId();
            throw ExceptionHelper.create(ErrCode.ERR_PHOENIX_USED, message);
        }
        List<CartItem> cartList = getCartList(request, context);
        long totalPrice = CartHelper.getTotalPrice(cartList);
        if (discount >= totalPrice) {
            String message = "400030971:套餐直降金额大于订单实际金额，请确认订单套餐是否选择正确或者返回继续添加商品";
            throw ExceptionHelper.create(ErrCode.ERR_PHOENIX_AMOUNT, message);
        }
        return true;
    }

    /**
     * 获取数据
     *
     * @param request 请求
     * @return 列表
     */
    protected List<CartItem> getCartList(CheckoutPromotionRequest request, CheckoutContext context) {
        return request.getSourceApi() == SourceApi.SUBMIT ? context.getCarts() : request.getCartList();
    }

    /**
     * 优惠金额计算：
     * 计算但前购物车总金额，math.min(totalPrice -1, balance）
     *
     * @param cartList 购物车金额
     * @param balance  券余额
     * @return 可优惠金额
     */
    protected long calculateReducePrice(List<CartItem> cartList, int balance) {
        long totalPrice = CartHelper.getTotalPrice(cartList);
        if (totalPrice == 0 || balance == 0) {
            return 0L;
        }
        return Math.min(totalPrice - 1, balance);
    }

    /**
     * 初始化资源
     *
     * @param request         请求对象
     * @param checkoutContext 结算上下文
     * @param thirdPromotion  三方优惠信息
     * @param reducePrice     优惠金额
     * @throws BizError 业务异常
     */
    protected void initOperatorResource(CheckoutPromotionRequest request, CheckoutContext checkoutContext, ThirdPromotion thirdPromotion, long reducePrice) throws BizError {
        Long orderId = request.getOrderId();
        PromotionToolType toolType = getType();
        PhoenixOperatorProvider.ResContent resContent = buildResContent(request, thirdPromotion, reducePrice);
        ResourceObject<PhoenixOperatorProvider.ResContent> resourceObjectObject = buildResource(resContent, orderId, toolType, ResourceType.PHOENIX_OPERATOR);
        PhoenixOperatorProvider couponProvider = (PhoenixOperatorProvider) resourceProviderFactory.getProvider(ResourceType.PHOENIX_OPERATOR);
        couponProvider.initResource(resourceObjectObject);
        checkoutContext.getResourceHandlers().add(couponProvider);
    }

    private PhoenixOperatorProvider.ResContent buildResContent(CheckoutPromotionRequest request, ThirdPromotion thirdPromotion, long reducePrice) {
        PhoenixOperatorProvider.ResContent resContent = new PhoenixOperatorProvider.ResContent();
        resContent.setUserId(request.getUserId());
        resContent.setOrderId(request.getOrderId());
        resContent.setOrderTime(request.getOrderTime());
        resContent.setOrgCode(request.getOrgCode());
        resContent.setPayBarCode(request.getPayBarCode());
        resContent.setAmount(reducePrice);
        resContent.setBusinessType(thirdPromotion.getBusinessType());
        resContent.setParams(thirdPromotion.getParams());
        return resContent;
    }

    protected <T> ResourceObject<T> buildResource(T content, Long orderId, PromotionToolType toolType, ResourceType resourceType) {
        ResourceObject<T> resourceObject = new ResourceObject<>();
        resourceObject.setContent(content);
        resourceObject.setResourceId(String.format("phoenix_%s_%s", toolType.getTypeId(), orderId));
        resourceObject.setResourceType(resourceType);
        resourceObject.setPromotionId(-1L);
        resourceObject.setOrderId(orderId);
        resourceObject.setPid(-1L);
        return resourceObject;
    }
}
