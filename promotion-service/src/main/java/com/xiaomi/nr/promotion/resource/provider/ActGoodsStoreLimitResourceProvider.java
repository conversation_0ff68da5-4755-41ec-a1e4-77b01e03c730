package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * 指定门店降价的sku/package参与次数
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class ActGoodsStoreLimitResourceProvider implements ResourceProvider<ActGoodsStoreLimitResourceProvider.ResContent> {

    private ResourceObject<ActGoodsStoreLimitResourceProvider.ResContent> resourceObject;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Autowired
    private NacosConfig nacosConfig;

    @Override
    public ResourceObject<ActGoodsStoreLimitResourceProvider.ResContent> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<ActGoodsStoreLimitResourceProvider.ResContent> object) {
        this.resourceObject = object;
    }

    @Override
    public void lock() throws BizError {
        String orgCode = resourceObject.getContent().getOrgCode();
        Map<String, ResContentItem> skuPackageList = resourceObject.getContent().getSkuPackageList();
        log.info("lock act limit resource. resourceObject:{}", resourceObject);
        if (skuPackageList == null || skuPackageList.size() == 0) {
            log.warn("lock skuPackageList is empty. resourceObject:{}", resourceObject);
            return;
        }
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("ActGoodsStoreLimitResourceProvider.lock(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        skuPackageList.forEach((skuPackage, item) -> activityRedisDao.incrActGoodsStoreLimitNum(resourceObject.getPromotionId(), skuPackage, orgCode,item.getCount()));
        log.info("lock act limit ok. resourceObject:{}", resourceObject);
    }

    @Override
    public void consume() {
        log.info("consume act bargain limit resource. resourceObject{}", resourceObject);
    }

    @Override
    public void rollback() throws BizError {
        String orgCode = resourceObject.getContent().getOrgCode();
        Map<String, ResContentItem> skuPackageList = resourceObject.getContent().getSkuPackageList();
        log.info("rollback act limit resource. resourceObject:{}", resourceObject);
        if (skuPackageList == null || skuPackageList.size() == 0) {
            log.warn("rollback skuPackageList is empty. resourceObject:{}", resourceObject);
            return;
        }
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("ActGoodsStoreLimitResourceProvider.rollback(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        skuPackageList.forEach((skuPackage, item) -> activityRedisDao.decrActGoodsStoreLimitNum(resourceObject.getPromotionId(), skuPackage, orgCode,item.getCount()));
        log.info("rollback act limit ok. resourceObject:{}", resourceObject);
    }

    @Override
    public String conflictText() {
        return "处理指定门店降价限购数失败";
    }

    @Data
    public static class ResContent {
        /**
         * 门店Code
         */
        private String orgCode;

        private Map<String, ResContentItem> skuPackageList;
    }

    @Data
    public static class ResContentItem {
        /**
         * 参与活动数量
         */
        private Integer count;
        /**
         * 限制数量
         */
        private Long limitNum;
    }
}
