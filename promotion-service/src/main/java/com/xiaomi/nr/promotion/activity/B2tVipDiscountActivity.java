package com.xiaomi.nr.promotion.activity;

import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.enums.GoodsTypeEnum;
import com.xiaomi.nr.promotion.api.dto.enums.UserLevelEnum;
import com.xiaomi.nr.promotion.api.dto.model.GoodsDto;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionPriceDTO;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.B2tVipDiscountRule;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.DiscountRate;
import com.xiaomi.nr.promotion.componet.action.B2tVipDiscountAction;
import com.xiaomi.nr.promotion.componet.condition.B2tVipDiscountCondition;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.PromotionPriceNormProvider;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.B2tVipDiscountPromotionConfig;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.PriceUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.xiaomi.nr.promotion.constant.ApplicationConstant.CONTEXT_PARAM_USER_LEVEL;


/**
 * 折扣价格
 *
 * <AUTHOR>
 * @date 2023/2/9
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class B2tVipDiscountActivity extends AbstractActivityTool implements ActivityTool, PromotionPriceNormProvider {
    /**
     * 等级折扣
     * key: userLevel  ID val:Discount
     */
    private DiscountRate discountRate;

    private B2tVipDiscountRule.GoodsDiscountRate goodsDiscountRate;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine
                .condition(B2tVipDiscountCondition.class)
                .action(B2tVipDiscountAction.class);
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.B2T_VIP_DISCOUNT;
    }

    /**
     * 获取优惠价格
     *
     * @param goodsList     详情信息
     * @param contextParams 上下文参数
     * @return key: SSU val: 价格信息
     */
    @Override
    public Map<Long, PromotionPriceDTO> getGoodsPromotionPrice(List<GoodsDto> goodsList, Map<String, String> contextParams) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return Collections.emptyMap();
        }
        Map<Long, PromotionPriceDTO> priceDtoMap = Maps.newHashMap();
        for (GoodsDto goodsDto : goodsList) {
            handlePrice(goodsDto, priceDtoMap, contextParams);
        }
        return priceDtoMap;
    }

    private void handlePrice(GoodsDto goodsDto,  Map<Long, PromotionPriceDTO> priceDtoMap, Map<String, String> contextParams) {
        // 确定折扣
        String userLevel = contextParams.get(CONTEXT_PARAM_USER_LEVEL);


        // 确定价格
        Long finalPrice = goodsDto.getPrice();
        if (finalPrice != null) {
            if (discountRate != null ) {
                finalPrice = PriceUtil.multiDiscount(goodsDto.getPrice(), getDiscountByUserLevel(userLevel));
            } else if (goodsDiscountRate != null) {
                finalPrice = PriceUtil.multiDiscount(goodsDto.getPrice(), getDiscountByGoodsType(goodsDto.getGoodsType()));
            }
        }

        B2tVipDiscountRule vipDiscountRule = new B2tVipDiscountRule();
        vipDiscountRule.setDiscountRate(discountRate);
        vipDiscountRule.setGoodsDiscountRate(goodsDiscountRate);

        // 响应结果
        PromotionPriceDTO priceDTO = buildPromotionPrice(GsonUtil.toJson(vipDiscountRule), finalPrice);
        priceDtoMap.put(goodsDto.getSsuId(),  priceDTO);
        // 替换价格
        goodsDto.setPrice(finalPrice);
    }

    private int getDiscountByUserLevel(String userLevel) {
        UserLevelEnum levelEnum = UserLevelEnum.getByLevel(userLevel);
        //userlevel为空时，不计算折扣价
        if (StringUtils.isEmpty(userLevel)) {
            return 100;
        }
        if (UserLevelEnum.NORMAL == levelEnum) {
            return discountRate.getNormal();
        }
        if (UserLevelEnum.VIP == levelEnum) {
            return discountRate.getVip();
        }
        if (UserLevelEnum.SVIP == levelEnum) {
            return discountRate.getSvip();
        }
        return discountRate.getNormal();
    }
    private int getDiscountByGoodsType(Integer goodsType) {
        if (GoodsTypeEnum.NORM.getValue().equals(goodsType)) {
            return goodsDiscountRate.getNormalRate();
        } else if (GoodsTypeEnum.RECOM.getValue().equals(goodsType)) {
            return goodsDiscountRate.getRecomRate();
        }
        return 100;
    }

    /**
     * 构建优惠信息
     *
     * @param context 上下文
     * @return 优惠信息
     */
    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) {
        return super.buildDefaultPromotionInfo(context);
    }

    /**
     * 获取产品站信息
     *
     * @param clientId       应用ID
     * @param orgCode        门店Code
     * @param skuPackageList 活动类别
     */
    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        return Collections.emptyMap();
    }

    /**
     * 获取活动详情
     *
     * @return 活动详情
     */
    @Override
    public ActivityDetail getActivityDetail() {
        return super.getBasicActivityDetail();
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof B2tVipDiscountPromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        B2tVipDiscountPromotionConfig promotionConfig = (B2tVipDiscountPromotionConfig) config;
        this.discountRate = promotionConfig.getDiscountRate();
        this.goodsDiscountRate = promotionConfig.getGoodsDiscountRate();
        return true;
    }
}
