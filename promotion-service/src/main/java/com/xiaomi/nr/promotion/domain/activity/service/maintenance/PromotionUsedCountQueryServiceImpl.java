package com.xiaomi.nr.promotion.domain.activity.service.maintenance;

import com.google.gson.JsonElement;
import com.xiaomi.nr.promotion.activity.pool.CarPromotionInstancePool;
import com.xiaomi.nr.promotion.api.dto.ProductActJoinInfoRequest;
import com.xiaomi.nr.promotion.api.dto.ProductActJoinInfoResponse;
import com.xiaomi.nr.promotion.api.dto.model.JoinedOrderInfoDto;
import com.xiaomi.nr.promotion.api.dto.model.ProductActJoinInfo;
import com.xiaomi.nr.promotion.dao.mysql.mdpromotion.ActivityEntity;
import com.xiaomi.nr.promotion.dao.mysql.mdpromotion.ActivityMapper;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionCarActivityCountMapper;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionUserActivityCountMapper;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.CarActivityCount;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.UserActivityCount;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MultiPromotionConfig;
import com.xiaomi.nr.promotion.resource.provider.VidActivityCountProvider;
import com.xiaomi.nr.promotion.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: zhangliwei6
 * @date: 2025/1/6 17:28
 * @description:
 */
@Slf4j
@Service
public class PromotionUsedCountQueryServiceImpl implements PromotionUsedCountQueryService {

    @Autowired
    private PromotionUserActivityCountMapper userActivityCountMapper;

    @Autowired
    private PromotionCarActivityCountMapper carActivityCountMapper;

    @Autowired
    private CarPromotionInstancePool carPromotionInstancePool;

    @Autowired
    private ActivityMapper activityMapper;

    @Override
    public ProductActJoinInfoResponse queryUsedCount(ProductActJoinInfoRequest request) {
        ProductActJoinInfoResponse response = new ProductActJoinInfoResponse();
        List<Long> promotionIdList = request.getPromotionIds();
        Map<Long, ProductActJoinInfo> promotionJoinInfoMap = promotionIdList.stream().collect(Collectors.toMap(Function.identity(), id -> {
            ProductActJoinInfo productActJoinInfo = new ProductActJoinInfo();
            productActJoinInfo.setPromotionId(id);
            productActJoinInfo.setJoinTimes(0);
            productActJoinInfo.setUserLimitNum(0);
            return productActJoinInfo;
        }));
        if (request.getMid() != null) {
            List<UserActivityCount> userActivityCountList = userActivityCountMapper.getByUserIdAndPromotionIds(request.getMid(),
                    promotionIdList);
            userActivityCountList.forEach(userActivityCount -> {
                ProductActJoinInfo productActJoinInfo = promotionJoinInfoMap.get(userActivityCount.getPromotionId());
                productActJoinInfo.setJoinTimes(userActivityCount.getNum());
            });
        } else if (StringUtils.isNotBlank(request.getVid())) {
            List<CarActivityCount> carActivityCountList = carActivityCountMapper.getByVidAndPromotionIds(request.getVid(), promotionIdList);
            carActivityCountList.forEach(carActivityCount -> {
                ProductActJoinInfo productActJoinInfo = promotionJoinInfoMap.get(carActivityCount.getPromotionId());
                productActJoinInfo.setJoinTimes(carActivityCount.getNum());
                List<JoinedOrderInfoDto> orderInfoDtoList = new ArrayList<>();
                VidActivityCountProvider.VidJoinActNum extend = GsonUtil.fromJson(carActivityCount.getExtend(), VidActivityCountProvider.VidJoinActNum.class);
                if (extend != null && MapUtils.isNotEmpty(extend.getValidOrderId())) {
                    Map<Long, Integer> validOrderIdMap = new HashMap<>();
                    extend.getValidOrderId().forEach((k, v) -> {
                        validOrderIdMap.merge(k, v, Integer::sum);
                    });

                    extend.getInValidOrderId().forEach((k, v) -> {
                        validOrderIdMap.merge(k, -v, Integer::sum);
                    });

                    validOrderIdMap.forEach((k, v) -> {
                        if (!Objects.equals(v, 0)) {
                            JoinedOrderInfoDto dto = new JoinedOrderInfoDto();
                            dto.setOrderId(k);
                            dto.setNum(v);
                            orderInfoDtoList.add(dto);
                        }
                    });
                }

                productActJoinInfo.setJoinedOrderInfoList(orderInfoDtoList);
            });
        }
        // 查活动缓存获取活动相关信息
        Map<Long, ActivityTool> cachedActivityMap =
                carPromotionInstancePool.getCurrentTools(promotionIdList).stream().collect(Collectors.toMap(ActivityTool::getId,
                        Function.identity()));
        // 填充字段
        cachedActivityMap.forEach((k, v) -> {
            ProductActJoinInfo productActJoinInfo = promotionJoinInfoMap.get(k);
            AbstractPromotionConfig promotionConfig = v.getPromotionConfig();
            productActJoinInfo.setPromotionName(promotionConfig.getName());
            productActJoinInfo.setPromotionType(v.getType().getTypeId());
            // 注意此处最大活动次数限制long转int，但业务上应该不会超过int限制
            ActNumLimitRule numLimitRule = ((MultiPromotionConfig) promotionConfig).getNumLimitRule();
            if (numLimitRule != null && numLimitRule.getPersonLimit() != null) {
                productActJoinInfo.setUserLimitNum(Math.toIntExact(numLimitRule.getPersonLimit()));
            }
        });
        // 缓存里没有的活动，查表
        if (cachedActivityMap.size() < promotionIdList.size()) {
            List<Long> remainIdList = promotionIdList.stream().filter(id -> !cachedActivityMap.containsKey(id)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(remainIdList)) {
                List<ActivityEntity> dbActivityList =
                        activityMapper.queryByIds(remainIdList);
                // 填充字段
                dbActivityList.forEach(activity -> {
                    ProductActJoinInfo productActJoinInfo = promotionJoinInfoMap.get(activity.getId());
                    productActJoinInfo.setPromotionName(activity.getName());
                    productActJoinInfo.setPromotionType(activity.getPromotionType());
                    // 活动限制次数约定为userLimitNum
                    JsonElement userLimitNum = GsonUtil.fromJson(activity.getRule(), JsonElement.class)
                            .getAsJsonObject().get("userLimitNum");
                    if (userLimitNum != null) {
                        productActJoinInfo.setUserLimitNum(userLimitNum.getAsInt());
                    }
                });
            }
        }
        response.setPromotionJoinInfoMap(promotionJoinInfoMap);
        return response;
    }
}
