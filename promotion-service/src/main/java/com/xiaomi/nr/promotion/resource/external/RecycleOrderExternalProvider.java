package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.CartItemChild;
import com.xiaomi.nr.promotion.api.dto.model.ThirdPromotion;
import com.xiaomi.nr.promotion.constant.PhoenixParamConstant;
import com.xiaomi.nr.promotion.enums.BusinessTypeEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.rpc.recycle.ActivityServiceProxy;
import com.xiaomi.nr.promotion.util.ThirdPromotionHelper;
import com.xiaomi.nr.recycle.api.activity.dto.ActivityQueryKeyDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 米家以旧换新
 *
 * <AUTHOR>
 * @date 2021/4/28
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class RecycleOrderExternalProvider extends ExternalDataProvider<ActivityQueryKeyDto> {
    private ListenableFuture<ActivityQueryKeyDto> future;

    @Autowired
    private ActivityServiceProxy activityServiceProxy;

    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        List<ThirdPromotion> thirdPromotionList = request.getThirdPromotions();
        if (CollectionUtils.isEmpty(thirdPromotionList)) {
            return;
        }
        BusinessTypeEnum type = StringUtils.isEmpty(request.getOrgCode()) ? BusinessTypeEnum.MISHOPRENEW : BusinessTypeEnum.RENEW;
        ThirdPromotion thirdPromotion = ThirdPromotionHelper.getThirdPromotion(thirdPromotionList, type);
        if (thirdPromotion == null) {
            return;
        }
        Long renewOrderId =  ThirdPromotionHelper.getLongParamValue(thirdPromotion, PhoenixParamConstant.RENEW_ORDER_ID, null);
        if (renewOrderId == null) {
            return;
        }
        List<Long> skuList = new ArrayList<>();
        boolean isPakage = false;//商品是否为套装
        for (CartItem cartItem : request.getCartList()) {
            if (SourceEnum.isGiftBargain(cartItem.getSource())) {
                continue;
            }
            if (Objects.equals(cartItem.getSource(), "deliveryCharge")) {
                continue;
            }
            if (Objects.equals(cartItem.getSource(), "insurance")) {
                continue;
            }
            if (StringUtils.isNotEmpty(cartItem.getPackageId())) {
                skuList.add(Long.valueOf(cartItem.getPackageId()));
                isPakage = true;
            } else if (StringUtils.isNotEmpty(cartItem.getSku())) {
                skuList.add(Long.valueOf(cartItem.getSku()));
            }

        }

        Integer activityChannel = StringUtils.isEmpty(request.getOrgCode()) ? 1 : 2;//线上请求传1，门店请求统一传2
        future = activityServiceProxy.queryCurrentRebateAsync(renewOrderId, skuList, isPakage, activityChannel);

    }

    @Override
    protected long getTimeoutMills() {
        return DEFAULT_TIMEOUT;
    }

    @Override
    public ListenableFuture<ActivityQueryKeyDto> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.RECYCLE_ORDER;
    }
}
