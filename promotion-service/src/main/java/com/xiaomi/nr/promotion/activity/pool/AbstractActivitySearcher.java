package com.xiaomi.nr.promotion.activity.pool;

import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.BizPlatformComponent;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.util.DateTimeUtil;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.reflections.Reflections.log;

/**
 * Created by wangweiyi on 2023/8/9
 */


public abstract class AbstractActivitySearcher implements BizPlatformComponent {

    public abstract List<Long> doSearchIndex(ActSearchParam actSearchParam);

    public abstract List<ActivityTool> doSearchAct(List<Long> actIds);


    public abstract List<ActivityTool> doFilter(ActSearchParam actSearchParam, List<ActivityTool> activityTools);


    public abstract BizPlatformEnum getBizPlatform();

    @Deprecated
    public List<ActivityTool> searchActivity(ActSearchParam actSearchParam) {

        //查询缓存索引
        List<Long> actIds = doSearchIndex(actSearchParam);

        //查询活动池
        List<ActivityTool> activityTools = doSearchAct(actIds);

        //过滤
        return doFilter(actSearchParam, activityTools);
    }

    /**
     * 根据搜索参数搜索汽车活动
     *
     * @param actSearchParam 搜索参数
     * @return 过滤后的活动工具列表
     */
    public List<ActivityTool> searchCarActivity(ActSearchParam actSearchParam) {

        //查询缓存索引
        List<Long> actIds = doSearchIndex(actSearchParam);

        //查询活动池
        List<ActivityTool> activityTools = doSearchAct(actIds);

        //过滤
        List<ActivityTool> filterTools = doFilter(actSearchParam, activityTools);
        log.info("searchCarActivity index size:{},  activityTools size:{}, filterTools size:{}", actIds.size(), activityTools.size(), filterTools.size());
        return filterTools;
    }

    /**
     * 检查活动时间
     *
     * @param tool 活动工具
     * @return 时间是否符合
     */
    protected boolean checkTime(ActivityTool tool, boolean isPreview) {
        long now = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        // 预览即将开始

        if (isPreview) {
            long preTime = DateTimeUtil.getFutureTime(7);
            return now < tool.getUnixStartTime() && tool.getUnixStartTime() < preTime;
        }
        // 进行中
        return now >= tool.getUnixStartTime() && now <= tool.getUnixEndTime();
    }

}
