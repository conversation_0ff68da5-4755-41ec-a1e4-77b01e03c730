package com.xiaomi.nr.promotion.activity.pool;

import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.BizPlatformComponent;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.util.DateTimeUtil;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 2023/8/9
 */


public abstract class AbstractActivitySearcher implements BizPlatformComponent {


    @Autowired
    protected PromotionInstancePool promotionInstancePool;

    public abstract List<Long> doSearchIndex(ActSearchParam actSearchParam);

    public List<ActivityTool> doSearchAct(List<Long> actIds) {
        return promotionInstancePool.getCurrentTools(actIds);
    }


    public abstract List<ActivityTool> doFilter(ActSearchParam actSearchParam, List<ActivityTool> activityTools);


    public abstract BizPlatformEnum getBizPlatform();

    public List<ActivityTool> searchActivity(ActSearchParam actSearchParam) {

        //查询缓存索引
        List<Long> actIds = doSearchIndex(actSearchParam);

        //查询活动池
        List<ActivityTool> activityTools = doSearchAct(actIds);

        //过滤
        return doFilter(actSearchParam, activityTools);
    }

    /**
     * 检查活动时间
     *
     * @param tool 活动工具
     * @return 时间是否符合
     */
    protected boolean checkTime(ActivityTool tool, boolean isPreview) {
        long now = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        // 预览即将开始

        if (isPreview) {
            long preTime = DateTimeUtil.getFutureTime(7);
            return now < tool.getUnixStartTime() && tool.getUnixStartTime() < preTime;
        }
        // 进行中
        return now >= tool.getUnixStartTime() && now < tool.getUnixEndTime();
    }

}
