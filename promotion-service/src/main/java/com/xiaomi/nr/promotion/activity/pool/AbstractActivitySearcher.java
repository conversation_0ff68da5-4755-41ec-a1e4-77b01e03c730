package com.xiaomi.nr.promotion.activity.pool;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.BizPlatformComponent;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * Created by wangweiyi on 2023/8/9
 */

@Slf4j
public abstract class AbstractActivitySearcher implements BizPlatformComponent {

    public abstract List<Long> doSearchIndex(ActSearchParam actSearchParam);

    public abstract List<ActivityTool> doSearchAct(List<Long> actIds);

    public abstract ActivityTool doSearchAct(Long actId);

    public abstract List<ActivityTool> doFilter(ActSearchParam actSearchParam, List<ActivityTool> activityTools);

    public abstract BizPlatformEnum getBizPlatform();

    @Deprecated
    public List<ActivityTool> searchActivity(ActSearchParam actSearchParam) {

        //查询缓存索引
        List<Long> actIds = doSearchIndex(actSearchParam);

        //查询活动池
        List<ActivityTool> activityTools = doSearchAct(actIds);

        //过滤
        return doFilter(actSearchParam, activityTools);
    }

    /**
     * 根据搜索参数搜索汽车活动
     *
     * @param actSearchParam 搜索参数
     * @return 过滤后的活动工具列表
     */
    public List<ActivityTool> searchCarActivity(ActSearchParam actSearchParam) {

        //查询缓存索引
        List<Long> actIds = doSearchIndex(actSearchParam);

        //查询活动池
        List<ActivityTool> activityTools = doSearchAct(actIds);

        //过滤
        List<ActivityTool> filterTools = doFilter(actSearchParam, activityTools);

        //日志打印
        if (CollectionUtils.isNotEmpty(actIds)) {
            log.info("searchCarActivity actIds: {}, activityTools size: {}, filterTools size: {}", actIds, Optional.ofNullable(activityTools).map(List::size).orElse(0), Optional.ofNullable(filterTools).map(List::size).orElse(0));
        }
        return filterTools;
    }

    /**
     * 检查活动时间
     *
     * @param tool 活动工具
     * @return 时间是否符合
     */
    protected boolean checkTime(ActivityTool tool, boolean isPreview) {
        long now = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        // 预览即将开始

        if (isPreview) {
            long preTime = DateTimeUtil.getFutureTime(7);
            return now < tool.getUnixStartTime() && tool.getUnixStartTime() < preTime;
        }
        // 进行中
        return now >= tool.getUnixStartTime() && now <= tool.getUnixEndTime();
    }

    public ActivityTool getPromotionById(Long actId) {
        // 查询活动池
        ActivityTool activityTool = doSearchAct(actId);

        if (activityTool == null) {
            return null;
        }

        // 过滤
        List<ActivityTool> filteredTools = doFilter(new ActSearchParam(), Lists.newArrayList(activityTool));

        //日志打印
        if (CollectionUtils.isEmpty(filteredTools)) {
            log.info("searchActivity actId: {}, activityTools is null", actId);
        }
        return filteredTools.getFirst();
    }
}
