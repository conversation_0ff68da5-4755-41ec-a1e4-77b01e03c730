package com.xiaomi.nr.promotion.enums;

import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.domain.coupon.service.base.condition.AllDeductCondition;
import com.xiaomi.nr.promotion.domain.coupon.service.base.condition.CouponCondition;
import com.xiaomi.nr.promotion.domain.coupon.service.base.condition.HalfDeductCondition;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

/**
 * Created by wang<PERSON><PERSON> on 2024/3/1
 * 优惠券组抵扣规则
 */
@Getter
@AllArgsConstructor
public enum CouponDeductRuleEnum {
    /**
     * 全部抵扣
     */
    ALL_DEDUCT(1),
    /**
     * 部分抵扣
     */
    PARTLY_DEDUCT(2);

    private final Integer code;

    private static final Map<Integer, CouponDeductRuleEnum> ENUM_MAP = Maps.newHashMap();

    static {
        ENUM_MAP.put(ALL_DEDUCT.code, ALL_DEDUCT);
        ENUM_MAP.put(PARTLY_DEDUCT.code, PARTLY_DEDUCT);
    }

    public static CouponDeductRuleEnum getEnumByCode(Integer code) {
        return ENUM_MAP.get(code);
    }

    public CouponCondition getConditionByEnum() {
        switch (this) {
            case ALL_DEDUCT -> {
                return new AllDeductCondition();
            }
            case PARTLY_DEDUCT -> {
                return new HalfDeductCondition();
            }
        }

        return null;
    }
}
