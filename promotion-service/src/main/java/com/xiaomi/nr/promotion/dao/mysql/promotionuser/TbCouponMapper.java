package com.xiaomi.nr.promotion.dao.mysql.promotionuser;

import com.xiaomi.nr.promotion.entity.mysql.promotionuser.TbCoupon;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

/**
 * 无码券Db访问接口
 *
 * <AUTHOR>
 * @date 2021/4/22
 */
@Repository
public interface TbCouponMapper {

    /**
     * 获取用户无码优惠券
     *
     * @param id     ID
     * @param userId 用户ID
     * @return 优惠券
     */
    @Select("select id, user_id, type_id, activity_id, start_time, end_time, " +
            "days, stat, order_id, use_time, expire_time, is_pass, admin_id, admin_name, " +
            "add_time, send_type, from_order_id, replace_money, invalid_time, " +
            "last_update_time, offline, reduce_express, parent_id ,extend_info " +
            "from tb_coupon " +
            "where id=#{id} and user_id=#{userId}")
    TbCoupon getByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 更新用户券状态
     *
     * @param id            ID
     * @param userId        用户Id
     * @param statOld       原状态
     * @param orderId       订单ID
     * @param statNew       新状态
     * @param replaceMoney  抵扣钱
     * @param reduceExpress 抵扣邮费
     * @param useTime       使用时间
     * @param offline       是否线下
     * @return 更新数
     */
    @Update("update tb_coupon " +
            "set stat=#{statNew}, " +
            "order_id=#{orderId}, " +
            "replace_money=#{replaceMoney}, " +
            "reduce_express=#{reduceExpress}, " +
            "use_time=#{useTime}, " +
            "offline=#{offline} " +
            "where id=#{id} and user_id=#{userId} and stat=#{statOld}")
    int updateStat(@Param("id") Long id, @Param("userId") Long userId, @Param("statOld") String statOld,
                   @Param("statNew") String statNew, @Param("orderId") Long orderId,
                   @Param("replaceMoney") BigDecimal replaceMoney, @Param("reduceExpress") BigDecimal reduceExpress,
                   @Param("useTime") Long useTime,
                   @Param("offline") Integer offline);
}
