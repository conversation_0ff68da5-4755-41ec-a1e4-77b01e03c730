package com.xiaomi.nr.promotion.rpc.recycle;

import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.recycle.api.activity.dto.ActivityQueryKeyDto;
import com.xiaomi.nr.recycle.api.activity.dto.ActivityQueryRequestDto;
import com.xiaomi.nr.recycle.api.activity.dto.OfflineActivityQueryReqDto;
import com.xiaomi.nr.recycle.api.activity.service.ActivityOfflineService;
import com.xiaomi.nr.recycle.api.activity.service.ActivityService;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.List;

/**
 * 换新中台活动
 *
 * <AUTHOR>
 * @date 2022/10/11
 */
@Component
@Slf4j
public class ActivityServiceProxy {
    @Reference(check = false, interfaceClass = ActivityService.class, version = "1.0", timeout = 2000, group = "${recycle.dubbo.group}")
    private ActivityService activityService;

    /**
     * 异步查询回收单信息
     *
     * @param renewOrderId 回收单号
     */
    @Async("phoenixTaskExecutor")
    public ListenableFuture<ActivityQueryKeyDto> queryCurrentRebateAsync(Long renewOrderId, List<Long> skuList, boolean isPakage, Integer activityChannel) {
        try {
            ActivityQueryKeyDto queryKeyDto = queryCurrentRebate(renewOrderId, skuList, isPakage, activityChannel);
            return AsyncResult.forValue(queryKeyDto);
        } catch (Exception e) {
            log.error("queryCurrentRebateAsync async error. renewOrderId:{}, err", renewOrderId, e);
            return AsyncResult.forExecutionException(e);
        }
    }

    /**
     * 查询回收单信息
     *
     * @param renewOrderId 回收单号
     * @throws BizError 业务异常
     */
    public ActivityQueryKeyDto queryCurrentRebate(Long renewOrderId, List<Long> skuList, boolean isPakage, Integer activityChannel) throws BizError {
        ActivityQueryRequestDto request = new ActivityQueryRequestDto();
        request.setOrderId(renewOrderId);
        request.setSkuOrSuitList(skuList);
        request.setIsSuit(isPakage);
        request.setActivityChannel(activityChannel);
        long startTime = System.currentTimeMillis();
        Result<ActivityQueryKeyDto> result;
        try {
            result = activityService.queryRebate(request);
        } catch (Exception e) {
            log.error("invoke rpc queryCurrentRebate error. request:{}, err", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "调用换新中台服务异常");
        }
        log.info("invoke rpc queryCurrentRebate. request:{}, result:{} ws:{}", request, result, System.currentTimeMillis() - startTime);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc queryCurrentRebate fail. request:{}, code:{} message:{}", request, result.getCode(), result.getMessage());
            throw ExceptionHelper.create(ErrCode.ERR_PHOENIX_RENEW_QUERY, "查询回收单失败:" + result.getMessage());
        }
        if (result.getData() == null) {
            log.error("invoke rpc checkOperatorPackage data error. request:{}, response:{}", request, result);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "调用换新中台数据异常");
        }
        return result.getData();
    }
}
