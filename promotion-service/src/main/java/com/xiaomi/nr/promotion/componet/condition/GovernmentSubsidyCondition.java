package com.xiaomi.nr.promotion.componet.condition;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.xiaomi.nr.phoenix.api.dto.response.QualificationSubsidyResponse;
import com.xiaomi.nr.phoenix.api.dto.response.tradeInfo.TradeInfoQueryListResp;
import com.xiaomi.nr.phoenix.api.dto.response.tradeInfo.TradeQualificationInfo;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.api.dto.model.carshop.Region;
import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.enums.ActivityReportCityEnum;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.GovernmentSubsidyPromotionConfig;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.GovernmentSubsidyPhoenixExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.resource.external.SubsidyPhoenixExternalProvider;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class GovernmentSubsidyCondition extends AbstractGiftCondition {

    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 优惠类型
     */
    private PromotionToolType promotionType;
    /**
     * sku-品类
     */
    private Map<String, String> goodsSpuGroupMap;
    /**
     * 活动层面收货地址区域限制
     */
    private List<com.xiaomi.nr.md.promotion.admin.api.dto.activity.Region> activityRegion;

    /**
     * 上报城市ID
     */
    private Integer reportCity;
    @Autowired
    private NacosConfig nacosConfig;

    /**
     * 预售定金
     */
    public static final String TAILORDER = "tailorder";


    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        //判断是否有参加国补活动
        if (CollUtil.isNotEmpty(context.getJoinActivityType()) && context.getJoinActivityType().contains(ActivityTypeEnum.GOVERNMENT_SUBSIDY.getValue())) {
            return false;
        }
        if (MapUtil.isEmpty(goodsSpuGroupMap)) {
            log.warn("goodsSpuGroupMap is empty. actId:{} uid:{}", promotionId, request.getUserId());
            return false;
        }
        // 是否有商品能参加广东国补
        boolean cannotJoinSubsidy = request.getCartList().stream()
                .allMatch(po -> po.getCannotJoinActTypes().contains((long) ActivityTypeEnum.GOVERNMENT_SUBSIDY.getValue()));
        if (cannotJoinSubsidy) {
            return false;
        }
        //获取购物车商品
        List<CartItem> cartList = request.getSourceApi() == SourceApi.SUBMIT ? context.getCarts() : request.getCartList();
        // 过滤赠品/加价购商品，参考  SourceEnum.isGiftBargain。 stream 过滤掉，只剩下主品
        List<CartItem> cartItems = cartList.stream().filter(item -> !SourceEnum.isGiftBargain(item.getSource())).toList();
        //强绑定商品(2个)
        List<CartItem> bindCartItems = cartItems.stream().filter(cartItem -> SourceEnum.isBind(cartItem.getSource())).toList();
        //单品
        List<CartItem> noBindCartItems = cartItems.stream().filter(cartItem -> !SourceEnum.isBind(cartItem.getSource())).toList();
        //强绑定和单品互斥
        if (CollUtil.isEmpty(cartItems) || (CollUtil.isNotEmpty(bindCartItems) && CollUtil.isNotEmpty(noBindCartItems))) {
            return false;
        }
        JSONObject qualification = null;
        //单品
        if (CollUtil.isEmpty(bindCartItems)) {
            //判断主品是否只有1个CartItem && CartItem.count==1
            if (noBindCartItems.size() != 1) {
                return false;
            }
            // 购物车item是否可以作为参与活动的候选item
            CartItem cartItem = noBindCartItems.getFirst();
            Integer cartCount = cartItem.getCount();
            //商品数量是否只有一件
            if (cartCount != 1) {
                return false;
            }
            //判断商品是否在活动池中
            JSONObject inActivityPool = isInActivityPool(cartItems, context, request.getOrgCode(), false);
            Boolean isMatch = inActivityPool.getBool("isMatch");
            String cateCode = "";
            if (isMatch) {
                cateCode = inActivityPool.getStr("cateCode");
            } else {
                String inActivityPoolReason = inActivityPool.getStr("reason");
                log.info("用户mid:{}在政府模式订单orderId:{}下没有活动池商品,原因是:{}", request.getUserId(), request.getOrderId(), inActivityPoolReason);
                return false;
            }
            //判断是否有资格
            if (ActivityReportCityEnum.BJ_REPORT_CITY.getId().equals(reportCity)) {
                qualification = hasBJQualification(cateCode, context, request.getOrgCode());
            } else {
                qualification = hasQualification(cateCode, context);
            }
            context.setIsBind(false);

            //目前只有商城才有6000元限制
            if (StrUtil.isBlank(request.getOrgCode())) {
                //国补活动基数增加6000元限制
                Boolean subsidyLimitComparePrice = subsidyLimitComparePrice(noBindCartItems, cateCode);
                if (!subsidyLimitComparePrice) {
                    log.warn("GovernmentSubsidyCondition.subsidyLimitComparePrice data:{},cateCode:{},subsidyLimit:{}",noBindCartItems,cateCode,subsidyLimitComparePrice);
                    return false;
                }
            }
        } else {
            //判断门店强绑定
            if (StrUtil.isEmpty(request.getOrgCode()) || bindCartItems.size() != 2) {
                return false;
            }
            Pair<CartItem, CartItem> pair = getBindCartItems(bindCartItems);
            if (Objects.isNull(pair)) {
                return false;
            }
            CartItem parentItem = pair.getLeft();
            CartItem childItem = pair.getRight();
            if (parentItem.getCount() != 1 || childItem.getCount() != 1) {
                return false;
            }
            //判断商品是否在活动池中
            JSONObject inActivityPool = isInActivityPool(cartItems, context, request.getOrgCode(), true);
            Boolean isMatch = inActivityPool.getBool("isMatch");
            String cateCode = "";
            if (isMatch) {
                cateCode = inActivityPool.getStr("cateCode");
            } else {
                String inActivityPoolReason = inActivityPool.getStr("reason");
                log.info("用户mid:{}在政府模式订单orderId:{}下没有活动池商品,原因是:{}", request.getUserId(), request.getOrderId(), inActivityPoolReason);
                return false;
            }
            qualification = hasBJQualification(cateCode, context, request.getOrgCode());
            context.setIsBind(true);
        }
        boolean filterResult = cartItems.stream().allMatch(cart -> CartHelper.checkItemActQualify(cart, promotionType.getTypeId(), StringUtils.isEmpty(request.getOrgCode()), null, null));
        if (!filterResult) {
            return false;
        }
        Boolean isQualificationSubsidy = qualification.getBool("isMatch");
        if (!isQualificationSubsidy) {
            String qualificationReason = qualification.getStr("reason");
            log.info("用户mid:{}在政府模式订单orderId:{}下没有资格,原因是:{}", request.getUserId(), request.getOrderId(), qualificationReason);
            return false;
        }
        // region过滤。 request是用户的收货地址region，  activityRegion是活动配置的收货区域限制。 活动上配置到什么级别就判断到什么级别
        JSONObject governmentAddress = isAnyMatchGovernmentAddress(activityRegion, request.getRegion(), request.getOrgCode());
        Boolean isAnyMatchGovernmentAddress = governmentAddress.getBool("isMatch");
        if (!isAnyMatchGovernmentAddress) {
            String addressReason = governmentAddress.getStr("reason");
            log.info("用户mid:{}在政府模式订单orderId:{}下Region没有匹配,原因是:{}", request.getUserId(), request.getOrderId(), addressReason);
            return false;
        }
        // 构建可参与商品的索引，用于action判断，只有一个索引
        return constructorGoodsIndex(cartList, cartItems.stream().map(CartItem::getItemId).toList(), context);
    }

    /**
     * @description: 获取强绑定数据对
     */
    private Pair<CartItem, CartItem> getBindCartItems(List<CartItem> cartItems) {
        //判断是否强绑定商品
        List<CartItem> bindItems = cartItems.stream().filter(cartItem -> SourceEnum.isBind(cartItem.getSource())).toList();
        if (CollUtil.isEmpty(bindItems) || bindItems.size() != 2) {
            return null;
        }
        Map<String, List<CartItem>> cartItemMap = bindItems.stream().collect(Collectors.groupingBy(CartItem::getItemId));
        List<CartItem> childItems = cartItems.stream().filter(cartItem -> StrUtil.isNotEmpty(cartItem.getParentItemId())).toList();
        if (CollUtil.isEmpty(childItems) || childItems.size() != 1) {
            return null;
        }
        //通过子品拿主品
        List<CartItem> parentItems = cartItemMap.get(childItems.getFirst().getParentItemId());
        if (CollUtil.isEmpty(parentItems) || parentItems.size() != 1) {
            return null;
        }
        return Pair.of(parentItems.getFirst(), childItems.getFirst());
    }

    //判断商品是否在活动池中
    private JSONObject isInActivityPool(List<CartItem> cartItems, LocalContext context, String orgCode, Boolean isBind) {
        JSONObject json = JSONUtil.createObj();
        String cateCode = "";
        if (!isBind) {
            //单品
            String skuPackage = CartHelper.getSkuPackage(cartItems.getFirst());
            cateCode = goodsSpuGroupMap.get(skuPackage);
            if (StrUtil.isEmpty(cateCode)) {
                json.put("isMatch", false);
                json.put("reason", "活动中商品品类为空!");
                if (StrUtil.isNotEmpty(orgCode)) {
                    context.setPurchaseSubsidyInvalid(String.valueOf(ErrCode.PROMOTION_ACTIVITY_GOVERNMENT_SUBSIDY_NO_ACTIVITY.getCode()));
                }
                return json;
            }
        } else {
            //强绑定
            String firstSkuPackage = CartHelper.getSkuPackage(cartItems.getFirst());
            String firstCateCode = goodsSpuGroupMap.get(firstSkuPackage);
            String lastSkuPackage = CartHelper.getSkuPackage(cartItems.getLast());
            String lastCateCode = goodsSpuGroupMap.get(lastSkuPackage);
            List<String> cateCodes = new ArrayList<>();
            cateCodes.add(firstCateCode);
            cateCodes.add(lastCateCode);
            List<String> nonNullCodes = cateCodes.stream().filter(Objects::nonNull).toList();
            if (CollUtil.isEmpty(nonNullCodes)) {
                json.put("isMatch", false);
                json.put("reason", "活动中商品品类为空!");
                if (StrUtil.isNotEmpty(orgCode)) {
                    context.setPurchaseSubsidyInvalid(String.valueOf(ErrCode.PROMOTION_ACTIVITY_GOVERNMENT_SUBSIDY_NO_ACTIVITY.getCode()));
                }
                return json;
            }
            if (StrUtil.isNotEmpty(firstCateCode) && StrUtil.isNotEmpty(lastCateCode)) {
                if (!firstCateCode.equals(lastCateCode)) {
                    json.put("isMatch", false);
                    json.put("reason", "购买的两个产品不属于同一类!");
                    return json;
                }
            }
            cateCode = nonNullCodes.getFirst();
        }
        json.put("isMatch", true);
        json.put("cateCode", cateCode);
        return json;
    }

    private JSONObject hasBJQualification(String cateCode, LocalContext context, String orgCode) {
        JSONObject json = JSONUtil.createObj();
        try {
            //判断资格是否满足
            Map<ResourceExtType, ExternalDataProvider<?>> externalDataMap = context.getExternalDataMap();
            SubsidyPhoenixExternalProvider provider = (SubsidyPhoenixExternalProvider) externalDataMap.get(ResourceExtType.PHOENIX_SUBSIDY_QUALIFICATION);
            if (null == provider || null == provider.getData()) {
                json.put("isMatch", false);
                json.put("reason", "活动中北京政府模式provider为空!");
                return json;
            }
            //获取北京政府模式下的资格码列表
            TradeInfoQueryListResp data = provider.getData();
            if (Objects.isNull(data) || CollUtil.isEmpty(data.getQualificationInfoList())) {
                json.put("isMatch", false);
                json.put("reason", "活动中北京政府模式获取三方资格为空!");
                //门店场景下返回987
                if (StrUtil.isNotEmpty(orgCode)) {
                    context.setPurchaseSubsidyInvalid(String.valueOf(ErrCode.PROMOTION_ACTIVITY_PURCHASE_SUBSIDY_NO_QUALIFY.getCode()));
                }
                return json;
            }
            List<TradeQualificationInfo> qualificationInfoList = data.getQualificationInfoList();

            boolean isMatch = qualificationInfoList.stream().anyMatch(qualification -> cateCode.equals(qualification.getQualificationType()));
            Map<String, TradeQualificationInfo> qualificationInfoMap = qualificationInfoList.stream().collect(Collectors.toMap(TradeQualificationInfo::getQualificationType, Function.identity()));
            if (isMatch) {
                //匹配的资格码待使用
                TradeQualificationInfo tradeQualificationInfo = qualificationInfoMap.get(cateCode);
                Map<String, TradeQualificationInfo> useQualificationInfoMap = new HashMap<>();
                useQualificationInfoMap.put(cateCode, tradeQualificationInfo);
                context.setUsedQualifyMap(useQualificationInfoMap);
                json.put("isMatch", true);
            } else {
                //该品类无可用资格码
                context.setPurchaseSubsidyInvalid(String.valueOf(ErrCode.PROMOTION_ACTIVITY_GOVERNMENT_SUBSIDY_NO_QUALIFY_CATE_CODE.getCode()));
                json.put("isMatch", false);
                json.put("reason", "活动中北京政府模式获取三方资格不可用!");
            }
            return json;
        } catch (BizError e) {
            json.put("isMatch", false);
            json.put("reason", "活动中北京政府模式获取三方资格异常!");
            return json;
        }
    }

    private JSONObject hasQualification(String cateCode, LocalContext context) throws BizError {
        JSONObject json = JSONUtil.createObj();
        try {
            //判断资格是否满足
            Map<ResourceExtType, ExternalDataProvider<?>> externalDataMap = context.getExternalDataMap();
            GovernmentSubsidyPhoenixExternalProvider provider = (GovernmentSubsidyPhoenixExternalProvider) externalDataMap.get(ResourceExtType.GOVERNMENT_PHOENIX_SUBSIDY_QUALIFICATION);
            if (null == provider || null == provider.getData()) {
                json.put("isMatch", false);
                json.put("reason", "活动中政府模式provider为空!");
                return json;
            }

            //获取政府模式下的资格码列表
            List<QualificationSubsidyResponse> personalInfoQualificationList = provider.getData();
            if (CollectionUtils.isEmpty(personalInfoQualificationList)) {
                json.put("isMatch", false);
                json.put("reason", "活动中政府模式获取三方资格为空!");
                return json;
            }
            boolean isMatch = personalInfoQualificationList.stream().anyMatch(qualification ->
                    reportCity.equals(qualification.getRegionId()) && cateCode.equals(qualification.getCateCode()) && qualification.getHasQualification());
            if (isMatch) {
                json.put("isMatch", true);
                //json.put("reason","活动中政府模式获取三方资格成功!");
            } else {
                json.put("isMatch", false);
                json.put("reason", "活动中政府模式获取三方资格regionId,cateCode不匹配!");
            }
            return json;
        } catch (BizError e) {
            json.put("isMatch", false);
            json.put("reason", "活动中政府模式获取三方资格异常!");
            return json;
        }
    }

    private Boolean constructorGoodsIndex(List<CartItem> cartList, List<String> itemIds, LocalContext context) {
        if (CollUtil.isEmpty(itemIds)) {
            return false;
        }
        // 如果前端勾选使用优惠，构造可参与活动的商品索引
        List<GoodsIndex> goodsIndexList = new ArrayList<>();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            if (itemIds.contains(item.getItemId())) {
                goodsIndexList.add(new GoodsIndex(item.getItemId(), idx));
            }
        }
        context.setGoodIndex(goodsIndexList);
        return true;
    }

    /**
     * 检查目标收货地址是否有效
     * 1、province匹配
     * 2、city = 0时，返回true，否则继续匹配city
     *
     * @param target         目标区域
     * @param activityRegion 源区域列表
     * @return 如果目标区域与源区域列表中的任何一个匹配，则返回true；否则返回false
     */
    private JSONObject isAnyMatchGovernmentAddress(List<com.xiaomi.nr.md.promotion.admin.api.dto.activity.Region> activityRegion, Region target, String orgCode) {
        JSONObject json = JSONUtil.createObj();
        if (CollUtil.isNotEmpty(nacosConfig.getActivityAreaWhiteList()) && nacosConfig.getActivityAreaWhiteList().contains(promotionId)) {
            json.put("isMatch", true);
            //json.put("reason","白名单活动不进行区域校验");
            return json;
        }
        //门店现场购不校验地址
        if (StrUtil.isNotEmpty(orgCode) && (Objects.isNull(target) || target.getProvince() == 0)) {
            json.put("isMatch", true);
            return json;
        }
        //区域匹配前校验用户收货地址Region不能为空
        if (Objects.isNull(target)) {
            json.put("isMatch", false);
            json.put("reason", "区域匹配前校验用户收货地址Region不能为空!");
            return json;
        }

        for (com.xiaomi.nr.md.promotion.admin.api.dto.activity.Region region : activityRegion) {
            if (region.getProvince() != 0 && region.getProvince().equals(target.getProvince())) {
                if (region.getCity() == 0 || region.getCity().equals(target.getCity())) {
                    json.put("isMatch", true);
                    //json.put("reason","活动配置区域匹配成功");
                    return json;
                }
            }
        }
        json.put("isMatch", false);
        json.put("reason", "活动配置区域匹配不成功");
        return json;
    }

    private Boolean subsidyLimitComparePrice(List<CartItem> cartItems,String cateCode) {
        List<String> subsidyLimitCateCode = nacosConfig.getSubsidyLimitCateCode();
        Long subsidyLimitMoney = nacosConfig.getSubsidyLimitMoney();

        if (CollUtil.isNotEmpty(subsidyLimitCateCode) && subsidyLimitCateCode.contains(cateCode)) {
            Long finalPrice = calculateFinalPrice(cartItems);
            return finalPrice.compareTo(subsidyLimitMoney) <= 0;
        } else {
            return true;
        }
    }

    private long calculateFinalPrice(List<CartItem> cartItems) {
        CartItem cartItem = cartItems.getFirst();
        long curPrice = cartItem.getCartPrice() * cartItem.getCount() - cartItem.getReduceAmount();
        if (curPrice >= 0) {
            //定金预售处理
            if (cartItem.getSaleSource().equals(TAILORDER)) {
                return curPrice + cartItem.getPrePrice() * cartItem.getCount();
            } else {
                return curPrice;
            }
        }
        return 0;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof GovernmentSubsidyPromotionConfig promotionConfig)) {
            log.error("config is not instanceof GovernmentSubsidyPromotionConfig. config:{}", config);
            return;
        }
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        this.goodsSpuGroupMap = promotionConfig.getGoodsSpuGroupMap();
        this.activityRegion = promotionConfig.getRegionList();
        this.reportCity = promotionConfig.getReportCity();
    }
}
