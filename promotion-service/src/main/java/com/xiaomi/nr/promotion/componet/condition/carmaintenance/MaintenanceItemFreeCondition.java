package com.xiaomi.nr.promotion.componet.condition.carmaintenance;

import com.google.common.collect.Lists;
import com.xiaomi.micar.club.api.model.member.MemberInfo;
import com.xiaomi.micar.club.api.resp.member.MemberInfoResp;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionCarActivityCountMapper;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionUserActivityCountMapper;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.CarActivityCount;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.UserActivityCount;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.CarIdentityTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MaintenanceItemFreePromotionConfig;
import com.xiaomi.nr.promotion.resource.external.CarUserVipExternalProvider;
import com.xiaomi.nr.promotion.resource.external.CarVidVipExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 立减条件判断
 *
 * <AUTHOR>
 * @date 2022/07/11
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class MaintenanceItemFreeCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 活动类型
     */
    private PromotionToolType promotionType;

    /**
     * 有效商品
     */
    private List<Long> validGoods;

    /**
     * 优惠车辆身份：赛道尊享会员(SVIP)，赛道版车主
     */
    private Integer carIdentityType;

    /**
     * 会员ID、购车权益ID
     */
    private String carIdentityId;

    /**
     * 活动限购数量
     */
    private long identityJoinLimitNum;

    /**
     * 订单类型
     */
    private List<Integer> workOrderType;

    @Autowired
    private PromotionUserActivityCountMapper userActivityCountMapper;

    @Autowired
    private PromotionCarActivityCountMapper carActivityCountMapper;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {

        // check biz platform
        if (!Objects.equals(context.getBizPlatform(), BizPlatformEnum.MAINTENANCE_REPAIR)) {
            return false;
        }

        // check vid
        if (Objects.isNull(request.getVid()) || StringUtils.isBlank(request.getVid())) {
            return false;
        }

        // check workOrderType
        if (!workOrderType.contains(request.getWorkOrderType())) {
            return false;
        }

        // check identity
        if (!checkVIPIdentity(request, context) || !checkBenefitIdentity(request, context)) {
            return false;
        }

        // check goods
        if (!checkGoods(request, context)) {
            return false;
        }

        return true;
    }

    private boolean checkGoods(CheckoutPromotionRequest request, LocalContext context) {
        List<CartItem> cartList = request.getCartList();

        // 筛选满足数据
        Pair<Boolean, List<GoodsIndex>> goodsPair = goodsActMatch(cartList);

        // 不能参加活动
        if (!goodsPair.getLeft()) {
            return false;
        }

        context.setGoodIndex(goodsPair.getRight());
        return true;
    }

    /**
     * 校验 vip 身份
     * @param request
     * @param context
     * @return
     * @throws BizError
     */
    private boolean checkVIPIdentity(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (!Objects.equals(carIdentityType, CarIdentityTypeEnum.CAR_SHOP_VIP.getCode())) {
            return true;
        }

        CarVidVipExternalProvider provider = (CarVidVipExternalProvider) context.getExternalDataMap().get(ResourceExtType.VID_ULTRA_VIP_MEMBER);
        MemberInfo data = provider.getData();

        if (data == null || !Objects.equals(request.getVid(), data.getVid()) || !Objects.equals(data.getLevel(), Integer.valueOf(carIdentityId))) {
            return false;
        }

        // 为0不限购
        if (Objects.equals(identityJoinLimitNum, 0L)) {
            return true;
        }
        UserActivityCount userActivityCount = userActivityCountMapper.getByUserIdAndPromotionId(Long.valueOf(data.getMid()), promotionId);
        if (userActivityCount == null) {
            return true;
        }
        if (userActivityCount.getNum() >= identityJoinLimitNum) {
            return false;
        }

        return true;
    }

    private boolean checkBenefitIdentity(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (!Objects.equals(carIdentityType, CarIdentityTypeEnum.USAGE_EQUITY.getCode())) {
            return true;
        }

        if (!request.getEquityKeys().contains(carIdentityId)) {
            return false;
        }

        // 为0不限购
        if (Objects.equals(identityJoinLimitNum, 0L)) {
            return true;
        }
        CarActivityCount carActivityCount = carActivityCountMapper.getByVidAndPromotionId(request.getVid(), promotionId);
        if (carActivityCount == null) {
            return true;
        }
        if (carActivityCount.getNum() >= identityJoinLimitNum) {
            return false;
        }

        return true;
    }

    private Pair<Boolean, List<GoodsIndex>> goodsActMatch(List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(cartList)) {
            return Pair.of(Boolean.FALSE, Collections.emptyList());
        }

        boolean canJoinAct = false;
        List<GoodsIndex> indexList = Lists.newArrayList();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            if (Objects.isNull(item)) {
                continue;
            }
            Long ssuId = item.getSsuId();

            // 工时和配件为自费才能使用
            if (Objects.isNull(item.getMaintenanceInfo().getPayType())
                    || !Objects.equals(item.getMaintenanceInfo().getPayType(), 1)) {
                continue;
            }

            // 商品匹配
            if (!validGoods.contains(ssuId)) {
                continue;
            } else {
                canJoinAct = true;
            }

            // 商品是否能参加活动
            if (!CartHelper.checkItemActQualifyCommon(item, promotionType.getTypeId())) {
                continue;
            }

            // 商品有效性
            Long curPrice = CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList());
            if (Objects.equals(curPrice, 0L)) {
                continue;
            }

            indexList.add(new GoodsIndex(item.getItemId(), idx));
        }
        return Pair.of(canJoinAct, indexList);
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof MaintenanceItemFreePromotionConfig)) {
            log.error("config is not instanceof MaintenanceItemFreePromotionConfig. config:{}", config);
            return;
        }
        MaintenanceItemFreePromotionConfig promotionConfig = (MaintenanceItemFreePromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        this.validGoods = promotionConfig.getJoinGoods().getSsuId();
        this.carIdentityType = promotionConfig.getCarIdentityType();
        this.carIdentityId = promotionConfig.getCarIdentityId();
        this.identityJoinLimitNum = promotionConfig.getIdentityJoinLimitNum();
        this.workOrderType = promotionConfig.getWorkOrderType();
    }
}
