package com.xiaomi.nr.promotion.activity.pool;

import com.xiaomi.nr.promotion.api.dto.enums.GoodsLevelEnum;
import com.xiaomi.nr.promotion.api.dto.model.InstallmentDetail;
import com.xiaomi.nr.promotion.api.dto.model.InstallmentGoodItem;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.NrActGoodsMapper;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.NrActScopeMapper;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.NrActivityMapper;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.InstallmentExtInfo;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.NrActGoodsPo;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.NrActScopePo;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.NrActivityPo;
import com.xiaomi.nr.promotion.enums.ActScopeTypeEnum;
import com.xiaomi.nr.promotion.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分期活动
 *
 * <AUTHOR>
 * @data 2024/09/30
 */
@Slf4j
@Component
public class InstallmentActivityPool {
    @Resource
    private NrActivityMapper nrActivityMapper;

    @Resource
    private NrActGoodsMapper nrActGoodsMapper;

    @Resource
    private NrActScopeMapper nrActScopeMapper;

    // 分期活动类型
    public static final Integer type = 13;

    /**
     * 门店范围-所有门店
     */
    private static final int STORE_SCOPE_ALL = 0;
    /**
     * 门店范围-指定门店
     */
    private static final int STORE_SCOPE_APPOINT = 1;

    /**
     * 本地锁
     */
    private final Lock lock = new ReentrantLock();

    /**
     * 所有门店都能参与的分期活动ID列表，key:*，value:活动ID列表
     * 指定门店可参与的分期活动ID列表，key:门店ID，value:活动ID列表
     */
    private Map<String, List<Long>> ORG_CODE_ACT_MAP_CACHE = new ConcurrentHashMap<>();

    /**
     * 商品可参与的分期活动ID列表，key:sku或套装ID，value:活动ID列表
     */
    private Map<Long, List<Long>> GOODS_ACT_MAP_CACHE = new ConcurrentHashMap<>();

    /**
     * 活动信息，key:活动ID，value:活动信息
     */
    private Map<Long, InstallmentDetail> ACT_INFO_MAP_CACHE = new ConcurrentHashMap<>();

    /**
     * 刷新分期活动相关本地缓存
     */
    public void refreshInstallmentCache() {
        Map<String, List<Long>> orgMarkMap = new ConcurrentHashMap<>();
        Map<Long, List<Long>> goodsActIdMap = new ConcurrentHashMap<>();
        Map<Long, InstallmentDetail> actInfoMap = new ConcurrentHashMap<>();

        // 活动列表
        long nowTime = System.currentTimeMillis() / 1000;
        List<NrActivityPo> actList = nrActivityMapper.queryValidListByType(type, nowTime);
        if (CollectionUtils.isEmpty(actList)) {
            log.warn("refreshInstallmentCache, act is empty, type:{}, nowTime:{}", type, nowTime);
            // 更新缓存
            rebuildMapCache(actInfoMap, orgMarkMap, goodsActIdMap);
            return;
        }

        List<Long> actIds = actList.stream().map(NrActivityPo::getId).collect(Collectors.toList());

        // 商品列表
        List<NrActGoodsPo> nrActGoodsPos = nrActGoodsMapper.queryListByType(actIds);
        if (CollectionUtils.isEmpty(actList)) {
            log.warn("refreshInstallmentCache, goods is empty, actIds:{}", actIds);
            // 更新缓存
            rebuildMapCache(actInfoMap, orgMarkMap, goodsActIdMap);
            return;
        }
        log.info("refreshInstallmentCache, queryListByType, nrActGoodsPos:{}", GsonUtil.toJson(nrActGoodsPos));

        // 活动范围
        List<NrActScopePo> nrActScopePos = nrActScopeMapper.queryListById(actIds);
        if (CollectionUtils.isEmpty(actList)) {
            log.info("refreshInstallmentCache, scope is empty, actIds:{}", actIds);
        }

        // 活动map
        Map<Long, NrActivityPo> actMap = actList.stream().collect(Collectors.toMap(NrActivityPo::getId, Function.identity(), (x, y) -> x));

        // 商品map
        Map<Long, List<NrActGoodsPo>> actGoodsPoMap = nrActGoodsPos.stream().collect(Collectors.groupingBy(NrActGoodsPo::getActivityId));

        // 活动门店map
        Map<Long, List<NrActScopePo>> actScopePoMap = nrActScopePos.stream().filter(e -> Objects.nonNull(e.getScopeType()) && e.getScopeType().equals(ActScopeTypeEnum.STORE.getValue())).collect(Collectors.groupingBy(NrActScopePo::getActivityId));

        // 处理活动信息
        for (Long actId : actIds) {
            // 活动信息
            NrActivityPo actPo = actMap.get(actId);
            if (Objects.isNull(actPo)) {
                log.warn("refreshInstallmentCache, skip act, act is {}", actId);
                continue;
            }

            // 商品信息
            List<NrActGoodsPo> goodsPos = actGoodsPoMap.get(actId);
            if (CollectionUtils.isEmpty(goodsPos)) {
                log.warn("refreshInstallmentCache, skip act, goods is empty, act is {}", actId);
                continue;
            }

            // 活动门店列表
            List<NrActScopePo> orgPos = actScopePoMap.get(actId);
            if (CollectionUtils.isEmpty(orgPos)) {
                log.info("refreshInstallmentCache, skip act, org scope is empty, act is {}", actId);
            }

            // 活动扩展信息
            InstallmentExtInfo ext = GsonUtil.fromJson(actPo.getExtInfo(), InstallmentExtInfo.class);
            if (Objects.isNull(ext)) {
                log.warn("refreshInstallmentCache, skip act, ext info is null or empty, actId:{}, ext:{}", actPo.getId(), actPo.getExtInfo());
                return;
            }

            // 构建活动信息
            InstallmentDetail activityInfo = buildInstallmentActivityInfo(actPo, goodsPos);
            if (Objects.isNull(activityInfo)) {
                continue;
            }

            // 门店map
            buildOrgMap(actPo, orgPos, orgMarkMap);

            // 商品map
            buildGoodsMap(activityInfo, goodsActIdMap);

            // 活动map
            buildActMap(activityInfo, actInfoMap);

            log.info("refreshInstallmentCache, cache info, ACT_INFO_MAP_CACHE: {}", GsonUtil.toJson(activityInfo));
        }


        // 更新缓存
        rebuildMapCache(actInfoMap, orgMarkMap, goodsActIdMap);

        log.info("refreshInstallmentCache, cache info, ORG_CODE_ACT_MAP_CACHE: {}", GsonUtil.toJson(ORG_CODE_ACT_MAP_CACHE));
        log.info("refreshInstallmentCache, cache info, GOODS_ACT_MAP_CACHE: {}", GsonUtil.toJson(GOODS_ACT_MAP_CACHE));
    }

    /**
     * 重置本地map缓存
     *
     * @param actInfoMap    活动信息
     * @param orgMarkMap    门店对应对应活动ID
     * @param goodsActIdMap 商品对应活动ID
     */
    private void rebuildMapCache(Map<Long, InstallmentDetail> actInfoMap, Map<String, List<Long>> orgMarkMap, Map<Long, List<Long>> goodsActIdMap) {
        try {
            lock.lock();
            ORG_CODE_ACT_MAP_CACHE = orgMarkMap;
            GOODS_ACT_MAP_CACHE = goodsActIdMap;
            ACT_INFO_MAP_CACHE = actInfoMap;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 构建商品信息缓存
     *
     * @param activityInfo 活动信息
     * @param actMap       活动信息缓存
     */
    private void buildActMap(InstallmentDetail activityInfo, Map<Long, InstallmentDetail> actMap) {
        if (Objects.isNull(activityInfo)) {
            return;
        }

        actMap.put(activityInfo.getId(), activityInfo);
    }

    /**
     * 构建商品可参与活动缓存
     *
     * @param actInfo  活动信息
     * @param goodsMap 商品可参与活动缓存
     */
    private void buildGoodsMap(InstallmentDetail actInfo, Map<Long, List<Long>> goodsMap) {
        if (CollectionUtils.isEmpty(actInfo.getGoodsList())) {
            return;
        }

        for (InstallmentGoodItem item : actInfo.getGoodsList()) {
            List<Long> actList = goodsMap.getOrDefault(item.getId(), new ArrayList<>());
            if (!actList.contains(actInfo.getId())) {
                actList.add(actInfo.getId());
            }
            if (!goodsMap.containsKey(item.getId())) {
                goodsMap.put(item.getId(), actList);
            }
        }
    }

    /**
     * 构建门店map缓存
     *
     * @param actPo  活动信息
     * @param orgPos 门店可参与活动po列表
     * @param orgMap 门店可参与活动缓存
     */
    private void buildOrgMap(NrActivityPo actPo, List<NrActScopePo> orgPos, Map<String, List<Long>> orgMap) {
        if (CollectionUtils.isEmpty(orgPos)) {
            return;
        }

        InstallmentExtInfo ext = GsonUtil.fromJson(actPo.getExtInfo(), InstallmentExtInfo.class);
        if (Objects.isNull(ext)) {
            log.error("refreshInstallmentCache, act ext info is null or empty, actId:{}, ext:{}", actPo.getId(), actPo.getExtInfo());
            return;
        }

        if (Objects.isNull(ext.getStoreRange())) {
            log.info("refreshInstallmentCache, act ext->storeRange is null, actId:{}, ext:{}", actPo.getId(), actPo.getExtInfo());
            return;
        }

        // 所有门店可参与的活动
        if (ext.getStoreRange().equals(STORE_SCOPE_ALL)) {
            List<Long> actList = orgMap.getOrDefault("*", new ArrayList<>());
            actList.add(actPo.getId());
            orgMap.put("*", actList);
            return;
        }

        // 指定门店可参与的活动
        NrActScopePo po = orgPos.getFirst();
        if (Objects.isNull(po) || Strings.isEmpty(po.getScopeValue())) {
            log.info("refreshInstallmentCache, act org scope is empty, actId:{}, po:{}", actPo.getId(), GsonUtil.toJson(po));
            return;
        }

        List<String> orgList = List.of(po.getScopeValue().split(","));
        if (CollectionUtils.isEmpty(orgList)) {
            log.info("refreshInstallmentCache, act org scope value is empty, actId:{}, po:{}", actPo.getId(), GsonUtil.toJson(po));
            return;
        }

        for (String orgCode : orgList) {
            List<Long> actList = orgMap.getOrDefault(orgCode, new ArrayList<>());
            actList.add(actPo.getId());
            orgMap.put(orgCode, actList);
        }
    }

    /**
     * 构建分期活动信息
     *
     * @param actPo    活动po信息
     * @param goodsPos 可参与活动的商品
     * @return InstallmentDetail
     */
    private InstallmentDetail buildInstallmentActivityInfo(NrActivityPo actPo, List<NrActGoodsPo> goodsPos) {
        InstallmentDetail act = new InstallmentDetail();
        act.setId(actPo.getId());
        act.setName(actPo.getName());
        act.setStartTime(actPo.getStartTime());
        act.setEndTime(actPo.getEndTime());
        act.setStatus(actPo.getStatus());

        List<Integer> channels = new ArrayList<>();
        if (Strings.isNotEmpty(actPo.getChannel())) {
            channels = Arrays.stream(actPo.getChannel().split(","))
                    .map(Integer::parseInt).collect(Collectors.toList());
        }
        act.setChannels(channels);

        InstallmentExtInfo ext = GsonUtil.fromJson(actPo.getExtInfo(), InstallmentExtInfo.class);
        if (Objects.isNull(ext)) {
            log.warn("refreshInstallmentCache, act ext info is empty or null, actId:{}, ext:{}", actPo.getId(), actPo.getExtInfo());
            return null;
        }

        List<InstallmentGoodItem> skuList = filterMakeGoodsList(goodsPos, GoodsLevelEnum.SKU.getValue());
        List<InstallmentGoodItem> packageList = filterMakeGoodsList(goodsPos, GoodsLevelEnum.PACKAGE_ID.getValue());
        List<InstallmentGoodItem> ssuList = filterMakeGoodsList(goodsPos, GoodsLevelEnum.SSU_ID.getValue());

        List<InstallmentGoodItem> goodsList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(skuList)) {
            goodsList.addAll(skuList);
        }
        if (CollectionUtils.isNotEmpty(packageList)) {
            goodsList.addAll(packageList);
        }

        if (CollectionUtils.isNotEmpty(ssuList)) {
            goodsList.addAll(ssuList);
        }

        act.setInstallmentType(ext.getInstallmentType());
        act.setInstallmentTimes(ext.getInstallmentTimes());
        act.setMinAmount(ext.getMinActivityAmount());
        act.setMaxAmount(ext.getMaxActivityAmount());
        act.setInterestPayer(ext.getInterestPayer());
        act.setGoodsList(goodsList);

        return act;
    }

    /**
     * 筛选并构建商品列表
     *
     * @param goodsPos   可参与活动的商品
     * @param goodsLevel 要筛选并构建的商品等级
     * @return
     */
    private List<InstallmentGoodItem> filterMakeGoodsList(List<NrActGoodsPo> goodsPos, int goodsLevel) {
        return goodsPos.stream()
                .filter(e -> Objects.nonNull(e.getProductIdType()) && e.getProductIdType().equals(goodsLevel))
                .map(NrActGoodsPo::getProductId).distinct().map(id -> {
                    InstallmentGoodItem item = new InstallmentGoodItem();
                    item.setId(id);
                    item.setType(goodsLevel);
                    return item;
                }).toList();
    }

    /**
     * 获取门店可参与的活动列表
     *
     * @param orgCode 门店ID
     * @return List
     */
    public List<Long> getOrgCanJoinActId(String orgCode) {
        if (Strings.isEmpty(orgCode)) {
            return Collections.emptyList();
        }

        if (ORG_CODE_ACT_MAP_CACHE.containsKey("*") && ORG_CODE_ACT_MAP_CACHE.containsKey(orgCode)) {
            List<Long> allOrgActList = new ArrayList<>();
            allOrgActList.addAll(ORG_CODE_ACT_MAP_CACHE.get("*"));
            allOrgActList.addAll(ORG_CODE_ACT_MAP_CACHE.get(orgCode));
            return allOrgActList;
        }

        if (ORG_CODE_ACT_MAP_CACHE.containsKey("*")) {
            return ORG_CODE_ACT_MAP_CACHE.get("*");
        }

        if (ORG_CODE_ACT_MAP_CACHE.containsKey(orgCode)) {
            return ORG_CODE_ACT_MAP_CACHE.get(orgCode);
        }
        return Collections.emptyList();
    }

    /**
     * 获取商品可以参与的活动ID
     *
     * @param id sku或套装ID
     * @return List
     */
    public List<Long> getGoodsCanJoinActId(long id) {
        return GOODS_ACT_MAP_CACHE.get(id);
    }

    /**
     * 获取活动信息
     *
     * @param id 活动ID
     * @return InstallmentDetail
     */
    public InstallmentDetail getActInfo(long id) {
        return ACT_INFO_MAP_CACHE.get(id);
    }

}
