package com.xiaomi.nr.promotion.engine;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutContext;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

/**
 * 促销工具
 *
 * <AUTHOR>
 */
public interface PromotionTool {
    /**
     * 进行优惠计算
     *
     * @param request         请求参数
     * @param checkoutContext 上下文
     * @throws BizError 业务异常
     */
    void doCheckout(CheckoutPromotionRequest request, CheckoutContext checkoutContext) throws BizError;


    /**
     * 获取优惠工具类型
     *
     * @return 优惠工具类型
     * @see PromotionToolType
     */
    PromotionToolType getType();

}
