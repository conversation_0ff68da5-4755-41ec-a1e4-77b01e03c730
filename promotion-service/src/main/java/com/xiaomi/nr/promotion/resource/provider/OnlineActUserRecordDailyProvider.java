package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 用户线上活动参与记录
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OnlineActUserRecordDailyProvider implements ResourceProvider<OnlineActUserRecordDailyProvider.ActUserRecordDaily> {
    /**
     * 线上用户参与活动记录
     */
    private ResourceObject<ActUserRecordDaily> resourceObject;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Override
    public ResourceObject<ActUserRecordDaily> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<ActUserRecordDaily> object) {
        this.resourceObject = object;
    }

    /**
     * 扣减库存
     */
    @Override
    public void lock() throws BizError {
        log.info("lock act user record daily resource. {}", resourceObject);
        activityRedisDao.setUserActDailyRecord(resourceObject.getContent().getActId(),
                resourceObject.getContent().getUid(), resourceObject.getContent().getDateTimeMills(),
                resourceObject.getOrderId(), resourceObject.getContent().getExpireTime());
        log.info("lock act user record daily resource. {}", resourceObject);
    }

    @Override
    public void consume() {
        log.info("consume act user record total resource. {}", resourceObject);
    }

    @Override
    public void rollback() throws BizError {
        log.info("rollback act user record daily resource. {}", resourceObject);
        activityRedisDao.delUserActDailyRecord(resourceObject.getContent().getActId(),
                resourceObject.getContent().getUid(), resourceObject.getContent().getDateTimeMills());
        log.info("rollback act user record daily resource. {}", resourceObject);
    }

    @Override
    public String conflictText() {
        return "插入记录失败";
    }

    /**
     * 活动记录
     */
    @Data
    public static class ActUserRecordDaily {
        /**
         * 用户ID
         */
        private Long uid;
        /**
         * 活动ID
         */
        private Long actId;
        /**
         * 时间(毫秒）
         */
        private Long dateTimeMills;
        /**
         * 过期时间 (秒）
         */
        private Long expireTime;
    }
}
