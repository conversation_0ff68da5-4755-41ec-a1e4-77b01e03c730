package com.xiaomi.nr.promotion.resource.external;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.xiaomi.nr.phoenix.api.dto.request.subsidyqualify.QuerySubsidyQualificationForTradeReq;
import com.xiaomi.nr.phoenix.api.dto.response.QualificationSubsidyResponse;
import com.xiaomi.nr.promotion.activity.pool.PromotionInstancePool;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.GovernmentQualificationEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.domain.subsidyactivity.service.common.QualificationInfoService;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.model.promotionconfig.GovernmentSubsidyPromotionConfig;
import com.xiaomi.nr.promotion.util.CartHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2024/12/18 16:58
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class GovernmentSubsidyPhoenixExternalProvider extends ExternalDataProvider<List<QualificationSubsidyResponse>> {
    @Autowired
    private QualificationInfoService qualificationInfoService;

    @Autowired
    private PromotionInstancePool promotionInstancePool;

    private ListenableFuture<List<QualificationSubsidyResponse>> future;

    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        if (StrUtil.isBlank(request.getPersonalInfo())) {
            return;
        }
        List<CartItem> cartList = request.getCartList();
        boolean cannotJoinSubsidy = cartList.stream()
                .allMatch(po -> po.getCannotJoinActTypes().contains((long) ActivityTypeEnum.GOVERNMENT_SUBSIDY.getValue()));
        if (cannotJoinSubsidy) {
            return;
        }
        List<ActivityTool> activityTools = promotionInstancePool.activitySearcher(request.getChannel(), request.getOrgCode(), CartHelper.getSkuPackageList(request.getCartList()));
        boolean canJoinAct = activityTools.stream()
                .anyMatch(activityTool -> activityTool.getType().getTypeId() == ActivityTypeEnum.GOVERNMENT_SUBSIDY.getValue());
        if (!canJoinAct) {
            return;
        }
        //3:深圳,15：天津   ----目前只有深圳和天津调此三方优惠获取资格码接口
        List<Integer> activityReportCity = Lists.newArrayList(GovernmentQualificationEnum.GUANGDONG_SZ.id,GovernmentQualificationEnum.TIANJIN.id);
        //获取国补活动
        List<ActivityTool> governmentActivity = activityTools.stream().filter(activityTool -> activityTool.getType().getTypeId() == ActivityTypeEnum.GOVERNMENT_SUBSIDY.getValue()).toList();
        //获取国补活动配置信息
        List<GovernmentSubsidyPromotionConfig> governmentConfigs = governmentActivity.stream().map(activity -> (GovernmentSubsidyPromotionConfig) activity.getPromotionConfig()).toList();
        //判断是否匹配
        boolean isMatch = governmentConfigs.stream().anyMatch(config -> activityReportCity.contains(config.getReportCity()));
        if (isMatch) {
            //调用三方请求资格列表
            QuerySubsidyQualificationForTradeReq qualification = QuerySubsidyQualificationForTradeReq.builder().build();
            qualification.setPersonalInfo(request.getPersonalInfo());
            future = qualificationInfoService.getPersonalInfoQualificationInfoAsync(qualification);
        }
    }

    @Override
    protected long getTimeoutMills() {
        return DEFAULT_TIMEOUT;
    }

    @Override
    protected ListenableFuture<List<QualificationSubsidyResponse>> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.GOVERNMENT_PHOENIX_SUBSIDY_QUALIFICATION;
    }
}
