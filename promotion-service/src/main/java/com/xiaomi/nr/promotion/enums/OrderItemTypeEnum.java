package com.xiaomi.nr.promotion.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;

@Getter
@AllArgsConstructor
public enum OrderItemTypeEnum {

    CAR_LIFE_SERVICE_PACKGE(53, "车商城服务包"),
    CAR_LIFE_TRAINING_COURSE(54, "车商城培训课程"),
    CAR_LIFE_SKIN_SERVICE(55, "车商城贴膜"),
    CAR_LIFE_EXTENDED_INSURANCE(56, "车商城整车延保"),
    CAR_LIFE_VIRTUAL_GOODS(57, "车商城虚拟商品");

    private final int code;

    private final String desc;

    private static final List<Integer> validOrderItemType = new ArrayList<>();

    static {
        validOrderItemType.add(CAR_LIFE_SERVICE_PACKGE.code);
        validOrderItemType.add(CAR_LIFE_TRAINING_COURSE.code);
        validOrderItemType.add(CAR_LIFE_SKIN_SERVICE.code);
        validOrderItemType.add(CAR_LIFE_EXTENDED_INSURANCE.code);
        validOrderItemType.add(CAR_LIFE_VIRTUAL_GOODS.code);
    }

    public static boolean isValidOrderItemType(Integer code) {
        if (Objects.isNull(code)) {
            return false;
        }
        return validOrderItemType.contains(code);
    }

}
