package com.xiaomi.nr.promotion.entity.mysql.promotionuser;

import lombok.Data;
import lombok.ToString;

/**
 * 对应数据表中的Activity_Resource
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
@Data
@ToString
public class PromotionResourceStatus {
    /**
     * 订单号码
     */
    private Long orderId;

    /**
     * init:0; locked:0; commit:1; rollback:2
     */
    private int status;

    /**
     * order from 1: 商城 2：门店
     */
    private Integer tradeFrom;

    /**
     * 记录生成时间
     */
    private Long createTime;

    /**
     * 状态更改时间
     */
    private Long updateTime;

    /**
     * 用户Id
     */
    private Long uid;
}
