package com.xiaomi.nr.promotion.disruptor;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class StockRecord {
    /**
     * 促销ID
     */
    private Long activityId;

    /**
     * 活动类型
     */
    private Integer activityType;

    /**
     * 行为 commit/rollback
     */
    private String action;

    /**
     * 资源类型
     */
    private Integer resourceType;

    /**
     * 门店
     */
    private String orgCode;

    /**
     * 从品数量 key: groupId, val:(key: sku val:count)
     */
    private Map<Long, Map<String, Integer>> additionalCountMap;

    /**
     * 时间(毫秒）
     */
    private Long dateTimeMills;
}
