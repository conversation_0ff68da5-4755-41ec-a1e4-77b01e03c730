package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.micar.club.api.model.member.MemberInfo;
import com.xiaomi.micar.club.api.resp.member.MemberInfoResp;
import com.xiaomi.nr.promotion.activity.pool.ActSearchParam;
import com.xiaomi.nr.promotion.activity.pool.MaintenanceActivitySearcher;
import com.xiaomi.nr.promotion.activity.pool.PromotionInstancePool;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.domain.activity.impl.CarMaintenanceActivityDomain;
import com.xiaomi.nr.promotion.domain.activity.service.common.VipMemberService;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.util.CartHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-01-04 10:27
*/
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class CarVidVipExternalProvider extends ExternalDataProvider<MemberInfo> {

    @Autowired
    private VipMemberService vipMemberService;

    @Autowired
    private MaintenanceActivitySearcher maintenanceActivitySearcher;

    private ListenableFuture<MemberInfo> future;

    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        if (!Objects.equals(request.getChannel(), ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue())) {
            return;
        }

        if (StringUtils.isBlank(request.getVid())) {
            return;
        }

        List<ActSearchParam.GoodsInSearch> goodsInSearchList = maintenanceActivitySearcher.createSearchGoods(request.getCartList());
        ActSearchParam param = new ActSearchParam()
                .setChannel(request.getChannel())
                .setGoodsList(goodsInSearchList);
        List<ActivityTool> activityTools = maintenanceActivitySearcher.searchCarActivity(param);

        boolean canJoinAct = activityTools.stream()
                .anyMatch(activityTool -> activityTool.getType().getTypeId() == ActivityTypeEnum.MAINTENANCE_REPAIR_DISCOUNT.getValue());
        if (!canJoinAct) {
            return;
        }
        future = vipMemberService.getVidMemberInfoAsync(request.getVid());
    }

    @Override
    protected long getTimeoutMills() {
        return DEFAULT_TIMEOUT;
    }

    @Override
    protected ListenableFuture<MemberInfo> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.VID_ULTRA_VIP_MEMBER;
    }
}
