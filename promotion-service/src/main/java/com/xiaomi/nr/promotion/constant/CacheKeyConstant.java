package com.xiaomi.nr.promotion.constant;

/**
 * 缓存key常量定义
 *
 * <AUTHOR>
 * @date 2021/3/24
 */
public class CacheKeyConstant {

    // -------------------- 活动开始 ------------------

    /**
     * 活动缓存key
     * type: string
     * key: pulse_activitycache_actid_{actId} (注：actId: 活动ID）
     * value: activity json {@link com.xiaomi.nr.promotion.entity.redis.ActivityInfo}
     */
    public static final String KEY_ACT = "pulse_activitycache_actid_{actId}";

    /**
     * 通过orgCode通过活动列表缓存
     * type: list
     * key: pulse_new_activity_list_orgcode_{orgCode} (注：orgCode: 组织码）
     * item-value: activityId {@link Long}
     */
    public static final String KEY_ACT_ID_LIST_ORGCODE = "pulse_activitycache_orgcode_{orgCode}";

    /**
     * 通过clientId获取活动列表缓存
     * type: list
     * key: pulse_new_activity_list_clientid_{clientId} (注：clientId: 客户端Id）
     * item-value: activityId long
     */
    public static final String KEY_ACT_CLIENTID = "pulse_activitycache_clientid_{clientId}";

    /**
     * 活动每人每天参与次数
     * type: string
     * key: pulse_act_new_limit_{uidType}_{actId}_{userId}_{date}
     * (注：uidType: 用户类型， actId: 活动ID userId: 用户ID date:yyyy-MM-dd）
     * value: int
     */
    public static final String KEY_ACT_NUM_UTYPE_PERSON_DAY = "pulse_act_new_limit_{uidType}_{actId}_{userId}_{date}";

    /**
     * 活动每人期间参与次数（无UidType)
     * type: string
     * key: pulse_act_new_limit_{actId}_{userId}_{date}
     * (注：actId: 活动ID userId: 用户ID date:yyyy-MM-dd）
     * value: int
     */
    public static final String KEY_ACT_NUM_PERSON = "pulse_act_new_limit_{actId}_{userId}";

    /**
     * 活动每人活动期间参与次数
     * type: string
     * key: pulse_act_new_limit_{uidType}_{actId}_{userId}_{date}
     * (注：uidType: 用户类型， actId: 活动ID userId: 用户ID date:yyyy-MM-dd）
     * value: int
     */
    public static final String KEY_ACT_NUM_UTYPE_PERSON = "pulse_act_new_limit_{uidType}_{actId}_{userId}";

    /**
     * 活动每人每天参与次数（无UidType)
     * type: string
     * key: pulse_act_new_limit_{actId}_{userId}_{date}
     * (注：actId: 活动ID userId: 用户ID date:yyyy-MM-dd）
     * value: int
     */
    public static final String KEY_ACT_NUM_PERSON_DAY = "pulse_act_new_limit_{actId}_{userId}_{date}";

    /**
     * 活动期间全国门店活动每天总量
     * type: string
     * key: pulse_act_new_allstore_limit_{actId}_{date} (注: actID: 活动ID date:yyyy-MM-dd）
     * value: int
     */
    public static final String KEY_ACT_NUM_ALL_STORE_DAY = "pulse_act_new_allstore_limit_{actId}_{date}";

    /**
     * 活动期间全国门店活动总量
     * type: string
     * key: pulse_act_new_allstore_limit_{actId} (注: actID: 活动ID）
     * value: int
     */
    public static final String KEY_ACT_NUM_ALL_STORE = "pulse_act_new_allstore_limit_{actId}";

    /**
     * 活动期间每个门店活动每天总量
     * type: string
     * key: pulse_act_new_store_limit_{orgCode}_{actId}_{date} (注： orgCode: 门店Code actID: 活动ID date: yyyy-MM-dd）
     * value: int
     */
    public static final String KEY_ACT_NUM_STORE_DAY = "pulse_act_new_store_limit_{orgCode}_{actId}_{date}";

    /**
     * 活动期间每个门店活动总量
     * type: string
     * key: pulse_act_new_store_limit_{orgCode}_{actId} (注： orgCode: 门店Code actID: 活动ID）
     * value: int
     */
    public static final String KEY_ACT_NUM_STORE = "pulse_act_new_store_limit_{orgCode}_{actId}";

    /**
     * 直降活动mid每人参与次数key直降
     * type: string
     * key: pulse_act_onsale_limit_{activityId}_{uid}_{skuPackage}
     * (注： activityId:活动ID uid: 用户ID skuPackage:sku或packageId）
     * value: int
     */
    public static final String KEY_ONSALE_LIMIT_USER_USED = "pulse_act_onsale_limit_{activityId}_{uid}_{skuPackage}";

    /**
     * 直降活动手机号每人参与次数key
     * type: string
     * key: pulse_act_onsale_limit_mobile_{activityId}_{uid}_{skuPackage}
     * (注： activityId:活动ID uid: 用户ID skuPackage:sku或packageId）
     * value: int
     */
    public static final String KEY_ONSALE_LIMIT_USER_MOBILE_USED = "pulse_act_onsale_limit_mobile_{activityId}_{uid}_{skuPackage}";

    /**
     * 直降活动每天每个门店参与次数key
     * type: string
     * key: pulse_act_onsale_limit_{activityId}_{orgcode}_{time}_{skuPackage}
     * (注： activityId:活动ID orgcode:门店Code time:时间yyyy-MM-dd skuPackage:sku或packageId）
     * value: int
     */
    public static final String KEY_ONSALE_LIMIT_DAY_ONE_USED = "pulse_act_onsale_limit_{activityId}_{orgcode}_{time}_{skuPackage}";

    /**
     * 直降活动每天全国门店参与次数key
     * type: string
     * key: pulse_act_onsale_limit_{activityId}_{time}_{skuPackage}
     * (注： activityId:活动ID time:时间yyyy-MM-dd skuPackage:sku或packageId）
     * value: int
     */
    public static final String KEY_ONSALE_LIMIT_DAY_ALL_USED = "pulse_act_onsale_limit_{activityId}_{time}_{skuPackage}";

    /**
     * 直降活动期间每个门店参与次数key
     * type: string
     * key: pulse_act_onsale_limit_{activityId}_{orgcode}_{skuPackage}
     * (注： activityId:活动ID orgCode:门店Code skuPackage:sku或packageId）
     * value: int
     */
    public static final String KEY_ONSALE_LIMIT_ACT_ONE_USED = "pulse_act_onsale_limit_{activityId}_{orgcode}_{skuPackage}";

    /**
     * 直降活动期间全国门店参与次数key
     * type: string
     * key: pulse_act_onsale_limit_{activityId}_{skuPackage}
     * (注： activityId:活动ID skuPackage:sku或packageId）
     * value: int
     */
    public static final String KEY_ONSALE_LIMIT_ACT_ALL_USED = "pulse_act_onsale_limit_{activityId}_{skuPackage}";

    /**
     * 活动状态
     * type: hash
     * key: pulse_act_status_{actId} (注： actID: 活动ID）
     * field: joined_num value: int
     */
    public static final String KEY_ACT_STATUS = "pulse_act_status_{actId}";
    public static final String FILED_ACT_STATUS_JOINED_NUM = "joined_num";

    /**
     * 用户参加活动的记录的redis key，子key是uid，值是订单号
     * type: hash
     * key: xm_shop_pulse_activity_record_{actId} (注： actID: 活动ID）
     * field: uid  value: int
     */
    public static final String KEY_ACT_TOTAL_RECORD = "xm_shop_pulse_activity_record_{actId}";
    public static final String FILED_ACT_TOTAL_RECORD_UID = "{uid}";

    /**
     * 用户参加活动的记录(DAILY)，子key是uid，值是订单号
     * type: hash
     * key: xm_shop_pulse_activity_record_{actId}_{date} (注： actID: 活动ID, date: yyyy-MM-dd）
     * field: uid  value: int
     */
    public static final String KEY_ACT_DAILY_RECORD = "xm_shop_pulse_activity_record_{actId}_{date}";
    public static final String FILED_ACT_DAILY_RECORD_UID = "{uid}";

    /**
     * 买赠参互次数
     * <p>
     * type: string
     * key: pulse_act_buygift_limit_{activityId}_{sku}_{groupId}
     * (注： activityId:活动ID sku:SKU groupId:赠品组ID）
     * value: int
     */
    public static final String KEY_ACT_BUYGIFT_LIMIT = "pulse_act_buygift_limit_{activityId}_{sku}_{groupId}";

    /**
     * 24年北京以旧换新补贴活动参加次数
     * <p>
     * type: string
     * key: promotion_act_purchase_subsidy_limit_{idCard}
     * (注： idCard：身份证）
     * value: int
     */
    public static final String KEY_ACT_PURCHASE_SUBSIDY_LIMIT_TOTAL = "promotion_act_purchase_subsidy_limit_{idCard}";

    /**
     * 24年北京以旧换新补贴活动各个品类参加次数
     * <p>
     * type: string
     * key: promotion_act_purchase_subsidy_limit_{idCard}_{spuGroup}
     * (注： idCard：身份证，spuGroup：品类）
     * value: int
     */
    public static final String KEY_ACT_PURCHASE_SUBSIDY_LIMIT_GROUP = "promotion_act_purchase_subsidy_limit_{idCard}_{spuGroup}";

    /**
     * 加价购参互次数
     * <p>
     * type: string
     * key: pulse_act_bargain_limit_{activityId}_{sku}_{groupId}
     * (注： activityId:活动ID sku:SKU groupId:加价购组ID）
     * value: int
     */
    public static final String KEY_ACT_BARGAIN_LIMIT = "pulse_act_bargain_limit_{activityId}_{sku}_{groupId}";

    /**
     * 活动商品库存
     * <p>
     * type: string
     * key: pulse_act_goods_limit_{activityId}_{sku}
     * (注： activityId:活动ID sku:SKU）
     * value: int
     */
    public static final String KEY_ACT_GOODS_LIMIT = "pulse_act_goods_limit_{activityId}_{sku}";

    /**
     * 活动商品每个门店数量
     */
    public static final String KEY_ACT_GOODS_STORE_LIMIT = "promotion_act_goods_store_limit_{activityId}_{skuPackageId}_{orgCode}";

    /**
     * 每人（mid）每个门店活动数量
     */
    public static final String KEY_ACT_PERSON_STORE_LIMIT = "promotion_act_store_limit_{actId}_{userId}_{orgCode}";

    /**
     * 每人（手机号）每个门店活动数量
     */
    public static final String KEY_ACT_PERSON_MOBILE_STORE_LIMIT = "promotion_act_store_limit_mobile_{actId}_{userId}_{orgCode}";
    // -------------------- 券开始 ------------------
    /**
     * 旧有码优惠券信息
     * type: string
     * key: pulse_new_codecoupontype_2_{typeId}  ）(注：typeId: 类型ID
     * value: json  {@link com.xiaomi.nr.promotion.entity.redis.CouponInfo}
     */
    public static final String KEY_COUPON_OLD_CODE_INFO = "pulse_new_codecoupontype_2_{typeId}";

    /**
     * 新有码和无码优惠券信息
     * type: string
     * key: pulse_new_coupontype_2_{typeId} (注：typeId: 类型ID
     * value: json  {@link com.xiaomi.nr.promotion.entity.redis.CouponInfo}
     */
    public static final String KEY_COUPON_NEW_INFO = "nr:coupon:oldInfo:{typeId}";

    // -------------------- 商品开始 ------------------
    /**
     * 通过sku获取商品信息
     * type: string
     * key: pulse_mapcache_skupackage_{sku} (注：sku: 商品sku）
     * value: json java.util.Map<Long,com.xiaomi.nr.promotion.entity.redis.SkuCatProdCommoPkg>
     */
    public static final String KEY_SKUPACKAGEINFO = "pulse_mapcache_skupackage_{sku}";

    // -------------------- 门店开始 ------------------
    /**
     * 门店详情缓存key
     * type: string
     * key: pulse_storecache_orgcode_{orgcode} (注：orgcode: 门店编码）
     * value: json {@link com.xiaomi.nr.promotion.entity.redis.OrgInfo}
     */
    public static final String KEY_STORE_ORGINFO = "pulse_storecache_orgcode_{orgcode}";

    /**
     * 活动缓存key
     * type: string
     * key: pulse_storescache_all
     * value: store-list json {@link com.xiaomi.nr.promotion.entity.redis.OrgInfo}
     */
    public static final String KEY_STORE_CACHE_ALL = "pulse_storescache_all";

    /**
     * global activity inexclude
     * type: string
     * key: pulse_global_activity_inexclude
     * value: json  {@link com.xiaomi.nr.promotion.entity.redis.CompareItem}
     */
    public static final String KEY_ACT_GLOBAL_INEXCLUDE = "pulse_global_activity_inexclude_2";

    /**
     * global coupon inexclude
     * type: string
     * key: pulse_global_activity_inexclude
     * value: json  {@link com.xiaomi.nr.promotion.entity.redis.CompareItem}
     */
    public static final String KEY_COUPON_GLOBAL_INEXCLUDE = "pulse_global_coupon_inexclude_2";

    /**
     * global inexclude
     * type: string
     * key: pulse_global_inexclude_2
     * value: json  {@link com.xiaomi.nr.promotion.entity.redis.CompareItem}
     */
    public static final String KEY_GLOBAL_INEXCLUDE = "pulse_global_inexclude_2";

    /**
     * 用户红包缓存key
     * type: string
     * key: shopapi_redpacket_user_{userId}  (注：userId: 用户ID）
     * value: json {@link com.xiaomi.nr.promotion.entity.mysql.promotionuser.Redpacket}
     */
    public static final String KEY_REDPACKET_USER = "shopapi_redpacket_user_{userId}";

    // -------------------- 礼品卡 ------------------
    /**
     * 礼品卡类型信息
     * type: hash
     * key: pulse_new_ecardtype_2
     * field: typeId  value: json  {@link com.xiaomi.nr.promotion.entity.redis.CouponInfo}
     */
    public static final String KEY_ECARD_TYPE_INFO = "shopapi_pulsev3_ecard_type_worker";
    public static final String FILED_ECARD_TYPE_INFO = "{typeId}";

    // -------------------- client ------------------
    /**
     * client类型信息
     * type: string
     * key: public:clients:cn_karos
     */
    public static final String KEY_CLIENT_ALL_INFO = "public:clients:cn_karos";

    // -------------------- 北京消费券人群 ------------------
    /**
     * 北京消费券人群信息
     * type: hash
     * key: shopapi_beijing_coupon_user_get_list
     * filed: string userId
     * value: string
     */
    public static final String KEY_BEIJINGCOUPON_USER = "shopapi_beijing_coupon_user_get_list";

    /**
     * 北京消费劵使用次数（支付后）
     * type: string
     * key: shoppingcoupon_{userId}_{ecardId}
     * value: int
     */
    public static final String KEY_BEIJINGCOUPON_USED_TIMES = "shoppingcoupon_{userId}_{ecardId}";
}

