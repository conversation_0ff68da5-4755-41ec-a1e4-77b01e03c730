package com.xiaomi.nr.promotion.dao.redis.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xiaomi.nr.promotion.constant.CacheKeyConstant;
import com.xiaomi.nr.promotion.dao.redis.StoreRedisDao;
import com.xiaomi.nr.promotion.entity.redis.OrgInfo;
import com.xiaomi.nr.promotion.enums.AreaEnum;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.RedisClusterAutoSwitchHelper;
import com.xiaomi.nr.promotion.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 门店缓存获取
 *
 * <AUTHOR>
 * @date 2021/4/29
 */
@Component
public class StoreRedisDaoImpl implements StoreRedisDao {
    /**
     * 信息本地缓存. 缓存5分钟
     * key: orgCode val: OrgInfo
     */
    private final Cache<String, OrgInfo> localCache = CacheBuilder.newBuilder().expireAfterWrite(5, TimeUnit.MINUTES).build();

    /**
     * 信息本地缓存. 缓存5分钟
     * key: orgCode val: OrgInfo
     */
    private final Cache<String, OrgInfo> localOrgCache = CacheBuilder.newBuilder().expireAfterWrite(5, TimeUnit.MINUTES).build();

    /**
     * 信息本地缓存. 缓存5分钟
     * key: KEY_STORE_CACHE_ALL val: allStoreCache
     */
    private final Cache<String, List<OrgInfo>> allStoreCache = CacheBuilder.newBuilder().expireAfterWrite(5, TimeUnit.MINUTES).build();

//    /**
//     * 缓存操作对象
//     */
//    @Autowired
//    @Qualifier("stringPromotionRedisTemplate")
//    private RedisTemplate<String, String> redisTemplate;

    /**
     * 缓存操作
     */
    @Autowired
    private RedisClusterAutoSwitchHelper switchHelper;


    /**
     * 获取门店详情
     *
     * @param orgCode 门店编码
     * @return 门店信息
     */
    @Override
    public OrgInfo getOrgInfo(String orgCode) {
        OrgInfo info = localCache.getIfPresent(orgCode);
        if (info != null) {
            return info;
        }

        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String key = StringUtil.formatContent(CacheKeyConstant.KEY_STORE_ORGINFO, orgCode);
        String redisOrgInfoJson = operations.get(key);
        info = GsonUtil.fromJson(redisOrgInfoJson, OrgInfo.class);
        if (info != null) {
            localCache.put(orgCode, info);
        }
        return info;
    }

    /**
     * 获取门店详情
     *
     * @param orgCode 门店编码
     * @return 门店信息
     */
    @Override
    public OrgInfo getOrgDetail(String orgCode) {
        OrgInfo orgInfo = localOrgCache.getIfPresent(orgCode);
        if (orgInfo == null) {
            loadOrgInfo();
        }
        return localOrgCache.getIfPresent(orgCode);
    }

    /**
     * 获取门店详情
     *
     * @return 全部门店信息
     */
    @Override
    public List<OrgInfo> listAllStore() {
        List<OrgInfo> storeInfoList = allStoreCache.getIfPresent(CacheKeyConstant.KEY_STORE_CACHE_ALL);
        if (storeInfoList != null) {
            return storeInfoList;
        }
        // ValueOperations<String, String> operations = redisTemplate.opsForValue();
        ValueOperations<String, String> operations = switchHelper.getOperations();
        String redisOrgInfoJson = operations.get(CacheKeyConstant.KEY_STORE_CACHE_ALL);
        storeInfoList = GsonUtil.fromListJson(redisOrgInfoJson, OrgInfo.class);
        if (storeInfoList != null) {
            allStoreCache.put(CacheKeyConstant.KEY_STORE_CACHE_ALL, storeInfoList);
        }
        return storeInfoList;
    }

    /**
     * 获取区域门店数据
     *
     * @return 区域门店数据
     */
    @Override
    public Map<String, List<OrgInfo>> getAreaStoresMap() {
        List<OrgInfo> storeInfoList = listAllStore();
        if (CollectionUtils.isEmpty(storeInfoList)) {
            return Collections.emptyMap();
        }

        // area store Map
        Map<String, List<OrgInfo>> provinceStoresMap = storeInfoList.stream().filter(store -> store.getProvince() != null && store.getProvince() >= 0).collect(Collectors.groupingBy(store -> AreaEnum.PROVINCE.getPrefix() + store.getProvince()));
        Map<String, List<OrgInfo>> areaStoresMap = new HashMap<>(provinceStoresMap);

        Map<String, List<OrgInfo>> areaIdStoresMap = storeInfoList.stream().filter(store -> store.getAreaId() != null && store.getAreaId() >= 0).collect(Collectors.groupingBy(store -> AreaEnum.AREA.getPrefix() + store.getAreaId()));
        areaStoresMap.putAll(areaIdStoresMap);

        Map<String, List<OrgInfo>> cityIdStoresMap = storeInfoList.stream().filter(store -> store.getCity() != null && store.getCity() >= 0).collect(Collectors.groupingBy(store -> AreaEnum.CITY.getPrefix() + store.getCity()));
        areaStoresMap.putAll(cityIdStoresMap);
        return areaStoresMap;
    }

    private void loadOrgInfo() {
        List<OrgInfo> orgInfoList = listAllStore();
        orgInfoList.forEach(orgInfo -> localOrgCache.put(orgInfo.getOrgCode(), orgInfo));
    }
}