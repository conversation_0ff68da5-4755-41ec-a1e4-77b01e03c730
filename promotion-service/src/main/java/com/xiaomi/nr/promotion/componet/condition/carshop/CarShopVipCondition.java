package com.xiaomi.nr.promotion.componet.condition.carshop;

import com.xiaomi.micar.club.api.model.member.MemberInfo;
import com.xiaomi.micar.club.api.resp.member.MemberInfoResp;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.CarShopVipDiscountPromotionConfig;
import com.xiaomi.nr.promotion.resource.external.CarUserVipExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 人群条件检查
 *
 * <AUTHOR>
 * @date 2021/6/1
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarShopVipCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;

    /**
     * 优惠类型
     */
    private PromotionToolType promotionType;

    /**
     * 身份限制
     */
    private Boolean identityLimit;

    /**
     * 会员ID
     */
    private Integer vipLevel;

    /**
     * 参与商品信息
     */
    private Set<Long> joinGoods;

    /**
     * 条件阶梯
     */
    private List<QuotaLevel> levelList;


    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        List<CartItem> cartList = request.getCartList();
        if (CollectionUtils.isEmpty(cartList)) {
            return false;
        }

        Long uid = request.getUserId();
        if (this.identityLimit) {
            MemberInfo vipIdentityInfo = getVipLevelFromContext(context);
            if (vipIdentityInfo == null || !Objects.equals(this.vipLevel, vipIdentityInfo.getLevel())) {
                log.debug("condition is not satisfied. user is not match vipId. uid:{} actId:{} vipIdentityInfo:{}", request.getUserId(), promotionId, vipIdentityInfo);
                return false;
            }
        }

        List<GoodsIndex> goodsIndexList = getGoodsIndexList(cartList, joinGoods);

        // 没有符合的商品，不满足活动
        if (CollectionUtils.isEmpty(goodsIndexList)) {
            log.debug("condition is not satisfied. fillGoodsList is empty. actId:{} uid:{}", promotionId, uid);
            return false;
        }

        QuotaLevel level = levelList.getFirst();
        if (level == null) {
            log.debug("condition is not satisfied. fill level is not found. actId:{} uid:{}", promotionId, uid);
            return false;
        }

        context.setQuotaLevel(level);
        context.setGoodIndex(goodsIndexList);
        return true;
    }

    private List<GoodsIndex> getGoodsIndexList(List<CartItem> cartList, Set<Long> ssuId) {
        List<GoodsIndex> goodsIndexList = new LinkedList<>();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem cartItem = cartList.get(idx);
            //处理当前item是否可参加活动
            boolean itemQualify = CartHelper.checkItemActQualifyCommon(cartItem, promotionType.getTypeId());
            if (!itemQualify) {
                continue;
            }
            if (!ssuId.contains(cartItem.getSsuId())) {
                continue;
            }
            goodsIndexList.add(new GoodsIndex(cartItem.getItemId(), idx));
        }
        return goodsIndexList;
    }

    private MemberInfo getVipLevelFromContext(LocalContext context) {
        Map<ResourceExtType, ExternalDataProvider<?>> externalDataMap = context.getExternalDataMap();
        CarUserVipExternalProvider provider = (CarUserVipExternalProvider) externalDataMap.get(ResourceExtType.MID_ULTRA_VIP_MEMBER);
        try {
            return provider.getData();
        } catch (BizError e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof CarShopVipDiscountPromotionConfig)) {
            log.error("config is not instanceof CarShopVipDiscountPromotionConfig. config:{}", config);
            return;
        }
        CarShopVipDiscountPromotionConfig promotionConfig = (CarShopVipDiscountPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        this.vipLevel = promotionConfig.getVipLevel();
        this.joinGoods = new HashSet<>(promotionConfig.getJoinGoods().getSsuId());
        this.levelList = promotionConfig.getLevelList();
        this.identityLimit = true;
    }
}
