package com.xiaomi.nr.promotion.domain.coupon.service.base.type;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 折扣券
 */
@Slf4j
public abstract class AbstractDiscountCoupon extends AbstractCouponTool {

    protected Set<Long> includeGoods;

    @Override
    public Long getCouponId() {
        return id;
    }

    @Override
    public Coupon generateCartCoupon() {
        return initCoupon();
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.COUPON_DISCOUNT;
    }

    @Override
    public CouponTypeEnum getCouponType() {
        return CouponTypeEnum.DISCOUNT;
    }

    @Override
    public boolean load(CheckoutCoupon checkoutCoupon) throws BizError {
        this.id = checkoutCoupon.getCouponId();
        this.checkoutCoupon = checkoutCoupon;
        if (checkoutCoupon.getValidGoodsList() != null) {
            this.includeGoods = new HashSet<>(checkoutCoupon.getValidGoodsList());
        } else {
            this.includeGoods = new HashSet<>();
        }
        return true;
    }

    @Override
    public CouponCheckoutResult checkoutCoupon(CheckoutPromotionRequest request, CheckoutContext context)
            throws BizError {
        return null;
    }

    protected abstract List<GoodsIndex> matchCartList(List<CartItem> cartList, CheckoutContext context) throws BizError;

    protected abstract ValidGoods buildValidGoods(List<CartItem> cartList, List<GoodsIndex> goodsInd);

    protected abstract long getCartTotalPrice(List<CartItem> cartList);
}
