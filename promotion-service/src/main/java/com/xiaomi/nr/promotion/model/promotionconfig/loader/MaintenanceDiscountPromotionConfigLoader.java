package com.xiaomi.nr.promotion.model.promotionconfig.loader;

import com.google.common.collect.Lists;
import com.xiaomi.nr.md.promotion.admin.api.constant.ProductWhileBlackEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.ScopeTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityScope;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ProductPolicy;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.MaintenanceDiscountRule;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.specification.DiscountSpecification;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.entity.redis.QuotaEle;
import com.xiaomi.nr.promotion.enums.ActFrequencyEnum;
import com.xiaomi.nr.promotion.enums.PolicyQuotaTypeEnum;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MaintenanceDiscountPromotionConfig;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 折扣配置数据加载
 *
 * <AUTHOR>
 * @date 2021/6/3
 */
@Slf4j
@Component
public class MaintenanceDiscountPromotionConfigLoader implements PromotionConfigLoader {

    @Override
    public boolean support(AbstractPromotionConfig promotionConfig) {
        return promotionConfig instanceof MaintenanceDiscountPromotionConfig;
    }

    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityConfig activityConfig) throws BizError {

        if (activityConfig == null || promotionConfig == null) {
            log.error("[MaintenanceDiscountPromotionConfigLoader] activityInfo is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "activityInfo is null");
        }

        List<ProductPolicy> productPolicyList = Optional.ofNullable(activityConfig.getProductPolicyList()).orElse(new ArrayList<>());

        MaintenanceDiscountPromotionConfig discountConfig = (MaintenanceDiscountPromotionConfig) promotionConfig;

        // 黑名单商品
        List<Long> invalidSsuList = Lists.newArrayList();
        List<Long> validSsuList = Lists.newArrayList();
        for (ProductPolicy productPolicy : productPolicyList) {
            MaintenanceDiscountRule.ProductRule productRule = GsonUtil.fromJson(productPolicy.getRule(), MaintenanceDiscountRule.ProductRule.class);
            if (Objects.isNull(productRule)) {
                continue;
            }
            if (Objects.equals(productRule.getLimitType(), ProductWhileBlackEnum.BLACK.value)) {
                invalidSsuList.add(productPolicy.getProductId());
            } else {
                validSsuList.add(productPolicy.getProductId());
            }
        }
        CompareItem joinGoods = new CompareItem();
        joinGoods.setSsuId(validSsuList);
        discountConfig.setJoinGoods(joinGoods);
        discountConfig.setInvalidGoods(invalidSsuList);

        // 工单类型
        Set<Integer> workOrderList = new HashSet<>();
        for (ActivityScope scope : activityConfig.getActivityScopeList()) {
            if (Objects.equals(scope.getScopeType(), ScopeTypeEnum.ORDER_FROM.code)) {
                Arrays.stream(scope.getScopeValue().split(",")).map(Integer::valueOf).forEach(workOrderList::add);
            }
        }
        discountConfig.setSupportWorkOrderTypes(workOrderList);

        String rule = activityConfig.getRule();
        MaintenanceDiscountRule discountRule = GsonUtil.fromJson(rule, MaintenanceDiscountRule.class);
        if (discountRule == null) {
            log.error("maintenance discount rule is invalid. actId:{} rule:{} ", activityConfig.getId(), rule);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "maintenance discount rule is invalid");
        }

        List<QuotaLevel> levelList = new ArrayList<>();
        for (DiscountSpecification discountSpecification : discountRule.getSpecificationList()) {
            QuotaLevel quotaLevel = new QuotaLevel();
            quotaLevel.setReduceDiscount(discountSpecification.getReduce());
            quotaLevel.setMaxReducePrice(discountSpecification.getMaxReduce());

            QuotaEle quotaEle = new QuotaEle();
            quotaEle.setCount(discountSpecification.getThreshold().intValue());
            quotaEle.setType(PolicyQuotaTypeEnum.POLICY_QUOTA_NUM.getType());
            quotaLevel.setQuotas(Lists.newArrayList(quotaEle));

            levelList.add(quotaLevel);
        }
        // 用户参与次数限制
        discountConfig.setUserJoinNumLimit(discountRule.getUserLimitNum());
        discountConfig.setLevelList(levelList);
        discountConfig.setCarIdentityId(Integer.valueOf(discountRule.getCarIdentityId()));
        discountConfig.setDescription(discountRule.getDescription());
        discountConfig.setFrequency(ActFrequencyEnum.NONE);
        ActNumLimitRule actNumLimitRule = new ActNumLimitRule();
        actNumLimitRule.setPersonLimit(Long.valueOf(discountRule.getUserLimitNum()));
        discountConfig.setNumLimitRule(actNumLimitRule);
    }
}
