package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 直降活动手机号每人参与次数
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OnsaleActMobileUserLimitProvider implements ResourceProvider<OnsaleActMobileUserLimitProvider.ActUserLimit> {
    /**
     * 线下每人参与活动次数
     */
    private ResourceObject<OnsaleActMobileUserLimitProvider.ActUserLimit> resourceObject;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Autowired
    private NacosConfig nacosConfig;

    @Override
    public ResourceObject<OnsaleActMobileUserLimitProvider.ActUserLimit> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<OnsaleActMobileUserLimitProvider.ActUserLimit> object) {
        this.resourceObject = object;
    }

    /**
     * 扣减库存
     */
    @Override
    public void lock() throws BizError {
        log.info("lock onsale act mobile user limit resource. {}", resourceObject);
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("OnsaleActMobileUserLimitProvider.lock(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        activityRedisDao.incrOnsaleMobileUserLimitNum(resourceObject.getContent().getActId(),
                resourceObject.getContent().getUid(),
                resourceObject.getContent().getSkuPackage(),
                resourceObject.getContent().getCount(),
                resourceObject.getContent().getLimitNum());
        log.info("lock onsale act mobile user limit resource ok. {}", resourceObject);
    }

    @Override
    public void consume() {
        log.info("consume onsale act mobile user limit total resource. {}", resourceObject);
    }

    @Override
    public void rollback() throws BizError {
        log.info("rollback onsale act mobile user limit resource. {}", resourceObject);
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("OnsaleActMobileUserLimitProvider.rollback(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        activityRedisDao.decrOnsaleMobileUserLimitNum(resourceObject.getContent().getActId(),
                resourceObject.getContent().getUid(),
                resourceObject.getContent().getSkuPackage(),
                resourceObject.getContent().getCount());
        log.info("rollback onsale act mobile user limit resource ok. {}", resourceObject);
    }

    @Override
    public String conflictText() {
        return "减限购失败";
    }

    /**
     * 活动记录
     */
    @Data
    public static class ActUserLimit {
        /**
         * 用户ID
         */
        private Long uid;
        /**
         * 活动ID
         */
        private Long actId;
        /**
         * skuPackage
         */
        private String skuPackage;
        /**
         * 参与活动数量
         */
        private Integer count;
        /**
         * 限制
         */
        private Long limitNum;
    }
}
