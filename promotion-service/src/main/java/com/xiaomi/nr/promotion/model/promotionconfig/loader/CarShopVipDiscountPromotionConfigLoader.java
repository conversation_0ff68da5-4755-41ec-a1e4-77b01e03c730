package com.xiaomi.nr.promotion.model.promotionconfig.loader;

import com.google.common.collect.Lists;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ProductPolicy;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.VipDiscountRule;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.specification.DiscountSpecification;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.entity.redis.QuotaEle;
import com.xiaomi.nr.promotion.enums.PolicyQuotaTypeEnum;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.CarShopVipDiscountPromotionConfig;
import com.xiaomi.nr.promotion.tool.PromotionDescRuleTool;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 折扣配置数据加载
 *
 * <AUTHOR>
 * @date 2021/6/3
 */
@Slf4j
@Component
public class CarShopVipDiscountPromotionConfigLoader implements PromotionConfigLoader {

    @Autowired
    private PromotionDescRuleTool promotionDescRuleTool;

    @Override
    public boolean support(AbstractPromotionConfig promotionConfig) {
        if (promotionConfig instanceof CarShopVipDiscountPromotionConfig) {
            return true;
        }
        return false;
    }

    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityConfig activityConfig) throws BizError {

        if (activityConfig == null || promotionConfig == null) {
            log.error("[DiscountPromotionConfigLoader] activityInfo is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "activityInfo is null");
        }

        List<ProductPolicy> productPolicyList = activityConfig.getProductPolicyList();
        CarShopVipDiscountPromotionConfig discountConfig = (CarShopVipDiscountPromotionConfig) promotionConfig;

        if (CollectionUtils.isEmpty(productPolicyList) || productPolicyList.get(0) == null) {
            log.error("policy discount is invalid. actId:{} policy:{} ", activityConfig.getId(), productPolicyList);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "policy is invalid");
        }

        List<Long> ssuList = productPolicyList.stream().map(ProductPolicy::getProductId).collect(Collectors.toList());

        CompareItem joinGoods = new CompareItem();
        joinGoods.setSsuId(ssuList);
        discountConfig.setJoinGoods(joinGoods);

        String rule = activityConfig.getRule();
        if (StringUtils.isEmpty(rule)) {
            log.error("policy vipDiscount rule err. actId:{}  rule:{} ", activityConfig.getId(), rule);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "rule is invalid");
        }

        VipDiscountRule vipDiscountRule = GsonUtil.fromJson(rule, VipDiscountRule.class);
        List<DiscountSpecification> discountSpecificationList = vipDiscountRule.getSpecificationList();

        if (CollectionUtils.isEmpty(discountSpecificationList)) {
            log.error("policy vipDiscount rule err. actId:{} discountSpecificationList:{} ", activityConfig.getId(), discountSpecificationList);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "rule is invalid");
        }

        List<QuotaLevel> levelList = discountSpecificationList.stream().map(spec -> {
                    QuotaLevel quotaLevel = new QuotaLevel();
                    quotaLevel.setReduceDiscount(spec.getReduce());
                    quotaLevel.setMaxReducePrice(Optional.ofNullable(spec.getMaxReduce()).orElse(0L));

                    QuotaEle quotaEle = new QuotaEle();
                    quotaEle.setCount(spec.getThreshold().intValue());
                    quotaEle.setType(PolicyQuotaTypeEnum.POLICY_QUOTA_NUM.getType());
                    quotaLevel.setQuotas(Lists.newArrayList(quotaEle));

                    return quotaLevel;
                }).collect(Collectors.toList());
        discountConfig.setUserJoinNumLimit(0);
        discountConfig.setLevelList(levelList);
        discountConfig.setVipLevel(vipDiscountRule.getVipLevel());
        ActNumLimitRule actNumLimitRule = new ActNumLimitRule();
        actNumLimitRule.setPersonLimit(vipDiscountRule.getUserLimitNum());
        discountConfig.setNumLimitRule(actNumLimitRule);

        // 活动规则
        // todo cxp
//        String descRuleIndex = promotionDescRuleTool.generateDiscountDescRuleIndex(levelList);
//        List<String> descRule = promotionDescRuleTool.generateDiscountDescRule(levelList);
//        discountConfig.setDescRuleIndex(descRuleIndex);
//        discountConfig.setDescRule(descRule);
    }
}
