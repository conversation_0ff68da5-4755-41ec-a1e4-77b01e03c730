package com.xiaomi.nr.promotion.entity.redis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 商品信息
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Data
public class CompareItem implements Serializable {
    private static final long serialVersionUID = -300248743347244862L;

    /**
     * 分类id
     */
    private List<String> cat;

    /**
     * sku
     */
    private List<String> sku;

    /**
     * 商品id
     */
    private List<String> commodity;

    /**
     * 货品id
     */
    private List<String> goods;

    /**
     * 产品ID
     */
    private List<String> product;

    /**
     * 套装ID
     * 注： 因为关键词 对应字段package
     */
    @SerializedName("package")
    private List<String> packages;

    /**
     * group;
     */
    private List<String> group;

    /**
     * 人群ID
     */
    private List<String> groupTag;

    /**
     * 是否所有商品可参与，0否 1是
     */
    private Integer all = 0;

    /**
     * 活动的修改时间
     */
    @SerializedName("modify_index")
    private Long modifyIndex = 0L;
}
