package com.xiaomi.nr.promotion.util;

import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.GetProductCurrentPriceRequest;
import com.xiaomi.nr.promotion.api.dto.ProductActJoinInfoRequest;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.enums.SsuIdTypeEnum;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.CartItemChild;
import com.xiaomi.nr.promotion.api.dto.model.EcardConsumeItemList;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.constant.PhoenixParamConstant;
import com.xiaomi.nr.promotion.constant.SourceConstant;
import com.xiaomi.nr.promotion.enums.BusinessTypeEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;

import static com.xiaomi.nr.promotion.constant.SourceConstant.SOURCE_EXCHANGE;
import static com.xiaomi.nr.promotion.enums.SourceEnum.SOURCE_GIFT;

/**
 * 请求参数验证器
 *
 * <AUTHOR>
 * @date 2021/5/28
 */
@Slf4j
public class RequestParamValidator {

    public static final String ITEM_ID_STR = ", itemId:";

    /**
     * 结算请求参数验证：做结算请求入参校验 CheckoutPromotionRequest
     *
     * @param request 请求参数
     * @throws BizError 参数异常
     */
    public static void valid(CheckoutPromotionRequest request) throws BizError {
        // 检查来源
        if (request.getSourceApi() == null) {
            log.error("sourceApi is null. uid:{}", request.getUserId());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "sourceApi为空");
        }
        if (request.getSourceApi() != SourceApi.CHECKOUT && request.getSourceApi() != SourceApi.SUBMIT) {
            log.error("sourceApi is not supported. uid:{}, sourceApi:{}", request.getUserId(), request.getSourceApi());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "sourceApi无效:" + request.getSourceApi());
        }

        // 校验ClientId
        if (request.getClientId() == null && request.getChannel() == null) {
            log.error("clientId is null. uid:{}", request.getUserId());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "clientId为空");
        }

        // 下单落库校验订单号
        if (request.getSourceApi() == SourceApi.SUBMIT && !request.getNoSaveDbSubmit() && request.getOrderId() <= 0L) {
            log.error("orderId is error. uid:{}, orderId:{}", request.getUserId(), request.getOrderId());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "订单号不能为空");
        }

        // 检查购物车列表，会进行兑换场景的识别，对request进行赋值
        checkCartList(request);

        // 检查券参数
        checkCoupon(request);

        // 检查礼品卡参数
        checkEcardParam(request);

        // 检查三方优惠参数
        checkPhoenixParam(request);

        // 检查三方优惠参数
        checkMaintenanceParam(request);
    }

    private static void checkMaintenanceParam(CheckoutPromotionRequest request) {
        if (CollectionUtils.isEmpty(request.getActivityIds())) {
            request.setActivityIds(new ArrayList<>());
        }

        if (CollectionUtils.isEmpty(request.getEquityKeys())) {
            request.setEquityKeys(new ArrayList<>());
        }

    }

    /**
     * 验证价保请求参数
     *
     * @param request 请求对象
     * @throws BizError 业务异常
     */
    public static void validProtectPriceRequest(GetProductCurrentPriceRequest request) throws BizError {
        if (request.getClientId() <= 0L) {
            log.warn("param clientId <=0. request:{}", request);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "clientId不能为空");
        }
        if (CollectionUtils.isEmpty(request.getCartList())) {
            log.warn("param cartlist is empty. request:{}", request);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "购物车不能为空");
        }
    }

    /**
     * 加购时商品参与活动入参校验
     *
     * @param request 请求参数
     * @throws BizError 业务异常
     */
    public static void valid(CheckProductsActRequest request) throws BizError {
        if (StringUtils.isBlank(request.getOrgCode())) {
            log.warn("param orgCode is null. request:{}", GsonUtil.toJson(request));
            throw ExceptionHelper.create(GeneralCodes.ParamError, "orgCode参数不能为空");
        }
        if (request.getPromotionId() == null || request.getPromotionId() == 0L) {
            log.warn("param promotionId is null. request:{}", GsonUtil.toJson(request));
            throw ExceptionHelper.create(GeneralCodes.ParamError, "promotionId参数不能为空");
        }
        // 检查主品列表
        checkMainGoodsItems(request.getMainGoodsList());
        // 检查从品列表
        checkAttachedGoodsItems(request.getAttachedGoodsList());
    }

    /**
     * 检查主品列表
     *
     * @param goodsItems 商品列表
     * @throws BizError 业务异常
     */
    private static void checkMainGoodsItems(List<CheckGoodsItem> goodsItems) throws BizError {
        if (CollectionUtils.isEmpty(goodsItems)) {
            log.warn("mainGoodsItem is empty.");
            throw ExceptionHelper.create(GeneralCodes.ParamError, "主品列表不能为空");
        }
        for (CheckGoodsItem checkGoodsItem : goodsItems) {
            boolean isSkuEmpty = false;
            boolean isPackageIdEmpty = false;
            if (checkGoodsItem.getSku() == null || checkGoodsItem.getSku() == 0L) {
                isSkuEmpty = true;
            }
            if (checkGoodsItem.getPackageId() == null || checkGoodsItem.getPackageId() == 0L) {
                isPackageIdEmpty = true;
            }
            // 要么是单品要么是套装, 有且只有一个不为空
            if (isSkuEmpty == isPackageIdEmpty) {
                log.warn("both sku and packageId is empty or not empty. sku:{}, packageId:{}", checkGoodsItem.getSku(), checkGoodsItem.getPackageId());
                throw ExceptionHelper.create(GeneralCodes.ParamError, "sku和packageId必须且只有一个不为空");
            }
        }
    }

    /**
     * 检查从品列表
     *
     * @param goodsItems 商品列表
     * @throws BizError 业务异常
     */
    private static void checkAttachedGoodsItems(List<CheckGoodsItem> goodsItems) throws BizError {
        if (CollectionUtils.isEmpty(goodsItems)) {
            return;
        }
        // 从品目前只支持单品，不支持套装
        for (CheckGoodsItem checkGoodsItem : goodsItems) {
            if (checkGoodsItem.getSku() == null || checkGoodsItem.getSku() == 0L) {
                log.warn("both sku and packageId is empty or not empty. sku:{}, packageId:{}", checkGoodsItem.getSku(), checkGoodsItem.getPackageId());
                throw ExceptionHelper.create(GeneralCodes.ParamError, "sku不能为空");
            }
        }
    }

    /**
     * 检查购物车项
     *
     * @param request 请求参数
     * @throws BizError 异常
     */
    private static void checkCartList(CheckoutPromotionRequest request) throws BizError {
        // 校验购物车
        List<CartItem> cartList = request.getCartList();
        if (CollectionUtils.isEmpty(cartList)) {
            log.warn("cartList is empty. uid:{}", request.getUserId());
            throw ExceptionHelper.create(ErrCode.ERR_EMPTY_CART, "购物车为空");
        }
        // 校验购物车项格式 排重辅助Set val：itemId
        final Set<String> itemIdSet = new HashSet<>();
        for (CartItem cartItem : cartList) {
            checkCartItem(cartItem, itemIdSet, request.getUserId(), request.getChannel());
            // 保存原始购物车价
            cartItem.setOriginalCartPrice(cartItem.getCartPrice());
        }

        // 兑换场景需要保证只有一种商品一个参与结算，兜底
        if (Objects.equals(request.getChannel(), ChannelEnum.CAR_SHOP.getValue())) {
            boolean isExchange = cartList.stream().anyMatch(item -> item.getSaleSources().contains(SOURCE_EXCHANGE));
            if (isExchange) {
                // 忽略赠品
                int totalCount = cartList.stream()
                        .filter(item -> !SOURCE_GIFT.getSource().equals(item.getSource()))
                        .mapToInt(OrderCartItem::getCount).sum();
                if (totalCount > 1) {
                    throw ExceptionHelper.create(ErrCode.ERR_EXCHANGE_GOODS, "只能兑换一个商品");
                }
            }
            request.setIsExchange(isExchange);
        }
    }

    /**
     * 校验购物车项，有错误， 抛出异常
     *
     * @param cartItem  购物车项
     * @param itemIdSet 临时容器，用来判别是否有重复ID的
     * @param userId    用户ID
     * @throws BizError 参数业务异常
     */
    private static void checkCartItem(CartItem cartItem, Set<String> itemIdSet, Long userId, Integer channel) throws BizError {
        // itemId 不能为空
        if (StringUtils.isEmpty(cartItem.getItemId())) {
            log.warn("itemId is null. uid:{}", userId);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "itemId不能为空");
        }

        String itemId = cartItem.getItemId();
        String sku = cartItem.getSku();
        String packageId = cartItem.getPackageId();
        Long ssuId = cartItem.getSsuId();
        boolean isPackage = CartHelper.isPackage(cartItem);
        boolean isSku = CartHelper.isSku(cartItem);
        boolean isSsu = CartHelper.isSsu(cartItem);
        // 要么是单品要么是套装, 必须要有一个不为空
        if (isSku == isPackage && isSku == isSsu) {
            log.warn("both sku and packageId is null or not null. uid:{}, itemId:{} sku:{} packageId:{} ssuId:{}", userId, itemId, sku, packageId, ssuId);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "sku和packageId必须且只有一个不为空, itemId:" + itemId);
        }

        // sku必须是<10位的数字
        if (isSku) {
            final int skuMaxLength = 10;
            if (sku.length() >= skuMaxLength) {
                log.warn("sku length should < 10. uid:{}, sku:{} ", userId, sku);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "sku长度必须<10位, sku:" + sku + ITEM_ID_STR + itemId);
            }
            if (!StringUtils.isNumeric(sku)) {
                log.warn("sku length should < 10 and sku must be number. uid:{}, sku:{} ", userId, sku);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "sku必须是<10位的数字, sku:" + sku + ITEM_ID_STR + itemId);
            }
        }

        // packageId必须是>=10位的数字
        if (isPackage) {
            final int packageIdMinLength = 10;
            // 老套装校验packageId长度，新套装不校验
            if (!CartHelper.isNewPackage(cartItem) && packageId.length() < packageIdMinLength) {
                log.warn("sku length should >= 10. uid:{} packageId:{} ", userId, packageId);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "packageId长度必须>=10位, packageId:" + packageId + ", itemId:" + itemId);
            }
            if (!CartHelper.isNewPackage(cartItem) && !StringUtils.isNumeric(packageId)) {
                log.warn("sku length should >= 10 and packageId must be number. uid:{}, packageId:{}", userId, packageId);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "packageId必须是>=10位的数字, packageId:" + packageId + ", itemId:" + itemId);
            }
        }

        // itemId 不能重复, 放入 itemIdMap value=1 只是一个标识位
        if (itemIdSet.contains(itemId)) {
            log.warn("itemId duplicated. uid:{} itemId:{}", userId, itemId);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "cartItem itemId不能重复, itemId:" + itemId);
        } else {
            itemIdSet.add(itemId);
        }

        // count > 0
        if (cartItem.getCount() == null || cartItem.getCount() <= 0) {
            log.warn("count <= 0 cart. uid:{} itemId:{}", userId, itemId);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "cartItem count必须大于0, itemId:" + itemId);
        }
        // standard price >= 0, cart price >= 0
        if (cartItem.getStandardPrice() < 0 || cartItem.getCartPrice() < 0) {
            log.warn("standard price < 0, cart price < 0. uid:{}, itemId:{}, standardPrice:{}, cartPrice:{}", userId, itemId, cartItem.getStandardPrice(), cartItem.getCartPrice());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "cartItem price不能小于0, itemId:" + itemId);
        }

        // 检查赠品加价购来源
        String sourceCode = cartItem.getSourceCode();
        if (StringUtils.isEmpty(cartItem.getSource()) && StringUtils.isNotEmpty(sourceCode)) {
            log.warn("source is empty and sourcecode is not empty. uid:{},itemId:{}, sourceCode:{}", userId, itemId, sourceCode);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "cartItem sourcecode错误, itemId:" + itemId);
        }

        if (isPackage) {
            // 套装的children 不能为空数组
            List<CartItemChild> children = cartItem.getChilds();
            if (CollectionUtils.isEmpty(children)) {
                log.warn("package children is empty. uid:{}, itemId:{}, packageId:{}", userId, itemId, packageId);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "child不能为空, itemId:" + itemId);
            }
        }

        // 检查套装
        validateAndProcessCartItem(cartItem, userId, packageId, itemId, channel);

        // 预售模式下，预售定金不能为0（线下）
        if (cartItem.getSaleSources().contains(SourceConstant.SOURCE_ON_SALE_BOOKING) && cartItem.getOnSaleBookingPrice() == 0) {
            log.warn("invalid cartItem onSaleBookingPrice. uid:{}, itemId:{}", userId, itemId);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "the cartItem onSaleBookingPrice is 0" + cartItem);
        }
        // 汽车维保售后maintenanceInfo不能为空
        if (Objects.equals(channel, ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue()) && cartItem.getMaintenanceInfo() == null) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "入参maintenanceInfo不能为空");
        }

        initializeCartItem(cartItem, itemId);
    }

    private static void initializeCartItem(CartItem cartItem, String itemId) {
        // 将itemId赋值到oriItemId. modify?
        cartItem.setOriItemId(itemId);
        if (cartItem.getReduceList() == null) {
            cartItem.setReduceList(new HashMap<>());
        }
        if (cartItem.getChilds() == null) {
            cartItem.setChilds(new ArrayList<>());
        }
        if (cartItem.getCannotJoinAct() == null) {
            cartItem.setCannotJoinAct(false);
        }
        if (cartItem.getCannotJoinActTypes() == null) {
            cartItem.setCannotJoinActTypes(new ArrayList<>());
        }

        if (cartItem.getReduceDetailList() == null) {
            cartItem.setReduceDetailList(new HashMap<>());
        }
        if (cartItem.getPreferentialInfos() == null) {
            cartItem.setPreferentialInfos(new ArrayList<>());
        }

        if (cartItem.getCannotUseCouponTypes() == null) {
            cartItem.setCannotUseCouponTypes(new ArrayList<>());
        }

    }

    private static void validateAndProcessCartItem(CartItem cartItem, Long userId, String packageId, String itemId, Integer channel) throws BizError {
        if (CollectionUtils.isNotEmpty(cartItem.getChilds())) {
            // 新套装子品数量拆成1处理,如果是车商城或者整车渠道，跳过
            if (SsuParamUtil.isNewPackage(cartItem.getSsuType()) && !Objects.equals(channel, ChannelEnum.CAR_VEHICLE.getValue()) &&
                        !Objects.equals(channel, ChannelEnum.CAR_SHOP.getValue())) {
                    cartItem.setChilds(splitCartItemChild(cartItem));
                    // 新套装兼容老套装处理
                    cartItem.setPackageId(cartItem.getSsuId().toString());
                }


            List<CartItemChild> children = cartItem.getChilds();
            long childPriceTotal = 0L;
            // 检查套装children
            for (CartItemChild child : children) {
                checkCartItemChild(child, packageId, userId, cartItem.getSsuType());
                childPriceTotal += child.getSellPrice() * child.getCount();
            }
            // 套装里所有child的sellPrice之和要>0
            if (childPriceTotal <= 0) {
                log.warn("invalid children sellPrice.uid:{}, itemId:{}, packageId:{}, childPriceTotal:{}", userId, itemId, packageId, childPriceTotal);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "套装里所有child的sellPrice之和必须大于0, itemId:" + itemId);
            }
            // 设置子商品在套装内的真实销售价
            if (childPriceTotal == cartItem.getStandardPrice()) {
                children.forEach(child -> child.setOriginalSellPrice(child.getSellPrice()));
            } else if (SsuParamUtil.isNewPackage(cartItem.getSsuType())) {
                log.warn("invalid new package children sellPrice.uid:{}, itemId:{}, packageId:{}, childPriceTotal:{}",
                        userId, itemId, packageId, childPriceTotal);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "新套装里所有child的sellPrice之和必须等于主品价格, itemId:" + itemId);
            } else {
                CartHelper.updateChildPrice(cartItem);
            }
        }
    }

    /**
     * 新套装子品数量拆成1处理
     *
     * @param cartItem 商品
     * @return 子品列表
     */
    private static List<CartItemChild> splitCartItemChild(CartItem cartItem) throws BizError {
        List<CartItemChild> childList = new ArrayList<>();
        for (CartItemChild child : cartItem.getChilds()) {
            Integer origCount = child.getCount();
            if (origCount == 1) {
                childList.add(child);
            } else if (origCount > 1) {
                for (int i = 0; i < origCount; i++) {
                    child.setCount(1);
                    CartItemChild tmpChild = new CartItemChild();
                    BeanUtils.copyProperties(child, tmpChild);
                    childList.add(tmpChild);
                }
            } else {
                log.warn("invalid new package children count<0 .cartItem:{}", cartItem);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "新套装子品count<=0");
            }
        }
        return childList;
    }

    /**
     * 检查套装子SKU
     *
     * @param child     用户ID
     * @param packageId 套装ID
     * @param userId    用户ID
     * @throws BizError 业务异常
     */
    private static void checkCartItemChild(CartItemChild child, String packageId, Long userId, Integer ssuType) throws BizError {
        if (StringUtils.isBlank(child.getSku()) && child.getSsuId() == null) {
            log.warn("package empty child sku. uid:{}, packageId:{}", userId, packageId);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "child sku不能为空");
        }
        // 老套装里每个单品只能有一个
        if (!SsuIdTypeEnum.PACKAGE.code.equals(ssuType) && child.getCount() != 1) {
            log.warn("package child sku count is not 1. uid:{},packageId:{},sku:{}", userId, packageId, child.getSku());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "child count只能为1");
        }
        // sellPrice >= 0
        if (child.getSellPrice() < 0) {
            log.warn("package child sku invalid sellPrice.uid:{},packageId:{},sku:{},sellPrice:{}", userId, packageId, child.getSku(), child.getSellPrice());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "child sellPrice不能小于0");
        }
    }

    /**
     * 检查券
     *
     * @param request 请求参数
     * @throws BizError 异常
     */
    private static void checkCoupon(CheckoutPromotionRequest request) throws BizError {
        if (CollectionUtils.isEmpty(request.getCouponCodes()) && CollectionUtils.isEmpty(request.getCouponIds())) {
            return;
        }

        List<String> couponCodes = Optional.ofNullable(request.getCouponCodes()).orElse(Collections.emptyList());
        List<Long> couponIds = Optional.ofNullable(request.getCouponIds()).orElse(Collections.emptyList());
        // 需要用户ID
        if (request.getUserId() == null || request.getUserId() == 0L) {
            log.warn("useId is none. uid:{}", request.getUserId());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "未指定用户");
        }
        // 最多只能有一张券
        final int maxUseNum = 3;
        if (((couponCodes.size() + couponIds.size()) > maxUseNum) && !Objects.equals(request.getChannel(), ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue())) {
            log.warn("coupon count over maxUseNum. uid:{} codeCouponSize:{}, idCouponSize:{}", request.getUserId(), couponCodes.size(), couponIds.size());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "一次最多只能三张优惠券");
        }

        // 下单结算 && 兑换 && 券必须使用，且只能使用一张
        if (request.getIsExchange() && Objects.equals(request.getSourceApi(), SourceApi.SUBMIT)) {
            if (CollectionUtils.isEmpty(couponIds)) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "兑换场景必须使用券");
            }
            if (couponIds.size() > 1) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "兑换场景只能使用一张券");
            }
        }
    }

    /**
     * 检查礼品卡参数
     *
     * @param request 结算请求参数
     * @throws BizError 业务异常
     */
    private static void checkEcardParam(CheckoutPromotionRequest request) throws BizError {
        if (CollectionUtils.isEmpty(request.getEcardIds())) {
            return;
        }
        // 用户id检查
        if (request.getUserId() == null || request.getUserId() == 0L) {
            log.warn("check ecard. userId is empty. ecards:{}", request.getEcardIds());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "传入礼品卡，用户id不可为空");
        }

        // 北京消费券，最多只能用一张
        if (Objects.equals(SourceApi.SUBMIT, request.getSourceApi()) && request.getUseBeijingcoupon()
                && CollectionUtils.isNotEmpty(request.getEcardIds()) && request.getEcardIds().size() > 1) {
            log.warn("submit:  user {} use more than one beijingcoupon", request.getUserId());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "北京消费劵只能使用一张");
        }

        // 礼品卡小单检查
        Map<String, EcardConsumeItemList> ecardConsumeDetail = request.getEcardConsumeDetail();
        if (MapUtils.isEmpty(ecardConsumeDetail)) {
            return;
        }
        for (Map.Entry<String, EcardConsumeItemList> entry : ecardConsumeDetail.entrySet()) {
            if (entry.getValue() == null || CollectionUtils.isEmpty(entry.getValue().getConsumeList())) {
                log.warn("check ecard. EcardConsumeDetails is invalid. detail is null or empty. ecardId:{}", entry.getKey());
                throw ExceptionHelper.create(GeneralCodes.ParamError, "礼品卡小单扣费详情为空");
            }
        }
    }

    /**
     * 检查三方优惠参数
     *
     * @param request 结算请求参数
     * @throws BizError 业务异常
     */
    private static void checkPhoenixParam(CheckoutPromotionRequest request) throws BizError {
        if (request.getUnicomHsBalance() != null && request.getUnicomHsBalance() != 0 && StringUtils.isEmpty(request.getOrgCode())) {
            log.warn("check orgCode is invalid. unicomHsBalance:{}", request.getUnicomHsBalance());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "OrgCode参数不能为空");
        }
        List<ThirdPromotion> thirdPromotions = request.getThirdPromotions();
        if (CollectionUtils.isEmpty(thirdPromotions)) {
            return;
        }
        // 米家以旧换新参数
        ThirdPromotion renewThirdPromotion = ThirdPromotionHelper.getThirdPromotion(thirdPromotions, BusinessTypeEnum.RENEW);
        if (renewThirdPromotion != null) {
            Map<String, String> params = Optional.ofNullable(renewThirdPromotion.getParams()).orElse(Collections.emptyMap());
            String paramValue = params.get(PhoenixParamConstant.RENEW_ORDER_ID);
            if (StringUtils.isEmpty(paramValue) || !StringUtils.isNumeric(paramValue)) {
                log.warn("renewThirdPromotion param error renewOrderId:{}", paramValue);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "renewOrderId参数错误");
            }
        }
    }

    public static void valid(ProductActJoinInfoRequest request) throws BizError {
        if (CollectionUtils.isEmpty(request.getPromotionIds())) {
            log.warn("getProductActJoinInfo param error promotionIds:{}", request.getPromotionIds());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "promotionIds参数错误");
        }
        // 活动id限制20个/次
        if (request.getPromotionIds().size() > 20) {
            log.warn("getProductActJoinInfo param error promotionIds:{}", request.getPromotionIds());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "promotionIds个数不能超过20");
        }
        if (request.getMid() == null && StringUtils.isBlank(request.getVid())) {
            log.warn("getProductActJoinInfo param error mid:{} or vid:{}", request.getMid(), request.getVid());
            throw ExceptionHelper.create(GeneralCodes.ParamError, "mid或vid参数错误");
        }
    }
}
