package com.xiaomi.nr.promotion.dao.redis;

import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;

import java.util.List;
import java.util.Map;

/**
 * 商品缓存操作对象
 *
 * <AUTHOR>
 * @date 2021/3/30
 */
public interface GoodsRedisDao {
    /**
     * 单品根据sku获取商品层级关系
     *
     * @param sku sku
     * @return 商品层级信息
     */
    GoodsHierarchy getHierarchyBySku(String sku);

    /**
     * 套装根据commodityId获取商品层级关系
     *
     * @param commodityId commodityId
     * @return 商品层级信息
     */
    GoodsHierarchy getHierarchyByCommodityId(String commodityId);
}
