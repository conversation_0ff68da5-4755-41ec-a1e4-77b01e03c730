package com.xiaomi.nr.promotion.domain.coupon.service.base.processor;

import com.google.common.collect.ImmutableMap;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.api.dto.model.CouponInfo;

import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.service.base.type.CouponFactory;
import com.xiaomi.nr.promotion.engine.CouponTool;
import com.xiaomi.nr.promotion.enums.CouponGeneralType;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.provider.CouponProvider;
import com.xiaomi.nr.promotion.util.NumberUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.TreeSet;

/**
 * Created by wangweiyi on 2023/1/10
 */
@Slf4j
@Component
public class GoodsCouponProcessor extends AbstractCouponProcessor {




    @Autowired
    private CouponFactory couponFactory;

    @Autowired
    private ResourceProviderFactory resourceProviderFactory;


    /**
     * 券类型排序
     */
    private static final Map<Integer, Integer> TYPE_SORT_ORDER = ImmutableMap.of(
            CouponTypeEnum.CASH.getType(), 3,
            CouponTypeEnum.DEDUCT.getType(), 2,
            CouponTypeEnum.DISCOUNT.getType(), 1);

    @Override
    public CouponGeneralType getGeneralType() {
        return CouponGeneralType.GOODS;
    }


    /**
     * 初始化可用券列表，排序规则：
     * 1.ClientID匹配的在前
     * 2. 金额排序： 优惠金额高的在前
     * 3. 时间排序：结束时间小的在前
     * 4. 类型排序：CASH > DEDUCT > DISCOUT > 免邮
     * @param checkoutResultMap
     * @return
     */
    public TreeSet<Coupon> initSortCouponList(Map<Long, CouponCheckoutResult> checkoutResultMap) {
        TreeSet<Coupon> validCouponList = new TreeSet<>((coupon1, coupon2) -> {

            //reduce大的在前
            CouponCheckoutResult result1 = checkoutResultMap.get(coupon1.getId());
            long reduceAmount1 = result1.getReduceAmount();
            CouponCheckoutResult result2 = checkoutResultMap.get(coupon2.getId());
            long reduceAmount2 = result2.getReduceAmount();
            if (reduceAmount1 > reduceAmount2) {
                return -1;
            } else if (reduceAmount1 < reduceAmount2) {
                return 1;
            }

            //结束时间排序，时间小的在前
            long endTime1 = coupon1.getEndTime();
            long endTime2 = coupon2.getEndTime();
            if (endTime1 < endTime2) {
                return -1;
            } else if (endTime1 > endTime2) {
                return 1;
            }

            //券类型排序,CASH > DEDUCT > DISCOUT
            int type1 = coupon1.getType();
            int type2 = coupon2.getType();
            if (TYPE_SORT_ORDER.getOrDefault(type1, 0) > TYPE_SORT_ORDER.getOrDefault(type2, 0)) {
                return -1;
            }
            if (TYPE_SORT_ORDER.getOrDefault(type1, 0) < TYPE_SORT_ORDER.getOrDefault(type2, 0)) {
                return 1;
            }

            //treeSet需要消除重复项
            if (coupon1.getId() < coupon2.getId()) {
                return -1;
            } else {
                return 1;
            }

        });

        return validCouponList;

    }



    @Override
    public CouponProvider initResource(CheckoutPromotionRequest request, CouponTool couponTool, CouponCheckoutResult result)
            throws BizError {
        long orderId = request.getOrderId();
        CouponInfo couponInfo = couponTool.generateCouponInfo(result);
        CouponProvider.ResContent resContent = new CouponProvider.ResContent();
        resContent.setUserId(request.getUserId());
        resContent.setOrgCode(request.getOrgCode());
        resContent.setId(couponInfo.getCouponId());
        if (couponInfo.getCouponCode() != null) {
            resContent.setCode(couponInfo.getCouponCode());
        }
        resContent.setOffline(couponInfo.getOffline());
        resContent.setClientId(request.getClientId());
        resContent.setReplaceMoney(NumberUtil.amountConvertF2Y(couponInfo.getReduceMoney()));
        resContent.setReduceExpress(NumberUtil.amountConvertF2Y(couponInfo.getReduceExpress()));

        resContent.setBizPlatform(couponTool.getBizPlatform().getValue());
        resContent.setSubmitType(request.getSubmitType());

        ResourceObject<CouponProvider.ResContent> resourceObject = buildResource(resContent, orderId, String.valueOf(couponInfo.getCouponId()));
        CouponProvider couponProvider = (CouponProvider) resourceProviderFactory.getProvider(ResourceType.COUPON);
        couponProvider.initResource(resourceObject);
        return couponProvider;
    }


    public CouponProvider initReturnResource(CheckoutPromotionRequest request, CouponTool couponTool)
            throws BizError {
        long orderId = request.getOrderId();
        CouponProvider.ResContent resContent = new CouponProvider.ResContent();
        resContent.setUserId(request.getUserId());
        resContent.setOrgCode(request.getOrgCode());
        resContent.setId(null);

        resContent.setOffline(0);
        resContent.setClientId(request.getClientId());
        resContent.setReplaceMoney(NumberUtil.amountConvertF2Y(0L));
        resContent.setReduceExpress(NumberUtil.amountConvertF2Y(0L));

        resContent.setUsedCouponId(couponTool.getCouponId());
        resContent.setSubmitType(request.getSubmitType());
        resContent.setBizPlatform(couponTool.getBizPlatform().getValue());

        ResourceObject<CouponProvider.ResContent> resourceObject = buildResource(resContent, orderId, String.valueOf(couponTool.getCouponId()));
        CouponProvider couponProvider = (CouponProvider) resourceProviderFactory.getProvider(ResourceType.COUPON);
        couponProvider.initResource(resourceObject);
        return couponProvider;
    }





}
