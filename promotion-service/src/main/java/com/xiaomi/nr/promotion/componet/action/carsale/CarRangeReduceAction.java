package com.xiaomi.nr.promotion.componet.action.carsale;

import com.google.common.collect.Lists;
import com.xiaomi.nr.md.promotion.admin.api.util.GsonUtil;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.api.dto.model.car.CarRangeExtend;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.RangeReduceRuleDto;
import com.xiaomi.nr.promotion.componet.action.AbstractAction;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.GoodsIndexNew;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.CarRangeReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.model.promotionconfig.common.RangeReduceInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 范围立减优惠分摊
 *
 * <AUTHOR>
 * @date 2023/11/26 17:49
 **/
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarRangeReduceAction extends AbstractAction {

    private PromotionToolType promotionType;

    private Map<String, ActPriceInfo> rangeReduceInfoMap;

    private String rangeReduceRule;

    private long rangeReducePrice;

    private String title;

    private String description;

    private String descriptionTitle;

    /**
     * 优惠分摊
     *
     * @param promotion 优惠工具
     * @param request   请求参数
     * @param context   请求上下文，活动组件间
     * @throws BizError
     */
    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        List<CartItem> cartList = request.getCartList();
        if (MapUtil.isEmpty(rangeReduceInfoMap)) {
            log.error("rangeReduceInfoMap is empty. actId:{} uid:{}", promotion, request.getUserId());
            return;
        }
        // 获取此次参加活动的商品
        List<GoodsIndexNew> indexNewList = getCartsIndex(cartList);
        // 计算参加商品的优惠
        Map<String, RangeReduceInfo> infoMap = calculateRangeReduceMap(cartList, indexNewList);
        // 生成优惠分摊
        calcRangeReduceItem(cartList, infoMap, promotion.getId(), promotion.getType().getTypeId());
        // 设置结果
        setResult(context, cartList, promotion, indexNewList);
    }

    /**
     * 加载范围立减活动的配置信息
     *
     * @param config 活动配置
     */
    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof CarRangeReducePromotionConfig)) {
            return;
        }
        CarRangeReducePromotionConfig promotionConfig = (CarRangeReducePromotionConfig) config;
        this.rangeReduceInfoMap = promotionConfig.getRangeReduceInfoMap();
        this.rangeReduceRule = promotionConfig.getRangeReduceRule();
        this.promotionType = promotionConfig.getPromotionType();
        this.rangeReducePrice = promotionConfig.getRangeReducePrice();
        this.title = promotionConfig.getTitle();
        this.description = promotionConfig.getDescription();
        this.descriptionTitle = promotionConfig.getDescriptionTitle();
    }

    private List<GoodsIndexNew> getCartsIndex(List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(cartList)) {
            return Collections.emptyList();
        }
        List<GoodsIndexNew> indexList = Lists.newArrayList();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            if (item.getSsuId() != null && item.getSsuId() != 0L) {
                String ssuId = item.getSsuId().toString();
                // 是否匹配
                ActPriceInfo actPriceInfo = rangeReduceInfoMap.get(ssuId);
                if (Objects.isNull(actPriceInfo)) {
                    continue;
                }
                // 规则判断
                boolean itemCheck = CartHelper.checkItemActQualifyCommon(item, promotionType.getTypeId());
                if (!itemCheck) {
                    continue;
                }

                indexList.add(new GoodsIndexNew(0L, item.getItemId(), idx));
            }
        }
        return indexList;
    }

    private void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool, List<GoodsIndexNew> goodsIndNew) throws BizError {
        List<GoodsIndex> goodsIndices = context.getGoodIndex();
        List<String> parentItemList = CartHelper.getParentItemIdList(goodsIndices);
        List<String> joinedItemIdList = CartHelper.getJoinedItemIdListNew(goodsIndNew, cartList);

        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setJoinedItemId(joinedItemIdList);
        promotionInfo.setParentItemId(parentItemList);
        promotionInfo.setJoinCounts(0);
        promotionInfo.setJoined(CollectionUtils.isNotEmpty(joinedItemIdList) ? 1 : 0);
        // 选装基金总金额
        CarRangeExtend carRangeExtend=new CarRangeExtend();
        carRangeExtend.setMaxTotalReduce(rangeReducePrice);
        promotionInfo.setExtend(GsonUtil.toJson(carRangeExtend));

        promotionInfo.setTitle(title);
        promotionInfo.setDescRule(Lists.newArrayList(description));
        promotionInfo.setDescShortName(descriptionTitle);

        // 设置上下文结果
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    /**
     * 生成优惠分摊
     *
     * @param cartItemList 购物车商品信息
     * @param infoMap 立减活动配置中筛选后的商品信息
     */
    private void calcRangeReduceItem(List<CartItem> cartItemList, Map<String, RangeReduceInfo> infoMap, long promotionId, int promotionType) {
        // 获取范围立减金额
        RangeReduceRuleDto ruleDto = GsonUtil.fromJson(rangeReduceRule, RangeReduceRuleDto.class);
        long rangeReducePrice = ruleDto.getReducePrice();

        // 比例分摊：按照优惠后的购物车商品进行比例分摊
        long marketPriceSum = 0L;
        for (CartItem item : cartItemList) {
            if (!infoMap.containsKey(item.getItemId())) { continue; }
            Long curPrice = infoMap.get(item.getItemId()).getPrice();
            marketPriceSum += curPrice;
        }

        // 范围立减中最终优惠的金额
        long reduceTotalAmount = Math.min(rangeReducePrice, marketPriceSum);

        // 已分摊优惠金额累加
        long reduceCurrentAmout = 0L;
        for (CartItem item : cartItemList) {
            if (!infoMap.containsKey(item.getItemId())) { continue; }
            List<ReduceDetailItem> reduceItemList = Optional.ofNullable(item.getReduceItemList()).orElse(Lists.newArrayList());
            RangeReduceInfo reduceInfo = infoMap.get(item.getItemId());

            // 优惠金额分摊, 按照选装商品价格比例均摊优惠金额, 向下取整, 商品划线价参与计算
            Long curPrice = infoMap.get(item.getItemId()).getPrice();
            long currentPrice = (long) Math.floor((double) (reduceTotalAmount * curPrice) / marketPriceSum);
            reduceCurrentAmout += currentPrice;

            ReduceDetailItem detailItem  = initReduceDetailItem(currentPrice, reduceInfo.getSsuId(), promotionId, promotionType);
            // 预算信息填充
            ActPriceInfo onSaleInfo = rangeReduceInfoMap.get(item.getSsuId().toString());
            if (onSaleInfo!=null){
                detailItem.setBudgetApplyNo(onSaleInfo.getBudgetApplyNo());
                detailItem.setLineNum(onSaleInfo.getLineNum());
            }
            reduceItemList.add(detailItem);
            item.setReduceItemList(reduceItemList);
        }

        // 剩余的优惠金额按照分维度均摊到每个商品优惠价格中
        long diffReduceAmount = reduceTotalAmount - reduceCurrentAmout;
        boolean shareStatus = true;
        while (shareStatus && diffReduceAmount > 0L) {
            for (CartItem item : cartItemList) {
                if (diffReduceAmount == 0L) { break; }
                if (!infoMap.containsKey(item.getItemId())) { continue; }

                List<ReduceDetailItem> reduceItemList = Optional.ofNullable(item.getReduceItemList()).orElse(Lists.newArrayList());
                for (ReduceDetailItem reduceItem : reduceItemList) {
                    if (reduceItem.getPromotionId() == promotionId && reduceItem.getPromotionType() == promotionType && reduceItem.getSsuId().equals(item.getSsuId())) {
                        long originReduce = reduceItemList.stream().mapToLong(ReduceDetailItem::getReduceSingle).sum();
                        long reduce = originReduce + 1L;
                        reduceItem.setReduce(reduce);
                        reduceItem.setReduceSingle(reduce);
                        diffReduceAmount --;
                    }
                }
            }
            if (diffReduceAmount == 0L) {
                shareStatus = false;
            }
        }
    }

    /**
     * item数据组装
     *
     * @param price
     * @param ssuId
     * @return
     */
    private ReduceDetailItem initReduceDetailItem(Long price, Long ssuId, long promotionId, int promotionType) {
        ReduceDetailItem detailItem  = new ReduceDetailItem();
        detailItem.setPromotionId(promotionId);
        detailItem.setPromotionType(promotionType);
        detailItem.setReduceSingle(price);
        detailItem.setReduce(price);
        detailItem.setSsuId(ssuId);

        return detailItem;
    }

    /**
     * 生成优惠分摊
     *
     * @param cartItems
     * @param indexList
     * @return
     */
    private Map<String, RangeReduceInfo> calculateRangeReduceMap(List<CartItem> cartItems, List<GoodsIndexNew> indexList) {
        Map<String, RangeReduceInfo> infoMap = new HashMap<>();
        for (CartItem item : cartItems) {
            // 判断当前商品是否可以参加范围立减
            // item为购物车中的商品, indexList为范围立减活动中的商品
            boolean canJoin = canJoinRangeReduceAct(item, indexList);
            if (!canJoin) {
                continue;
            }

            // 获取可以参加的活动(rangeReduceInfo == nil代表没获取到)
            Long ssuId = item.getSsuId();
            String productId = String.valueOf(ssuId);
            ActPriceInfo rangeReduceInfo = rangeReduceInfoMap.get(productId);
            if (rangeReduceInfo == null) {
                log.error("CarRangeReduceAction.calculateRangeReduceMap rangeReduceInfo is null. cart:{}", item);
                continue;
            }


            Long curReducePrice = CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList());
            if (curReducePrice <= 0L) {
                log.warn("rangeReduceInfo curReducePrice <= 0, cart:{} ",item);
                continue;
            }

            // 范围立减活动信息封装
            RangeReduceInfo reduceInfo = new RangeReduceInfo();
            reduceInfo.setPrice(curReducePrice);
            reduceInfo.setSsuId(ssuId);
            infoMap.put(item.getItemId(), reduceInfo);
        }

        return infoMap;
    }

    /**
     * 判断购物车商品是否可以参加范围立减活动
     *
     * @param cartItem 为购物车中的商品
     * @param canJoinItems 为范围立减活动中的商品
     * @return
     */
    private boolean canJoinRangeReduceAct(CartItem cartItem, List<GoodsIndexNew> canJoinItems) {
        // ItemID必须有
        if (StringUtils.isEmpty(cartItem.getItemId())) {
            log.warn("the ItemID is of the cart: {} is null", cartItem);
            return false;
        }
        Long ssuId = cartItem.getSsuId();
        if (ssuId == null) {
            log.warn("CarRangeReduceAction.canJoinRangeReduceAct ssuId is empty. cart: {}, ssuId：{}", cartItem, ssuId);
            return false;
        }
        // 价格、数量合法性校验
        Long standardPrice = cartItem.getStandardPrice();
        Long cartPrice = cartItem.getCartPrice();
        Long reduceAmount = cartItem.getReduceAmount();
        Integer count = cartItem.getCount();
        boolean isValidPriceAmount = standardPrice >= 0 && cartPrice >= 0 && reduceAmount >= 0 && count > 0;
        if (!isValidPriceAmount) {
//            log.warn("CarRangeReduceAction.canJoinRangeReduceAct the cart: {}", cartItem);
//            log.warn("CarRangeReduceAction.canJoinRangeReduceAct the StandardPrice is {}, CartPrice is {}, ReduceAmount is {}, all should >=0",
//                    standardPrice, cartPrice, reduceAmount);
            return false;
        }
        boolean isValidCurPrice = CartHelper.itemCurPrice(cartItem) > 0;
        if (!isValidCurPrice) {
//            log.warn("the cart: {}", cartItem);
//            log.warn("the Count is {}, should >0", count);
        }

        if (CollectionUtils.isEmpty(canJoinItems)) {
            return false;
        }

        return canJoinItems.stream().anyMatch(item -> item.getItemId().equals(cartItem.getItemId()));
    }
}
