package com.xiaomi.nr.promotion.resource.model;

import com.xiaomi.nr.promotion.entity.mysql.promotionuser.PromotionResourceDetail;
import com.xiaomi.nr.promotion.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 资源转化器
 *
 * <AUTHOR>
 * @date 2019/2/27
 */
@Slf4j
public final class PromotionResourceConverter {

    /**
     * 转化资源对象为资源详情
     *
     * @param object 资源对象
     * @return 资源详情
     */
    public static PromotionResourceDetail toDetail(ResourceObject object) {
        PromotionResourceDetail detail = new PromotionResourceDetail();
        detail.setPid(object.getPid());
        detail.setPromotionId(object.getPromotionId());
        detail.setOrderId(object.getOrderId());
        detail.setResourceType(object.getResourceType().getValue());
        detail.setResourceId(object.getResourceId());
        detail.setTransactionId(object.getTransactionId());
        detail.setReturnStatus(object.getReturnStatus());
        detail.setContent(GsonUtil.toJson(object.getContent()));
        return detail;
    }

    /**
     * 更新资源详情的资源对象
     *
     * @param detail 资源详情
     * @param object 资源对象
     * @return 更新后的资源详情
     */
    public static PromotionResourceDetail updateDetail(PromotionResourceDetail detail, ResourceObject<?> object) {
        detail.setPid(object.getPid());
        detail.setPromotionId(object.getPromotionId());
        detail.setOrderId(object.getOrderId());
        detail.setResourceType(object.getResourceType().getValue());
        detail.setResourceId(object.getResourceId());
        detail.setContent(GsonUtil.toJson(object.getContent()));
        return detail;
    }

    /**
     * 转化资源详情为资源对象
     *
     * @param detail 资源详情
     * @return 资源对象
     */
    public static ResourceObject<?> fromDetail(PromotionResourceDetail detail) {
        ResourceType resourceType = ResourceType.valueOf(detail.getResourceType());
        if (resourceType == null) {
            return null;
        }
        Class<?> clazz = resourceType.getResourceClazz();
        ResourceObject object = new ResourceObject<>();
        object.setResourceId(detail.getResourceId());
        object.setResourceType(ResourceType.valueOf(detail.getResourceType()));
        object.setPromotionId(detail.getPromotionId());
        object.setOrderId(detail.getOrderId());
        object.setPid(detail.getPid());
        object.setTransactionId(detail.getTransactionId());
        object.setReturnStatus(detail.getReturnStatus());
        object.setContent(GsonUtil.fromJson(detail.getContent(), clazz));
        return object;
    }

    /**
     * 转化资源详情为资源对象-指定类型
     *
     * @param detail 资源详情
     * @param type   content类型
     * @return 资源对象
     */
    public static <T> ResourceObject<T> fromDetail(PromotionResourceDetail detail, Class<T> type) {
        return fromDetail(detail, type);
    }
}
