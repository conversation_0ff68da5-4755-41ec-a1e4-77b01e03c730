package com.xiaomi.nr.promotion.domain.activity.impl;

import com.xiaomi.nr.promotion.domain.activity.AbstractActivityDomain;
import com.xiaomi.nr.promotion.domain.activity.facade.MergeThirdActivityFacade;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MergeThirdActivityDomain extends AbstractActivityDomain {

    @Autowired
    private MergeThirdActivityFacade facade;

    @Override
    public void checkout(DomainCheckoutContext domainCheckoutContext) throws BizError {
        List<ActivityTool> activityTools = facade.activitySearch(domainCheckoutContext);
        super.executeActivity(domainCheckoutContext,activityTools);
    }


}
