package com.xiaomi.nr.promotion.activity;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.GetProductActRequest;
import com.xiaomi.nr.promotion.api.dto.model.CheckGoodsItem;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.componet.action.ReduceAction;
import com.xiaomi.nr.promotion.componet.condition.*;
import com.xiaomi.nr.promotion.componet.preparation.GlobalExcludePreparation;
import com.xiaomi.nr.promotion.componet.preparation.GoodsHierarchyPreparation;
import com.xiaomi.nr.promotion.constant.PromotionTextConstant;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.entity.redis.QuotaEle;
import com.xiaomi.nr.promotion.enums.PolicyQuotaTypeEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.ActPromExtend;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.ReducePromotionConfig;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.NumberUtil;
import com.xiaomi.nr.promotion.util.StringUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.text.StrBuilder;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 满减活动工具
 *
 * <AUTHOR>
 * @date 2021/4/9
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class ReduceActivity extends AbstractActivityTool implements ActivityTool {
    /**
     * 阶梯数据
     */
    private List<QuotaLevel> levelList;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine
                .conditionPreparation(GoodsHierarchyPreparation.class)
                .conditionPreparation(GlobalExcludePreparation.class)
                .condition(DailyTimeCondition.class)
                .condition(QuotaCondition.class)
                .condition(UserGroupCondition.class)
                .condition(OnlineJoinLimitCondition.class)
                .condition(FrequencyCondition.class)
                .condition(FrequencyForProtectPriceCondition.class)
                .action(ReduceAction.class);
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.REDUCE;
    }

    /**
     * 构建购物车优惠数据
     *
     * @param context 上下文
     * @return 内容
     */
    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) {
        Integer frequencyVal = frequency != null ? frequency.getValue() : null;

        PromotionInfo promotionInfo = buildDefaultPromotionInfo(context);
        promotionInfo.setFrequent(frequencyVal);
        promotionInfo.setTotalLimitNum(actLimitNum);
        return promotionInfo;
    }

    /**
     * 获取产品站信息
     *
     * @param clientId       应用ID
     * @param orgCode        门店Code
     * @param skuPackageList 活动类别
     */
    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        if (!checkProductGoodsActCondition(clientId, orgCode)) {
            log.debug("reduce condition is not match. actId:{} clientId:{} orgCode:{}", id, clientId, orgCode);
            return Collections.emptyMap();
        }
        List<String> joinedSkuPackageList = Lists.newArrayList(includeSkuPackages);
        joinedSkuPackageList.retainAll(skuPackageList);
        if (CollectionUtils.isEmpty(joinedSkuPackageList)) {
            log.debug("reduce joinedSkuPackageList retain empty. actId:{} clientId:{} orgCode:{}", id, clientId, orgCode);
            return Collections.emptyMap();
        }

        ProductActInfo productActInfo = new ProductActInfo();
        productActInfo.setType(type.getValue());
        productActInfo.setId(id);
        productActInfo.setName(name);
        productActInfo.setChannels(channels);
        productActInfo.setSelectClientList(selectClientList);
        productActInfo.setSelectOrgList(selectOrgList);
        productActInfo.setUnixStartTime(getUnixStartTime());
        productActInfo.setUnixEndTime(getUnixEndTime());
        productActInfo.setPolicys(getPolicyList(levelList));
        return joinedSkuPackageList.stream().collect(Collectors.toMap(skuPackage -> skuPackage, skuPackage -> productActInfo));
    }

    /**
     * 获取活动详情
     *
     * @return 活动详情
     */
    @Override
    public ActivityDetail getActivityDetail() {
        ActivityDetail detail = getBasicActivityDetail();
        detail.setPolicys(getPolicyList(levelList));
        return detail;
    }

    /**
     * 获取产品站活动优惠信息
     *
     * @param request   产品站信息
     * @param hierarchy 商品层级关系
     * @param isOrgTool 是否只检查门店
     * @return 优惠数据
     */
    @Override
    public PromotionInfo getProductAct(GetProductActRequest request, GoodsHierarchy hierarchy, boolean isOrgTool) throws BizError {
        if (!checkProductActCondition(request, isOrgTool)) {
            log.debug("reduce condition is not match. request:{}", request);
            return null;
        }
        // 商城主品>1 展示去展示可用商品
        boolean showUsableGoods = !isOrgTool
                && CollectionUtils.isNotEmpty(includeSkuPackages) && includeSkuPackages.size() > 1;
        ActPromExtend extend = buildReducePromotionExtend(levelList);
        extend.setShowUsableGoods(showUsableGoods);

        PromotionInfo promotionInfo = getDefaultProductAct();
        promotionInfo.setExtend(GsonUtil.toJson(extend));
        promotionInfo.setQuotaEles(getQuotaEleList(levelList));
        promotionInfo.setPolicys(getPolicyList(levelList));
        promotionInfo.setPolicyNew(null);
        return promotionInfo;
    }

    /**
     * 构建PromotionInfo Extend 信息
     *
     * @param levelList 阶梯列表
     * @return 拓展信息
     */
    private ActPromExtend buildReducePromotionExtend(List<QuotaLevel> levelList) {
        ActPromExtend extend = new ActPromExtend();
        if (CollectionUtils.isEmpty(levelList)) {
            return extend;
        }
        List<com.xiaomi.nr.promotion.entity.redis.QuotaEle> quotas = levelList.get(0).getQuotas();
        if (CollectionUtils.isEmpty(levelList)) {
            return extend;
        }
        Long reduceMoney = levelList.get(0).getReduceMoney();
        boolean existsPerNum = Objects.equals(PolicyQuotaTypeEnum.POLICY_QUOTA_PER_NUM.getType(), quotas.get(0).getType());
        boolean isVipAct = CollectionUtils.isNotEmpty(selectGroups);

        extend.setVipAct(isVipAct);
        extend.setPerNum(existsPerNum);
        extend.setPerNumMoney(reduceMoney);
        extend.setEndTime(getUnixEndTime());
        extend.setAccessCode(accessCode);
        return extend;
    }

    @Override
    public List<CheckGoodsItem> checkProductsAct(CheckProductsActRequest request, Map<String, GoodsHierarchy> goodsHierarchyMap) throws BizError {
        return null;
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof ReducePromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        ReducePromotionConfig promotionConfig = (ReducePromotionConfig) config;
        this.levelList = promotionConfig.getLevelList();
        return true;
    }

}
