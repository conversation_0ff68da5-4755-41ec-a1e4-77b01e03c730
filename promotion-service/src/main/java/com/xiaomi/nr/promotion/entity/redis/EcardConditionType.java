package com.xiaomi.nr.promotion.entity.redis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2021/5/8 11:20 上午
 */
@Data
public class EcardConditionType implements Serializable {
    private static final long serialVersionUID = 3700263971409311693L;
    /**
     * 支持的client
     */
    private List<String> client;
    /**
     * 什么商品可以参与  礼品卡校验了只能是一组考虑是否变更此数组
     */
    @SerializedName("goods_include")
    private List<CompareItem> goodsInclude;
    /**
     * 在参与的商品中，哪些需要排除掉
     */
    @SerializedName("goods_inexclude")
    private CompareItem goodsInexclude;
    /**
     * 哪些商品必须存在
     */
    @SerializedName("goods_need")
    private CompareItem goodsNeed;

}
