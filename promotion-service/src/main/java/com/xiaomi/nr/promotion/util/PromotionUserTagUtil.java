package com.xiaomi.nr.promotion.util;

import com.google.common.collect.Sets;
import com.xiaomi.nr.md.promotion.admin.api.constant.ScopeTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityScope;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/5/20 21:33
 */
public class PromotionUserTagUtil {

    private PromotionUserTagUtil() {
    }

    /**
     * 获取人群
     *
     * @param activityConfig
     * @return
     */
    public static Set<String> getUserTagScopes(ActivityConfig activityConfig) {
        if (Objects.isNull(activityConfig)) {
            return Collections.emptySet();
        }
        ActivityScope userTagScope = getUserTagScope(activityConfig.getActivityScopeList());
        return getScopes(userTagScope);
    }

    /**
     * 获取人群scope
     *
     * @param scopes
     * @return
     */
    private static ActivityScope getUserTagScope(List<ActivityScope> scopes) {
        if (CollectionUtils.isEmpty(scopes)) {
            return null;
        }
        return scopes.stream()
                .filter(activityScope -> ScopeTypeEnum.USER_TAG.code == activityScope.getScopeType())
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断是否需要车主信息
     *
     * @param scope
     * @return
     */
    private static Set<String> getScopes(ActivityScope scope) {
        if (Objects.isNull(scope) || StringUtils.isBlank(scope.getScopeValue())) {
            return Collections.emptySet();
        }
        return Sets.newHashSet(scope.getScopeValue().split(","));
    }

}
