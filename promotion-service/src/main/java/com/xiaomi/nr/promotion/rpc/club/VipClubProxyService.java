package com.xiaomi.nr.promotion.rpc.club;

import com.google.common.collect.Lists;
import com.xiaomi.micar.club.api.MemberApi;
import com.xiaomi.micar.club.api.model.member.MemberInfo;
import com.xiaomi.micar.club.api.req.member.BatchMemberInfoByVidReq;
import com.xiaomi.micar.club.api.req.member.MemberInfoReq;
import com.xiaomi.micar.club.api.resp.member.BatchMemberInfoResp;
import com.xiaomi.micar.club.api.resp.member.MemberInfoResp;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

/**
 * 人群服务java版本
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VipClubProxyService {

    @Reference(interfaceClass = MemberApi.class, timeout = 200, group = "${vipmember.dubbo.group}", check = false)
    private MemberApi memberApi;

    /**
     * 获取用户会员信息
     *
     * @param userId 用户ID
     * @return 用户会员信息
     * @throws BizError 当调用远程服务或处理响应时发生错误
     */
    public MemberInfoResp getUserMemberInfo(Long userId, String vid) throws BizError {
        MemberInfoReq request = new MemberInfoReq();
        request.setMid(String.valueOf(userId));
        request.setVid(vid);
        // 开始请求
        Result<MemberInfoResp> response;
        try {
            response = memberApi.getMemberByUser(request);
            log.info("invoke rpc getUserTagList success. request:{}. response:{}", GsonUtil.toJson(request), GsonUtil.toJson(response));
        } catch (Exception e) {
            log.error("invoke rpc getUserTagList error. request:{} err", GsonUtil.toJson(request), e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
        if (response.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc memberApi.getMemberByUser fail. userId:{}, code:{} message:{}", userId, response.getCode(), response.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "获取用户Club会员用户信息失败");
        }

        return response.getData();
    }


    /**
     * 根据vid获取会员信息
     *
     * @param vid 会员的vid
     * @return 会员信息响应对象
     * @throws BizError 当调用远程服务或处理响应时发生错误
     */
    public MemberInfo getVidMemberInfo(String vid) throws BizError {
        BatchMemberInfoByVidReq request = new BatchMemberInfoByVidReq();
        request.setVidList(Lists.newArrayList(vid));
        // 开始请求
        Result<BatchMemberInfoResp> response;
        try {
            response = memberApi.batchGetMemberByVid(request);
            log.info("invoke rpc getVidMemberInfo success. vid:{} response:{}", vid, GsonUtil.toJson(response));
        } catch (Exception e) {
            log.error("invoke rpc getVidMemberInfo error. vid:{} err", vid, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
        if (response.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc getVidMemberInfo fail. vid:{}, code:{}  message:{}", vid, response.getCode(), response.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "获取车辆Club会员信息失败");
        }
        if (response.getData() == null) {
            log.error("invoke rpc getVidMemberInfo fail. vid:{}, code:{}  message:{}", vid, response.getCode(), response.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "获取车辆Club会员信息失败");
        }
        if (CollectionUtils.isEmpty(response.getData().getList())) {
            return null;
        }
        return response.getData().getList().getFirst();
    }
}
