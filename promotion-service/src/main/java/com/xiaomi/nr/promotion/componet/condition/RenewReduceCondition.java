package com.xiaomi.nr.promotion.componet.condition;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.RenewReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.GoodsReduceInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.CompareHelper;
import com.xiaomi.nr.promotion.util.IdKeyHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 换新立减条件判断
 *
 * <AUTHOR>
 * @date 2021/5/9
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class RenewReduceCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 活动类型
     */
    private PromotionToolType promotionType;
    /**
     * 销售来源
     */
    private List<String> saleSources;
    /**
     * 活动密码
     */
    private String accessCode;
    /**
     * 减价信息
     */
    private Map<String, GoodsReduceInfo> renewReduceMap;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    /**
     * - 遍历购物车，查询符合条件的商品列表
     *
     * @param request 请求参数
     * @param context 活动内上下文
     * @return 是否满足
     */
    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) {
        Long uid = request.getUserId();
        if (MapUtil.isEmpty(renewReduceMap)) {
            log.error("renewReduceMap is empty. actId:{} uid:{}", promotionId, uid);
            return false;
        }

        String orgCode = request.getOrgCode();
        String renewLevelKey = context.getRenewLevelKey();
        // 门店需要有档位KEY才能参与
        if (StringUtils.isNotEmpty(orgCode) && StringUtils.isEmpty(renewLevelKey)) {
            return false;
        }

        List<CartItem> cartList = request.getCartList();
        boolean fromMiShop = StringUtils.isEmpty(request.getOrgCode());
        // 查找可以参加的购物车Item
        List<GoodsIndex> indexList = findFillGoodsList(context, cartList, fromMiShop);
        if (CollectionUtils.isEmpty(indexList)) {
            return false;
        }
        context.setGoodIndex(indexList);
        return true;
    }

    /**
     * 筛选出可以改价的item index List
     *
     * @param cartList   购物车列表
     * @param fromMiShop 是否来源小米商城
     * @return goodsIndList
     */
    private List<GoodsIndex> findFillGoodsList(LocalContext context, List<CartItem> cartList, boolean fromMiShop) {
        if (CollectionUtils.isEmpty(cartList)) {
            return Collections.emptyList();
        }

        Map<String, GoodsHierarchy> hierarchyMap = context.getGoodsHierarchyMap();
        CompareItem globalInExclude = context.getGlobalInExclu();
        CompareItem globalActInExclude = context.getGlobalActInExclu();
        String renewLevelKey = context.getRenewLevelKey();

        // 做条件匹配
        List<GoodsIndex> indexList = Lists.newArrayList();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            // 检查是否为CartItem 能参加的活动
            boolean itemValid = checkItem(item, renewLevelKey, fromMiShop, hierarchyMap, globalInExclude, globalActInExclude);
            if (!itemValid) {
                continue;
            }
            indexList.add(new GoodsIndex(item.getItemId(), idx));
        }
        return indexList;
    }

    private boolean checkItem(CartItem item, String levelKey, boolean fromMiShop, Map<String, GoodsHierarchy> hierarchyMap,
                              CompareItem globalInExclude, CompareItem globalActInExclude) {
        String skuPackage = CartHelper.getSkuPackage(item);
        boolean itemQualify = CartHelper.checkItemActQualify(item, promotionType.getTypeId(), fromMiShop, saleSources, accessCode);
        if (!itemQualify) {
            log.warn("findFillGoodsList promotionId:{},skuPackage:{},itemQualify:{}", promotionId, skuPackage, itemQualify);
            return false;
        }

        // 是否满足全局排除
        GoodsHierarchy goodsHierarchy = hierarchyMap.get(skuPackage);
        if (CompareHelper.isFillInclude(goodsHierarchy, globalInExclude, true)) {
            log.warn("findFillGoodsList promotionId:{},skuPackage:{},globalInExclude", promotionId, skuPackage);
            return false;
        }
        // 满足全局活动排除的
        if (CompareHelper.isFillInclude(goodsHierarchy, globalActInExclude, true)) {
            log.warn("findFillGoodsList promotionId:{},skuPackage:{},globalActInExclude", promotionId, skuPackage);
            return false;
        }
        String reduceMapKey = IdKeyHelper.generalReduceKey(skuPackage, levelKey);
        GoodsReduceInfo reduceInfo = renewReduceMap.get(reduceMapKey);
        if (reduceInfo == null) {
            return false;
        }
        // 检查金额
        Long reduceAmount = reduceInfo.getReduceAmount();
        long curPrice = CartHelper.goodsCurPrice(item);
        if ((curPrice - reduceAmount) <= 0) {
            log.warn("item curPrice less to reduceAmount. item:{},actId:{},reduceAmount:{}", item, promotionId, reduceAmount);
            return false;
        }
        // 检查活动库存，因为这是订单次数，所有不用同sku相加
        if (reduceInfo.getLimitNum() > 0) {
            Integer usedNum = activityRedisDao.getActGoodsLimitNum(promotionId, reduceMapKey);
            if (usedNum >= reduceInfo.getLimitNum()) {
                log.warn("findFillGoodsList promotionId:{},skuPackage:{},no stock", promotionId, reduceMapKey);
                return false;
            }
        }
        return true;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof RenewReducePromotionConfig)) {
            log.error("config is not instanceof RenewReducePromotionConfig. config:{}", config);
            return;
        }
        RenewReducePromotionConfig promotionConfig = (RenewReducePromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        this.saleSources = promotionConfig.getSaleSources();
        this.accessCode = promotionConfig.getAccessCode();
        this.renewReduceMap = promotionConfig.getRenewReduceInfoMap();
    }
}
