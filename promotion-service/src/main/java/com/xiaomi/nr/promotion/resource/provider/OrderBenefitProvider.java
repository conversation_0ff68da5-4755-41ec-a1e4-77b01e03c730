package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.dao.mysql.promotionuser.OrderBenefitMapper;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.OrderBenefit;
import com.xiaomi.nr.promotion.enums.OrderBenefitEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OrderBenefitProvider implements ResourceProvider<OrderBenefitProvider.OrderBenefitResource> {

    private ResourceObject<OrderBenefitResource> resourceObject;

    @Autowired
    private OrderBenefitMapper orderBenefitMapper;


    @Override
    public ResourceObject<OrderBenefitResource> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<OrderBenefitResource> object) {
        this.resourceObject = object;
    }

    @Override
    public void lock() throws BizError {
        OrderBenefitResource orderBenefitResource = resourceObject.getContent();
        int resCount = orderBenefitMapper.insertOrderBenefit(orderBenefitTransfer(orderBenefitResource));
        if (resCount <= 0) {
            log.warn("lock noCode fail. orderId:{}", orderBenefitResource.getOrderId());
            throw ExceptionHelper.create(ErrCode.COUPON_LOCK_FAIL, "锁定订单优惠资源失败");
        }
        log.info("lock orderBenefit resource success. {}", resourceObject);
    }

    @Override
    public void consume() {
        log.info("consume orderBenefit resource. {}", resourceObject);
    }

    @Override
    public void rollback() throws BizError {
        OrderBenefitResource orderBenefitResource = resourceObject.getContent();
        orderBenefitMapper.updateLockStatus(orderBenefitResource.getUid(), orderBenefitResource.getOrderId(), OrderBenefitEnum.UNLOCKED.getType(), OrderBenefitEnum.LOCKED.getType());
    }

    @Override
    public String conflictText() {
        return "订单优惠资源失败";
    }

    /**
     * 订单优惠资源格式化数据库结构输出
     *
     * @param orderBenefitResource 订单优惠资源
     * @return 订单优惠数据库结构
     */
    private OrderBenefit orderBenefitTransfer(OrderBenefitResource orderBenefitResource) {
        OrderBenefit orderBenefit = new OrderBenefit();
        orderBenefit.setOrderId(orderBenefitResource.getOrderId());
        orderBenefit.setUserId(orderBenefitResource.getUid());
        orderBenefit.setAllReduceMoney(orderBenefitResource.getAllReduceMoney());
        orderBenefit.setOrderMoney(orderBenefitResource.getOrderMoney());
        orderBenefit.setCartMoney(orderBenefitResource.getCartMoney());
        orderBenefit.setProductMoney(orderBenefitResource.getProductMoney());
        orderBenefit.setItems(orderBenefitResource.getItems());
        orderBenefit.setUsedCoupons(orderBenefitResource.getUsedCoupons());
        orderBenefit.setJoinedActs(orderBenefitResource.getJoinedActs());
        orderBenefit.setUsedRedpacket(orderBenefitResource.getUsedRedpacket());
        orderBenefit.setRedpacketReduceMoney(orderBenefitResource.getRedpacketReduceMoney());
        orderBenefit.setCouponReduceMoney(orderBenefitResource.getCouponReduceMoney());
        orderBenefit.setCodeCouponReduceMoney(orderBenefitResource.getCodecouponReduceMoney());
        orderBenefit.setCouponExpressReduceMoney(orderBenefitResource.getCouponExpressReduceMoney());
        orderBenefit.setCouponIds(orderBenefitResource.getCouponIds());
        orderBenefit.setCodeCouponIds(orderBenefitResource.getCodecouponIds());
        orderBenefit.setAppId(orderBenefitResource.getAppId());
        orderBenefit.setAddTime(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()));
        orderBenefit.setLockStatus(orderBenefitResource.getLockStatus());
        orderBenefit.setOrgCode(orderBenefitResource.getOrgCode());
        orderBenefit.setUidType(orderBenefitResource.getUidType());
        return orderBenefit;
    }

    /**
     * 订单优惠信息
     */
    @Data
    public static class OrderBenefitResource {
        /**
         * 订单id
         */
        private Long orderId;

        /**
         * 用户ID
         */
        private Long uid;

        /**
         * 优惠总金额
         */
        private BigDecimal allReduceMoney;

        /**
         * 订单结算总金额
         */
        private BigDecimal orderMoney;

        /**
         * 购物车商品总金额（加入购物车时各金额总和）
         */
        private BigDecimal cartMoney;

        /**
         * 原商品总金额
         */
        private BigDecimal productMoney;

        /**
         * 订单项目列表
         */
        private String items;

        /**
         * 已经使用的优惠券
         */
        private String usedCoupons;

        /**
         * 已经参加的活动
         */
        private String joinedActs;

        /**
         * 已经使用的红包列表
         */
        private String usedRedpacket;

        /**
         * 红包优惠金额
         */
        private BigDecimal redpacketReduceMoney = new BigDecimal("0.00");

        /**
         * 无码券抵扣商品金额
         */
        private BigDecimal couponReduceMoney;

        /**
         * 有码券抵扣商品金额
         */
        private BigDecimal codecouponReduceMoney;

        /**
         * 优惠券邮费减免
         */
        private BigDecimal couponExpressReduceMoney;


        /**
         * 无码券id列表，逗号分割
         */
        private String couponIds;

        /**
         * 有码券id列表，逗号分割
         */
        private String codecouponIds;

        /**
         * clientId
         */
        private Long appId;

        /**
         * 优惠资格锁定状态，1锁定（默认），0已经释放
         */
        private Integer lockStatus;

        /**
         * 机构id
         */
        private String orgCode;

        /**
         * 用户id类型，线下用户id为手机号时为"mobile"
         */
        private String uidType;
    }
}
