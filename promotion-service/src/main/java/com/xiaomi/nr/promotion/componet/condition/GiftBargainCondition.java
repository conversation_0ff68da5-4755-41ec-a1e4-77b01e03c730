package com.xiaomi.nr.promotion.componet.condition;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.entity.redis.FillGoodsGroup;
import com.xiaomi.nr.promotion.entity.redis.QuotaEle;
import com.xiaomi.nr.promotion.enums.PolicyQuotaTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.*;
import com.xiaomi.nr.promotion.model.promotionconfig.*;
import com.xiaomi.nr.promotion.util.CartHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 赠品加价购条件
 *
 * <AUTHOR>
 * @date 2021/6/1
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class GiftBargainCondition extends AbstractCondition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 活动类型
     */
    private PromotionToolType promotionType;
    /**
     * 满足商品组列表(商品+条件）
     */
    private List<FillGoodsGroup> includeGoodsGroups;
    /**
     * 是否允许套装内商品参加
     */
    private boolean checkPackage;
    /**
     * 销售来源
     */
    private List<String> saleSources;
    /**
     * 活动密码
     */
    private String accessCode;

    /**
     * - 获取符合条件组的商品组. 可能有多组， 是一个且的关系
     * - 计算每组可得赠品 加价购数量，并且获取最小值
     * - 获取可参与的主商品
     *
     * @param request 请求参数
     * @param context 活动内上下文
     * @return true/false
     */
    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) {
        if (CollectionUtils.isEmpty(includeGoodsGroups)) {
            log.error("condition is not satisfied. includedGoodsGroups is empty. actId:{} uid:{}", promotionId, request.getUserId());
            return false;
        }

        Long uid = request.getUserId();
        List<CartItem> cartList = request.getCartList();
        boolean online = StringUtils.isEmpty(request.getOrgCode());

        // 获取符合条件组的商品组. 赠品加价购有可能多组
        List<List<GoodsIndex>> indexListGroup = doGoodsListGroupMatch(cartList, uid, online, context);

        // 统计累加符合商品，计算叠加的金额或者件数
        List<ValidCondition> staticGroupList = doStaticGroups(indexListGroup, cartList);

        // 计算次数， 并获得最小值，多个条件为且的关系，遍历判断条件是否满足金额或者件数，其中一个不满足则整体不满足，直接返回
        long fillTimes = calculateFillTimes(staticGroupList, uid);
        if (fillTimes <= 0) {
            log.debug("condition is not satisfied. validCondition is not match groups. actId:{}, uid:{},fillTimes:{}", promotionId, uid, fillTimes);
            return false;
        }

        // 获取符合商品列表，合并fillGoodsListGroup
        List<GoodsIndex> indexList = mergeListGroup(indexListGroup);

        // 没有符合的商品，不满足活动
        if (CollectionUtils.isEmpty(indexList)) {
            log.debug("condition is not satisfied. fillGoodsList is empty. actId:{} uid:{}", promotionId, uid);
            return false;
        }

        context.setFillTimes((int) fillTimes);
        context.setGoodIndex(indexList);
        return true;
    }

    /**
     * 做每组主商品条件商品匹配
     *
     * @param cartList 购物车列表
     * @param uid      用户ID
     * @param online   是否线上
     * @param context  上下文
     * @return 符合组列表
     */
    private List<List<GoodsIndex>> doGoodsListGroupMatch(List<CartItem> cartList, Long uid, boolean online, LocalContext context) {
        // 构建组列表 进行[group] * [cartList] 比对
        return includeGoodsGroups.stream()
                .map(group -> {
                    CompareItem includeGoods = group.getJoinGoods();
                    Integer activityType = promotionType.getTypeId();
                    return doGoodsGroupMatch(cartList, context, includeGoods, uid, online, promotionId, activityType,
                            checkPackage, saleSources, accessCode);
                }).collect(Collectors.toList());
    }

    /**
     * 每组数据统计：统计数量和金额
     *
     * @param indexListGroup 符合商品下标组
     * @param cartList       购物车列表
     * @return 每组统计值
     */
    private List<ValidCondition> doStaticGroups(List<List<GoodsIndex>> indexListGroup, List<CartItem> cartList) {
        List<FillGoodsGroup> goodsGroupList = includeGoodsGroups;
        if (CollectionUtils.isEmpty(indexListGroup) || CollectionUtils.isEmpty(goodsGroupList)) {
            return Collections.emptyList();
        }
        if (indexListGroup.size() != goodsGroupList.size()) {
            return Collections.emptyList();
        }
        List<ValidCondition> includeList = new ArrayList<>();
        for (int gi = 0; gi < indexListGroup.size(); gi++) {
            List<GoodsIndex> indexList = indexListGroup.get(gi);
            FillGoodsGroup goodsGroup = goodsGroupList.get(gi);
            if (goodsGroup == null || goodsGroup.getQuota() == null) {
                continue;
            }
            // 汇总金额，数量
            ValidCondition validCondition = summarizeValidCondition(indexList, goodsGroup.getQuota(), cartList);
            includeList.add(validCondition);
        }
        return includeList;
    }

    private ValidCondition summarizeValidCondition(List<GoodsIndex> fillGoodsList, QuotaEle quotaEle, List<CartItem> cartList) {
        long validMoney = 0L;
        int validCount = 0;
        // 赠品加价购只有一个
        PolicyQuotaTypeEnum quotaTypeEnum = PolicyQuotaTypeEnum.getQuotaType(quotaEle.getType());
        // 统计money
        if (quotaTypeEnum == PolicyQuotaTypeEnum.POLICY_QUOTA_MONEY || quotaTypeEnum == PolicyQuotaTypeEnum.POLICY_QUOTA_PER_MONEY) {
            long money = fillGoodsList.stream().map(index -> cartList.get(index.getIndex())).filter(Objects::nonNull)
                    .mapToLong(item -> item.getCartPrice() * item.getCount() - item.getReduceAmount()).sum();
            validMoney += money;

            // 统计count
        } else if (quotaTypeEnum == PolicyQuotaTypeEnum.POLICY_QUOTA_NUM || quotaTypeEnum == PolicyQuotaTypeEnum.POLICY_QUOTA_PER_NUM) {
            int count = fillGoodsList.stream().map(index -> cartList.get(index.getIndex())).filter(Objects::nonNull)
                    .mapToInt(CartItem::getCount).sum();
            validCount += count;
        }

        // 统计packageIdList
        List<String> packageIdList = fillGoodsList.stream().map(index -> cartList.get(index.getIndex())).filter(Objects::nonNull)
                .filter(CartHelper::isPackage).map(CartItem::getPackageId).collect(Collectors.toList());

        ValidCondition validCondition = new ValidCondition();
        validCondition.setMoney(validMoney);
        validCondition.setCount(validCount);
        validCondition.setTimes(0);
        validCondition.setPackageIdList(packageIdList);
        return validCondition;
    }

    /**
     * 计算可获得赠品数/加价购数
     *
     * @param includeList 统计组列表
     * @param uid         用户ID
     * @return 次数
     */
    private long calculateFillTimes(List<ValidCondition> includeList, Long uid) {
        List<FillGoodsGroup> groupList = includeGoodsGroups;
        if (CollectionUtils.isEmpty(includeList)) {
            return 0L;
        }

        long fillTimes = Long.MAX_VALUE;
        for (int i = 0; i < includeList.size(); i++) {
            ValidCondition validCondition = includeList.get(i);
            QuotaEle quotaEle = groupList.get(i).getQuota();
            long validNum = calculateGroupFillTime(validCondition, quotaEle, fillTimes, uid);
            fillTimes = Math.min(fillTimes, validNum);
        }
        log.info("fill. uid:{}, actId:{}, fillTime:{}", uid, promotionId, fillTimes);
        return fillTimes;
    }

    private long calculateGroupFillTime(ValidCondition validCondition, QuotaEle quotaEle, long fillTimes, Long uid) {
        Integer quotaType = quotaEle.getType();
        PolicyQuotaTypeEnum quotaTypeEnum = PolicyQuotaTypeEnum.getQuotaType(quotaType);
        // 满元
        Long quotaMoney = quotaEle.getMoney();
        if (PolicyQuotaTypeEnum.POLICY_QUOTA_MONEY == quotaTypeEnum) {
            if (validCondition.getMoney() < quotaMoney) {
                return 0L;
            }
            return 1L;
        }
        // 满件
        Integer quotaCount = quotaEle.getCount();
        if (PolicyQuotaTypeEnum.POLICY_QUOTA_NUM == quotaTypeEnum) {
            if (validCondition.getCount() < quotaCount) {
                return 0L;
            }
            return 1L;
        }
        // 每满元
        if (PolicyQuotaTypeEnum.POLICY_QUOTA_PER_MONEY == quotaTypeEnum) {
            // 校验quotaMoney是否为正整数
            if (quotaMoney <= 0) {
                log.error("quotaMoney is invalid. user:{}, actId:{}", uid, promotionId);
                return 0L;
            }
            // 不满足每满元
            if (validCondition.getMoney() < quotaMoney) {
                return 0L;
            }
            long validTimes = Math.floorDiv(validCondition.getMoney(), quotaMoney);
            if (validTimes < fillTimes) {
                fillTimes = validTimes;
            }
            return fillTimes;
        }
        // 每满件
        if (PolicyQuotaTypeEnum.POLICY_QUOTA_PER_NUM == quotaTypeEnum) {
            // 校验quotaMoney是否为正整数
            if (quotaCount <= 0) {
                log.error("quota count is invalid. user:{}, actId:{}", uid, promotionId);
                return 0L;
            }
            // 不满足每满元
            if (validCondition.getCount() < quotaCount) {
                return 0L;
            }
            long validTimes = Math.floorDiv(validCondition.getCount(), quotaCount);
            if (validTimes < fillTimes) {
                fillTimes = validTimes;
            }
            return fillTimes;
        }
        return 0L;
    }

    private List<GoodsIndex> mergeListGroup(List<List<GoodsIndex>> indexGroupList) {
        if (CollectionUtils.isEmpty(indexGroupList)) {
            return Collections.emptyList();
        }
        // 只有一组， 直接返回
        if (indexGroupList.size() == 1) {
            return indexGroupList.get(0);
        }
        // 多组，进行匹配
        List<GoodsIndex> fillGoodsList = new ArrayList<>(indexGroupList.get(0));
        for (int idx = 1; idx < indexGroupList.size(); idx++) {
            List<GoodsIndex> indexList = indexGroupList.get(idx);
            if (CollectionUtils.isEmpty(indexList)) {
                continue;
            }
            List<GoodsIndex> noneMatchList = indexList.stream()
                    .filter(index -> fillGoodsList.stream().noneMatch(item -> item.getItemId().equals(index.getItemId())))
                    .collect(Collectors.toList());
            fillGoodsList.addAll(noneMatchList);
        }
        return fillGoodsList;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof MultiPromotionConfig)) {
            log.error("config is not instanceof MultiPromotionConfig. config:{}", config);
            return;
        }
        MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        this.checkPackage = promotionConfig.isCheckPackage();
        this.saleSources = promotionConfig.getSaleSources();
        this.accessCode = promotionConfig.getAccessCode();
        if (config instanceof BargainPromotionConfig) {
            this.includeGoodsGroups = ((BargainPromotionConfig) config).getIncludeGoodsGroups();
        }
        // 买赠
        if (config instanceof BuyGiftPromotionConfig) {
            this.includeGoodsGroups = Lists.newArrayList(((BuyGiftPromotionConfig) config).getIncludeGoodsGroup());
        }
    }
}
