package com.xiaomi.nr.promotion.componet.action;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsActCount;
import com.xiaomi.nr.promotion.model.common.PartOnsaleExtend;
import com.xiaomi.nr.promotion.model.common.PartOnsaleInfo;
import com.xiaomi.nr.promotion.model.common.PromotionExtend;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.PartOnsalePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.resource.provider.ActGoodsStoreLimitResourceProvider;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 指定门店降价活动的优惠计算和分摊
 *
 * <AUTHOR>
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class PartOnsaleAction extends AbstractAction {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 指定门店降价信息Map
     * key: skuPackage val:ActPriceInfo
     */
    private Map<String, ActPriceInfo> onsaleInfoMap;
    /**
     * 是否限制商品数量（活动维度）
     */
    private boolean numLimit;
    /**
     * 活动限制规则
     */
    private ActNumLimitRule numLimitRule;
    /**
     * 分摊信息
     */
    @Autowired
    private CheckoutCartTool checkoutCartTool;

    @Autowired
    private ActivityRedisDao activityRedisDao;


    @Override
    public void execute(ActivityTool activityTool, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (MapUtils.isEmpty(context.getCartItemActCountMap())) {
            log.info("PartOnsaleAction cartItemActCountMap is empty. actId:{} uid:{}", promotionId, request.getUserId());
            return;
        }

        Map<String, GoodsActCount> cartItemActCountMap = context.getCartItemActCountMap();
        List<CartItem> cartList = request.getCartList();

        // 算价，计算优惠 (从context获取可以参加活动的购物车)
        calcReduce(request.getUserId(), cartList, cartItemActCountMap, onsaleInfoMap);

        // 组织优惠信息promotionInfo，填充到context
        setResult(request, context, activityTool, cartList);

        // 初始化资源
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            submitResourceInit(request, context, cartList, cartItemActCountMap);
        }
    }

    private void submitResourceInit(CheckoutPromotionRequest request, LocalContext context, List<CartItem> cartList, Map<String, GoodsActCount> cartItemActCountMap) throws BizError {
        AtomicInteger actSumReduceCount = new AtomicInteger();
        Map<String, ActGoodsStoreLimitResourceProvider.ResContentItem> skuPackageList = new HashMap<>();
        cartList.forEach(item -> {
            if (item == null) {
                return;
            }
            GoodsActCount actCount = cartItemActCountMap.get(item.getItemId());
            if (actCount == null) {
                return;
            }
            String skuPackage = CartHelper.getSkuPackage(item);
            ActPriceInfo priceInfo = onsaleInfoMap.get(skuPackage);
            if (priceInfo == null) {
                log.warn("PartOnsaleAction onsaleInfoMap.get(skuPackage) is not exit. actId:{} userId:{} itemId:{}", promotionId, request.getUserId(), item.getItemId());
                return;
            }
            if (priceInfo.getLimitRule() == null || priceInfo.getLimitRule().getActivityLimitOne() == null) {
                log.warn("PartOnsaleAction getLimitRule or getActivityLimitOne is null. actId:{} userId:{} itemId:{}", promotionId, request.getUserId(), item.getItemId());
                return;
            }
            ActGoodsStoreLimitResourceProvider.ResContentItem actCountItem = new ActGoodsStoreLimitResourceProvider.ResContentItem();

            int joinCount = actCount.getJoinActCount();
            if (skuPackageList.containsKey(skuPackage)) {
                joinCount += skuPackageList.get(skuPackage).getCount();
            }
            actCountItem.setCount(joinCount);
            actCountItem.setLimitNum(priceInfo.getLimitRule().getActivityLimitOne().longValue());
            skuPackageList.put(skuPackage, actCountItem);
            actSumReduceCount.addAndGet(joinCount);
        });
        initPartOnsaleResource(request, promotionId, request.getOrgCode(), actSumReduceCount.intValue(), skuPackageList, context);
    }

    private void calcReduce(Long userId, List<CartItem> cartList, Map<String, GoodsActCount> cartItemActCountMap, Map<String, ActPriceInfo> onsaleInfoMap) {
        for (CartItem item : cartList) {
            if (item == null) {
                continue;
            }

            if (item.getSubItemList() == null) {
                item.setSubItemList(new ArrayList<>());
            }

            GoodsActCount actCount = cartItemActCountMap.get(item.getItemId());
            if (actCount == null) {
                continue;
            }

            String skuPackage = CartHelper.getSkuPackage(item);
            ActPriceInfo priceInfo = onsaleInfoMap.get(skuPackage);
            if (priceInfo == null) {
                log.warn("PartOnsaleAction onsaleInfoMap.get(skuPackage) is not exit. actId:{} userId:{} itemId:{}", promotionId, userId, item.getItemId());
                continue;
            }

            doChangeCartPrice(item, actCount, priceInfo);
        }
    }

    private void doChangeCartPrice(CartItem cartItem, GoodsActCount actCount, ActPriceInfo priceInfo) {
        //总减免金额
        long reduceAmount = cartItem.getCartPrice() - priceInfo.getPrice();
        if (reduceAmount <= 0) {
            return;
        }
        long totalReduceAmount = reduceAmount * actCount.getJoinActCount();

        //物车条目减免金额+优惠明细
        checkoutCartTool.divideCartsReduce(totalReduceAmount, Collections.singletonList(cartItem), getIdKey(promotionId), ActivityTypeEnum.PARTONSALE.getValue(), promotionId);

        //指定门店活动
        SubItem subItem = new SubItem();
        subItem.setPrice(priceInfo.getPrice());
        subItem.setCount(actCount.getJoinActCount());
        subItem.setActType(ActivityTypeEnum.PARTONSALE.getValue());
        cartItem.getSubItemList().add(subItem);

        //直降 或 门店价 等活动（改价的，非改价的活动忽略）
        int noActCount = cartItem.getCount() - actCount.getJoinActCount();
        if (noActCount > 0) {
            SubItem otherSubItem = new SubItem();
            otherSubItem.setPrice(cartItem.getCartPrice());
            otherSubItem.setCount(noActCount);
            otherSubItem.setActType(cartItem.getChangePriceActType());
            cartItem.getSubItemList().add(otherSubItem);
        }
    }

    private void setResult(CheckoutPromotionRequest request, LocalContext context, ActivityTool tool, List<CartItem> cartList) throws BizError {
        Map<String, GoodsActCount> cartItemActCountMap = context.getCartItemActCountMap();
        List<String> itemList = cartItemActCountMap.keySet().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<CartItem> fillCartList = cartList.stream().filter(cartItem -> {
            if (cartItem == null) {
                return false;
            }
            return cartItemActCountMap.containsKey(cartItem.getItemId());
        }).collect(Collectors.toList());

        Integer joined = CollectionUtils.isNotEmpty(itemList) ? BooleanEnum.YES.getValue() : BooleanEnum.NO.getValue();

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setJoinedItemId(itemList);
        promotionInfo.setParentItemId(itemList);
        promotionInfo.setExtend(generateActExpandInfo(request, context, tool, fillCartList));
        promotionInfo.setJoined(joined);

        // 设置上下文结果
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    private String generateActExpandInfo(CheckoutPromotionRequest request, LocalContext context, ActivityTool tool, List<CartItem> fillCartList) {
        boolean isEStore = isEStore(request, context);

        //所有返回的值都是redis里的剩余量(含购物车占用的)
        List<PartOnsaleInfo> onSaleJoinList = getSkuPackageRemainActCount(context, fillCartList);

        //目前现状：非云店即为门店，以后再加别的场景，再加进来（如果能区分是门店就最好了，目前还无法直接区分）
        if (!isEStore) {
            complementAllSkuPackageRemainActCount(request.getOrgCode(), tool.getId(), onSaleJoinList);
        }

        AtomicInteger cartCount = new AtomicInteger();
        context.getCartItemActCountMap().forEach((itemId, actCount) -> {
            cartCount.addAndGet(actCount.getJoinActCount());
        });

        //要在活动扩展信息里也放一份，购物里也要保留一份
        Map<String, List<SubItem>> cartSubItemList = getCartSubItemList(request);

        PartOnsaleExtend partExtend = new PartOnsaleExtend();
        partExtend.setJoinExtend(onSaleJoinList);
        partExtend.setCartSubItemList(cartSubItemList);
        // pos未升级pulse.thrift文件，活动限购配置在extend里冗余一份
        partExtend.setNumLimit(numLimit);
        partExtend.setNumLimitRule(numLimitRule);
        PromotionExtend extend = new PromotionExtend();
        extend.setCurCount(cartCount.intValue());
        extend.setMaxCount(context.getRemainPersonStoreNum().intValue());
        extend.setPartOnsaleExtend(partExtend);
        return GsonUtil.toJson(extend);
    }

    /**
     * 补足所有sku/package剩余活动数量
     *
     * @param orgCode        String
     * @param actId          Long
     * @param onSaleJoinList List<PartOnsaleInfo>
     */
    private void complementAllSkuPackageRemainActCount(String orgCode, Long actId, List<PartOnsaleInfo> onSaleJoinList) {
        List<String> skuPackages = new ArrayList<>(onsaleInfoMap.keySet());
        Map<String, Long> allSkuPackageLimit = activityRedisDao.batchGetActGoodsStoreLimitNum(orgCode, actId, skuPackages);
        allSkuPackageLimit.forEach((skuPackage, limitCount) -> {
            AtomicBoolean exist = new AtomicBoolean(false);
            onSaleJoinList.forEach(item -> {
                if (skuPackage.equals(item.getSkuOrPackage().toString())) {
                    exist.set(true);
                }
            });
            if (!exist.get()) {
                long sum = onsaleInfoMap.get(skuPackage).getLimitRule().getActivityLimitOne();
                long surplus = sum - limitCount > 0 ? sum - limitCount : 0L;
                PartOnsaleInfo join = new PartOnsaleInfo();
                join.setSkuOrPackage(Long.parseLong(skuPackage));
                join.setRemainActCount(surplus);
                onSaleJoinList.add(join);
            }
        });
    }

    /**
     * 获取购物车中sku/package剩余活动数量, 所有返回的值都是redis里的剩余量(含购物车占用的)
     *
     * @param context      LocalContext
     * @param fillCartList List<CartItem>
     * @return List<PartOnsaleInfo>
     */
    private List<PartOnsaleInfo> getSkuPackageRemainActCount(LocalContext context, List<CartItem> fillCartList) {
        Map<Long, List<CartItem>> cartsMap = fillCartList.stream().collect(Collectors.groupingBy(item -> Long.valueOf(CartHelper.getSkuPackage(item))));
        List<PartOnsaleInfo> onSaleJoinList = Lists.newArrayList();
        cartsMap.forEach((skuPackage, list) -> {
            GoodsActCount actCount = context.getGoodsActCountMap().get(skuPackage.toString());
            long remainActCount = (actCount == null || actCount.getRemainStoreOne() == null || actCount.getRemainStoreOne() < 0) ? 0 : actCount.getRemainStoreOne();
            PartOnsaleInfo join = new PartOnsaleInfo();
            join.setSkuOrPackage(skuPackage);
            join.setRemainActCount(remainActCount);
            onSaleJoinList.add(join);
        });
        return onSaleJoinList;
    }

    /**
     * 购物车参加指定门店降价活动的具体扣减明细
     *
     * @param request CheckoutPromotionRequest
     * @return Map<String, List < SubItem>>
     */
    private Map<String, List<SubItem>> getCartSubItemList(CheckoutPromotionRequest request) {
        Map<String, List<SubItem>> cartSubItemList = new HashMap<>();
        List<CartItem> cartList = request.getCartList();
        cartList.forEach(item -> {
            cartSubItemList.put(item.getItemId(), item.getSubItemList());
        });
        return cartSubItemList;
    }

    /**
     * 是否为云店请求
     *
     * @param request CheckoutPromotionRequest
     * @param context LocalContext
     * @return boolean
     */
    private boolean isEStore(CheckoutPromotionRequest request, LocalContext context) {
        String globalBusinessPartner = request.getGlobalBusinessPartner();
        if (!StringUtils.isEmpty(globalBusinessPartner) && globalBusinessPartner.equals(PromotionConstant.ESTORE)) {
            return true;
        }
        return false;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof PartOnsalePromotionConfig)) {
            return;
        }
        PartOnsalePromotionConfig promotionConfig = (PartOnsalePromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.onsaleInfoMap = promotionConfig.getOnsaleInfoMap();
        this.numLimitRule = promotionConfig.getNumLimitRule();
        this.numLimit = promotionConfig.isNumLimit();
    }
}
