package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.enums.UserGroupActionEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MultiPromotionConfig;
import com.xiaomi.nr.promotion.rpc.usertag.UserTagProxyService;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 人群条件检查
 *
 * <AUTHOR>
 * @date 2021/6/1
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class UserGroupCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 能参与人群范围
     */
    private UserGroupActionEnum groupAction;
    /**
     * 能参与的人群
     */
    private List<String> selectGroups;

    @Autowired
    private UserTagProxyService userTagServiceProxy;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (StringUtils.isNotEmpty(request.getOrgCode()) || groupAction == null) {
            return true;
        }
        // 包含全部人群
        if (groupAction == UserGroupActionEnum.INCLUDE && CollectionUtils.isEmpty(selectGroups)) {
            return true;
        }

        // rpc调人群服务
        Long uid = request.getUserId();
        List<String> groups = userTagServiceProxy.getUserTagList(uid);
        // 用户不属于任何分组  条件里包含分组
        if (groupAction == UserGroupActionEnum.INCLUDE && CollectionUtils.isEmpty(groups)) {
            log.debug("condition is not satisfied. user does not belong to any group. uid:{}", uid);
            return false;
        }

        // 用户的组在条件组里
        boolean exist = groups.stream().anyMatch(g -> selectGroups.contains(g));

        // 用户不在活动配置人群中，不满足活动
        if (UserGroupActionEnum.INCLUDE == groupAction && !exist) {
            log.debug("condition is not satisfied. user act group not fulfill. uid:{} actId:{} selectGroups:{} groups:{}", uid, promotionId, selectGroups, groups);
            return false;
        }
        return true;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof MultiPromotionConfig)) {
            log.error("config is not instanceof MultiPromotionConfig. config:{}", config);
            return;
        }
        MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.selectGroups = promotionConfig.getSelectGroups();
        this.groupAction = promotionConfig.getGroupAction();
    }
}
