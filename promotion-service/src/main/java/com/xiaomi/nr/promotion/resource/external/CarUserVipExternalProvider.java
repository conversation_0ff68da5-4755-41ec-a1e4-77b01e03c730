package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.micar.club.api.model.member.MemberInfo;
import com.xiaomi.micar.club.api.resp.member.MemberInfoResp;
import com.xiaomi.nr.promotion.activity.pool.ActSearchParam;
import com.xiaomi.nr.promotion.activity.pool.CarShopActivitySearcher;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.domain.activity.service.common.VipMemberService;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/21 15:36
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class CarUserVipExternalProvider extends ExternalDataProvider<MemberInfo> {

    @Autowired
    private VipMemberService vipMemberService;

    @Autowired
    private CarShopActivitySearcher carShopActivitySearcher;


    private ListenableFuture<MemberInfo> future;

    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        if (!Objects.equals(request.getChannel(), ChannelEnum.CAR_SHOP.getValue())) {
            return;
        }

        if (request.getUserId() == null || request.getUserId() <= 0) {
            return;
        }

        List<ActSearchParam.GoodsInSearch> goodsInSearchList = carShopActivitySearcher.createSearchGoods(request.getCartList());
        ActSearchParam param = new ActSearchParam()
                .setChannel(request.getChannel())
                .setGoodsList(goodsInSearchList);
        List<ActivityTool> activityTools = carShopActivitySearcher.searchCarActivity(param);

        boolean canJoinAct = activityTools.stream()
                .anyMatch(activityTool -> activityTool.getType().getTypeId() == ActivityTypeEnum.CAR_SHOP_VIP.getValue());
        if (!canJoinAct) {
            return;
        }
        future = vipMemberService.getUserMemberInfoAsync(request.getUserId(), request.getVid());
    }

    @Override
    protected long getTimeoutMills() {
        return DEFAULT_TIMEOUT;
    }

    @Override
    public ListenableFuture<MemberInfo> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.MID_ULTRA_VIP_MEMBER;
    }
}
