package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.RequestContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MultiPromotionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.util.concurrent.TimeUnit;

/**
 * 周期性活动时间判断组件
 *
 * <AUTHOR>
 * @date 2021/6/1
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class DailyTimeCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 活动结束时间
     */
    private long endDateTime;
    /**
     * 是否周期性
     */
    private boolean daily;
    /**
     * 每周期开始时间
     */
    private LocalTime dailyStartTime;
    /**
     * 每周期结束时间
     */
    private LocalTime dailyEndTime;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) {
        if (StringUtils.isNotBlank(request.getOrgCode()) || !daily) {
            context.setActEndTime(endDateTime);
            return true;
        }

        long requestTime = RequestContext.getCurrent().getRequestTime();
        long now = TimeUnit.MILLISECONDS.toSeconds(requestTime);

        // 当天重复
        LocalTime nowTime = LocalTime.now();
        if (dailyStartTime.isBefore(dailyEndTime)) {
            if (dailyStartTime.isAfter(nowTime) || dailyEndTime.isBefore(nowTime)) {
                return false;
            }
            // 跨天重复
        } else if (nowTime.isAfter(dailyEndTime) && nowTime.isBefore(dailyStartTime)) {
            return false;
        }
        context.setActEndTime(now);
        return true;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof MultiPromotionConfig)) {
            log.error("config is not instanceof MultiPromotionConfig. config:{}", config);
            return;
        }
        MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.endDateTime = promotionConfig.getUnixEndTime();
        this.daily = promotionConfig.isDaily();
        this.dailyStartTime = promotionConfig.getDailyStartTime();
        this.dailyEndTime = promotionConfig.getDailyEndTime();
    }
}
