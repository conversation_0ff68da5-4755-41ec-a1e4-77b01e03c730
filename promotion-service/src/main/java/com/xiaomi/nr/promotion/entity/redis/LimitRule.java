package com.xiaomi.nr.promotion.entity.redis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * 直降政策
 *
 * <AUTHOR>
 * @date 2021/3/26
 */
@Data
public class LimitRule implements Serializable {
    private static final long serialVersionUID = 355449167249845669L;
    /**
     * 每单限购数
     */
    @SerializedName("order_limit")
    private Integer orderLimitBuy = 0;

    /**
     * 每人限购数
     */
    @SerializedName("person_limit")
    private Integer personLimit = 0;

    /**
     * 每天每个门店限量
     */
    @SerializedName("day_limit_one")
    private Integer dayLimitOne = 0;

    /**
     * 每天全部门店限量
     */
    @SerializedName("day_limit_all")
    private Integer dayLimitAll = 0;

    /**
     * 活动期间每个门店限量
     */
    @SerializedName("activity_limit_one")
    private Integer activityLimitOne = 0;

    /**
     * 活动期间全部门店限量
     */
    @SerializedName("activity_limit_all")
    private Integer activityLimitAll = 0;

    /**
     * 活动期间每个用户在每个门店限购数量
     */
    @SerializedName("person_limit_one")
    private Integer personLimitOne = 0;
}
