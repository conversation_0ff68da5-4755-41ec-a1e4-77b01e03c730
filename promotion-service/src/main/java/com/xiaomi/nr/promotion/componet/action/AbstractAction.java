package com.xiaomi.nr.promotion.componet.action;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.CartItemChild;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.domain.phoenix.model.SubsidyPhoenixConvert;
import com.xiaomi.nr.promotion.domain.subsidyactivity.model.TradeInQualifyInfo;
import com.xiaomi.nr.promotion.engine.componet.Action;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.entity.redis.GiftBargainGroup;
import com.xiaomi.nr.promotion.entity.redis.QuotaEle;
import com.xiaomi.nr.promotion.entity.redis.SkuGroup;
import com.xiaomi.nr.promotion.enums.ActFrequencyEnum;
import com.xiaomi.nr.promotion.enums.PolicyQuotaTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.GroupCurCount;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.model.ReturnStatus;
import com.xiaomi.nr.promotion.resource.provider.*;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动: 计价和均摊动作
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Slf4j
public abstract class AbstractAction extends Action {
    /**
     * 购物车商品删除FLAG
     */
    protected static final String CART_DEL_FLAG = "del";

    @Autowired
    private ResourceProviderFactory resourceProviderFactory;

    /**
     * 获取ID key
     *
     * @param activityId 活动ID
     * @return IdKey
     */
    protected String getIdKey(Long activityId) {
        return PromotionConstant.CARTLIST_ACTIVITY_PREFIX + activityId;
    }

    /**
     * 获取索引列表
     *
     * @param indexList 索引信息列表
     * @return 索引列表
     */
    protected List<Integer> getCartIndexList(List<GoodsIndex> indexList) {
        return indexList.stream()
                .map(GoodsIndex::getIndex)
                .collect(Collectors.toList());
    }

    /**
     * 赠品加价购查找匹配
     *
     * @param groups   组
     * @param cartItem 购物车项
     * @param orgCode  门店Code
     * @return SkuGroup
     */
    protected com.xiaomi.nr.promotion.entity.redis.SkuGroup getMatchSkuGroup(com.xiaomi.nr.promotion.entity.redis.SkuGroup groups, CartItem cartItem,
                                        String orgCode) {
        List<GiftBargainGroup> skuInfoList = groups.getListInfo();
        if (CollectionUtils.isEmpty(skuInfoList)) {
            return null;
        }

        GiftBargainGroup skuInfo = skuInfoList.stream()
                .filter(item -> this.isMatchSkuInfo(item, groups, cartItem, orgCode)).findAny().orElse(null);
        if (skuInfo == null) {
            return null;
        }
        final List<GiftBargainGroup> giftGroupList = new ArrayList<>();
        giftGroupList.add(skuInfo);
        SkuGroup skuGroup = new SkuGroup();
        skuGroup.setListInfo(giftGroupList);
        skuGroup.setGroupId(groups.getGroupId());
        return skuGroup;
    }

    private boolean isMatchSkuInfo(GiftBargainGroup skuInfo, SkuGroup groups, CartItem cartItem, String orgCode) {
        String skuPackage = String.valueOf(skuInfo.getSku());
        boolean isSku = Objects.equals(skuPackage, cartItem.getSku());
        boolean isPackage = Objects.equals(skuPackage, cartItem.getPackageId());
        // 线上
        if (StringUtils.isEmpty(orgCode)) {
            return isSku || isPackage;
        }
        // 线下
        boolean isGroup = Objects.equals(groups.getGroupId(), cartItem.getGroupId());
        return (isSku || isPackage) && isGroup;
    }

    /**
     * 检查组赠品加价购数，如果大于maxCount需要调整到maxCount
     *
     * @param cartGroup 赠品组
     * @param maxCount  最大赠品数
     * @return 调整后组数
     */
    protected Map<Long, Integer> adjustGroupCount(Map<Long, List<CartItem>> cartGroup, Integer maxCount) {
        Map<Long, Integer> countMap = new HashMap<>();
        for (Map.Entry<Long, List<CartItem>> entry : cartGroup.entrySet()) {
            int groupValidCount = checkAndReduceGroupCount(entry.getValue(), maxCount);
            countMap.put(entry.getKey(), groupValidCount);
        }
        return countMap;
    }

    private int checkAndReduceGroupCount(List<CartItem> cartItemGroup, Integer maxCount) {
        if (CollectionUtils.isEmpty(cartItemGroup)) {
            return maxCount;
        }
        // 同groupId，组赠品总数
        int validCountGroup = cartItemGroup.stream().mapToInt(CartItem::getCount).sum();
        if (validCountGroup <= maxCount) {
            return validCountGroup;
        }
        // 超出数量，需要减到合理值
        for (CartItem groupItem : cartItemGroup) {
            validCountGroup = reduceGroupCount(validCountGroup, groupItem, maxCount);
        }
        return validCountGroup;
    }

    private int reduceGroupCount(int validCountGroup, CartItem item, Integer maxCount) {
        if (CART_DEL_FLAG.equals(item.getItemId())) {
            return validCountGroup;
        }
        int itemCount = item.getCount();
        int diff = validCountGroup - maxCount;
        // 减去当前项count后数量还是>maxCount, 则删除
        if ((validCountGroup - itemCount) > maxCount) {
            item.setItemId(CART_DEL_FLAG);
            diff = itemCount;
        } else {
            // 减去当前项后数量<=maxCount, 则减去到maxCount差值就好
            item.setCount(itemCount - diff);
        }
        // 减完了
        if (Objects.equals(item.getCount(), 0)) {
            item.setItemId(CART_DEL_FLAG);
        }
        validCountGroup -= diff;
        return validCountGroup;
    }

    /**
     * 购物车改价
     */
    protected void changePriceWithReduceDetail(CartItem item, long price, Long promotionId, PromotionToolType promotionType) {
        // 记录原来价格
        Long originalPrice = item.getCartPrice();
        // 记录套装原价
        Map<Integer, Long> childOriPriceMap = new HashMap<>(item.getChilds().size());
        for (int idx = 0; idx < item.getChilds().size(); idx++) {
            childOriPriceMap.put(idx, item.getChilds().get(idx).getSellPrice());
        }
        // 改价
        //不能改cartPrice，否则会影响交易直降价的判断
        //CartHelper.changePrice(item, price);

        // sellPrice代表直降价，仅能被直降活动修改
        CartHelper.updateChildSellPrice(item, price);

        // 记录优惠
        List<ReduceDetailItem> reduceItemList = Optional.ofNullable(item.getReduceItemList()).orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(item.getChilds())) {
            ReduceDetailItem detailItem  = initReduceDetailItem(promotionId, promotionType, item.getSsuId(),
                    originalPrice, price, item.getCount());
            reduceItemList.add(detailItem);
        } else {
            childOriPriceMap.forEach((idx, originalChildPrice) -> {
                CartItemChild child = item.getChilds().get(idx);
                ReduceDetailItem detailItem  = initReduceDetailItem(promotionId, promotionType, child.getSsuId(),
                        originalChildPrice, child.getSellPrice(), item.getCount());
                reduceItemList.add(detailItem);
            });
        }

        // sellPrice回写, sellPrice代表直降价，仅能被直降活动修改
        for (int idx = 0; idx < item.getChilds().size(); idx++) {
            Long originalSellPrice = childOriPriceMap.get(idx);
            item.getChilds().get(idx).setSellPrice(originalSellPrice);
        }
    }

    protected ReduceDetailItem initReduceDetailItem(Long promotionId, PromotionToolType promotionType, Long ssuId,
                                                  Long originalPrice, Long price, Integer count) {
        long reduceSingle = originalPrice - price;
        long reduceTotal = reduceSingle * count;

        ReduceDetailItem detailItem = new ReduceDetailItem();
        detailItem.setPromotionId(promotionId);
        detailItem.setPromotionType(promotionType.getTypeId());
        detailItem.setSsuId(ssuId);
        detailItem.setReduce(reduceTotal);
        detailItem.setReduceSingle(reduceSingle);
        return detailItem;
    }

    /**
     * 删除无效类
     *
     * @param cartList 购物车列表
     */
    protected void delInvalidCarts(List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(cartList)) {
            return;
        }
        List<CartItem> delCartItemList = cartList.stream().filter(item -> CART_DEL_FLAG.equals(item.getItemId()))
                .collect(Collectors.toList());
        cartList.removeAll(delCartItemList);
    }

    /**
     * 获取满足的条件
     *
     * @param quotas     条件列表
     * @param validGoods 统计对象
     * @return 条件
     */
    protected QuotaEle getSatisfiedQuota(List<QuotaEle> quotas, ValidGoods validGoods) {
        if (CollectionUtils.isEmpty(quotas)) {
            return null;
        }
        return quotas.stream().filter(quotaEle -> isSatisfiedQuota(quotaEle, validGoods)).findAny().orElse(null);
    }

    private boolean isSatisfiedQuota(QuotaEle quotaEle, ValidGoods validGoods) {
        PolicyQuotaTypeEnum quotaType = PolicyQuotaTypeEnum.getQuotaType(quotaEle.getType());
        if (quotaType == null) {
            return false;
        }
        boolean isSatisfied = false;
        switch (quotaType) {
            case POLICY_QUOTA_MONEY:
            case POLICY_QUOTA_PER_MONEY:
                isSatisfied = validGoods.getValidPrice() >= quotaEle.getMoney();
                break;
            case POLICY_QUOTA_NUM:
            case POLICY_QUOTA_PER_NUM:
                isSatisfied = validGoods.getValidNum() >= quotaEle.getCount();
                break;
            default:
                break;
        }
        return isSatisfied;
    }

    /**
     * 构建不断增长map
     *
     * @param cartGroup 商品组
     * @param countMap  数量组
     * @return 增长轨迹列表
     */
    protected List<GroupCurCount> buildIncrCountMap(Map<Long, List<CartItem>> cartGroup, Map<Long, Integer> countMap) {
        List<GroupCurCount> groupCurCount = new ArrayList<>();
        for (Map.Entry<Long, List<CartItem>> entry : cartGroup.entrySet()) {
            Integer curCount = countMap.getOrDefault(entry.getKey(), 0);
            // 生成组内当前使用活动的数量
            GroupCurCount tmpCurCount = new GroupCurCount();
            tmpCurCount.setGroupId(entry.getKey());
            tmpCurCount.setCurrentCount(curCount);
            groupCurCount.add(tmpCurCount);
        }
        return groupCurCount;
    }

    /**
     * 初始化资源
     *
     * @param request       请求对象
     * @param activityId    活动ID
     * @param joinCounts    参与次数
     * @param context       上下文
     * @param actLimitNum   活动规则
     * @param frequencyEnum 频率
     * @param limitRule     限制规则
     * @throws BizError 业务异常
     */
    protected void initResource(CheckoutPromotionRequest request, Long activityId, Integer joinCounts, LocalContext context, Long actLimitNum, ActFrequencyEnum frequencyEnum, ActNumLimitRule limitRule) throws BizError {
        if (joinCounts == null || joinCounts == 0L) {
            return;
        }
        // 线上
        if (StringUtils.isEmpty(request.getOrgCode())) {
            initOnlineResourceHandler(request, activityId, context, actLimitNum, frequencyEnum);
            // 线下
        } else {
            // 赠品和加价购
            initOfflineResourceHandler(request, activityId, joinCounts, context, limitRule);
        }
    }

    protected void initGiftResource(CheckoutPromotionRequest request, Long activityId, LocalContext context, Map<Long, List<CartItem>> cartGroup, ActNumLimitRule limitRule) throws BizError {
        if (StringUtils.isNotEmpty(request.getOrgCode())) {
            initOfflineResourceHandler(request, activityId, context.getFillTimes(), context, limitRule);
        }
        initGiftLimitResource(request, activityId, context, cartGroup);
    }

    protected void initGiftLimitResource(CheckoutPromotionRequest request, Long activityId, LocalContext context, Map<Long, List<CartItem>> cartGroup) throws BizError {
        GiftLimitProvider.ResContent resContent = buildGiftActLimit(cartGroup);
        ResourceObject<GiftLimitProvider.ResContent> resourceObject = buildLimitResource(ResourceType.GIFT_ACT_LIMIT, resContent, activityId, request.getOrderId());
        GiftLimitProvider resourceProvider = (GiftLimitProvider) resourceProviderFactory.getProvider(ResourceType.GIFT_ACT_LIMIT);
        resourceProvider.initResource(resourceObject);
        context.addProvider(resourceProvider);
    }

    protected void initBargainResource(CheckoutPromotionRequest request, Long activityId, Integer joinCounts, LocalContext context, Map<Long, List<CartItem>> cartGroup, Long actLimitNum, ActFrequencyEnum frequencyEnum, ActNumLimitRule limitRule) throws BizError {
        if (joinCounts == null || joinCounts == 0L) {
            return;
        }
        // 线上
        if (StringUtils.isEmpty(request.getOrgCode())) {
            initOnlineResourceHandler(request, activityId, context, actLimitNum, frequencyEnum);
            // 线下
        } else {
            // 赠品和加价购
            initOfflineResourceHandler(request, activityId, joinCounts, context, limitRule);
        }
        BargainLimitProvider.ResContent resContent = buildBargainActLimit(cartGroup);
        ResourceObject<BargainLimitProvider.ResContent> resourceObject = buildLimitResource(ResourceType.BARGAIN_ACT_LIMIT, resContent, activityId, request.getOrderId());
        BargainLimitProvider resourceProvider = (BargainLimitProvider) resourceProviderFactory.getProvider(ResourceType.BARGAIN_ACT_LIMIT);
        resourceProvider.initResource(resourceObject);
        context.addProvider(resourceProvider);
    }

    private GiftLimitProvider.ResContent buildGiftActLimit(Map<Long, List<CartItem>> cartGroup) {
        Map<Long, Map<String, Integer>> countMap = buildNumLimitMap(cartGroup);
        GiftLimitProvider.ResContent resContent = new GiftLimitProvider.ResContent();
        resContent.setCountMap(countMap);
        return resContent;
    }

    private BargainLimitProvider.ResContent buildBargainActLimit(Map<Long, List<CartItem>> cartGroup) {
        Map<Long, Map<String, Integer>> countMap = buildNumLimitMap(cartGroup);
        BargainLimitProvider.ResContent resContent = new BargainLimitProvider.ResContent();
        resContent.setCountMap(countMap);
        return resContent;
    }

    private Map<Long, Map<String, Integer>> buildNumLimitMap(Map<Long, List<CartItem>> cartGroup) {
        Map<Long, Map<String, Integer>> countMap = Maps.newHashMap();
        cartGroup.forEach((groupId, giftList) -> {
            if (CollectionUtils.isEmpty(giftList)) {
                return;
            }
            Map<String, Integer> groupGiftMap = countMap.getOrDefault(groupId, Maps.newHashMap());
            giftList.stream()
                    .filter(item -> !Objects.equals(CART_DEL_FLAG, item.getItemId()))
                    .forEach(item -> groupGiftMap.put(item.getSku(), item.getCount()));
            countMap.put(groupId, groupGiftMap);
        });
        return countMap;
    }

    /**
     * 初始化线上资源
     *
     * @param request       请求对象
     * @param activityId    活动ID
     * @param context       上下文
     * @param actLimitNum   活动规则
     * @param frequencyEnum 频率
     * @throws BizError 业务异常
     */
    private void initOnlineResourceHandler(CheckoutPromotionRequest request, Long activityId, LocalContext context, Long actLimitNum, ActFrequencyEnum frequencyEnum) throws BizError {
        Long finishTime = context.getActEndTime();
        long expireTime = -1L;
        if (finishTime != null && finishTime > 0L) {
            expireTime = finishTime + 3600 * 24 * 7;
        }
        // 用户次数资源
        if (frequencyEnum == ActFrequencyEnum.DAILY) {
            OnlineActUserRecordDailyProvider.ActUserRecordDaily recordDaily = buildOnlineActUserRecordDaily(activityId, request.getUserId(), expireTime);
            ResourceObject<OnlineActUserRecordDailyProvider.ActUserRecordDaily> resourceObject = buildLimitResource(ResourceType.ONLINE_ACT_RECORD_DAILY, recordDaily, activityId, request.getOrderId());
            OnlineActUserRecordDailyProvider resourceProvider = (OnlineActUserRecordDailyProvider) resourceProviderFactory.getProvider(ResourceType.ONLINE_ACT_RECORD_DAILY);
            resourceProvider.initResource(resourceObject);
            context.addProvider(resourceProvider);
        } else if (frequencyEnum == ActFrequencyEnum.TOTAL) {
            OnlineActUserRecordTotalProvider.ActUserRecord recordTotal = buildOnlineActUserRecord(activityId, request.getUserId(), expireTime);
            ResourceObject<OnlineActUserRecordTotalProvider.ActUserRecord> resourceObject = buildLimitResource(ResourceType.ONLINE_ACT_RECORD, recordTotal, activityId, request.getOrderId());
            OnlineActUserRecordTotalProvider resourceProvider = (OnlineActUserRecordTotalProvider) resourceProviderFactory.getProvider(ResourceType.ONLINE_ACT_RECORD);
            resourceProvider.initResource(resourceObject);
            context.addProvider(resourceProvider);
        }
        if (actLimitNum != null && actLimitNum > 0L) {
            int count = 1;
            OnlineActLimitProvider.ActOnlineLimit actOnlineLimit = buildOnlineActLimit(activityId, count, actLimitNum);
            ResourceObject<OnlineActLimitProvider.ActOnlineLimit> resourceObject = buildLimitResource(ResourceType.ONLINE_ACT_LIMIT, actOnlineLimit, activityId, request.getOrderId());
            OnlineActLimitProvider resourceProvider = (OnlineActLimitProvider) resourceProviderFactory.getProvider(ResourceType.ONLINE_ACT_LIMIT);
            resourceProvider.initResource(resourceObject);
            context.addProvider(resourceProvider);
        }
    }

    /**
     * 线下活动资源
     *
     * @param request    请求对象
     * @param activityId 活动ID
     * @param joinCounts 参与数量
     * @param context    上下文
     * @param limitRule  限制规则
     * @throws BizError 业务异常
     */
    private void initOfflineResourceHandler(CheckoutPromotionRequest request, Long activityId, Integer joinCounts, LocalContext context, ActNumLimitRule limitRule) throws BizError {
        String uType = request.getUidType();
        // 每人
        if (StringUtils.isNotEmpty(uType)) {
            OfflineActUtypePersonLimitDailyProvider.ActUtypePersonDayLimit personDayLimit = buildActUTypePersonLimitDaily(activityId, uType, request.getUserId(), joinCounts, limitRule.getPersonLimitDay());
            ResourceObject<OfflineActUtypePersonLimitDailyProvider.ActUtypePersonDayLimit> resourceDayObject = buildLimitResource(ResourceType.OFFLINE_ACT_UTYPE_PERSON_LIMIT_DAILY, personDayLimit, activityId, request.getOrderId());
            OfflineActUtypePersonLimitDailyProvider resourcePersonDayProvider = (OfflineActUtypePersonLimitDailyProvider) resourceProviderFactory.getProvider(ResourceType.OFFLINE_ACT_UTYPE_PERSON_LIMIT_DAILY);
            resourcePersonDayProvider.initResource(resourceDayObject);
            context.addProvider(resourcePersonDayProvider);

            OfflineActUtypePersonLimitProvider.ActUtypePersonLimit personLimit = buildActUTypePersonLimit(activityId, uType, request.getUserId(), joinCounts, limitRule.getPersonLimit());
            ResourceObject<OfflineActUtypePersonLimitProvider.ActUtypePersonLimit> resourceObject = buildLimitResource(ResourceType.OFFLINE_ACT_UTYPE_PERSON_LIMIT, personLimit, activityId, request.getOrderId());
            OfflineActUtypePersonLimitProvider resourcePersonProvider = (OfflineActUtypePersonLimitProvider) resourceProviderFactory.getProvider(ResourceType.OFFLINE_ACT_UTYPE_PERSON_LIMIT);
            resourcePersonProvider.initResource(resourceObject);
            context.addProvider(resourcePersonProvider);
        } else {
            OfflineActPersonLimitDailyProvider.ActPersonDayLimit personDayLimit = buildActPersonLimitDaily(activityId, request.getUserId(), joinCounts, limitRule.getPersonLimitDay());
            ResourceObject<OfflineActPersonLimitDailyProvider.ActPersonDayLimit> resourceDayObject = buildLimitResource(ResourceType.OFFLINE_ACT_PERSON_LIMIT_DAILY, personDayLimit, activityId, request.getOrderId());
            OfflineActPersonLimitDailyProvider resourcePersonDayProvider = (OfflineActPersonLimitDailyProvider) resourceProviderFactory.getProvider(ResourceType.OFFLINE_ACT_PERSON_LIMIT_DAILY);
            resourcePersonDayProvider.initResource(resourceDayObject);
            context.addProvider(resourcePersonDayProvider);

            OfflineActPersonLimitProvider.ActPersonLimit personLimit = buildActPersonLimit(activityId, request.getUserId(), joinCounts, limitRule.getPersonLimit());
            ResourceObject<OfflineActPersonLimitProvider.ActPersonLimit> resourceObject = buildLimitResource(ResourceType.OFFLINE_ACT_PERSON_LIMIT, personLimit, activityId, request.getOrderId());
            OfflineActPersonLimitProvider resourcePersonProvider = (OfflineActPersonLimitProvider) resourceProviderFactory.getProvider(ResourceType.OFFLINE_ACT_PERSON_LIMIT);
            resourcePersonProvider.initResource(resourceObject);
            context.addProvider(resourcePersonProvider);
        }
        // 每天全国所有门店
        OfflineActAllStoreLimitDailyProvider.ActAllStoreDayLimit allStoreDay = buildActAllStoreLimitDaily(activityId, joinCounts, limitRule.getDayLimitAll());
        ResourceObject<OfflineActAllStoreLimitDailyProvider.ActAllStoreDayLimit> allStoreDayResourceObject = buildLimitResource(ResourceType.OFFLINE_ACT_ALL_STORE_LIMIT_DAILY, allStoreDay, activityId, request.getOrderId());
        OfflineActAllStoreLimitDailyProvider allStoreDayResourceProvider = (OfflineActAllStoreLimitDailyProvider) resourceProviderFactory.getProvider(ResourceType.OFFLINE_ACT_ALL_STORE_LIMIT_DAILY);
        allStoreDayResourceProvider.initResource(allStoreDayResourceObject);
        context.addProvider(allStoreDayResourceProvider);

        // 全国所有门店
        OfflineActAllStoreLimitProvider.ActAllStoreLimit allStore = buildActAllStoreLimit(activityId, joinCounts, limitRule.getActivityLimitAll());
        ResourceObject<OfflineActAllStoreLimitProvider.ActAllStoreLimit> allStoreResourceObject = buildLimitResource(ResourceType.OFFLINE_ACT_ALL_STORE_LIMIT, allStore, activityId, request.getOrderId());
        OfflineActAllStoreLimitProvider allStoreResourceProvider = (OfflineActAllStoreLimitProvider) resourceProviderFactory.getProvider(ResourceType.OFFLINE_ACT_ALL_STORE_LIMIT);
        allStoreResourceProvider.initResource(allStoreResourceObject);
        context.addProvider(allStoreResourceProvider);

        // 每个门店每天
        OfflineActStoreLimitDailyProvider.ActStoreDayLimit storeDay = buildActStoreLimitDaily(activityId, request.getOrgCode(), joinCounts, limitRule.getDayLimitOne());
        ResourceObject<OfflineActStoreLimitDailyProvider.ActStoreDayLimit> storeDayResourceObject = buildLimitResource(ResourceType.OFFLINE_ACT_STORE_LIMIT_DAILY, storeDay, activityId, request.getOrderId());
        OfflineActStoreLimitDailyProvider storeDayResourceProvider = (OfflineActStoreLimitDailyProvider) resourceProviderFactory.getProvider(ResourceType.OFFLINE_ACT_STORE_LIMIT_DAILY);
        storeDayResourceProvider.initResource(storeDayResourceObject);
        context.addProvider(storeDayResourceProvider);

        // 全国所有门店
        OfflineActStoreLimitProvider.ActStoreLimit store = buildActStoreLimit(activityId, request.getOrgCode(), joinCounts, limitRule.getActivityLimitOne());
        ResourceObject<OfflineActStoreLimitProvider.ActStoreLimit> storeResourceObject = buildLimitResource(ResourceType.OFFLINE_ACT_STORE_LIMIT, store, activityId, request.getOrderId());
        OfflineActStoreLimitProvider storeResourceProvider = (OfflineActStoreLimitProvider) resourceProviderFactory.getProvider(ResourceType.OFFLINE_ACT_STORE_LIMIT);
        storeResourceProvider.initResource(storeResourceObject);
        context.addProvider(storeResourceProvider);
    }

    /**
     * 初始化直降活动限购资源
     *
     * @param request    请求对象
     * @param activityId 活动ID
     * @param skuPackage sku/package
     * @param joinCounts 参与活动ID
     * @param context    上下文
     * @param limitRule  活动限制
     * @throws BizError 业务异常
     */
    protected void initOnsaleResource(CheckoutPromotionRequest request, Long activityId, String skuPackage, Integer joinCounts, LocalContext context, ActNumLimitRule limitRule) throws BizError {
        if (StringUtils.isEmpty(request.getOrgCode())) {
            return;
        }
        String uType = request.getUidType();
        Long userId = request.getUserId();
        // 每人
        if (StringUtils.isNotEmpty(uType)) {
            OnsaleActMobileUserLimitProvider.ActUserLimit personDayLimit =
                    buildActMobileUserLimit(activityId, userId, skuPackage, joinCounts, limitRule.getPersonLimitDay());
            ResourceObject<OnsaleActMobileUserLimitProvider.ActUserLimit> resourceDayObject = buildLimitResource(ResourceType.ONSALE_ACT_MOBILE_USER_LIMIT, personDayLimit, activityId, request.getOrderId());
            OnsaleActMobileUserLimitProvider resourcePersonDayProvider = (OnsaleActMobileUserLimitProvider) resourceProviderFactory.getProvider(ResourceType.ONSALE_ACT_MOBILE_USER_LIMIT);
            resourcePersonDayProvider.initResource(resourceDayObject);
            context.addProvider(resourcePersonDayProvider);
        } else {
            OnsaleActUserLimitProvider.ActUserLimit personDayLimit = buildActUserLimit(activityId, userId, skuPackage, joinCounts, limitRule.getPersonLimitDay());
            ResourceObject<OnsaleActUserLimitProvider.ActUserLimit> resourceDayObject = buildLimitResource(ResourceType.ONSALE_ACT_USER_LIMIT, personDayLimit, activityId, request.getOrderId());
            OnsaleActUserLimitProvider resourcePersonDayProvider = (OnsaleActUserLimitProvider) resourceProviderFactory.getProvider(ResourceType.ONSALE_ACT_USER_LIMIT);
            resourcePersonDayProvider.initResource(resourceDayObject);
            context.addProvider(resourcePersonDayProvider);
        }
        // 每天全国所有门店
        OnsaleActAllStoreLimitDailyProvider.ActAllStoreDayLimit allStoreDay = buildActAllStoreDayLimit(activityId, skuPackage, joinCounts, limitRule.getDayLimitAll());
        ResourceObject<OnsaleActAllStoreLimitDailyProvider.ActAllStoreDayLimit> allStoreDayResourceObject = buildLimitResource(ResourceType.ONSALE_ACT_ALL_STORE_LIMIT_DAILY, allStoreDay, activityId, request.getOrderId());
        OnsaleActAllStoreLimitDailyProvider allStoreDayResourceProvider = (OnsaleActAllStoreLimitDailyProvider) resourceProviderFactory.getProvider(ResourceType.ONSALE_ACT_ALL_STORE_LIMIT_DAILY);
        allStoreDayResourceProvider.initResource(allStoreDayResourceObject);
        context.addProvider(allStoreDayResourceProvider);

        // 全国所有门店
        OnsaleActAllStoreLimitProvider.ActAllStoreLimit allStore = buildActAllStoreLimit(activityId, skuPackage, joinCounts, limitRule.getActivityLimitAll());
        ResourceObject<OnsaleActAllStoreLimitProvider.ActAllStoreLimit> allStoreResourceObject = buildLimitResource(ResourceType.ONSALE_ACT_ALL_STORE_LIMIT, allStore, activityId, request.getOrderId());
        OnsaleActAllStoreLimitProvider allStoreResourceProvider = (OnsaleActAllStoreLimitProvider) resourceProviderFactory.getProvider(ResourceType.ONSALE_ACT_ALL_STORE_LIMIT);
        allStoreResourceProvider.initResource(allStoreResourceObject);
        context.addProvider(allStoreResourceProvider);

        // 每个门店每天
        OnsaleActStoreLimitDailyProvider.ActStoreDayLimit storeDay = buildActStoreDayLimit(activityId, request.getOrgCode(), skuPackage, joinCounts, limitRule.getDayLimitOne());
        ResourceObject<OnsaleActStoreLimitDailyProvider.ActStoreDayLimit> storeDayResourceObject = buildLimitResource(ResourceType.ONSALE_ACT_STORE_LIMIT_DAILY, storeDay, activityId, request.getOrderId());
        OnsaleActStoreLimitDailyProvider storeDayResourceProvider = (OnsaleActStoreLimitDailyProvider) resourceProviderFactory.getProvider(ResourceType.ONSALE_ACT_STORE_LIMIT_DAILY);
        storeDayResourceProvider.initResource(storeDayResourceObject);
        context.addProvider(storeDayResourceProvider);

        // 全国所有门店
        OnsaleActStoreLimitProvider.ActStoreLimit store = buildActStoreLimit(activityId, request.getOrgCode(), skuPackage, joinCounts, limitRule.getDayLimitOne());
        ResourceObject<OnsaleActStoreLimitProvider.ActStoreLimit> storeResourceObject = buildLimitResource(ResourceType.ONSALE_ACT_STORE_LIMIT, store, activityId, request.getOrderId());
        OnsaleActStoreLimitProvider storeResourceProvider = (OnsaleActStoreLimitProvider) resourceProviderFactory.getProvider(ResourceType.ONSALE_ACT_STORE_LIMIT);
        storeResourceProvider.initResource(storeResourceObject);
        context.addProvider(storeResourceProvider);
    }

    /**
     * 初始化换新立减
     *
     * @param request        请求对象
     * @param activityId     活动ID
     * @param skuPackageList sku/package
     * @param context        上下文
     * @param renewLevelKey
     * @throws BizError 业务异常
     */
    protected void initRenewResourceResource(CheckoutPromotionRequest request, Long activityId, List<String> skuPackageList, LocalContext context, String renewLevelKey) throws BizError {
        RenewReduceResourceProvider.ResContent resContent = new RenewReduceResourceProvider.ResContent();
        resContent.setSkuPackageList(skuPackageList);
        resContent.setRenewLevelKey(renewLevelKey);
        ResourceObject<RenewReduceResourceProvider.ResContent> resContentResourceObject = buildLimitResource(ResourceType.RENEW_REDUCE_ACT_LIMIT, resContent, activityId, request.getOrderId());
        RenewReduceResourceProvider renewReduceResourceProvider = (RenewReduceResourceProvider) resourceProviderFactory.getProvider(ResourceType.RENEW_REDUCE_ACT_LIMIT);
        renewReduceResourceProvider.initResource(resContentResourceObject);
        context.addProvider(renewReduceResourceProvider);
    }

    /**
     * 初始化立减
     *
     * @param request        请求对象
     * @param activityId     活动ID
     * @param skuPackageList sku/package
     * @param context        上下文
     * @throws BizError 业务异常
     */
    protected void initReduceResourceResource(CheckoutPromotionRequest request, Long activityId, List<String> skuPackageList, LocalContext context) throws BizError {
        GoodsLimitResourceProvider.ResContent resContent = new GoodsLimitResourceProvider.ResContent();
        resContent.setSkuPackageList(skuPackageList);
        ResourceObject<GoodsLimitResourceProvider.ResContent> resContentResourceObject = buildLimitResource(ResourceType.GOODS_ACT_LIMIT, resContent, activityId, request.getOrderId());
        resContentResourceObject.setReturnStatus(ReturnStatus.CANCEL_RETURN.getValue());
        GoodsLimitResourceProvider reduceResourceProvider = (GoodsLimitResourceProvider) resourceProviderFactory.getProvider(ResourceType.GOODS_ACT_LIMIT);
        reduceResourceProvider.initResource(resContentResourceObject);
        context.addProvider(reduceResourceProvider);

        long expireTime = -1L;
        OnlineActUserRecordTotalProvider.ActUserRecord recordTotal = buildOnlineActUserRecord(activityId, request.getUserId(), expireTime);
        ResourceObject<OnlineActUserRecordTotalProvider.ActUserRecord> resourceObject = buildLimitResource(ResourceType.ONLINE_ACT_RECORD, recordTotal, activityId, request.getOrderId());
        OnlineActUserRecordTotalProvider resourceProvider = (OnlineActUserRecordTotalProvider) resourceProviderFactory.getProvider(ResourceType.ONLINE_ACT_RECORD);
        resourceProvider.initResource(resourceObject);
        context.addProvider(resourceProvider);
    }

    /**
     * 初始化指定门店降价
     *
     * @param request        请求对象
     * @param activityId     活动ID
     * @param orgCode        门店ID
     * @param actSumReduceCount 每人每个门店活动总扣除数量
     * @param skuPackageList sku/package
     * @param context        上下文
     * @throws BizError 业务异常
     */
    protected void initPartOnsaleResource(CheckoutPromotionRequest request, Long activityId, String orgCode, int actSumReduceCount, Map<String, ActGoodsStoreLimitResourceProvider.ResContentItem> skuPackageList, LocalContext context) throws BizError {
        long personLimitOne = (context.getPromotion() != null && context.getPromotion().getNumLimitRule() != null &&
                context.getPromotion().getNumLimitRule().getPersonLimitOne()!=null) ? context.getPromotion().getNumLimitRule().getPersonLimitOne() : 0L;

        //指定门店降价的每人每个门店活动数量
        ActUserStoreLimitProvider.ActUserLimit actUserLimit = new ActUserStoreLimitProvider.ActUserLimit();
        actUserLimit.setActId(activityId);
        actUserLimit.setUid(request.getUserId());
        actUserLimit.setUType(request.getUidType());
        actUserLimit.setOrgCode(request.getOrgCode());
        actUserLimit.setCount(actSumReduceCount);
        actUserLimit.setLimitNum(personLimitOne);
        ResourceObject<ActUserStoreLimitProvider.ActUserLimit> contentResourceObject = buildLimitResource(ResourceType.PARTONSALE_USER_REDUCE_ACT_LIMIT, actUserLimit, activityId, request.getOrderId());
        contentResourceObject.setReturnStatus(ReturnStatus.CANCEL_RETURN.getValue());
        ActUserStoreLimitProvider actUserStoreLimitProvider = (ActUserStoreLimitProvider) resourceProviderFactory.getProvider(ResourceType.PARTONSALE_USER_REDUCE_ACT_LIMIT);
        actUserStoreLimitProvider.initResource(contentResourceObject);
        context.addProvider(actUserStoreLimitProvider);

        //指定门店降价的sku/package参与次数
        ActGoodsStoreLimitResourceProvider.ResContent resContent = new ActGoodsStoreLimitResourceProvider.ResContent();
        resContent.setOrgCode(orgCode);
        resContent.setSkuPackageList(skuPackageList);
        ResourceObject<ActGoodsStoreLimitResourceProvider.ResContent> resContentResourceObject = buildLimitResource(ResourceType.PARTONSALE_GOODS_REDUCE_ACT_LIMIT, resContent, activityId, request.getOrderId());
        resContentResourceObject.setReturnStatus(ReturnStatus.CANCEL_RETURN.getValue());
        ActGoodsStoreLimitResourceProvider actGoodsStoreLimitResourceProvider = (ActGoodsStoreLimitResourceProvider) resourceProviderFactory.getProvider(ResourceType.PARTONSALE_GOODS_REDUCE_ACT_LIMIT);
        actGoodsStoreLimitResourceProvider.initResource(resContentResourceObject);
        context.addProvider(actGoodsStoreLimitResourceProvider);
    }

    /**
     * 扣减库存
     *
     * @param actId    活动ID
     * @param count    扣减数量
     * @param limitNum 限制数
     * @return 资源对象数据
     */
    private OnlineActLimitProvider.ActOnlineLimit buildOnlineActLimit(Long actId, Integer count, Long limitNum) {
        OnlineActLimitProvider.ActOnlineLimit onlineLimit = new OnlineActLimitProvider.ActOnlineLimit();
        onlineLimit.setActId(actId);
        onlineLimit.setCount(count);
        onlineLimit.setLimitNum(limitNum);
        return onlineLimit;
    }

    /**
     * 参与活动记录用户每天
     *
     * @param actId      活动ID
     * @param uid        用户ID
     * @param expireTime 过期时间
     * @return 资源对象数据
     */
    private OnlineActUserRecordDailyProvider.ActUserRecordDaily buildOnlineActUserRecordDaily(Long actId, Long uid, Long expireTime) {
        OnlineActUserRecordDailyProvider.ActUserRecordDaily userRecordDaily = new OnlineActUserRecordDailyProvider.ActUserRecordDaily();
        userRecordDaily.setActId(actId);
        userRecordDaily.setUid(uid);
        userRecordDaily.setDateTimeMills(Instant.now().toEpochMilli());
        userRecordDaily.setExpireTime(expireTime);
        return userRecordDaily;
    }

    /**
     * 参与活动记录用户
     *
     * @param actId      活动ID
     * @param uid        用户ID
     * @param expireTime 过期时间
     * @return 资源对象数据
     */
    private OnlineActUserRecordTotalProvider.ActUserRecord buildOnlineActUserRecord(Long actId, Long uid, Long expireTime) {
        OnlineActUserRecordTotalProvider.ActUserRecord userRecord = new OnlineActUserRecordTotalProvider.ActUserRecord();
        userRecord.setActId(actId);
        userRecord.setUid(uid);
        userRecord.setExpireTime(expireTime);
        return userRecord;
    }

    /**
     * 扣减库存-全部门店每天
     *
     * @param actId    活动ID
     * @param count    扣减数量
     * @param limitNum 限制数
     * @return 资源对象数据
     */
    private OfflineActAllStoreLimitDailyProvider.ActAllStoreDayLimit buildActAllStoreLimitDaily(Long actId, Integer count, Long limitNum) {
        OfflineActAllStoreLimitDailyProvider.ActAllStoreDayLimit actLimit = new OfflineActAllStoreLimitDailyProvider.ActAllStoreDayLimit();
        actLimit.setActId(actId);
        actLimit.setCount(count);
        actLimit.setDateTimeMills(Instant.now().toEpochMilli());
        actLimit.setLimitNum(limitNum);
        return actLimit;
    }

    /**
     * 扣减库存-全部门店
     *
     * @param actId    活动ID
     * @param count    扣减数量
     * @param limitNum 限制数
     * @return 资源对象数据
     */
    private OfflineActAllStoreLimitProvider.ActAllStoreLimit buildActAllStoreLimit(Long actId, Integer count, Long limitNum) {
        OfflineActAllStoreLimitProvider.ActAllStoreLimit actLimit = new OfflineActAllStoreLimitProvider.ActAllStoreLimit();
        actLimit.setActId(actId);
        actLimit.setCount(count);
        actLimit.setLimitNum(limitNum);
        return actLimit;
    }

    /**
     * 扣减库存-每个门店每天
     *
     * @param actId    活动ID
     * @param orgCode  门店Code
     * @param count    扣减数量
     * @param limitNum 限制数
     * @return 资源对象数据
     */
    private OfflineActStoreLimitDailyProvider.ActStoreDayLimit buildActStoreLimitDaily(Long actId, String orgCode, Integer count, Long limitNum) {
        OfflineActStoreLimitDailyProvider.ActStoreDayLimit actLimit = new OfflineActStoreLimitDailyProvider.ActStoreDayLimit();
        actLimit.setActId(actId);
        actLimit.setOrgCode(orgCode);
        actLimit.setCount(count);
        actLimit.setDateTimeMills(Instant.now().toEpochMilli());
        actLimit.setLimitNum(limitNum);
        return actLimit;
    }

    /**
     * 扣减库存-每个门店
     *
     * @param actId    活动ID
     * @param orgCode  门店Code
     * @param count    扣减数量
     * @param limitNum 限制数
     * @return 资源对象数据
     */
    private OfflineActStoreLimitProvider.ActStoreLimit buildActStoreLimit(Long actId, String orgCode, Integer count, Long limitNum) {
        OfflineActStoreLimitProvider.ActStoreLimit actLimit = new OfflineActStoreLimitProvider.ActStoreLimit();
        actLimit.setActId(actId);
        actLimit.setOrgCode(orgCode);
        actLimit.setCount(count);
        actLimit.setLimitNum(limitNum);
        return actLimit;
    }

    /**
     * 扣减库存-每人每天
     *
     * @param actId    活动ID
     * @param uid      用户ID
     * @param count    扣减数量
     * @param limitNum 限制数
     * @return 资源对象数据
     */
    private OfflineActPersonLimitDailyProvider.ActPersonDayLimit buildActPersonLimitDaily(Long actId, Long uid, Integer count, Long limitNum) {
        OfflineActPersonLimitDailyProvider.ActPersonDayLimit actLimit = new OfflineActPersonLimitDailyProvider.ActPersonDayLimit();
        actLimit.setActId(actId);
        actLimit.setUid(uid);
        actLimit.setCount(count);
        actLimit.setDateTimeMills(Instant.now().toEpochMilli());
        actLimit.setLimitNum(limitNum);
        return actLimit;
    }

    /**
     * 扣减库存-每人
     *
     * @param actId    活动ID
     * @param uid      用户ID
     * @param count    扣减数量
     * @param limitNum 限制数
     * @return 资源对象数据
     */
    private OfflineActPersonLimitProvider.ActPersonLimit buildActPersonLimit(Long actId, Long uid, Integer count, Long limitNum) {
        OfflineActPersonLimitProvider.ActPersonLimit actLimit = new OfflineActPersonLimitProvider.ActPersonLimit();
        actLimit.setActId(actId);
        actLimit.setUid(uid);
        actLimit.setCount(count);
        actLimit.setLimitNum(limitNum);
        return actLimit;
    }

    /**
     * 扣减库存-类型用户每人每天
     *
     * @param actId    活动ID
     * @param uType    用户类型
     * @param uid      用户ID
     * @param count    扣减数量
     * @param limitNum 限制数
     * @return 资源对象数据
     */
    private OfflineActUtypePersonLimitDailyProvider.ActUtypePersonDayLimit buildActUTypePersonLimitDaily(Long actId, String uType, Long uid, Integer count, Long limitNum) {
        OfflineActUtypePersonLimitDailyProvider.ActUtypePersonDayLimit actLimit = new OfflineActUtypePersonLimitDailyProvider.ActUtypePersonDayLimit();
        actLimit.setActId(actId);
        actLimit.setUType(uType);
        actLimit.setUid(uid);
        actLimit.setCount(count);
        actLimit.setDateTimeMills(Instant.now().toEpochMilli());
        actLimit.setLimitNum(limitNum);
        return actLimit;
    }

    /**
     * 扣减库存-类型用户每人
     *
     * @param actId    活动ID
     * @param uType    用户类型
     * @param uid      用户ID
     * @param count    扣减数量
     * @param limitNum 限制数
     * @return 资源对象数据
     */
    private OfflineActUtypePersonLimitProvider.ActUtypePersonLimit buildActUTypePersonLimit(Long actId, String uType, Long uid, Integer count, Long limitNum) {
        OfflineActUtypePersonLimitProvider.ActUtypePersonLimit actLimit = new OfflineActUtypePersonLimitProvider.ActUtypePersonLimit();
        actLimit.setActId(actId);
        actLimit.setUType(uType);
        actLimit.setUid(uid);
        actLimit.setCount(count);
        actLimit.setLimitNum(limitNum);
        return actLimit;
    }

    /**
     * 直降所有门店每天参与活动次数
     *
     * @param actId      活动ID
     * @param skuPackage sku / package
     * @param count      扣减数量
     * @param limitNum   限制数
     * @return 资源对象数据
     */
    private OnsaleActAllStoreLimitDailyProvider.ActAllStoreDayLimit buildActAllStoreDayLimit(Long actId, String skuPackage, Integer count, Long limitNum) {
        OnsaleActAllStoreLimitDailyProvider.ActAllStoreDayLimit actLimit = new OnsaleActAllStoreLimitDailyProvider.ActAllStoreDayLimit();
        actLimit.setActId(actId);
        actLimit.setDateTimeMills(Instant.now().toEpochMilli());
        actLimit.setSkuPackage(skuPackage);
        actLimit.setCount(count);
        actLimit.setLimitNum(limitNum);
        return actLimit;
    }

    /**
     * 直降所有门店参与活动次数
     *
     * @param actId      活动ID
     * @param skuPackage sku / package
     * @param count      扣减数量
     * @param limitNum   限制数
     * @return 资源对象数据
     */
    private OnsaleActAllStoreLimitProvider.ActAllStoreLimit buildActAllStoreLimit(Long actId, String skuPackage, Integer count, Long limitNum) {
        OnsaleActAllStoreLimitProvider.ActAllStoreLimit actLimit = new OnsaleActAllStoreLimitProvider.ActAllStoreLimit();
        actLimit.setActId(actId);
        actLimit.setSkuPackage(skuPackage);
        actLimit.setCount(count);
        actLimit.setLimitNum(limitNum);
        return actLimit;
    }

    /**
     * 直降所有门店参与活动次数
     *
     * @param actId      活动ID
     * @param uid        用户ID
     * @param skuPackage sku / package
     * @param count      扣减数量
     * @param limitNum   限制数
     * @return 资源对象数据
     */
    private OnsaleActMobileUserLimitProvider.ActUserLimit buildActMobileUserLimit(Long actId, Long uid, String skuPackage, Integer count, Long limitNum) {
        OnsaleActMobileUserLimitProvider.ActUserLimit actLimit = new OnsaleActMobileUserLimitProvider.ActUserLimit();
        actLimit.setActId(actId);
        actLimit.setUid(uid);
        actLimit.setSkuPackage(skuPackage);
        actLimit.setCount(count);
        actLimit.setLimitNum(limitNum);
        return actLimit;
    }

    /**
     * 直降每个门店每天参与活动次数
     *
     * @param actId      活动ID
     * @param orgCode    门店Code
     * @param skuPackage sku / package
     * @param count      扣减数量
     * @param limitNum   限制数
     * @return 资源对象数据
     */
    private OnsaleActStoreLimitDailyProvider.ActStoreDayLimit buildActStoreDayLimit(Long actId, String orgCode, String skuPackage, Integer count, Long limitNum) {
        OnsaleActStoreLimitDailyProvider.ActStoreDayLimit actLimit = new OnsaleActStoreLimitDailyProvider.ActStoreDayLimit();
        actLimit.setActId(actId);
        actLimit.setOrgCode(orgCode);
        actLimit.setDateTimeMills(Instant.now().toEpochMilli());
        actLimit.setSkuPackage(skuPackage);
        actLimit.setCount(count);
        actLimit.setLimitNum(limitNum);
        return actLimit;
    }

    /**
     * 直降每个门店参与活动次数
     *
     * @param actId      活动ID
     * @param orgCode    门店Code
     * @param skuPackage sku / package
     * @param count      扣减数量
     * @param limitNum   限制数
     * @return 资源对象数据
     */
    private OnsaleActStoreLimitProvider.ActStoreLimit buildActStoreLimit(Long actId, String orgCode, String skuPackage, Integer count, Long limitNum) {
        OnsaleActStoreLimitProvider.ActStoreLimit actLimit = new OnsaleActStoreLimitProvider.ActStoreLimit();
        actLimit.setActId(actId);
        actLimit.setOrgCode(orgCode);
        actLimit.setSkuPackage(skuPackage);
        actLimit.setCount(count);
        actLimit.setLimitNum(limitNum);
        return actLimit;
    }

    /**
     * 直降活动mid每人参与次数
     *
     * @param actId      活动ID
     * @param uid        用户ID
     * @param skuPackage sku / package
     * @param count      扣减数量
     * @param limitNum   限制数
     * @return 资源对象数据
     */
    private OnsaleActUserLimitProvider.ActUserLimit buildActUserLimit(Long actId, Long uid, String skuPackage, Integer count, Long limitNum) {
        OnsaleActUserLimitProvider.ActUserLimit actLimit = new OnsaleActUserLimitProvider.ActUserLimit();
        actLimit.setActId(actId);
        actLimit.setUid(uid);
        actLimit.setSkuPackage(skuPackage);
        actLimit.setCount(count);
        actLimit.setLimitNum(limitNum);
        return actLimit;
    }

/*    protected void initSubsidyUserLimitProvider(CheckoutPromotionRequest request, Long promotionId, LocalContext context, List<ActUserLimitPo> actUserLimitPoList) throws BizError {
        SubsidyUserLimitProvider.ResContent resContent=new SubsidyUserLimitProvider.ResContent();
        resContent.setUid(request.getUserId());
        resContent.setIdCard(request.getIdCard());
        resContent.setActUserLimitPoList(actUserLimitPoList);
        ResourceObject<SubsidyUserLimitProvider.ResContent> resourceObject = buildLimitResource(ResourceType.PURCHASE_SUBSIDY_ACT_LIMIT, resContent, promotionId, request.getOrderId());
        SubsidyUserLimitProvider resourceProvider = (SubsidyUserLimitProvider) resourceProviderFactory.getProvider(ResourceType.PURCHASE_SUBSIDY_ACT_LIMIT);
        resourceProvider.initResource(resourceObject);
        context.addProvider(resourceProvider);
    }*/



    /**
     * 扣减三方优惠资格码资源对象
     *
     * @param request    资源类型
     * @param promotionId      资源对象内容
     * @param context      订单ID
     * @param <T>          资源对象内容类型
     * @return 资源对象
     */
	protected void initializePurchaseSubsidyLimit(CheckoutPromotionRequest request, Long promotionId, LocalContext context, List<TradeInQualifyInfo> tradeInQualifyInfoList) throws BizError {
        SubsidyPhoenixProvider.ResContent resContent=new SubsidyPhoenixProvider.ResContent();
        resContent.setOrderId(String.valueOf(request.getOrderId()));
        resContent.setUserId(request.getUserId());
        resContent.setQualifyDetailReqList(SubsidyPhoenixConvert.toTradeInQualifyDetailReq(tradeInQualifyInfoList));
        ResourceObject<SubsidyPhoenixProvider.ResContent> resourceObject = buildLimitResource(ResourceType.PURCHASE_SUBSIDY_ACT_LIMIT, resContent, promotionId, request.getOrderId());
        SubsidyPhoenixProvider resourceProvider = (SubsidyPhoenixProvider) resourceProviderFactory.getProvider(ResourceType.PURCHASE_SUBSIDY_ACT_LIMIT);
        resourceProvider.initResource(resourceObject);
        context.addProvider(resourceProvider);
    }

    /**
     * 政府资源初始化
     * @param request    资源类型
     * @param promotionId      资源对象内容
     * @param context      订单ID
     * @param <T>          资源对象内容类型
     */
    protected void initializeGovernmentSubsidyLimit(CheckoutPromotionRequest request, Long promotionId, LocalContext context, String cateCode, Integer reportCity, String orderInfo) throws BizError {
        GovernmentSubsidyPhoenixProvider.ResContent resContent = new GovernmentSubsidyPhoenixProvider.ResContent();
        resContent.setOrderId(request.getOrderId());
        resContent.setUserId(request.getUserId());
        resContent.setCateCode(cateCode);
        resContent.setMid(request.getUserId());
        resContent.setRegionId(reportCity);
        resContent.setOrderInfo(orderInfo);
        ResourceObject<GovernmentSubsidyPhoenixProvider.ResContent> resourceObject = buildLimitResource(ResourceType.GOVERNMENT_SUBSIDY_ACT_LIMIT, resContent, promotionId, request.getOrderId());
        GovernmentSubsidyPhoenixProvider resourceProvider = (GovernmentSubsidyPhoenixProvider) resourceProviderFactory.getProvider(ResourceType.GOVERNMENT_SUBSIDY_ACT_LIMIT);
        resourceProvider.initResource(resourceObject);
        context.addProvider(resourceProvider);
    }



    /**
     * 扣减库存资源对象
     *
     * @param resourceType 资源类型
     * @param content      资源对象内容
     * @param orderId      订单ID
     * @param <T>          资源对象内容类型
     * @return 资源对象
     */
    private <T> ResourceObject<T> buildLimitResource(ResourceType resourceType, T content, Long activityId, Long orderId) {
        ResourceObject<T> resourceObject = new ResourceObject<>();
        resourceObject.setContent(content);
        resourceObject.setResourceId(UUID.randomUUID().toString());
        resourceObject.setResourceType(resourceType);
        resourceObject.setPromotionId(activityId);
        resourceObject.setOrderId(orderId);
        resourceObject.setPid(-1L);
        return resourceObject;
    }
}



