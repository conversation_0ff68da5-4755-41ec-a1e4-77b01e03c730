package com.xiaomi.nr.promotion.domain.coupon.facade;

import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Request;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.service.base.processor.CouponProcessorInterface;
import com.xiaomi.nr.promotion.engine.CouponTool;
import com.xiaomi.nr.promotion.entity.redis.OrgInfo;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.CouponGeneralType;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import com.xiaomi.nr.promotion.enums.PostFreeEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.domain.coupon.model.DeduceIndex;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.common.BasicInfo;
import com.xiaomi.nr.promotion.resource.external.OrgInfoExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.resource.provider.CouponProvider;
import com.xiaomi.nr.promotion.domain.coupon.service.common.CouponInfoService;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.CouponHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.NumberUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xiaomi.nr.promotion.constant.CouponConstant.TYPE_DEDUCT_PENNY;
import static com.xiaomi.nr.promotion.error.ClientSideErr.COMMON_SYSTEM_ERR_MSG;

@Slf4j
@Component
public class CouponFacade implements CouponFacadeInterFace {

    @Autowired
    private List<CouponProcessorInterface> processorList;

    @Autowired
    @Qualifier("couponInfoServiceRemoteImpl")
    private CouponInfoService couponInfoService;


    /**
     * 券下单结算逻辑，按要求结算指定优惠券
     * <p>
     * - 没有用券，直接返回。如果是结算提交则需要 返回部分信息
     * - 获取用户券列表
     * - 校验优惠券数量
     * - 按类型分组
     * - 转换为优惠工具，处理得到优惠结果
     * - 处理资源，包装响应
     *
     * @param request  业务请求
     * @param response 业务响应
     * @param context  计算上下文
     * @throws BizError 业务异常
     */
    public void checkoutForSubmit(CheckoutPromotionRequest request, CheckoutPromotionResponse response, CheckoutContext context) throws Exception {

        //限制结算业务
        if (context.getBizPlatform() != BizPlatformEnum.NEW_RETAIL) {
            processEmptyCoupon(request, context);
            return;
        }
        if (CollectionUtils.isEmpty(request.getCouponIds()) && CollectionUtils.isEmpty(request.getCouponCodes())) {
            processEmptyCoupon(request, context);
            return;
        }

        if (CollectionUtils.isNotEmpty(request.getCouponIds()) && CollectionUtils.isNotEmpty(request.getCouponCodes())) {
            throw ExceptionHelper.create(ErrCode.ERR_COUPON_MONEY_NUM, "无效的结算类型");
        }
        OrgInfo orgInfo = new OrgInfo();
        if (StringUtils.isNotEmpty(request.getOrgCode())) {
            OrgInfoExternalProvider externalDataProvider = (OrgInfoExternalProvider) context.getExternalDataMap().get(ResourceExtType.ORG_INFO);
            orgInfo = externalDataProvider.getFuture().get(2000, TimeUnit.MILLISECONDS);
        }

        ListenableFuture<List<CheckoutCoupon>> couponForCheckout = couponInfoService.getCouponForCheckout(request, orgInfo.getOrgType(), false);
        List<CheckoutCoupon> checkoutCouponList = couponForCheckout.get(2000, TimeUnit.MILLISECONDS);

        //校验优惠券数量
        checkCouponCount(request, checkoutCouponList);
        try {

            //按类型分组
            Map<Integer, List<CheckoutCoupon>> couponGroupMap = checkoutCouponList.stream()
                    .collect(Collectors.groupingBy(coupon -> coupon.getCouponType(),
                            () -> new TreeMap<>(Comparator.comparingInt(o -> CouponGeneralType.valueOf(o).getPriority())
                            ),
                            Collectors.toList()));

            log.info("checkoutCoupons couponGroupMap:{}", GsonUtil.toJson(couponGroupMap));


            for (Map.Entry<Integer, List<CheckoutCoupon>> entry : couponGroupMap.entrySet()) {
                CouponProcessorInterface couponProcessor = processorList.stream()
                        .filter(processor -> processor.getGeneralType().getCode() == entry.getKey())
                        .findFirst()
                        .orElse(null);
                if (couponProcessor == null) {
                    throw ExceptionHelper.create(ErrCode.ERR_EMPTY_COUPONS, "无效的优惠券类型");
                }
                List<CheckoutCoupon> coupons = entry.getValue();
                if (coupons.size() > 1) {
                    throw ExceptionHelper.create(ErrCode.ERR_TOO_MANY_COUPONS, "同种类型最多只能使用一张优惠券");
                }
                CheckoutCoupon checkoutCoupon = coupons.get(0);
                if (checkoutCoupon == null) {
                    throw ExceptionHelper.create(ErrCode.ERR_EMPTY_COUPONS, "无效的优惠券类型");
                }
                // 转换为优惠工具，处理得到优惠结果
                List<CouponTool> couponTools = couponProcessor.loadCouponToolsForCheckout(coupons, context.getBizPlatform());

                Map<Long, CouponTool> couponToolMap = couponTools.stream().collect(Collectors.toMap(CouponTool::getCouponId, Function.identity()));
                //log.info("checkoutCoupons tools:{}", GsonUtil.toJson(couponTools));
                CouponTool selectCouponTool = couponToolMap.get(checkoutCoupon.getCouponId());
                CouponCheckoutResult result = checkoutSelectCoupon(request, context, selectCouponTool, couponProcessor, orgInfo);

                //生成context.carts
                if (request.getSourceApi() == SourceApi.SUBMIT) {
                    if (selectCouponTool.getCouponType() == CouponTypeEnum.DEDUCT) {
                        String couponId = String.valueOf(result.getCouponId());
                        List<CartItem> copyCartList = copyList(request.getCartList());
                        preDivideItemDeductCoupon(copyCartList, result.getDeductedInfo().getIndex(), couponId, 1);
                        context.setCarts(copyCartList);
                    } else {
                        selectCouponTool.updateItemReduce(context.getCarts(), result);
                    }
                }
            }
            if (context.getCarts() == null) {
                List<CartItem> simpleCarts = copyList(request.getCartList());
                context.setCarts(simpleCarts);
            }
        } catch (Exception e) {
            log.error("checkoutCouponsForSubmit Error. request:{} ", GsonUtil.toJson(request), e);
            if (e instanceof BizError) {
                context.setCouponBaseInfoInvalid(((BizError) e).getMsg());
            }
            if (request.getSourceApi() == SourceApi.SUBMIT) {
                log.error("coupon submit error. request:{}", request, e);
                throw e;
            }
            processEmptyCoupon(request, context);
        }

    }

    public void checkoutForCouponList(CheckoutPromotionV2Request request, CheckoutPromotionResponse response, CheckoutContext context) throws BizError {
        try {



            // 没传用户ID或按渠道结算暂时不处理券
            if (request.getUserId() == null || request.getUserId() == 0L) {
                return;
            }

            //限制结算业务
            if (context.getBizPlatform() != BizPlatformEnum.NEW_RETAIL && context.getBizPlatform() != BizPlatformEnum.CAR) {
                return;
            }

            OrgInfo orgInfo = new OrgInfo();
            if (StringUtils.isNotEmpty(request.getOrgCode())) {

                OrgInfoExternalProvider externalDataProvider = (OrgInfoExternalProvider) context.getExternalDataMap().get(ResourceExtType.ORG_INFO);
                orgInfo = externalDataProvider.getFuture().get(2000, TimeUnit.MILLISECONDS);
            }

            ListenableFuture<List<CheckoutCoupon>> couponForCheckout = couponInfoService.getCouponForCheckout(request, orgInfo.getOrgType(), request.getGetCouponList());
            List<CheckoutCoupon> checkoutCouponList = couponForCheckout.get(2000, TimeUnit.MILLISECONDS);


            //先计算最优券
            List<Long> bestCouponList = getBestCoupon(request, context, checkoutCouponList);

            //计算券列表 && 分摊
            List<Coupon> finalCouponList = getCouponList(request, context, checkoutCouponList);

            //最终排序
            finalCouponList.sort(Comparator.comparingInt(o -> CouponGeneralType.valueOf(o.getCouponType()).getSortValue()));
            finalCouponList.forEach(coupon -> {
                if (bestCouponList.contains(coupon.getId())) {
                    coupon.setTags(Arrays.asList("optimal"));
                }
            });
            context.setCouponList(finalCouponList);
        } catch (Exception e) {
            log.error("CouponServiceV2 checkout error. request:{}", GsonUtil.toJson(request), e);
        }


    }

    public void checkoutForProtectPrice(CheckoutPromotionRequest request, CheckoutPromotionResponse response, CheckoutContext context)
            throws Exception {

        if (CollectionUtils.isEmpty(request.getCouponCodes()) && CollectionUtils.isEmpty(request.getCouponIds())) {
            processEmptyCoupon(request, context);
            return;
        }
        try {
            //取券

            OrgInfo orgInfo = new OrgInfo();
            if (StringUtils.isNotEmpty(request.getOrgCode())) {
                OrgInfoExternalProvider externalDataProvider = (OrgInfoExternalProvider) context.getExternalDataMap().get(ResourceExtType.ORG_INFO);
                orgInfo = externalDataProvider.getFuture().get(2000, TimeUnit.MILLISECONDS);
            }

            ListenableFuture<List<CheckoutCoupon>> couponForCheckout = couponInfoService.getCouponForProtectPrice(request, orgInfo.getOrgType(), false);
            List<CheckoutCoupon> checkoutCouponList = couponForCheckout.get(2000, TimeUnit.MILLISECONDS);

            //按类型分组
            Map<Integer, List<CheckoutCoupon>> couponGroupMap = checkoutCouponList.stream()
                    .collect(Collectors.groupingBy(coupon -> coupon.getCouponType(),
                            () -> new TreeMap<>(Comparator.comparingInt(o -> CouponGeneralType.valueOf(o).getPriority())
                            ),
                            Collectors.toList()));


            for (Map.Entry<Integer, List<CheckoutCoupon>> entry : couponGroupMap.entrySet()) {
                CouponProcessorInterface couponProcessor = processorList.stream()
                        .filter(processor -> processor.getGeneralType().getCode() == entry.getKey())
                        .findFirst()
                        .orElse(null);
                if (couponProcessor == null) {
                    throw ExceptionHelper.create(ErrCode.ERR_EMPTY_COUPONS, "无效的优惠券类型");
                }
                List<CheckoutCoupon> coupons = entry.getValue();
                if (coupons.size() > 1) {
                    throw ExceptionHelper.create(ErrCode.ERR_TOO_MANY_COUPONS, "同种类型最多只能使用一张优惠券");
                }
                CheckoutCoupon checkoutCoupon = coupons.get(0);
                if (checkoutCoupon == null) {
                    throw ExceptionHelper.create(ErrCode.ERR_EMPTY_COUPONS, "无效的优惠券类型");
                }
                // 转换为优惠工具，处理得到优惠结果
                List<CouponTool> couponTools = couponProcessor.loadCouponToolsForCheckout(coupons, context.getBizPlatform());

                Map<Long, CouponTool> couponToolMap = couponTools.stream().collect(Collectors.toMap(CouponTool::getCouponId, Function.identity()));
                //log.info("checkoutCoupons tools:{}", GsonUtil.toJson(couponTools));
                CouponTool selectCouponTool = couponToolMap.get(checkoutCoupon.getCouponId());
                checkoutSelectCoupon(request, context, selectCouponTool, couponProcessor, orgInfo);

            }
            context.setCarts(request.getCartList());


        } catch (Exception e) {
            log.error("checkoutCouponsForProtectPrice Error. request:{} ", GsonUtil.toJson(request), e);
            if (e instanceof BizError) {
                context.setCouponBaseInfoInvalid(((BizError) e).getMsg());
            }
            if (request.getSourceApi() == SourceApi.SUBMIT) {
                log.error("coupon submit error. request:{}", request, e);
                throw e;
            }
        }


    }

    /**
     * 结算指定的优惠券
     * @param request
     * @param context
     * @param selectCouponTool
     * @param couponProcessor
     * @param orgInfo
     * @throws Exception
     */
    protected CouponCheckoutResult checkoutSelectCoupon(CheckoutPromotionRequest request, CheckoutContext context, CouponTool selectCouponTool, CouponProcessorInterface couponProcessor, OrgInfo orgInfo) throws Exception {

        Map<Long, CouponCheckoutResult> checkoutResultMap = new HashMap<>();

        // 构造不可用列表
        List<Coupon> invalidCouponList = new ArrayList<>();

        Map<Long, CouponTool> couponToolMap = new HashMap<>();
        couponToolMap.put(selectCouponTool.getCouponId(), selectCouponTool);

        doCheckout(request, context, checkoutResultMap, couponToolMap, new HashSet<>(), invalidCouponList);
        log.info("checkoutCoupons checkoutResults:{}", GsonUtil.toJson(checkoutResultMap));

        CouponCheckoutResult result = checkoutResultMap.get(selectCouponTool.getCouponId());

        if (result == null) {
            throw ExceptionHelper.create(ErrCode.ERR_EMPTY_COUPONS, "无效的优惠券");
        }

        if (!result.isAllow()) {
            throw ExceptionHelper.create(ErrCode.ERR_COUPON_INVALID, result.getUnusableReason());
        }

        selectCouponTool.updateCartsReduce(request, context, result);
        //初始化资源
        if (request.getSourceApi() == SourceApi.SUBMIT) {

            //如果有扣减为0的券，默认不计算在内
            if (result.getReduceAmount() == 0) {
                Coupon coupon = selectCouponTool.generateCartCoupon();
                if (coupon.getPostFree() != PostFreeEnum.POST_FREE.getVal()) {
                    return result;
                }
            }
            BasicInfo baseInfo = context.getBaseInfo();
            if (baseInfo == null) {
                baseInfo = new BasicInfo(request.getUserId(), request.getOrderId(), request.getClientId());
            }
            context.setBaseInfo(baseInfo);
            CouponInfo couponInfo = selectCouponTool.generateCouponInfo(result);

            if (couponProcessor.getGeneralType() == CouponGeneralType.SHIPMENT) {
                baseInfo.setShipmentCouponInfo(couponInfo);
            }  else if(StringUtils.isNotEmpty(couponInfo.getCouponCode())) {
                couponInfo.setOffline(orgInfo.getType());
                baseInfo.setCodeCouponInfo(couponInfo);
            } else {
                couponInfo.setOffline(orgInfo.getType());
                List<CouponInfo> couponInfoList = baseInfo.getCouponInfoList();
                if (couponInfoList == null) {
                    couponInfoList = new ArrayList<>();
                }
                couponInfoList.add(couponInfo);
                baseInfo.setCouponInfoList(couponInfoList);
            }

            CouponProvider couponProvider = couponProcessor.initResource(request, selectCouponTool, result);
            context.getResourceHandlers().add(couponProvider);
        }
        return result;
    }







    /**
     * 执行结算逻辑：
     * 根据结算结果添加到可用或不可用
     * 更新可用/不可用信息
     *
     * @param request
     * @param context
     * @param checkoutResultMap
     * @param couponToolMap
     * @param validCouponList
     * @param invalidCouponList
     */
    private void doCheckout(CheckoutPromotionRequest request, CheckoutContext context, Map<Long, CouponCheckoutResult> checkoutResultMap, Map<Long, CouponTool> couponToolMap, Set<Coupon> validCouponList, List<Coupon> invalidCouponList) {
        for (CouponTool couponTool : couponToolMap.values()) {
            //构造
            Coupon cartCoupon = couponTool.generateCartCoupon();
            if (cartCoupon.getAllow() == 0) {
                invalidCouponList.add(cartCoupon);
                continue;
            }
            try {
                //得到结算结果
                CouponCheckoutResult couponCheckoutResult = couponTool.checkoutCoupon(request, context);
                checkoutResultMap.put(couponCheckoutResult.getCouponId(), couponCheckoutResult);
                if (couponCheckoutResult.isAllow()) {
                    //更新可用信息
                    cartCoupon.updateValidInfo(couponCheckoutResult.getValidGoodsPrice(), NumberUtil.amountConvertF2Y(couponCheckoutResult.getReduceAmount()), couponCheckoutResult.getReduceAmount());
                    validCouponList.add(cartCoupon);
                } else {
                    //更新不可用信息
                    cartCoupon.updateInvalidInfo(couponCheckoutResult.getUnusableCode(), couponCheckoutResult.getUnusableReason(), couponCheckoutResult.getKeyDataUnusable());
                    invalidCouponList.add(cartCoupon);
                }
            } catch (Exception e) {
                log.error("checkout couponTool error. couponId:{}, type:{}, ex", couponTool.getCouponId(), couponTool.getCouponType(), e);
                if (e instanceof BizError) {
                    cartCoupon.updateInvalidInfo(((BizError) e).getCode(), ((BizError) e).getMsg(), ((BizError) e).getMsg());
                    invalidCouponList.add(cartCoupon);
                }
            }
        }
    }



    protected void processEmptyCoupon(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        doProcessEmptyCoupon(request.getCartList(), request.getUserId(), request.getOrderId(), request.getClientId(), context);
    }

    private void doProcessEmptyCoupon(List<CartItem> cartItemList, Long userId, Long orderId, Long clientId, CheckoutContext context) throws BizError {
        try {
            CartHelper.checkAndStatistics(cartItemList);
        } catch (BizError biz) {
            log.error("param.CartList.CheckAndStatistics err:{}. uid:{}", biz.getMsg(), userId);
            throw ExceptionHelper.create(ErrCode.ERR_CART_CHECK, "校验统计信息错误");
        }
        BasicInfo baseInfoPtr = new BasicInfo(userId, orderId, clientId);
        List<CartItem> simpleCarts = copyList(cartItemList);

        context.setCarts(simpleCarts);
        context.setBaseInfo(baseInfoPtr);
    }


    protected List<CartItem> copyList(List<CartItem> cartList) {
        List<CartItem> simCarts = new ArrayList<>();
        for (CartItem item : cartList) {
            CartItem newItem = SerializationUtils.clone(item);
            simCarts.add(newItem);
        }
        return simCarts;
    }

    protected void checkCouponCount(CheckoutPromotionRequest request, List<CheckoutCoupon> checkoutCouponList) throws BizError {
        int couponCount = 0;
        if (CollectionUtils.isNotEmpty(request.getCouponIds())) {
            couponCount += request.getCouponIds().size();
        }

        if (CollectionUtils.isNotEmpty(request.getCouponCodes())) {
            couponCount += request.getCouponCodes().size();
        }
        if (couponCount != checkoutCouponList.size()) {
            throw ExceptionHelper.create(ErrCode.ERR_EMPTY_COUPONS, "无效的优惠券");
        }
    }

    /**
     * 对抵扣券做预分摊
     *
     * @param simpleCarts 购物车
     * @param indexInfo   抵扣信息
     * @param couponId    券ID
     * @throws BizError 业务异常
     */
    private void preDivideItemDeductCoupon(List<CartItem> simpleCarts, DeduceIndex indexInfo, String couponId, Integer deductType) throws BizError {
        if (indexInfo == null || indexInfo.getInCarts() >= simpleCarts.size()) {
            log.warn("UpdateCartsReduceDeductCoupon cart is wrong :{}", indexInfo);
            throw ExceptionHelper.create(ErrCode.ERR_FUNC_INPUT, COMMON_SYSTEM_ERR_MSG);
        }
        CartItem cartItem = simpleCarts.get(indexInfo.getInCarts());
        if (cartItem == null) {
            log.warn("UpdateCartsReduceDeductCoupon {}th cart is wrong", indexInfo.getInCarts());
            throw ExceptionHelper.create(ErrCode.ERR_FUNC_INPUT, COMMON_SYSTEM_ERR_MSG);
        }
        String idKey = CouponHelper.getCartListCouponKey(couponId);
        Map<String, Long> reduceList = cartItem.getReduceList();
        Long deductMoney = reduceList.getOrDefault(idKey, 0L);
        if (cartItem.getCount() == 1) {
            // 抵扣劵的优惠明细
            List<ReduceDetail> reduceDetails = cartItem.getReduceDetailList().getOrDefault(PromotionConstant.SHARE_COUPON_DEDUCT_KEY, new ArrayList<>());
            //防止重复增加
            reduceDetails.removeIf(reduceDetail -> Objects.equals(reduceDetail.getId(), Long.valueOf(couponId)));

            ReduceDetail reduceDetail = new ReduceDetail();
            reduceDetail.setId(Long.valueOf(couponId));
            reduceDetail.setAmount(deductMoney);
            reduceDetails.add(reduceDetail);
            cartItem.getReduceDetailList().put(PromotionConstant.SHARE_COUPON_DEDUCT_KEY, reduceDetails);
            return;
        }
        // 如果被抵扣的产品同时买了多个，则拆出一个放在最后，构建抵扣Item
        CartItem newCart = buildDeductItem(cartItem, idKey, deductMoney, deductType, Long.valueOf(couponId));

        simpleCarts.add(newCart);
    }


    private CartItem buildDeductItem(CartItem cartItem, String idKey, Long deductMoney, Integer deductType, long CouponId) throws BizError{
        Map<String, Long> newReduceList = Maps.newHashMap();
        newReduceList.put(idKey, deductMoney);
        // 活动扣减分摊 reduceAmount
        long reduceAmount = (cartItem.getReduceAmount() - deductMoney) / cartItem.getCount();
        if ((reduceAmount + deductMoney) >= cartItem.getCartPrice()) {
            reduceAmount = cartItem.getCartPrice() - deductMoney;
            if (Objects.equals(deductType, TYPE_DEDUCT_PENNY)) {
                reduceAmount--;
            }
        }

        CartItem newCart = SerializationUtils.clone(cartItem);
        newCart.setCount(1);
        newCart.setItemId(newCart.getItemId() + PromotionConstant.ITEMID_DEDUCT_NEW);
        newCart.setReduceAmount(reduceAmount + deductMoney);
        newCart.setReduceList(newReduceList);

        // 剩余均摊优惠金额
        long diffReduceAmount = reduceAmount;
        if (diffReduceAmount > 0L) {
            // 做优惠金额分摊
            Map<String, Long> reduceList = cartItem.getReduceList();
            for (Map.Entry<String, Long> entry : reduceList.entrySet()) {
                if (Objects.equals(idKey, entry.getKey())) {
                    continue;
                }
                Long deductPrice = entry.getValue() / cartItem.getCount();
                entry.setValue(entry.getValue() - deductPrice);
                newReduceList.put(entry.getKey(), deductPrice);
                diffReduceAmount -= deductPrice;
            }
            if (diffReduceAmount != 0L) {
                Long epoch = diffReduceAmount > 0L ? 1L : -1L;
                // 最多允许迭代次数
                final long maxIteratorNum = Math.abs(diffReduceAmount);
                for (int iteratorNum = 0; iteratorNum < maxIteratorNum; iteratorNum++) {
                    for (Map.Entry<String, Long> entry : reduceList.entrySet()) {
                        if (Objects.equals(idKey, entry.getKey())) {
                            continue;
                        }
                        if (diffReduceAmount == 0L) {
                            break;
                        }
                        Long price = newReduceList.get(entry.getKey());
                        if ((entry.getValue() - epoch) < 0 || (price + epoch) < 0) {
                            continue;
                        }
                        entry.setValue(entry.getValue() - epoch);
                        newReduceList.put(entry.getKey(), newReduceList.get(entry.getKey()) + epoch);
                        diffReduceAmount -= epoch;
                    }
                    if (diffReduceAmount == 0L) {
                        break;
                    }
                }
            }
        }

        // 更新reduceList
        cartItem.setReduceAmount(cartItem.getReduceAmount() - newCart.getReduceAmount());
        cartItem.setCount(cartItem.getCount() - newCart.getCount());
        cartItem.getReduceList().remove(idKey);
        List<ReduceDetail> deductCouponDetails = cartItem.getReduceDetailList().get(PromotionConstant.SHARE_COUPON_DEDUCT_KEY);
        if (deductCouponDetails != null) {
            deductCouponDetails.removeIf(reduceDetail -> {
                long id = reduceDetail.getId() == null? 0: reduceDetail.getId();
                return id == CouponId;
            });
            if (CollectionUtils.isEmpty(deductCouponDetails)) {
                cartItem.getReduceDetailList().remove(PromotionConstant.SHARE_COUPON_DEDUCT_KEY);
            }
        }

        // 带抵扣劵的item优惠明细的重新分摊
        CartHelper.updateReduceDetailByReduceList(newCart.getReduceList(), newCart.getReduceDetailList());
        // 加上抵扣劵
        Map<String, List<ReduceDetail>> reduceDetailsMap = newCart.getReduceDetailList();
        List<ReduceDetail>  reduceDetails = new ArrayList<>();
        ReduceDetail reduceDetail = new ReduceDetail();
        reduceDetail.setId(CouponId);
        reduceDetail.setAmount(deductMoney);
        reduceDetails.add(reduceDetail);
        reduceDetailsMap.put(PromotionConstant.SHARE_COUPON_DEDUCT_KEY, reduceDetails);

        // 原item优惠明细的重新分摊
        CartHelper.updateReduceDetailByReduceList(cartItem.getReduceList(), cartItem.getReduceDetailList());

        return newCart;
    }



    /**
     * 计算优惠券列表及分摊
     * @param request
     * @param context
     * @param checkoutCouponList
     * @return
     * @throws BizError
     */
    protected List<Coupon> getCouponList(CheckoutPromotionV2Request request, CheckoutContext context, List<CheckoutCoupon> checkoutCouponList)
            throws BizError {
        Map<Integer, List<CheckoutCoupon>> couponGroupMap = checkoutCouponList.stream()
                .collect(Collectors.groupingBy(coupon -> coupon.getCouponType()));

        Map<CouponGeneralType, Integer> checkMap = new HashMap<>();

        //记录选中状态
        couponGroupMap.forEach((type, list) -> {
            if (request.getCouponIds() != null) {
                for (CheckoutCoupon checkoutCoupon : list) {
                    if (request.getCouponIds().contains(checkoutCoupon.getCouponId())) {
                        checkMap.put(CouponGeneralType.valueOf(type), 1);
                        break;
                    }
                }
            }
        });
        checkMap.put(CouponGeneralType.SHIPMENT, 2);

        //按优先级排序
        List<CouponGeneralType> sortList = couponGroupMap.keySet().stream()
                .map(integer -> CouponGeneralType.valueOf(integer))
                .collect(Collectors.toList());

        sortList.sort((o1, o2) -> {
            int check1 = checkMap.getOrDefault(o1, 2);
            int check2 = checkMap.getOrDefault(o2, 2);
            if (check1 == check2) {
                return Integer.compare(o1.getPriority(), o2.getPriority()) ;
            } else {
                return Integer.compare(check1, check2);
            }

        });


        //计算券列表
        List<Coupon> validCouponList = new ArrayList<>();
        List<Coupon> invalidCouponList = new ArrayList<>();
        for (CouponGeneralType type : sortList) {
            List<CheckoutCoupon> couponList = couponGroupMap.get(type.getCode());

            CouponProcessorInterface couponProcessor = initCouponProcessorInterface(type.getCode());
            if (couponProcessor == null) continue;
            // 转换为优惠工具，处理得到优惠结果
            List<CouponTool> couponTools = couponProcessor.loadCouponToolsForCheckout(couponList, context.getBizPlatform());

            //计算券列表
            Pair<TreeSet<Coupon>, List<Coupon>> couponListPair = checkoutCouponList(request, context, couponTools, couponProcessor);


            validCouponList.addAll(couponListPair.getLeft());
            invalidCouponList.addAll(couponListPair.getRight());
        }

        List<Coupon> finalCouponList = new ArrayList<>();

        if (request.getGetCouponList()) {
            finalCouponList.addAll(validCouponList);

            if (request.getShowType() == 1) {
                finalCouponList.addAll(invalidCouponList);
            }

        }

        return finalCouponList;
    }

    protected List<Long> getBestCoupon(CheckoutPromotionV2Request request, CheckoutContext context, List<CheckoutCoupon> checkoutCouponList)
            throws BizError {

        //按类型分组
        Map<Integer, List<CheckoutCoupon>> couponGroupMap = checkoutCouponList.stream()
                .collect(Collectors.groupingBy(coupon -> coupon.getCouponType(),
                        () -> new TreeMap<>(Comparator.comparingInt(o -> CouponGeneralType.valueOf(o).getPriority())
                        ),
                        Collectors.toList()));

        //复制出request
        CheckoutPromotionRequest copyRequest = new CheckoutPromotionRequest();
        List<CartItem> copyCartList = copyList(request.getCartList());
        copyRequest.setCartList(copyCartList);
        copyRequest.setShipmentId(request.getShipmentId());
        copyRequest.setShipmentExpense(request.getShipmentExpense());
        copyRequest.setSourceApi(request.getSourceApi());

        List<Long> bestCouponList = new ArrayList<>();

        for (Map.Entry<Integer, List<CheckoutCoupon>> groupEntry : couponGroupMap.entrySet()) {

            CouponProcessorInterface couponProcessor = initCouponProcessorInterface(groupEntry.getKey());
            if (couponProcessor == null) continue;
            // 转换为优惠工具，处理得到优惠结果
            List<CouponTool> couponTools = couponProcessor.loadCouponToolsForCheckout(groupEntry.getValue(), context.getBizPlatform());
            //计算理想情况最优券
            long bestCouponId = calcBestCoupon(copyRequest, context, couponTools, couponProcessor);
            if (bestCouponId > 0) {
                bestCouponList.add(bestCouponId);
            }

        }
        return bestCouponList;
    }

    private CouponProcessorInterface initCouponProcessorInterface(Integer type) {
        CouponProcessorInterface couponProcessor = processorList.stream()
                .filter(processor -> processor.getGeneralType().getCode() == type)
                .findFirst()
                .orElse(null);
        if (couponProcessor == null) {
            log.error("checkoutCoupons invalid couponType:{}", type);
            return null;
        }
        return couponProcessor;
    }



    private long calcBestCoupon(CheckoutPromotionRequest request, CheckoutContext context, List<CouponTool> couponTools, CouponProcessorInterface couponProcessor)
            throws BizError {

        Map<Long, CouponTool> couponToolMap = couponTools.stream().collect(Collectors.toMap(CouponTool::getCouponId, Function.identity()));
//        log.info("checkoutBestCoupon tools:{}", GsonUtil.toJson(couponTools));

        // 构造结算结果
        Map<Long, CouponCheckoutResult> checkoutResultMap = new HashMap<>();
        // 构造可用列表
        TreeSet<Coupon> validCouponList  = couponProcessor.initSortCouponList(checkoutResultMap);
        // 构造不可用列表
        List<Coupon> invalidCouponList = new ArrayList<>();



        doCheckout(request, context,checkoutResultMap, couponToolMap, validCouponList, invalidCouponList);
        log.info("checkoutBestCoupon checkoutResults:{}", GsonUtil.toJson(checkoutResultMap));

        try {
            //选理想状况下的最优券
            if (CollectionUtils.isNotEmpty(validCouponList)) {

                Coupon first = validCouponList.first();
                if (first.getRealReduce() <= 0) {
                    return 0;
                }
                CouponCheckoutResult result = checkoutResultMap.get(first.getId());
                CouponTool couponTool = couponToolMap.get(first.getId());
                if (context.getCarts() == null) {
                    context.setCarts(copyList(request.getCartList()));
                }
                couponTool.updateItemReduce(request.getCartList(), result);
                log.info("checkoutBestCoupon bestCoupon:{}", GsonUtil.toJson(first));
                return first.getId();
            }


        } catch (Exception e) {
            log.error("checkoutBestCoupon select Error. request:{} ", GsonUtil.toJson(request), e);
        }
        return 0;

    }


    private Pair<TreeSet<Coupon>, List<Coupon>> checkoutCouponList(CheckoutPromotionV2Request request, CheckoutContext context, List<CouponTool> couponTools, CouponProcessorInterface couponProcessor)
            throws BizError {


        Map<Long, CouponTool> couponToolMap = couponTools.stream().collect(Collectors.toMap(CouponTool::getCouponId, Function.identity()));
        //log.info("checkoutCoupons tools:{}", GsonUtil.toJson(couponTools));

        // 构造结算结果
        Map<Long, CouponCheckoutResult> checkoutResultMap = new HashMap<>();
        // 构造可用列表
        TreeSet<Coupon> validCouponList  = couponProcessor.initSortCouponList(checkoutResultMap);
        // 构造不可用列表
        List<Coupon> invalidCouponList = new ArrayList<>();


        // 完成结算操作
        doCheckout(request, context, checkoutResultMap, couponToolMap, validCouponList, invalidCouponList);
        log.info("checkoutCoupons checkoutResults:{}", GsonUtil.toJson(checkoutResultMap));


        //todo 逻辑上移
        try {
            // 默认选中
            Coupon selectCoupon = couponProcessor.setSelectCoupon(request, validCouponList, invalidCouponList, checkoutResultMap, context);

            log.info("checkoutCoupons selectCoupon:{}", GsonUtil.toJson(selectCoupon));

            // 劵结算分摊
            if (selectCoupon != null) {

                CouponCheckoutResult result = checkoutResultMap.get(selectCoupon.getId());
                CouponTool selectCouponTool = couponToolMap.get(selectCoupon.getId());
                if (context.getCarts() == null) {
                    context.setCarts(copyList(request.getCartList()));
                }
                selectCouponTool.updateCartsReduce(request, context, result);

            }

        } catch (Exception e) {
            log.error("checkoutCoupons select Error. request:{} ", GsonUtil.toJson(request), e);
            if (request.getSourceApi() == SourceApi.SUBMIT) {
                throw ExceptionHelper.create(GeneralCodes.InternalError, "优惠券结算失败", e);
            }

        }

        return Pair.of(validCouponList, invalidCouponList);
    }


}
