package com.xiaomi.nr.promotion.mq.consumer;

import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.constant.MqConstant;
import com.xiaomi.nr.promotion.enums.TradeFromEnum;
import com.xiaomi.nr.promotion.mq.consumer.entity.OrderData;
import com.xiaomi.nr.promotion.mq.consumer.entity.OrderMessage;
import com.xiaomi.nr.promotion.mq.consumer.entity.OrderRefundBody;
import com.xiaomi.nr.promotion.mq.consumer.entity.OrderRefundMessage;
import com.xiaomi.nr.promotion.resource.impl.ResourceManager;
import com.xiaomi.nr.promotion.resource.model.ResourceManageContext;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.rpc.order.OrderQueryServiceProxy;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 订单消息
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(value="rocketmq.topic.order.enabled", havingValue="true")
@RocketMQMessageListener(
        topic = "${rocketmq.topic.order}",
        selectorExpression = "OrderClosed || StoreGoClose || OrderRefund",
        nameServer = "${rocketmq.order.name-server}",
        accessKey = "${rocketmq.access-key}",
        secretKey = "${rocketmq.secret-key}",
        consumerGroup = "${rocketmq.topic.order.consumerGroup}")
public class OrderMessageListener implements RocketMQListener<String> {

    @Autowired
    private ResourceManager resourceManager;
    @Autowired
    private OrderQueryServiceProxy orderQueryServiceProxy;

    @Autowired
    private NacosConfig nacosConfig;



    @Override
    public void onMessage(String s) {
        long startTime = System.currentTimeMillis();
        log.info("OrderMessageListener receive startTime:{} msg:{}", startTime, s);
        try {
            OrderMessage closeMessage = GsonUtil.fromJson(s, OrderMessage.class);
            if (closeMessage == null) {
                log.error("OrderMessageListener data error. message:{}", s);
                return;
            }
            List<Long> blackList = nacosConfig.getMiShopMessageHandleBlackList();
            // 黑名单中的mid不处理
            if (Objects.isNull(closeMessage.getBuyerId())) {
                log.info("消息中mid为空, orderMessage:{}", GsonUtil.toJson(closeMessage));
                return;
            }
            if (CollectionUtils.isNotEmpty(blackList) && blackList.contains(closeMessage.getBuyerId())) {
                log.info("该mid在消息处理黑名单中, 不处理, mid :{}", closeMessage.getBuyerId());
                return;
            }
            String event = closeMessage.getEvent();
            // 关单
            if (Objects.equals(MqConstant.TOPIC_ORDER_CLOSED, event) || Objects.equals(MqConstant.TOPIC_ORDER_GO_CLOSED, event)) {
                onHandleCloseMessage(s);
                // 退款
            } else if (Objects.equals(MqConstant.TOPIC_ORDER_REFUND, event)) {
                onHandleRefundMessage(s);
            }
        } catch (Throwable e) {
            log.error("OrderMessageListener error. message:{}", s, e);
            throw new RuntimeException(e);
        } finally {
            log.info("OrderMessageListener receive ws:{} msg:{}", System.currentTimeMillis() - startTime, s);
        }
    }

    /**
     * 对SA订单进行关闭，通过资源进行判断
     * 如果为可关闭的订单，则通过resourceManager进行资源关闭
     *
     * @param s 订单关闭消息
     */
    private void onHandleCloseMessage(String s) throws BizError {
        OrderMessage closeMessage = GsonUtil.fromJson(s, OrderMessage.class);
        if (closeMessage == null) {
            log.error("OrderMessageListener data error. message:{}", s);
            return;
        }
        String saOrderId = closeMessage.getIndex();
        saOrderId = StringUtils.replace(saOrderId, "SA", "");
        if (StringUtils.isEmpty(saOrderId) || !StringUtils.isNumeric(saOrderId)) {
            log.warn("OrderMessageListener not support data. message:{} saOrderId:{}", s, saOrderId);
            return;
        }
        long orderId = Long.parseLong(saOrderId);
        ResourceManageContext context = ResourceManageContext.fromMessage(orderId);
       /* List<ResourceType> resourceTypes = context.getResourceTypes();
        List<ResourceType> collect = resourceTypes.stream().filter(resourceType -> !resourceType.equals(ResourceType.GOVERNMENT_SUBSIDY_ACT_LIMIT)).toList();
        context.setResourceTypes(collect);*/
        resourceManager.rollback(context);
    }

    /**
     * 如果大单下所有子单关闭了，则可以关闭
     * - 当前只退券和红包
     *
     * @param s 订单关闭消息
     */
    private void onHandleRefundMessage(String s) throws Exception {
        OrderRefundMessage refundMessage = GsonUtil.fromJson(s, OrderRefundMessage.class);
        if (refundMessage == null) {
            log.error("OrderMessageListener data error. message:{}", s);
            return;
        }
        String body = refundMessage.getBody();
        OrderRefundBody refundBody = GsonUtil.fromJson(body, OrderRefundBody.class);
        if (refundBody == null || refundBody.getOrderData() == null) {
            log.warn("OrderMessageListener body is null . message:{}", s);
            return;
        }
        OrderData orderData = refundBody.getOrderData();
//        if (!Objects.equals(orderData.getOrgOrderId(), refundMessage.getOdOrderId())) {
//            log.warn("OrderMessageListener odOrderId not equal to orgOrderId. message:{}", s);
//            return;
//        }
        long orderId = Long.parseLong(orderData.getOrgOrderId());
        ResourceManageContext context = ResourceManageContext.fromMessage(orderId);
        context.setResourceTypes(Arrays.asList(ResourceType.COUPON, ResourceType.RED_PACKET));
        context.setTradeFrom(TradeFromEnum.SHOP);
        resourceManager.partRollback(context);
    }
}
