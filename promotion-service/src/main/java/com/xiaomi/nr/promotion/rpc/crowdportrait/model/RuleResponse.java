package com.xiaomi.nr.promotion.rpc.crowdportrait.model;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/27 16:11
 */
@Data
public class RuleResponse {
    /**
     * 状态返回码（0表示请求正常，1表示未知错误, 3表示参数不正确，4表示权限不正确，8表示服务内部异常）
     */
    private int retCode;

    /**
     * 状态信息 （如果有错误，会返回错误原因）
     */
    private String retMsg;

    /**
     * 人群包命中结果
     */
    private Map<String, RuleResult> result;
}
