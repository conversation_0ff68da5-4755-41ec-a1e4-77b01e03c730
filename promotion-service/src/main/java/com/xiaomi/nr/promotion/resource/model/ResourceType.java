package com.xiaomi.nr.promotion.resource.model;

import com.xiaomi.nr.promotion.resource.provider.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 资源的类型：　赠品，减去金额，等
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
public enum ResourceType {
    /**
     * 线上活动库存限购
     */
    ONLINE_ACT_LIMIT(1, OnlineActLimitProvider.ActOnlineLimit.class),
    /**
     * 线上用户参加活动记录每天
     */
    ONLINE_ACT_RECORD_DAILY(2, OnlineActUserRecordDailyProvider.ActUserRecordDaily.class),
    /**
     * 线上用户参加活动记录
     */
    ONLINE_ACT_RECORD(3, OnlineActUserRecordTotalProvider.ActUserRecord.class),
    /**
     * 线下全部门店每天参与活动次数每天
     */
    OFFLINE_ACT_ALL_STORE_LIMIT_DAILY(4, OfflineActAllStoreLimitDailyProvider.ActAllStoreDayLimit.class),
    /**
     * 线下全部门店每天参与活动次数
     */
    OFFLINE_ACT_ALL_STORE_LIMIT(5, OfflineActAllStoreLimitProvider.ActAllStoreLimit.class),
    /**
     * 线下用户每人每天
     */
    OFFLINE_ACT_PERSON_LIMIT_DAILY(6, OfflineActPersonLimitDailyProvider.ActPersonDayLimit.class),
    /**
     * 线下用户每人
     */
    OFFLINE_ACT_PERSON_LIMIT(7, OfflineActPersonLimitProvider.ActPersonLimit.class),
    /**
     * 线下门店每天
     */
    OFFLINE_ACT_STORE_LIMIT_DAILY(8, OfflineActStoreLimitDailyProvider.ActStoreDayLimit.class),
    /**
     * 线下用户每人
     */
    OFFLINE_ACT_STORE_LIMIT(9, OfflineActStoreLimitProvider.ActStoreLimit.class),
    /**
     * 线下门店每天
     */
    OFFLINE_ACT_UTYPE_PERSON_LIMIT_DAILY(10, OfflineActUtypePersonLimitDailyProvider.ActUtypePersonDayLimit.class),
    /**
     * 线下用户每人
     */
    OFFLINE_ACT_UTYPE_PERSON_LIMIT(11, OfflineActUtypePersonLimitProvider.ActUtypePersonLimit.class),
    /**
     * 直降活动所有门店每天
     */
    ONSALE_ACT_ALL_STORE_LIMIT_DAILY(12, OnsaleActAllStoreLimitDailyProvider.ActAllStoreDayLimit.class),
    /**
     * 直降活动所有门店
     */
    ONSALE_ACT_ALL_STORE_LIMIT(13, OnsaleActAllStoreLimitProvider.ActAllStoreLimit.class),
    /**
     * 直降活动手机用户
     */
    ONSALE_ACT_MOBILE_USER_LIMIT(14, OnsaleActMobileUserLimitProvider.ActUserLimit.class),
    /**
     * 直降活动门店每天
     */
    ONSALE_ACT_STORE_LIMIT_DAILY(15, OnsaleActStoreLimitDailyProvider.ActStoreDayLimit.class),
    /**
     * 直降活动门店
     */
    ONSALE_ACT_STORE_LIMIT(16, OnsaleActStoreLimitProvider.ActStoreLimit.class),
    /**
     * 直降活动用户
     */
    ONSALE_ACT_USER_LIMIT(17, OnsaleActUserLimitProvider.ActUserLimit.class),
    /**
     * 赠品次数资源
     */
    GIFT_ACT_LIMIT(18, GiftLimitProvider.ResContent.class),
    /**
     * 加价购次数资源
     */
    BARGAIN_ACT_LIMIT(19, BargainLimitProvider.ResContent.class),
    /**
     * 换新立减次数
     */
    RENEW_REDUCE_ACT_LIMIT(25, RenewReduceResourceProvider.ResContent.class),
    /**
     * 主品活动次数
     */
    GOODS_ACT_LIMIT(22, GoodsLimitResourceProvider.ResContent.class),
    /**
     * 指定门店降价的每人每个门店活动数量
     */
    PARTONSALE_USER_REDUCE_ACT_LIMIT(26, ActUserStoreLimitProvider.ActUserLimit.class),
    /**
     * 指定门店降价的sku/package参与次数
     */
    PARTONSALE_GOODS_REDUCE_ACT_LIMIT(27, ActGoodsStoreLimitResourceProvider.ResContent.class),
    /**
     * 24年北京以旧换新购新补贴每人限购
     */
    PURCHASE_SUBSIDY_ACT_LIMIT(28, SubsidyPhoenixProvider.ResContent.class),

    /**
     * 政府模式以旧换新购新补贴每人限购
     */
    GOVERNMENT_SUBSIDY_ACT_LIMIT(29, GovernmentSubsidyPhoenixProvider.ResContent.class),
    /**
     * 用户无码券
     */
    COUPON(20, CouponProvider.ResContent.class),
    /**
     * 红包
     */
    RED_PACKET(40, RedpacketProvider.RedpacketResource.class),
    /**
     * 积分
     */
    POINT(41, PointProvider.PointResource.class),
    /**
     * 礼品卡
     */
    ECARD(50, EcardProvider.EcardContent.class),

    /**
     * 订单优惠
     */
    ORDER_BENEFIT(60, OrderBenefitProvider.OrderBenefitResource.class),

    /**
     * 订单优惠明细
     */
    ORDER_PROMOTION(61, OrderBenefitProvider.OrderBenefitResource.class),
    /**
     * 三方优惠联通华盛资源
     */
    PHOENIX_HUASHENG(70, PhoenixHuashengProvider.ResContent.class),
    /**
     * 运营商入驻资源
     */
    PHOENIX_OPERATOR(71, PhoenixOperatorProvider.ResContent.class),
    /**
     * 门店以旧换新资源
     */
    PHOENIX_RENEW(72, PhoenixRenewProvider.ResContent.class),
    /**
     * 汽车订单优惠
     */
    CAR_ORDER_BENEFIT(73, CarOrderPromotionProvider.OrderPromotionDetailResource.class),
    /**
     * 用户参加活动次数
     */
    USER_JOIN_ACT_NUM(74, UserActivityCountProvider.UserJoinActNum.class),
    /**
     * 汽车维修折扣
     */
    VID_JOIN_ACT_LIMIT(75, VidActivityCountProvider.VidJoinActNum.class)
    ;

    private final static Map<Integer, ResourceType> INNER_MAP = new HashMap<>();

    static {
        for (ResourceType pageType : ResourceType.values()) {
            INNER_MAP.put(pageType.value, pageType);
        }
    }

    private final int value;

    private final Class<?> resourceType;

    ResourceType(int type, Class<?> resourceType) {
        this.value = type;
        this.resourceType = resourceType;
    }

    /**
     * 获取资源类型
     *
     * @param type 类型
     * @return ResourceTypel
     */
    public static ResourceType valueOf(int type) {
        return INNER_MAP.get(type);
    }

    public int getValue() {
        return value;
    }

    public Class<?> getResourceClazz() {
        return resourceType;
    }
}
