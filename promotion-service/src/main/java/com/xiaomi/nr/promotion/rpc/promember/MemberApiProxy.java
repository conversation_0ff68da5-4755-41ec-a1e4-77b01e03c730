package com.xiaomi.nr.promotion.rpc.promember;

import com.xiaomi.cnzone.b2csvr.promember.api.member.MemberApi;
import com.xiaomi.cnzone.b2csvr.promember.api.member.req.CheckIsMemberReq;
import com.xiaomi.cnzone.b2csvr.promember.api.member.res.CheckIsMemberVo;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

/**
 * F会员
 */
@Component
@Slf4j
public class MemberApiProxy {

    private static final String SYSTEM_SOURCE = "promotion";

    @Reference(interfaceClass = MemberApi.class, version = "${promember.dubbo.version}", timeout = 50, group = "${promember.dubbo.group}")
    private MemberApi memberApi;

    /**
     * 获取用户标签
     *
     * @param uid mid
     * @return 结果
     */
    @Async("proMemberAsyncTaskExecutor")
    public ListenableFuture<Boolean> checkIsMember(Long uid) {
        CheckIsMemberReq request = new CheckIsMemberReq();
        request.setMid(uid);
        request.setChannel(SYSTEM_SOURCE);
        try {
            Result<CheckIsMemberVo> result = memberApi.checkIsMember(request);
            log.info("MemberApiProxy checkIsMember request:{}, result:{}", request, result);
            // 汇总结果
            if (result.getCode() == 0 && result.getData() != null && result.getData().getMembership() != null) {
                if (result.getData().getMembership()) {
                    return AsyncResult.forValue(true);
                }
            }
        } catch (Exception e) {
            log.error("UserPropertyApi userProperty error, request:{}, result:{}", request, e);
        }
        return AsyncResult.forValue(false);
    }


}
