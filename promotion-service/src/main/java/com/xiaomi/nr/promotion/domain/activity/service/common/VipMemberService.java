package com.xiaomi.nr.promotion.domain.activity.service.common;

import com.xiaomi.micar.club.api.model.member.MemberInfo;
import com.xiaomi.micar.club.api.resp.member.MemberInfoResp;
import org.springframework.util.concurrent.ListenableFuture;

/**
 * 门店信息服务接口
 *
 * <AUTHOR>
 * @date 2021/4/29
 */
public interface VipMemberService {


    ListenableFuture<MemberInfo> getUserMemberInfoAsync(Long userId, String vid);


    ListenableFuture<MemberInfo> getVidMemberInfoAsync(String vid);
}
