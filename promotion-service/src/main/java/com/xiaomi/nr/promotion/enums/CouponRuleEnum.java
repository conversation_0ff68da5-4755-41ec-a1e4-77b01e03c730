package com.xiaomi.nr.promotion.enums;

import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.domain.coupon.service.base.condition.AllDeductCondition;
import com.xiaomi.nr.promotion.domain.coupon.service.base.condition.CouponCondition;
import com.xiaomi.nr.promotion.domain.coupon.service.base.condition.HalfDeductCondition;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/3/4
 */
@Getter
@AllArgsConstructor
public enum CouponRuleEnum {
    
    /**
     * 全抵扣
     */
    ALL_DEDUCT(1),
    
    /**
     * 半抵扣
     */
    HALF_DEDUCT(2),
    
    ;
    private final Integer code;
    
    private static final Map<Integer, CouponRuleEnum> COUPON_RULE_ENUM_MAP = Maps.newHashMap();
    
    static {
        COUPON_RULE_ENUM_MAP.put(ALL_DEDUCT.code, ALL_DEDUCT);
        COUPON_RULE_ENUM_MAP.put(HALF_DEDUCT.code, HALF_DEDUCT);
    }
    
    public static CouponRuleEnum getEnumByCode(Integer code) {
        return COUPON_RULE_ENUM_MAP.get(code);
    }
    
    public CouponCondition getConditionByEnum() {
        switch (this) {
            case ALL_DEDUCT -> {
                return new AllDeductCondition();
            }
            case HALF_DEDUCT -> {
                return new HalfDeductCondition();
            }
        }
        
        return null;
    }
}
