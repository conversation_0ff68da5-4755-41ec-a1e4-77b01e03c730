package com.xiaomi.nr.promotion.componet.condition.carshop;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.PostFreePromotionConfig;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @author: zhangliwei6
 * @date: 2025/5/8 19:40
 * @description:
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class CarShopPostFreeCondition extends Condition {

    /**
     * 活动ID
     */
    private Long promotionId;

    /**
     * 优惠类型
     */
    private PromotionToolType promotionType;

    /**
     * 参与商品信息
     */
    private Set<Long> ssuIds;

    /**
     * 条件阶梯
     */
    private List<QuotaLevel> levelList;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        List<CartItem> cartList = request.getCartList();
        if (CollectionUtils.isEmpty(cartList)) {
            return false;
        }

        // bizPlatform、channel
        if (! Objects.equals(context.getBizPlatform(), BizPlatformEnum.CAR_SHOP) || ! Objects.equals(request.getChannel(), ChannelEnum.CAR_SHOP.getValue())) {
            return false;
        }

        List<GoodsIndex> goodsIndexList = getGoodsIndexList(cartList);
        // 没有符合的商品，不满足活动
        if (CollectionUtils.isEmpty(goodsIndexList)) {
            log.debug("condition is not satisfied. fillGoodsList is empty. actId:{}", promotionId);
            return false;
        }

        QuotaLevel level = levelList.getFirst();
        if (level == null) {
            log.debug("condition is not satisfied. fill level is not found. actId:{}", promotionId);
            return false;
        }

        context.setQuotaLevel(level);
        context.setGoodIndex(goodsIndexList);
        return true;
    }

    private List<GoodsIndex> getGoodsIndexList(List<CartItem> cartList) {
        List<GoodsIndex> goodsIndexList = new LinkedList<>();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem cartItem = cartList.get(idx);
            //处理当前item是否可参加活动
            boolean itemQualify = CartHelper.checkItemActQualifyCommon(cartItem, promotionType.getTypeId());
            if (! itemQualify) {
                continue;
            }
            if (! ssuIds.contains(cartItem.getSsuId())) {
                continue;
            }
            goodsIndexList.add(new GoodsIndex(cartItem.getItemId(), idx));
        }
        return goodsIndexList;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof PostFreePromotionConfig promotionConfig)) {
            log.error("config is not instanceof CarShopPostFreePromotionConfig. config:{}", config);
            return;
        }
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        List<Long> ssuIdList = Optional.ofNullable(promotionConfig.getJoinGoods())
                .map(CompareItem::getSsuId)
                .orElse(Collections.emptyList());
        this.ssuIds = new HashSet<>(ssuIdList);
        this.levelList = promotionConfig.getLevelList();
    }
}
