package com.xiaomi.nr.promotion.domain.coupon.service.mishop;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.api.dto.model.CouponInfo;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.constant.CouponConstant;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.model.ShipmentCouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.service.base.type.AbstractCouponTool;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import com.xiaomi.nr.promotion.enums.OrderShipmentIdEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.CouponHelper;
import com.xiaomi.nr.promotion.util.ExpressHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by wangweiyi on 2022/6/1
 * 闪送运费券
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class ShipmentCoupon extends AbstractCouponTool {


    @Override
    public Long getCouponId() {
        return id;
    }

    @Override
    public CouponTypeEnum getCouponType() {
        return CouponTypeEnum.CASH;
    }

    /**
     * 商品跳过运费券使用的Code
     */
    private static final Long SHIPMENT_BANNED_CODE= 99L;

    @Override
    public CouponCheckoutResult checkoutCoupon(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        // 筛选满足商品列表
        List<CartItem> cartList = new ArrayList<>();
        ShipmentCouponCheckoutResult result = new ShipmentCouponCheckoutResult();

        if (request.getShipmentId() != OrderShipmentIdEnum.FLASH_POST.val) {
            result.setCouponId(this.id);
            result.setAllow(false);
            result.setUnusableReason(CouponConstant.NOT_SATISFY_USAGE_CONDITION);
            result.setUnusableCode(ErrCode.ERR_COUPON_MONEY_NUM.getCode());
            result.setKeyDataUnusable(CouponConstant.NOT_SATISFY_USAGE_CONDITION);
            result.setCouponShipmentReduce(checkoutCoupon.getPromotionValue());
            return result;
        }


        //过滤交易标记出来的不可用券商品
        for (CartItem cartItem : request.getCartList()) {
            if (cartItem.getCannotUseCouponTypes() != null && cartItem.getCannotUseCouponTypes().contains(SHIPMENT_BANNED_CODE)) {
                continue;
            }
            cartList.add(cartItem);
        }

        List<GoodsIndex> indexList = matchCartList(cartList, context, joinGoods, (long) getCouponType().getType());

        //log.info("ShipmentCoupon indexList:{}", GsonUtil.toJson(indexList));

        if (CollectionUtils.isEmpty(indexList)) {
            result.setAllow(false);
            result.setCouponId(this.id);
            result.setUnusableCode(ErrCode.ERR_COUPON_NO_VALID_GOODS.getCode());
            result.setUnusableReason("订单中不含可用券商品");
            result.setKeyDataUnusable("rdata_invalid_coupon_reason");
            return result;
        }
        // 计算符合总价
        ValidGoods validGoods = CartHelper.buildValidGoods(cartList, indexList);

        //计算扣减运费
        Long shipmentExpress = request.getShipmentExpense();
        //优惠券面额
        Long expressReduce = checkoutCoupon.getPromotionValue();

        long reduce = Math.min(expressReduce, shipmentExpress);
        Pair<Boolean, String> satisfiedQuota = isSatisfiedQuota(validGoods);

        if (satisfiedQuota.getLeft()) {

            result.setCouponId(this.id);
            result.setAllow(true);
            result.setValidGoods(indexList);
            result.setReduceAmount(reduce);
            result.setCouponShipmentReduce(expressReduce);
            result.setRealShipmentReduce(reduce);
        } else {
            result.setCouponId(this.id);
            result.setAllow(false);
            result.setUnusableReason(CouponConstant.NOT_SATISFY_USAGE_CONDITION);
            result.setUnusableCode(ErrCode.ERR_COUPON_MONEY_NUM.getCode());
            result.setKeyDataUnusable(satisfiedQuota.getRight());
            result.setCouponShipmentReduce(expressReduce);
        }
        return result;

    }

    @Override
    public void updateCartsReduce(CheckoutPromotionRequest request, CheckoutContext context, CouponCheckoutResult result) {

        long couponId = result.getCouponId();

        String idKey = CouponHelper.getCartListCouponKey(String.valueOf(couponId));

        List<String> ids = result.getValidGoods().stream().map(GoodsIndex::getItemId).collect(Collectors.toList());
        long realShipmentExpress = ((ShipmentCouponCheckoutResult) result).getRealShipmentReduce();
        //处理运费，这里与包邮活动的券包邮互斥
        Express express = ExpressHelper.buildShipmentExpress(idKey, ids, realShipmentExpress, couponId);
        context.appendExpress(express);
        context.setShipmentCouponName(checkoutCoupon.getCouponName());
        context.setShipmentCouponType(checkoutCoupon.getType());

    }

    @Override
    public void updateItemReduce(List<CartItem> cartList, CouponCheckoutResult result) {
        //置空
        return;
    }

    @Override
    public Coupon generateCartCoupon() {
        return initCoupon();
    }

    @Override
    public CouponInfo generateCouponInfo(CouponCheckoutResult result) {
        ShipmentCouponCheckoutResult shipmentCouponCheckoutResult = (ShipmentCouponCheckoutResult)result;
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setOffline(CouponConstant.COUPON_ONLINE_DB);
        couponInfo.setCouponCode("");
        couponInfo.setCouponId(this.id);
        couponInfo.setReduceMoney(0L);
        couponInfo.setReduceExpress(shipmentCouponCheckoutResult.getRealShipmentReduce());
        couponInfo.setCouponName(checkoutCoupon.getCouponName());
        couponInfo.setCouponType(checkoutCoupon.getType());
        return couponInfo;
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.COUPON_CASH;
    }
}
