package com.xiaomi.nr.promotion.componet.action;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.CrowdEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.entity.redis.GiftBargainGroup;
import com.xiaomi.nr.promotion.entity.redis.Goods;
import com.xiaomi.nr.promotion.entity.redis.SkuGroup;
import com.xiaomi.nr.promotion.enums.BooleanV2Enum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.*;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyGiftPromotionConfig;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 买赠计算
 *
 * <AUTHOR>
 * @date 2021/9/10
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class BuyGiftAction extends AbstractAction {
    /**
     * 促销活动ID
     */
    private Long promotionId;
    /**
     * 赠品信息
     */
    private Goods giftGoods;
    /**
     * 活动限制. 数据值：0-不限制， 没有限制就没有对应字段
     */
    private ActNumLimitRule numLimitRule;

    /**
     * 是否F会员专属赠品 1-是，2-否
     */
    private Integer isFMember;

    /**
     * 人群列表，枚举见@CrowdEnum
     */
    private List<String> crowdList;



    @Autowired
    private ActivityRedisDao activityRedisDao;

    /**
     * 处理逻辑
     * 1. 对赠品进行分组
     * 2. 对超出最大数量的赠品需要扣减为正常基数
     * 3. 构建PromotionInfo
     * 4. 处理资源
     *
     * @param promotion 优惠工具
     * @param request   请求参数
     * @param context   请求上下文，活动组件间
     * @throws BizError 业务异常
     */
    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        // 购物车中来源当前活动的赠品
        List<CartItem> cartList = request.getCartList();
        List<GoodsIndexNew> indexNewList = CartHelper.getCartsIndex(cartList, SourceEnum.SOURCE_GIFT.getSource(), String.valueOf(promotionId));

        // 获取赠品规则， 对赠品进行分组 (key: itemId, val: 信息）
        Map<String, GiftBargainGroup> groupMap = doGiftMatch(cartList, indexNewList);
        Map<Long, List<CartItem>> cartGroup = groupByGroupId(cartList, groupMap);

        // 对每组赠品进行检查， 如果超出最大，需要递减到合适值. key: groupId, val: count
        for (Map.Entry<Long, List<CartItem>> entry : cartGroup.entrySet()) {
            List<CartItem> itemList = entry.getValue();
            checkAndReduceGroupCount(itemList, groupMap, context.getFillTimes());
        }

        // 删除购物车 CART_DEL_FLAG
        delInvalidCarts(cartList);

        // 构建promotionInfo
        setResult(context, cartList, promotion, indexNewList, cartGroup, groupMap);

        // 初始化资源
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            initGiftResource(request, promotionId, context, cartGroup, numLimitRule);
        }
    }

    /**
     * 获取组赠品信息（信息列表）
     *
     * @param cartList     购物车信息
     * @param indexNewList 索引信息列表
     * @return 组赠品信息
     */
    private Map<String, GiftBargainGroup> doGiftMatch(List<CartItem> cartList, List<GoodsIndexNew> indexNewList) {
        if (CollectionUtils.isEmpty(indexNewList)) {
            return Collections.emptyMap();
        }

        Map<String, GiftBargainGroup> groupMap = Maps.newHashMap();
        indexNewList.forEach(indexNew -> {
            CartItem item = CartHelper.getCartItem(cartList, indexNew);
            if (item == null) {
                return;
            }
            GiftBargainGroup giftInfo = getGiftGoods(item);
            if (giftInfo == null) {
                item.setItemId(CART_DEL_FLAG);
                indexNew.setItemId(CART_DEL_FLAG);
                return;
            }
            groupMap.put(indexNew.getItemId(), giftInfo);
        });
        return groupMap;
    }

    /**
     * 获取赠品信息
     *
     * @param cartItem 购物车Item
     * @return 赠品信息
     */
    private GiftBargainGroup getGiftGoods(CartItem cartItem) {
        if (giftGoods == null || CollectionUtils.isEmpty(giftGoods.getSkuGroupList())) {
            log.error("data giftGoods error. promotionId:{}", promotionId);
            return null;
        }
        SkuGroup skuGroup = giftGoods.getSkuGroupList().stream()
                .filter(group -> Objects.equals(group.getGroupId(), cartItem.getGroupId()))
                .findAny().orElse(null);
        if (skuGroup == null || CollectionUtils.isEmpty(skuGroup.getListInfo())) {
            log.warn("data giftGoods error, no match group. promotionId:{} groupId:{}", promotionId, cartItem.getGroupId());
            return null;
        }
        return skuGroup.getListInfo().stream()
                .filter(group -> Objects.equals(String.valueOf(group.getSku()), cartItem.getSku()))
                .findAny().orElse(null);
    }

    /**
     * 对赠品进行分组
     *
     * @param cartList 购物车列表
     * @param groupMap 赠品信息组
     * @return 赠品分组信息 key: groupId val: 赠品组信息
     */
    private Map<Long, List<CartItem>> groupByGroupId(List<CartItem> cartList, Map<String, GiftBargainGroup> groupMap) {
        if (CollectionUtils.isEmpty(cartList)) {
            return Collections.emptyMap();
        }
        Map<Long, List<CartItem>> cartGroupMap = new HashMap<>(groupMap.size());
        for (CartItem item : cartList) {
            if (!SourceEnum.isGift(item.getSource())) {
                continue;
            }
            if (groupMap.get(item.getItemId()) == null) {
                continue;
            }
            cartGroupMap.merge(item.getGroupId(), Lists.newArrayList(item), (list1, list2) -> {
                list1.addAll(list2);
                return list1;
            });
        }
        return cartGroupMap;
    }

    /**
     * 处理每组赠品组信息
     *
     * @param cartItemGroup 购物车赠品商品组
     * @param groupMap      赠品信息
     * @param maxCount      服务
     */
    private void checkAndReduceGroupCount(List<CartItem> cartItemGroup, Map<String, GiftBargainGroup> groupMap, Integer maxCount) {
        if (CollectionUtils.isEmpty(cartItemGroup)) {
            return;
        }

        long curCount = 0L;
        for (CartItem item : cartItemGroup) {
            if (CART_DEL_FLAG.equals(item.getItemId())) {
                continue;
            }
            if (curCount >= maxCount) {
                item.setItemId(CART_DEL_FLAG);
                continue;
            }
            GiftBargainGroup giftBargainGroup = groupMap.get(item.getItemId());
            if (giftBargainGroup == null) {
                item.setItemId(CART_DEL_FLAG);
                continue;
            }
            // 检查次数
            long count = (item.getCount() / giftBargainGroup.getGiftBaseNum());
            if (curCount + count > maxCount) {
                count = maxCount - curCount;
                item.setCount((int) (count * giftBargainGroup.getGiftBaseNum()));
            }
            curCount += count;
            item.setCartPrice(giftBargainGroup.getCartPrice());
        }
    }

    /**
     * 设置PromotionInfo
     *
     * @param context     结果
     * @param cartList    购物车列表
     * @param tool        工具
     * @param goodsIndNew 索引信息
     * @param cartGroup   组信息
     * @throws BizError 业务异常
     */
    private void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool, List<GoodsIndexNew> goodsIndNew,
                           Map<Long, List<CartItem>> cartGroup, Map<String, GiftBargainGroup> groupMap) throws BizError {
        // 总赠品数 和 每组之间参加活动的最大值
        Map<Long, Integer> countMap = getCountMap(cartGroup, groupMap);

        // 生成活动拓展信息
        Integer maxCount = context.getFillTimes();
        int validCountAll = countMap.values().stream().mapToInt(Integer::intValue).sum();
        List<GroupCurCount> groupCurCount = buildIncrCountMap(cartGroup, countMap);
        String activityInfo = generateExtendInfo(maxCount, validCountAll, groupCurCount);

        // 活动优惠信息
        int joinCount = countMap.values().stream().mapToInt(Integer::intValue).max().orElse(0);
        List<String> joinedItemIdList = CartHelper.getJoinedItemIdListNew(goodsIndNew, cartList);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setJoinedItemId(joinedItemIdList);
        promotionInfo.setParentItemId(CartHelper.getParentItemIdList(context.getGoodIndex()));
        promotionInfo.setExtend(activityInfo);
        promotionInfo.setJoinCounts(joinCount);
        promotionInfo.setJoined(CollectionUtils.isNotEmpty(joinedItemIdList) ? 1 : 0);
        promotionInfo.setCrowdList(context.getCrowdList());
        // 线上兼容, 上线后可废弃
        if (context.getCrowdList().contains(CrowdEnum.F_MEMBER.getCode())){
            promotionInfo.setIsFMember(1);
        }else {
            promotionInfo.setIsFMember(2);
        }
        // 设置上下文结果
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    private Map<Long, Integer> getCountMap(Map<Long, List<CartItem>> cartGroup, Map<String, GiftBargainGroup> groupMap) {
        Map<Long, Integer> countMap = Maps.newHashMap();
        for (Map.Entry<Long, List<CartItem>> entry : cartGroup.entrySet()) {
            Long groupId = entry.getKey();
            List<CartItem> itemList = entry.getValue();
            if (CollectionUtils.isEmpty(itemList)) {
                countMap.put(groupId, 0);
            }
            long count = itemList.stream()
                    .filter(item -> !Objects.equals(item.getItemId(), CART_DEL_FLAG))
                    .filter(item -> Objects.nonNull(groupMap.get(item.getItemId())))
                    .mapToInt(item -> (int) (item.getCount() / groupMap.get(item.getItemId()).getGiftBaseNum()))
                    .sum();
            countMap.put(groupId, (int) count);
        }
        return countMap;
    }

    private String generateExtendInfo(Integer maxCount, Integer curCount, List<GroupCurCount> countList) {
        if (giftGoods == null || CollectionUtils.isEmpty(giftGoods.getSkuGroupList())) {
            log.error("giftGoods is empty. promotionId:{}", promotionId);
            return null;
        }
        List<SkuGroup> skuGroupsList = giftGoods.getSkuGroupList();
        List<GiftBargainGroup> listInfo = skuGroupsList.get(0).getListInfo();
        if (CollectionUtils.isEmpty(listInfo)) {
            return null;
        }
        List<SkuGroupBean> skuGroupBeanList = checkAndConvertGroup(skuGroupsList, maxCount);

        // 获取skulist和list,兼容旧逻辑
        Map<String, String> skuMap = listInfo.stream().map(GiftBargainGroup::getSku).map(String::valueOf)
                .collect(Collectors.toMap(sku -> sku, sku -> sku, (val1, val2) -> val2));
        List<String> skuList = new ArrayList<>(skuMap.keySet());

        // 返回活动信息,对应promotion结构里的extend字段
        PromotionExtend expandInfo = new PromotionExtend();
        expandInfo.setCartPrice(listInfo.get(0).getCartPrice());
        expandInfo.setMaxCount(maxCount);
        expandInfo.setCurCount(curCount);
        expandInfo.setGroupCurCount(countList);
        expandInfo.setList(skuMap);
        expandInfo.setSkuList(skuList);
        expandInfo.setSelectType(giftGoods.getSelectType());
        expandInfo.setIgnoreStock(giftGoods.getIgnoreStock());
        expandInfo.setRefundValue(listInfo.get(0).getRefundValue());
        expandInfo.setAccessCode("");
        expandInfo.setSkuGroupsList(skuGroupBeanList);
        return GsonUtil.toJson(expandInfo);
    }

    private List<SkuGroupBean> checkAndConvertGroup(List<SkuGroup> skuGroupsList, Integer maxCount) {
        if (CollectionUtils.isEmpty(skuGroupsList)) {
            return Collections.emptyList();
        }
        List<SkuGroupBean> skuGroupBeanList = Lists.newArrayList();
        for (SkuGroup skuGroup : skuGroupsList) {
            Long groupId = skuGroup.getGroupId();
            List<GiftBargainGroup> listInfo = Optional.ofNullable(skuGroup.getListInfo()).orElse(Collections.emptyList());
            List<GiftBargainBean> giftBeanList = listInfo.stream().map(item -> convert(item, maxCount, groupId)).collect(Collectors.toList());
            boolean noneMatch = giftBeanList.stream().noneMatch(bean -> Objects.equals(BooleanV2Enum.YES.getValue(), bean.getActNumLimit()));
            if (noneMatch) {
                continue;
            }
            SkuGroupBean groupBean = new SkuGroupBean();
            groupBean.setGroupId(groupId);
            groupBean.setListInfo(giftBeanList);
            skuGroupBeanList.add(groupBean);
        }
        return skuGroupBeanList;
    }

    private GiftBargainBean convert(GiftBargainGroup item, Integer maxCount, Long groupId) {
        Integer limitNum = activityRedisDao.getActBuyGiftLimitNum(promotionId, String.valueOf(item.getSku()), groupId);
        int actNumLimit = BooleanV2Enum.NO.getValue();
        if ((item.getGiftLimitNum() - limitNum) >= maxCount * item.getGiftBaseNum()) {
            actNumLimit = BooleanV2Enum.YES.getValue();
        }
        GiftBargainBean bean = new GiftBargainBean();
        bean.setSku(item.getSku());
        bean.setMarketPrice(item.getMarketPrice());
        bean.setCartPrice(item.getCartPrice());
        bean.setShipmentType(item.getShipmentType());
        bean.setRefundValue(item.getRefundValue());
        bean.setActNumLimit(actNumLimit);
        bean.setBaseNum(item.getGiftBaseNum());
        return bean;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof BuyGiftPromotionConfig)) {
            log.error("config is not instanceof BuyGiftPromotionConfig. class:{}", config.getName());
            return;
        }
        BuyGiftPromotionConfig promotionConfig = (BuyGiftPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.giftGoods = promotionConfig.getGiftGoods();
        this.numLimitRule = promotionConfig.getNumLimitRule();
        this.isFMember = promotionConfig.getIsFMember();
        this.crowdList=promotionConfig.getCrowdList();
    }
}
