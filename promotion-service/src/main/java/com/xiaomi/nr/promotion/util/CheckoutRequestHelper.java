package com.xiaomi.nr.promotion.util;

import com.xiaomi.nr.promotion.api.dto.CartPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Request;
import com.xiaomi.nr.promotion.api.dto.GetProductCurrentPriceRequest;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;


import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

/**
 * 结算请求辅助工具
 *
 * <AUTHOR>
 */
public class CheckoutRequestHelper {
    /**
     * 结算V2请求参数转结算请求参数
     *
     * @param requestV2 结算V2请求
     * @return 结算请求
     */
    public static CheckoutPromotionRequest convertV2Request(CheckoutPromotionV2Request requestV2) {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        request.setSourceApi(SourceApi.CHECKOUT);
        request.setCartList(requestV2.getCartList());
        request.setClientId(requestV2.getClientId());
        request.setUserId(requestV2.getUserId());
        request.setCouponIds(requestV2.getCouponIds());
        request.setCouponCodes(requestV2.getCouponCodes());
        request.setEcardIds(requestV2.getEcardIds());
        request.setBargainSize(requestV2.getBargainSize());
        request.setEcardConsumeDetail(requestV2.getEcardConsumeDetail());
        request.setUseRedPacket(requestV2.getUseRedPacket());
        request.setCalculateRedpacket(requestV2.getCalculateRedpacket());
        request.setOrgCode(requestV2.getOrgCode());
        request.setUidType(requestV2.getUidType());
        request.setGlobalBusinessPartner(requestV2.getGlobalBusinessPartner());
        request.setShipmentExpense(requestV2.getShipmentExpense());
        request.setShipmentId(requestV2.getShipmentId());
        request.setUseBeijingcoupon(requestV2.getUseBeijingcoupon());
        request.setUserIsFriend(requestV2.getUserIsFriend());
        request.setThirdPromotions(requestV2.getThirdPromotions());
        request.setChannel(requestV2.getChannel());
        request.setUserLevel(requestV2.getUserLevel());
        request.setUsePoint(requestV2.getUsePoint());
        request.setUsedCouponId(requestV2.getUsedCouponId());
        request.setSubmitType(Optional.ofNullable(requestV2.getSubmitType()).orElse(0));
        request.setExtendOrderId(Optional.ofNullable(requestV2.getExtendOrderId()).orElse(Boolean.FALSE));
        if (Objects.equals(ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue(), requestV2.getChannel()) && StringUtils.isNotEmpty(requestV2.getVid())) {
            request.setVid(requestV2.getVid());
        }
        request.setIdCard(requestV2.getIdCard());
        request.setUsePurchaseSubsidy(requestV2.getUsePurchaseSubsidy());
        request.setRegion(requestV2.getRegion());
        request.setOrderTime(requestV2.getOrderTime());
        request.setWorkOrderType(requestV2.getWorkOrderType());
        request.setIsExchange(requestV2.getIsExchange());
        request.setIsFirstCarOwner(requestV2.getIsFirstCarOwner());
        request.setEquityKeys(requestV2.getEquityKeys());
        request.setActivityIds(requestV2.getActivityIds());
        request.setVid(requestV2.getVid());
        return request;
    }

    public static CheckoutPromotionRequest generateRequestFromCart(CartPromotionRequest cartRequest) {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        request.setSourceApi(SourceApi.CHECKOUT);
        request.setCartList(cartRequest.getCartList());
        request.setClientId(cartRequest.getClientId());
        request.setUserId(cartRequest.getUserId());
        request.setCouponIds(cartRequest.getCouponIds());
        request.setCouponCodes(Collections.emptyList());
        request.setBargainSize(cartRequest.getBargainSize());
        request.setEcardConsumeDetail(Collections.emptyMap());
        request.setUseRedPacket(false);
        request.setCalculateRedpacket(false);
        request.setOrgCode(cartRequest.getOrgCode());
        request.setUidType(cartRequest.getUidType());
        request.setGlobalBusinessPartner(cartRequest.getGlobalBusinessPartner());
        request.setUserIsFriend(cartRequest.getUserIsFriend());
        request.setChannel(cartRequest.getChannel());
        request.setUserLevel(cartRequest.getUserLevel());
        request.setUsedCouponId(cartRequest.getUsedCouponId());
        request.setSubmitType(Optional.ofNullable(cartRequest.getSubmitType()).orElse(0));
        request.setOrderTime(cartRequest.getOrderTime());
        request.setVid(cartRequest.getVid());
        return request;
    }

    public static CheckoutPromotionV2Request generateRequestV2FromCart(CartPromotionRequest cartRequest) {
        CheckoutPromotionV2Request requestV2 = new CheckoutPromotionV2Request();
        requestV2.setGetCouponList(cartRequest.getGetCouponList());
        requestV2.setShowType(cartRequest.getShowType());
        requestV2.setUseDefaultCoupon(cartRequest.getUseDefaultCoupon());
        requestV2.setBargainSize(cartRequest.getBargainSize());
        requestV2.setCartList(cartRequest.getCartList());
        requestV2.setClientId(cartRequest.getClientId());
        requestV2.setUserId(cartRequest.getUserId());
        requestV2.setCouponIds(cartRequest.getCouponIds());
        requestV2.setCouponCodes(Collections.emptyList());
        requestV2.setBargainSize(cartRequest.getBargainSize());
        requestV2.setEcardConsumeDetail(Collections.emptyMap());
        requestV2.setUseRedPacket(false);
        requestV2.setCalculateRedpacket(false);
        requestV2.setOrgCode(cartRequest.getOrgCode());
        requestV2.setUidType(cartRequest.getUidType());
        requestV2.setGlobalBusinessPartner(cartRequest.getGlobalBusinessPartner());
        requestV2.setChannel(cartRequest.getChannel());
        requestV2.setUserLevel(cartRequest.getUserLevel());
        requestV2.setUsedCouponId(cartRequest.getUsedCouponId());
        requestV2.setSubmitType(Optional.ofNullable(cartRequest.getSubmitType()).orElse(0));
        requestV2.setOrderTime(cartRequest.getOrderTime());
        requestV2.setVid(cartRequest.getVid());
        return requestV2;
    }




    /**
     * 价保请求入参转结算请求入参
     *
     * @param request 价保请求入参
     * @return 结算请求入参
     */
    public static CheckoutPromotionRequest convertProtectPriceRequest(GetProductCurrentPriceRequest request) {
        CheckoutPromotionRequest checkoutPromotionRequest = new CheckoutPromotionRequest();
        checkoutPromotionRequest.setSourceApi(SourceApi.CHECKOUT);
        checkoutPromotionRequest.setUserId(request.getUserId());
        checkoutPromotionRequest.setClientId(request.getClientId());
        checkoutPromotionRequest.setCartList(request.getCartList());

        if (CollectionUtils.isNotEmpty(request.getCouponIds())) {
            checkoutPromotionRequest.setCouponIds(request.getCouponIds());
        } else if (request.getCouponId() != 0) {
            checkoutPromotionRequest.setCouponIds(Collections.singletonList(request.getCouponId()));
        }
        if (StringUtils.isNotEmpty(request.getCouponCode())) {
            checkoutPromotionRequest.setCouponCodes(Collections.singletonList(request.getCouponCode()));
        }
        checkoutPromotionRequest.setOrderId(request.getOrderId());
        return checkoutPromotionRequest;
    }
}
