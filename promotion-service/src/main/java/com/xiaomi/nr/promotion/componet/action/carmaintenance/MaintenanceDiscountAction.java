package com.xiaomi.nr.promotion.componet.action.carmaintenance;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.componet.action.AbstractAction;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.redis.QuotaEle;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.PolicyQuotaTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MaintenanceDiscountPromotionConfig;
import com.xiaomi.nr.promotion.tool.CheckoutCartToolV2;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description 汽车维保-会员折扣活动
 * @date 2025-01-03 15:05
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class MaintenanceDiscountAction extends AbstractAction {

    /**
     * 活动ID
     */
    private Long promotionId;

    /**
     * 会员ID
     */
    private Integer vipLevel;

    /**
     * 商品黑名单
     */
    private List<Long> invalidGoods;

    /**
     * 用户参与次数限制
     */
    private Integer userJoinNumLimit;

    /**
     * 折扣阶梯
     */
    private List<QuotaLevel> levelList;

    @Autowired
    private CheckoutCartToolV2 checkoutCartToolV2;

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {

        List<GoodsIndex> indexList = context.getGoodIndex();
        List<CartItem> cartList = request.getCartList();

        if (CollectionUtils.isNotEmpty(indexList) && request.getActivityIds().contains(promotionId)) {

            ValidGoods validGoods = CartHelper.buildValidGoods2(cartList, indexList);
            QuotaLevel level = getMatchSatisfiedQuota(levelList, validGoods);
            if (CollectionUtils.isEmpty(indexList) || level == null) {
                log.error("maintenanceDiscountAction context indexList is empty or level is null. actId:{}, uid:{} indexList:{}", promotionId, request.getUserId(), indexList);
                return;
            }

            // 计算实际总折扣金额
            long reduceDiscount = level.getReduceDiscount();
            long maxReducePrice = level.getMaxReducePrice();
            long realTotalReduceMoney = calculateDiscountMoney(validGoods.getValidPrice(), reduceDiscount, maxReducePrice);

            // 处理分摊，分摊购物车减免金额
            List<Integer> cartIndexList = getCartIndexList(indexList);
            checkoutCartToolV2.divideCartsReduce(realTotalReduceMoney, cartIndexList, cartList, ActivityTypeEnum.MAINTENANCE_REPAIR_DISCOUNT.getValue(), promotionId);

            // 初始化资源
            if (request.getSourceApi() == SourceApi.SUBMIT) {
                initUidActivityCountResource(request, context, promotionId, vipLevel, userJoinNumLimit);
            }

            // content
            context.setActivityChecked(Boolean.TRUE);
        }

        // 组织优惠信息promotionInfo，填充到context
        setResult(context, cartList, promotion);

    }

    private QuotaLevel getMatchSatisfiedQuota(List<QuotaLevel> levelList, ValidGoods validGoods) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(levelList)) {
            log.error("act levelList is empty. actId:{}", promotionId);
            return null;
        }
        return levelList.stream()
                .filter(level -> level.getQuotas().stream().anyMatch(quotaEle -> isSatisfiedQuota(quotaEle, validGoods)))
                .findFirst().orElse(null);
    }

    private boolean isSatisfiedQuota(QuotaEle quotaEle, ValidGoods validGoods) {
        PolicyQuotaTypeEnum quotaType = PolicyQuotaTypeEnum.getQuotaType(quotaEle.getType());
        if (quotaType == null) {
            return false;
        }
        boolean isSatisfied = false;
        switch (quotaType) {
            case POLICY_QUOTA_MONEY:
            case POLICY_QUOTA_PER_MONEY:
            case POLICY_QUOTA_NUM:
            case POLICY_QUOTA_PER_NUM:
                isSatisfied = validGoods.getValidNum() >= quotaEle.getCount();
                break;
            default:
                log.error("act policy quota type is wrong. actId:{}", promotionId);
                break;
        }
        return isSatisfied;
    }

    private long calculateDiscountMoney(long totalPrice, long reduceDiscount, long maxReducePrice) {
        //打折后的价格
        long newPrice = totalPrice * reduceDiscount / 100;
        long reduceMoney = totalPrice - newPrice;
        reduceMoney = Math.min(reduceMoney, totalPrice);

        //最高可以减免的钱
        if (maxReducePrice > 0) {
            reduceMoney = Math.min(maxReducePrice, reduceMoney);
        }
        return reduceMoney;
    }

    private void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool) throws BizError {
        List<GoodsIndex> indexList = context.getGoodIndex();
        List<String> joinedItemIdList = CartHelper.getJoinedItemIdList(indexList, cartList, promotionId);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setParentItemId(CartHelper.getParentItemIdList(indexList));
        promotionInfo.setJoinedItemId(joinedItemIdList);
        promotionInfo.setExtend(Strings.EMPTY);
        promotionInfo.setJoined(CollectionUtils.isNotEmpty(joinedItemIdList) ? 1 : 0);
        promotionInfo.setInValidGoodsList(invalidGoods);
        promotionInfo.setCanSelect(CollectionUtils.isNotEmpty(context.getGoodIndex()));
        promotionInfo.setChecked(context.getActivityChecked());

        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof MaintenanceDiscountPromotionConfig)) {
            return;
        }
        MaintenanceDiscountPromotionConfig promotionConfig = (MaintenanceDiscountPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.vipLevel = promotionConfig.getCarIdentityId();
        this.invalidGoods = promotionConfig.getInvalidGoods();
        this.userJoinNumLimit = promotionConfig.getUserJoinNumLimit();
        this.levelList = promotionConfig.getLevelList();
    }
}
