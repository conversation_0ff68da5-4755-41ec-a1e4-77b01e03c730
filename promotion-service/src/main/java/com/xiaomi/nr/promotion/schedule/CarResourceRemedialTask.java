package com.xiaomi.nr.promotion.schedule;

import com.xiaomi.hera.trace.annotation.Trace;
import com.xiaomi.nr.infra.aop.clustertask.ClusterTask;
import com.xiaomi.nr.promotion.annotation.load.PromotionCondition;
import com.xiaomi.nr.promotion.annotation.load.PromotionLoadConstants;
import com.xiaomi.nr.promotion.resource.impl.ResourceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 汽车资源补偿任务
 *
 * <AUTHOR>
 * @date 2025/6/3
 */
@Component
@Slf4j
@PromotionCondition(value = {PromotionLoadConstants.CAR_BIZ})
public class CarResourceRemedialTask {
    /**
     * 资源补偿分布式锁
     */
    private final static String LOCK_RESOURCE_REMEDIAL = "/cnzone/config/nr-mid-platform/promotion/car_remedial_task_lock";

    /**
     * 执行锁，保证一个同一时间只有一个调度任务在执行
     */
    private final Lock lock = new ReentrantLock();

    @Autowired
    private ResourceManager resourceManager;

    /**
     * 汽车集群补偿任务：每分钟执行一次
     */
    @Trace
    @Scheduled(fixedDelay = 1000 * 60, initialDelay = 1000 * 60)
    @ClusterTask(value = "", zkLockPath = LOCK_RESOURCE_REMEDIAL)
    public void remedialResource() {
        long startTime = System.currentTimeMillis();
        log.info("car remedialResource start. startTime={}", startTime);
        try {
            if (lock.tryLock()) {
                try {
                    resourceManager.carRemedial();
                    log.info("car remedialResource end success . ws={}", System.currentTimeMillis() - startTime);
                } finally {
                    lock.unlock();
                }
            } else {
                log.warn("car remedialResource locked. ws={}", System.currentTimeMillis() - startTime);
            }
        } catch (Exception e) {
            log.error("car remedialResource err.", e);
        }
    }
}
