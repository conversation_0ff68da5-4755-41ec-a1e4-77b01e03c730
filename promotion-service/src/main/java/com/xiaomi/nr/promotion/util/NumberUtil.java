package com.xiaomi.nr.promotion.util;

import org.apache.dubbo.common.utils.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Objects;
import java.util.function.Function;

/**
 * 数字类型工具类
 *
 * <AUTHOR>
 * @date 2021/4/2
 */
public class NumberUtil {

    /**
     * 所有数据进行汇总Long
     *
     * @param nums 数据集, 遇到NULL转为0
     * @return 汇总后
     */
    public static long sum(Long... nums) {
        if (nums == null || nums.length == 0) {
            return 0L;
        }
        long total = 0L;
        for (Long num : nums) {
            total += (num == null ? 0L : num);
        }
        return total;
    }

    /**
     * 所有数据进行汇总Integer
     *
     * @param nums 数据集, 遇到NULL转为0
     * @return 汇总后
     */
    public static int sum(Integer... nums) {
        if (nums == null || nums.length == 0) {
            return 0;
        }
        int total = 0;
        for (Integer num : nums) {
            total += (num == null ? 0 : num);
        }
        return total;
    }

    /**
     * 是否在范围内
     *
     * @param target 目标数
     * @param begin  开始
     * @param end    结束
     * @return 左闭右闭
     */
    public static boolean inRange(Long target, long begin, long end) {
        if (target < begin) {
            return false;
        }
        if (target > end) {
            return false;
        }
        return true;
    }

    /**
     * 分转为元
     *
     * @param fen 分
     */
    public static BigDecimal amountConvertF2Y(Long fen) {
        BigDecimal bd = new BigDecimal(100);
        return BigDecimal.valueOf(fen).divide(bd, 2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 折扣由Long转String，保留一位小数
     *
     * @param discount 折扣
     */
    public static String disCountConvertStr(Long discount) {
        BigDecimal bd = new BigDecimal(10);
        BigDecimal divide = BigDecimal.valueOf(discount).divide(bd, 1, BigDecimal.ROUND_HALF_UP);
        return String.valueOf(divide);
    }

    /**
     * 元转为分
     *
     * @param yuan 元
     */
    public static Long amountConvertY2F(BigDecimal yuan) {
        BigDecimal bd = new BigDecimal(100);
        return yuan.multiply(bd).setScale(2, BigDecimal.ROUND_HALF_UP).longValue();
    }

    /**
     * 分转为元 默认0舍去
     */
    public static String amountConvertF2YStr(Long fen) {
        BigDecimal bd = new BigDecimal(100);
        return BigDecimal.valueOf(fen).divide(bd, 2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
    }
}
