package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.model.promotionconfig.common.GoodsReduceInfo;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 主品资源立减
 *
 * <AUTHOR>
 * @date 2022/7/14
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class GoodsLimitResourceProvider implements ResourceProvider<GoodsLimitResourceProvider.ResContent> {
    private ResourceObject<GoodsLimitResourceProvider.ResContent> resourceObject;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Autowired
    private NacosConfig nacosConfig;

    @Override
    public ResourceObject<GoodsLimitResourceProvider.ResContent> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<GoodsLimitResourceProvider.ResContent> object) {
        this.resourceObject = object;
    }

    @Override
    public void lock() throws BizError {
        List<String> skuPackageList = resourceObject.getContent().skuPackageList;
        log.info("lock goods act limit resource. orderId:{}, promotionId:{} skuPackageList:{}", resourceObject.getOrderId(), resourceObject.getPromotionId(), skuPackageList);
        if (CollectionUtils.isEmpty(skuPackageList)) {
            log.warn("skuPackageList is empty. resourceObject:{}", resourceObject);
            return;
        }
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("GoodsLimitResourceProvider.lock(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        skuPackageList.forEach(skuPackage -> activityRedisDao.incrActGoodsLimitNum(resourceObject.getPromotionId(), skuPackage, 1));
        log.info("lock goods act limit ok. resourceObject:{}", resourceObject);
    }

    @Override
    public void consume() {
        log.info("consume act bargain limit resource. resourceObject{}", resourceObject);
    }

    @Override
    public void rollback() throws BizError {
        List<String> skuPackageList = resourceObject.getContent().skuPackageList;
        log.info("rollback goods act limit resource. resourceObject:{}", resourceObject);
        if (CollectionUtils.isEmpty(skuPackageList)) {
            log.warn("rollback goods skuPackageList is empty. resourceObject:{}", resourceObject);
            return;
        }
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("GoodsLimitResourceProvider.rollback(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        skuPackageList.forEach(skuPackage -> activityRedisDao.decrActGoodsLimitNum(resourceObject.getPromotionId(), skuPackage, 1));
        log.info("rollback goods act limit ok. resourceObject:{}", resourceObject);
    }

    @Override
    public String conflictText() {
        return "处理数据失败";
    }

    @Data
    public static class ResContent {
        /**
         * 扣减次数商品
         */
        private List<String> skuPackageList;

        /**
         * 下单立减优惠信息
         * <p>
         * key: skuPackage val:GoodsReduceInfo
         */
        private Map<String, GoodsReduceInfo> buyReduceInfoMap;
    }
}
