package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionUserActivityCountMapper;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.UserActivityCount;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.mq.producer.CarActivityUseProducer;
import com.xiaomi.nr.promotion.mq.producer.entity.CarActivityUseMessage;
import com.xiaomi.nr.promotion.mq.producer.entity.Header;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025-01-04 15:59
*/
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class UserActivityCountProvider implements ResourceProvider<UserActivityCountProvider.UserJoinActNum> {

    private ResourceObject<UserActivityCountProvider.UserJoinActNum> resourceObject;

    @Autowired
    private PromotionUserActivityCountMapper userActivityCountMapper;

    @Autowired
    private CarActivityUseProducer carActivityUseProducer;

    @Value("${spring.profiles.active:}")
    private String profile;

    @Override
    public ResourceObject<UserJoinActNum> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<UserActivityCountProvider.UserJoinActNum> object) {
        this.resourceObject = object;
    }

    @Override
    public void lock() throws BizError {
        long currentTime = System.currentTimeMillis() / 1000;
        UserActivityCountProvider.UserJoinActNum content = resourceObject.getContent();
        UserActivityCount userActivityCount = userActivityCountMapper.getByUserIdAndPromotionId(content.getUid(), content.getActId());
        if (content.userJoinNumLimit > 0) {
            Integer userJoinNum = Optional.ofNullable(userActivityCount).map(UserActivityCount::getNum).orElse(0);
            if (userJoinNum >= content.userJoinNumLimit) {
                log.error("UserActivityCountProvider lock failed. user join num is over limit. uid:{} actId:{} userJoinNum:{}", content.getUid(), content.getActId(), userJoinNum);
                throw ExceptionHelper.create(GeneralCodes.InternalError, "用户参与次数超过限制");
            }
        }
        if (Objects.isNull(userActivityCount)) {
            userActivityCount = new UserActivityCount();
            userActivityCount.setUserId(content.getUid());
            userActivityCount.setPromotionId(content.getActId());
            userActivityCount.setNum(1);
            userActivityCount.setCreateTime(currentTime);
            userActivityCount.setUpdateTime(currentTime);
            userActivityCount.setExtend(GsonUtil.toJson(content));
            userActivityCountMapper.insert(userActivityCount);
        } else {
            int num = userActivityCount.getNum();
            userActivityCountMapper.updateCount(content.getUid(), content.getActId(), num + 1, num, GsonUtil.toJson(content), currentTime);
        }
        log.info("lock UserActivityCountProvider resource, resourceObject:{}", GsonUtil.toJson(resourceObject));

        // SVIP发会员核销mq，失败打日志即可
        CarActivityUseMessage carActivityUseMessage = CarActivityUseMessage.builder()
                .header(Header.builder().version("1.0").profile(profile).build())
                .body(CarActivityUseMessage.Body.builder()
                        .unikey(UUID.randomUUID().toString().replace("-", ""))
                        .promotionId(content.getActId())
                        .userId(content.getUid())
                        .bizPlatform(BizPlatformEnum.CAR_SHOP.getValue())
                        .orderId(resourceObject.getOrderId())
                        .joinTimes(userActivityCount.getNum() + 1)
                        .changeTimeMillis(currentTime)
                        .orderRefund(false)
                        .build())
                .build();
        carActivityUseProducer.sendMessage(carActivityUseMessage);
    }

    @Override
    public void consume() throws BizError {
        log.info("consume UserActivityCountProvider resource, resourceObject:{}", GsonUtil.toJson(resourceObject));
    }

    @Override
    public void rollback() throws BizError {
        long currentTime = System.currentTimeMillis() / 1000;
        UserActivityCountProvider.UserJoinActNum content = resourceObject.getContent();
        UserActivityCount userActivityCount = userActivityCountMapper.getByUserIdAndPromotionId(content.getUid(), content.getActId());
        if (userActivityCount == null) {
            return;
        }
        int num = userActivityCount.getNum();
        if (num < 1) {
            return;
        }
        userActivityCountMapper.updateCount(content.getUid(), content.getActId(), num - 1, num, GsonUtil.toJson(content), currentTime);
        log.info("rollback UserActivityCountProvider resource, resourceObject:{}", GsonUtil.toJson(resourceObject));

        // 发回滚消息，失败打日志即可
        CarActivityUseMessage carActivityUseMessage = CarActivityUseMessage.builder()
                .header(Header.builder().version("1.0").profile(profile).build())
                .body(CarActivityUseMessage.Body.builder()
                        .unikey(UUID.randomUUID().toString().replace("-", ""))
                        .promotionId(content.getActId())
                        .userId(content.getUid())
                        .bizPlatform(BizPlatformEnum.CAR_SHOP.getValue())
                        .orderId(resourceObject.getOrderId())
                        .joinTimes(num - 1)
                        .changeTimeMillis(currentTime)
                        .orderRefund(true)
                        .build())
                .build();
        carActivityUseProducer.sendMessage(carActivityUseMessage);
    }

    @Override
    public String conflictText() {
        return "维修折扣活动资源失败";
    }

    @Data
    public static class UserJoinActNum {

        /**
         * 用户ID
         */
        private Long uid;

        /**
         * 活动ID
         */
        private Long actId;

        /**
         * VID
         */
        private String vid;

        /**
         * 会员ID
         */
        private Integer vipLevel;

        /**
         * 用户参与活动次数限制
         */
        private Integer userJoinNumLimit;
    }
}
