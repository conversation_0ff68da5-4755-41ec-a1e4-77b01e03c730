package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.nr.mropolicy.api.dto.workhour.WorkHourDetailDto;
import com.xiaomi.nr.mropolicy.api.dto.workhour.WorkHourDto;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.enums.BizSubTypeEnum;
import com.xiaomi.nr.promotion.domain.workhour.service.common.WorkHourInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 工时去重外部接口
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class WorkHourExternalProvider extends ExternalDataProvider<Map<Long, WorkHourDetailDto>>{

    @Autowired
    private WorkHourInfoService  workHourInfoService;

    private ListenableFuture<Map<Long, WorkHourDetailDto>> future;

    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        // 过滤购物车，只处理工时
        List<CartItem> cartList = request.getCartList();
        List<WorkHourDto> workHourList=new ArrayList<>();
        cartList.forEach(po->{
            if (Objects.equals(po.getBizSubType(), BizSubTypeEnum.CAR_WORK_HOUR.getCode())){
                WorkHourDto workHourDto=new WorkHourDto();
                workHourDto.setSsuId(po.getSsuId());
                workHourDto.setHourType(po.getPayType());
                workHourList.add(workHourDto);
            }
        });
        if (workHourList.isEmpty()){
            return;
        }
        // 获取工时去重结果
        future = workHourInfoService.getUnDuplicatedWorkHourInfoAsync(workHourList);
    }

    @Override
    protected long getTimeoutMills() {
        return DEFAULT_TIMEOUT;
    }

    @Override
    public ListenableFuture<Map<Long, WorkHourDetailDto>> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.UN_DUPLICATED_WORK_HOUR;
    }
}
