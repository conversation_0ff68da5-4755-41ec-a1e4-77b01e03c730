package com.xiaomi.nr.promotion.componet.action;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.componet.Action;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MultiPromotionConfig;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import lombok.extern.slf4j.Slf4j;

/**
 * 限制券的使用: 主要为赠品加价购
 *
 * <AUTHOR>
 * @date 2021/6/3
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CouponLimitAction extends Action {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 是否可使用优惠券
     */
    private boolean couponLimit;

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (StringUtils.isEmpty(request.getOrgCode()) || couponLimit) {
            return;
        }

        List<CartItem> cartList = request.getCartList();
        // 是当前活动，则设置为不可使用优惠券
        List<CartItem> actCartList = cartList.stream()
                .filter(item -> Objects.equals(item.getSourceCode(), String.valueOf(promotionId)))
                .collect(Collectors.toList());
        actCartList.forEach(item -> item.setCannotUseCoupon(true));
        if (CollectionUtils.isEmpty(actCartList)) {
            return;
        }

        // 对符合主商品之外的主商品添加不可使用优惠券
        List<GoodsIndex> fillGoodsList = Optional.ofNullable(context.getGoodIndex()).orElse(Collections.emptyList());
        fillGoodsList.forEach(goodsIndex -> {
            if (goodsIndex.getIndex() >= cartList.size()) {
                return;
            }
            CartItem item = cartList.get(goodsIndex.getIndex());
            item.setCannotUseCoupon(true);
        });
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof MultiPromotionConfig)) {
            return;
        }
        MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.couponLimit = promotionConfig.isCouponLimit();
    }
}
