package com.xiaomi.nr.promotion.domain.coupon.facade;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Request;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.service.base.processor.GoodsCouponProcessor;
import com.xiaomi.nr.promotion.domain.coupon.service.common.CouponInfoService;
import com.xiaomi.nr.promotion.engine.CouponTool;
import com.xiaomi.nr.promotion.entity.redis.OrgInfo;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.CouponGeneralType;
import com.xiaomi.nr.promotion.enums.CouponPromotionType;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.resource.external.OrgInfoExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 车商城券-结算工具
 */
@Slf4j
@Component
public class CarShopCouponFacade extends CouponFacade {

    @Autowired
    @Qualifier("couponInfoServiceRemoteImpl")
    private CouponInfoService couponInfoService;

    @Autowired
    private GoodsCouponProcessor goodsCouponProcessor;

    @Autowired
    private NacosConfig nacosConfig;

    /**
     * 券加购结算逻辑
     */
    public void checkoutForCouponList(CheckoutPromotionV2Request request, CheckoutPromotionResponse response, CheckoutContext context) throws
            BizError {
        try {
            // 没传用户ID暂时不处理券
            if (request.getUserId() == null || request.getUserId() == 0L) {
                return;
            }
            // 限制业务领域
            if (context.getBizPlatform() != BizPlatformEnum.CAR_SHOP) {
                return;
            }
            // 降级开关
            if (nacosConfig.isCarShopCouponDegrade()) {
                processEmptyCoupon(request, context);
                return;
            }
            // 获取门店信息
            OrgInfo orgInfo = new OrgInfo();
            if (StringUtils.isNotEmpty(request.getOrgCode())) {

                OrgInfoExternalProvider externalDataProvider = (OrgInfoExternalProvider) context.getExternalDataMap().get(ResourceExtType.ORG_INFO);
                orgInfo = externalDataProvider.getFuture().get(2000, TimeUnit.MILLISECONDS);
            }
            // 调券三方接口，获取用户券列表
            request.setFromInterface(context.getFromInterface().getValue());
            ListenableFuture<List<CheckoutCoupon>> couponForCheckout = couponInfoService.getCouponForCheckout(request, orgInfo.getOrgType(), request.getGetCouponList());
            List<CheckoutCoupon> checkoutCouponList = couponForCheckout.get(2000, TimeUnit.MILLISECONDS);
            log.info("CarShopCouponFacade checkoutForCouponList checkoutCouponList:{}", GsonUtil.toJson(checkoutCouponList));

            // 选择最优券
            List<Long> bestCouponList = getBestCoupon(request, context, checkoutCouponList);

            // 选择默认选中券
            List<Coupon> finalCouponList = getCouponList(request, context, checkoutCouponList);

            //最终排序
            finalCouponList.sort(Comparator.comparingInt(o -> CouponGeneralType.valueOf(o.getCouponType()).getSortValue()));
            finalCouponList.forEach(coupon -> {
                if (bestCouponList.contains(coupon.getId())) {
                    List<String> tags = Optional.ofNullable(coupon.getTags()).orElse(new ArrayList<>());
                    tags.add("optimal");
                    coupon.setTags(tags);
                }
            });
            context.setCouponList(finalCouponList);
        } catch (Exception e) {
            log.error("CarShopCouponFacade checkoutForCouponList error. request:{}", GsonUtil.toJson(request), e);
        }

    }

    /**
     * 券下单结算逻辑，按要求结算指定优惠券
     */
    public void checkoutForSubmit(CheckoutPromotionRequest request, CheckoutPromotionResponse response, CheckoutContext context) throws Exception {

        // 限制业务场景
        if (context.getBizPlatform() != BizPlatformEnum.CAR_SHOP) {
            processEmptyCoupon(request, context);
            return;
        }
        // 降级开关
        if (nacosConfig.isCarShopCouponDegrade()) {
            processEmptyCoupon(request, context);
            return;
        }
        // 客户端传券信息无效
        if (CollectionUtils.isEmpty(request.getCouponIds())) {
            processEmptyCoupon(request, context);
            return;
        }
        // 客户端传明码券报错
        if (CollectionUtils.isNotEmpty(request.getCouponCodes())) {
            throw ExceptionHelper.create(ErrCode.ERR_COUPON_MONEY_NUM, "无效的结算类型");
        }
        // 获取门店信息
        OrgInfo orgInfo = new OrgInfo();
        if (StringUtils.isNotEmpty(request.getOrgCode())) {
            OrgInfoExternalProvider externalDataProvider = (OrgInfoExternalProvider) context.getExternalDataMap().get(ResourceExtType.ORG_INFO);
            orgInfo = externalDataProvider.getFuture().get(2000, TimeUnit.MILLISECONDS);
        }
        // 调券三方接口，获取券信息
        ListenableFuture<List<CheckoutCoupon>> couponForCheckout = couponInfoService.getCouponForCheckout(request, orgInfo.getOrgType(), false);
        List<CheckoutCoupon> checkoutCouponList = couponForCheckout.get(2000, TimeUnit.MILLISECONDS);
        log.info("CarShopCouponFacade checkoutForSubmit checkoutCouponList:{}", GsonUtil.toJson(checkoutCouponList));

        // 校验优惠券数量
        checkCouponCount(request, checkoutCouponList);
        try {
            Map<Long, CheckoutCoupon> coupons = checkoutCouponList.stream().collect(Collectors.toMap(CheckoutCoupon::getCouponId, Function.identity()));//<券id，券对象>

            //生成券对应的券优惠工具对象，加载数据
            List<CouponTool> couponTools = goodsCouponProcessor.loadCouponToolsForCheckout(checkoutCouponList, context.getBizPlatform());

            Map<Long, CouponTool> couponToolMap = couponTools.stream().collect(Collectors.toMap(CouponTool::getCouponId, Function.identity()));//<券id，券工具对象>

            //客户端传递券非空
            if (CollectionUtils.isNotEmpty(request.getCouponIds())) {

                long couponId = request.getCouponIds().get(0);
                CheckoutCoupon checkoutCoupon = coupons.get(couponId);//客户端传递的券对应的券对象

                CouponTool selectCouponTool = couponToolMap.get(checkoutCoupon.getCouponId());//客户端传递的券对应的券工具对象
                //使用指定券进行结算
                CouponCheckoutResult result = checkoutSelectCoupon(request, context, selectCouponTool, goodsCouponProcessor, orgInfo);

                //生成context.carts
                if (request.getSourceApi() == SourceApi.SUBMIT) {
                    selectCouponTool.updateItemReduce(context.getCarts(), result);
                }
            }

            if (context.getCarts() == null) {
                List<CartItem> simpleCarts = copyList(request.getCartList());
                context.setCarts(simpleCarts);
            }
        } catch (Exception e) {
            log.error("CarShopCouponFacade checkoutForSubmit Error. request:{} ", GsonUtil.toJson(request), e);
            if (e instanceof BizError) {
                context.setCouponBaseInfoInvalid(((BizError) e).getMsg());
            }
            if (request.getSourceApi() == SourceApi.SUBMIT) {
                log.error("CarShopCouponFacade checkoutForSubmit error. request:{}", request, e);
                throw e;
            }
            processEmptyCoupon(request, context);
        }

    }

    @Override
    protected void checkCouponCount(CheckoutPromotionRequest request, List<CheckoutCoupon> checkoutCouponList) throws BizError {
        if (request.getCouponIds().size() > 1) {
            throw ExceptionHelper.create(ErrCode.ERR_TOO_MANY_COUPONS, "同种类型最多只能使用一张优惠券");
        }

        // 兑换场景
        if (request.getIsExchange()) {
            // 入参校验已经限制入参couponIds存在且size为1，此时列表为空，证明券id无效
            if (CollectionUtils.isEmpty(checkoutCouponList)) {
                throw ExceptionHelper.create(ErrCode.ERR_COUPON_INVALID, "无效优惠券");
            }
            // 兜底冗余代码
            if (checkoutCouponList.size() > 1) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "兑换场景只能使用一张券");
            }
            // 兑换场景只能用兑换券
            if (!Objects.equals(CouponPromotionType.Gift.getCode(), checkoutCouponList.getFirst().getPromotionType())) {
                throw ExceptionHelper.create(ErrCode.ERR_COUPON_PROMOTION_TYPE_MISMATCH, "券优惠类型不匹配");
            }
        }
    }

}
