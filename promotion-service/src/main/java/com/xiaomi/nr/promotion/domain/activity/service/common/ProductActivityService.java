package com.xiaomi.nr.promotion.domain.activity.service.common;

import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.nr.promotion.api.dto.model.GetProductGoodsActRequestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

/**
 * 产品活动接口
 *
 * <AUTHOR>
 * @date 2021/6/15
 */
public interface ProductActivityService {

    /**
     * 产品站批量获取可参加进行中的活动
     * @param request 请求参数
     * @return 响应参数
     */
    MultiProductGoodsActResponse getMultiProductGoodsAct(MultiProductGoodsActRequest request) throws BizError;
    /**
     * 获取产品站活动接口：pos2.0
     *
     * @param request 请求参数
     * @return 响应参数
     * @throws BizError 业务异常
     */
    GetProductGoodsActResponse getProductGoodsAct(GetProductGoodsActRequest request) throws BizError;

    /**
     * 获取产品站活动接口：pos2.0
     *
     * @param request 请求参数
     * @return 响应参数
     * @throws BizError 业务异常
     */
    GetProductGoodsActResponse getProductGoodsActV2(GetProductGoodsActRequestV2 request) throws BizError;

    /**
     * 获取商品可参加即将开始活动
     *
     * @param request 请求参数
     * @return 响应
     * @throws BizError 业务异常
     */
    GetPreProductGoodsActResponse getPreProductGoodsAct(GetPreProductGoodsActRequest request) throws BizError;

    /**
     * 获取商品活动价
     * 现阶段只有直降
     *
     * @param request 请求参数
     * @return 价格Map
     * @throws BizError 业务异常
     */
    GetProductActPriceResponse getProductActPrice(GetProductActPriceRequest request) throws BizError;

    /**
     * 获取商品活动价详情列表
     * 现阶段只有直降
     *
     * @param request 请求参数
     * @return 价格Map
     * @throws BizError 业务异常
     */
    GetProductActPriceDetailResponse getProductActPriceDetail(GetProductActPriceDetailRequest request) throws BizError;

    /**
     * 获取产品站活动
     *
     * @param request 请求参数
     * @return 活动列表信息
     * @throws BizError 业务异常
     */
    GetProductActResponse getProductAct(GetProductActRequest request) throws BizError;

    /**
     * 获取商城产品站活动（新版，支持批量商品，getProductAct 的升级版）
     *
     * @param request 请求参数
     * @return 活动列表信息
     * @throws BizError 业务异常
     */
    GetProductActV2Response getProductActV2(GetProductActV2Request request) throws BizError;

    /**
     * 获取活动详情
     *
     * @param request 请求参数
     * @return 活动详情
     * @throws BizError 业务异常
     */
    GetActivityDetailResponse getActivityDetail(GetActivityDetailRequest request) throws BizError;

    /**
     * 获取活动详情  根据是否传入区域信息，获取库存信息
     *
     * @param request 请求参数
     * @return 活动详情
     * @throws BizError 业务异常
     */
    GetActivityAreaDetailResponse getActivityAreaDetail(GetActivityAreaDetailRequest request) throws BizError;

    /**
     * 获取门店商品活动价 (直降 | 门店价）
     * <p>
     * 注：全量获取门店下已经配置了活动价（直降｜门店价）的商品价
     * </p>
     *
     * @param request 请求参数
     * @return 响应
     * @throws BizError 业务异常
     */
    GetStoreActPriceResponse getStoreActPrice(GetStoreActPriceRequest request) throws BizError;

    /**
     * 获取门店商品活动价 (直降 | 门店价）
     * <p>
     * 注：全量获取门店下已经配置了活动价（直降｜门店价）的商品价
     * </p>
     *
     * @param request 请求参数
     * @return 响应
     * @throws BizError 业务异常
     */
    GetStoreGoodsActPriceResponse getStoreGoodsActPrice(GetStoreGoodsActPriceRequest request) throws BizError;

    /**
     * 获取活动售价
     *
     * @param request 请求对象
     * @return 活动价格数据
     */
    GetGoodsActPriceResponse getGoodsActPrice(GetGoodsActPriceRequest request) throws BizError;

    /**
     * 获取商品有效活动列表
     */
    GetActivitysByGoodsResponse getActivitysByGoods(GetActivitysByGoodsRequest request) throws BizError;

    /**
     * 加购时校验商品参加活动的有效性
     *
     * @param request 请求参数
     * @return 响应
     * @throws BizError 业务异常
     */
    CheckProductsActResponse checkProductsAct(CheckProductsActRequest request) throws BizError;

    /**
     * 获取商品活动价V2
     * 现阶段只有直降
     *
     * @param request 请求参数
     * @return 价格Map
     * @throws BizError 业务异常
     */
    GetProductActPriceV2Response getProductActPrice(GetProductActPriceV2Request request) throws BizError;

    /**
     * 批量获取活动详情
     *
     * @param request 请求对象
     * @return 活动详情
     * @throws BizError 业务异常
     */
    BatchGetActivityDetailResponse batchGetActivityDetail(BatchGetActivityDetailRequest request) throws BizError;
}
