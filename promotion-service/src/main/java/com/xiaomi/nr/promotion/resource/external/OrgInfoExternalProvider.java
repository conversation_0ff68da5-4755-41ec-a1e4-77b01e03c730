package com.xiaomi.nr.promotion.resource.external;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.entity.redis.OrgInfo;
import com.xiaomi.nr.promotion.domain.activity.service.common.StoreService;

/**
 * 门店信息资源
 *
 * <AUTHOR>
 * @date 2021/4/28
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OrgInfoExternalProvider extends ExternalDataProvider<OrgInfo> {
    /**
     * 门店信息数据
     */
    private ListenableFuture<OrgInfo> future;

    @Autowired
    private StoreService storeService;

    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        String orgCode = request.getOrgCode();
        if (StringUtils.isNotEmpty(orgCode)) {
            future = storeService.getOrgInfoByCodeAsync(orgCode);
        }
    }

    @Override
    protected long getTimeoutMills() {
        return DEFAULT_TIMEOUT;
    }

    @Override
    public ListenableFuture<OrgInfo> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.ORG_INFO;
    }
}
