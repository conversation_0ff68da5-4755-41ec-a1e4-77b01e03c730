package com.xiaomi.nr.promotion.componet.condition.carshop;

import com.google.common.collect.Lists;
import com.xiaomi.nr.md.promotion.admin.api.constant.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.componet.condition.AbstractCondition;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.OnsalePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/8/13
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarShopOnsaleCondition extends Condition {

    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 活动类型
     */
    private PromotionToolType promotionType;
    /**
     * 直降信息
     */
    private Map<String, ActPriceInfo> onsaleInfoMap;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (!Objects.equals(context.getBizPlatform(), BizPlatformEnum.CAR_SHOP)) {
            return false;
        }

        if (!Objects.equals(request.getChannel(), ChannelEnum.CAR_SHOP.value)) {
            return false;
        }

        Long uid = request.getUserId();
        if (MapUtil.isEmpty(onsaleInfoMap)) {
            log.error("onsaleInfoMap is empty. actId:{} uid:{}", promotionId, uid);
            return false;
        }

        List<GoodsIndex> goodsIndexList = buildGoodsIndex(request.getCartList());
        if (CollectionUtils.isEmpty(goodsIndexList)) {
            log.warn("goodsIndexList is empty. actId:{} uid:{}", promotionId, uid);
            return false;
        }

        context.setGoodIndex(goodsIndexList);
        return true;
    }

    private List<GoodsIndex> buildGoodsIndex(List<CartItem> cartList) {
        List<GoodsIndex> goodsIndexList = Lists.newArrayList();
        for (int i = 0; i < cartList.size(); i++) {
            CartItem item = cartList.get(i);
            if (Objects.isNull(item)) {
                continue;
            }
            // 如果该item不能参加活动, 或者item本身是赠品或加价购，该item不参加活动
            if (Objects.equals(item.getCannotJoinAct(), Boolean.TRUE) || SourceEnum.isGiftBargain(item.getSource())) {
                continue;
            }
            // 不能参加的活动类型判断
            List<Long> excludeActTypes = item.getCannotJoinActTypes();
            if (CollectionUtils.isNotEmpty(excludeActTypes) && excludeActTypes.contains((long) promotionType.getTypeId())) {
                continue;
            }

            // 直降价为空
            ActPriceInfo actPriceInfo = onsaleInfoMap.get(String.valueOf(item.getSsuId()));
            if (Objects.isNull(actPriceInfo)) {
                continue;
            }

            // 促销价 大于等于 购物车价
            if (actPriceInfo.getPrice() >= item.getCartPrice()) {
                continue;
            }

            // 满足直降条件
            goodsIndexList.add(new GoodsIndex(item.getItemId(), i));
        }



        return goodsIndexList;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof OnsalePromotionConfig)) {
            log.error("config is not instanceof OnsalePromotionConfig. config:{}", config);
            return;
        }
        OnsalePromotionConfig onsaleConfig = (OnsalePromotionConfig) config;
        this.promotionId = onsaleConfig.getPromotionId();
        this.onsaleInfoMap = onsaleConfig.getOnsaleInfoMap();
        this.promotionType = onsaleConfig.getPromotionType();
    }
}
