package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 线下用户类型用户每天参与活动次数
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OfflineActUtypePersonLimitDailyProvider implements ResourceProvider<OfflineActUtypePersonLimitDailyProvider.ActUtypePersonDayLimit> {
    /**
     * 线上用户参与活动记录
     */
    private ResourceObject<ActUtypePersonDayLimit> resourceObject;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Autowired
    private NacosConfig nacosConfig;

    @Override
    public ResourceObject<ActUtypePersonDayLimit> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<ActUtypePersonDayLimit> object) {
        this.resourceObject = object;
    }

    /**
     * 扣减库存
     */
    @Override
    public void lock() throws BizError {
        log.info("lock act utype person daily limit resource. {}", resourceObject);
        // 写开关关闭时: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("OfflineActUtypePersonLimitDailyProvider lock(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        activityRedisDao.incrActUtypePersonDayLimitNum(resourceObject.getContent().getActId(),
                resourceObject.getContent().getUType(),
                resourceObject.getContent().getUid(),
                resourceObject.getContent().getDateTimeMills(),
                resourceObject.getContent().getCount(),
                resourceObject.getContent().getLimitNum());
        log.info("lock act utype person daily limit resource. {}", resourceObject);
    }

    @Override
    public void consume() {
        log.info("consume act utype person daily limit resource. {}", resourceObject);
    }

    @Override
    public void rollback() throws BizError {
        log.info("rollback act utype person daily limit resource. {}", resourceObject);
        // 写开关关闭时: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("OfflineActUtypePersonLimitDailyProvider rollback(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        activityRedisDao.decrActUtypePersonDayLimitNum(resourceObject.getContent().getActId(),
                resourceObject.getContent().getUType(),
                resourceObject.getContent().getUid(),
                resourceObject.getContent().getDateTimeMills(),
                resourceObject.getContent().getCount());
        log.info("rollback act utype person daily limit resource. {}", resourceObject);
    }

    @Override
    public String conflictText() {
        return "减限购失败";
    }

    /**
     * 活动记录
     */
    @Data
    public static class ActUtypePersonDayLimit {
        /**
         * 用户ID
         */
        private Long uid;
        /**
         * 用户类型
         */
        private String uType;
        /**
         * 活动ID
         */
        private Long actId;
        /**
         * 时间(毫秒）
         */
        private Long dateTimeMills;
        /**
         * 数量
         */
        private Integer count;
        /**
         * 限制数量
         */
        private Long limitNum;
    }
}
