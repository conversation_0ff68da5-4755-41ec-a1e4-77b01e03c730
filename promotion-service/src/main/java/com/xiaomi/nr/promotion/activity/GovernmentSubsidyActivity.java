package com.xiaomi.nr.promotion.activity;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.Region;
import com.xiaomi.nr.promotion.activity.entity.GovernmentModeAct;
import com.xiaomi.nr.promotion.api.dto.model.GovActInfo;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.componet.action.GovernmentSubsidyAction;
import com.xiaomi.nr.promotion.componet.condition.GovernmentSubsidyCondition;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.GovernmentSubsidyPromotionConfig;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
@Data
public class GovernmentSubsidyActivity extends AbstractActivityTool implements ActivityTool {

    /**
     * sku-品类
     */
    private Map<String, String> goodsSpuGroupMap;

    /**
     * 标签
     */
    private String reportTag;

    public Integer subsidyMode;


    public Integer reportCity;

    public Long reduceDiscount;

    public Long maxReduce;

    public List<Region> region;

    private Integer fetchType;

    private String usageGuide;

    private String usageGuideImgUrl;

    private String activityUrl;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine.condition(GovernmentSubsidyCondition.class)
                .action(GovernmentSubsidyAction.class);
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof GovernmentSubsidyPromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        GovernmentSubsidyPromotionConfig govConfig = (GovernmentSubsidyPromotionConfig) config;
        this.goodsSpuGroupMap = govConfig.getGoodsSpuGroupMap();
        this.reportTag = govConfig.getReportTag();
        this.subsidyMode = govConfig.getSubsidyMode();
        this.reportCity = govConfig.getReportCity();
        this.reduceDiscount = govConfig.getReduceDiscount();
        this.maxReduce = govConfig.getMaxReducePrice();
        this.region = govConfig.getRegionList();
        this.fetchType = govConfig.getFetchType();
        this.usageGuide = govConfig.getUsageGuide();
        this.usageGuideImgUrl = govConfig.getUsageGuideImgUrl();
        this.activityUrl = govConfig.getActivityUrl();
        return true;
    }

    @Override
    public ActivityDetail getActivityDetail() {
        ActivityDetail activityDetail = getBasicActivityDetail();
        activityDetail.setDescRule(descRule);
        return activityDetail;
    }

    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) throws BizError {
        return null;
    }

    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        List<String> joinedSkuPackageList = Lists.newArrayList(includeSkuPackages);
        joinedSkuPackageList.retainAll(skuPackageList);
        if (CollectionUtils.isEmpty(joinedSkuPackageList)) {
            return Collections.emptyMap();
        }
        Map<String, ProductActInfo> actMap = Maps.newHashMap();
        for (String skuPackage : joinedSkuPackageList) {
            if (!goodsSpuGroupMap.containsKey(skuPackage)){
                continue;
            }
            ProductActInfo productActInfo = new ProductActInfo();
            productActInfo.setType(type.getValue());
            productActInfo.setId(id);
            productActInfo.setTag(reportTag);
            productActInfo.setUnixStartTime(getUnixStartTime());
            productActInfo.setUnixEndTime(getUnixEndTime());
            actMap.put(skuPackage, productActInfo);
        }
        return actMap;
    }

    public GovernmentModeAct getProductGovActInfo(Long productId) {
        String skuPackage = String.valueOf(productId);
        GovernmentModeAct govActInfo = new GovernmentModeAct();
        if (!goodsSpuGroupMap.containsKey(skuPackage)) {
            govActInfo.setHitGovAct(false);
            return govActInfo;
        }
        govActInfo.setId(id);
        govActInfo.setSubsidyMode(subsidyMode);
        govActInfo.setReportCity(reportCity);
        govActInfo.setReduceDiscount(reduceDiscount);
        govActInfo.setMaxReduce(maxReduce);
        String cateCode = getCateCodeBySku(String.valueOf(productId));
        if (cateCode == null) {
            govActInfo.setHitGovAct(false);
            return govActInfo;
        }
        govActInfo.setCateCode(cateCode);
        govActInfo.setFetchType(fetchType);
        govActInfo.setUsageGuide(usageGuide);
        govActInfo.setUsageGuideImgUrl(usageGuideImgUrl);
        govActInfo.setActivityUrl(activityUrl);
        govActInfo.setHitGovAct(true);
        return govActInfo;
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.GOVERNMENT_SUBSIDY;
    }

    public String getCateCodeBySku(String sku) {
        return goodsSpuGroupMap.get(sku);
    }
}
