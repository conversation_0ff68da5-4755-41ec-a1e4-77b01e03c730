package com.xiaomi.nr.promotion.activity.carsale;

import com.xiaomi.nr.promotion.activity.AbstractActivityTool;
import com.xiaomi.nr.promotion.api.dto.MultiProductGoodsActRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.api.dto.model.car.ExchangeSubsidyRule;
import com.xiaomi.nr.promotion.componet.condition.carsale.CarExchangeSubsidyCondition;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.ProductDetailContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.CarExchangeSubsidyConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.resource.external.GoodsStockExternalProvider;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class CarExchangeSubsidyActivity extends AbstractActivityTool implements ActivityTool {

    private Map<String, ActPriceInfo> exchangeSubsidyInfoMap;

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof CarExchangeSubsidyConfig)) {
            return false;
        }
        super.DSLLoad(config);
        CarExchangeSubsidyConfig promotionConfig = (CarExchangeSubsidyConfig) config;
        this.exchangeSubsidyInfoMap = promotionConfig.getExchangeSubsidyMap();
        return true;
    }

    @Override
    public ActivityDetail getActivityDetail() {
        ActivityDetail detail = super.getBasicActivityDetail();
        detail.setPriceInfoMap(exchangeSubsidyInfoMap);
        return detail;
    }

    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) throws BizError {
        return null;
    }

    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        return null;
    }

    @Override
    public Boolean checkUsableAddAct(ReduceDetailAddItem item){
        if(exchangeSubsidyInfoMap == null) {
            return false;
        }
        ActPriceInfo actPriceInfo = exchangeSubsidyInfoMap.get(item.getSsuId().toString());
        return actPriceInfo != null && actPriceInfo.getPrice().equals(item.getReduce());
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.EXCHANGE_SUBSIDY;
    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR;
    }

    @Override
    public PromotionInfoDTO getMultiProductAct(MultiGoodItem goodItem, MultiProductGoodsActRequest request, ProductDetailContext context) throws BizError {
        ActPriceInfo actPriceInfo = exchangeSubsidyInfoMap.get(goodItem.getSsuId().toString());
        if (actPriceInfo==null){
            return null;
        }
        PromotionInfoDTO promotionInfo = initPromotionInfo();
        CarExchangeSubsidyConfig config = (CarExchangeSubsidyConfig)getPromotionConfig();
        promotionInfo.setId(config.getPromotionId());
        promotionInfo.setPromotionName(config.getName());
        promotionInfo.setStartTime(config.getUnixStartTime());
        promotionInfo.setEndTime(config.getUnixEndTime());
        ExchangeSubsidyRule rule=new ExchangeSubsidyRule();
        rule.setReduceMoney(actPriceInfo.getPrice());
        promotionInfo.setRule(GsonUtil.toJson(rule));
        return promotionInfo;
    }


    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine.condition(CarExchangeSubsidyCondition.class);
    }
}
