package com.xiaomi.nr.promotion.enums;

/**
 * 活动类型枚举
 * 包含3部分数据内容
 * value: 唯一标识活动类型字段
 * name:  类型名称
 * policyList: 支持活动类型列表
 *
 * <AUTHOR>
 * @date 2021/3/5
 */
public enum ActivityTypeEnum {
    /**
     * 满减活动
     */
    REDUCE(5, "满减", 0, 0),
    /**
     * "折扣活动"
     */
    DISCOUNT(7, "折扣", 81, 81),
    /**
     * "加价购活动"
     */
    BARGAIN(8, "加价购", 1, 1),
    /**
     * "赠品活动"
     */
    GIFT(9, "赠完即止", 3, 3),
    /**
     * "包邮活动"
     */
    POST_FREE(10, "包邮", 0, 91),
    /**
     * "直降活动"
     */
    ONSALE(20, "直降", 89, 89),
    /**
     * "买赠活动"
     */
    BUY_GIFT(21, "买赠", 2, 2),
    /**
     * "门店价"
     */
    STORE_PRICE(22, "门店价", 90, 90),
    /**
     * "合约机"
     */
    CONTRACT_PHONE(23, "合约机", 88, 88),
    /**
     * "指定门店降价"
     */
    PARTONSALE(25, "指定门店降价",85, 85),
    /**
     * "换新立减"
     */
    RENEW_REDUCE(26, "换新立减", 83, 83),
    /**
     * 下单立减
     */
    BUY_REDUCE(27, "下单立减", 82, 82),
    /**
     * 范围立减
     */
    RANGE_REDUCE(28, "范围立减", 0, 0),
    /**
     * 置换补贴
     */
    EXCHANGE_SUBSIDY(29, "置换补贴", 0, 0),
    /**
     * 订单立减
     */
    ORDER_REDUCE(31, "订单立减", 0, 0),
    /**
     * "24年北京大家电以旧换新-购新补贴"
     */
    NEW_PURCHASE_SUBSIDY(40,"购新补贴",0, 0),
    /**
     * "24年北京大家电以旧换新-换新补贴"
     */
    UPGRADE_PURCHASE_SUBSIDY(41,"换新补贴",1, 1),

    /**
     * 会员折扣
     */
    CAR_SHOP_VIP(55, "会员折扣(车商城)", 50, 50),

    /**
     * 维保折扣活动
     */
    MAINTENANCE_REPAIR_DISCOUNT(61, "维保折扣活动", 100, 100),

    /**
     * 工项免费活动
     */
    MAINTENANCE_ITEM_FREE(62, "工项免费活动", 101, 101),
    /**
     * 团购价格折扣
     */
    B2T_VIP_DISCOUNT(70, "团购价格折扣", 80, 80),
    /**
     * 阶梯价
     */
    B2T_STEP_PRICE(71, "阶梯价", 88, 88),
    /**
     * 提货底价
     */
    B2T_CHANNEL_PRICE(72, "提货底价", 98, 98);

    /**
     * 活动类型值
     */
    private final int value;

    /**
     * 活动类型的名称
     */
    private final String name;

    /**
     * 优先级
     */
    private final Integer weight;

    private final Integer carWeight;

    ActivityTypeEnum(Integer value, String name, Integer weight, Integer carWeight) {
        this.value = value;
        this.name = name;
        this.weight = weight;
        this.carWeight = carWeight;
    }


    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public Integer getWeight() {
        return weight;
    }

    public Integer getCarWeight() {
        return carWeight;
    }

    /**
     * 根据活动类型值获取活动类型枚举对象
     *
     * @param value 类型值
     * @return enum枚举对象
     */
    public static ActivityTypeEnum getByValue(int value) {
        ActivityTypeEnum[] values = ActivityTypeEnum.values();
        for (ActivityTypeEnum activityTypeEnum : values) {
            if (value == activityTypeEnum.value) {
                return activityTypeEnum;
            }
        }
        return null;
    }

    /**
     * 是否是赠品活动
     *
     * @param value 值
     * @return 值
     */
    public static boolean isGiftAct(int value) {
        return GIFT.getValue() == value;
    }

    /**
     * 是否是买赠活动
     *
     * @param value 值
     * @return 值
     */
    public static boolean isBuyGiftAct(int value) {
        return BUY_GIFT.getValue() == value;
    }

    /**
     * 是否是加价购活动
     *
     * @param value 类型值
     * @return true/false
     */
    public static boolean isBargainAct(int value) {
        return BARGAIN.getValue() == value;
    }

    /**
     * 是否是赠品或加价购活动
     *
     * @param value 类型值
     * @return true/false
     */
    public static boolean isGiftBargainAct(int value) {
        return GIFT.getValue() == value || BARGAIN.getValue() == value || BUY_GIFT.getValue() == value;
    }

    /**
     * 减钱类活动包括
     *
     * @param value 类型值
     * @return true/false
     */
    public static boolean isReduceMoneyAct(int value) {
        return REDUCE.getValue() == value || DISCOUNT.getValue() == value;
    }
}
