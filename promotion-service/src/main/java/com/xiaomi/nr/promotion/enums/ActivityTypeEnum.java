package com.xiaomi.nr.promotion.enums;

/**
 * 活动类型枚举
 * 包含3部分数据内容
 * value: 唯一标识活动类型字段
 * name:  类型名称
 * policyList: 支持活动类型列表
 *
 * <AUTHOR>
 * @date 2021/3/5
 */
public enum ActivityTypeEnum {
    /**
     * 满减活动
     */
    REDUCE(5, "满减", 0),
    /**
     * "折扣活动"
     */
    DISCOUNT(7, "折扣", 81),
    /**
     * "加价购活动"
     */
    BARGAIN(8, "加价购", 1),
    /**
     * "赠品活动"
     */
    GIFT(9, "赠完即止", 3),
    /**
     * "包邮活动"
     */
    POST_FREE(10, "包邮", 0),
    /**
     * "直降活动"
     */
    ONSALE(20, "直降", 89),
    /**
     * "买赠活动"
     */
    BUY_GIFT(21, "买赠", 2),
    /**
     * "门店价"
     */
    STORE_PRICE(22, "门店价", 90),
    /**
     * "合约机"
     */
    CONTRACT_PHONE(23, "合约机", 88),
    /**
     * "指定门店降价"
     */
    PARTONSALE(25, "指定门店降价",85),
    /**
     * "换新立减"
     */
    RENEW_REDUCE(26, "换新立减", 83),
    /**
     * 下单立减
     */
    BUY_REDUCE(27, "下单立减", 82),
    /**
     * 范围立减
     */
    RANGE_REDUCE(28, "范围立减", 0),
    /**
     * 置换补贴
     */
    EXCHANGE_SUBSIDY(29, "置换补贴", 0),
    /**
     * "24年北京大家电以旧换新-购新补贴"
     */
    NEW_PURCHASE_SUBSIDY(40,"购新补贴",0),
    /**
     * "24年北京大家电以旧换新-换新补贴"
     */
    UPGRADE_PURCHASE_SUBSIDY(41,"换新补贴",1),

    /**
     * "政府补贴"
     */
    GOVERNMENT_SUBSIDY(42,"政府补贴",2),
    /**
     * 团购价格折扣
     */
    B2T_VIP_DISCOUNT(70, "团购价格折扣", 80),
    /**
     * 阶梯价
     */
    B2T_STEP_PRICE(71, "阶梯价", 88),
    /**
     * 提货底价
     */
    B2T_CHANNEL_PRICE(72, "提货底价", 98);


    /**
     * 活动类型值
     */
    private final int value;

    /**
     * 活动类型的名称
     */
    private final String name;

    /**
     * 优先级
     */
    private final Integer weight;

    ActivityTypeEnum(Integer value, String name, Integer weight) {
        this.value = value;
        this.name = name;
        this.weight = weight;
    }


    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public Integer getWeight() {
        return weight;
    }

    /**
     * 根据活动类型值获取活动类型枚举对象
     *
     * @param value 类型值
     * @return enum枚举对象
     */
    public static ActivityTypeEnum getByValue(int value) {
        ActivityTypeEnum[] values = ActivityTypeEnum.values();
        for (ActivityTypeEnum activityTypeEnum : values) {
            if (value == activityTypeEnum.value) {
                return activityTypeEnum;
            }
        }
        return null;
    }

    /**
     * 是否是赠品活动
     *
     * @param value 值
     * @return 值
     */
    public static boolean isGiftAct(int value) {
        return GIFT.getValue() == value;
    }

    /**
     * 是否是买赠活动
     *
     * @param value 值
     * @return 值
     */
    public static boolean isBuyGiftAct(int value) {
        return BUY_GIFT.getValue() == value;
    }

    /**
     * 是否是加价购活动
     *
     * @param value 类型值
     * @return true/false
     */
    public static boolean isBargainAct(int value) {
        return BARGAIN.getValue() == value;
    }

    /**
     * 是否是赠品或加价购活动
     *
     * @param value 类型值
     * @return true/false
     */
    public static boolean isGiftBargainAct(int value) {
        return GIFT.getValue() == value || BARGAIN.getValue() == value || BUY_GIFT.getValue() == value;
    }

    /**
     * 减钱类活动包括
     *
     * @param value 类型值
     * @return true/false
     */
    public static boolean isReduceMoneyAct(int value) {
        return REDUCE.getValue() == value || DISCOUNT.getValue() == value;
    }
}
