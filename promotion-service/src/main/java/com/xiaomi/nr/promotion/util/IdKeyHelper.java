package com.xiaomi.nr.promotion.util;

import com.xiaomi.nr.promotion.constant.PromotionConstant;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/6/28
 */
public class IdKeyHelper {

    /**
     * 获取通用活动ID KEY
     *
     * @param activityId 活动ID
     * @return ID_KEY
     */
    public static String getGeneralActIdKey(Long activityId) {
        return PromotionConstant.CARTLIST_ACTIVITY_PREFIX + activityId;
    }

    /**
     * 获取档位立减KEY
     *
     * @param skuPackage SKU / cid
     * @param levelKey 档位key
     * @return KEY
     */
    public static String generalReduceKey(String skuPackage, String levelKey) {
        return StringUtils.isNotEmpty(levelKey) ? skuPackage + "_" + levelKey : skuPackage;
    }
}
