package com.xiaomi.nr.promotion.activity.pool;

import com.xiaomi.nr.promotion.dao.mysql.mdpromotion.TInstallmentGiftGoodsMapper;
import com.xiaomi.nr.promotion.dao.mysql.mdpromotion.TInstallmentGiftGoodsPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class InstallmentGiftGoodsPool {

    private volatile Map<Long, TInstallmentGiftGoodsPo> currentValidProductMapCache = new ConcurrentHashMap<>();

    @Autowired
    private TInstallmentGiftGoodsMapper installmentGiftGoodsMapper;

    public void reBuildValidProductCache() {
        List<TInstallmentGiftGoodsPo> tInstallmentGiftGoodsPos = installmentGiftGoodsMapper.queryValidGoodsList();
        log.info("reBuildValidProductCache new size={}, old size={}", tInstallmentGiftGoodsPos.size(), currentValidProductMapCache.size());
        currentValidProductMapCache = tInstallmentGiftGoodsPos.stream().collect(Collectors.toMap(TInstallmentGiftGoodsPo::getProductId, Function.identity()));
    }

    public Map<Long, TInstallmentGiftGoodsPo> getCurrentValidProductMapCache() {
        return currentValidProductMapCache;
    }
}
