package com.xiaomi.nr.promotion.domain.coupon.facade;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Request;
import com.xiaomi.nr.promotion.model.CheckoutContext;

/**
 * <AUTHOR>
 * @Date 2024/3/6
 */
public interface CouponFacadeInterFace {
    
    /**
     * 下单
     * @param request
     * @param response
     * @param context
     * @throws Exception
     */
    void checkoutForSubmit(CheckoutPromotionRequest request, CheckoutPromotionResponse response, CheckoutContext context) throws Exception;
    
    /**
     * 结算
     * @param request
     * @param response
     * @param context
     * @throws Exception
     */
    void checkoutForCouponList(CheckoutPromotionV2Request request, CheckoutPromotionResponse response, CheckoutContext context) throws Exception;
}
