package com.xiaomi.nr.promotion.componet.apportion;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.componet.Action;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/4/1
 */
@Component
@Scope(value = "prototype")
@Slf4j
public class FixedApportion extends Action {

    private Long youpinCost = 0L;


    @Override
    public void loadConfig(AbstractPromotionConfig config) {

    }

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context)
            throws BizError {
    }

}
