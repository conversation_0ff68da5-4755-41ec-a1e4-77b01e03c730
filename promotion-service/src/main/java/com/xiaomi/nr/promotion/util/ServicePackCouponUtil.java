package com.xiaomi.nr.promotion.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.ServicePackCheckoutCouponItem;
import com.xiaomi.nr.promotion.api.dto.model.ServicePackDeductedInfo;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponRangeGoodsDO;
import com.xiaomi.nr.promotion.domain.coupon.model.MaintenanceCouponRangeGoodsDO;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

/**
 * <AUTHOR>
 * @date 2025/1/23 15:22
 */
public class ServicePackCouponUtil {


    private ServicePackCouponUtil() {}

    /**
     * 获取有效的组套商品和Id
     *
     * @param cartList
     * @param validGoodsMap
     * @return
     */
    public static TreeMap<String, List<CartItem>> getValidServicePackItemMap(List<CartItem> cartList,
                                                                             Map<Long, ValidGoods> validGoodsMap) {
        if (CollectionUtils.isEmpty(cartList) || MapUtils.isEmpty(validGoodsMap)) {
            return Maps.newTreeMap();
        }
        TreeMap<String, List<CartItem>> ret = new TreeMap<>();
        Set<String> inValidServicePackSet = Sets.newHashSet();
        for (CartItem cartItem : cartList) {
            if (StringUtils.isBlank(cartItem.getServicePackId())) {
                continue;
            }
            ValidGoods validGood = validGoodsMap.get(cartItem.getSsuId());
            if (Objects.isNull(validGood)) {
                inValidServicePackSet.add(cartItem.getServicePackId());
                continue;
            }
            List<CartItem> cartItems = ret.getOrDefault(cartItem.getServicePackId(), Lists.newArrayList());
            cartItems.add(cartItem);
            ret.put(cartItem.getServicePackId(), cartItems);
        }
        for (String servicePackId : inValidServicePackSet) {
            ret.remove(servicePackId);
        }
        return ret;
    }

    /**
     * 获取组套的优惠信息
     *
     * @param servicePackIds
     * @param indexList
     * @param cartItemList
     * @param rangeGoodsList
     * @return
     */
    public static List<ServicePackCheckoutCouponItem> getServicePackCheckoutCouponItemList(List<String> servicePackIds,
                                                                                            List<Integer> indexList,
                                                                                            List<CartItem> cartItemList,
                                                                                            List<CouponRangeGoodsDO> rangeGoodsList) {
        if (CollectionUtils.isEmpty(servicePackIds)) {
            return Collections.emptyList();
        }
        List<ServicePackCheckoutCouponItem> ret = Lists.newArrayList();
        for (String servicePackId : servicePackIds) {
            List<ServicePackDeductedInfo> checkoutCouponItems =
                    getCheckoutCouponItems(servicePackId, indexList, cartItemList, rangeGoodsList);
            if (CollectionUtils.isEmpty(checkoutCouponItems)) {
                continue;
            }
            ServicePackCheckoutCouponItem item = new ServicePackCheckoutCouponItem();
            item.setServicePackId(servicePackId);
            item.setDeductedInfos(checkoutCouponItems);
            long totalDeductedAmount = checkoutCouponItems.stream()
                    .flatMapToLong(info -> LongStream.of(info.getReduceAmount()))
                    .sum();
            item.setTotalDeduceAmount(totalDeductedAmount);
            ret.add(item);
        }
        return ret;
    }

    /**
     * 获取单个组套优惠信息
     *
     * @param servicePackId
     * @param indexList
     * @param cartItemList
     * @param rangeGoodsList
     * @return
     */
    public static List<ServicePackDeductedInfo> getCheckoutCouponItems(String servicePackId,
                                                                      List<Integer> indexList,
                                                                      List<CartItem> cartItemList,
                                                                      List<CouponRangeGoodsDO> rangeGoodsList) {
        if (CollectionUtils.isEmpty(cartItemList)
                || StringUtils.isBlank(servicePackId)
                || CollectionUtils.isEmpty(indexList)) {
            return Collections.emptyList();
        }
        Map<Long, MaintenanceCouponRangeGoodsDO> rangeGoodsMap = rangeGoodsList.stream()
                .map(MaintenanceCouponRangeGoodsDO.class::cast)
                .collect(Collectors.toMap(MaintenanceCouponRangeGoodsDO::getSsuId, Function.identity(),
                        (o1, o2) -> o2));
        List<ServicePackDeductedInfo> ret = Lists.newArrayList();
        for (int i = 0; i < indexList.size(); i++) {
            Integer inCartIndex = indexList.get(i);
            CartItem cartItem = cartItemList.get(inCartIndex);
            if (!StringUtils.equals(servicePackId, cartItem.getServicePackId())) {
                continue;
            }
            Long ssuId = cartItem.getSsuId();
            MaintenanceCouponRangeGoodsDO goodDO = rangeGoodsMap.get(ssuId);
            if (Objects.isNull(goodDO)) {
                continue;
            }
            ServicePackDeductedInfo deductedInfo = new ServicePackDeductedInfo();
            deductedInfo.setSsuId(ssuId);
            deductedInfo.setInCartIndex(inCartIndex);
            Integer deductedNum = Math.min(cartItem.getCount(), goodDO.getCount());
            deductedInfo.setNum(deductedNum);
            deductedInfo.setSsuType(cartItem.getSsuType());
            deductedInfo.setBizSubType(cartItem.getBizSubType());
            Long itemCurPrice = CartHelper.itemCurPrice(cartItem.getOriginalCartPrice(), cartItem.getReduceItemList());
            // 获取价格
            long curDeductMoney = itemCurPrice * deductedNum;
            deductedInfo.setReduceAmount(curDeductMoney);
            ret.add(deductedInfo);
        }
        return ret;
    }

}
