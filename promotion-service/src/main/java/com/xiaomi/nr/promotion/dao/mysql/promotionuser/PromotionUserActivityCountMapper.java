package com.xiaomi.nr.promotion.dao.mysql.promotionuser;

import com.xiaomi.nr.promotion.entity.mysql.promotionuser.UserActivityCount;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description 用户参与活动记录表
 * <AUTHOR>
 * @date 2025-01-04 17:34
*/
@Repository
public interface PromotionUserActivityCountMapper {

    /**
     * 插入用户参与活动记录
     * @param userActivityCount 用户参与活动记录
     * @return 插入记录数
     */
    @Insert("INSERT INTO " +
            "promotion_user_activity_count " +
            "   (user_id, promotion_id, num, extend, create_time, update_time) " +
            "VALUES " +
            "   (#{userId}, #{promotionId}, #{num}, #{extend}, #{createTime}, #{updateTime})")
    Integer insert(UserActivityCount userActivityCount);

    /**
     * 根据用户id和活动id获取用户参与活动记录
     * @param userId 用户id
     * @param promotionId 活动id
     * @return 用户参与活动记录
     */
    @Select("select * from promotion_user_activity_count where user_id = #{userId} and promotion_id = #{promotionId} order by id desc limit 1")
    UserActivityCount getByUserIdAndPromotionId(@Param("userId") Long userId, @Param("promotionId") Long promotionId);

    /**
     * 更新用户参与活动次数
     * @param userId 用户ID
     * @param promotionId 活动ID
     * @param newNum 新的次数
     * @param oldNum 旧的次数
     * @param updateTime 更新时间
     * @return 更新记录数
     */
    @Update("update promotion_user_activity_count " +
            "set " +
            "   num = #{newNum}, extend = #{extend}, update_time = #{updateTime} " +
            "where user_id = #{userId} and promotion_id = #{promotionId} and num = #{oldNum}")
    Integer updateCount(@Param("userId") Long userId, @Param("promotionId") Long promotionId, @Param("newNum") Integer newNum, @Param("oldNum") Integer oldNum, @Param("extend") String extend, @Param("updateTime") Long updateTime);

    List<UserActivityCount> getByUserIdAndPromotionIds(@Param("mid") Long mid,
                                                       @Param("promotionIds") List<Long> promotionIds);
}
