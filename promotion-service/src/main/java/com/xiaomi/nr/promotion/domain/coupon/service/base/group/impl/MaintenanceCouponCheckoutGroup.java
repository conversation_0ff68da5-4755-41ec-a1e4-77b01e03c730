package com.xiaomi.nr.promotion.domain.coupon.service.base.group.impl;

import afu.org.checkerframework.checker.oigj.qual.O;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.api.dto.model.CouponInfo;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutContext;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponGroupInfoDO;
import com.xiaomi.nr.promotion.domain.coupon.service.base.group.CouponGroupInterface;
import com.xiaomi.nr.promotion.domain.coupon.service.base.type.CouponFactory;
import com.xiaomi.nr.promotion.engine.BizPlatformComponent;
import com.xiaomi.nr.promotion.engine.CouponTool;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.provider.CouponProvider;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.NumberUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by wangweiyi on 2024/2/28
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class MaintenanceCouponCheckoutGroup implements BizPlatformComponent, CouponGroupInterface {

    @Autowired
    private CouponFactory couponFactory;

    @Autowired
    private ResourceProviderFactory resourceProviderFactory;

    private Map<Long, CouponTool> couponToolMap;

    private Map<Long, CouponCheckoutResult> checkoutResultMap;

    @Getter
    private List<Coupon> validCouponList;

    @Getter
    private List<Coupon> invalidCouponList;

    //互斥规则

    //抵扣规则

    //排序规则
    private Comparator<Coupon> invalidSortRule;

    private Comparator<Coupon> validSortRule;


    @Override
    public void init(List<CheckoutCoupon> couponList, CouponGroupInfoDO couponGroupInfo) {
        //初始化互斥规则
        //初始化抵扣规则
        for (CheckoutCoupon checkoutCoupon : couponList) {
            checkoutCoupon.setMutualRule(couponGroupInfo.getMutualRule());
            checkoutCoupon.setDeductRule(couponGroupInfo.getDeductRule());
        }
        // 初始化优惠工具
        this.couponToolMap = initCouponTools(couponList);


        this.checkoutResultMap = new HashMap<>();
        this.validCouponList = new ArrayList<>();
        this.invalidCouponList = new ArrayList<>();

        // 初始化排序规则
        this.validSortRule = (coupon1, coupon2) -> {
            return coupon2.getEndTime().compareTo(coupon1.getEndTime());
        };
        this.invalidSortRule = (coupon1, coupon2) -> {
            return coupon2.getEndTime().compareTo(coupon1.getEndTime());
        };

    }

    public Map<Long, CouponTool> initCouponTools(List<CheckoutCoupon> checkoutCouponList) {
        Map<Long, CouponTool> toolList = new HashMap<>();
        for (CheckoutCoupon checkoutCoupon : checkoutCouponList) {
            try {
                PromotionToolType promotionToolType = PromotionToolType.fromTypeId(checkoutCoupon.getType());
                if (promotionToolType == null) {
                    continue;
                }
                CouponTool couponTool = couponFactory.getByTypeAndBiz(promotionToolType, this.getBizPlatform());
                boolean success = couponTool.load(checkoutCoupon);
                if (success) {
                    toolList.put(couponTool.getCouponId(), couponTool);
                }
            } catch (Exception e) {
                log.error("loadCurrentCouponTools error. couponId:{}, info:{}, err:", checkoutCoupon.getCouponId(), GsonUtil.toJson(checkoutCoupon),  e);
            }
        }
        return toolList;
    }


    public List<Long> matchCouponList(CheckoutPromotionRequest request) throws BizError {
        List<Long> selectedCouponIdList = new ArrayList<>();
        for (Long selectCouponId : request.getCouponIds()) {
            if (couponToolMap.containsKey(selectCouponId)) {
                selectedCouponIdList.add(selectCouponId);
            }
        }

        return selectedCouponIdList;
    }


    @Override
    public void checkoutForSubmit(CouponCheckoutContext couponContext, List<Long> selectedCouponList)
            throws Exception {

        //完成结算
        CheckoutPromotionRequest request = couponContext.getRequest();
        CheckoutContext context = couponContext.getContext();
        doCheckout(couponToolMap, request, context);

        if (CollectionUtils.isEmpty(selectedCouponList)) {
            return;
        }

        if (selectedCouponList.size() > 1) {
            throw ExceptionHelper.create(ErrCode.ERR_TOO_MANY_COUPONS, "此种类型最多只能使用一张优惠券");
        }


        //执行券分摊逻辑
        Long selectedId = selectedCouponList.get(0);

        CouponTool selectCouponTool = getToolById(selectedId);

        CouponCheckoutResult result = getResultById(selectedId);
        //根据某张券来使用并计算分摊
        checkoutSelectCoupon(couponContext, selectedId);

        //生成context.carts
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            //初始化provider
            couponContext.getResourceHandlers().add(initResource(request, selectCouponTool, result));
        }

    }


    @Override
    public void checkoutForCouponList(CouponCheckoutContext couponContext, List<Long> selectedCouponList)
            throws Exception {


        CheckoutPromotionRequest request = couponContext.getRequest();
        CheckoutContext context = couponContext.getContext();
        //完成结算，获取结算结果
        doCheckout(couponToolMap, request, context);

        if (CollectionUtils.isEmpty(selectedCouponList)) {
            return;
        }

        if (selectedCouponList.size() > 1) {
            throw ExceptionHelper.create(ErrCode.ERR_TOO_MANY_COUPONS, "此种类型最多只能使用一张优惠券");
        }
        //券列表内部排序
        sortCouponList();

        //选择最优券逻辑
        Long selectedId = selectedCouponList.get(0);
        //执行券分摊逻辑
        checkoutSelectCoupon(couponContext, selectedId);
    }




    protected void sortCouponList() {
        //内部排序
        validCouponList.sort(validSortRule);

        //内部排序
        invalidCouponList.sort(invalidSortRule);

    }



    public CouponProvider initResource(CheckoutPromotionRequest request, CouponTool couponTool, CouponCheckoutResult result)
            throws BizError {
        long orderId = request.getOrderId();
        CouponInfo couponInfo = couponTool.generateCouponInfo(result);
        CouponProvider.ResContent resContent = new CouponProvider.ResContent();
        resContent.setUserId(request.getUserId());
        resContent.setOrgCode(request.getOrgCode());
        resContent.setId(couponInfo.getCouponId());
        if (couponInfo.getCouponCode() != null) {
            resContent.setCode(couponInfo.getCouponCode());
        }
        resContent.setOffline(couponInfo.getOffline());
        resContent.setClientId(request.getClientId());
        resContent.setReplaceMoney(NumberUtil.amountConvertF2Y(couponInfo.getReduceMoney()));
        resContent.setReduceExpress(NumberUtil.amountConvertF2Y(couponInfo.getReduceExpress()));

        resContent.setSubmitType(request.getSubmitType());
        resContent.setBizPlatform(getBizPlatform().getValue());
        resContent.setVid(request.getVid());

        ResourceObject<CouponProvider.ResContent> resourceObject = buildResource(resContent, orderId, String.valueOf(couponInfo.getCouponId()));
        CouponProvider couponProvider = (CouponProvider) resourceProviderFactory.getProvider(ResourceType.COUPON);
        couponProvider.initResource(resourceObject);
        return couponProvider;
    }

    protected  <T> ResourceObject<T> buildResource(T content, Long orderId, String couponId) {
        ResourceObject<T> resourceObject = new ResourceObject<>();
        resourceObject.setContent(content);
        resourceObject.setResourceId(String.format("%s_%s", orderId, couponId));
        resourceObject.setResourceType(ResourceType.COUPON);
        resourceObject.setPromotionId(-1L);
        resourceObject.setOrderId(orderId);
        resourceObject.setPid(-1L);
        return resourceObject;
    }

    /**
     * 执行结算逻辑：
     * 根据结算结果添加到可用或不可用
     * 更新可用/不可用信息
     *
     * @param request
     * @param context
     */
    private void doCheckout(Map<Long, CouponTool> couponToolMap, CheckoutPromotionRequest request, CheckoutContext context) {
        for (CouponTool couponTool : couponToolMap.values()) {
            //构造
            Coupon cartCoupon = couponTool.generateCartCoupon();
            if (cartCoupon.getAllow() == 0) {
                invalidCouponList.add(cartCoupon);
                continue;
            }
            try {
                //得到结算结果
                CouponCheckoutResult couponCheckoutResult = couponTool.checkoutCoupon(request, context);
                checkoutResultMap.put(couponTool.getCouponId(), couponCheckoutResult);
                if (couponCheckoutResult.isAllow()) {
                    //更新可用信息
                    cartCoupon.updateValidInfo(couponCheckoutResult.getValidGoodsPrice(), NumberUtil.amountConvertF2Y(couponCheckoutResult.getReduceAmount()), couponCheckoutResult.getReduceAmount());
                    validCouponList.add(cartCoupon);
                } else {
                    //更新不可用信息
                    cartCoupon.updateInvalidInfo(couponCheckoutResult.getUnusableCode(), couponCheckoutResult.getUnusableReason(), couponCheckoutResult.getKeyDataUnusable());
                    invalidCouponList.add(cartCoupon);
                }
            } catch (Exception e) {
                log.error("checkout couponTool error. couponId:{}, type:{}, ex", couponTool.getCouponId(), couponTool.getCouponType(), e);
                if (e instanceof BizError) {
                    cartCoupon.updateInvalidInfo(((BizError) e).getCode(), ((BizError) e).getMsg(), ((BizError) e).getMsg());
                    invalidCouponList.add(cartCoupon);
                }
            }
        }
    }



    /**
     * 选定某张券来使用并计算分摊
     * @param request
     * @param context

     * @throws Exception
     */
    protected void checkoutSelectCoupon(CouponCheckoutContext couponContext, Long couponId) throws Exception {

        CouponCheckoutResult result = getResultById(couponId);

        if (!result.isAllow()) {
            throw ExceptionHelper.create(ErrCode.ERR_COUPON_INVALID, result.getUnusableReason());
        }

        if (result.getReduceAmount() == 0) {
            return;
        }

        CouponTool selectCouponTool = getToolById(couponId);

        selectCouponTool.updateCartsReduce(couponContext.getRequest(), couponContext.getContext(), result);

    }



    public CouponCheckoutResult getResultById(Long couponId) throws BizError {

        if (!checkoutResultMap.containsKey(couponId)) {
            throw ExceptionHelper.create(ErrCode.ERR_EMPTY_COUPONS, "无效的优惠券");
        }
        return checkoutResultMap.get(couponId);

    }

    public CouponTool getToolById(Long couponId) throws BizError {

        if (!couponToolMap.containsKey(couponId)) {
            throw ExceptionHelper.create(ErrCode.ERR_EMPTY_COUPONS, "无效的优惠券");
        }
        return couponToolMap.get(couponId);

    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.MAINTENANCE_REPAIR;
    }
}
