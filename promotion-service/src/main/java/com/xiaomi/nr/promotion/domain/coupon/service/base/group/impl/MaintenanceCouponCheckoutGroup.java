package com.xiaomi.nr.promotion.domain.coupon.service.base.group.impl;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.api.dto.model.CouponInfo;
import com.xiaomi.nr.promotion.domain.coupon.model.*;
import com.xiaomi.nr.promotion.domain.coupon.service.base.group.CouponGroupInterface;
import com.xiaomi.nr.promotion.domain.coupon.service.base.type.CouponFactory;
import com.xiaomi.nr.promotion.engine.BizPlatformComponent;
import com.xiaomi.nr.promotion.engine.CouponTool;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.CouponGroupMutualRuleEnum;
import com.xiaomi.nr.promotion.enums.CouponServiceTypeEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.provider.CouponProvider;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.NumberUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Created by wangweiyi on 2024/2/28
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class MaintenanceCouponCheckoutGroup implements BizPlatformComponent, CouponGroupInterface {

    @Autowired
    private CouponFactory couponFactory;

    @Autowired
    private ResourceProviderFactory resourceProviderFactory;

    /**
     * 优惠券工具
     */
    @Getter
    private Map<Long, CouponTool> couponToolMap;
    /**
     * 不可用券排序规则
     */
    private Comparator<Coupon> invalidSortRule;
    /**
     * 可用券排序规则
     */
    private Comparator<Coupon> validSortRule;
    /**
     * 组内互斥规则
     */
    private CouponGroupMutualRuleEnum mutualRule;
    /**
     * 组内叠加最大数量
     */
    private Integer maxCount;
    /**
     * 组间排序顺序
     */
    @Getter
    private int sortNum = 0;


    @Override
    public void init(List<CheckoutCoupon> couponList, CouponGroupInfoDO couponGroupInfo) {
        // 初始化优惠券互斥规则、抵扣规则
        for (CheckoutCoupon checkoutCoupon : couponList) {
            checkoutCoupon.setMutualRule(couponGroupInfo.getMutualRule());
            checkoutCoupon.setDeductRule(couponGroupInfo.getDeductRule());
        }
        // 初始化组内互斥规则
        maxCount = CouponGroupMutualRuleEnum.calcMaxCountByCode(couponGroupInfo.getMutualRule());
        // 初始化组间排序
        CouponServiceTypeEnum serviceTypeEnum = CouponServiceTypeEnum.valueOf(couponList.get(0).getServiceType());
        sortNum = serviceTypeEnum.getSortNum();
        // 初始化优惠工具
        this.couponToolMap = initCouponTools(couponList);
        // 初始化排序规则
        this.validSortRule = (coupon1, coupon2) -> coupon2.getEndTime().compareTo(coupon1.getEndTime());
        this.invalidSortRule = (coupon1, coupon2) -> coupon2.getEndTime().compareTo(coupon1.getEndTime());

    }


    public Map<Long, CouponTool> initCouponTools(List<CheckoutCoupon> checkoutCouponList) {
        Map<Long, CouponTool> toolList = new HashMap<>();
        for (CheckoutCoupon checkoutCoupon : checkoutCouponList) {
            try {
                PromotionToolType promotionToolType = PromotionToolType.fromTypeId(checkoutCoupon.getType());
                if (promotionToolType == null) {
                    continue;
                }
                CouponTool couponTool = couponFactory.getByTypeAndBiz(promotionToolType, this.getBizPlatform());
                boolean success = couponTool.load(checkoutCoupon);
                if (success) {
                    toolList.put(couponTool.getCouponId(), couponTool);
                }
            } catch (Exception e) {
                log.error("loadCurrentCouponTools error. couponId:{}, info:{}, err:", checkoutCoupon.getCouponId(), GsonUtil.toJson(checkoutCoupon), e);
            }
        }
        return toolList;
    }



    public List<Long> preSelectCoupon(CouponCheckoutContext couponCheckoutContext) throws BizError {

        List<Long> selectedCouponIdList = new ArrayList<>();
        for (Long selectCouponId : couponCheckoutContext.getSelectCouponIds()) {
            if (couponToolMap.containsKey(selectCouponId)) {
                selectedCouponIdList.add(selectCouponId);
            }
        }
        if (selectedCouponIdList.size() > maxCount) {
            log.error("MaintenanceCouponCheckoutGroup selectedCouponIdList error! coupons selected in the same group " +
                    "are mutually exclusive, couponToolMap:{}", couponToolMap.keySet());
            throw ExceptionHelper.create(ErrCode.PROMOTION_GROUP_COUPON_MUTEX, "同组选择的券存互斥");
        }

        return selectedCouponIdList;
    }


    @Override
    public void checkoutForSelectCoupons(CouponCheckoutContext couponContext, List<Long> selectedCouponList) throws Exception {
        for (Long couponId : selectedCouponList) {
            Map<Long, CouponTool> selectedMap = new HashMap<>();
            CouponTool couponTool = couponToolMap.get(couponId);
            selectedMap.put(couponId, couponTool);
            //完成结算，获取结算结果
            CouponListCheckoutResult couponListCheckoutResult = doCheckout(selectedMap, couponContext);

            //结果校验
            Map<Long, CouponCheckoutResult> checkoutResultMap = couponListCheckoutResult.getCheckoutResultMap();

            CouponCheckoutResult result = checkoutResultMap.get(couponId);
            if (result == null || !result.isAllow()) {
                log.error("MaintenanceCouponCheckoutGroup preSelectCoupon error! The currently selected coupon is not available, couponId:{}", couponId);
                throw ExceptionHelper.create(ErrCode.PROMOTION_GROUP_COUPON_MUTEX, "选择的券不可使用");
            }
            // 更新优惠券上下文(更新购物车reduceItemList)
            updateByResult(couponContext, result);

            // 更新可用券列表：勾选的券一定是可用的
            List<Coupon> validCouponList = couponListCheckoutResult.getValidCouponList();
            for (Coupon coupon : validCouponList) {
                coupon.setChecked(true);
            }
            couponContext.getSelectedCouponList().addAll(validCouponList);

            //初始化provider
            couponContext.getResourceHandlers().add(initResource(couponContext.getRequest(), couponTool, result));
        }

    }



    @Override
    public CouponProvider initResource(CheckoutPromotionRequest request, CouponTool couponTool, CouponCheckoutResult result)
            throws BizError {
        long orderId = request.getOrderId();
        CouponInfo couponInfo = couponTool.generateCouponInfo(result);
        CouponProvider.ResContent resContent = new CouponProvider.ResContent();
        resContent.setUserId(request.getUserId());
        resContent.setOrgCode(request.getOrgCode());
        resContent.setId(couponInfo.getCouponId());
        if (couponInfo.getCouponCode() != null) {
            resContent.setCode(couponInfo.getCouponCode());
        }
        resContent.setOffline(couponInfo.getOffline());
        resContent.setClientId(request.getClientId());
        resContent.setReplaceMoney(NumberUtil.amountConvertF2Y(couponInfo.getReduceMoney()));
        resContent.setReduceExpress(NumberUtil.amountConvertF2Y(couponInfo.getReduceExpress()));

        resContent.setSubmitType(request.getSubmitType());
        resContent.setBizPlatform(getBizPlatform().getValue());
        resContent.setVid(request.getVid());

        ResourceObject<CouponProvider.ResContent> resourceObject = buildResource(resContent, orderId, String.valueOf(couponInfo.getCouponId()));
        CouponProvider couponProvider = (CouponProvider) resourceProviderFactory.getProvider(ResourceType.COUPON);
        couponProvider.initResource(resourceObject);
        return couponProvider;
    }

    protected <T> ResourceObject<T> buildResource(T content, Long orderId, String couponId) {
        ResourceObject<T> resourceObject = new ResourceObject<>();
        resourceObject.setContent(content);
        resourceObject.setResourceId(String.format("%s_%s", orderId, couponId));
        resourceObject.setResourceType(ResourceType.COUPON);
        resourceObject.setPromotionId(-1L);
        resourceObject.setOrderId(orderId);
        resourceObject.setPid(-1L);
        return resourceObject;
    }

    /**
     * 执行结算逻辑：
     * 根据结算结果添加到可用或不可用
     * 更新可用/不可用信息
     */
    @Override
    public CouponListCheckoutResult doCheckout(Map<Long, CouponTool> couponToolMap, CouponCheckoutContext couponCheckoutContext) {

        CouponListCheckoutResult couponListCheckoutResult = new CouponListCheckoutResult();
        for (CouponTool couponTool : couponToolMap.values()) {
            //构造
            Coupon cartCoupon = couponTool.generateCartCoupon();
            if (cartCoupon.getAllow() == 0) {
                couponListCheckoutResult.addInvalidCoupon(cartCoupon);
                continue;
            }
            try {
                //得到结算结果
                CouponCheckoutResult couponCheckoutResult = couponTool.checkoutCoupon(couponCheckoutContext);
                couponListCheckoutResult.addCheckoutResult(couponCheckoutResult);
                if (couponCheckoutResult.isAllow()) {
                    //更新可用信息
                    cartCoupon.updateValidInfo(couponCheckoutResult.getValidGoodsPrice(),
                            NumberUtil.amountConvertF2Y(couponCheckoutResult.getReduceAmount()),
                            couponCheckoutResult.getReduceAmount());
                    cartCoupon.setSelectedServicePackId(couponCheckoutResult.getSelectedServicePackId());
                    cartCoupon.setAvailableServicePackItems(couponCheckoutResult.getServicePackCouponItemList());
                    couponListCheckoutResult.addValidCoupon(cartCoupon);
                } else {
                    //更新不可用信息
                    cartCoupon.updateInvalidInfo(couponCheckoutResult.getUnusableCode(),
                            couponCheckoutResult.getUnusableReason(),
                            couponCheckoutResult.getKeyDataUnusable());
                    couponListCheckoutResult.addInvalidCoupon(cartCoupon);
                }
            } catch (Exception e) {
                log.error("checkout couponTool error. couponId:{}, type:{}, ex", couponTool.getCouponId(), couponTool.getCouponType(), e);
                if (e instanceof BizError) {
                    cartCoupon.updateInvalidInfo(((BizError) e).getCode(), ((BizError) e).getMsg(), ((BizError) e).getMsg());
                    couponListCheckoutResult.addInvalidCoupon(cartCoupon);
                }
            }
        }
        return couponListCheckoutResult;
    }

    /**
     * 选定某张券来使用并完成分摊
     *
     * @param couponContext
     * @param result
     * @throws Exception
     */
    protected void updateByResult(CouponCheckoutContext couponContext, CouponCheckoutResult result) throws Exception {
        if (result.getReduceAmount() == 0) {
            return;
        }

        CouponTool selectCouponTool = getToolById(result.getCouponId());
        selectCouponTool.updateItemReduce(couponContext, result);
    }


    public CouponTool getToolById(Long couponId) throws BizError {

        if (!couponToolMap.containsKey(couponId)) {
            throw ExceptionHelper.create(ErrCode.ERR_EMPTY_COUPONS, "无效的优惠券");
        }
        return couponToolMap.get(couponId);

    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.MAINTENANCE_REPAIR;
    }
}
