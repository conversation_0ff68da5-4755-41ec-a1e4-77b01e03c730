package com.xiaomi.nr.promotion.model.promotionconfig;

import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import lombok.ToString;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 置换补贴
 */
@ToString
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarOrderReduceConfig extends MultiPromotionConfig{


    private Map<String, ActPriceInfo> orderReduceMap;

    public Map<String, ActPriceInfo> getOrderReduceMap() {
        return orderReduceMap;
    }

    public void setOrderReduceMap(Map<String, ActPriceInfo> orderReduceMap) {
        this.orderReduceMap = orderReduceMap;
    }

}
