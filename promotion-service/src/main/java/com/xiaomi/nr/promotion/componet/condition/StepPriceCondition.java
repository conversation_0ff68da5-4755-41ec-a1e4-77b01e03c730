package com.xiaomi.nr.promotion.componet.condition;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.StepPricePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 阶梯价条件
 *
 * <AUTHOR>
 * @date 2023/2/16
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class StepPriceCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 优惠类型
     */
    private PromotionToolType promotionType;
    /**
     * 阶梯信息
     * key: SSU ID val:ActPriceInfo
     */
    private Map<Long, ActPriceInfo> stepPriceInfoMap;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        List<CartItem> cartList = request.getCartList();
        if (CollectionUtils.isEmpty(cartList)) {
            return false;
        }
        List<GoodsIndex> goodsIndexList = Lists.newLinkedList();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem cartItem = cartList.get(idx);
            if (cartItem.getCannotJoinAct()) {
                continue;
            }
            if (cartItem.getCannotJoinActTypes().contains((long)promotionType.getTypeId())) {
                continue;
            }
            Long ssuId = cartItem.getSsuId();
            Set<Long> actIdSet = context.getSceneActIdsMap().get(ssuId);
            if (!actIdSet.contains(promotionId)) {
                continue;
            }
            ActPriceInfo actPriceInfo = stepPriceInfoMap.get(ssuId);
            if (actPriceInfo == null) {
                continue;
            }
            goodsIndexList.add(new GoodsIndex(cartItem.getItemId(), idx));
        }
        if (CollectionUtils.isEmpty(goodsIndexList)) {
            return false;
        }
        context.setGoodIndex(goodsIndexList);
        return true;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof StepPricePromotionConfig)) {
            log.error("config is not instanceof StepPricePromotionConfig. config:{}", config);
            return;
        }
        StepPricePromotionConfig promotionConfig = (StepPricePromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        this.stepPriceInfoMap = promotionConfig.getStepPriceInfoMap();
    }
}
