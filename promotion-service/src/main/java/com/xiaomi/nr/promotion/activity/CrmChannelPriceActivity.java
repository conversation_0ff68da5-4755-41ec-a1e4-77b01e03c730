package com.xiaomi.nr.promotion.activity;

import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.GetProductActRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.CrmChannelPriceProductRule;
import com.xiaomi.nr.promotion.componet.action.CrmChannelPriceAction;
import com.xiaomi.nr.promotion.componet.condition.CrmChannelPriceCondition;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.PromotionPriceNormProvider;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.CrmChannelPricePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * Crm渠道价
 *
 * <AUTHOR>
 * @date 2023/2/9
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class CrmChannelPriceActivity extends AbstractActivityTool implements ActivityTool, PromotionPriceNormProvider {
    /**
     * 价格信息
     * key: SSU ID val:ActPriceInfo
     */
    private Map<Long, ActPriceInfo> channelPriceInfoMap;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine
                .condition(CrmChannelPriceCondition.class)
                .action(CrmChannelPriceAction.class);
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.B2T_CHANNEL_PRICE;
    }

    /**
     * 获取活动详情
     *
     * @return 活动详情
     */
    @Override
    public ActivityDetail getActivityDetail() {
        return super.getBasicActivityDetail();
    }

    /**
     * 获取优惠价格
     *
     * @param goodsList     详情信息
     * @param contextParams 上下文参数
     * @return key: SSU val: 价格信息
     */
    @Override
    public Map<Long, PromotionPriceDTO> getGoodsPromotionPrice(List<GoodsDto> goodsList, Map<String, String> contextParams) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return Collections.emptyMap();
        }
        Map<Long, PromotionPriceDTO> priceDtoMap = Maps.newHashMap();
        for (GoodsDto goodsDto : goodsList) {
            handlePrice(goodsDto, priceDtoMap);
        }
        return priceDtoMap;
    }

    private void handlePrice(GoodsDto goodsDto, Map<Long, PromotionPriceDTO> priceDtoMap) {
        ActPriceInfo priceInfo = channelPriceInfoMap.get(goodsDto.getSsuId());
        if (priceInfo == null) {
            return;
        }
        long finalPrice = priceInfo.getPrice();
        if (goodsDto.getPrice() != null) {
            finalPrice = Math.min(finalPrice, goodsDto.getPrice());
        }

        CrmChannelPriceProductRule productRule = new CrmChannelPriceProductRule();
        productRule.setCashBackPrice(priceInfo.getCashBackPrice());
        // 包装结果
        PromotionPriceDTO priceDTO = buildPromotionPrice(GsonUtil.toJson(productRule), finalPrice);
        priceDtoMap.put(goodsDto.getSsuId(), priceDTO);
        // 改价
        goodsDto.setPrice(finalPrice);
    }

    /**
     * 获取产品站活动信息：获取PromotionInfo
     *
     * @param request   产品站信息
     * @param hierarchy 商品层级关系
     * @param isOrgTool 是否来源门店工具
     * @return 促销信息
     */
    @Override
    public PromotionInfo getProductAct(GetProductActRequest request, GoodsHierarchy hierarchy, boolean isOrgTool) {
        return null;
    }

    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) throws BizError {
        return super.buildDefaultPromotionInfo(context);
    }

    @Override
    public List<CheckGoodsItem> checkProductsAct(CheckProductsActRequest request, Map<String, GoodsHierarchy> goodsHierarchyMap) throws BizError {
        return null;
    }

    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        return null;
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof CrmChannelPricePromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        CrmChannelPricePromotionConfig promotionConfig = (CrmChannelPricePromotionConfig) config;
        this.channelPriceInfoMap = promotionConfig.getChannelPriceInfoMap();
        return true;
    }
}
