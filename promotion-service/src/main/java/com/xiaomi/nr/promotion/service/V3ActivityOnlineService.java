package com.xiaomi.nr.promotion.service;

import com.xiaomi.nr.promotion.dao.mysql.promotion.V3ActivityOnlineMapper;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.entity.redis.ActivityInfo;
import com.xiaomi.nr.promotion.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/8 21:02
 */
@Component
@Slf4j
public class V3ActivityOnlineService {

    @Autowired
    private V3ActivityOnlineMapper activityOnlineMapper;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    /**
     * 获取活动id
     *
     * @param nowTime
     * @return
     */
    public List<Long> getActivityIds(Long nowTime) {
        String dateTimeStr = DateTimeUtil.formatByPattern(nowTime, "yyyy-MM-dd HH:mm");
        return activityOnlineMapper.listIdApprovedByDateStr(dateTimeStr);
    }


    /**
     * 从缓存中获取活动列表
     * 1. 获取活动ID列表
     * 2. 获取活动ID对应活动缓存
     *
     * @return 活动缓存列表
     */
    public List<ActivityInfo> getActivityListFromCache(Long nowTime) {
        // 获取活动ID列表
        List<Long> actIdList = getActivityIds(nowTime);
        if (CollectionUtils.isEmpty(actIdList)) {
            return Collections.emptyList();
        }
        // 获取活动ID对应活动缓存
        log.info("refresh load count:{} actIdList:{} dateTime:{}", actIdList.size(), actIdList, DateTimeUtil.formatByPattern(nowTime, "yyyy-MM-dd HH:mm"));
        return actIdList.parallelStream()
                .map(this::getActCache)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取活动缓存
     *
     * @param actId 活动ID
     * @return 活动缓存
     */
    private ActivityInfo getActCache(Long actId) {
        ActivityInfo activityInfo = activityRedisDao.getActivityById(actId);
        if (activityInfo == null) {
            log.warn("actId:{} not found cache", actId);
            return null;
        }
        log.info("load act info load ok. actId:{}", actId);
        return activityInfo;
    }
}
