package com.xiaomi.nr.promotion.activity;


import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.GetProductActRequest;
import com.xiaomi.nr.promotion.api.dto.model.CheckGoodsItem;
import com.xiaomi.nr.promotion.api.dto.model.GoodsPriceInfo;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.ProductActOnsaleGoods;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.componet.action.StorePriceAction;
import com.xiaomi.nr.promotion.componet.condition.ChannelCondition;
import com.xiaomi.nr.promotion.componet.condition.OrgCondition;
import com.xiaomi.nr.promotion.componet.condition.StorePriceCondition;
import com.xiaomi.nr.promotion.componet.preparation.GoodsHierarchyPreparation;
import com.xiaomi.nr.promotion.componet.preparation.OrgInfoPreparation;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.GoodsActPriceProvider;
import com.xiaomi.nr.promotion.engine.StoreActPriceProvider;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.ChannelScopeEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.StorePricePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import lombok.extern.slf4j.Slf4j;


/**
 * 门店价活动
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class StorePriceActivity extends AbstractActivityTool implements ActivityTool, StoreActPriceProvider, GoodsActPriceProvider {
    /**
     * 门店价信息Map
     * key: skuPackage val:ActPriceInfo
     */
    private Map<String, ActPriceInfo> storePriceInfoMap;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine
                .conditionPreparation(GoodsHierarchyPreparation.class)
                .conditionPreparation(OrgInfoPreparation.class)
                .condition(OrgCondition.class)
                .condition(ChannelCondition.class)
                .condition(StorePriceCondition.class)
                .action(StorePriceAction.class);
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.STORE_PRICE;
    }

    /**
     * 获取门店价
     *
     * @param skuPackageList SKU or Package列表
     * @param orgCode        orgCode
     * @return 门店价Map key：skuPackage val: 门店价
     */
    @Override
    public Map<String, Long> getStoreActPrice(List<String> skuPackageList, String orgCode) {
        if (CollectionUtils.isEmpty(skuPackageList)) {
            return Collections.emptyMap();
        }
        return skuPackageList.stream()
                .map(storePriceInfoMap::get).filter(Objects::nonNull)
                .filter(info -> info.getPrice() >= 0L)
                .collect(Collectors.toMap(info -> String.valueOf(info.getSkuPackage()), ActPriceInfo::getPrice, (val1, val2) -> val1));
    }

    /**
     * 构建优惠信息
     *
     * @param context 上下文
     * @return 优惠信息
     */
    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) {
        return buildDefaultPromotionInfo(context);
    }

    /**
     * 获取产品站信息
     *
     * @param clientId       应用ID
     * @param orgCode        门店Code
     * @param skuPackageList 活动类别
     */
    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) {
        List<String> joinedSkuPackageList = Lists.newArrayList(includeSkuPackages);
        joinedSkuPackageList.retainAll(skuPackageList);
        if (CollectionUtils.isEmpty(joinedSkuPackageList)) {
            return Collections.emptyMap();
        }
        return joinedSkuPackageList.stream()
                .map(storePriceInfoMap::get).filter(Objects::nonNull)
                .collect(Collectors.toMap(info -> String.valueOf(info.getSkuPackage()), this::convertActInfo, (val1, val2) -> val2));
    }

    /**
     * 获取活动详情
     *
     * @return 活动详情
     */
    @Override
    public ActivityDetail getActivityDetail() {
        ActivityDetail detail = super.getBasicActivityDetail();
        detail.setPriceInfoMap(storePriceInfoMap);
        return detail;
    }


    /**
     * 获取产品站活动信息：获取PromotionInfo
     *
     * @param request   产品站信息
     * @param hierarchy 商品层级关系
     * @param isOrgTool 是否来源门店工具
     * @return 促销信息
     */
    @Override
    public PromotionInfo getProductAct(GetProductActRequest request, GoodsHierarchy hierarchy, boolean isOrgTool) {
        String skuPackage = StringUtils.isNotEmpty(request.getSku()) ? request.getSku() : request.getCommodityId();
        ActPriceInfo priceInfo = storePriceInfoMap.get(skuPackage);
        if (priceInfo == null) {
            log.warn("skuPackage priceInfo is null. skuPackage:{} actId:{}", skuPackage, id);
            return null;
        }
        Long storePrice = priceInfo.getPrice();
        if (storePrice == null || storePrice <= 0L) {
            log.error("skuPackage storePrice error. skuPackage:{} actId:{} storePrice:{}", skuPackage, id, storePrice);
            return null;
        }
        PromotionInfo promotionInfo = new PromotionInfo();
        promotionInfo.setPromotionId(String.valueOf(id));
        promotionInfo.setType(String.valueOf(type.getValue()));
        promotionInfo.setTypeInfo(ActivityTypeEnum.STORE_PRICE.getName());
        promotionInfo.setTitle(name);
        promotionInfo.setTypeCode(metaInfo.getTypeCode());
        promotionInfo.setOffline(offline.getValue());
        promotionInfo.setExtend(String.valueOf(storePrice));
        return promotionInfo;
    }

    @Override
    public List<CheckGoodsItem> checkProductsAct(CheckProductsActRequest request, Map<String, GoodsHierarchy> goodsHierarchyMap) throws BizError {
        return null;
    }

    private ProductActInfo convertActInfo(ActPriceInfo priceInfo) {
        ProductActOnsaleGoods onsaleGoods = new ProductActOnsaleGoods();
        onsaleGoods.setLowerPrice(priceInfo.getPrice());

        ProductActInfo productActInfo = new ProductActInfo();
        productActInfo.setType(type.getValue());
        productActInfo.setId(id);
        productActInfo.setName(name);
        productActInfo.setUnixStartTime(getUnixStartTime());
        productActInfo.setUnixEndTime(getUnixEndTime());
        productActInfo.setOnsaleGoods(onsaleGoods);
        return productActInfo;
    }

    @Override
    public Map<String, List<GoodsPriceInfo>> getGoodsActPrice(List<String> skuPackageList) throws BizError {
        List<String> joinedSkuPackageList = Lists.newArrayList(includeSkuPackages);
        joinedSkuPackageList.retainAll(skuPackageList);
        if (CollectionUtils.isEmpty(joinedSkuPackageList)) {
            return Collections.emptyMap();
        }
        Map<String, List<GoodsPriceInfo>> actMap = Maps.newHashMap();
        for (String skuPackage : joinedSkuPackageList) {
            List<GoodsPriceInfo> priceInfoList = Lists.newArrayList();
            actMap.put(skuPackage, priceInfoList);
            ActPriceInfo onsaleInfo = storePriceInfoMap.get(skuPackage);
            if (onsaleInfo == null) {
                continue;
            }
            GoodsPriceInfo priceInfo = new GoodsPriceInfo();
            priceInfo.setActType(type.getValue());
            priceInfo.setActPrice(onsaleInfo.getPrice());
            priceInfo.setChannelScope(ChannelScopeEnum.PART_STORE.getValue());
            priceInfo.setSelectOrgList(selectOrgList);
            priceInfoList.add(priceInfo);
        }
        return actMap;
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof StorePricePromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        StorePricePromotionConfig promotionConfig = (StorePricePromotionConfig) config;
        this.storePriceInfoMap = promotionConfig.getStorePriceInfoMap();
        return true;
    }


}
