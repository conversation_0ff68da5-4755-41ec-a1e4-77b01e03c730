package com.xiaomi.nr.promotion.model.promotionconfig;

import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import lombok.ToString;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 置换补贴
 */
@ToString
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarExchangeSubsidyConfig extends MultiPromotionConfig{


    private Map<String, ActPriceInfo> exchangeSubsidyInfoMap;

    public Map<String, ActPriceInfo> getExchangeSubsidyMap() {
        return exchangeSubsidyInfoMap;
    }

    public void setExchangeSubsidyInfoMap(Map<String, ActPriceInfo> exchangeSubsidyInfoMap) {
        this.exchangeSubsidyInfoMap = exchangeSubsidyInfoMap;
    }

}
