package com.xiaomi.nr.promotion.tool;

import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.CartItemChild;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @description 优惠分摊工具类——适用车商城&汽车售后
 * @date 2025-01-07 10:02
 */
@Slf4j
@Component
public class CheckoutCartToolV2 {

    /**
     * 优惠分摊
     *
     * @param totalReduceMoney 优惠总金额
     * @param indexList        有效商品索引
     * @param cartItemList     购物车商品列表
     * @param promotionType    优惠类型
     * @param promotionId      优惠ID
     */
    public void divideCartsReduce(Long totalReduceMoney, List<Integer> indexList, List<CartItem> cartItemList, Integer promotionType, Long promotionId) throws BizError {
        if (totalReduceMoney <=0 ) {
            log.error("totalReduceMoney is less than 0, promotionType:{}, promotionId:{}", promotionType, promotionId);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠总金额不能小于等于0");
        }
        if (CollectionUtils.isEmpty(cartItemList) || CollectionUtils.isEmpty(indexList)) {
            log.error("cartItemList or indexList is empty, promotionType:{}, promotionId:{}", promotionType, promotionId);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "购物车商品列表或有效商品索引不能为空");
        }
        if (Objects.isNull(promotionType) || Objects.isNull(promotionId)) {
            log.error("promotionType or promotionId is null, promotionType:{}, promotionId:{}", promotionType, promotionId);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "优惠类型和优惠ID不能为空");
        }
        // 选出有效购物车商品列表
        List<CartItem> fillCartList = CartHelper.getCartList(cartItemList, indexList);
        // 执行优惠分摊
        divideCartsReduce(totalReduceMoney, fillCartList, promotionType, promotionId);
    }

    /**
     * 优惠分摊
     *
     * @param totalReduceMoney 优惠总金额
     * @param fillCartList     有效商品列表
     * @param promotionType    优惠类型
     * @param promotionId      优惠ID
     */
    public void divideCartsReduce(Long totalReduceMoney, List<CartItem> fillCartList, Integer promotionType, Long promotionId) throws BizError {
        long originalTotal = CartHelper.getTotalPrice(fillCartList);

        if (originalTotal == 0) {
            log.warn("carts original total price is 0, promotionType:{}, promotionId:{}", promotionType, promotionId);
            return;
        }

        // 按比例分摊，向下取整可能会有没分摊完的部分
        long totalActualReduceMoney = updateCartsReduce(fillCartList, totalReduceMoney, originalTotal, promotionType, promotionId);

        // 处理没分摊完的部分
        long diffReduce = totalReduceMoney - totalActualReduceMoney;
        if (diffReduce > 0) {
            updateCartsReduceDiff(fillCartList, diffReduce, promotionType, promotionId);
        }
    }

    /**
     * 按比例分摊
     *
     * @param cartList         购物车商品列表
     * @param totalReduceMoney 总优惠金额
     * @param originalTotal    购物车总价
     * @param promotionType    优惠类型
     * @param promotionId      优惠ID
     * @return 实际优惠分摊金额总和
     */
    private long updateCartsReduce(List<CartItem> cartList, Long totalReduceMoney, Long originalTotal, Integer promotionType, Long promotionId) throws BizError {
        long totalActualReduceMoney = 0L;
        for (CartItem cartItem : cartList) {
            long curPrice = CartHelper.itemCurPrice(cartItem);
            long reduceMoney = Math.floorDiv(curPrice * totalReduceMoney, originalTotal);
            if (reduceMoney <= 0) {
                continue;
            }
            // 更新商品优惠明细
            updateReduceItemList(cartItem, reduceMoney, promotionType, promotionId);

            totalActualReduceMoney += reduceMoney;
        }
        return totalActualReduceMoney;
    }

    /**
     * 一分一分地分摊
     *
     * @param cartList      购物车商品列表
     * @param diffMoney     优惠金额
     * @param promotionType 优惠类型
     * @param promotionId   优惠ID
     */
    private void updateCartsReduceDiff(List<CartItem> cartList, Long diffMoney, Integer promotionType, Long promotionId) throws BizError {
        if (diffMoney <= 0) {
            log.warn("diffMoney is less than or equal to 0, diffMoney:{}, cartList:{}, promotionId:{}", diffMoney, GsonUtil.toJson(cartList), promotionId);
            return;
        }
        long eachDiff = 1L;
        while (true) {
            boolean updated = false;
            for (CartItem cartItem : cartList) {
                if (diffMoney <= 0) {
                    break;
                }
                long curPrice = CartHelper.itemCurPrice(cartItem);
                if (curPrice <= 0L) {
                    continue;
                }
                // 更新商品优惠明细
                updateReduceItemList(cartItem, eachDiff, promotionType, promotionId);

                diffMoney -= eachDiff;
                updated = true;
            }
            if (!updated) {
                break;
            }
        }
    }

    /**
     * 更新商品优惠明细
     *
     * @param cartItem      购物车商品项
     * @param reduceMoney   优惠金额
     * @param promotionType 优惠类型
     * @param promotionId   优惠ID
     */
    private void updateReduceItemList(CartItem cartItem, Long reduceMoney, Integer promotionType, Long promotionId) throws BizError {

        if (CartHelper.isPackage(cartItem)) {
            // 更新套装优惠信息
            updatePackageReduceItemList(cartItem, reduceMoney, promotionType, promotionId);
        } else {
            // 更新单品优惠信息
            updateSingleReduceItemList(cartItem, reduceMoney, promotionType, promotionId);
        }

        // 记录商品总减免金额，直降活动除外
        if (!Objects.equals(promotionType, PromotionToolType.ONSALE.getTypeId())) {
            cartItem.setReduceAmount(cartItem.getReduceAmount() + reduceMoney);
        }
    }


    /**
     * 更新单品的优惠信息
     *
     * @param cartItem      购物车商品项
     * @param reduceMoney   优惠金额
     * @param promotionType 优惠类型
     * @param promotionId   优惠ID
     */
    private void updateSingleReduceItemList(CartItem cartItem, Long reduceMoney, Integer promotionType, Long promotionId) {

        List<ReduceDetailItem> reduceItemList = Optional.ofNullable(cartItem.getReduceItemList()).orElseGet(ArrayList::new);

        ReduceDetailItem reduceDetailItem = reduceItemList.stream()
                .filter(x -> Objects.nonNull(x.getPromotionId())
                        && Objects.nonNull(x.getPromotionType())
                        && Objects.nonNull(x.getSsuId())
                        && Objects.equals(promotionId, x.getPromotionId())
                        && Objects.equals(promotionType, x.getPromotionType())
                        && Objects.equals(cartItem.getSsuId(), x.getSsuId()))
                .findAny().orElse(null);

        if (Objects.isNull(reduceDetailItem)) {
            reduceDetailItem = new ReduceDetailItem()
                    .setPromotionId(promotionId)
                    .setPromotionType(promotionType)
                    .setSsuId(cartItem.getSsuId())
                    .setReduce(reduceMoney)
                    .setReduceSingle(reduceMoney / cartItem.getCount());
            reduceItemList.add(reduceDetailItem);
        } else {
            reduceDetailItem.setReduce(reduceDetailItem.getReduce() + reduceMoney);
            reduceDetailItem.setReduceSingle(reduceDetailItem.getReduce() / cartItem.getCount());
        }
    }

    /**
     * 更新套装商品优惠信息
     *
     * @param cartItem      套装商品
     * @param reduceMoney   优惠金额
     * @param promotionType 优惠类型
     * @param promotionId   优惠ID
     */
    private void updatePackageReduceItemList(CartItem cartItem, Long reduceMoney, Integer promotionType, Long promotionId) throws BizError {
        long itemCurPrice = CartHelper.itemCheckoutAmount(cartItem);
        if (itemCurPrice <= 0L) {
            log.warn("itemCurPrice is less than or equal to 0, itemCurPrice:{}, cartItem:{}", itemCurPrice, GsonUtil.toJson(cartItem));
            return;
        }

        // 套装优惠后价格
        long reducedPrice = itemCurPrice - reduceMoney;
        // 记录实际分摊金额总和
        long totalActualReduceMoney = 0L;

        // 子品按价格从大到小排序
        List<CartItemChild> sortedChildren = cartItem.getChilds().stream()
                .sorted(Comparator.comparing(childItem -> CartHelper.itemChildCheckoutAmount(cartItem, (CartItemChild) childItem)).reversed()).toList();

        // 前n-1个子品按价格比例分摊
        for (int i = 0; i < sortedChildren.size() - 1; i++) {
            CartItemChild child = sortedChildren.get(i);
            long childCurPrice = CartHelper.itemChildCheckoutAmount(cartItem, child);
            // 子分摊优惠后的售价
            long reducedChildPrice = Math.floorDiv(childCurPrice * reducedPrice, itemCurPrice);
            // 子品实际分摊的金额
            long childActualReduceMoney = childCurPrice - reducedChildPrice;
            // 更新子品优惠明细
            updateChildReduceItem(cartItem, child, childActualReduceMoney, promotionType, promotionId);
            // 记录实际分摊金额总和
            totalActualReduceMoney += childActualReduceMoney;
        }
        // 剩余未分摊金额
        long remainReduceMoney = reduceMoney - totalActualReduceMoney;

        if (remainReduceMoney == 0L) {
            return;
        }

        // 需要额外处理的子品
        CartItemChild specialChild = remainReduceMoney > 0 ? sortedChildren.getLast() : sortedChildren.getFirst();

        //若剩余分摊金额为整数，则直接分摊到最后一个子品上；否则，因为计算子品优惠后价格时向下取整，前n-1个子品可能会多分摊，导致剩余分摊金额为负，此时在价格最高的子品上进行扣减
        updateChildReduceItem(cartItem, specialChild, remainReduceMoney, promotionType, promotionId);
    }

    /**
     * 更新套装商品优惠信息
     *
     * @param cartItem      套装商品
     * @param childItem     子品
     * @param reduceMoney   优惠金额
     * @param promotionType 优惠类型
     * @param promotionId   优惠ID
     */
    private void updateChildReduceItem(CartItem cartItem, CartItemChild childItem, Long reduceMoney, Integer promotionType, Long promotionId) throws BizError {

        List<ReduceDetailItem> reduceItemList = Optional.ofNullable(cartItem.getReduceItemList()).orElseGet(ArrayList::new);

        ReduceDetailItem reduceDetailItem = reduceItemList.stream()
                .filter(x -> Objects.nonNull(x.getPromotionId())
                        && Objects.nonNull(x.getPromotionType())
                        && Objects.nonNull(x.getSsuId())
                        && Objects.equals(promotionId, x.getPromotionId())
                        && Objects.equals(promotionType, x.getPromotionType())
                        && Objects.equals(childItem.getSsuId(), x.getSsuId()))
                .findAny().orElse(null);

        if (Objects.isNull(reduceDetailItem)) {
            // reduceMoney为负数情况只能是在价格最高的子品上扣减优惠金额，此情况下，reduceDetailItem不能为空
            if (reduceMoney < 0L) {
                log.error("reduceMoney is less than 0, reduceMoney: {}, cartItem: {}, childItem: {}", reduceMoney, GsonUtil.toJson(cartItem), GsonUtil.toJson(childItem));
                throw ExceptionHelper.create(GeneralCodes.InternalError, "优惠分摊异常");
            }

            reduceDetailItem = new ReduceDetailItem()
                    .setPromotionId(promotionId)
                    .setPromotionType(promotionType)
                    .setSsuId(childItem.getSsuId())
                    .setReduce(reduceMoney)
                    .setReduceSingle(reduceMoney / childItem.getCount());
            reduceItemList.add(reduceDetailItem);
        } else {
            reduceDetailItem.setReduce(reduceDetailItem.getReduce() + reduceMoney);
            reduceDetailItem.setReduceSingle(reduceDetailItem.getReduce() / childItem.getCount());
        }
    }
}
