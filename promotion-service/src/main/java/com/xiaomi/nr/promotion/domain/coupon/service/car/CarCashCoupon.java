package com.xiaomi.nr.promotion.domain.coupon.service.car;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.SubmitTypeEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.api.dto.model.OrderCartItem;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.api.dto.model.car.ReduceDetailExtInfo;
import com.xiaomi.nr.promotion.constant.CouponConstant;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.service.base.type.AbstractCashCoupon;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.CouponCheckoutStageEnum;
import com.xiaomi.nr.promotion.enums.CouponQuotaTypeEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by wangweiyi on 2023/9/21
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class CarCashCoupon extends AbstractCashCoupon {

    private Integer checkoutStage;

    /**
     * 1. 判断是否为改配场景
     * 2. 改配场景下， 使用定金券 != 该券，则该券不能用
     * 3. 改配场景下， 使用尾款券。 则定金券不能用
     * @param request
     * @param context
     * @return
     */
    @Override
    public Boolean checkCoupon(CheckoutPromotionRequest request, CheckoutContext context) {

        // 改配场景
        if (Objects.equals(request.getSubmitType(), SubmitTypeEnum.REPLACE.getCode()) && Objects.nonNull(request.getUsedCouponId())) {
            if (Objects.equals(context.getUsedCouponCheckoutStage(), CouponCheckoutStageEnum.BOOKING.getValue()) && !Objects.equals(request.getUsedCouponId(), getCouponId())) {
                return false;
            }

            if (Objects.equals(context.getUsedCouponCheckoutStage(), CouponCheckoutStageEnum.PAYMENT_FINAL.getValue()) && Objects.equals(checkoutStage, CouponCheckoutStageEnum.BOOKING.getValue())) {
                return false;
            }
        }

        return true;
    }

    @Override
    public boolean load(CheckoutCoupon checkoutCoupon) throws BizError {
        super.load(checkoutCoupon);
        this.checkoutStage = checkoutCoupon.getCheckoutStage();
        return true;
    }

    public long getCartTotalPrice(List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(cartList)) {
            return 0L;
        }

        if (Objects.equals(checkoutStage, CouponCheckoutStageEnum.BOOKING.getValue())) {
            return cartList.stream()
                    .filter(item -> !SourceEnum.isGift(item.getSource()))
                    .filter(item -> item.getPrePrice() > 0)
                    .mapToLong(OrderCartItem::getPrePrice)
                    .sum();
        } else if (Objects.equals(checkoutStage, CouponCheckoutStageEnum.PAYMENT_FINAL.getValue())) {
            return cartList.stream()
                    .filter(item -> !SourceEnum.isGift(item.getSource()))
                    .filter(item -> CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList()) - item.getPrePrice() > 0)
                    .mapToLong(item -> CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList()) - item.getPrePrice())
                    .sum();
        } else {
            return 0L;
        }
    }

    protected ValidGoods buildValidGoods(List<CartItem> cartList, List<GoodsIndex> goodsInd) {
        long validPrice = 0L;
        long includeCount = 0L;
        int len = cartList.size();
        for (GoodsIndex index : goodsInd) {
            int idx = index.getIndex();
            if (idx >= len) {
                continue;
            }
            CartItem item = cartList.get(idx);
            if (item == null) {
                continue;
            }

            Long curPrice = 0L;
            if (Objects.equals(checkoutStage, CouponCheckoutStageEnum.BOOKING.getValue())) {
                curPrice = item.getPrePrice();
            } else if (Objects.equals(checkoutStage, CouponCheckoutStageEnum.PAYMENT_FINAL.getValue())) {
                curPrice = CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList()) - item.getPrePrice();
            } else {
                curPrice = CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList());
            }

            validPrice += (curPrice * item.getCount());
            includeCount += item.getCount();
        }
        ValidGoods validGoods = new ValidGoods();
        validGoods.setValidNum(includeCount);
        validGoods.setValidPrice(validPrice);
        return validGoods;
    }


    protected List<GoodsIndex> matchCartList(List<CartItem> cartList, CheckoutContext context)
            throws BizError {

        // 查找符合包含条件的购物车item列表
        List<GoodsIndex> indexList = new ArrayList<>();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);

            if (SourceEnum.isGiftBargain(item.getSource())) {
                continue;
            }
            //处理当前item是否可参加活动
            boolean itemQualify = CartHelper.isCouponQualifyItem(item, (long) getCouponType().getType());
            if (!itemQualify) {
                log.debug("coupon:{}  matchCartList itemQualify item:{}", getCouponId(), GsonUtil.toJson(item));
                continue;
            }
            // 做单品或套装的匹配
            boolean isMatched;

            isMatched = includeGoods.contains(getJoinGoods(item));
            if (!isMatched) {
                log.debug("coupon:{} matchCartList isMatched item:{}", getCouponId(), GsonUtil.toJson(item));
                continue;
            }
            indexList.add(new GoodsIndex(item.getItemId(), idx));
        }
        return indexList;
    }

    @Override
    public void updateCartsReduce(CheckoutPromotionRequest request, CheckoutContext context, CouponCheckoutResult result) {
        //处理商品分摊
        updateItemReduce(request.getCartList(), result);


        //更新context
        updateCommonInfo(request, context);
    }

    @Override
    public void updateItemReduce(List<CartItem> cartList, CouponCheckoutResult result) {

        if (CollectionUtils.isEmpty(cartList)) {
            return;
        }
        Map<String, CartItem> itemMap = cartList.stream().collect(Collectors.toMap(cartItem -> cartItem.getItemId(), Function.identity()));

        GoodsIndex valid = result.getValidGoods().get(0);
        CartItem item = itemMap.get(valid.getItemId());

        Long reduceSingle = result.getReduceAmount() / item.getCount();

        //TODO 后续处理多件商品的分摊逻辑
        ReduceDetailExtInfo extInfo = new ReduceDetailExtInfo();
        extInfo.setCheckoutStage(checkoutStage);

        ReduceDetailItem detailItem = new ReduceDetailItem()
            .setPromotionId(getCouponId())
            .setPromotionType(getType().getTypeId())
            .setSsuId(item.getSsuId())
            .setReduce(result.getReduceAmount())
            .setReduceSingle(reduceSingle)
            .setBudgetApplyNo(result.getBudgetApplyNo())
            .setLineNum(result.getLineNum()).setExtInfo(extInfo);
        List<ReduceDetailItem> reduceDetailItems = Optional.ofNullable(item.getReduceItemList()).orElseGet(() -> new ArrayList<>());
        reduceDetailItems.add(detailItem);
    }

    public Long getJoinGoods(CartItem cartItem) {
        return cartItem.getSsuId();
    }

    /**
     * 使用的券为尾款券，则改配场景才能退券
     * @return
     */
    public boolean canReturnUsedCoupon() {
        return Objects.equals(checkoutStage, CouponCheckoutStageEnum.PAYMENT_FINAL.getValue());
    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR;
    }
}
