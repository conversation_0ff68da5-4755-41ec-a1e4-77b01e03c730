package com.xiaomi.nr.promotion.domain.coupon.service.car;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.service.base.type.AbstractCashCoupon;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by wangweiyi on 2023/9/21
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class CarCashCoupon extends AbstractCashCoupon {



    public long getCartTotalPrice(List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(cartList)) {
            return 0L;
        }
        return cartList.stream()
                .filter(item -> !SourceEnum.isGift(item.getSource()))
                .mapToLong(item -> CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList()))
                .sum();
    }

    protected ValidGoods buildValidGoods(List<CartItem> cartList, List<GoodsIndex> goodsInd) {
        long validPrice = 0L;
        long includeCount = 0L;
        int len = cartList.size();
        for (GoodsIndex index : goodsInd) {
            int idx = index.getIndex();
            if (idx >= len) {
                continue;
            }
            CartItem item = cartList.get(idx);
            if (item == null) {
                continue;
            }

            Long curPrice = CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList());

            validPrice += (curPrice * item.getCount());
            includeCount += item.getCount();
        }
        ValidGoods validGoods = new ValidGoods();
        validGoods.setValidNum(includeCount);
        validGoods.setValidPrice(validPrice);
        return validGoods;
    }


    protected List<GoodsIndex> matchCartList(List<CartItem> cartList, CheckoutContext context)
            throws BizError {

        // 查找符合包含条件的购物车item列表
        List<GoodsIndex> indexList = new ArrayList<>();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);

            if (SourceEnum.isGiftBargain(item.getSource())) {
                continue;
            }
            //处理当前item是否可参加活动
            boolean itemQualify = CartHelper.isCouponQualifyItem(item, (long) getCouponType().getType());
            if (!itemQualify) {
                log.debug("coupon:{}  matchCartList itemQualify item:{}", getCouponId(), GsonUtil.toJson(item));
                continue;
            }
            // 做单品或套装的匹配
            boolean isMatched;

            isMatched = includeGoods.contains(getJoinGoods(item));
            if (!isMatched) {
                log.debug("coupon:{} matchCartList isMatched item:{}", getCouponId(), GsonUtil.toJson(item));
                continue;
            }
            indexList.add(new GoodsIndex(item.getItemId(), idx));
        }
        return indexList;
    }

    @Override
    public void updateCartsReduce(CheckoutPromotionRequest request, CheckoutContext context, CouponCheckoutResult result) {
        //处理商品分摊
        updateItemReduce(request.getCartList(), result);


        //更新context
        updateCommonInfo(request, context);
    }

    @Override
    public void updateItemReduce(List<CartItem> cartList, CouponCheckoutResult result) {

        if (CollectionUtils.isEmpty(cartList)) {
            return;
        }
        Map<String, CartItem> itemMap = cartList.stream().collect(Collectors.toMap(cartItem -> cartItem.getItemId(), Function.identity()));

        GoodsIndex valid = result.getValidGoods().get(0);
        CartItem item = itemMap.get(valid.getItemId());

        Long reduceSingle = result.getReduceAmount() / item.getCount();

        //TODO 后续处理多件商品的分摊逻辑
        ReduceDetailItem detailItem = new ReduceDetailItem()
            .setPromotionId(getCouponId())
            .setPromotionType(getType().getTypeId())
            .setSsuId(item.getSsuId())
            .setReduce(result.getReduceAmount())
            .setReduceSingle(reduceSingle)
            .setBudgetApplyNo(result.getBudgetApplyNo())
            .setLineNum(result.getLineNum());
        List<ReduceDetailItem> reduceDetailItems = Optional.ofNullable(item.getReduceItemList()).orElseGet(() -> new ArrayList<>());
        reduceDetailItems.add(detailItem);
    }

    public Long getJoinGoods(CartItem cartItem) {
        return cartItem.getSsuId();
    }


    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR;
    }
}
