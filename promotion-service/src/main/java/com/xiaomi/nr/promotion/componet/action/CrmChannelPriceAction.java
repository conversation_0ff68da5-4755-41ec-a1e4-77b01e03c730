package com.xiaomi.nr.promotion.componet.action;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.StepPriceProductRule;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.PromotionExtend;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.CrmChannelPricePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * CRM渠道价行为表达
 *
 * <AUTHOR>
 * @date 2023/2/14
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CrmChannelPriceAction extends AbstractAction {
    /**
     * 优惠促销ID
     */
    private Long promotionId;
    /**
     * 优惠类型
     */
    private PromotionToolType promotionType;
    /**
     * 阶梯信息
     * key: SSU ID val:ActPriceInfo
     */
    private Map<Long, ActPriceInfo> channelPriceInfoMap;

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (MapUtil.isEmpty(channelPriceInfoMap)) {
            log.error("channelPriceInfoMap is empty. actId:{} uid:{}", promotionId, request.getUserId());
            return;
        }
        List<GoodsIndex> indexList = context.getGoodIndex();
        List<CartItem> cartList = request.getCartList();
        if (CollectionUtils.isEmpty(indexList) || CollectionUtils.isEmpty(cartList)) {
            log.warn("channelPrice indexList or carts is empty. actId:{} uid:{}", promotionId, indexList);
            return;
        }

        // 进行改价
        changePrice(cartList, indexList);

        // 设置结果
        setResult(context, cartList, promotion);
    }

    private void changePrice(List<CartItem> cartList, List<GoodsIndex> indexList) {
        for (GoodsIndex index : indexList) {
            CartItem item = cartList.get(index.getIndex());
            if (item == null || !Objects.equals(item.getItemId(), index.getItemId())) {
                log.warn("channelPrice idx not found in cats. idx:{}, item:{}", index, item);
                continue;
            }
            Long ssuId = item.getSsuId();
            // 获取价格
            ActPriceInfo priceInfo = channelPriceInfoMap.get(ssuId);
            if (priceInfo == null) {
                continue;
            }
            long finalPrice = Math.min(item.getCartPrice(), priceInfo.getPrice());
            if (finalPrice < item.getCartPrice()) {
                changePriceWithReduceDetail(item, finalPrice, promotionId, promotionType);
            }
        }
    }

    private void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool) throws BizError {
        List<String> parentItemList = CartHelper.getParentItemIdList(context.getGoodIndex());
        List<Long> ssuIdList = cartList.stream()
                .filter(item -> parentItemList.contains(item.getItemId()))
                .map(CartItem::getSsuId)
                .distinct()
                .collect(Collectors.toList());
        List<StepPriceProductRule> ruleList = ssuIdList.stream()
                .map(channelPriceInfoMap::get).filter(Objects::nonNull)
                .map(this::convert)
                .collect(Collectors.toList());
        PromotionExtend extend = new PromotionExtend();
        extend.setChannelpriceExtend(ruleList);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setJoinedItemId(parentItemList);
        promotionInfo.setParentItemId(parentItemList);
        promotionInfo.setJoined(BooleanEnum.YES.getValue());
        promotionInfo.setExtend(GsonUtil.toJson(extend));

        // 设置上下文结果
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    private StepPriceProductRule convert(ActPriceInfo priceInfo) {
        StepPriceProductRule priceRule = new StepPriceProductRule();
        priceRule.setCashBackPrice(priceInfo.getCashBackPrice());
        return priceRule;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof CrmChannelPricePromotionConfig)) {
            log.error("config is not instanceof CrmChannelPricePromotionConfig. class:{}", config.getName());
            return;
        }
        CrmChannelPricePromotionConfig promotionConfig = (CrmChannelPricePromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        this.channelPriceInfoMap = promotionConfig.getChannelPriceInfoMap();
    }
}
