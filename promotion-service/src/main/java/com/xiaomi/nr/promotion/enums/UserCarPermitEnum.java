package com.xiaomi.nr.promotion.enums;

/**
 * <AUTHOR>
 * @date 2025/5/10 13:43
 */
public enum UserCarPermitEnum {

    UNKNOWN(-1, "未知"),
    NONE(0, "无"),
    OWNER(1, "车主"),
    UNDELIVERED_OWNER(2, "准车主"),
    OWNER_AND_UNDELIVERED_OWNER(3, "车主和准车主"),
    ;

    private final int code;

    private final String desc;

    UserCarPermitEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
