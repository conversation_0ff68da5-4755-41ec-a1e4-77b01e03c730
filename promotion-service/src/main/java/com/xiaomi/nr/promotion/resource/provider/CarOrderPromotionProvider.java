package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.dao.mysql.promotionuser.OrderPromotionDetailMapper;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.OrderPromotionDetailStatusMapper;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.OrderPromotionDetail;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.OrderPromotionDetailStatus;
import com.xiaomi.nr.promotion.enums.OrderTypeEnum;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.OrderPromotionStatusEnum;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class CarOrderPromotionProvider implements ResourceProvider<CarOrderPromotionProvider.OrderPromotionDetailResource> {

    private ResourceObject<CarOrderPromotionProvider.OrderPromotionDetailResource> resourceObject;

    @Autowired
    private OrderPromotionDetailMapper orderPromotionDetailMapper;

    @Autowired
    private OrderPromotionDetailStatusMapper orderPromotionDetailStatusMapper;

    @Override
    public ResourceObject<OrderPromotionDetailResource> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<OrderPromotionDetailResource> object) {
        this.resourceObject = object;
    }

    private OrderPromotionDetailStatus resourceConvertEntityStatusList(OrderPromotionDetailResource resource, List<OrderPromotionDetail> oldPromotionDetailList) {
        OrderPromotionDetailStatus res=new OrderPromotionDetailStatus();
        res.setOrderId(resource.getOrderId());
        res.setOrderScene(resource.getOrderScene());
        res.setOrderType(resource.getOrderType());
        res.setUserId(resource.getUserId());
        res.setPromotionId(-1L);
        res.setPromotionType(-1);
        res.setStatus(OrderPromotionStatusEnum.LOCK.getValue());

        PromotionDetail promotionDetail=new PromotionDetail();
        List<OrderPromotionDetail> newPromotionDetailList=new ArrayList<>();
        promotionDetail.setNewPromotionDetailList(newPromotionDetailList);
        // 新增优惠
        resource.getPromotionDetailList().forEach(po -> {
            OrderPromotionDetail newDetail = new OrderPromotionDetail();
            newDetail.setOrderId(resource.getOrderId());
            newDetail.setOrderScene(resource.getOrderScene());
            newDetail.setOrderType(resource.getOrderType());
            newDetail.setUserId(resource.getUserId());
            newDetail.setPromotionId(po.getPromotionId());
            newDetail.setPromotionType(po.getPromotionType());
            newDetail.setPromotionDetail(po.getPromotionDetail());
            newPromotionDetailList.add(newDetail);
        });
        // 老优惠
        promotionDetail.setOldPromotionDetailList(oldPromotionDetailList);
        res.setPromotionDetail(GsonUtil.toJson(promotionDetail));
        return res;
    }

    @Transactional(transactionManager = "promotionUserConfigTransactionManager", rollbackFor = {Exception.class, BizError.class})
    @Override
    public void lock() throws BizError {
        OrderPromotionDetailResource content = resourceObject.getContent();
        // 1、重复下单 先删除上次状态表记录,以最后一次checkout为准
        orderPromotionDetailStatusMapper.deleteByOrderIdAndOrderTypeAndStatus(content.getOrderId(), content.getOrderType(), OrderPromotionStatusEnum.LOCK.getValue());
        // 2、查询老优惠，联合新优惠，合并为一条数据落库
        List<OrderPromotionDetail> oldPromotionDetailList = orderPromotionDetailMapper.getByOrderIdAndOrderType(content.getOrderId(), content.getOrderType());
        OrderPromotionDetailStatus orderPromotionDetailStatus = resourceConvertEntityStatusList(content,oldPromotionDetailList);
        // 3、锁定优惠数据新增
        orderPromotionDetailStatusMapper.insertList(List.of(orderPromotionDetailStatus));
    }



    @Transactional(transactionManager = "promotionUserConfigTransactionManager", rollbackFor = {Exception.class, BizError.class})
    @Override
    public void consume() throws BizError {
        Long orderId = resourceObject.getOrderId();
        int orderType = OrderTypeEnum.NORMAL_ORDER.getOrderType();
        // 1、查询锁定快照
        List<OrderPromotionDetailStatus> snapshotList = orderPromotionDetailStatusMapper.getByOrderIdAndOrderTypeAndStatus(orderId, orderType, OrderPromotionStatusEnum.LOCK.getValue());
        // 2、如果查不到锁定快照，提交异常
        if (snapshotList==null||snapshotList.isEmpty()){
            log.warn("CarOrderPromotionProvider consume snapshotList is empty. orderId:{}", orderId);
            return;
        }
        // 3、解析新增最新明细
        String promotionDetailStr = snapshotList.getLast().getPromotionDetail();
        PromotionDetail promotionDetail = GsonUtil.fromJson(promotionDetailStr, PromotionDetail.class);
        if (promotionDetail==null){
            log.warn("CarOrderPromotionProvider consume promotionDetail is null. orderId:{}", orderId);
            return;
        }
        // 4、改配删除历史明细
        orderPromotionDetailMapper.deleteByOrderIdAndOrderType(orderId, orderType);
        // 5、新增最新明细
        List<OrderPromotionDetail> newPromotionDetailList = promotionDetail.getNewPromotionDetailList();
        if (newPromotionDetailList!=null&&!newPromotionDetailList.isEmpty()){
            orderPromotionDetailMapper.insertList(newPromotionDetailList);
        }
        // 6、状态表更新为已提交
        orderPromotionDetailStatusMapper.updateStatusByOrderIdAndOrderType(orderId,orderType,OrderPromotionStatusEnum.COMMIT.getValue(),OrderPromotionStatusEnum.LOCK.getValue());
    }


    @Transactional(transactionManager = "promotionUserConfigTransactionManager", rollbackFor = {Exception.class, BizError.class})
    @Override
    public void rollback() throws BizError {
        Long orderId = resourceObject.getOrderId();
        int orderType = OrderTypeEnum.NORMAL_ORDER.getOrderType();
        // 1、查询最近一条提交记录
        OrderPromotionDetailStatus recentStatus = orderPromotionDetailStatusMapper.getRecentDetail(orderId, orderType, OrderPromotionStatusEnum.COMMIT.getValue());
        if (recentStatus==null){
            log.warn("CarOrderPromotionProvider rollback recentStatus is null. orderId:{}", orderId);
            return;
        }
        String promotionDetailStr = recentStatus.getPromotionDetail();
        PromotionDetail promotionDetail = GsonUtil.fromJson(promotionDetailStr, PromotionDetail.class);
        if (promotionDetail==null){
            log.warn("CarOrderPromotionProvider rollback promotionDetail is null. orderId:{}", orderId);
            return;
        }
        // 2、先删除上次提交的新增明细
        orderPromotionDetailMapper.deleteByOrderIdAndOrderType(orderId, orderType);
        // 3、再恢复历史明细
        List<OrderPromotionDetail> oldPromotionDetailList = promotionDetail.getOldPromotionDetailList();
        if (oldPromotionDetailList!=null&&!oldPromotionDetailList.isEmpty()){
            orderPromotionDetailMapper.insertList(oldPromotionDetailList);
        }
        // 状态表更新为回滚
        orderPromotionDetailStatusMapper.updateStatusByIdAndStatus(orderId,recentStatus.getPromotionId(),OrderPromotionStatusEnum.ROLLBACK.getValue(),OrderPromotionStatusEnum.COMMIT.getValue());
//        orderPromotionDetailStatusMapper.updateStatusByOrderIdAndOrderType(orderId,orderType,OrderPromotionStatusEnum.ROLLBACK.getValue(),OrderPromotionStatusEnum.COMMIT.getValue());
    }

    @Override
    public String conflictText() {
        return null;
    }

    @Data
    private static class PromotionDetail{
        /**
         * 新增优惠明细
         */
        List<OrderPromotionDetail> newPromotionDetailList;
        /**
         * 历史优惠明细
         */
        List<OrderPromotionDetail> oldPromotionDetailList;
    }

    @Data
    public static class OrderPromotionDetailResource {
        /**
         * 订单id
         */
        private Long orderId;
        /**
         * 下单类型：普通交易、追加交易，OrderTypeEnum
         */
        private Integer orderType;
        /**
         * 下单场景：BizPlatformEnum
         */
        private Integer orderScene;
        /**
         * 用户id
         */
        private Long userId;
        /**
         * 优惠明细
         */
        private List<OrderPromotionDetailPo> promotionDetailList;
    }

    @Data
    public static class OrderPromotionDetailPo {
        /**
         * 促销id
         */
        private Long promotionId;
        /**
         * 下单场景：PromotionType
         */
        private Integer promotionType;
        /**
         * 优惠明细分摊json
         */
        private String promotionDetail;


    }


}
