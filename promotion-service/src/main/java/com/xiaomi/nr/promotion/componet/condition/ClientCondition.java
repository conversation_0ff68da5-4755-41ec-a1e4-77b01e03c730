package com.xiaomi.nr.promotion.componet.condition;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.enums.OnOffLineEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MultiPromotionConfig;

import lombok.extern.slf4j.Slf4j;

/**
 * Client信息检查条件
 *
 * <AUTHOR>
 * @date 2021/6/1
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class ClientCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 能参与的客户端列表
     */
    private List<String> selectClients;
    /**
     * 是否线下可用 1仅线上使用，2仅线下使用 3均可使用
     */
    private OnOffLineEnum offline;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) {
        // 线下门店不用校验clientId
        if (StringUtils.isNotEmpty(request.getOrgCode())) {
            return true;
        }
        if (request.getChannel() != null) {
            return true;
        }

        Long uid = request.getUserId();
        Long clientId = request.getClientId();
        // 纯线下活动不应该走到这个逻辑
        if (offline == OnOffLineEnum.OFFLINE) {
            log.error("condition is not satisfied. act is offline. actId:{}, uid:{}, clientId:{} ", promotionId, uid, clientId);
            return false;
        }
        if (CollectionUtils.isNotEmpty(selectClients) && !selectClients.contains(String.valueOf(clientId))) {
            log.debug("condition is not satisfied. client is not support. actId:{}, uid:{}, selectClients:{}, clientId:{}", promotionId, uid, selectClients, clientId);
            return false;
        }
        return true;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof MultiPromotionConfig)) {
            log.error("config is not instanceof MultiPromotionConfig. config:{}", config);
            return;
        }
        MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.selectClients = promotionConfig.getSelectClients();
        this.offline = promotionConfig.getOffline();
    }
}
