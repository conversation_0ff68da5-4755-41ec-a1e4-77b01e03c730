package com.xiaomi.nr.promotion.domain.coupon.service.base.group;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutContext;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponGroupInfoDO;
import com.xiaomi.nr.promotion.engine.CouponTool;
import com.xiaomi.nr.promotion.resource.provider.CouponProvider;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;

/**
 * Created by wangweiyi on 2024/3/5
 */
public interface CouponGroupInterface {

    void init(List<CheckoutCoupon> couponList, CouponGroupInfoDO couponGroupInfo);


    void checkoutForSubmit(CouponCheckoutContext couponContext, List<Long> selectedCouponList)
            throws Exception;

    void checkoutForCouponList(CouponCheckoutContext couponContext, List<Long> selectedCouponList)
            throws Exception;

    CouponProvider initResource(CheckoutPromotionRequest request, CouponTool couponTool, CouponCheckoutResult result)
            throws BizError;

}
