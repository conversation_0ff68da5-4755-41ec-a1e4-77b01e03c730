package com.xiaomi.nr.promotion.domain.coupon.service.base.group;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.domain.coupon.model.*;
import com.xiaomi.nr.promotion.engine.CouponTool;
import com.xiaomi.nr.promotion.resource.provider.CouponProvider;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;
import java.util.Map;

/**
 * Created by wangweiyi on 2024/3/5
 */
public interface CouponGroupInterface {

    void init(List<CheckoutCoupon> couponList, CouponGroupInfoDO couponGroupInfo);

    void checkoutForSelectCoupons(CouponCheckoutContext couponContext, List<Long> selectedCouponList) throws Exception;

    CouponListCheckoutResult doCheckout(Map<Long, CouponTool> couponToolMap, CouponCheckoutContext couponCheckoutContext);


    CouponProvider initResource(CheckoutPromotionRequest request, CouponTool couponTool, CouponCheckoutResult result)
            throws BizError;

}
