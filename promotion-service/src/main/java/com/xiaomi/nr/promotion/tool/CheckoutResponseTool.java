package com.xiaomi.nr.promotion.tool;

import com.google.common.collect.ImmutableMap;
import com.xiaomi.nr.phoenix.api.dto.response.tradeInfo.TradeQualificationInfo;
import com.xiaomi.nr.promotion.api.dto.CartPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Response;
import com.xiaomi.nr.promotion.api.dto.enums.PromotionType;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.common.Statistics;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.ExpressHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.xiaomi.nr.promotion.util.CartHelper.checkAndStatistics;

/**
 * 结算处理响应工具
 *
 * <AUTHOR>
 * @date 2021/6/9
 */
@Slf4j
@Component
public class CheckoutResponseTool {

    private static final ImmutableMap<String, PromotionType> KEY_TO_ACT_ENUM_MAP = new ImmutableMap.Builder<String, PromotionType>()
            .put(PromotionConstant.SHARE_ACTIVITY_DISCOUNT_KEY, PromotionType.DISCOUNT)
            .put(PromotionConstant.SHARE_ACTIVITY_BUYREDUCE_KEY, PromotionType.BUY_REDUCE)
            .put(PromotionConstant.SHARE_ACTIVITY_REDUCE_KEY, PromotionType.REDUCE)
            .put(PromotionConstant.SHARE_COUPON_REDUCE_KEY, PromotionType.COUPON)
            .put(PromotionConstant.SHARE_COUPON_DISCOUNT_KEY, PromotionType.COUPON)
            .put(PromotionConstant.SHARE_COUPON_DEDUCT_KEY, PromotionType.COUPON)
            .build();



    /**
     * 转化上下文为结算响应对象<br>
     * 封装checkout 部分
     * <p>
     * 1. 购物车优惠信息汇总
     * 2. 生成价格计算汇总实体
     * 3. 生成优惠信息
     * 4. 包装响应结果， 返回 <br>
     * 封装checkout-submit 部分
     * <p>
     * 1. 订单列表信息
     * 2. 订单信息
     *
     * @param context  结算上下文
     * @param request  请求对象
     * @param response 响应对象
     * @throws BizError 业务异常
     */
    public void convertCheckoutContextToResponse(CheckoutContext context, CheckoutPromotionRequest request, CheckoutPromotionResponse response) throws BizError {
        // 处理结算部分
        transformCheckoutResponse(request, response, context);
        if (request.getSourceApi() == SourceApi.CHECKOUT) {
            return;
        }
        // 订单信息以及订单维度的item数据
        Long userId = request.getUserId();
        Long orderId = request.getOrderId();
        transformCheckoutOrderResponse(userId, orderId, response, context, request.getUseRedPacket());
    }

    public void convertCheckoutContextToResponseV2(CheckoutContext context, CheckoutPromotionRequest request, CheckoutPromotionV2Response response) throws BizError {
        // 处理结算部分
        CheckoutPromotionResponse response1 = new CheckoutPromotionResponse();
        transformCheckoutResponse(request, response1, context);
        // 构造结算和劵列表的结果
        formatCheckoutV2Response(response1, response, context);
    }

    public void convertCheckoutContextToCartResponse(CheckoutContext context, CheckoutPromotionRequest request, CartPromotionResponse response)
            throws BizError {
        // 处理结算部分
        CheckoutPromotionResponse checkoutResponse = new CheckoutPromotionResponse();
        transformCheckoutResponse(request, checkoutResponse, context);

        //构造结算和劵列表的结果
        formatCartResponse(checkoutResponse, response, context);

        //构造购物车字段
        SummaryPriceInfo cartSummaryInfo = buildCartSummaryInfo(request, response.getSummation());
        response.setSummaryPriceInfo(cartSummaryInfo);

        for (CartItem cartItem : request.getCartList()) {
            if (CollectionUtils.isNotEmpty(cartItem.getReduceItemList())) {
                cartItem.setCheckoutPrice(CartHelper.goodsCurPriceByReduceItem(cartItem));
            } else {
                cartItem.setCheckoutPrice(CartHelper.goodsCurPrice(cartItem));
            }
            CartHelper.updateChildCheckoutPrice(cartItem);
        }
        //兼容promotion字段
        initPromotionNull(response.getPromotions());
        initCartItemNull(response.getCartList());

    }

    public void initCartItemNull(List<CartItem> cartItems) {
        for (CartItem cartItem : cartItems) {
           if (cartItem.getChilds() == null) {
               cartItem.setChilds(new ArrayList<>());
           }
        }
    }

    public void initPromotionNull(List<PromotionInfo> promotionInfoList) {
        for (PromotionInfo promotion : promotionInfoList) {
            if (promotion.getPolicyNew() == null) {
                PolicyNew policyNew = new PolicyNew();
                policyNew.setPolicy(new ArrayList<>());
                policyNew.setType(0L);
                promotion.setPolicyNew(new PolicyNew());
            }
            if (promotion.getJoinedItemId() == null) {
                promotion.setJoinedItemId(new ArrayList<>());
            }
        }
    }

    private SummaryPriceInfo buildCartSummaryInfo(CheckoutPromotionRequest request, Summation summation) {

        //log.info("getCartSummaryInfo request:{}, summation:{}", GsonUtil.toJson(request), GsonUtil.toJson(summation));
        SummaryPriceInfo summaryPriceInfo = new SummaryPriceInfo();

        summaryPriceInfo.setReduceAmount(summation.getReduceAmount());
        summaryPriceInfo.setTotalAmount(summation.getTotalAmount());
        Map<Integer, Long> promotionReduceMap = new HashMap<>();

        for (CartItem cartItem : request.getCartList()) {
            if(MapUtils.isEmpty(cartItem.getReduceDetailList())) {
                continue;
            }

            for (Map.Entry<String, List<ReduceDetail>> reduceEntry : cartItem.getReduceDetailList().entrySet()) {
                String key = reduceEntry.getKey();
                //不计算直降和门店价的明细
                //不计算赠品和加价购的明细
                PromotionType promotionType = KEY_TO_ACT_ENUM_MAP.get(key);
                if (promotionType == null) {
                    continue;
                }
                long sum = reduceEntry.getValue().stream()
                        .mapToLong(ReduceDetail::getAmount)
                        .sum();
                long oldSum = promotionReduceMap.getOrDefault(promotionType.getValue(), 0L);
                promotionReduceMap.put(promotionType.getValue(), sum + oldSum);
            }
        }
        summaryPriceInfo.setPromotionReduceMap(promotionReduceMap);
        return summaryPriceInfo;
    }

    private void formatCartResponse(CheckoutPromotionResponse response, CartPromotionResponse cartResponse, CheckoutContext context) {
        cartResponse.setCartList(response.getCartList());
        cartResponse.setPromotions(response.getPromotions());
        if (response.getSummation() == null) {
            cartResponse.setSummation(new Summation());
        } else {
            cartResponse.setSummation(response.getSummation());
        }
        if (response.getEcardList() == null) {
            cartResponse.setEcardList(new ArrayList<>());
        } else {
            cartResponse.setEcardList(response.getEcardList());
        }
        cartResponse.setExInfo(response.getExInfo());
        cartResponse.setCouponList(context.getCouponList());
    }

    private void formatCheckoutV2Response(CheckoutPromotionResponse response, CheckoutPromotionV2Response responseV2, CheckoutContext context) {
        responseV2.setCartList(response.getCartList());
        responseV2.setPromotions(response.getPromotions());
        responseV2.setSummation(response.getSummation());
        responseV2.setEcardList(response.getEcardList());
        responseV2.setExInfo(response.getExInfo());
        responseV2.setCouponList(context.getCouponList());
        responseV2.setHasBeijingcoupon(response.getHasBeijingcoupon());
        return;
    }


    private void transformCheckoutResponse(CheckoutPromotionRequest request, CheckoutPromotionResponse response,
                                           CheckoutContext context) throws BizError {
        List<CartItem> cartList = request.getSourceApi() == SourceApi.SUBMIT ? context.getCarts() : request.getCartList();
        //购物车在最后一个活动分摊时可能删空了，这是可能和正常的
        if (CollectionUtils.isEmpty(cartList)) {
            return;
        }

        Statistics statistics = checkAndStatistics(cartList);
        List<Express> expressList = context.getExpress();
        Long hasRedpacket = context.getHasRedpacket();
        // 价格计算信息 校验和获取统计信息
        Summation summation = transformSummation(statistics, expressList, hasRedpacket);

        // 优惠信息
        List<PromotionInfo> promotionInfoList = context.getPromotion();
        List<PromotionInfo> resultPromotionTemp = filterPromotionList(promotionInfoList, request.getBargainSize());

        // 拓展信息
        Map<String, String> exInfo = initExInfo(context);
        for (CartItem cartItem : cartList) {
            if (CollectionUtils.isNotEmpty(cartItem.getReduceItemList())) {
                cartItem.setCheckoutPrice(CartHelper.goodsCurPriceByReduceItem(cartItem));
            } else {
                cartItem.setCheckoutPrice(CartHelper.goodsCurPrice(cartItem));
            }
            CartHelper.updateChildCheckoutPrice(cartItem);
        }
        response.setCartList(cartList);
        response.setSummation(summation);
        response.setPromotions(resultPromotionTemp);
        response.setEcardList(context.getECardInfoList());
        response.setExInfo(exInfo);
        response.setHasBeijingcoupon(context.isHasFulFillBeijingCoupon());
    }

    private void transformCheckoutOrderResponse(long userId, long orderId, CheckoutPromotionResponse response,
                                                CheckoutContext context, Boolean useRedPacket) throws BizError {
        List<OrderItem> orderItems = context.getOrderItems();
        response.setOrderItems(orderItems);
        long goodsAmount = orderItems.stream().mapToLong(OrderItem::getFinalPrice).sum();
        // 礼品卡信息
        OrderExtention extention = new OrderExtention();
        if(CollectionUtils.isNotEmpty(context.getECardInfoList())) {
            extention.setEcardList(context.getECardInfoList());
        }

        // 三方优惠资格码信息
        if (MapUtils.isNotEmpty(context.getCartItemQualifyMap())) {
            Map<String, QualifyDetailInfo> qualifyDetailInfoMap = context.getCartItemQualifyMap().entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                        TradeQualificationInfo qualificationInfo = entry.getValue();
                                QualifyDetailInfo qualifyDetailInfo = new QualifyDetailInfo();
                        qualifyDetailInfo.setQualificationCode(qualificationInfo.getQualificationCode());
                                qualifyDetailInfo.setQualificationSeq(qualificationInfo.getQualificationSeq());
                                qualifyDetailInfo.setQualificationType(qualificationInfo.getQualificationType());
                                return qualifyDetailInfo;
                            }
                    ));
            extention.setQualifyDetailInfoMap(qualifyDetailInfoMap);
        }

        response.setOrderExtention(extention);

        List<Express> express = Optional.ofNullable(context.getExpress()).orElse(Collections.emptyList());
        Map<String, ExpressDetail> expressDetailMap = express.stream()
                .filter(ExpressHelper::valid)
                .collect(Collectors.toMap(Express::getIdKey, Express::getDetail, (val1, val2) -> val1));

        // 订单对象信息
        Order order = new Order();
        order.setUserId(userId);
        order.setOrderId(orderId);
        order.setGoodsAmount(goodsAmount);
        order.setReduceExpressDetail(expressDetailMap);
        response.setOrder(order);

        // 红包信息
        Long hasRedPacket = useRedPacket? context.getHasRedpacket() : 0L;
        response.setHasRedpacket(hasRedPacket);
    }

    private Map<String, String> initExInfo(CheckoutContext checkoutContext) {
        Map<String, String> exInfo = new HashMap<>();
        exInfo.put(PromotionConstant.COUPON_BASE_INFO, checkoutContext.getCouponBaseInfo());
        exInfo.put(PromotionConstant.COUPON_BASE_INFO_V2, checkoutContext.getCouponBaseInfoList());
        exInfo.put(PromotionConstant.COUPON_BASE_INFO_INVALID, checkoutContext.getCouponBaseInfoInvalid());
        exInfo.put(PromotionConstant.COUPON_USE_REGION, checkoutContext.getCouponUseRegion());
        exInfo.put(PromotionConstant.ECARD_BASE_INFO, checkoutContext.getECardBaseInfo());
        exInfo.put(PromotionConstant.ECARD_BASE_INFO_INVALID, checkoutContext.getEcardBaseInfoInvalid());
        exInfo.put(PromotionConstant.ECARD_ITEM_LIST, checkoutContext.getCardFullGoods());
        exInfo.put(PromotionConstant.REDPACKET_BASE_INFO_INVALID, checkoutContext.getRedPacketBaseInfoInvalid());
        exInfo.put(PromotionConstant.PHOENIX_BASE_INFO_INVALID, checkoutContext.getPhoenixBaseInfoInvalid());
        exInfo.put(PromotionConstant.PURCHASE_SUBSIDY_INVALID, checkoutContext.getPurchaseSubsidyInvalid());
        return exInfo;
    }

    private Summation transformSummation(Statistics statistics, List<Express> expressList, Long hasRedpacket) {
        // 统计信息, 红包，券，礼品卡总优惠金额
        Map<String, Long> reduceList = new HashMap<>();
        reduceList.put(PromotionConstant.CARTLIST_ACTIVITY_PREFIX, statistics.getTotalActReduce());
        reduceList.put(PromotionConstant.CARTLIST_COUPON_PREFIX, statistics.getTotalCouponReduce());
        reduceList.put(PromotionConstant.CARTLIST_REDPACKET_PREFIX, statistics.getTotalRedPacketReduce());
        reduceList.put(PromotionConstant.CARTLIST_PHOENIX_PREFIX, statistics.getTotalPhoenixReduce());
        reduceList.putAll(statistics.getActReduce());

        // 邮费信息
        Map<String, ExpressDetail> reduceExpressDetailMap = expressList.stream()
                .filter(exp -> exp != null && ExpressHelper.valid(exp))
                .collect(Collectors.toMap(Express::getIdKey, Express::getDetail, (v1, v2) -> v2));

        // 最终价格
        final long finalAmount = statistics.getTotalPrice() - statistics.getTotalReduce();

        Summation summation = new Summation();
        summation.setReduceList(reduceList);
        summation.setTotalAmount(statistics.getTotalPrice());
        summation.setTotalCount(statistics.getTotalCount());
        summation.setReduceAmount(statistics.getTotalReduce());
        summation.setFinalAmount(finalAmount);
        summation.setReduceExpressDetail(reduceExpressDetailMap);
        summation.setHasRedPacket(hasRedpacket);
        return summation;
    }

    private static List<PromotionInfo> filterPromotionList(List<PromotionInfo> promotionInfoList, Long bargainSize) {
        if (CollectionUtils.isEmpty(promotionInfoList)) {
            return Collections.emptyList();
        }

        int size = bargainSize.intValue();
        final List<PromotionInfo> resProTemp = new ArrayList<>();
        String bargainType = String.valueOf(ActivityTypeEnum.BARGAIN.getValue());
        for (PromotionInfo promotionInfo : promotionInfoList) {
            int joinedItemSize = CollectionUtils.isNotEmpty(promotionInfo.getJoinedItemId()) ? promotionInfo.getJoinedItemId().size() : 0;
            // 加价购活动数量限制
            if (bargainType.equals(promotionInfo.getType()) && joinedItemSize == 0) {
                if (size < 0) {
                    continue;
                }
                size--;
            }
            // 是否已参与
            if (joinedItemSize > 0) {
                promotionInfo.setJoined(BooleanEnum.YES.getValue());
            }
            resProTemp.add(promotionInfo);
        }
        return resProTemp;
    }

    public OrderItem convertOrderItem(OrderCartItem orderCartItem, Long userId, Long orderId) {
        long finalPrice = CartHelper.itemCurPrice(orderCartItem);
        OrderItem orderItem = new OrderItem();
        orderItem.setItemId(orderCartItem.getItemId());
        orderItem.setSku(orderCartItem.getSku());
        orderItem.setPackageId(orderCartItem.getPackageId());
        orderItem.setSkuCount(orderCartItem.getCount());
        orderItem.setUserId(userId);
        orderItem.setOrderId(orderId);
        orderItem.setStandardPrice(orderCartItem.getStandardPrice());
        orderItem.setCartPrice(orderCartItem.getCartPrice());
        orderItem.setFinalPrice(finalPrice);
        orderItem.setOriItemId(orderCartItem.getOriItemId());
        orderItem.setOrderItemReduceList(orderCartItem.getOrderItemReduceList());
        orderItem.setPreferentialInfos(orderCartItem.getPreferentialInfos());
        orderItem.setUnitId(orderCartItem.getUnitId());
        return orderItem;
    }
}
