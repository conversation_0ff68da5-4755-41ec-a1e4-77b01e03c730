package com.xiaomi.nr.promotion.componet.action;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.entity.redis.GiftBargainGroup;
import com.xiaomi.nr.promotion.entity.redis.Goods;
import com.xiaomi.nr.promotion.entity.redis.SkuGroup;
import com.xiaomi.nr.promotion.enums.ActFrequencyEnum;
import com.xiaomi.nr.promotion.enums.ChannelEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.*;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BargainPromotionConfig;
import com.xiaomi.nr.promotion.tool.ConditionCheckTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 加价购活动: 计价和均摊动作
 * <p>
 * 1.将不满足的加价购删除
 * 2.将超出的加价购count进行调整
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class BargainAction extends AbstractAction {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 加价购商品信息
     */
    private Goods bargainGoods;
    /**
     * 密码
     */
    private String accessCode;
    /**
     * 频次限制 1不限制 2整个活动一次 3每天一次
     */
    private ActFrequencyEnum frequency;
    /**
     * 活动总数
     */
    private long actLimitNum;
    /**
     * 活动限制. 数据值：0-不限制， 没有限制就没有对应字段
     */
    private ActNumLimitRule numLimitRule;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Autowired
    private ConditionCheckTool conditionCheckTool;

    /**
     * 进行加价购活动减价
     * <p>
     * - 获取商品下标， 这边商品是加价购列表
     * 1. 根据购物车加价购从活动缓存中找到对应组信息
     * 2. 如果没找到，说明当前加价购不符合， 标记删除
     * 3. 找到了，将购物车加价购改价
     * 4. 将对应购物车商品（加价购）放入对应有groupId的组 cartGroup
     * - 进行加价购减价
     * - 生成活动结算
     * 1. 按groupId 进行分组， 并进行减价
     * 2.每组数量进行控制在 <= maxCount
     * - 生成结算资源
     *
     * @param promotion 优惠工具
     * @param request   请求参数
     * @param context   请求上下文，活动组件间
     * @throws BizError 业务异常
     */
    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        // 处理和过滤sku组信息
        List<SkuGroup> skuGroupList = handleSkuGroupList(promotionId, bargainGoods.getSkuGroupList());
        if (CollectionUtils.isEmpty(skuGroupList)) {
            log.warn("all bargain numLimit is zero. actId:{}", promotionId);
            CartHelper.delGiftBargain(request.getCartList(), promotionId);
            return;
        }
        if (ChannelEnum.MI_SHOP.getId().equals(request.getChannel())) {
            skuGroupList = skuGroupList.stream().map(item -> sortGoods(item)).collect(Collectors.toList());
        }
        // 购物车中来源当前活动的加价购
        List<CartItem> cartList = request.getCartList();
        List<GoodsIndexNew> indexNewList = CartHelper.getCartsIndex(cartList, SourceEnum.SOURCE_BARGAIN.getSource(), String.valueOf(promotionId));

        // 获取加价购商品规则， 对加价购商进行分组, 过程中会改价. key：groupId, val: cartList
        Map<Long, List<CartItem>> cartGroup = doMatchRule(cartList, indexNewList, request.getOrgCode(), skuGroupList);

        // 调整加价购数量
        adjustGroupLimitCount(cartGroup);

        // 对每组加价购进行检查， 如果超出最大，需要递减到合适值. key: groupId, val: count
        Integer maxCount = context.getFillTimes();
        Map<Long, Integer> countMap = adjustGroupCount(cartGroup, maxCount);
        log.debug("bargain act adjusted.actId:{} uid:{} fillTimes:{} countMap:{}", promotionId, request.getUserId(), context.getFillTimes(), countMap);

        // 删除购物车 CART_DEL_FLAG
        delInvalidCarts(cartList);

        // 设置活动计算结果
        setResult(context, cartList, promotion, indexNewList, cartGroup, countMap, skuGroupList);

        // 初始化资源
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            int joinCount = countMap.values().stream().mapToInt(Integer::intValue).max().orElse(0);
            initBargainResource(request, promotionId, joinCount, context, cartGroup, actLimitNum, frequency, numLimitRule);
        }
    }

    /**
     * 对从品组中的从品列表进行排序
     * @param skuGroup 从品组
     * @return skuGroup 从品组
     */
    private SkuGroup sortGoods(SkuGroup skuGroup) {
        if (Objects.isNull(skuGroup) || CollectionUtils.isEmpty(skuGroup.getListInfo())) {
            return skuGroup;
        }
        skuGroup.getListInfo().sort((o1, o2) -> {
            long reduceMoney1 = o1.getMarketPrice() - o1.getCartPrice();
            long reduceMoney2 = o2.getMarketPrice() - o2.getCartPrice();
            if (reduceMoney1 == reduceMoney2) {
                return (int) (o1.getMarketPrice() - o2.getMarketPrice());
            } else {
                return (int) (reduceMoney2 - reduceMoney1);
            }
        });
        return skuGroup;
    }

    private void adjustGroupLimitCount(Map<Long, List<CartItem>> cartGroup) {
        List<SkuGroup> skuGroupList = bargainGoods.getSkuGroupList();
        cartGroup.forEach((groupId, itemList) -> {
            if (CollectionUtils.isEmpty(itemList)) {
                return;
            }
            SkuGroup skuGroup = skuGroupList.stream().filter(group -> Objects.equals(groupId, group.getGroupId())).findAny().orElse(null);
            if (skuGroup == null) {
                return;
            }
            itemList.forEach(item -> adjustCartItemLimit(item, skuGroup, groupId));
        });
    }

    public void adjustCartItemLimit(CartItem item, SkuGroup skuGroup, Long groupId) {
        String sku = item.getSku();
        GiftBargainGroup bargainInfo = skuGroup.getListInfo().stream().filter(info -> Objects.equals(sku, String.valueOf(info.getSku()))).findAny().orElse(null);
        if (bargainInfo == null) {
            item.setItemId(CART_DEL_FLAG);
            return;
        }
        Long bargainLimitNum = bargainInfo.getBargainLimitNum();
        if (bargainLimitNum == null) {
            return;
        }
        Integer limitNum = activityRedisDao.getActBargainLimitNum(promotionId, sku, groupId);
        long leftNum = bargainLimitNum - limitNum;
        if (leftNum < 0L) {
            item.setItemId(CART_DEL_FLAG);
            return;
        }
        if (leftNum < item.getCount()) {
            item.setCount((int) leftNum);
        }
    }

    private Map<Long, List<CartItem>> doMatchRule(List<CartItem> cartList, List<GoodsIndexNew> goodsInd, String orgCode,
                                                  List<SkuGroup> skuGroupList) throws BizError {
        //按组获取符合条件的购物车条目
        Map<Long, List<CartItem>> cartGroupMap = new HashMap<>();
        for (GoodsIndexNew actItem : goodsInd) {
            CartItem cartItem = CartHelper.getCartItem(cartList, actItem);
            if (cartItem == null || !CartHelper.filterOk(cartItem)) {
                log.error("the info's cart now is invalid! bargain actId: {}, actItem: {}, cartItem: {}", promotionId, actItem, cartItem);
                throw ExceptionHelper.create(ErrCode.ERR_CART, "购物车错误，购物车条目为空或者不合法");
            }
            doMatchActItem(actItem, cartItem, cartGroupMap, orgCode, skuGroupList);
        }
        return cartGroupMap;
    }

    private void doMatchActItem(GoodsIndexNew actItem, CartItem cartItem, Map<Long, List<CartItem>> cartGroupMap,
                                String orgCode, List<SkuGroup> skuGroupList) {
        //处理不符合活动条件的购物车条目 skuGroup==nil代表不符合活动参加条件
        SkuGroup skuGroup = getJoinBargainActGroup(cartItem, orgCode, skuGroupList);
        if (skuGroup == null) {
            log.error("checkout cartItem is invalid and will be del! bargain cartItem:{}", cartItem);
            actItem.setItemId(CART_DEL_FLAG);
            cartItem.setItemId(CART_DEL_FLAG);
            return;
        }
        // 购物车改价
        GiftBargainGroup info = skuGroup.getListInfo().get(0);
        CartHelper.changePrice(cartItem, info.getCartPrice());

        // 按活动组id分类购物车
        cartGroupMap.computeIfAbsent(skuGroup.getGroupId(), k -> new ArrayList<>());
        cartGroupMap.get(skuGroup.getGroupId()).add(cartItem);
    }

    private SkuGroup getJoinBargainActGroup(CartItem cartItem, String orgCode, List<SkuGroup> skuGroupList) {
        // 遍历加价购组，进行查找， 查看加价购组下加价购是否有和当前sku一样的
        SkuGroup skuGroup = skuGroupList.stream().map(groups -> getMatchSkuGroup(groups, cartItem, orgCode))
                .filter(Objects::nonNull).findAny().orElse(null);
        if (skuGroup == null) {
            log.error("the bargain cartItem item_id:{}, sku:{} and packageID:{} and groupID:{} not in gift {} rule list {}",
                    cartItem.getItemId(), cartItem.getSku(), cartItem.getPackageId(), cartItem.getGroupId(), promotionId, skuGroupList);
        }
        return skuGroup;
    }

    private void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool, List<GoodsIndexNew> goodsIndNew,
                           Map<Long, List<CartItem>> cartGroup, Map<Long, Integer> countMap, List<SkuGroup> skuGroupsList) throws BizError {
        // 总赠品数 和 每组之间参加活动的最大值
        int validCountAll = countMap.values().stream().mapToInt(Integer::intValue).sum();
        List<GroupCurCount> groupCurCount = buildIncrCountMap(cartGroup, countMap);
        // 生成活动拓展信息
        Integer maxCount = context.getFillTimes();
        String activityInfo = generateExtendInfo(maxCount, validCountAll, groupCurCount, accessCode, skuGroupsList);
        // 符合条件主商品 和 符合条件赠品
        List<String> parentItemIds = CartHelper.getParentItemIdList(context.getGoodIndex());
        List<String> joinedItemIdList = CartHelper.getJoinedItemIdListNew(goodsIndNew, cartList);
        // 活动优惠信息
        int joinCount = countMap.values().stream().mapToInt(Integer::intValue).max().orElse(0);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setJoinedItemId(joinedItemIdList);
        promotionInfo.setParentItemId(parentItemIds);
        promotionInfo.setExtend(activityInfo);
        promotionInfo.setJoinCounts(joinCount);
        promotionInfo.setJoined(CollectionUtils.isNotEmpty(joinedItemIdList) ? 1 : 0);

        // 设置上下文结果
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    private String generateExtendInfo(Integer maxCount, Integer curCount, List<GroupCurCount> countList, String accessCode, List<SkuGroup> skuGroupsList) {
        List<GiftBargainGroup> listInfo = skuGroupsList.get(0).getListInfo();
        if (CollectionUtils.isEmpty(listInfo)) {
            return null;
        }
        // 获取skulist和list,兼容旧逻辑
        Map<String, String> skuMap = listInfo.stream().map(GiftBargainGroup::getSku).map(String::valueOf)
                .collect(Collectors.toMap(sku -> sku, sku -> sku, (val1, val2) -> val2, LinkedHashMap::new));
        List<String> skuList = new ArrayList<>(skuMap.keySet());

        List<SkuGroupBean> skuGroupBeanList = skuGroupsList.stream()
                .map(SkuGroupConverter::convert).filter(Objects::nonNull).collect(Collectors.toList());

        // 返回活动信息,对应promotion结构里的extend字段
        PromotionExtend expandInfo = new PromotionExtend();
        expandInfo.setCartPrice(listInfo.get(0).getCartPrice());
        expandInfo.setMaxCount(maxCount);
        expandInfo.setCurCount(curCount);
        expandInfo.setGroupCurCount(countList);
        expandInfo.setList(skuMap);
        expandInfo.setSkuList(skuList);
        expandInfo.setForceParent(bargainGoods.getForceParent());
        expandInfo.setSelectType(bargainGoods.getSelectType());
        expandInfo.setIgnoreStock(bargainGoods.getIgnoreStock());
        expandInfo.setRefundValue(listInfo.get(0).getRefundValue());
        expandInfo.setAccessCode(accessCode);
        expandInfo.setSkuGroupsList(skuGroupBeanList);
        expandInfo.setOnsaleExtend(new OnsaleExtendInfo());
        return GsonUtil.toJson(expandInfo);
    }

    /**
     * 处理SkuGroup列表
     * <p>
     * 过滤有限制但是无次数组
     *
     * @param promotionId  活动ID
     * @param skuGroupList 从品组信息
     * @return SkuGroup列表
     */
    protected List<SkuGroup> handleSkuGroupList(Long promotionId, List<SkuGroup> skuGroupList) {
        if (CollectionUtils.isEmpty(skuGroupList)) {
            return Collections.emptyList();
        }

        // 处理从品组信息
        return skuGroupList.stream().map(skuGroup -> handleSlaveGroup(promotionId, skuGroup)).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 处理从品组
     *
     * @param promotionId 活动ID
     * @param skuGroup    组数据
     * @return 组
     */
    private SkuGroup handleSlaveGroup(Long promotionId, SkuGroup skuGroup) {
        Long groupId = skuGroup.getGroupId();

        // 检查策略
        Predicate<GiftBargainGroup> checkStrategy = info -> checkBargainNumLimit(promotionId, groupId, info);
        // 开始检查，如果遇到不满足的直接过滤
        List<GiftBargainGroup> listInfo = Optional.ofNullable(skuGroup.getListInfo()).orElse(Collections.emptyList()).stream()
                .filter(checkStrategy).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(listInfo)) {
            return null;
        }
        SkuGroup skuGroupNew = new SkuGroup();
        skuGroupNew.setGroupId(groupId);
        skuGroupNew.setListInfo(listInfo);
        return skuGroupNew;
    }

    /**
     * 检查加价购从品数量
     *
     * @param promotionId 活动ID
     * @param groupId     组ID
     * @param info        从品信息
     * @return 是否符合
     */
    private boolean checkBargainNumLimit(Long promotionId, Long groupId, GiftBargainGroup info) {
        Long limitNum = info.getBargainLimitNum();
        // 表示没有限制
        if (limitNum == null || limitNum == 0L) {
            return true;
        }
        return conditionCheckTool.checkActBargainLimit(limitNum, promotionId, groupId, info.getSku());
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof BargainPromotionConfig)) {
            return;
        }
        BargainPromotionConfig promotionConfig = (BargainPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.bargainGoods = promotionConfig.getBargainGoods();
        this.accessCode = promotionConfig.getAccessCode();
        this.frequency = promotionConfig.getFrequency();
        this.actLimitNum = promotionConfig.getActLimitNum();
        this.numLimitRule = promotionConfig.getNumLimitRule();
    }
}



