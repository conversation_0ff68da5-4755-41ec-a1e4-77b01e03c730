package com.xiaomi.nr.promotion.mq.consumer.entity;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/9
 */
@Data
public class OrderData {

    /**
     * p_order_id
     */
    @SerializedName("p_order_id")
    private String pOrderId;

    /**
     * org_order_id
     */
    @SerializedName("org_order_id")
    private String orgOrderId;

    /**
     * top_order_id
     */
    @SerializedName("top_order_id")
    private String topOrderId;

    /**
     * order_flow
     */
    @SerializedName("order_flow")
    private Integer orderFlow;

    /**
     * order_type
     */
    @SerializedName("order_type")
    private Integer orderType;

    /**
     * integral_amount
     */
    @SerializedName("integral_amount")
    private BigDecimal integralAmount;

    /**
     * order_tags
     */
    @SerializedName("order_tags")
    private List<String> orderTags;
}
