package com.xiaomi.nr.promotion.model.promotionconfig.loader;

import com.google.common.collect.Sets;
import com.xiaomi.nr.promotion.entity.redis.*;
import com.xiaomi.nr.promotion.model.common.GoodsConverter;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.GoodsReduceInfo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 下单立减配置数据加载
 *
 * <AUTHOR>
 * @date 2022/07/04
 */
@Slf4j
@Component
public class BuyReducePromotionConfigLoader implements PromotionConfigLoader {

    @Override
    public boolean support(AbstractPromotionConfig promotionConfig) {
        if (promotionConfig instanceof BuyReducePromotionConfig) {
            return true;
        }
        return false;
    }

    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityInfo activityInfo) throws BizError {
        if (activityInfo == null || promotionConfig == null) {
            log.error("buyReduce activityInfo is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "activityInfo is null");
        }
        TypeBase typeBase = activityInfo.getBasetype();
        Condition condition = activityInfo.getCondition();
        Policy policy = activityInfo.getPolicy();
        if (typeBase == null || condition == null || policy == null) {
            log.error("buyReduce baseType or condition or policy is null. actInfo:{}", activityInfo);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "baseType or condition or policy is null");
        }
        Long id = typeBase.getId();
        BuyReducePromotionConfig reducePromotionConfig = (BuyReducePromotionConfig) promotionConfig;
        List<RenewReduceInfo> buyReduceList = policy.getBuyReduce();
        if (CollectionUtils.isEmpty(buyReduceList)) {
            log.error("buyReduce policy is empty. actId:{} policy:{} ", id, policy);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "policy is empty");
        }

        // 下单立减信息
        Map<String, GoodsReduceInfo> reduceInfoMap = buyReduceList.stream()
                .collect(Collectors.toMap(info -> String.valueOf(info.getSkuPackage()), GoodsConverter::convertReduceInfo, (va1, va2) -> va1));
        reducePromotionConfig.setBuyReduceInfoMap(reduceInfoMap);

        // 主品信息列表
        Set<String> skuPackageSet = Sets.newHashSet();
        buyReduceList.forEach(reduceInfo ->
                skuPackageSet.add(String.valueOf(reduceInfo.getSkuPackage())));
        reducePromotionConfig.setIncludeSkuPackages(skuPackageSet);
    }
}
