package com.xiaomi.nr.promotion.model.promotionconfig.loader;

import com.google.common.collect.Sets;
import com.xiaomi.nr.md.promotion.admin.api.constant.ProductIdTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ProductPolicy;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.BuyReduceRule;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.specification.BrBudgetSpecification;
import com.xiaomi.nr.promotion.entity.redis.ActivityInfo;
import com.xiaomi.nr.promotion.entity.redis.Condition;
import com.xiaomi.nr.promotion.entity.redis.Policy;
import com.xiaomi.nr.promotion.entity.redis.RenewReduceInfo;
import com.xiaomi.nr.promotion.entity.redis.TypeBase;
import com.xiaomi.nr.promotion.model.common.GoodsConverter;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.GoodsReduceInfo;
import com.xiaomi.nr.promotion.util.CarShopBuyReduceUtil;
import com.xiaomi.nr.promotion.util.ChannelUtil;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 下单立减配置数据加载
 *
 * <AUTHOR>
 * @date 2022/07/04
 */
@Slf4j
@Component
public class BuyReducePromotionConfigLoader implements PromotionConfigLoader {

    @Override
    public boolean support(AbstractPromotionConfig promotionConfig) {
        if (promotionConfig instanceof BuyReducePromotionConfig) {
            return true;
        }
        return false;
    }

    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityInfo activityInfo) throws BizError {
        if (activityInfo == null || promotionConfig == null) {
            log.error("buyReduce activityInfo is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "activityInfo is null");
        }
        TypeBase typeBase = activityInfo.getBasetype();
        Condition condition = activityInfo.getCondition();
        Policy policy = activityInfo.getPolicy();
        if (typeBase == null || condition == null || policy == null) {
            log.error("buyReduce baseType or condition or policy is null. actInfo:{}", activityInfo);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "baseType or condition or policy is null");
        }
        Long id = typeBase.getId();
        BuyReducePromotionConfig reducePromotionConfig = (BuyReducePromotionConfig) promotionConfig;
        List<RenewReduceInfo> buyReduceList = policy.getBuyReduce();
        if (CollectionUtils.isEmpty(buyReduceList)) {
            log.error("buyReduce policy is empty. actId:{} policy:{} ", id, policy);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "policy is empty");
        }

        // 下单立减信息
        Map<String, GoodsReduceInfo> reduceInfoMap = buyReduceList.stream()
                .collect(Collectors.toMap(info -> String.valueOf(info.getSkuPackage()), GoodsConverter::convertReduceInfo, (va1, va2) -> va1));
        reducePromotionConfig.setBuyReduceInfoMap(reduceInfoMap);

        // 主品信息列表
        Set<String> skuPackageSet = Sets.newHashSet();
        buyReduceList.forEach(reduceInfo ->
                skuPackageSet.add(String.valueOf(reduceInfo.getSkuPackage())));
        reducePromotionConfig.setIncludeSkuPackages(skuPackageSet);
    }

    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityConfig activityConfig) throws BizError {
        if (activityConfig == null || promotionConfig == null) {
            log.error("buyReduce activityInfo is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "activityInfo is null");
        }
        BuyReducePromotionConfig config = (BuyReducePromotionConfig) promotionConfig;
        BuyReduceRule buyReduceRule = GsonUtil.fromJson(activityConfig.getRule(), BuyReduceRule.class);
        BrBudgetSpecification brBudgetSpecification =
                Optional.ofNullable(buyReduceRule).map(BuyReduceRule::getBudgetSpecification).orElse(null);
        config.setLimitType(Optional.ofNullable(buyReduceRule).map(BuyReduceRule::getUserLimitNum).orElse(0));
        config.setLimitCount(Optional.ofNullable(buyReduceRule).map(BuyReduceRule::getLimitCount).orElse(0));
        Map<String, GoodsReduceInfo> goodsReduceInfoMap = new HashMap<>();
        for (ProductPolicy productPolicy : activityConfig.getProductPolicyList()) {
            if (productPolicy.getProductIdType() != ProductIdTypeEnum.SSU.code) {
                continue;
            }
            BuyReduceRule.ProductRule productRule =
                    GsonUtil.fromJson(productPolicy.getRule(), BuyReduceRule.ProductRule.class);
            GoodsReduceInfo goodsReduceInfo = new GoodsReduceInfo();
            goodsReduceInfo.setLevel(String.valueOf(productPolicy.getProductLevel()));
            goodsReduceInfo.setSsuId(productPolicy.getProductId());
            Long reducePrice = Optional.ofNullable(productRule)
                    .map(BuyReduceRule.ProductRule::getReduce)
                    .orElse(0L);
            goodsReduceInfo.setReducePrice(reducePrice);
            goodsReduceInfo.setReduceAmount(reducePrice);
            goodsReduceInfo.setLimitNum(productPolicy.getStock());
            if (Objects.nonNull(brBudgetSpecification)) {
                goodsReduceInfo.setBudgetApplyNo(brBudgetSpecification.getBudgetApplyNo());
                goodsReduceInfo.setLineNum(brBudgetSpecification.getLineNum());
            }
            goodsReduceInfoMap.put(String.valueOf(productPolicy.getProductId()), goodsReduceInfo);
        }
        config.setBuyReduceInfoMap(goodsReduceInfoMap);
        config.setActivityCarTag(CarShopBuyReduceUtil.needCarOwnerInfo(activityConfig));
        config.setCarShop(ChannelUtil.isCarShop(activityConfig.getChannels()));
    }


}
