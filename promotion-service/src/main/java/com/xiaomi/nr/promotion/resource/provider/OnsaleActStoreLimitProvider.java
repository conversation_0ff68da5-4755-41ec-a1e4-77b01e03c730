package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 直降每个门店参与活动次数
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OnsaleActStoreLimitProvider implements ResourceProvider<OnsaleActStoreLimitProvider.ActStoreLimit> {
    /**
     * 线上用户参与活动记录
     */
    private ResourceObject<ActStoreLimit> resourceObject;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Autowired
    private NacosConfig nacosConfig;

    @Override
    public ResourceObject<ActStoreLimit> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<ActStoreLimit> object) {
        this.resourceObject = object;
    }

    /**
     * 扣减库存
     */
    @Override
    public void lock() throws BizError {
        log.info("lock onsale act store limit resource. {}", resourceObject);
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("OnsaleActStoreLimitProvider.lock(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        activityRedisDao.incrOnsaleStoreLimitNum(resourceObject.getContent().getActId(),
                resourceObject.getContent().getOrgCode(),
                resourceObject.getContent().getSkuPackage(),
                resourceObject.getContent().getCount(),
                resourceObject.getContent().getLimitNum());
        log.info("lock onsale act store limit resource ok. {}", resourceObject);
    }

    @Override
    public void consume() {
        log.info("consume onsale act store  resource. {}", resourceObject);
    }

    @Override
    public void rollback() throws BizError {
        log.info("rollback onsale act store limit resource. {}", resourceObject);
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("OnsaleActStoreLimitProvider.rollback(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        activityRedisDao.decrOnsaleStoreLimitNum(resourceObject.getContent().getActId(),
                resourceObject.getContent().getOrgCode(),
                resourceObject.getContent().getSkuPackage(),
                resourceObject.getContent().getCount());
        log.info("rollback onsale act store limit resource ok. {}", resourceObject);
    }

    @Override
    public String conflictText() {
        return "减限购失败";
    }

    /**
     * 活动记录
     */
    @Data
    public static class ActStoreLimit {
        /**
         * 门店Code
         */
        private String orgCode;
        /**
         * 活动ID
         */
        private Long actId;
        /**
         * sku/package
         */
        private String skuPackage;
        /**
         * 扣减数量
         */
        private Integer count;
        /**
         * 过期时间 (秒）
         */
        private Long limitNum;
    }
}
