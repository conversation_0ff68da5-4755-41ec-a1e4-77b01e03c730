package com.xiaomi.nr.promotion.mq.producer.entity;

import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @author: zhangliwei6
 * @date: 2025/1/7 14:34
 * @description: 用户、整车参与活动记录消息
 */
@Data
@Builder
public class CarActivityUseMessage implements Serializable {

    @Serial
    private static final long serialVersionUID = 7306056324467368338L;

    private Header header;

    private Body body;

    @Data
    @Builder
    public static class Body implements Serializable {

        @Serial
        private static final long serialVersionUID = -5413388690465030968L;

        /**
         * 唯一键
         */
        private String unikey;

        /**
         * 活动id
         */
        private Long promotionId;

        private Long userId;

        private String vid;

        /**
         * 业务领域：0-3C零售; 3-汽车整车销售; 4-汽车售后服务 5-车商城
         */
        private Integer bizPlatform;

        /**
         * 订单号
         */
        private Long orderId;

        /**
         * 使用次数
         */
        private Integer joinTimes;

        /**
         * 使用时间
         */
        private Long useTime;

        /**
         * 描述信息
         */
        private String desc;

        /**
         * 变更时间
         */
        private Long changeTimeMillis;

        /**
         * 交易类型 false 正向核销  true 逆向退还
         */
        private Boolean orderRefund;
    }
}
