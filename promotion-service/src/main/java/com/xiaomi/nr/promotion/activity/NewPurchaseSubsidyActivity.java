package com.xiaomi.nr.promotion.activity;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.ProductActOnsaleGoods;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.componet.action.NewPurchaseSubsidyAction;
import com.xiaomi.nr.promotion.componet.condition.NewPurchaseSubsidyCondition;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.ActRespConverter;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.NewPurchaseSubsidyPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class NewPurchaseSubsidyActivity extends AbstractActivityTool implements ActivityTool {

    /**
     * sku-品类
     */
    private Map<String, String> goodsSpuGroupMap;

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof NewPurchaseSubsidyPromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        this.goodsSpuGroupMap=((NewPurchaseSubsidyPromotionConfig) config).getGoodsSpuGroupMap();
        return true;
    }

    @Override
    public ActivityDetail getActivityDetail() {
        ActivityDetail activityDetail = getBasicActivityDetail();
        activityDetail.setDescRule(descRule);
        return activityDetail;
    }

    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) throws BizError {
        return null;
    }

    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        List<String> joinedSkuPackageList = Lists.newArrayList(includeSkuPackages);
        joinedSkuPackageList.retainAll(skuPackageList);
        if (CollectionUtils.isEmpty(joinedSkuPackageList)) {
            return Collections.emptyMap();
        }
        Map<String, ProductActInfo> actMap = Maps.newHashMap();
        for (String skuPackage : joinedSkuPackageList) {
            if (!goodsSpuGroupMap.containsKey(skuPackage)){
                continue;
            }
            ProductActInfo productActInfo = new ProductActInfo();
            productActInfo.setType(type.getValue());
            productActInfo.setId(id);
            actMap.put(skuPackage, productActInfo);
        }
        return actMap;
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.NEW_PURCHASE_SUBSIDY;
    }

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine.condition(NewPurchaseSubsidyCondition.class)
                .action(NewPurchaseSubsidyAction.class);
    }
}
