package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.entity.redis.OrgInfo;
import com.xiaomi.nr.promotion.enums.OnOffLineEnum;
import com.xiaomi.nr.promotion.enums.OrgScopeEnum;
import com.xiaomi.nr.promotion.enums.OrgTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MultiPromotionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 门店信息检查
 * <p>
 * 1. 线上渠道不需要检查，直接返回true
 * 2. 仅线上参加活动不需要检查，直接返回true
 * 3. 门店信息获取失败，返回false
 * 4. 活动门店信息检查 （检查主要包含，类型 和 selectOrgCodes 是否包含）
 * - 授权店走授权店类型检查，
 * - 非授权店走非授权店类型检查
 *
 * <AUTHOR>
 * @date 2021/6/1
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class OrgCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 是否线下可用 1仅线上使用，2仅线下使用 3均可使用
     */
    private OnOffLineEnum offline;
    /**
     * 门店范围
     */
    private OrgScopeEnum orgScope;
    /**
     * 指定门店或区域ID
     */
    private List<String> selectOrgCodes;

    /**
     * 活动版本，0-b.d创建，1-promotion-admin创建
     */
    private Integer version;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) {
        if (StringUtils.isEmpty(request.getOrgCode())) {
            return true;
        }
        // promotion-admin创建的活动不再走这个逻辑
        if (!PromotionConstant.VERSION_BD.equals(version)) {
            return true;
        }
        // 校验线上线下渠道
        String orgCode = request.getOrgCode();
        Long uid = request.getUserId();
        if (offline == OnOffLineEnum.ONLINE) {
            log.debug("act offline is online.  actId:{}, uid:{}, orgCode:{}", promotionId, uid, orgCode);
            return false;
        }
        // 获取门店信息
        OrgInfo orgInfo = context.getOrgInfo();
        if (orgInfo == null) {
            log.error("orgInfo get is null. actId:{}, uid:{},orgCode:{}", promotionId, uid, orgCode);
            return false;
        }
        // 授权店检查
        if (OrgTypeEnum.isAuthorized(orgInfo.getOrgType())) {
            return checkAuthorizeOrgInfo(orgInfo, orgCode, uid);
        }
        // 非授权店检查
        return checkOrgInfo(orgInfo, orgCode, uid);
    }

    /**
     * 检查授权店信息
     *
     * @param orgInfo 门店信息
     * @param orgCode 门店Code
     * @param uid     用户ID
     * @return 是否符合
     */
    private boolean checkAuthorizeOrgInfo(OrgInfo orgInfo, String orgCode, Long uid) {
        switch (orgScope) {
            case ORG_ALL_STORE:
            case ORG_ALL_SPECIALTY_STORE:
            case ORG_SPECIFY_STORE:
            case ORG_SPECIFY_AREA:
                log.debug("checkOrgInfo uid: {} orgCode: {} is authorized store. actId:{}", uid, orgCode, promotionId);
                return false;
            case ORG_ALL_AUTHORIZED_STORE:
                return true;
            case ORG_AUTHORIZED_STORE:
                if (!selectOrgCodes.contains(orgCode)) {
                    log.debug("checkOrgInfo uid:{} orgCode: {} store is not match. actId:{}", uid, orgCode, promotionId);
                    return false;
                }
                return true;
            case ORG_AUTHORIZED_AREA:
                if (!(CollectionUtils.containsAny(selectOrgCodes, orgInfo.getArea()) || selectOrgCodes.contains(orgCode))) {
                    log.debug("checkOrgInfo uid:{} orgCode: {} area is not match. actId:{}", uid, orgCode, promotionId);
                    return false;
                }
                return true;
            default:
                log.error("condition orgScope is invalid. uid:{}, orgCode:{}, actID:{},", uid, orgCode, promotionId);
                return false;
        }
    }

    /**
     * 检查直营/专卖店门店信息
     *
     * @param orgInfo 门店信息
     * @param orgCode 门店Code
     * @param uid     用户ID
     * @return 是否符合
     */
    private boolean checkOrgInfo(OrgInfo orgInfo, String orgCode, Long uid) {
        switch (orgScope) {
            case ORG_ALL_STORE:
                return true;
            case ORG_ALL_DIRECT_STORE:
                if (!OrgTypeEnum.isDirect(orgInfo.getOrgType())) {
                    log.debug("condition is not satisfied. checkOrgInfo uid:{} orgCode: {} type is not match. actId:{}", uid, orgCode, promotionId);
                    return false;
                }
                return true;
            case ORG_ALL_SPECIALTY_STORE:
                if (!OrgTypeEnum.isSpecialty(orgInfo.getOrgType())) {
                    log.debug("condition is not satisfied. checkOrgInfo uid:{} orgCode: {} type is not match. actId:{}", uid, orgCode, promotionId);
                    return false;
                }
                return true;
            case ORG_SPECIFY_STORE:
                if (!selectOrgCodes.contains(orgCode)) {
                    log.debug("condition is not satisfied. checkOrgInfo uid:{} orgCode: {} type is not match. actId:{}", uid, orgCode, promotionId);
                    return false;
                }
                return true;
            case ORG_SPECIFY_AREA:
                if (!(CollectionUtils.containsAny(selectOrgCodes, orgInfo.getArea()) || selectOrgCodes.contains(orgCode))) {
                    log.debug("condition is not satisfied. checkOrgInfo uid:{} orgCode: {} area is not match. actId:{}", uid, orgCode, promotionId);
                    return false;
                }
                return true;
            case ORG_ALL_AUTHORIZED_STORE:
            case ORG_AUTHORIZED_STORE:
            case ORG_AUTHORIZED_AREA:
                return false;
            default:
                log.error("condition is not satisfied. condition orgScope is invalid. uid:{}, orgCode:{}, actID:{},", uid, orgCode, promotionId);
                return false;
        }
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof MultiPromotionConfig)) {
            log.error("config is not instanceof MultiPromotionConfig. config:{}", config);
            return;
        }
        MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.offline = promotionConfig.getOffline();
        this.orgScope = promotionConfig.getOrgScope();
        this.selectOrgCodes = promotionConfig.getSelectOrgCodes();
        this.version = promotionConfig.getVersion();
    }
}
