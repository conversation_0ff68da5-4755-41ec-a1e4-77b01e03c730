package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.enums.BooleanV2Enum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyGiftPromotionConfig;
import com.xiaomi.nr.promotion.tool.ConditionCheckTool;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * F会员赠品条件判断组件
 *
 * <AUTHOR>
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class FmemberCondition extends Condition {
    /**
     * 是否F会员专属赠品 1-是，2-否
     */
    private Integer isFMember;

    @Autowired
    private ConditionCheckTool conditionCheckTool;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        return conditionCheckTool.checkFmember(isFMember, request.getUserIsFriend());
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof BuyGiftPromotionConfig)) {
            log.error("config is not instanceof BuyGiftPromotionConfig. config:{}", config);
            return;
        }
        BuyGiftPromotionConfig promotionConfig = (BuyGiftPromotionConfig) config;
        isFMember = promotionConfig.getIsFMember();
    }
}
