package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.entity.redis.QuotaEle;
import com.xiaomi.nr.promotion.enums.PolicyQuotaTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.model.promotionconfig.*;
import com.xiaomi.nr.promotion.util.CartHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 减钱类条件
 *
 * <AUTHOR>
 * @date 2021/6/1
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class QuotaCondition extends AbstractCondition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 参与商品信息
     */
    private CompareItem joinGoods;
    /**
     * 活动类型
     */
    private PromotionToolType promotionType;
    /**
     * 条件阶梯
     */
    private List<QuotaLevel> levelList;
    /**
     * 是否允许套装内商品参加
     */
    private boolean checkPackage;
    /**
     * 销售来源
     */
    private List<String> saleSources;
    /**
     * 活动密码
     */
    private String accessCode;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) {
        Long uid = request.getUserId();
        if (joinGoods == null) {
            log.error("condition is not satisfied. includedGoodsGroups is empty. actId:{} uid:{}", promotionId, uid);
            return false;
        }

        List<CartItem> cartList = request.getCartList();
        Integer activityType = promotionType.getTypeId();
        boolean online = StringUtils.isEmpty(request.getOrgCode());

        // 获取符合商品
        List<GoodsIndex> indexList = doGoodsGroupMatch(cartList, context, joinGoods, uid, online, promotionId, activityType,
                checkPackage, saleSources, accessCode);

        // 没有符合的商品，不满足活动
        if (CollectionUtils.isEmpty(indexList)) {
            log.debug("condition is not satisfied. fillGoodsList is empty. actId:{} uid:{}", promotionId, uid);
            return false;
        }

        // 累加满足商品的总钱数、总件数, 查找是否有匹配的条件
        ValidGoods validGoods = CartHelper.buildValidGoods(cartList, indexList);
        QuotaLevel level = getMatchSatisfiedQuota(levelList, validGoods);

        // 商品金额或件数未达到，不满足活动
        if (level == null) {
            log.debug("condition is not satisfied. fill level is not found. actId:{} uid:{}", promotionId, uid);
            return false;
        }

        context.setQuotaLevel(level);
        context.setGoodIndex(indexList);
        return true;
    }

    private QuotaLevel getMatchSatisfiedQuota(List<QuotaLevel> levelList, ValidGoods validGoods) {
        if (CollectionUtils.isEmpty(levelList)) {
            log.error("act levelList is empty. actId:{}", promotionId);
            return null;
        }
        return levelList.stream()
                .filter(level -> level.getQuotas().stream().anyMatch(quotaEle -> isSatisfiedQuota(quotaEle, validGoods)))
                .findFirst().orElse(null);
    }

    private boolean isSatisfiedQuota(QuotaEle quotaEle, ValidGoods validGoods) {
        PolicyQuotaTypeEnum quotaType = PolicyQuotaTypeEnum.getQuotaType(quotaEle.getType());
        if (quotaType == null) {
            return false;
        }
        boolean isSatisfied = false;
        switch (quotaType) {
            case POLICY_QUOTA_MONEY:
            case POLICY_QUOTA_PER_MONEY:
                isSatisfied = validGoods.getValidPrice() >= quotaEle.getMoney();
                break;
            case POLICY_QUOTA_NUM:
            case POLICY_QUOTA_PER_NUM:
                isSatisfied = validGoods.getValidNum() >= quotaEle.getCount();
                break;
            default:
                log.error("act policy quota type is wrong. actId:{}", promotionId);
                break;
        }
        return isSatisfied;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof MultiPromotionConfig)) {
            log.error("config is not instanceof MultiPromotionConfig. config:{}", config);
            return;
        }
        if (config instanceof ReducePromotionConfig) {
            ReducePromotionConfig promotionConfig = (ReducePromotionConfig) config;
            this.levelList = promotionConfig.getLevelList();
        }
        if (config instanceof DiscountPromotionConfig) {
            DiscountPromotionConfig promotionConfig = (DiscountPromotionConfig) config;
            this.levelList = promotionConfig.getLevelList();
        }
        if (config instanceof PostFreePromotionConfig) {
            PostFreePromotionConfig promotionConfig = (PostFreePromotionConfig) config;
            this.levelList = promotionConfig.getLevelList();
        }
        MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.joinGoods = promotionConfig.getJoinGoods();
        this.promotionType = promotionConfig.getPromotionType();
        this.checkPackage = promotionConfig.isCheckPackage();
        this.saleSources = promotionConfig.getSaleSources();
        this.accessCode = promotionConfig.getAccessCode();
    }
}
