package com.xiaomi.nr.promotion.activity.carsale;

import com.xiaomi.nr.promotion.activity.AbstractActivityTool;
import com.xiaomi.nr.promotion.api.dto.MultiProductGoodsActRequest;
import com.xiaomi.nr.promotion.api.dto.model.MultiGoodItem;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfoDTO;
import com.xiaomi.nr.promotion.componet.action.carsale.CarRangeReduceAction;
import com.xiaomi.nr.promotion.componet.condition.carsale.CarRangeReduceCondition;
import com.xiaomi.nr.promotion.constant.PromotionTextConstant;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.ProductDetailContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.CarRangeReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.BenefitInfo;
import com.xiaomi.nr.promotion.resource.external.GoodsStockExternalProvider;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 范围立减
 *
 * <AUTHOR>
 * @date 2023/11/26 17:32
 **/
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class CarRangeReduceActivity extends AbstractActivityTool implements ActivityTool {

    protected Integer tradeType;

    protected BenefitInfo benefitInfo;

    /**
     * 条件判断、优惠分摊
     *
     * @return
     */
    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine.condition(CarRangeReduceCondition.class)
                .action(CarRangeReduceAction.class);
    }

    /**
     * 加载配置信息
     *
     * @param config 配置
     * @return
     * @throws BizError
     */
    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof CarRangeReducePromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        CarRangeReducePromotionConfig promotionConfig = (CarRangeReducePromotionConfig) config;
        this.actMutexLimit = promotionConfig.isActMutexLimit();
        this.tradeType = promotionConfig.getTradeType();
        this.benefitInfo = promotionConfig.getBenefitInfo();
        return true;
    }

    @Override
    public ActivityDetail getActivityDetail() {
        return null;
    }

    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) throws BizError {
        PromotionInfo promotionInfo = buildDefaultPromotionInfo(context);
        promotionInfo.setActivityMutexLimit(actMutexLimit ? BooleanEnum.YES.getValue() : BooleanEnum.NO.getValue());
        return promotionInfo;
    }

    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        return null;
    }
    @Override
    public PromotionInfoDTO getMultiProductAct(MultiGoodItem goodItem, MultiProductGoodsActRequest request, ProductDetailContext context) throws BizError {
        PromotionInfoDTO promotionInfo = initPromotionInfo();
        CarRangeReducePromotionConfig config = (CarRangeReducePromotionConfig)getPromotionConfig();
        String rule = config.getRangeReduceRule();
        promotionInfo.setRule(rule);
        promotionInfo.setPromotionText(PromotionTextConstant.CAR_RANGE_REDUCE);
        return promotionInfo;
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.RANGE_REDUCE;
    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR;
    }
}
