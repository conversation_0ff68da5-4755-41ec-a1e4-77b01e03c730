package com.xiaomi.nr.promotion.activity.carshop;

import com.google.common.collect.Lists;
import com.xiaomi.goods.gis.dto.stock.GiftStockRespParam;
import com.xiaomi.nr.promotion.activity.AbstractBuyGfitActivity;
import com.xiaomi.nr.promotion.api.dto.MultiProductGoodsActRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.MultiGoodItem;
import com.xiaomi.nr.promotion.api.dto.model.NumLimitRule;
import com.xiaomi.nr.promotion.api.dto.model.OrderCartItem;
import com.xiaomi.nr.promotion.api.dto.model.PolicyNew;
import com.xiaomi.nr.promotion.api.dto.model.PolicyNewGoods;
import com.xiaomi.nr.promotion.api.dto.model.PolicyNewGroup;
import com.xiaomi.nr.promotion.api.dto.model.PolicyNewLevel;
import com.xiaomi.nr.promotion.api.dto.model.PolicyNewRule;
import com.xiaomi.nr.promotion.api.dto.model.PolicyNewSkuGroup;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfoDTO;
import com.xiaomi.nr.promotion.componet.action.carshop.CarShopBuyGiftAction;
import com.xiaomi.nr.promotion.componet.condition.carshop.CarShopBuyGiftCondition;
import com.xiaomi.nr.promotion.constant.PromotionTextConstant;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.GiftBargainGroup;
import com.xiaomi.nr.promotion.entity.redis.SkuGroup;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.enums.BooleanV2Enum;
import com.xiaomi.nr.promotion.enums.SendTypeEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.ProductDetailContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.ActRespConverter;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyGiftPromotionConfig;
import com.xiaomi.nr.promotion.resource.external.GoodsStockExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024/6/18
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class CarShopBuyGiftActivity extends AbstractBuyGfitActivity {

    @Resource
    private ActivityRedisDao activityRedisDao;

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof BuyGiftPromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        BuyGiftPromotionConfig promotionConfig = (BuyGiftPromotionConfig) config;
        this.giftGoods = promotionConfig.getGiftGoods();
        this.includeGoodsGroups = Collections.singletonList(promotionConfig.getIncludeGoodsGroup());
        return true;
    }
    
    @Override
    public ActivityDetail getActivityDetail() {
        return null;
    }
    
    @Override
    public PromotionToolType getType() {
        return PromotionToolType.BUY_GIFT;
    }
    
    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine
                .condition(CarShopBuyGiftCondition.class)
                .action(CarShopBuyGiftAction.class);
    }
    
    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList)
            throws BizError {
        return null;
    }
    
    /**
     * 构建购物车优惠数据
     *
     * @param context 上下文
     * @return 优惠促销信息
     */
    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) {
        Map<String, GiftStockRespParam> stockMap;
        try {
            GoodsStockExternalProvider provider =
                    (GoodsStockExternalProvider) context.getExternalDataMap().get(ResourceExtType.GIFT_GOODS_STOCK);
            stockMap = provider.getData();
        } catch (Exception e) {
            return new PromotionInfo();
        }

        // build master id list : 由于存在删购物车情况，不能使用idx下标取购物车商品
        List<Long> masterIdList = Lists.newArrayList();
        Map<String, CartItem> cartMap = context.getCarts().stream()
                .collect(Collectors.toMap(OrderCartItem::getItemId, Function.identity(), (x, y) -> x));
        for (GoodsIndex goodIndex : context.getGoodIndex()) {
            CartItem cartItem = cartMap.get(goodIndex.getItemId());
            if (Objects.isNull(cartItem)) {
                continue;
            }
            masterIdList.add(cartItem.getSsuId());
        }

        NumLimitRule numLimitRule = ActRespConverter.convert(this.numLimitRule);
        PolicyNew policyNew = buildPolicyNew((BuyGiftPromotionConfig) promotionConfig, masterIdList, stockMap,
                context.getFillTimes());
        if (Objects.isNull(policyNew)) {
            return null;
        }
        
        PromotionInfo promotionInfo = buildDefaultPromotionInfo(context);
        promotionInfo.setFrequent(null);
        promotionInfo.setTotalLimitNum(actLimitNum);
        promotionInfo.setActivityMutexLimit(BooleanEnum.NO.getValue());
        promotionInfo.setActivityMutex(Collections.emptyList());
        promotionInfo.setNumLimitRule(numLimitRule);
        promotionInfo.setPolicyNew(policyNew);
        return promotionInfo;
    }
    
    @Override
    public PromotionInfoDTO getMultiProductAct(MultiGoodItem goodItem, MultiProductGoodsActRequest request, ProductDetailContext context) throws BizError {
        PromotionInfoDTO promotionInfo = initPromotionInfo();
        promotionInfo.setPromotionText(PromotionTextConstant.CAR_SHOP_BUY_GIFT);
        try {

            GoodsStockExternalProvider externalDataProvider = (GoodsStockExternalProvider) context.getExternalDataMap().get(ResourceExtType.GIFT_GOODS_STOCK);

            Map<String, GiftStockRespParam> stockMap = externalDataProvider.getData();

            List<Long> goods = request.getGoodsList().stream().map(item -> item.getSsuId()).collect(Collectors.toList());
            List<Long> masters = includeGoodsGroups.get(0).getJoinGoods().getSsuId();
            goods.retainAll(masters);
            PolicyNew policyNew = buildPolicyNew((BuyGiftPromotionConfig) promotionConfig, goods, stockMap, 1);
            if (policyNew == null) {
                return null;
            }
            promotionInfo.setPolicyNew(policyNew);
        } catch (Exception e) {
            log.error("getMultiProductAct error request:{}", request, e);
            return null;
        }
        return promotionInfo;
    }
    
    private PolicyNew buildPolicyNew(BuyGiftPromotionConfig promotionConfig, List<Long> masterIdList, Map<String,
            GiftStockRespParam> stockMap, Integer fillTimes) {
        PolicyNew policyNew = new PolicyNew();
        policyNew.setType(Long.valueOf(getPromotionType().getTypeId()));
        
        PolicyNewLevel policyNewLevel = new PolicyNewLevel();
        PolicyNewRule policyNewRule = new PolicyNewRule();
        PolicyNewGoods policyNewGoods = new PolicyNewGoods();
        List<PolicyNewSkuGroup> list = this.giftGoods.getSkuGroupList().stream()
                .map(x -> this.convertNewSkuGroup(x, masterIdList, stockMap, fillTimes))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        policyNewGoods.setSkuGroupsList(list);
        policyNewRule.setIgnoreStock(promotionConfig.getGiftGoods().getIgnoreStock());
        policyNewRule.setGiftGoods(policyNewGoods);
        policyNewRule.setConfigUrl(promotionConfig.getConfigUrl());
        policyNewLevel.setRule(policyNewRule);
        
        policyNew.setPolicy(Lists.newArrayList(policyNewLevel));
        return policyNew;
    }
    
    private PolicyNewSkuGroup convertNewSkuGroup(SkuGroup skuGroup, List<Long> masterIdList, Map<String, GiftStockRespParam> stockMap,
                                                 Integer fillTimes) {
        List<PolicyNewGroup> listInfo =
                skuGroup.getListInfo().stream()
                        .map(x -> this.convertNewGroup(x, skuGroup.getGroupId(), masterIdList, stockMap, fillTimes))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(listInfo)) {
            return null;
        }

        PolicyNewSkuGroup policyNewSkuGroup = new PolicyNewSkuGroup();
        policyNewSkuGroup.setGroupId(skuGroup.getGroupId());
        policyNewSkuGroup.setListInfo(listInfo);
        policyNewSkuGroup.setSendType(SendTypeEnum.ONE.getCode());
        return policyNewSkuGroup;
    }
    
    private PolicyNewGroup convertNewGroup(GiftBargainGroup giftBargainGroup, Long groupId, List<Long> masterIdList,
                                           Map<String, GiftStockRespParam> stockMap, Integer fillTimes) {
        Integer isInStock = BooleanV2Enum.YES.getValue();

        // check activity stock
        Integer limitNum = activityRedisDao.getActBuyGiftLimitNum(promotionConfig.getPromotionId(),
                String.valueOf(giftBargainGroup.getSsuId()), groupId);
        if ((giftBargainGroup.getGiftLimitNum() - limitNum) < fillTimes * giftBargainGroup.getGiftBaseNum()) {
            isInStock = BooleanV2Enum.NO.getValue();
        }


        // check goods stock
        if (Objects.equals(giftGoods.getIgnoreStock(), BooleanV2Enum.NO.getValue())) {
            for (Long master : masterIdList) {
                String stockKey = master + "_" + giftBargainGroup.getSsuId();
                GiftStockRespParam resp = stockMap.get(stockKey);
                if (Objects.isNull(resp) || Objects.isNull(resp.getStockNum()) || resp.getStockNum() / giftBargainGroup.getGiftBaseNum() < fillTimes) {
                    isInStock = BooleanV2Enum.NO.getValue();
                }
            }
        }

        // 没有库存则不返回此赠品
        if (Objects.equals(isInStock, BooleanV2Enum.NO.getValue())) {
            return null;
        }


        PolicyNewGroup policyNewGroup = new PolicyNewGroup();
        policyNewGroup.setSsuId(giftBargainGroup.getSsuId());
        policyNewGroup.setSku(giftBargainGroup.getSku());
        policyNewGroup.setBaseNum(giftBargainGroup.getGiftBaseNum());
        policyNewGroup.setActNumLimit(isInStock);
        policyNewGroup.setIsInStock(isInStock);
        policyNewGroup.setCartPrice(giftBargainGroup.getCartPrice());
        policyNewGroup.setMarketPrice(giftBargainGroup.getMarketPrice());
        return policyNewGroup;
    }
    
    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR_SHOP;
    }
}
