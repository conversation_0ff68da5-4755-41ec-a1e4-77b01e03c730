package com.xiaomi.nr.promotion.rpc.crowdportrait;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import com.google.common.base.Stopwatch;
import com.xiaomi.iauth.java.sdk.app.IAuthAppSDKTool;
import com.xiaomi.iauth.java.sdk.common.AppInfo;
import com.xiaomi.iauth.java.sdk.common.IAuthTokenInfo;
import com.xiaomi.nr.promotion.rpc.crowdportrait.model.RuleResponse;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/27 15:46
 */
@Component
@Slf4j
public class CrowdPortraitServiceProxy {
    private static final String SERVICE_ID = "virtual_assistant";

    @Value("${iauth.sdk.app.appId}")
    private String appId;

    @Value("${iauth.sdk.app.appKey}")
    private String appKey;

    @Value("${wanxiang.host}")
    private String host;

    @Value("${wanxiang.path}")
    private String path;

    @Value("${wanxiang.path.suffix:}")
    private String pathSuffix;

    /**
     * 判断用户是否命中人群包
     *
     * @param idType     身份类型
     * @param idValue    身份值
     * @param ruleIds 规则ID集合
     * @return RuleResponse 查询结果
     * @throws BizError 业务异常
     */
    @Async("crowdPortraitAsyncTaskExecutor")
    public ListenableFuture<RuleResponse> batchRuleQuery(String idType, String idValue, List<String> ruleIds) {
        Stopwatch stopwatch = Stopwatch.createStarted();

        HttpRequest request = null;

        RuleResponse ruleResponse = new RuleResponse();

        try {
            // 代码中传递appInfo进行替换
            AppInfo appInfo = new AppInfo();
            appInfo.setAppId(Long.parseLong(appId));
            appInfo.setAppSecret(appKey);

            // 获取token
            IAuthTokenInfo info = IAuthAppSDKTool.getInstance(appInfo).getIAuthToken(SERVICE_ID, false);

            log.info("CrowdPortraitServiceProxy.batchRuleQuery token = {}", GsonUtil.toJson(info));

            // 拼接URL
            URIBuilder uriBuilder = new URIBuilder();
            uriBuilder.setScheme("http");
            uriBuilder.setHost(host);
            // TODO: 测试环境进行人群包判存的时候，需要在用户id末尾加上#staging，上线后去掉#staging
            uriBuilder.setPath(path + "/" + idType + "/" + idValue + pathSuffix);
            // 规则ID集合，多个rule之间用英文逗号分隔，例："up_1618,up_8121"(up表示的是画像平台，1618表示的是画像平台上的人群包ID）
            uriBuilder.setParameter("ruleIdList",
                    ruleIds.stream().map(s -> "up_" + s)
                            .collect(Collectors.joining(",")));
            uriBuilder.setParameter("appId", appId);
            uriBuilder.setParameter("token", info.getToken());
            String url = uriBuilder.build().toString();

            // 创建 HTTP 请求
            request = HttpUtil.createGet(url);

            HttpResponse response = request.execute();

            log.info("CrowdPortraitServiceProxy.batchRuleQuery finished, url = {}, response = {}, cost = {}", url, response, stopwatch.elapsed(TimeUnit.MILLISECONDS));

            if (response.getStatus() != HttpStatus.HTTP_OK) {
                log.error("CrowdPortraitServiceProxy.batchRuleQuery 查询万象平台，判断用户是否命中人群包失败！");
                throw ExceptionHelper.create(GeneralCodes.InternalError, "查询万象平台，判断用户是否命中人群包失败");
            }

            ruleResponse = GsonUtil.fromJson(response.body(), RuleResponse.class);

            if (ruleResponse == null) {
                log.error("CrowdPortraitServiceProxy.batchRuleQuery response is null");
                throw ExceptionHelper.create(GeneralCodes.InternalError, "查询万象平台，判断用户是否命中人群包返回结果为空");
            }

            if (ruleResponse.getRetCode() != 0) {
                log.error("CrowdPortraitServiceProxy.batchRuleQuery 查询万象平台，判断用户是否命中人群包失败！ retMst = {}", ruleResponse.getRetMsg());
                throw ExceptionHelper.create(GeneralCodes.InternalError, "查询万象平台，判断用户是否命中人群包失败");
            }

        } catch (Exception e) {
            log.error("CrowdPortraitServiceProxy.batchRuleQuery error, e =", e);
        }

        return AsyncResult.forValue(ruleResponse);
    }
}
