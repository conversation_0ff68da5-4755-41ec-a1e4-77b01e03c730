package com.xiaomi.nr.promotion.domain.coupon.service.carshop;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.service.base.type.AbstractDiscountCoupon;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.CouponHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 车商城-折扣券（2满折）
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class CarShopDiscountCoupon extends AbstractDiscountCoupon {

    @Autowired
    private transient CheckoutCartTool checkoutCartTool;

    @Override
    public Coupon generateCartCoupon() {
        return initCoupon();
    }

    @Override
    public CouponCheckoutResult checkoutCoupon(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        // 筛选适用该券的商品列表
        List<CartItem> cartList = request.getCartList();
        List<GoodsIndex> indexList = matchCartList(cartList, context);

        // 计算适用商品列表的总价（去掉前置优惠）
        ValidGoods validGoods = buildValidGoods(cartList, indexList);

        // 判断适用商品列表是否满足券的使用条件：满额/满件
        CouponCheckoutResult result = new CouponCheckoutResult();
        Pair<Boolean, String> pair = isSatisfiedQuota(validGoods);
        long cartTotalPrice = getCartTotalPrice(cartList);
        // 构建结果
        // 条件满足
        if (pair.getLeft()) {
            result.setCouponId(this.id);
            if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                result.setCouponCode(checkoutCoupon.getCouponCode());
            }
            result.setAllow(true);
            result.setValidGoodsPrice(validGoods.getValidPrice());//购物车中适用该券的所有商品的总价（去掉前置优惠）
            result.setValidGoods(indexList);//购物车中适用该券的所有商品
            // 计算减免金额
            Long reduceMoney = getReduce(cartTotalPrice, validGoods);
            result.setReduceAmount(reduceMoney);//该券的可减免金额
        } else {
            result.setAllow(false);
            result.setCouponId(this.id);
            if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                result.setCouponCode(checkoutCoupon.getCouponCode());
            }
            result.setUnusableReason("未满足优惠券使用条件");
            result.setUnusableCode(ErrCode.ERR_COUPON_MONEY_NUM.getCode());
            result.setKeyDataUnusable(pair.getRight());
        }
        return result;
    }

    //计算当前券的减免金额
    long getReduce(long cartTotalPrice, ValidGoods validGoods) {
        long discount = checkoutCoupon.getPromotionValue();//券折扣
        long newPrice = discount * validGoods.getValidPrice() / 100;// 打折后的价钱
        long reduceMoney = validGoods.getValidPrice() - newPrice;// 券的理论减免金额
        reduceMoney = Math.min(reduceMoney, validGoods.getValidPrice());//券的减免金额<券的理论减免金额，所有适用商品的总价(去掉前置优惠)>
        long maxPrice = checkoutCoupon.getMaxReduce();//券配置的折后最高可以减免的金额
        if (maxPrice != 0) {
            reduceMoney = Math.min(maxPrice, reduceMoney);
        }
        // 减的金额需小于当前总价，消除0元订单
        if (reduceMoney >= 1 && reduceMoney == validGoods.getValidPrice() && cartTotalPrice == validGoods.getValidPrice()) {
            reduceMoney -= 1L;
        }
        log.debug("reduce {}, maxPrice {} discount {}", reduceMoney, maxPrice, discount);
        return reduceMoney;
    }

    // 查找购物车中适用该券的商品
    protected List<GoodsIndex> matchCartList(List<CartItem> cartList, CheckoutContext context)
            throws BizError {
        List<GoodsIndex> indexList = new ArrayList<>();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);

            if (SourceEnum.isGiftBargain(item.getSource())) {
                continue;
            }
            //车商城加购页未选中商品跳过
            Boolean selected = item.getSelected();
            if (context.getFromInterface().equals(FromInterfaceEnum.CHECKOUT_CART) && (Objects.isNull(selected) || Objects.equals(selected, Boolean.FALSE))) {
                continue;
            }
            // 判断当前商品是否适用该券类型
            boolean itemQualify = CartHelper.isCouponQualifyItem(item, (long) getCouponType().getType());
            if (!itemQualify) {
                log.debug("coupon:{}  matchCartList itemQualify item:{}", getCouponId(), GsonUtil.toJson(item));
                continue;
            }
            // 判断当前商品是否适是该券的适用商品
            boolean isMatched;
            isMatched = includeGoods.contains(getJoinGoods(item));
            if (!isMatched) {
                log.debug("coupon:{} matchCartList isMatched item:{}", getCouponId(), GsonUtil.toJson(item));
                continue;
            }
            indexList.add(new GoodsIndex(item.getItemId(), idx));
        }
        return indexList;
    }

    public Long getJoinGoods(CartItem cartItem) {
        return cartItem.getSsuId();
    }

    // 计算适用商品列表的总价（去掉前置优惠）
    protected ValidGoods buildValidGoods(List<CartItem> cartList, List<GoodsIndex> goodsInd) {
        long validPrice = 0L;
        long includeCount = 0L;
        int len = cartList.size();
        for (GoodsIndex index : goodsInd) {
            int idx = index.getIndex();
            if (idx >= len) {
                continue;
            }
            CartItem item = cartList.get(idx);
            if (item == null) {
                continue;
            }

            Long curPrice = CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList());

            validPrice += (curPrice * item.getCount());
            includeCount += item.getCount();
        }
        ValidGoods validGoods = new ValidGoods();
        validGoods.setValidNum(includeCount);
        validGoods.setValidPrice(validPrice);
        return validGoods;
    }

    public long getCartTotalPrice(List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(cartList)) {
            return 0L;
        }
        return cartList.stream()
                .filter(item -> !SourceEnum.isGift(item.getSource()))
                .mapToLong(item -> CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList()) * item.getCount())
                .sum();
    }

    @Override
    public void updateItemReduce(List<CartItem> cartList, CouponCheckoutResult result) {

        long couponId = result.getCouponId();
        String idKey = CouponHelper.getCartListCouponKey(String.valueOf(couponId));
        List<Integer> indexList = result.getValidGoods().stream().map(GoodsIndex::getIndex).collect(Collectors.toList());
        List<CartItem> fillCartList = CartHelper.getCartList(cartList, indexList);
        //分摊金额
        checkoutCartTool.divideCartsReduceBySsu(result.getReduceAmount(), fillCartList, idKey, getCouponType().getType(), couponId, result.getBudgetApplyNo(), result.getLineNum());
    }

    @Override
    public void updateCartsReduce(CheckoutPromotionRequest request, CheckoutContext context, CouponCheckoutResult result) {
        //处理商品分摊
        updateItemReduce(request.getCartList(), result);

        //更新context
        updateCommonInfo(request, context);
    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR_SHOP;
    }
}
