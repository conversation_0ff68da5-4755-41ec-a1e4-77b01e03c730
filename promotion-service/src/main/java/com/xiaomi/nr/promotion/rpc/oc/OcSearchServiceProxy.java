package com.xiaomi.nr.promotion.rpc.oc;

import com.google.gson.JsonObject;
import com.xiaomi.nr.promotion.mq.consumer.entity.OcSearchApiResponse;
import com.xiaomi.nr.promotion.mq.consumer.entity.RefundStatistic;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.TreeMap;

/**
 * 订单服务
 *
 * <AUTHOR>
 * @date 2025/4/17
 */
@Service
@Slf4j
public class OcSearchServiceProxy {

    @Value("${oc.search.appid}")
    private String appId;

    @Value("${oc.search.appsecret}")
    private String appSecret;

    @Value("${oc.search.url}")
    private String url;

    /**
     * 查询订单是否整单退完
     *
     * @param orderId 订单ID
     * @return true/false
     */
    public Boolean isAllRefund(Long orderId) throws Exception {
        if (orderId == null || orderId <= 0) {
            log.warn("订单id异常, orderId:{}", orderId);
            return Boolean.FALSE;
        }
        log.info("调用oc方接口入参,orderId:{}",orderId);
        String result = getRefundInfo(String.valueOf(orderId));
        log.info("调用oc方接口出参,response:{}", result);
        if (StringUtils.isEmpty(result)) {
            log.info("getRefundInfo method 返回空字符串, orderId:{}", orderId);
            return Boolean.FALSE;
        }
        OcSearchApiResponse apiResponse = GsonUtil.fromJson(result, OcSearchApiResponse.class);
        if (Objects.isNull(apiResponse) || Objects.isNull(apiResponse.getBody())) {
            log.error("invoke oc search,getRefundInfo method, result异常, orderId:{}", orderId);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "调用oc方查询异常");
        }
        List<RefundStatistic> refundStatistics = apiResponse.getBody().getRefundStatistics();
        if (CollectionUtils.isEmpty(refundStatistics)) {
            log.error("invoke oc search,返回退款数据为空, orderId:{}", orderId);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "调用oc方退款数据异常");
        }
        return refundStatistics.stream()
                .allMatch(refundStatistic -> Objects.equals(refundStatistic.getValidGoodsCount(), 0));
    }


    private String getRefundInfo(String orderId) throws Exception {
        // 构造原始请求数据
        TreeMap<String, Object> reqData = new TreeMap<>();
        reqData.put("order_id", orderId);
        String jsonBody = GsonUtil.toJson(reqData);
        // 生成签名
        String signRaw = appId + jsonBody + appSecret;
        String sign = generateMD5(signRaw).toUpperCase();

        // 构造最终data结构
        JsonObject data = new JsonObject();
        JsonObject header = new JsonObject();
        header.addProperty("appid", appId);
        header.addProperty("sign", sign);
        header.addProperty("method", "refundStatistics");
        data.add("header", header);
        data.addProperty("body", jsonBody);

        // Base64编码
        String base64Data = Base64.getEncoder().encodeToString(data.toString().getBytes(StandardCharsets.UTF_8));
        // 构建Form表单请求体
        String formBody = "data=" + base64Data + "&order_id=" + orderId;

        // 发送请求
        return sendPostRequest(url, formBody);
    }

    private String generateMD5(String input) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] digest = md.digest(input.getBytes(StandardCharsets.UTF_8));
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    private String sendPostRequest(String url, String formBody) throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = getHttpPost(url, formBody);
            // 执行请求并打印结果
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity entity = response.getEntity();
                if (entity != null) {
                    return EntityUtils.toString(entity);
                } else {
                    log.error("invoke oc search,http返回结果为空, url:{}, formBody:{}", GsonUtil.toJson(url), GsonUtil.toJson(formBody));
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "invoke oc方entity为null");
                }
            }
        } catch (Exception e) {
            log.error("invoke sendPostRequest method fail, url:{}, formBody:{}", GsonUtil.toJson(url), GsonUtil.toJson(formBody));
            throw ExceptionHelper.create(GeneralCodes.InternalError, "invoke oc方异常");
        }
    }

    private static HttpPost getHttpPost(String url, String formBody) throws URISyntaxException {
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("User-Agent", "PostmanRuntime/7.43.3");
        httpPost.setHeader("Accept", "*/*");
        URI uri = new URI(url);
        httpPost.setHeader("Host", uri.getHost());
        httpPost.setHeader("Accept-Encoding", "gzip, deflate, br");
        httpPost.setHeader("Connection", "keep-alive");
        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
        // 设置请求体
        httpPost.setEntity(new StringEntity(formBody, ContentType.APPLICATION_FORM_URLENCODED));
        return httpPost;
    }


}
