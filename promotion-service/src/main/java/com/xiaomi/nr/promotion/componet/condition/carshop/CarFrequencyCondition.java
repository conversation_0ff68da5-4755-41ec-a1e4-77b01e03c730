package com.xiaomi.nr.promotion.componet.condition.carshop;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.enums.ActFrequencyEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyReducePromotionConfig;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/14 08:57
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarFrequencyCondition extends Condition {

    private Long promotionId;

    private Integer limitType;

    private Integer limitCount;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        Long uid = request.getUserId();
        if (limitType == null) {
            log.error("condition is not satisfied. condition frequency is illegal. actId:{} uid:{}", promotionId, uid);
            return false;
        }
        ActFrequencyEnum frequency = ActFrequencyEnum.getByValue(limitType);
        if (Objects.isNull(frequency)) {
            log.error("condition is not satisfied. condition frequency is illegal. actId:{} uid:{}", promotionId, uid);
            return false;
        }
        // 没有频次限制
        if (frequency == ActFrequencyEnum.NONE) {
            return true;
        }
        // 有频次限制，用户又没有登录，认为不能参加活动
        if (uid == null || uid == 0L) {
            return false;
        }
        Integer actPersonLimitNum = activityRedisDao.getActPersonLimitNum(promotionId, uid);
        if (frequency == ActFrequencyEnum.TOTAL) {
            return NumberUtils.INTEGER_ZERO.equals(actPersonLimitNum);
        }
        if (frequency == ActFrequencyEnum.TOTAL_TIMES) {
            return actPersonLimitNum < limitCount;
        }
        return true;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig buyReducePromotionConfig) {
        if (!(buyReducePromotionConfig instanceof BuyReducePromotionConfig)) {
            log.error("config is not instanceof OnsalePromotionConfig. config:{}", buyReducePromotionConfig);
            return;
        }
        BuyReducePromotionConfig config = (BuyReducePromotionConfig) buyReducePromotionConfig;
        this.promotionId = config.getPromotionId();
        this.limitType = config.getLimitType();
        this.limitCount = config.getLimitCount();
    }
}
