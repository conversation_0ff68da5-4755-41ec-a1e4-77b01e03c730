package com.xiaomi.nr.promotion.rpc.order;

import com.xiaomi.nr.order.api.dto.request.order.submit.OrderSubmitQueryReq;
import com.xiaomi.nr.order.api.dto.response.submit.OrderSubmitQueryRsp;
import com.xiaomi.nr.order.api.service.order.SubmitOrderService;
import com.xiaomi.nr.promotion.enums.TradeFromEnum;
import com.xiaomi.nr.promotion.resource.model.OrderStatus;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

/**
 * 订单服务
 *
 * <AUTHOR>
 * @date 2021/8/16
 */
@Service
@Slf4j
public class SubmitOrderServiceProxy {

    @Reference(interfaceClass = SubmitOrderService.class, version = "1.0", timeout = 2000, group = "${order.dubbo.group}", check = false)
    private SubmitOrderService submitOrderService;

    /**
     * 查询订单是否存在
     *
     * @param orderId 订单ID
     * @return true/false
     */
    public OrderStatus getOrderStatus(Long orderId) throws BizError {
        OrderSubmitQueryReq req = OrderSubmitQueryReq.builder().build();
        req.setOrderId(orderId);
        req.setTradeFrom((long) TradeFromEnum.SHOP.getValue());

        // 开始请求
        Result<OrderSubmitQueryRsp> result;
        try {
            result = submitOrderService.orderSubmitQuery(req);
        } catch (Exception e) {
            log.error("invoke rpc orderSubmitQuery error. orderId:{}, err", orderId, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc orderSubmitQuery fail. orderId:{}, code:{} message:{}", orderId, result.getCode(), result.getMessage());
            return OrderStatus.NOT_FOUND;
        }
        if (result.getData() == null || CollectionUtils.isEmpty(result.getData().getOrderIds())) {
            log.error("invoke rpc orderSubmitQuery not found. orderId:{}, result:{}", orderId, result);
            return OrderStatus.NOT_FOUND;
        }
        return OrderStatus.COMMIT;
    }

    /**
     * 查询订单是否存在
     *
     * @param orderId 订单ID
     * @return true/false
     */
    public OrderStatus getOrderStatus(Long orderId, Long userId, TradeFromEnum tradeFromEnum) throws BizError {
        OrderSubmitQueryReq req = OrderSubmitQueryReq.builder().build();
        req.setOrderId(orderId);
        req.setTradeFrom((long) tradeFromEnum.getValue());
        req.setUserId(userId);

        // 开始请求
        Result<OrderSubmitQueryRsp> result;
        try {
            result = submitOrderService.orderSubmitQuery(req);
        } catch (Exception e) {
            log.error("invoke rpc orderSubmitQuery error. orderId:{}, err", orderId, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc orderSubmitQuery fail. orderId:{}, code:{} message:{}", orderId, result.getCode(), result.getMessage());
            return OrderStatus.NOT_FOUND;
        }
        if (result.getData() == null || CollectionUtils.isEmpty(result.getData().getOrderIds())) {
            log.error("invoke rpc orderSubmitQuery not found. orderId:{}, result:{}", orderId, result);
            return OrderStatus.NOT_FOUND;
        }
        return OrderStatus.COMMIT;
    }
}
