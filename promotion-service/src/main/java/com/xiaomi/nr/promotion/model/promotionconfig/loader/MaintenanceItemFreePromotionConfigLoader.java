package com.xiaomi.nr.promotion.model.promotionconfig.loader;

import com.xiaomi.nr.md.promotion.admin.api.constant.ScopeTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityScope;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ProductPolicy;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.MaintenanceItemFreeRule;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.enums.CarIdentityTypeEnum;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MaintenanceItemFreePromotionConfig;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 折扣配置数据加载
 *
 * <AUTHOR>
 * @date 2021/6/3
 */
@Slf4j
@Component
public class MaintenanceItemFreePromotionConfigLoader implements PromotionConfigLoader {

    @Override
    public boolean support(AbstractPromotionConfig promotionConfig) {
        if (promotionConfig instanceof MaintenanceItemFreePromotionConfig) {
            return true;
        }
        return false;
    }

    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityConfig activityConfig) throws BizError {

        if (activityConfig == null || promotionConfig == null) {
            log.error("[MaintenanceItemFreePromotionConfigLoader] activityInfo is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "activityInfo is null");
        }

        MaintenanceItemFreePromotionConfig itemFreePromotionConfig = (MaintenanceItemFreePromotionConfig) promotionConfig;

        // product
        List<ProductPolicy> productPolicyList = activityConfig.getProductPolicyList();
        if (CollectionUtils.isEmpty(productPolicyList) || productPolicyList.get(0) == null) {
            log.error("product policy is invalid. actId:{} policy:{} ", activityConfig.getId(), activityConfig.getProductPolicyList());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "policy is invalid");
        }
        List<Long> ssuList = Lists.newArrayList();
        for (ProductPolicy productPolicy : productPolicyList) {
            ssuList.add(productPolicy.getProductId());
        }
        CompareItem joinGoods = new CompareItem();
        joinGoods.setSsuId(ssuList);
        itemFreePromotionConfig.setJoinGoods(joinGoods);


        // rule
        MaintenanceItemFreeRule rule = GsonUtil.fromJson(activityConfig.getRule(), MaintenanceItemFreeRule.class);
        if (Objects.isNull(rule)) {
            throw ExceptionHelper.create(GeneralCodes.InternalError, "rule is null");
        }

        // 优先级 小 ---> 大， vip优先于其他活动
        CarIdentityTypeEnum carIdentityTypeEnum = CarIdentityTypeEnum.getEnumByCode(rule.getCarIdentityType());
        itemFreePromotionConfig.setPriority(carIdentityTypeEnum.getPriority());
        itemFreePromotionConfig.setPromotionPrice(rule.getPromotionPrice());
        itemFreePromotionConfig.setMaxReduceAmount(rule.getMaxReduceAmount());
        itemFreePromotionConfig.setDescription(rule.getDescription());
        itemFreePromotionConfig.setCarIdentityType(carIdentityTypeEnum.getCode());
        itemFreePromotionConfig.setCarIdentityId(rule.getCarIdentityId());
        itemFreePromotionConfig.setIdentityJoinLimitNum(rule.getUserLimitNum());
        ActNumLimitRule actNumLimitRule = new ActNumLimitRule();
        actNumLimitRule.setPersonLimit(Long.valueOf(rule.getUserLimitNum()));
        itemFreePromotionConfig.setNumLimitRule(actNumLimitRule);
        // scope
        List<Integer> workOrderList = Lists.newArrayList();
        for (ActivityScope scope : activityConfig.getActivityScopeList()) {
            if (Objects.equals(scope.getScopeType(), ScopeTypeEnum.ORDER_FROM.code)) {
                Arrays.stream(scope.getScopeValue().split(",")).map(Integer::valueOf).forEach(workOrderList::add);
            }
        }
        itemFreePromotionConfig.setWorkOrderType(workOrderList);

    }
}
