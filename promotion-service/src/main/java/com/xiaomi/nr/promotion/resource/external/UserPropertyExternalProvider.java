package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.resource.model.UserPropertyResult;
import com.xiaomi.nr.promotion.rpc.userProperty.UserPropertyProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class UserPropertyExternalProvider extends ExternalDataProvider<UserPropertyResult>{

    private ListenableFuture<UserPropertyResult> future;

    @Autowired
    private UserPropertyProxy userPropertyProxy;

    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        if (request.getUserId() == null || request.getUserId() <= 0) {
            return;
        }

        if (!request.getFMemberFlag() && !request.getStudentFlag() && CollectionUtils.isEmpty(request.getHaikuiIds())) {
            return;
        }

        future = userPropertyProxy.userPropertyV2(request.getUserId(), request.getFMemberFlag(), request.getStudentFlag(), request.getHaikuiIds());
    }

    @Override
    protected long getTimeoutMills() {
        return 50;
    }

    @Override
    protected ListenableFuture<UserPropertyResult> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.USER_PROPERTY_RESULT;
    }
}
