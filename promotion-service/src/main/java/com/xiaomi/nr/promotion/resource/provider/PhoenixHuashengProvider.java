package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.rpc.phoenix.ChinaUnicomHsServiceProxy;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 联通华盛错误信息
 *
 * <AUTHOR>
 * @date 2022/8/1
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class PhoenixHuashengProvider implements ResourceProvider<PhoenixHuashengProvider.ResContent> {
    private ResourceObject<PhoenixHuashengProvider.ResContent> resourceObject;

    @Autowired
    private ChinaUnicomHsServiceProxy chinaUnicomHsServiceProxy;

    @Override
    public ResourceObject<PhoenixHuashengProvider.ResContent> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<PhoenixHuashengProvider.ResContent> object) {
        this.resourceObject = object;
    }

    @Override
    public void lock() throws BizError {
        ResContent content = resourceObject.getContent();
        chinaUnicomHsServiceProxy.freezeCoupon(content.getUserId(), content.getOrgCode(), content.getOrderId(),
                content.getOrderTime(), content.getAmount(), content.getGoodsName(), content.getPayBarCode());
        log.info("freeze chinaUnicom coupon ok. resourceObject:{}", resourceObject);
    }

    @Override
    public void consume() throws BizError {
    }

    @Override
    public void rollback() throws BizError {
        ResContent content = resourceObject.getContent();
        chinaUnicomHsServiceProxy.unFreezeCoupon(content.getUserId(), content.getOrgCode(), content.getOrderId(),
                content.getOrderTime(), content.getAmount());
        log.info("unfreeze chinaUnicom coupon ok. resourceObject:{}", resourceObject);
    }

    @Override
    public String conflictText() {
        return null;
    }

    @Data
    public static class ResContent {
        /**
         * 用户ID
         */
        private Long userId;
        /**
         * 门店Code
         */
        private String orgCode;
        /**
         * 订单id
         */
        private Long orderId;
        /**
         * 提单时间
         */
        private Long orderTime;
        /**
         * 金额
         */
        private int amount;
        /**
         * 第一个商品名称
         */
        private String goodsName;
        /**
         * 支付条码
         */
        private String payBarCode;
    }
}
