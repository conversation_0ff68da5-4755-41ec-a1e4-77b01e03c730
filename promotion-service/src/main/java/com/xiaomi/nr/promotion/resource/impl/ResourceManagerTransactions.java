package com.xiaomi.nr.promotion.resource.impl;

import com.xiaomi.nr.promotion.api.dto.enums.SubmitTypeEnum;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionResourceDetailMapper;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionResourceOperationLogMapper;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionResourceStatusMapper;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.PromotionResourceDetail;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.PromotionResourceOperationLog;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.PromotionResourceStatus;
import com.xiaomi.nr.promotion.enums.TradeFromEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.resource.model.ResourceManageContext;
import com.xiaomi.nr.promotion.resource.model.ResourceStatus;
import com.xiaomi.nr.promotion.resource.model.ReturnStatus;
import com.xiaomi.nr.promotion.resource.model.TransactionResourceTool;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Objects;

/**
 * 资源数据操作，用于事务操作
 *
 * <AUTHOR>
 * @date : 2021/4/21
 */
@Component
@Slf4j
public class ResourceManagerTransactions {
    @Autowired
    private PromotionResourceDetailMapper resourceDetailMapper;
    @Autowired
    private PromotionResourceStatusMapper resourceStatusMapper;
    @Autowired
    private PromotionResourceOperationLogMapper resourceOperationLogMapper;

    /**
     * 插入初始化资源
     *
     * @param context 资源管理上下文
     * @param items   资源详情列表
     * @throws BizError 业务异常
     */
    @Transactional(transactionManager = "promotionUserConfigTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void initResourceData(ResourceManageContext context, List<PromotionResourceDetail> items) throws BizError {
        long orderId = context.getOrderId();
        long uid = context.getUid();
        long now = Instant.now().getEpochSecond();
        TradeFromEnum tradeFromEnum = context.getTradeFrom();
        PromotionResourceStatus status = initResourceStatus(orderId, uid, now, tradeFromEnum.getValue());

        if (Objects.equals(context.getSubmitType(), SubmitTypeEnum.REPLACE.getCode())) {
            replaceInitResource(context, items, status, now);
        } else {
            insertInitResource(context, items, status, now);
        }
    }

    private void replaceInitResource(ResourceManageContext context, List<PromotionResourceDetail> items, PromotionResourceStatus status, long now) throws BizError {

        List<PromotionResourceDetail> oldDetails = resourceDetailMapper.getDetailByOrderId(context.getOrderId());

        //开启事务
        PromotionResourceStatus oldStatusEntity = resourceStatusMapper.selectForUpdate(context.getOrderId());



        //删除老的优惠资源详情
        for (PromotionResourceDetail oldDetail : oldDetails) {
            resourceDetailMapper.deleteDetailByPrimaryId(oldDetail.getId());
        }

        //写入新的优惠资源详情
        insertResourceDetail(context, items, now);


        //修改status表的事务状态
        if (oldStatusEntity == null) {
            resourceStatusMapper.insertStatus(status);
        } else {
            resourceStatusMapper.updateStatus(context.getOrderId(), status.getStatus(), now);
        }


    }

    private void insertInitResource(ResourceManageContext context, List<PromotionResourceDetail> items, PromotionResourceStatus status, long now) throws BizError {
        long orderId = context.getOrderId();
        // 利用status表的orderId主键锁订单号，防止重复下单
        try {
            resourceStatusMapper.insertStatus(status);
        } catch (DuplicateKeyException e) {
            throw ExceptionHelper.create(ErrCode.CODE_RESOURCE_ORDER_ID_CONFLICT, "重复的订单号");
        }

        insertResourceDetail(context, items, now);
    }

    private void insertResourceDetail(ResourceManageContext context, List<PromotionResourceDetail> items, long now) throws BizError {

        //生成一批不重复的primaryKey
        List<Long> primaryKeys = TransactionResourceTool.generatePrimaryKeys(context.getOrderId(), items.size());
        for (int i = 0; i < items.size(); i++) {
            PromotionResourceDetail resource = items.get(i);
            // 利用detail表的resourceId+resourceType主键锁资源号，帮助资源对象锁资源
            String uniqueKey = String.format("%s-%d", resource.getResourceId(), resource.getResourceType());
            //提前生成分片后的主键
            resource.setId(primaryKeys.get(i));
            resource.setUniqueKey(uniqueKey);
            resource.setCreateTime(now);
            resource.setUpdateTime(now);
            try {
                resourceDetailMapper.insertDetail(resource);
            } catch (DuplicateKeyException e) {
                throw ExceptionHelper.create(ErrCode.CODE_RESOURCE_ITEM_LOCK_CONFLICT, context.getProviders().get(i).conflictText());
            }
        }
    }

    /**
     * 插入初始化资源
     *
     * @param context 资源管理上下文
     * @param status  资源状态
     */
    @Transactional(transactionManager = "promotionUserConfigTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void updateResourceStatus(ResourceManageContext context, ResourceStatus status) {
        log.debug("updateResourceStatus start. context:{}", context);
        context.setResourceStatus(status);

        // 资源已经提交或者回滚，释放uniqueKey资源
        if (status == ResourceStatus.COMMITED || status == ResourceStatus.ROLLBACKED) {
            resourceDetailMapper.clearDetailUniqueKey(context.getOrderId());
        }

        // 更新状态
        long updateTime = Instant.now().getEpochSecond();
        resourceStatusMapper.updateStatus(context.getOrderId(), status.getValue(), updateTime);

        // 插入操作日志
        try {
            PromotionResourceOperationLog operationLog = PromotionResourceOperationLog.fromResourceContext(context);
            log.debug("updateResourceStatus insert operation log:{}", operationLog);
            resourceOperationLogMapper.insertOperationLog(operationLog);
        } catch (Throwable e) {
            log.error("updateResourceStatus update promotion log error. context:{}", context, e);
        }
    }

    /**
     * 更新资源状态
     *
     * @param context 上下文
     * @param resourceDetail 资源详情
     * @param returnStatus 返还状态
     */
    @Transactional(transactionManager = "promotionUserConfigTransactionManager", rollbackFor = {Exception.class, BizError.class})
    public void updateResourceDetailStatus(ResourceManageContext context, PromotionResourceDetail resourceDetail, ReturnStatus returnStatus) {
        log.debug("updateResourceDetailStatus start. context:{}", context);
        resourceDetailMapper.updateDetailReturnStatusByOidId(returnStatus.getValue(), resourceDetail.getId(), resourceDetail.getOrderId());
    }

    private PromotionResourceStatus initResourceStatus(long orderId, long uid, long time, int tradeFrom) {
        PromotionResourceStatus status = new PromotionResourceStatus();
        status.setOrderId(orderId);
        status.setStatus(ResourceStatus.INIT.getValue());
        status.setTradeFrom(tradeFrom);
        status.setCreateTime(time);
        status.setUpdateTime(time);
        status.setUid(uid);
        return status;
    }
}
