package com.xiaomi.nr.promotion.dao.mysql.promotionuser;


import com.xiaomi.nr.promotion.entity.mysql.promotionuser.PromotionResourceStatus;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 资源状态Mapper
 *
 * <AUTHOR>
 * @date 2021/5/6
 */
@Repository
public interface PromotionResourceStatusMapper {
    /**
     * 插入资源状态
     *
     * @param resource 资源
     * @return 影响数量
     */
    @Insert("insert into promotion_resource_status (order_id,status,trade_from,create_time,update_time,uid, biz_platform) " +
            "values(#{orderId}, #{status}, #{tradeFrom}, #{createTime}, #{updateTime}, #{uid}, #{bizPlatform})")
    Integer insertStatus(PromotionResourceStatus resource);

    /**
     * 更新资源状态
     *
     * @param orderId    订单ID
     * @param status     状态
     * @param updateTime 更新时间
     * @return 影响数量
     */
    @Update("update promotion_resource_status " +
            "set status=#{status},update_time=#{updateTime} " +
            "where order_id=#{orderId}")
    Integer updateStatus(@Param("orderId") long orderId, @Param("status") int status, @Param("updateTime") long updateTime);

    /**
     * 根据订单ID获取资源状态实体
     *
     * @param orderId 订单ID
     * @return 资源状态实体
     */
    @Select("select order_id,status,trade_from,create_time,update_time,uid " +
            "from promotion_resource_status " +
            "where order_id=#{orderId}")
    PromotionResourceStatus getStatusByOrderId(@Param("orderId") long orderId);

    /**
     * 根据订单ID删除预售资源
     *
     * @param orderId 订单ID
     * @return 删除数量
     */
    @Delete("delete " +
            "from promotion_resource_status " +
            "where order_id=#{orderId} and status=3")
    Integer deletePreSaleStatus(@Param("orderId") long orderId);

    /**
     * 根据订单号删除资源状态信息，必须是已回滚状态
     *
     * @param orderId 订单ID
     * @return 删除数量
     */
    @Delete("delete " +
            "from promotion_resource_status " +
            "where order_id = #{orderId} and status = 3")
    Integer deleteStatusByOrderId(@Param("orderId") long orderId);

    /**
     * 因为设计问题，导致一些预售订单无法补偿，把那些订单的status状态设置成99
     *
     * @param orderId 订单ID
     * @return 更新数量
     */
    @Update("update promotion_resource_status " +
            "set status=99 " +
            "where order_id=#{orderId} and status =0")
    Integer updateUnableRemedialRecord(@Param("orderId") long orderId);

    /**
     * 获取超时订单数据
     *
     * @param keepTime 保持时间
     * @return 资源状态列表
     */
    @Select("select order_id,status,trade_from,create_time,update_time,uid" +
            " from promotion_resource_status " +
            " where (status = 0 or status = 1) and create_time<#{keepTime} and trade_from=1")
    List<PromotionResourceStatus> getTimeoutStatus(@Param("keepTime") long keepTime);


    @Select(" <script> " +
            " select order_id, status, trade_from, create_time, update_time, uid, biz_platform " +
            " from promotion_resource_status " +
            " where (status = 0 or status = 1) " +
            " and biz_platform in " +
            " <foreach collection='bizPlatformList' item='item' separator=',' open='(' close=')'> " +
            " #{item} " +
            " </foreach> " +
            " and update_time &lt; #{endTime} " +
            " and update_time &gt; #{beginTime} " +
            " limit 100 " +
            " </script> ")
    List<PromotionResourceStatus> getTimeoutOrder(@Param("beginTime") long beginTime, @Param("endTime") long endTime, @Param("bizPlatformList") List<Integer> bizPlatformList);

    /**
     * 获取超时订单数据
     *
     * @param keepTime 保持时间
     * @return 资源状态列表
     */
    @Select("select order_id,status,trade_from,create_time,update_time,uid" +
            " from promotion_resource_status " +
            " where order_id=#{orderId} for update")
    PromotionResourceStatus selectForUpdate(@Param("orderId") long orderId);
}
