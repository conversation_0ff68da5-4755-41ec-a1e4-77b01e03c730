package com.xiaomi.nr.promotion.model.promotionconfig.loader;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.md.promotion.admin.api.constant.ScopeTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityScope;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ProductPolicy;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.GovernmentSubsidyRule;
import com.xiaomi.nr.promotion.model.common.GovernmentRule;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.GovernmentSubsidyPromotionConfig;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class GovernmentSubsidyPromotionConfigLoader implements PromotionConfigLoader {
    @Override
    public boolean support(AbstractPromotionConfig promotionConfig) {
        return promotionConfig instanceof GovernmentSubsidyPromotionConfig;
    }

    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityConfig activityConfig) throws BizError {
        GovernmentSubsidyPromotionConfig governmentSubsidyPromotionConfig = (GovernmentSubsidyPromotionConfig) promotionConfig;

        GovernmentSubsidyRule activityRule = GsonUtil.fromJson(activityConfig.getRule(),
                GovernmentSubsidyRule.class);
        //国补折扣
        governmentSubsidyPromotionConfig.setReduceDiscount(activityRule.getReduceSpecification().getReduce());
        //国补最大减免的钱
        governmentSubsidyPromotionConfig.setMaxReducePrice(activityRule.getReduceSpecification().getMaxReduce());
        //国补能效等级
        governmentSubsidyPromotionConfig.setCateLevel(activityRule.getReduceSpecification().getCateLevel());
        //国补模式
        governmentSubsidyPromotionConfig.setSubsidyMode(activityRule.getReduceSpecification().getSubsidyMode());
        //国补上报城市ID
        governmentSubsidyPromotionConfig.setReportCity(activityRule.getReduceSpecification().getReportCity());
        //国补上报城市标签
        governmentSubsidyPromotionConfig.setReportTag(activityRule.getReduceSpecification().getReportTag());
        //国补开票规则
        governmentSubsidyPromotionConfig.setInvoiceRule(activityRule.getReduceSpecification().getInvoiceRule());
        //国补开票主体(深圳小米景明)
        governmentSubsidyPromotionConfig.setInvoiceCompanyId(activityRule.getReduceSpecification().getInvoiceCompanyId());
        //国补活动region
        governmentSubsidyPromotionConfig.setRegionList(activityRule.getReduceSpecification().getRegionList());
        // 活动领券方式
        governmentSubsidyPromotionConfig.setFetchType(activityRule.getReduceSpecification().getFetchType());
        // 使用指南
        governmentSubsidyPromotionConfig.setUsageGuide(activityRule.getReduceSpecification().getUsageGuide());
        // 使用指南图片url
        governmentSubsidyPromotionConfig.setUsageGuideImgUrl(activityRule.getReduceSpecification().getUsageGuideImgUrl());
        // 活动url
        governmentSubsidyPromotionConfig.setActivityUrl(activityRule.getReduceSpecification().getActivityUrl());

        Map<String, String> goodsSpuGroupMap = Maps.newHashMap();
        Map<String,GovernmentRule> governmentRuleMap = Maps.newHashMap();
        for (ProductPolicy product : activityConfig.getProductPolicyList()) {
            GovernmentSubsidyRule.ProductRule productRule = GsonUtil.fromJson(product.getRule(),
                    GovernmentSubsidyRule.ProductRule.class);
            goodsSpuGroupMap.put(product.getProductId().toString(), Optional.ofNullable(productRule.getCateCode()).orElse(""));
            GovernmentRule rule = new GovernmentRule();
            rule.setBrand(productRule.getBrand());
            rule.setItemName(productRule.getItemName());
            rule.setSpecModel(productRule.getSpecModel());
            rule.setBarcode(productRule.getBarcode());
            governmentRuleMap.put(product.getProductId().toString(),rule);
        }

        governmentSubsidyPromotionConfig.setGoodsSpuGroupMap(goodsSpuGroupMap);
        governmentSubsidyPromotionConfig.setGovernmentRuleMap(governmentRuleMap);

        List<String> selectOrgList = Lists.newArrayList();
        for (ActivityScope scope : activityConfig.getActivityScopeList()) {
            if (Objects.equals(ScopeTypeEnum.STORE_CODE.code, scope.getScopeType())) {
                selectOrgList.addAll(Lists.newArrayList(scope.getScopeValue().split(",")));
            }
        }
        governmentSubsidyPromotionConfig.setSelectOrgCodes(selectOrgList);
    }
}
