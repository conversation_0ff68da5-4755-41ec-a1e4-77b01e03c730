package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.entity.redis.GiftBargainGroup;
import com.xiaomi.nr.promotion.entity.redis.Goods;
import com.xiaomi.nr.promotion.entity.redis.SkuGroup;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyGiftPromotionConfig;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 买赠基数检查
 *
 * <AUTHOR>
 * @date 2021/9/27
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class BuyGiftNumCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 赠品信息
     */
    private Goods giftGoods;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (giftGoods == null || CollectionUtils.isEmpty(giftGoods.getSkuGroupList())) {
            return false;
        }
        Integer maxCount = context.getFillTimes();
        boolean anyMatch = false;
        for (SkuGroup skuGroup : giftGoods.getSkuGroupList()) {
            if (checkGroupNum(skuGroup, maxCount, request.getCartList())) {
                anyMatch = true;
            }
        }
        return anyMatch;
    }

    private boolean checkGroupNum(SkuGroup group, Integer maxCount, List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(group.getListInfo())) {
            return false;
        }
        Long groupId = group.getGroupId();
        boolean anyMatch = false;
        for (GiftBargainGroup giftGoods1 : group.getListInfo()) {
            if (checkGiftGoods(giftGoods1, groupId, maxCount, cartList)) {
                anyMatch = true;
            }
        }
        return anyMatch;
    }

    private boolean checkGiftGoods(GiftBargainGroup giftGoods, Long groupId, Integer maxCount, List<CartItem> cartList) {
        Integer limitNum = activityRedisDao.getActBuyGiftLimitNum(promotionId, String.valueOf(giftGoods.getSku()), groupId);
        if ((giftGoods.getGiftLimitNum() - limitNum) >= maxCount * giftGoods.getGiftBaseNum()) {
            return true;
        }
        // 处理是否存在有在购物车里但是没有库存的， 有就需要给删了，让重新选
        CartItem cartItem = cartList.stream()
                .filter(item -> SourceEnum.isGift(item.getSource()))
                .filter(item -> Objects.equals(String.valueOf(promotionId), item.getSourceCode()))
                .filter(item -> Objects.equals(String.valueOf(giftGoods.getSku()), item.getSku())).findAny().orElse(null);
        if (cartItem != null) {
            CartHelper.delGiftBargain(cartList, promotionId);
        }
        return false;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof BuyGiftPromotionConfig)) {
            log.error("config is not instanceof BuyGiftPromotionConfig. config:{}", config);
            return;
        }
        BuyGiftPromotionConfig promotionConfig = (BuyGiftPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.giftGoods = promotionConfig.getGiftGoods();
    }
}
