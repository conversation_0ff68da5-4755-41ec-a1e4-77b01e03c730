package com.xiaomi.nr.promotion.componet.action;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.constant.SourceConstant;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.OnsaleExtendInfo;
import com.xiaomi.nr.promotion.model.common.OnsaleJoin;
import com.xiaomi.nr.promotion.model.common.PromotionExtend;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.ChannelsHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.nr.promotion.util.SsuParamUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 直降活动: 计价和均摊动作
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class OnsaleAction extends AbstractOnsaleAction {


    /**
     * 执行减价
     *
     * @param promotion 优惠工具
     * @param request   请求参数
     * @param context   请求上下文，活动组件间
     * @throws BizError 业务异常
     */
    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {


        List<CartItem> cartList = request.getCartList();
        if (MapUtil.isEmpty(onsaleInfoMap)) {
            log.error("onsaleInfoMap is empty. actId:{} uid:{}", promotion, request.getUserId());
            return;
        }

        // 改价 (获取可以参加活动的购物车和参加的活动) key: skuPackage value:OnsaleJoin
        List<GoodsIndex> indexList = context.getGoodIndex();


        Map<String, OnsaleJoin> joinSkuMap = null;

        joinSkuMap = changePrice(cartList, indexList, request.getChannel());

        // 设置结果
        setResult(context, cartList, promotion, joinSkuMap);

        // 初始化资源
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            for (OnsaleJoin onsaleJoin : joinSkuMap.values()) {
                String skuPackage = String.valueOf(onsaleJoin.getSkuOrPackage());
                initOnsaleResource(request, promotionId, skuPackage, onsaleJoin.getJoinCounts(), context, numLimitRule);
            }
        }
    }


    protected void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool, Map<String, OnsaleJoin> joinSkuMap) throws BizError {
        List<GoodsIndex> goodsIndices = context.getGoodIndex();
        List<String> parentItemList = CartHelper.getParentItemIdList(goodsIndices);
        List<String> joinedItemIdList = CartHelper.getJoinedItemIdList(goodsIndices, cartList);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setJoinedItemId(joinedItemIdList);
        promotionInfo.setParentItemId(parentItemList);
        promotionInfo.setExtend(generateActExpandInfo(joinSkuMap));
        promotionInfo.setJoinCounts(0);
        promotionInfo.setJoined(CollectionUtils.isNotEmpty(joinedItemIdList) ? 1 : 0);

        // 设置上下文结果
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    private Map<String, OnsaleJoin> changePrice(List<CartItem> cartList, List<GoodsIndex> indexList, Integer channel) throws BizError {
        final Map<String, OnsaleJoin> joinSkuMap = new HashMap<>();
        for (CartItem item : cartList) {
            doChangeCartPrice(joinSkuMap, item, indexList, channel);
        }
        return joinSkuMap;
    }


    private void doChangeBindMainAccessoryPrice(CartItem item) {
        long finalPrice = 0L;
        boolean isExistChildAct = false;
        for (int index = 0; index < item.getChilds().size(); index++) {
            CartItemChild child = item.getChilds().get(index);
            ActPriceInfo actPriceInfo = onsaleInfoMap.get(child.getSku());
            if (actPriceInfo != null && actPriceInfo.getPrice() < child.getSellPrice()) {
                isExistChildAct = true;
                finalPrice += actPriceInfo.getPrice();
                // 记录子品参加的活动ID，用于
                item.getOnSalePromotionIdMap().put(index, promotionId.toString());
                // 修改子品的价格
                child.setSellPrice(actPriceInfo.getPrice());
                child.setLowerPrice(actPriceInfo.getPrice());
                child.setOnsaleReduce(child.getOriginalSellPrice() - child.getLowerPrice());
                // 记录优惠
                List<ReduceDetailItem> reduceItemList = Optional.ofNullable(item.getReduceItemList()).orElse(Lists.newArrayList());
                reduceItemList.removeIf(reduceItem -> reduceItem.getSsuId().equals(child.getSsuId()));
                ReduceDetailItem detailItem = initReduceDetailItem(promotionId, promotionType, child.getSsuId(),
                        child.getOriginalSellPrice(), child.getSellPrice(), item.getCount());
                reduceItemList.add(detailItem);
            } else {
                finalPrice += child.getSellPrice();
            }
        }
        // 如果子品走了直降价，那么修改主品的价格
        if (isExistChildAct) {
            item.setCartPrice(finalPrice);
            item.setOnsaleReduce(item.getOriginalCartPrice() - item.getCartPrice());
            item.setJoinOnsale(true);
            item.setChangePriceActType(ActivityTypeEnum.ONSALE.getValue());
        }
    }

    private void  doChangeCartPrice(Map<String, OnsaleJoin> joinSkuMap, CartItem item, List<GoodsIndex> goodIndex, Integer channel) throws BizError {
        // 判断是否可以参加直降
        boolean canJoin = canJoinOnSaleAct(item, goodIndex);
        if (!canJoin) {
            return;
        }
        // 主附品强绑定的改价策略
        if (ChannelsHelper.isBindMainAccessory(channel, item.getDepartment(), item.getBindMainAccessory())) {
            doChangeBindMainAccessoryPrice(item);
            return;
        }

        // 获取可以参加的直降活动(onSaleInfo == nil代表没获取到)
        ActPriceInfo onSaleInfo = onsaleInfoMap.get(CartHelper.getSkuPackageForOnsale(item));
        if (onSaleInfo == null) {
            log.error("onSaleInfo is null. cart:{}", item);
            return;
        }

        // 直降后的价格不能小于等于0
        Long lowerPrice = onSaleInfo.getPrice();
        if (lowerPrice <= 0L) {
            log.warn("onSaleInfo lowerPrice <= 0, onSaleInfo:{} cart:{} ", onSaleInfo, item);
            return;
        }
        // 当前直降幅度小于上次的,不改价，给用户最优的价格
        if (item.isJoinOnsale() && item.getCartPrice() <= lowerPrice) {
            return;
        }

        // 记录原来价格
        Long originalPrice = item.getCartPrice();
        // 记录套装原价
        Map<Integer, Long> childOriPriceMap = new HashMap<>(item.getChilds().size());
        for (int idx = 0; idx < item.getChilds().size(); idx++) {
            childOriPriceMap.put(idx, item.getChilds().get(idx).getSellPrice());
        }
        // 改价（单品）
        if (StringUtils.isEmpty(item.getPackageId())) {
            doChangeSkuCartPrice(item, onSaleInfo);
            //改价(套装)
        } else if (StringUtils.isNotEmpty(item.getPackageId())) {
            doChangePackageCartPrice(item, onSaleInfo);
        }
        item.setJoinOnsale(true);
        item.setSourceCode(promotionId.toString());
        item.setChangePriceActType(ActivityTypeEnum.ONSALE.getValue());
        // 记录优惠
        applyPromotionDiscount(item, childOriPriceMap);
        // 生成直降活动 extend结构,主要做直降计数用
        updateOnSaleJoinSkuMap(joinSkuMap, item, onSaleInfo);
    }

    private void applyPromotionDiscount(CartItem item, Map<Integer, Long> childOriPriceMap) {
        // 记录优惠
        if (item.getSsuId() != null && item.getSsuId() != 0 && !SsuParamUtil.isNewPackage(item.getSsuType())) {
            Optional.ofNullable(item.getReduceItemList()).orElse(Collections.emptyList())
                    .removeIf(reduceItem -> Objects.equals(reduceItem.getPromotionType(), promotionType.getTypeId()));
            List<ReduceDetailItem> reduceItemList = Optional.ofNullable(item.getReduceItemList()).orElse(Lists.newArrayList());
            if (org.apache.dubbo.common.utils.CollectionUtils.isEmpty(item.getChilds())) {
                ReduceDetailItem detailItem  = initReduceDetailItem(promotionId, promotionType, item.getSsuId(),
                        item.getOriginalCartPrice(), item.getCartPrice(), item.getCount());
                reduceItemList.add(detailItem);
            } else {
                childOriPriceMap.forEach((idx, originalChildPrice) -> {
                    CartItemChild child = item.getChilds().get(idx);
                    ReduceDetailItem detailItem  = initReduceDetailItem(promotionId, promotionType, child.getSsuId(),
                            originalChildPrice, child.getSellPrice(), item.getCount());
                    reduceItemList.add(detailItem);
                });
            }
        }
    }

    /**
     * 更新直降活动参与Map
     *
     * @param joinSkuMap map
     * @param item       购物车项
     * @param onSaleInfo 直降信息
     */
    private void updateOnSaleJoinSkuMap(Map<String, OnsaleJoin> joinSkuMap, CartItem item, ActPriceInfo onSaleInfo) {
        String key = CartHelper.getSkuPackageForOnsale(item);
        OnsaleJoin onSale = joinSkuMap.get(key);
        if (onSale != null) {
            int newCount = onSale.getJoinCounts() + item.getCount();
            onSale.setJoinCounts(newCount);
            return;
        }

        OnsaleJoin onSaleJoin = new OnsaleJoin();
        onSaleJoin.setJoinCounts(item.getCount());
        onSaleJoin.setLimitRule(onSaleInfo.getLimitRule());
        onSaleJoin.setSkuOrPackage(onSaleInfo.getSkuPackage());
        joinSkuMap.put(key, onSaleJoin);
    }

    /**
     * 生成活动拓展信息
     *
     * @param onSaleJoinSkuMap 直降商品列表
     * @return 拓展信息Json字符串
     */
    private String generateActExpandInfo(Map<String, OnsaleJoin> onSaleJoinSkuMap) {
        List<OnsaleJoin> onSaleJoinList = new ArrayList<>(onSaleJoinSkuMap.values());

        OnsaleExtendInfo onSaleExtend = new OnsaleExtendInfo();
        onSaleExtend.setJoinExtend(onSaleJoinList);
        PromotionExtend extend = new PromotionExtend();
        extend.setOnsaleExtend(onSaleExtend);
        return GsonUtil.toJson(extend);
    }

    /**
     * 判断购物车是否可以参加直降
     *
     * @param cartItem     购物车项
     * @param canJoinItems 能参加活动的项
     * @return true/false
     */
    protected boolean canJoinOnSaleAct(CartItem cartItem, List<GoodsIndex> canJoinItems) {
        if (!CartHelper.filterOrderOk(cartItem)) {
            log.warn("inner.invalidCalculate");
            return false;
        }

        if (CollectionUtils.isEmpty(canJoinItems)) {
            return false;
        }
        // 任意匹配上就是匹配上了
        return canJoinItems.stream().anyMatch(item -> item.getItemId().equals(cartItem.getItemId()));
    }

    /**
     * 更改单品的价格
     *
     * @param cartItem   购物车项
     * @param onSaleInfo 直降信息
     */
    private void doChangeSkuCartPrice(CartItem cartItem, ActPriceInfo onSaleInfo) throws BizError {
        log.info("cart doChangeSkuCartPrice. cart:{}", cartItem);
        long lowerPrice = onSaleInfo.getPrice();
        long onSaleBookingPrice = cartItem.getOnSaleBookingPrice();
        List<String> saleSources = cartItem.getSaleSources();
        if (!saleSources.contains(SourceConstant.SOURCE_ON_SALE_BOOKING)) {
            // 直降价
            cartItem.setCartPrice(lowerPrice);
            // 直降优惠的价格
            cartItem.setOnsaleReduce(cartItem.getOriginalCartPrice() - lowerPrice);
            return;
        }
        long tempCartPrice = lowerPrice - onSaleBookingPrice;
        if (tempCartPrice <= 0) {
            log.warn("tempCartPrice <= 0 cartItem:{} lowerPrice:{}, onSaleBookingPrice:{}", cartItem.getItemId(), lowerPrice, onSaleBookingPrice);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "尾款支付金额小于等于0元, lowerPrice:" + lowerPrice + ", OnSaleBookingPrice:" + onSaleBookingPrice);
        }
        cartItem.setCartPrice(tempCartPrice);
    }

    /**
     * 更改套装的价格
     *
     * @param cartItem   购物车项
     * @param onSaleInfo 直降信息
     */
    private void doChangePackageCartPrice(CartItem cartItem, ActPriceInfo onSaleInfo) throws BizError {
        log.info("cart doChangePackageCartPrice. cart:{}", cartItem);
        long lowerPrice = onSaleInfo.getPrice();
        long onSaleBookingPrice = cartItem.getOnSaleBookingPrice();
        long tempCartPrice = lowerPrice - onSaleBookingPrice;
        if (tempCartPrice <= 0) {
            log.warn("tempCartPrice <= 0 cartItem:{} lowerPrice:{}, onSaleBookingPrice:{}", cartItem.getItemId(), lowerPrice, onSaleBookingPrice);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "尾款支付金额小于等于0元, lowerPrice:" + lowerPrice + ", OnSaleBookingPrice:" + onSaleBookingPrice);
        }
        cartItem.setCartPrice(tempCartPrice);

        // 直降优惠的价格
        cartItem.setOnsaleReduce(cartItem.getOriginalCartPrice() - tempCartPrice);
        // 获得套装子商品的直降价格 key：sku  value: lowerPrice
        List<String> saleSourceS = cartItem.getSaleSources();
        if (saleSourceS.contains(SourceConstant.SOURCE_ON_SALE_BOOKING)) {
            List<CartItemChild> children = cartItem.getChilds();
            Map<String, Long> skuLowerPriceMap = CartHelper.getChildPriceMap(cartItem, onSaleInfo.getChildPriceList());
            // sellPrice改价
            calBookingChildSellPrice(children, skuLowerPriceMap);
            // 计算定金金额
            calPackageReducePrice(cartItem);
            //重新计算sellPrice和lowerPrice
            reCalBookingChildPrice(children);
        } else {
            // 修改套装价和分组价格
            cartItem.setCartPrice(lowerPrice);
            // 新套装 ssuType=1，走新分摊逻辑
            if (SsuParamUtil.isNewPackage(cartItem.getSsuType())) {
                calNewPackageChildPrice(cartItem, onSaleInfo.getPrice());
            } else {
                calChildPrice(cartItem, onSaleInfo.getChildGroupPriceMap());
            }

        }
    }

    /**
     * 计算套装子品直降价：前n-1个子品按比例向下取整，最后一个子品用总直降减前N-1个子品的直降
     *
     * @param cartItem
     * @param onSalePrice
     */
    private void calNewPackageChildPrice(CartItem cartItem, long onSalePrice) {
        long totalSellPrice = CartHelper.getPackageSellPrice(cartItem);

        List<CartItemChild> childList = cartItem.getChilds().stream()
                .sorted(Comparator.comparingLong(CartItemChild::getSellPrice).reversed()).toList();

        long totalChildOnSalePrice = 0;
        for (int index = 0; index < childList.size() - 1; index++) {
            CartItemChild child = childList.get(index);
            long childOnSalePrice = Math.floorDiv(onSalePrice * child.getSellPrice(), totalSellPrice);
            child.setSellPrice(childOnSalePrice);
            child.setLowerPrice(childOnSalePrice);
            child.setOnsaleReduce(child.getOriginalSellPrice() - childOnSalePrice);
            totalChildOnSalePrice += childOnSalePrice;
        }

        CartItemChild lastChild = childList.getLast();
        long lastChildOnSalePrice = onSalePrice - totalChildOnSalePrice;

        if (lastChildOnSalePrice >= lastChild.getOriginalSellPrice()) {
            // 更新最后一个商品的价格不变
            lastChild.setSellPrice(lastChild.getOriginalSellPrice());
            lastChild.setLowerPrice(lastChild.getOriginalSellPrice());
            lastChild.setOnsaleReduce(0L);
            // 剩余分摊到第一个商品上
            CartItemChild firstChild = childList.getFirst();
            long firstChildSellPrice = firstChild.getSellPrice() + lastChildOnSalePrice - lastChild.getOriginalSellPrice();
            firstChild.setSellPrice(firstChildSellPrice);
            firstChild.setLowerPrice(firstChildSellPrice);
            firstChild.setOnsaleReduce(firstChild.getOriginalSellPrice() - firstChildSellPrice);
        } else {
            lastChild.setSellPrice(lastChildOnSalePrice);
            lastChild.setLowerPrice(lastChildOnSalePrice);
            lastChild.setOnsaleReduce(lastChild.getOriginalSellPrice() - lastChildOnSalePrice);
        }
    }

    /**
     * 更新套装内子商品的售价和直降价
     *
     * @param cartItem 购物车项
     */
    private void calChildPrice(CartItem cartItem, Map<Integer, Long> childGroupPriceMap) {
        if (CollectionUtils.isEmpty(cartItem.getChilds())) {
            return;
        }
        boolean allContainGroupId = cartItem.getChilds().stream().allMatch(item -> Objects.nonNull(item.getGroupId()));
        // 符合走分组价格情况
        if (MapUtil.isNotEmpty(childGroupPriceMap) && allContainGroupId) {
            for (CartItemChild child : cartItem.getChilds()) {
                Long lowerPrice = childGroupPriceMap.get(child.getGroupId());
                child.setSellPrice(lowerPrice);
                child.setLowerPrice(lowerPrice);
                child.setOnsaleReduce(child.getOriginalSellPrice() - child.getLowerPrice());
            }
            return;
        }
        // 计算按旧逻辑
        long totalSellPrice = CartHelper.getPackageSellPrice(cartItem);
        long cartPrice = cartItem.getCartPrice();
        long actualCartPrice = 0L;
        // 孩子的直降价是按sellPrice比例做分摊
        for (CartItemChild child : cartItem.getChilds()) {
            long childPrice = Math.floorDiv(cartPrice * child.getSellPrice(), totalSellPrice);
            child.setSellPrice(childPrice);
            child.setLowerPrice(childPrice);
            child.setOnsaleReduce(child.getOriginalSellPrice() - child.getLowerPrice());
            actualCartPrice += childPrice;
        }
        long priceDiff = cartPrice - actualCartPrice;
        if (priceDiff == 0L) {
            return;
        }

        // 有分摊余额, 一分钱一分钱分摊
        long moneyDiff = 1;
        for (CartItemChild child : cartItem.getChilds()) {
            if (priceDiff <= 0) {
                return;
            }
            if (child.getLowerPrice() >= child.getOriginalSellPrice()) {
                continue;
            }
            child.setSellPrice(child.getSellPrice() + moneyDiff);
            child.setLowerPrice(child.getLowerPrice() + moneyDiff);
            child.setOnsaleReduce(child.getOriginalSellPrice() - child.getLowerPrice());
            priceDiff -= moneyDiff;
        }
    }

    /**
     * 更新套装子商品售价
     *
     * @param children         套装子商品
     * @param skuLowerPriceMap 直降价格Map
     */
    private void calBookingChildSellPrice(List<CartItemChild> children, Map<String, Long> skuLowerPriceMap) {
        if (CollectionUtils.isEmpty(children) || MapUtil.isEmpty(skuLowerPriceMap)) {
            return;
        }
        for (CartItemChild child : children) {
            Long childPrice = skuLowerPriceMap.get(child.getSku());
            if (childPrice != null && childPrice > 0) {
                child.setSellPrice(childPrice);
            }
        }
    }

    /**
     * 重新计算套装子商品售价
     *
     * @param children 套装子商品
     */
    private void reCalBookingChildPrice(List<CartItemChild> children) throws BizError {
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        for (CartItemChild child : children) {
            long tempSellPrice = child.getSellPrice() - child.getOnSaleBookingPrice();
            if (tempSellPrice < 0) {
                throw ExceptionHelper.create(GeneralCodes.InternalError, "[carts.ChangeCartPriceForOnSale], 直降预售尾款支付金额小于等于0元, sellPrice:" + child.getSellPrice() + ", OnSaleBookingPrice:" + child.getOnSaleBookingPrice());
            }
            child.setSellPrice(tempSellPrice);
            child.setLowerPrice(tempSellPrice);
        }
    }

    /**
     * 是否是直降活动定金预售模式
     *
     * @param cartItem 购物车项
     */
    private void calPackageReducePrice(CartItem cartItem) {
        Long packageSellPrice = calPackageSellPrice(cartItem);
        // 直降item数量是1，都是打平的
        Long curShouldReduce = cartItem.getOnSaleBookingPrice() / cartItem.getCount();
        List<String> saleSources = cartItem.getSaleSources();
        if (saleSources.contains(SourceConstant.SOURCE_ON_SALE_BOOKING)) {
            List<CartItemChild> children = cartItem.getChilds();
            for (CartItemChild child : children) {
                long childBookingPrice = curShouldReduce * child.getSellPrice() / packageSellPrice;
                child.setOnSaleBookingPrice(childBookingPrice);
            }
        }
    }

    /**
     * 获取套装总价 不是套装的购物车价格 用来分摊求比例
     *
     * @param cartItem 购物车项
     * @return 套装SellPrice
     */
    private Long calPackageSellPrice(CartItem cartItem) {
        List<CartItemChild> children = cartItem.getChilds();
        Long price = 0L;
        for (CartItemChild child : children) {
            price += child.getSellPrice();
        }
        return price;
    }

    @Override
    public String getJoinGoods(CartItem cartItem) {
        return null;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        super.loadConfig(config);
    }
}