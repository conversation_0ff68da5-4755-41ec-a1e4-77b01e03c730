

package com.xiaomi.nr.promotion.model.promotionconfig.loader;

import com.xiaomi.nr.md.promotion.admin.api.constant.ScopeTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityScope;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.B2tVipDiscountRule;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.DiscountRate;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.B2tVipDiscountPromotionConfig;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 团购折扣数据加载
 *
 * <AUTHOR>
 * @date 2023/2/9
 */
@Slf4j
@Component
public class B2tVipDiscountPromotionConfigLoader implements PromotionConfigLoader {

    @Override
    public boolean support(AbstractPromotionConfig promotionConfig) {
        if (promotionConfig instanceof B2tVipDiscountPromotionConfig) {
            return true;
        }
        return false;
    }

    /**
     * 加载数据配置
     *
     * @param promotionConfig 配置对象
     * @param activityConfig  活动缓存
     * @throws BizError 业务异常
     */
    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityConfig activityConfig) throws BizError {
        if (activityConfig == null || promotionConfig == null) {
            log.error("[BtcVipDiscountPromotionConfigLoader] activityConfig is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "activityConfig is null");
        }
        // 商品所属部门
        ActivityScope departmentScope = activityConfig.getActivityScopeList().stream()
                .filter(scope -> Objects.equals(ScopeTypeEnum.PRODUCT_DEPARTMENT.code, scope.getScopeType()))
                .findAny().orElse(null);
        Integer department = departmentScope != null ? Integer.valueOf(departmentScope.getScopeValue()) : null;

        // 折扣信息
        com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.B2tVipDiscountRule b2tVipDiscountRule = GsonUtil.fromJson(activityConfig.getRule(), com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.B2tVipDiscountRule.class);
        if (b2tVipDiscountRule == null) {
            log.error("[BtcVipDiscountPromotionConfigLoader] b2tVipDiscountRule is null. {}", activityConfig);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "b2tVipDiscountRule is null");
        }
        B2tVipDiscountPromotionConfig discountPromotionConfig = (B2tVipDiscountPromotionConfig) promotionConfig;


        com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.B2tVipDiscountRule.DiscountRate vipDiscountRate = b2tVipDiscountRule.getDiscountRate();
        if (vipDiscountRate != null) {

            DiscountRate discountRate = new DiscountRate();
            discountRate.setNormal(vipDiscountRate.getNormal());
            discountRate.setVip(vipDiscountRate.getVip());
            discountRate.setSvip(vipDiscountRate.getSvip());
            discountPromotionConfig.setDiscountRate(discountRate);
        }

        com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.B2tVipDiscountRule.GoodsDiscountRate mdGoodsRate = b2tVipDiscountRule.getGoodsDiscountRate();
        if (mdGoodsRate != null) {
            B2tVipDiscountRule.GoodsDiscountRate goodsDiscountRate = new B2tVipDiscountRule.GoodsDiscountRate();
            goodsDiscountRate.setNormalRate(mdGoodsRate.getNormalRate());
            goodsDiscountRate.setRecomRate(mdGoodsRate.getRecomRate());
            discountPromotionConfig.setGoodsDiscountRate(goodsDiscountRate);
        }

        discountPromotionConfig.setDepartment(department);
    }
}
