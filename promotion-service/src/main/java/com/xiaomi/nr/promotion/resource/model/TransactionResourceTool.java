package com.xiaomi.nr.promotion.resource.model;

import com.xiaomi.nr.promotion.resource.ResourceProvider;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 资源工具
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
@Slf4j
public class TransactionResourceTool {

    private final static List<Integer> ORIGIN_LIST;

    static {
        ORIGIN_LIST = new ArrayList<>(900);
        final int startRes = 100;
        final int endRes = 1000;
        for (int i = startRes; i < endRes; i++) {
            ORIGIN_LIST.add(i);
        }
    }


    /**
     * 合并资源对象
     * 对于有相同 promotion id , pid ,suffix 的库存资源进行合并
     * 生成新的资源对象，库存扣减数量为合并对象的总和
     * 此情景通常在处理赠品库存资源对象时
     * 其他情况无合并
     *
     * @return 提供者列表
     */
    public static List<ResourceProvider<?>> mergeResourceProviders(List<ResourceProvider<?>> resources) {



        return resources;
    }

    /**
     * 根据订单id和数量生成不重复的detail表的主键id（考虑预售的因素）
     *
     * @param orderId 订单ID
     * @param size    数量
     * @return 主键id
     */
    public static List<Long> generatePrimaryKeys(long orderId, int size) {
        List<Long> list = new ArrayList<>(size);
        List<Integer> allList = new ArrayList<>(ORIGIN_LIST);
        Collections.shuffle(allList, ThreadLocalRandom.current());
        for (int i = 0; i < size; i++) {
            list.add(orderId * 1000 + allList.get(i));
        }
        return list;
    }
}