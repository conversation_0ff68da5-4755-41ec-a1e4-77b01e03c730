package com.xiaomi.nr.promotion.entity.redis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 直降政策 onsaleItem类型字段
 *
 * <AUTHOR>
 * @date 2021/3/26
 * @see OnsaleInfo
 */
@Data
@ToString
public class OnsaleInfo implements Serializable {
    private static final long serialVersionUID = 355449167249845669L;
    /**
     * sku/套装id
     */
    @SerializedName("sku_package")
    private Long skuPackage = 0L;
    /**
     * sku或package
     */
    private String level = "";
    /**
     * 直降后的价格（单位分）
     */
    @SerializedName("lower_price")
    private Long lowerPrice = 0L;
    /**
     * 套装内的sku直降列表
     */
    @SerializedName("lower_price_list")
    private List<List<BatchInfo>> lowPriceList;
    /**
     * 是否有限制规则  0-否 1-是
     */
    @SerializedName("is_limit")
    private Integer isLimit = 0;
    /**
     * 限制规则，如果是0则代表不限制（如果没有限制，则没有此字段）
     */
    @SerializedName("limit_rule")
    private LimitRule limitRule;

    /**
     * 直降套装分组列表 KYE：组ID，value: 直降后金额(分）
     */
    @SerializedName("lower_price_groups")
    private Map<Integer, Long> lowerPriceGroups;
}
