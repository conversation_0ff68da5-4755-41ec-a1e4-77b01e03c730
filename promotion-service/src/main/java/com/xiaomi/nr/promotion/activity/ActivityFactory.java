package com.xiaomi.nr.promotion.activity;

import com.xiaomi.nr.promotion.activity.carmaintenance.MaintenanceDiscountActivity;
import com.xiaomi.nr.promotion.activity.carmaintenance.MaintenanceItemFreeActivity;
import com.xiaomi.nr.promotion.activity.carshop.CarShopBuyReduceActivity;
import com.xiaomi.nr.promotion.activity.carsale.CarExchangeSubsidyActivity;
import com.xiaomi.nr.promotion.activity.carsale.CarOnsaleActivity;
import com.xiaomi.nr.promotion.activity.carsale.CarOrderReduceActivity;
import com.xiaomi.nr.promotion.activity.carsale.CarRangeReduceActivity;
import com.xiaomi.nr.promotion.activity.carshop.CarShopBuyGiftActivity;
import com.xiaomi.nr.promotion.activity.carshop.CarShopOnsaleActivity;
import com.xiaomi.nr.promotion.activity.carshop.CarShopPostFreeActivity;
import com.xiaomi.nr.promotion.activity.carshop.CarShopVipDiscountActivity;
import com.xiaomi.nr.promotion.engine.dsl.DSLGeneralPromotion;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 活动工厂
 *
 * <AUTHOR>
 * @date 2021/3/15
 */
@Component
public class ActivityFactory {
    @Autowired
    private ObjectFactory<OnsaleActivity> onsaleActivityObjectFactory;

    @Autowired
    private ObjectFactory<CarOnsaleActivity> carOnsaleActivityObjectFactory;

    @Autowired
    private ObjectFactory<CarShopOnsaleActivity> carShopOnsaleActivityObjectFactory;
    @Autowired
    private ObjectFactory<CarRangeReduceActivity> carRangeReduceActivityObjectFactory;
    @Autowired
    private ObjectFactory<CarExchangeSubsidyActivity> carExchangeSubsidyActivityObjectFactory;
    @Autowired
    private ObjectFactory<CarOrderReduceActivity> carOrderReduceActivityObjectFactory;
    @Autowired
    private ObjectFactory<GiftActivity> giftActivityObjectFactory;
    @Autowired
    private ObjectFactory<BargainActivity> bargainActivityObjectFactory;
    @Autowired
    private ObjectFactory<PostFreeActivity> postFreeActivityObjectFactory;
    @Autowired
    private ObjectFactory<ReduceActivity> reduceActivityObjectFactory;
    @Autowired
    private ObjectFactory<DiscountActivity> discountActivityObjectFactory;
    @Autowired
    private ObjectFactory<BuyGiftActivity> buyGiftActivityObjectFactory;
    @Autowired
    private ObjectFactory<CarShopBuyGiftActivity> carShopBuyGiftActivityObjectFactory;
    @Autowired
    private ObjectFactory<StorePriceActivity> storePriceActivityObjectFactory;
    @Autowired
    private ObjectFactory<RenewReduceActivity> renewReduceActivityObjectFactory;
    @Autowired
    private ObjectFactory<BuyReduceActivity> buyReduceActivityObjectFactory;
    @Autowired
    private ObjectFactory<PartOnsaleActivity> partOnsaleActivityObjectFactory;
    @Autowired
    private ObjectFactory<CrmChannelPriceActivity> crmChannelPriceActivityObjectFactory;
    @Autowired
    private ObjectFactory<StepPriceActivity> stepPricePromotionConfigObjectFactory;
    @Autowired
    private ObjectFactory<B2tVipDiscountActivity> b2tVipDiscountActivityObjectFactory;
    @Autowired
    private ObjectFactory<NewPurchaseSubsidyActivity> newPurchaseSubsidyActivityObjectFactory;
    @Autowired
    private ObjectFactory<UpgradePurchaseSubsidyActivity> upgradePurchaseSubsidyActivityObjectFactory;

    @Autowired
    private ObjectFactory<CarShopVipDiscountActivity> carShopVipDiscountActivityObjectFactory;

    @Autowired
    private ObjectFactory<MaintenanceDiscountActivity> maintenanceDiscountActivityObjectFactory;

    @Autowired
    private ObjectFactory<MaintenanceItemFreeActivity> maintenanceItemFreeActivityObjectFactory;

    @Autowired
    private ObjectFactory<CarShopPostFreeActivity> carShopPostFreeActivityObjectFactory;

    @Autowired
    private ObjectFactory<CarShopBuyReduceActivity> carBuyReduceActivityObjectFactory;


    /**
     * 获取优惠组件
     *
     * @param type 促销类型
     * @return 获取优惠组件
     * @throws BizError 错误异常
     */
    public DSLGeneralPromotion getByTypeAndBiz(PromotionToolType type, BizPlatformEnum bizPlatform) throws BizError {
        switch (type) {
            case ONSALE:
                return getOnsalePromotion(bizPlatform);
            case GIFT:
                return giftActivityObjectFactory.getObject();
            case BARGAIN:
                return bargainActivityObjectFactory.getObject();
            case POST_FREE:
                return getPostFreePromotion(bizPlatform);
            case REDUCE:
                return reduceActivityObjectFactory.getObject();
            case DISCOUNT:
                return discountActivityObjectFactory.getObject();
            case BUY_GIFT:
                return getBuyGiftPromotion(bizPlatform);
            case STORE_PRICE:
                return storePriceActivityObjectFactory.getObject();
            case RENEW_REDUCE:
                return renewReduceActivityObjectFactory.getObject();
            case BUY_REDUCE:
                return getBuyReducePromotion(bizPlatform);
            case PARTONSALE:
                return partOnsaleActivityObjectFactory.getObject();
            case B2T_CHANNEL_PRICE:
                return crmChannelPriceActivityObjectFactory.getObject();
            case B2T_STEP_PRICE:
                return stepPricePromotionConfigObjectFactory.getObject();
            case B2T_VIP_DISCOUNT:
                return b2tVipDiscountActivityObjectFactory.getObject();
            case RANGE_REDUCE:
                return carRangeReduceActivityObjectFactory.getObject();
            case EXCHANGE_SUBSIDY:
                return carExchangeSubsidyActivityObjectFactory.getObject();
            case ORDER_REDUCE:
                return carOrderReduceActivityObjectFactory.getObject();
            case NEW_PURCHASE_SUBSIDY:
                return newPurchaseSubsidyActivityObjectFactory.getObject();
            case UPGRADE_PURCHASE_SUBSIDY:
                return upgradePurchaseSubsidyActivityObjectFactory.getObject();
            case CAR_SHOP_VIP:
                return carShopVipDiscountActivityObjectFactory.getObject();
            case MAINTENANCE_REPAIR_DISCOUNT:
                return maintenanceDiscountActivityObjectFactory.getObject();
            case MAINTENANCE_ITEM_FREE:
                return maintenanceItemFreeActivityObjectFactory.getObject();
            default:
                throw ExceptionHelper.create(GeneralCodes.NotFound, String.format("activity %s not implement", type));
        }
    }

    private DSLGeneralPromotion getPostFreePromotion(BizPlatformEnum bizPlatformEnum) {
        if (bizPlatformEnum == BizPlatformEnum.CAR_SHOP) {
            return carShopPostFreeActivityObjectFactory.getObject();
        } else {
            return postFreeActivityObjectFactory.getObject();
        }
    }

    private DSLGeneralPromotion getOnsalePromotion(BizPlatformEnum bizPlatformEnum) {
        if (bizPlatformEnum == BizPlatformEnum.CAR_SHOP) {
            return carShopOnsaleActivityObjectFactory.getObject();
        } else if (bizPlatformEnum == BizPlatformEnum.CAR) {
            return carOnsaleActivityObjectFactory.getObject();
        } else {
            return onsaleActivityObjectFactory.getObject();
        }
    }

    private DSLGeneralPromotion getBuyGiftPromotion(BizPlatformEnum bizPlatformEnum) {
        if (bizPlatformEnum == BizPlatformEnum.CAR_SHOP) {
            return carShopBuyGiftActivityObjectFactory.getObject();
        } else {
            return buyGiftActivityObjectFactory.getObject();
        }
    }

    private DSLGeneralPromotion getBuyReducePromotion(BizPlatformEnum bizPlatformEnum) {
        if (bizPlatformEnum == BizPlatformEnum.CAR_SHOP) {
            return carBuyReduceActivityObjectFactory.getObject();
        } else {
            return buyReduceActivityObjectFactory.getObject();
        }
    }
}
