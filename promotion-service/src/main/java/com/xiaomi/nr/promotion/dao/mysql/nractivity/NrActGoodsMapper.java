package com.xiaomi.nr.promotion.dao.mysql.nractivity;

import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.NrActGoodsPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： Pancras
 * @date： 2024/9/19
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Mapper
@Component
public interface NrActGoodsMapper {
    
    @Select(" <script> "
            + "select id, activity_id, product_id_type, product_id, status, `group`, rule " +
            " from nr_act_goods " +
            " where status=1 and activity_id in " +
            " <foreach collection='actIds' item='item' separator=',' open='(' close=')'> "
            + " #{item} "
            + "</foreach> "
            + " </script>")
    List<NrActGoodsPo> queryListByType(@Param("actIds") List<Long> ids);
}
