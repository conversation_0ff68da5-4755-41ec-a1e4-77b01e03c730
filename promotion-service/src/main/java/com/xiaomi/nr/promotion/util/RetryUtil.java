package com.xiaomi.nr.promotion.util;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;

/**
 *
 *
 * @author: zhangliwei6
 * @date: 2025/3/4 14:32
 * @description:
 */
@Slf4j
public class RetryUtil {

    /*
     * 重试执行一个方法
     * @param callable 需要执行的方法
     * @param retryTimes 重试次数
     * @throws Exception 如果所有重试都失败，抛出最后一次的异常
     */
    public static <T> T retryableCall(Callable<T> callable, int retryTimes) throws Exception {
        Exception lastException = null;
        // 执行次数=重试次数+1
        for (int i = 0; i < retryTimes + 1; i++) {
            try {
                return callable.call();
            } catch (Exception e) {
                lastException = e;
                log.info("function call failed, try times-{}, error-{}", i + 1, e.getMessage());
            }
        }
        if (lastException == null) {
            lastException = new RuntimeException("unknown error");
        }
        throw lastException;
    }

    /*
     * 重试执行一个方法
     * @param runnable 需要执行的方法
     * @param retryTimes 重试次数
     * @throws Exception 如果所有重试都失败，抛出最后一次的异常
     */
    public static void retryableRun(Runnable runnable, int retryTimes) throws Exception {
        Exception lastException = null;
        // 执行次数=重试次数+1
        for (int i = 0; i < retryTimes + 1; i++) {
            try {
                runnable.run();
                return;
            } catch (Exception e) {
                lastException = e;
                log.info("function run failed, try times-{}, error-{}", i + 1, e.getMessage());
            }
        }
        if (lastException == null) {
            lastException = new RuntimeException("unknown error");
        }
        throw lastException;
    }
}
