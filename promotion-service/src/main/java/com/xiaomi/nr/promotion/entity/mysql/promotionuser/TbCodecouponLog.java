package com.xiaomi.nr.promotion.entity.mysql.promotionuser;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 旧的有码优惠券日志
 *
 * <AUTHOR>
 * @date 2021/4/22
 */
@Data
public class TbCodecouponLog {
    /**
     * 索引(主键) 	#码的MD5值1
     */
    private String couponIndex;

    /**
     * 日志类型(主键) 不写
     */
    private Integer logType;

    /**
     * 实际抵用金额 	#金额/原来的相反数2
     */
    private BigDecimal replaceMoney;

    /**
     * 实际减免邮费 	#金额/原来的相反数3
     */
    private BigDecimal reduceExpress;

    /**
     * 老剩余次数 不写
     */
    private Long oldResidueCount;

    /**
     * 老剩余金额 不写
     */
    private BigDecimal oldResidueMoney;

    /**
     * 新剩余次数 不写
     */
    private Long newResidueCount;

    /**
     * 新剩余金额 不写
     */
    private BigDecimal newResidueMoney;

    /**
     * 线下使用 		#金额/不变4
     */
    private Integer offline;

    /**
     * 订单编号 		#订单/不变5
     */
    private Long orderId;

    /**
     * 用户编号 		#用户/不变6
     */
    private Long userId;

    /**
     * 使用描述 		#下单/关单7
     */
    private String useDesc;

    /**
     * #0
     */
    private Long adminId;

    /**
     * #clientID
     */
    private String adminDesc;

    /**
     * 添加时间(主键)	#
     */
    private Long addTime;
}
