package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.domain.point.facade.CarShopPointFacade;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.OrderDataInfo;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ReturnType;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 积分资源管理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class PointProvider implements ResourceProvider<PointProvider.PointResource> {
    @Autowired
    private CarShopPointFacade carShopPointFacade;

    private ResourceObject<PointResource> resourceObject;

    @Override
    public ResourceObject<PointResource> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<PointResource> object) {
        this.resourceObject = object;
    }


    @Override
    public void initResource(ResourceObject<PointResource> object, OrderDataInfo orderData) {
        PointResource pointResource = object.getContent();
        if (orderData != null ) {
            pointResource.setRefundNo(orderData.getRefundNo());
            pointResource.setRefundAmount(orderData.getReturnPointAmount());
            pointResource.setRefundType(orderData.getReturnType().getValue());
        }
        this.resourceObject = object;
    }

    /**
     * 锁定积分资源
     *
     * @throws BizError
     */
    @Override
    public void lock() throws BizError {
        boolean locked = carShopPointFacade.consume(resourceObject.getContent().getUserId(), resourceObject.getOrderId(), resourceObject.getContent().getAmount());
        if (!locked) {
            log.warn("lock point fail. resourceObject:{}", GsonUtil.toJson(resourceObject));
            throw ExceptionHelper.create(ErrCode.POINT_CONSUME_FAIL, "核销积分资源失败");
        }
        log.info("lock point ok. resourceObject:{}", GsonUtil.toJson(resourceObject));
    }

    @Override
    public void consume() {
        log.info("consume point resource. {}", GsonUtil.toJson(resourceObject));
    }

    /**
     * 回滚积分资源
     *
     * @throws BizError
     */
    @Override
    public void rollback() throws BizError {
        PointResource pointResource = resourceObject.getContent();
        if (pointResource.getRefundType() != ReturnType.RETURN.getValue()) {
            boolean rollback = carShopPointFacade.rollback(pointResource.getUserId(), resourceObject.getOrderId());
            if (!rollback) {
                log.warn("rollback point fail. resourceObject:{}", GsonUtil.toJson(resourceObject));
                throw ExceptionHelper.create(ErrCode.POINT_ROLLBACK_FAIL, "回滚积分资源失败");
            }
        } else {
            if (pointResource.getRefundAmount() <= 0) {
                log.warn("refund point amount is zero. resourceObject:{}", GsonUtil.toJson(resourceObject));
                return;
            }
            boolean refund = carShopPointFacade.refund(pointResource.getUserId(), resourceObject.getOrderId(), pointResource.getRefundNo(), pointResource.getRefundAmount());
            if (!refund) {
                log.warn("refund point fail. resourceObject:{}", GsonUtil.toJson(resourceObject));
                throw ExceptionHelper.create(ErrCode.POINT_ROLLBACK_FAIL, "退还积分资源失败");
            }
        }
        log.info("return point ok. resourceObject:{}", GsonUtil.toJson(resourceObject));

    }



    @Override
    public String conflictText() {
        return "积分资源初始化异常";
    }

    @Data
    public static class PointResource {
        private long userId;
        private long amount;
        private long refundNo;
        private long refundAmount;
        private int refundType;
    }
}
