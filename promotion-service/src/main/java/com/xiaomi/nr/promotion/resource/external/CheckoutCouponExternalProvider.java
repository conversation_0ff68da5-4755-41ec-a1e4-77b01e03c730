package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.domain.coupon.service.common.CouponInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.List;

/**
 * Created by wang<PERSON>yi on 2023/1/13
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class CheckoutCouponExternalProvider extends ExternalDataProvider<List<CheckoutCoupon>> {

    /**
     * 券类型信息数据
     */
    private ListenableFuture<List<CheckoutCoupon>> future;

    @Autowired
    @Qualifier("couponInfoServiceRemoteImpl")
    private CouponInfoService couponInfoService;


    @Override
    protected boolean switchOn() {
        return true;
    }

    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
//        Long uid = request.getUserId();
//        List<String> codes = request.getCouponCodes();
//        List<Long> ids = request.getCouponIds();
//        //  券码
//        if (CollectionUtils.isNotEmpty(codes) || CollectionUtils.isNotEmpty(ids)) {
//            this.future = couponInfoService.getCouponForCheckout(request);
//            // 券ID
//        }
    }

    @Override
    protected long getTimeoutMills() {
        return DEFAULT_TIMEOUT;
    }

    @Override
    public ListenableFuture<List<CheckoutCoupon>> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.COUPON;
    }
}
