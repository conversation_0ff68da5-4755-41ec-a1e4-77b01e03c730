package com.xiaomi.nr.promotion.domain.coupon.service.mishop;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetail;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.model.DeductedInfo;
import com.xiaomi.nr.promotion.domain.coupon.service.base.type.AbstractDeductCoupon;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import com.xiaomi.nr.promotion.enums.OrderShipmentIdEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.CouponHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.xiaomi.nr.promotion.error.ClientSideErr.COMMON_SYSTEM_ERR_MSG;

/**
 * 抵扣券
 *
 * <AUTHOR>
 * @date 2022/3/3
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class DeductCoupon extends AbstractDeductCoupon {



    @Override
    public PromotionToolType getType() {
        return PromotionToolType.COUPON_DEDUCT;
    }


    @Override
    public CouponTypeEnum getCouponType() {
        return CouponTypeEnum.DEDUCT;
    }

    @Override
    public Long getCouponId() {
        return id;
    }


    @Override
    public Coupon generateCartCoupon() {
        return initCoupon();
    }

    @Override
    public boolean load(CheckoutCoupon checkoutCoupon) throws BizError {
        boolean success =  super.load(checkoutCoupon);
       if (checkoutCoupon.getPromotionValue() == 0) {
           throw ExceptionHelper.create(GeneralCodes.ParamError, "无效的抵扣券");

       }
       return true;
    }


    @Override
    public CouponCheckoutResult checkoutCoupon(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        List<CartItem> cartList = request.getCartList();
        List<GoodsIndex> goodsList = matchCartList(cartList, context, joinGoods, (long) getCouponType().getType());
        ValidGoods validGoods = CartHelper.buildValidGoods(cartList, goodsList);

        List<Integer> indexList = goodsList.stream().map(GoodsIndex::getIndex).collect(Collectors.toList());


        Pair<Boolean, String> satisfiedResult = isSatisfiedQuota(validGoods);

        if (satisfiedResult.getLeft()) {
            //获取抵扣商品
            List<DeductedInfo> deductedInfoList = getDeductedInfoList(cartList, indexList);


            //计算抵扣金额
            if (CollectionUtils.isNotEmpty(deductedInfoList)) {
                deductedInfoList.sort((info1, info2) -> (int) (info2.getGoodPrice() - info1.getGoodPrice()));
                DeductedInfo deductedGood = deductedInfoList.get(0);
                log.debug("deductedGood.goodPrice {}", deductedGood.getGoodPrice());

                // 只有一个可以被分摊，摊的是当前单品的购物车价格
                long deductMoney = 0L;

                //只处理一分钱抵扣
                deductMoney = Math.max(deductedGood.getGoodPrice() - checkoutCoupon.getPromotionValue(), 0L);

                CouponCheckoutResult result = new CouponCheckoutResult();
                result.setCouponId(id);
                if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                    result.setCouponCode(checkoutCoupon.getCouponCode());
                }
                result.setAllow(true);
                result.setReduceAmount(deductMoney);
                result.setDeductedInfo(deductedGood);
                result.setValidGoods(goodsList);
                result.setValidGoodsPrice(validGoods.getValidPrice());
                return result;
            } else {
                CouponCheckoutResult result = new CouponCheckoutResult();
                result.setCouponId(id);
                if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                    result.setCouponCode(checkoutCoupon.getCouponCode());
                }
                result.setAllow(false);
                result.setUnusableReason("未满足优惠券使用条件");
                result.setUnusableCode(ErrCode.ERR_COUPON_MONEY_NUM.getCode());
                result.setKeyDataUnusable(satisfiedResult.getRight());
                return result;
            }

        } else {
            CouponCheckoutResult result = new CouponCheckoutResult();

            result.setAllow(false);
            result.setUnusableReason("未满足优惠券使用条件");
            result.setUnusableCode(ErrCode.ERR_COUPON_MONEY_NUM.getCode());
            result.setKeyDataUnusable(satisfiedResult.getRight());
            return result;
        }
    }

    private List<DeductedInfo> getDeductedInfoList(List<CartItem> cartList, List<Integer> indexes) throws BizError {

        CompareItem validGoods = this.joinGoods;
        if (CollectionUtils.isEmpty(indexes)) {
            return Collections.emptyList();
        }

        List<DeductedInfo> deductedGoodsInfo = new ArrayList<>();
        for (Integer index : indexes) {
            if (index > cartList.size()) {
                log.error("UpdateCartsReduceDeductCoupon {}th cart is wrong", index);
                throw ExceptionHelper.create(ErrCode.ERR_FUNC_INPUT, COMMON_SYSTEM_ERR_MSG);
            }
            CartItem cartItem = cartList.get(index);

            // 是否在可抵扣列表
            String skuPackage = CartHelper.getSkuPackage(cartItem);
            if (!validGoods.getSku().contains(skuPackage) && !validGoods.getPackages().contains(skuPackage)) {
                continue;
            }
            //当前价格已经为０，不抵扣
            long curPrice = CartHelper.goodsCurPrice(cartItem);
            if (curPrice <= 0) {
                continue;
            }

            DeductedInfo deductedGood = new DeductedInfo(index, curPrice);
            deductedGoodsInfo.add(deductedGood);
        }
        return deductedGoodsInfo;
    }



    @Override
    public void updateCartsReduce(CheckoutPromotionRequest request, CheckoutContext context, CouponCheckoutResult result) {
        //处理商品分摊
        updateItemReduce(request.getCartList(), result);

        //处理邮费
        if (request.getShipmentId() != OrderShipmentIdEnum.FLASH_POST.val) {
            updateExpressInfo(context, result);
        }
        //更新context
        updateCommonInfo(request, context);

    }

    @Override
    public void updateItemReduce(List<CartItem> cartList, CouponCheckoutResult result) {
        String idKey = CouponHelper.getCartListCouponKey(String.valueOf(result.getCouponId()));
        CartItem cartItem = cartList.get(result.getDeductedInfo().getIndex().getInCarts());
        if (cartItem == null) {
            log.error("in updateCartsReduceDeduct cart is nil, IDKey:{}", idKey);
            return;
        }

        long deductMoney = result.getReduceAmount();

        if (deductMoney < 0 || CartHelper.itemCurPrice(cartItem) < deductMoney) {
            String errStr = String.format("in func updateReduceListAndAmount, %s", idKey);
            log.error("{}, ReduceList is {}, reduce is {}, ItemCurPrice is {}", errStr, cartItem.getReduceList(), deductMoney,
                    CartHelper.itemCurPrice(cartItem));
            return;
        }

        //处理分摊
        long reduceVal = Optional.ofNullable(cartItem.getReduceList().get(idKey)).orElse(0L);
        long reduceAmount = Optional.ofNullable(cartItem.getReduceAmount()).orElse(0L);
        cartItem.getReduceList().put(idKey, reduceVal + deductMoney);
        cartItem.setReduceAmount(reduceAmount + deductMoney);


//        // 抵扣劵的优惠明细
        List<ReduceDetail> reduceDetails = cartItem.getReduceDetailList().getOrDefault(PromotionConstant.SHARE_COUPON_DEDUCT_KEY, new ArrayList<>());
        ReduceDetail reduceDetail = new ReduceDetail();
        reduceDetail.setId(result.getCouponId());
        reduceDetail.setAmount(deductMoney);
        reduceDetails.add(reduceDetail);
        cartItem.getReduceDetailList().put(PromotionConstant.SHARE_COUPON_DEDUCT_KEY, reduceDetails);
    }
}
