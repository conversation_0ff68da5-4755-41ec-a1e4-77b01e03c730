package com.xiaomi.nr.promotion.domain.workhour.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.xiaomi.nr.mropolicy.api.dto.workhour.WorkHourDetailDto;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.PromotionIdEnum;
import com.xiaomi.nr.promotion.api.dto.enums.PromotionType;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.api.dto.model.WorkHourDeduplicationInfo;
import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.enums.BizSubTypeEnum;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.resource.external.WorkHourExternalProvider;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WorkHourFacade {
    
    @Autowired
    private NacosConfig nacosConfig;
    
    public void checkout(DomainCheckoutContext domainCheckoutContext) throws Exception {
        if (domainCheckoutContext.getFromInterface().equals(FromInterfaceEnum.CHECKOUT_CART)) {
            return;
        }
        CheckoutPromotionRequest request = domainCheckoutContext.getRequest();
        
        if (CollectionUtil.isNotEmpty(nacosConfig.getVidBlackList())) {
            if (nacosConfig.getVidBlackList().contains(request.getVid())) {
                log.info("work hour black list, skip work hour domain, request is {}", GsonUtil.toJson(request));
                return;
            }
        }
        
        
        CheckoutContext context = domainCheckoutContext.getContext();
        // 过滤购物车，只处理工时
        List<CartItem> cartList = request.getCartList();
        Set<Long> workHourSsuIdSet = cartList.stream().filter(f -> Objects.equals(f.getBizSubType(), BizSubTypeEnum.CAR_WORK_HOUR.getCode())).map(CartItem::getSsuId).collect(Collectors.toSet());
        if (workHourSsuIdSet.isEmpty()) {
            return;
        }
        // 获取工时去重结果
        WorkHourExternalProvider externalDataProvider = (WorkHourExternalProvider) context.getExternalDataMap().get(ResourceExtType.UN_DUPLICATED_WORK_HOUR);
        Map<Long, WorkHourDetailDto> workHourMap = externalDataProvider.getFuture().get(2000, TimeUnit.MILLISECONDS);
        if (workHourMap == null) {
            log.warn("get work hour data is empty, workHourSsuIdSet{}. ", workHourSsuIdSet);
            throw ExceptionHelper.create(ErrCode.ERR_WORK_HOUR, "获取工时数据失败");
        }

        // 遍历购物车
        boolean existWorkHour=false;
        for (CartItem cartItem : cartList) {
            // 只处理工时，过滤配件
            if (!cartItem.getBizSubType().equals(BizSubTypeEnum.CAR_WORK_HOUR.getCode())){
                continue;
            }
            Long ssuId = cartItem.getSsuId();
            WorkHourDetailDto workHourData = workHourMap.get(ssuId);
            if (workHourData == null) {
                String errorInfo = String.format("维保策略系统未查询到工时ssuId:%s", ssuId);
                log.warn(errorInfo);
                continue;
            }
            BigDecimal originWorkHourCount = workHourData.getOriginWorkHourCount();
            BigDecimal finalWorkHourCount = workHourData.getFinalWorkHourCount();

            // 交易和维保策略系统原始工时不一致时，抛异常
            if (originWorkHourCount.compareTo(cartItem.getMaintenanceInfo().getWorkHour())!=0) {
                log.warn("work hour data updated. original work hour error, ssuId{}. cartItem.getWorkHour:{},workHourData.getOriginWorkHourCount:{}", ssuId, cartItem.getMaintenanceInfo().getWorkHour(), originWorkHourCount);
                throw ExceptionHelper.create(ErrCode.ERR_WORK_HOUR, "工时数据已更新，请稍后重试");
            }
            // 交易和维保策略系统原始价格不一致时，抛异常
            BigDecimal originalMultiply = BigDecimal.valueOf(cartItem.getMaintenanceInfo().getUnitPrice()).multiply(originWorkHourCount);
            long originalPrice = Long.parseLong(originalMultiply.setScale(0, RoundingMode.HALF_UP).toString());
            if (originalPrice != cartItem.getOriginalCartPrice()) {
                log.warn("work hour data updated. original price error, ssuId{}. originalPrice:{},cartItem.getOriginalCartPrice:{}", ssuId, originalPrice, cartItem.getOriginalCartPrice());
                throw ExceptionHelper.create(ErrCode.ERR_WORK_HOUR, "工时数据已更新，请稍后重试");
            }

            if (originWorkHourCount.compareTo(finalWorkHourCount)==0) {
                continue;
            }

            // 计算价格: 工时*单价
            BigDecimal multiply = BigDecimal.valueOf(cartItem.getMaintenanceInfo().getUnitPrice()).multiply(finalWorkHourCount);
            long price = Long.parseLong(multiply.setScale(0, RoundingMode.HALF_UP).toString());
            // 改价: 修改cartPrice
            cartItem.setCartPrice(price);
            // 记录优惠: 将去重后优惠记录到reduceItemList
            List<ReduceDetailItem> reduceItemList = Optional.ofNullable(cartItem.getReduceItemList()).orElse(Lists.newArrayList());
            ReduceDetailItem detailItem = new ReduceDetailItem();
            reduceItemList.add(detailItem);
            detailItem.setPromotionId(PromotionIdEnum.UN_DUPLICATED_WORK_HOUR.getPromotionId());
            detailItem.setPromotionType(PromotionType.UN_DUPLICATED_WORK_HOUR.getValue());
            detailItem.setSsuId(ssuId);
            long reduceSingle = cartItem.getOriginalCartPrice() - price;
            detailItem.setReduce(reduceSingle * cartItem.getCount());
            detailItem.setReduceSingle(reduceSingle);
            WorkHourDeduplicationInfo workHourDeduplicationInfo = new WorkHourDeduplicationInfo();
            workHourDeduplicationInfo.setOriginWorkHour(originWorkHourCount);
            workHourDeduplicationInfo.setFinalWorkHour(finalWorkHourCount);
            detailItem.setExtend(GsonUtil.toJson(workHourDeduplicationInfo));
            existWorkHour=true;
        }

        // 记录促销信息: 不存储实际内容，只存入活动ID，方便交易关联处理
        if (existWorkHour){
            List<PromotionInfo> promotionInfoList = Optional.ofNullable(context.getPromotion()).orElse(Lists.newArrayList());
            PromotionInfo promotionInfo = new PromotionInfo();
            promotionInfo.setPromotionId(PromotionIdEnum.UN_DUPLICATED_WORK_HOUR.getPromotionId().toString());
            promotionInfo.setTitle(PromotionIdEnum.UN_DUPLICATED_WORK_HOUR.getName());
            promotionInfo.setType(String.valueOf(PromotionType.UN_DUPLICATED_WORK_HOUR.getValue()));
            promotionInfoList.add(promotionInfo);
            domainCheckoutContext.getContext().setPromotion(promotionInfoList);
        }
    }
}
