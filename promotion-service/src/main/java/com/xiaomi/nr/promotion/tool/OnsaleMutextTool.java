package com.xiaomi.nr.promotion.tool;

import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 直降互斥
 *
 * <AUTHOR>
 * @date 2021/11/3
 */
@Component
public class OnsaleMutextTool {

    /**
     * 处理多个直降活动，取最优惠的
     *
     * @param productActInfoList 可参加活动列表
     */
    public void handleOnsaleMutex(List<ProductActInfo> productActInfoList) {
        if (CollectionUtils.isEmpty(productActInfoList)) {
            return;
        }
        List<ProductActInfo> actInfoList = productActInfoList.stream()
                .filter(actInfo -> actInfo.getType() == ActivityTypeEnum.ONSALE.getValue())
                .collect(Collectors.toList());
        if (actInfoList.size() <= 1) {
            return;
        }
        // 取直降幅度最大的
        ProductActInfo minLowerPriceActInfo = actInfoList.get(0);
        for (ProductActInfo actInfo : actInfoList) {
            Long lowerPrice = actInfo.getOnsaleGoods().getLowerPrice();
            if (lowerPrice < minLowerPriceActInfo.getOnsaleGoods().getLowerPrice()) {
                minLowerPriceActInfo = actInfo;
            }
        }
        // 保留直降幅度最大的， 删除其他直降活动
        actInfoList.remove(minLowerPriceActInfo);
        productActInfoList.removeAll(actInfoList);
    }

    /**
     * 处理promotion直降
     *
     * @param promotionList 优惠信息列表
     */
    public void handlePromtionOnsaleMutex(List<PromotionInfo> promotionList) {
        if (CollectionUtils.isEmpty(promotionList)) {
            return;
        }
        List<PromotionInfo> onsalePromotionList = promotionList.stream()
                .filter(info -> Objects.equals(info.getType(), String.valueOf(PromotionToolType.ONSALE.getTypeId())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(onsalePromotionList)) {
            return;
        }
        // 查找价格最低的
        PromotionInfo lowerPromotionInfo = null;
        long lowerPrice = Long.MAX_VALUE;
        for (PromotionInfo promotionInfo : onsalePromotionList) {
            if (!StringUtils.isNumeric(promotionInfo.getExtend())) {
                continue;
            }
            long price = Long.parseLong(promotionInfo.getExtend());
            if (price <= 0L) {
                continue;
            }
            if (price < lowerPrice) {
                lowerPrice = price;
                lowerPromotionInfo = promotionInfo;
            }
        }
        // 查找价格最低的
        if (lowerPromotionInfo != null) {
            onsalePromotionList.remove(lowerPromotionInfo);
        }
        promotionList.removeAll(onsalePromotionList);
    }
}
