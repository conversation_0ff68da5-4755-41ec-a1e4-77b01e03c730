package com.xiaomi.nr.promotion.activity;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.xiaomi.nr.promotion.api.dto.enums.ProductDepartmentEnum;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.OnsaleProductRuleDto;
import com.xiaomi.nr.promotion.util.ChannelsHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.GetProductActRequest;
import com.xiaomi.nr.promotion.componet.action.OnsaleAction;
import com.xiaomi.nr.promotion.componet.condition.ChannelCondition;
import com.xiaomi.nr.promotion.componet.condition.ClientCondition;
import com.xiaomi.nr.promotion.componet.condition.DailyTimeCondition;
import com.xiaomi.nr.promotion.componet.condition.OnsaleCondition;
import com.xiaomi.nr.promotion.componet.condition.OrgCondition;
import com.xiaomi.nr.promotion.componet.preparation.GoodsHierarchyPreparation;
import com.xiaomi.nr.promotion.componet.preparation.OrgInfoPreparation;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.GoodsActPriceProvider;
import com.xiaomi.nr.promotion.engine.PromotionPriceNormProvider;
import com.xiaomi.nr.promotion.engine.PromotionPriceProvider;
import com.xiaomi.nr.promotion.engine.PromotionPriceV2Provider;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.enums.ChannelScopeEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.ActRespConverter;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.OnsalePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.tool.ConditionCheckTool;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import lombok.extern.slf4j.Slf4j;



/**
 * 直降活动
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class OnsaleActivity extends AbstractOnsaleActivity implements ActivityTool, PromotionPriceProvider, GoodsActPriceProvider, PromotionPriceV2Provider, PromotionPriceNormProvider {
    /**
     * 直降信息Map
     * key: skuPackage val:ActPriceInfo
     */
    protected Map<String, ActPriceInfo> onsaleInfoMap;

    @Autowired
    private ConditionCheckTool conditionCheckTool;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine
                .conditionPreparation(GoodsHierarchyPreparation.class)
                .conditionPreparation(OrgInfoPreparation.class)
                .condition(DailyTimeCondition.class)
                .condition(OrgCondition.class)
                .condition(ChannelCondition.class)
                .condition(ClientCondition.class)
                .condition(OnsaleCondition.class)
                .action(OnsaleAction.class);
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.ONSALE;
    }

    /**
     * 获取优惠价
     *
     * @param skuPackageList SKU or Package列表
     * @param orgCode        orgCode
     * @return 优惠活动价格Map key：skuPackage val: 优惠价
     */
    @Override
    public Map<String, Long> getPromotionPrice(List<String> skuPackageList, String orgCode) {
        if (CollectionUtils.isEmpty(skuPackageList)) {
            return Collections.emptyMap();
        }
        return skuPackageList.stream()
                .map(onsaleInfoMap::get).filter(Objects::nonNull)
                .filter(info -> conditionCheckTool.checkOnsaleSkuLimitNum(info, id, orgCode))
                .filter(info -> info.getPrice() >= 0L)
                .collect(Collectors.toMap(info -> String.valueOf(info.getSkuPackage()), ActPriceInfo::getPrice, Math::min));
    }

    /**
     * 获取优惠价（暂只有直降）
     *
     * @param goodsDtoList SKU or Package列表
     * @return 优惠活动价格Map key：skuPackage val: 优惠价
     */
    @Override
    public Map<Long, GoodsPriceDto> getPromotionPrice(List<GoodsDto> goodsDtoList) {
        if (CollectionUtils.isEmpty(goodsDtoList)) {
            return Collections.emptyMap();
        }
        Map<Long, GoodsPriceDto> priceDtoMap = Maps.newHashMap();
        for (GoodsDto goodsDto : goodsDtoList) {
            Long id = goodsDto.getId();
            ActPriceInfo priceInfo = onsaleInfoMap.get(String.valueOf(id));
            if (priceInfo == null) {
                continue;
            }
            GoodsPriceDto priceDto = new GoodsPriceDto();
            priceDto.setId(goodsDto.getId());
            priceDto.setLevel(goodsDto.getLevel());
            priceDto.setLowerPrice(priceInfo.getPrice());
            priceDto.setLowerPriceGroups(priceInfo.getChildGroupPriceMap());
            priceDtoMap.put(id, priceDto);
        }
        return priceDtoMap;
    }

    /**
     * 构建优惠信息
     *
     * @param context 上下文
     * @return 优惠信息
     */
    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) {
        PromotionInfo promotionInfo = buildDefaultPromotionInfo(context);
        promotionInfo.setFrequent(frequency != null ? frequency.getValue() : null);
        promotionInfo.setTotalLimitNum(actLimitNum);
        promotionInfo.setActivityMutexLimit(actMutexLimit ? BooleanEnum.YES.getValue() : BooleanEnum.NO.getValue());
        promotionInfo.setActivityMutex(actMutexes);
        promotionInfo.setNumLimitRule(ActRespConverter.convert(this.numLimitRule));
        return promotionInfo;
    }

    /**
     * 获取产品站信息
     *
     * @param clientId       应用ID
     * @param orgCode        门店Code
     * @param skuPackageList 活动类别
     */
    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        List<String> joinedSkuPackageList = Lists.newArrayList(includeSkuPackages);
        joinedSkuPackageList.retainAll(skuPackageList);
        if (CollectionUtils.isEmpty(joinedSkuPackageList)) {
            return Collections.emptyMap();
        }
        Map<String, ProductActInfo> actMap = Maps.newHashMap();
        for (String skuPackage : joinedSkuPackageList) {
            ActPriceInfo onsaleInfo = onsaleInfoMap.get(skuPackage);
            if (onsaleInfo == null) {
                continue;
            }
            boolean validLimitNum = conditionCheckTool.checkOnsaleSkuLimitNum(onsaleInfo, id, orgCode);
            if (!validLimitNum) {
                log.warn("validLimitNum not match. actId:{} skuPackage:{}", id, skuPackage);
                continue;
            }

            ProductActOnsaleGoods onsaleGoods = new ProductActOnsaleGoods();
            onsaleGoods.setLowerPrice(onsaleInfo.getPrice());
            if (BooleanEnum.isYes(onsaleInfo.getIsLimit()) && onsaleInfo.getLimitRule() != null) {
                onsaleGoods.setLimitRule(ActRespConverter.convert(onsaleInfo.getLimitRule()));
            }

            ProductActInfo productActInfo = new ProductActInfo();
            productActInfo.setType(type.getValue());
            productActInfo.setId(id);
            productActInfo.setName(name);
            productActInfo.setChannels(channels);
            productActInfo.setSelectClientList(selectClientList);
            productActInfo.setSelectOrgList(selectOrgList);
            productActInfo.setUnixStartTime(getUnixStartTime());
            productActInfo.setUnixEndTime(getUnixEndTime());
            productActInfo.setOnsaleGoods(onsaleGoods);
            actMap.put(skuPackage, productActInfo);
        }
        return actMap;
    }


    private boolean isBindMainAccessory(Integer department, Boolean bindMainAccessory) {
        if (department == null || bindMainAccessory == null) {
            return false;
        }
        return department.equals(ProductDepartmentEnum.S2.getValue()) && bindMainAccessory;
    }

    /**
     * 获取优惠价格
     *
     * @param goodsList     详情信息
     * @param contextParams 上下文参数
     * @return key: SSU val: 价格信息
     */
    @Override
    public Map<Long, PromotionPriceDTO> getGoodsPromotionPrice(List<GoodsDto> goodsList, Map<String, String> contextParams) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(goodsList)) {
            return Collections.emptyMap();
        }
        Map<Long, PromotionPriceDTO> priceDtoMap = Maps.newHashMap();

        for (GoodsDto goodsDto : goodsList) {
            long onSalePrice;
            // 团购主附品强绑定场景算价
            if (isBindMainAccessory(goodsDto.getDepartment(),goodsDto.getBindMainAccessory())){
                onSalePrice = calMainAccessoryPrice(goodsDto);
            } else {
                ActPriceInfo priceInfo = onsaleInfoMap.get(String.valueOf(goodsDto.getId()));
                if (priceInfo == null) {
                    continue;
                }
                onSalePrice = priceInfo.getPrice();
            }
            Long finalPrice=goodsDto.getPrice();
            if (goodsDto.getPrice() != null) {
                finalPrice = Math.min(onSalePrice, goodsDto.getPrice());
            }
            // 包装结果
            OnsaleProductRuleDto onsaleRule = new OnsaleProductRuleDto();
            onsaleRule.setPromotionPrice(onSalePrice);
            PromotionPriceDTO priceDTO = buildPromotionPrice(GsonUtil.toJson(onsaleRule), finalPrice);
            priceDtoMap.put(goodsDto.getSsuId(), priceDTO);
            // 改价
            goodsDto.setPrice(finalPrice);
        }
        return priceDtoMap;
    }

    private long calMainAccessoryPrice(GoodsDto goodsDto){
        long finalPrice=0L;
        List<CartItemChild> childs = goodsDto.getChilds();
        for (CartItemChild child : childs) {
            ActPriceInfo priceInfo = onsaleInfoMap.get(String.valueOf(child.getSku()));
            if (priceInfo != null) {
                long onSalePrice = priceInfo.getPrice();
                if (onSalePrice<child.getSellPrice()){
                    child.setSellPrice(onSalePrice);
                }
            }
            finalPrice += child.getSellPrice() * child.getCount();
        }
        return finalPrice;
    }

    @Override
    public Map<String, List<GoodsPriceInfo>> getGoodsActPrice(List<String> skuPackageList) throws BizError {
        List<String> joinedSkuPackageList = Lists.newArrayList(includeSkuPackages);
        joinedSkuPackageList.retainAll(skuPackageList);
        if (CollectionUtils.isEmpty(joinedSkuPackageList)) {
            return Collections.emptyMap();
        }
        if (CollectionUtils.isEmpty(channels)) {
            return Collections.emptyMap();
        }

        Map<String, List<GoodsPriceInfo>> actMap = Maps.newHashMap();
        for (String skuPackage : joinedSkuPackageList) {
            List<GoodsPriceInfo> priceInfoList = Lists.newArrayList();
            actMap.put(skuPackage, priceInfoList);
            ActPriceInfo onsaleInfo = onsaleInfoMap.get(skuPackage);
            if (onsaleInfo == null) {
                continue;
            }
            channels.forEach(channel -> {
                //只对外透出指定范围内的渠道
                if (ChannelScopeEnum.isContains(channel)) {
                    GoodsPriceInfo priceInfo = new GoodsPriceInfo();
                    priceInfo.setActType(type.getValue());
                    priceInfo.setActPrice(onsaleInfo.getPrice());
                    priceInfo.setChannelScope(channel);
                    priceInfoList.add(priceInfo);
                }
            });
        }
        return actMap;
    }

    /**
     * 获取活动详情
     *
     * @return 活动详情
     */
    @Override
    public ActivityDetail getActivityDetail() {
        ActivityDetail detail = super.getBasicActivityDetail();
        detail.setPriceInfoMap(onsaleInfoMap);
        return detail;
    }

    /**
     * 获取产品站活动信息：获取PromotionInfo
     *
     * @param request   产品站信息
     * @param hierarchy 商品层级关系
     * @param isOrgTool 是否来源门店工具
     * @return 促销信息
     */
    @Override
    public PromotionInfo getProductAct(GetProductActRequest request, GoodsHierarchy hierarchy, boolean isOrgTool) {
        if (!conditionCheckTool.checkDailyTime(daily, dailyStartTime, dailyEndTime)) {
            log.debug("onsale condition is not match. request:{}", request);
            return null;
        }

        String skuPackage = StringUtils.isNotEmpty(request.getSku()) ? request.getSku() : request.getCommodityId();
        ActPriceInfo onsaleInfo = onsaleInfoMap.get(skuPackage);
        if (onsaleInfo == null) {
            log.warn("skuPackage onsaleInfo is null. skuPackage:{} actId:{}", skuPackage, id);
            return null;
        }
        Long lowerPrice = onsaleInfo.getPrice();
        if (lowerPrice == null || lowerPrice >= request.getPrice()) {
            return null;
        }
        PromotionInfo promotionInfo = new PromotionInfo();
        promotionInfo.setPromotionId(String.valueOf(id));
        promotionInfo.setType(String.valueOf(type.getValue()));
        promotionInfo.setTypeInfo(ActivityTypeEnum.ONSALE.getName());
        promotionInfo.setTitle(name);
        promotionInfo.setTypeCode(metaInfo.getTypeCode());
        promotionInfo.setExtend(String.valueOf(lowerPrice));
        promotionInfo.setOffline(offline.getValue());
        return promotionInfo;
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof OnsalePromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        OnsalePromotionConfig promotionConfig = (OnsalePromotionConfig) config;
        this.actMutexLimit = promotionConfig.isActMutexLimit();
        this.actMutexes = promotionConfig.getActMutexes();
        this.onsaleInfoMap = promotionConfig.getOnsaleInfoMap();
        return true;
    }
}
