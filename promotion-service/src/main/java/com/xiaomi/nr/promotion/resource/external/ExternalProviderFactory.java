package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.xiaomi.nr.promotion.enums.BizPlatformEnum.*;

/**
 * 外部资源工厂
 *
 * <AUTHOR>
 * @date 2021/4/21
 */
@Component
public class ExternalProviderFactory {
    @Resource
    private ObjectFactory<GoodsHierarchyExternalProvider> goodsHierarchyExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<OrgInfoExternalProvider> orgInfoExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<EcardExternalProvider> ecardExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<ClientInfoExternalProvider> clientInfoExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<GlobalActExcludeExternalProvider> globalActInExcludeExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<GlobalCouponInExcludeExternalProvider> globalCouponInExcludeExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<GlobaExcludeExternalProvider> globalInExcludeExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<BeijingCouponExternalProvider> beijingCouponExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<RecycleOrderExternalProvider> recycleOrderExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<WorkHourExternalProvider> workHourExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<SubsidyPhoenixExternalProvider> subsidyPhoenixExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<GoodsStockExternalProvider> goodsStockExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<CarUserVipExternalProvider> carUserVipExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<CarVidVipExternalProvider> carVidVipExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<CarUserPermitExternalProvider> carUserPermitExternalProviderFactory;


    /**
     * 获取所有需要外部提供资源提供者
     *
     * @return providers
     */
    public List<ExternalDataProvider<?>> getAllExternalProviders(FromInterfaceEnum fromInterface, BizPlatformEnum bizPlatform) {
        if (Objects.equals(bizPlatform, CAR) || Objects.equals(bizPlatform, CAR_PARALLEL)) {
            return getAllExternalProvidersByCar();
        }
        if (Objects.equals(bizPlatform, CAR_SHOP)) {
            return getAllExternalProvidersByCarShop();
        }
        if (Objects.equals(bizPlatform, MAINTENANCE_REPAIR)) {
            return getAllExternalProvidersByCarMaintenance();
        }
        return getAllExternalProviders(fromInterface);
    }


    /**
     * 整车销售场景
     * @return
     */
    public List<ExternalDataProvider<?>> getAllExternalProvidersByCar() {
        List<ExternalDataProvider<?>> providers = new ArrayList<>();
        providers.add(orgInfoExternalProviderObjectFactory.getObject());
        return providers;
    }

    /**
     * 车商城场景
     * @return
     */
    public List<ExternalDataProvider<?>> getAllExternalProvidersByCarShop() {
        List<ExternalDataProvider<?>> providers = new ArrayList<>();
        providers.add(goodsStockExternalProviderObjectFactory.getObject());
        providers.add(orgInfoExternalProviderObjectFactory.getObject());
        providers.add(carUserVipExternalProviderObjectFactory.getObject());
        providers.add(carUserPermitExternalProviderFactory.getObject());
        return providers;
    }

    /**
     * 整车售后场景
     * @return
     */
    public List<ExternalDataProvider<?>> getAllExternalProvidersByCarMaintenance() {
        List<ExternalDataProvider<?>> providers = new ArrayList<>();
        providers.add(workHourExternalProviderObjectFactory.getObject());
        providers.add(orgInfoExternalProviderObjectFactory.getObject());
        providers.add(carVidVipExternalProviderObjectFactory.getObject());
        return providers;
    }

    /**
     * 获取所有需要外部提供资源提供者
     *
     * @return providers
     */
    public List<ExternalDataProvider<?>> getAllExternalProviders(FromInterfaceEnum fromInterface) {
        List<ExternalDataProvider<?>> providers = new ArrayList<>();
//        if (Objects.equals(bizPlatform, CAR) || Objects.equals(bizPlatform, CAR_SHOP) || Objects.equals(bizPlatform, MAINTENANCE_REPAIR)) {
//            providers.add(workHourExternalProviderObjectFactory.getObject());
//            providers.add(goodsStockExternalProviderObjectFactory.getObject());
//            providers.add(orgInfoExternalProviderObjectFactory.getObject());
//            providers.add(carUserVipExternalProviderObjectFactory.getObject());
//            providers.add(carVidVipExternalProviderObjectFactory.getObject());
//            return providers;
//        }
        providers.add(goodsHierarchyExternalProviderObjectFactory.getObject());
        providers.add(orgInfoExternalProviderObjectFactory.getObject());
        providers.add(ecardExternalProviderObjectFactory.getObject());
        providers.add(clientInfoExternalProviderObjectFactory.getObject());
        providers.add(globalActInExcludeExternalProviderObjectFactory.getObject());
        providers.add(globalCouponInExcludeExternalProviderObjectFactory.getObject());
        providers.add(globalInExcludeExternalProviderObjectFactory.getObject());
        providers.add(beijingCouponExternalProviderObjectFactory.getObject());
        providers.add(recycleOrderExternalProviderObjectFactory.getObject());
        providers.add(workHourExternalProviderObjectFactory.getObject());
        // 加购不过
        if (!fromInterface.equals(FromInterfaceEnum.CHECKOUT_CART)) {
            providers.add(subsidyPhoenixExternalProviderObjectFactory.getObject());
        }
        providers.add(goodsStockExternalProviderObjectFactory.getObject());
        return providers;
    }


    /**
     * 获取所有需要外部提供资源提供者
     *
     * @return providers
     */
    public List<ExternalDataProvider<?>> getProductDetailExternalProviders(BizPlatformEnum bizPlatform) {
        List<ExternalDataProvider<?>> providers = new ArrayList<>();
        providers.add(goodsStockExternalProviderObjectFactory.getObject());
        return providers;
    }
}
