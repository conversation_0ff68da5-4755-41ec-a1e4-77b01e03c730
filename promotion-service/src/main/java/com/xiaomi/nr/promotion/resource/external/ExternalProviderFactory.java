package com.xiaomi.nr.promotion.resource.external;

import com.google.common.collect.Sets;
import com.xiaomi.nr.promotion.activity.pool.ActivityPool;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.CrowdEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.api.dto.model.CrowdInfo;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyGiftPromotionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 外部资源工厂
 *
 * <AUTHOR>
 * @date 2021/4/21
 */
@Component
@Slf4j
public class ExternalProviderFactory {

    @Autowired
    private ActivityPool activityPool;

    @Resource
    private ObjectFactory<GoodsHierarchyExternalProvider> goodsHierarchyExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<OrgInfoExternalProvider> orgInfoExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<EcardExternalProvider> ecardExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<ClientInfoExternalProvider> clientInfoExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<GlobalActExcludeExternalProvider> globalActInExcludeExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<GlobalCouponInExcludeExternalProvider> globalCouponInExcludeExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<GlobaExcludeExternalProvider> globalInExcludeExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<BeijingCouponExternalProvider> beijingCouponExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<RecycleOrderExternalProvider> recycleOrderExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<WorkHourExternalProvider> workHourExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<SubsidyPhoenixExternalProvider> subsidyPhoenixExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<ProMemberExternalProvider> proMemberExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<UserPropertyExternalProvider> userPropertyExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<GovernmentSubsidyPhoenixExternalProvider> governmentSubsidyPhoenixExternalProviderObjectFactory;
    @Resource
    private ObjectFactory<CrowdPortraitExternalProvider> crowdPortraitExternalProviderObjectFactory;


    /**
     * 获取所有需要外部提供资源提供者
     *
     * @return providers
     */
    public List<ExternalDataProvider<?>> getAllExternalProviders(CheckoutPromotionRequest request, FromInterfaceEnum fromInterface) {
        List<ExternalDataProvider<?>> providers = new ArrayList<>();
        providers.add(goodsHierarchyExternalProviderObjectFactory.getObject());
        providers.add(orgInfoExternalProviderObjectFactory.getObject());
        providers.add(ecardExternalProviderObjectFactory.getObject());
        providers.add(clientInfoExternalProviderObjectFactory.getObject());
        providers.add(globalActInExcludeExternalProviderObjectFactory.getObject());
        providers.add(globalCouponInExcludeExternalProviderObjectFactory.getObject());
        providers.add(globalInExcludeExternalProviderObjectFactory.getObject());
        providers.add(beijingCouponExternalProviderObjectFactory.getObject());
        providers.add(recycleOrderExternalProviderObjectFactory.getObject());
        providers.add(workHourExternalProviderObjectFactory.getObject());
        // 加购不过
        if (!fromInterface.equals(FromInterfaceEnum.CHECKOUT_CART)) {
            providers.add(subsidyPhoenixExternalProviderObjectFactory.getObject());
            providers.add(governmentSubsidyPhoenixExternalProviderObjectFactory.getObject());
        }
        // 国内新零售资源, 买赠人群
        addBuyGiftCrowdProvider(request,providers);
        return providers;
    }

    private void addBuyGiftCrowdProvider(CheckoutPromotionRequest request, List<ExternalDataProvider<?>> providers){
        if (request.getUserId() == null || request.getUserId() <= 0) {
            return;
        }
        List<CartItem> cartList = request.getCartList();
        boolean cannotJoinBuyGift = cartList.stream()
                .allMatch(po -> po.getCannotJoinActTypes().contains((long) ActivityTypeEnum.BUY_GIFT.getValue()));
        if (cannotJoinBuyGift) {
            // 如果所有商品都不能参加购买赠礼活动，则直接返回，不进行后续处理
            log.info("ExternalProviderFactory.addBuyGiftCrowdProvider 所有商品都不能参加购买赠礼活动");
            return;
        }
        List<ActivityTool> activityTools = activityPool.getCheckoutPromotions(request);
        List<ActivityTool> buyGiftActivityToolList = activityTools.stream().filter(activityTool -> Objects.nonNull(activityTool.getType()) && activityTool.getType().getTypeId() == ActivityTypeEnum.BUY_GIFT.getValue()).toList();
        if (buyGiftActivityToolList.isEmpty()) {
            // 没有找到买赠类型的ActivityTool，则直接返回，不进行后续处理
            log.info("ExternalProviderFactory.addBuyGiftCrowdProvider 没有找到买赠类型的ActivityTool");
            return;
        }

        // 标记活动是否配置了F会员可参加
        boolean hitFMember = false;
        // 标记活动是否配置了学生可参加
        boolean hitStudent = false;
        // 标记活动是否配置了海葵人群可参加
        boolean hitMarketPlatform = false;
        // 标记活动是否配置了画像平台人群可参加
        boolean hitCrowdPhoto = false;
        // 活动中配置的海葵id
        Set<String> haikuiIds = Sets.newHashSet();
        // 活动中配置的人群包id
        Set<String> ruleIds = Sets.newHashSet();

        for (ActivityTool tool : buyGiftActivityToolList) {
            BuyGiftPromotionConfig promotionConfig = (BuyGiftPromotionConfig) tool.getPromotionConfig();
            Map<String, CrowdInfo> crwodMap = promotionConfig.getCrwodMap();
            if (promotionConfig.getCrowdList().contains(CrowdEnum.F_MEMBER.getCode())) {
                hitFMember = true;
            }

            if (promotionConfig.getCrowdList().contains(CrowdEnum.STUDENT.getCode())) {
                hitStudent = true;
            }

            if (promotionConfig.getCrowdList().contains(CrowdEnum.MARKET_PLATFORM.getCode())) {
                if (MapUtils.isNotEmpty(crwodMap)) {
                    CrowdInfo crowdInfo = crwodMap.getOrDefault(CrowdEnum.MARKET_PLATFORM.getCode(), null);
                    if (crowdInfo == null) {
                        log.error("ExternalProviderFactory.addBuyGiftCrowdProvider crowdMap中没有海葵人群信息！id = {}", tool.getId());
                        continue;
                    }

                    hitMarketPlatform = true;
                    haikuiIds.add(crowdInfo.getCrowdId());
                }
            }

            if (promotionConfig.getCrowdList().contains(CrowdEnum.CROWD_PHOTO.getCode())) {
                if (MapUtils.isNotEmpty(crwodMap)) {
                    CrowdInfo crowdInfo = crwodMap.getOrDefault(CrowdEnum.CROWD_PHOTO.getCode(), null);
                    if (crowdInfo == null) {
                        log.error("ExternalProviderFactory.addBuyGiftCrowdProvider crowdMap中没有人群画像信息！id = {}", tool.getId());
                        continue;
                    }

                    hitCrowdPhoto = true;
                    ruleIds.add(crowdInfo.getCrowdId());
                }
            }
        }

        // 活动配置了F会员、学生或海葵人群可参加，加载米网人群服务外部资源
        if (hitFMember || hitStudent || hitMarketPlatform) {
            providers.add(userPropertyExternalProviderObjectFactory.getObject());
            if (hitFMember) {
                request.setFMemberFlag(true);
            }

            if (hitStudent) {
                request.setStudentFlag(true);
            }

            if (hitMarketPlatform) {
                request.setHaikuiIds(new ArrayList<>(haikuiIds));
            }
        }

        // 如果活动配置了画像平台人群可参加，加载万象人群画像外部资源
        if (hitCrowdPhoto) {
            providers.add(crowdPortraitExternalProviderObjectFactory.getObject());

            request.setRuleIds(new ArrayList<>(ruleIds));
        }
    }
}
