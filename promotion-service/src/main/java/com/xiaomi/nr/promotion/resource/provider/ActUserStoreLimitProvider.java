package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.enums.RequestUidTypeEnum;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 指定门店降价的每人每个门店活动数量
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class ActUserStoreLimitProvider implements ResourceProvider<ActUserStoreLimitProvider.ActUserLimit> {

    private ResourceObject<ActUserStoreLimitProvider.ActUserLimit> resourceObject;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Autowired
    private NacosConfig nacosConfig;

    @Override
    public ResourceObject<ActUserStoreLimitProvider.ActUserLimit> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<ActUserStoreLimitProvider.ActUserLimit> object) {
        this.resourceObject = object;
    }

    /**
     * 扣减库存
     */
    @Override
    public void lock() throws BizError {
        log.info("lock partOnsale act user limit resource. {}", resourceObject);
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("ActUserStoreLimitProvider.lock(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        if (RequestUidTypeEnum.MOBILE.getUidType().equals(resourceObject.getContent().getUType())) {
            activityRedisDao.incrActPersonStoreMobileLimitNum(
                    resourceObject.getContent().getActId(),
                    resourceObject.getContent().getUid(),
                    resourceObject.getContent().getOrgCode(),
                    resourceObject.getContent().getCount());
        } else {
            activityRedisDao.incrActPersonStoreUserIdLimitNum(
                    resourceObject.getContent().getActId(),
                    resourceObject.getContent().getUid(),
                    resourceObject.getContent().getOrgCode(),
                    resourceObject.getContent().getCount());
        }
        log.info("lock partOnsale act user limit resource ok. {}", resourceObject);
    }

    @Override
    public void consume() {
        log.info("consume onsale act user limit total resource. {}", resourceObject);
    }

    @Override
    public void rollback() throws BizError {
        log.info("rollback partOnsale act user limit resource. {}", resourceObject);
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("ActUserStoreLimitProvider.rollback(): writeSwitch has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        if (RequestUidTypeEnum.MOBILE.getUidType().equals(resourceObject.getContent().getUType())) {
            activityRedisDao.decrActPersonStoreMobileLimitNum(
                    resourceObject.getContent().getActId(),
                    resourceObject.getContent().getUid(),
                    resourceObject.getContent().getOrgCode(),
                    resourceObject.getContent().getCount());
        } else {
            activityRedisDao.decrActPersonStoreUserIdLimitNum(
                    resourceObject.getContent().getActId(),
                    resourceObject.getContent().getUid(),
                    resourceObject.getContent().getOrgCode(),
                    resourceObject.getContent().getCount());
        }
        log.info("rollback partOnsale act user limit resource ok. {}", resourceObject);
    }

    @Override
    public String conflictText() {
        return "指定门店降价减限购失败";
    }

    /**
     * 活动记录
     */
    @Data
    public static class ActUserLimit {
        /**
         * 门店Code
         */
        private String orgCode;
        /**
         * 用户类型
         */
        private String uType;
        /**
         * 用户ID
         */
        private Long uid;
        /**
         * 活动ID
         */
        private Long actId;
        /**
         * 参与活动数量
         */
        private Integer count;
        /**
         * 限制数量
         */
        private Long limitNum;
    }
}
