package com.xiaomi.nr.promotion.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 国补上报城市枚举
 * @date 2024/12/26 15:11
 */
public enum ActivityReportCityEnum {
    /**
     * 深圳上报城市ID
     */
    SZ_REPORT_CITY(3, "深圳上报城市ID"),
    /**
     * 天津上报城市ID
     */
    TJ_REPORT_CITY(15, "天津上报城市ID"),
    /**
     * 北京上报城市ID
     */
    BJ_REPORT_CITY(32, "北京上报城市ID"),

    ;

    private final Integer id;

    private final String name;


    ActivityReportCityEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

}
