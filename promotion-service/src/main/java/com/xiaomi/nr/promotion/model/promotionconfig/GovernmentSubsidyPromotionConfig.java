package com.xiaomi.nr.promotion.model.promotionconfig;

import com.xiaomi.nr.md.promotion.admin.api.dto.activity.Region;
import com.xiaomi.nr.promotion.model.common.GovernmentRule;
import lombok.Data;
import lombok.ToString;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Data
@ToString
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class GovernmentSubsidyPromotionConfig extends MultiPromotionConfig {

    /**
     * 折扣打的折扣，9折就是90
     */
    private long reduceDiscount = 0;
    /**
     * 折扣最高可以减免的钱
     */
    private long maxReducePrice = 0;
    /**
     * sku-品类
     */
    private Map<String, String> goodsSpuGroupMap;
    /**
     * sku-深圳政府模式上报数据
     */
    private Map<String, GovernmentRule> governmentRuleMap;
    /**
     * 收货地址区域限制
     */
    private List<com.xiaomi.nr.md.promotion.admin.api.dto.activity.Region> regionList;
    /**
     * 能效等级
     */
    private String cateLevel;
    /**
     * 国补模式：1-政府模式 2-银联模式 3-极简模式
     */
    private Integer subsidyMode;
    /**
     * 上报城市ID
     */
    private Integer reportCity;
    /**
     * 上报城市标签(xx以旧换新)
     */
    private String reportTag;
    /**
     * 开票规则： 1-国补金额优惠开票，2-国补金额优惠不开票
     */
    private Integer invoiceRule;
    /**
     * 开票主体：(深圳小米景明)
     */
    private Integer invoiceCompanyId;

    /**
     * 领券方式
     * 1-站内
     * 2-站外
     * 3-无需领券
     */
    private Integer fetchType;

    /**
     *使用指南
     */
    private String usageGuide;

    /**
     * 使用指南图片url
     */
    private String usageGuideImgUrl;

    /**
     * 活动url
     */
    private String activityUrl;

    public void setRegionList(List<Region> regionList) {
        this.regionList = regionList;
    }

    public void setReduceDiscount(long reduceDiscount) {
        this.reduceDiscount = reduceDiscount;
    }

    public void setMaxReducePrice(long maxReducePrice) {
        this.maxReducePrice = maxReducePrice;
    }

    public void setGoodsSpuGroupMap(Map<String, String> goodsSpuGroupMap) {
        this.goodsSpuGroupMap = goodsSpuGroupMap;
    }

    public void setCateLevel(String cateLevel) {
        this.cateLevel = cateLevel;
    }

    public void setSubsidyMode(Integer subsidyMode) {
        this.subsidyMode = subsidyMode;
    }

    public void setReportCity(Integer reportCity) {
        this.reportCity = reportCity;
    }

    public void setReportTag(String reportTag) {
        this.reportTag = reportTag;
    }

    public void setInvoiceRule(Integer invoiceRule) {
        this.invoiceRule = invoiceRule;
    }

    public void setInvoiceCompanyId(Integer invoiceCompanyId) {
        this.invoiceCompanyId = invoiceCompanyId;
    }

    public void setGovernmentRuleMap(Map<String, GovernmentRule> governmentRuleMap) {
        this.governmentRuleMap = governmentRuleMap;
    }
}
