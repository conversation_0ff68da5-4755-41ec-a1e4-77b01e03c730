package com.xiaomi.nr.promotion.activity;

import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.ActRespConverter;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * Created by wangweiyi on 2023/9/20
 */
@Slf4j
public abstract class AbstractOnsaleActivity extends AbstractActivityTool{
    @Override
    public abstract boolean load(AbstractPromotionConfig config) throws BizError;

    @Override
    public abstract ActivityDetail getActivityDetail();

    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) {
        PromotionInfo promotionInfo = buildDefaultPromotionInfo(context);
        promotionInfo.setFrequent(frequency != null ? frequency.getValue() : null);
        promotionInfo.setTotalLimitNum(actLimitNum);
        promotionInfo.setActivityMutexLimit(actMutexLimit ? BooleanEnum.YES.getValue() : BooleanEnum.NO.getValue());
        promotionInfo.setActivityMutex(actMutexes);
        promotionInfo.setNumLimitRule(ActRespConverter.convert(this.numLimitRule));
        return promotionInfo;
    }

    @Override
    public abstract Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError;


    @Override
    public abstract PromotionToolType getType();

    @Override
    protected abstract DSLStream getDSLDefinition();
}
