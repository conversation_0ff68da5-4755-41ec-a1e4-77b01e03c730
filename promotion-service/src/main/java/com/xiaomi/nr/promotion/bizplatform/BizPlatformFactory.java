package com.xiaomi.nr.promotion.bizplatform;

import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class BizPlatformFactory {

    private Map<BizPlatformEnum, BaseBizPlatform> bizPlatformMap=new HashMap<>();

    @Autowired
    public BizPlatformFactory(List<BaseBizPlatform> bizList){
        if (bizList==null){
            return;
        }
        for (BaseBizPlatform biz:bizList){
            bizPlatformMap.put(biz.getBiz(),biz);
        }
    }

    public BaseBizPlatform getExecutor(BizPlatformEnum bizPlatform){
        return bizPlatformMap.get(bizPlatform);
    }
}
