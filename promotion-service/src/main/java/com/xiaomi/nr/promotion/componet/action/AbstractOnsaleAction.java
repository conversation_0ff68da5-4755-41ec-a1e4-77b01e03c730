package com.xiaomi.nr.promotion.componet.action;


import com.google.gson.annotations.SerializedName;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.CartItemChild;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.entity.redis.LimitRule;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.OnsaleExtendInfo;
import com.xiaomi.nr.promotion.model.common.OnsaleJoin;
import com.xiaomi.nr.promotion.model.common.PromotionExtend;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.OnsalePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by wangweiyi on 2023/9/15
 */
@Slf4j
public abstract class AbstractOnsaleAction extends AbstractAction {

    /**
     * 活动ID
     */
    protected Long promotionId;
    /**
     * 活动类型
     */
    protected PromotionToolType promotionType;
    /**
     * 直降信息Map
     * key: skuPackage val:ActPriceInfo
     */
    protected Map<String, ActPriceInfo> onsaleInfoMap;
    /**
     * 活动限制. 数据值：0-不限制， 没有限制就没有对应字段
     */
    protected ActNumLimitRule numLimitRule;

    public abstract String getJoinGoods(CartItem cartItem);

    /**
     * 判断购物车是否可以参加直降
     *
     * @param cartItem     购物车项
     * @param canJoinItems 能参加活动的项
     * @return true/false
     */
    protected boolean canJoinOnSaleAct(CartItem cartItem, List<GoodsIndex> canJoinItems) {
        if (!CartHelper.filterOrderOk(cartItem)) {
            log.warn("inner.invalidCalculate");
            return false;
        }

        if (CollectionUtils.isEmpty(canJoinItems)) {
            return false;
        }
        // 任意匹配上就是匹配上了
        return canJoinItems.stream().anyMatch(item -> item.getItemId().equals(cartItem.getItemId()));
    }

    protected void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool, Map<String, OnsaleJoin> joinSkuMap) throws
                                                                                                                                 BizError {
        List<GoodsIndex> goodsIndices = context.getGoodIndex();
        List<String> parentItemList = CartHelper.getParentItemIdList(goodsIndices);
        List<String> joinedItemIdList = CartHelper.getJoinedItemIdList(goodsIndices, cartList);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setJoinedItemId(joinedItemIdList);
        promotionInfo.setParentItemId(parentItemList);
        promotionInfo.setExtend(generateActExpandInfo(joinSkuMap));
        promotionInfo.setJoinCounts(0);
        promotionInfo.setJoined(CollectionUtils.isNotEmpty(joinedItemIdList) ? 1 : 0);

        // 设置上下文结果
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    /**
     * 生成活动拓展信息
     *
     * @param onSaleJoinSkuMap 直降商品列表
     * @return 拓展信息Json字符串
     */
    private String generateActExpandInfo(Map<String, OnsaleJoin> onSaleJoinSkuMap) {
        List<OnsaleJoin> onSaleJoinList = new ArrayList<>(onSaleJoinSkuMap.values());

        OnsaleExtendInfo onSaleExtend = new OnsaleExtendInfo();
        onSaleExtend.setJoinExtend(onSaleJoinList);
        PromotionExtend extend = new PromotionExtend();
        extend.setOnsaleExtend(onSaleExtend);
        return GsonUtil.toJson(extend);
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof OnsalePromotionConfig)) {
            return;
        }
        OnsalePromotionConfig promotionConfig = (OnsalePromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        this.onsaleInfoMap = promotionConfig.getOnsaleInfoMap();
        this.numLimitRule = promotionConfig.getNumLimitRule();
        this.numLimitRule = promotionConfig.getNumLimitRule();
    }


    @Data
    public static class OnsaleCalcInfo {

        /**
         * 直降前价格
         */
        private long beforeOnsalePrice;


        /**
         * 直降价
         */
        private long onsalePrice;

        /**
         * sku或套装ID或ssuId
         */
        private Long productId = 0L;

        /**
         * 购物车id
         */
        private String cartItemId;

        /**
         * 参加个数
         */
        private Integer joinCounts = 0;

        /**
         * 限制条件
         */
        private LimitRule limitRule;
    }
}
