package com.xiaomi.nr.promotion.activity;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.GetProductActRequest;
import com.xiaomi.nr.promotion.api.dto.model.CheckGoodsItem;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.componet.action.PostFreeAction;
import com.xiaomi.nr.promotion.componet.condition.*;
import com.xiaomi.nr.promotion.componet.preparation.GlobalExcludePreparation;
import com.xiaomi.nr.promotion.componet.preparation.GoodsHierarchyPreparation;
import com.xiaomi.nr.promotion.componet.preparation.OrgInfoPreparation;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.PostFreePromotionConfig;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 包邮活动
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class PostFreeActivity extends AbstractActivityTool implements ActivityTool {
    /**
     * 阶梯数据
     */
    private List<QuotaLevel> levelList;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine
                .conditionPreparation(GoodsHierarchyPreparation.class)
                .conditionPreparation(GlobalExcludePreparation.class)
                .conditionPreparation(OrgInfoPreparation.class)
                .condition(DailyTimeCondition.class)
                .condition(OrgCondition.class)
                .condition(ChannelCondition.class)
                .condition(QuotaCondition.class)
                .condition(UserGroupCondition.class)
                .condition(OnlineJoinLimitCondition.class)
                .condition(FrequencyCondition.class)
                .action(PostFreeAction.class);
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.POST_FREE;
    }

    /**
     * 构建购物车优惠数据
     *
     * @param context 上下文
     * @return 内容
     */
    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) {
        Integer frequencyVal = frequency != null ? frequency.getValue() : null;

        PromotionInfo promotionInfo = buildDefaultPromotionInfo(context);
        promotionInfo.setFrequent(frequencyVal);
        promotionInfo.setTotalLimitNum(actLimitNum);
        return promotionInfo;
    }

    /**
     * 获取产品站信息
     *
     * @param clientId       应用ID
     * @param orgCode        门店Code
     * @param skuPackageList 活动类别
     */
    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        if (!checkProductGoodsActCondition(clientId, orgCode)) {
            log.debug("postFree condition is not match. actId:{} clientId:{} orgCode:{}", id, clientId, orgCode);
            return Collections.emptyMap();
        }
        List<String> joinedSkuPackageList = Lists.newArrayList(includeSkuPackages);
        joinedSkuPackageList.retainAll(skuPackageList);
        if (CollectionUtils.isEmpty(joinedSkuPackageList)) {
            log.debug("postFree joinedSkuPackageList retain empty. actId:{} clientId:{} orgCode:{}", id, clientId, orgCode);
            return Collections.emptyMap();
        }

        ProductActInfo productActInfo = new ProductActInfo();
        productActInfo.setType(type.getValue());
        productActInfo.setId(id);
        productActInfo.setName(name);
        productActInfo.setChannels(channels);
        productActInfo.setSelectClientList(selectClientList);
        productActInfo.setSelectOrgList(selectOrgList);
        productActInfo.setUnixStartTime(getUnixStartTime());
        productActInfo.setUnixEndTime(getUnixEndTime());
        return joinedSkuPackageList.stream().collect(Collectors.toMap(skuPackage -> skuPackage, skuPackage -> productActInfo));
    }

    /**
     * 获取活动详情
     *
     * @return 活动详情
     */
    @Override
    public ActivityDetail getActivityDetail() {
        return getBasicActivityDetail();
    }

    /**
     * 获取产品站活动优惠信息
     *
     * @param request   产品站信息
     * @param hierarchy 商品层级关系
     * @param isOrgTool 是否只检查门店
     * @return 优惠数据
     */
    @Override
    public PromotionInfo getProductAct(GetProductActRequest request, GoodsHierarchy hierarchy, boolean isOrgTool) throws BizError {
        if (!checkProductActCondition(request, isOrgTool)) {
            log.debug("postFree condition is not match. request:{}", request);
            return null;
        }
        PromotionInfo promotionInfo = getDefaultProductAct();
        promotionInfo.setExtend(GsonUtil.toJson(buildPromotionExtend()));
        promotionInfo.setQuotaEles(getQuotaEleList(levelList));
        promotionInfo.setPolicys(getPolicyList(levelList));
        promotionInfo.setPolicyNew(null);
        return promotionInfo;
    }

    @Override
    public List<CheckGoodsItem> checkProductsAct(CheckProductsActRequest request, Map<String, GoodsHierarchy> goodsHierarchyMap) throws BizError {
        return null;
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof PostFreePromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        PostFreePromotionConfig promotionConfig = (PostFreePromotionConfig) config;
        this.levelList = promotionConfig.getLevelList();
        return true;
    }
}
