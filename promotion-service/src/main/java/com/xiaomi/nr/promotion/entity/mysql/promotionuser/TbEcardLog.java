package com.xiaomi.nr.promotion.entity.mysql.promotionuser;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡日志
 *
 * <AUTHOR>
 * @date 2021/5/7
 */
@Data
public class TbEcardLog {
    /**
     * ecard编号
     */
    private Long cardId;
    /**
     * 添加时间 秒
     */
    private Long addTime;

    /**
     * Hash 码
     */
    private String hashCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 退款单ID
     */
    private Long refundNo;

    /**
     * 日志类型：-1系统异常日志，0系统正常日志，1消费（含退款）
     */
    private int logType;

    /**
     * '金额变化' 更新 有正负
     */
    private BigDecimal income;

    /**
     * 老余额
     */
    private BigDecimal oldBalance;

    /**
     * 新余额
     */
    private BigDecimal newBalance;

    /**
     * 操作人ID，系统操作为0
     */
    private Long operatorId;

    /**
     * '描述', 下单关单+clientid
     */
    private String description;

    /**
     * 最后更新时间
     */
    private Date lastUpdateTime;

    /**
     * 0 线下使用
     */
    private Integer offline;
}