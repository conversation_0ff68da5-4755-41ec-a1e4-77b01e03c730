package com.xiaomi.nr.promotion.flows.facade;

import com.google.common.collect.Lists;
import com.xiaomi.nr.md.promotion.admin.api.constant.PromotionTypeEnum;
import com.xiaomi.nr.promotion.activity.pool.ActSearchParam;
import com.xiaomi.nr.promotion.activity.pool.ActivityPool;
import com.xiaomi.nr.promotion.activity.pool.CarActivitySearcher;
import com.xiaomi.nr.promotion.activity.pool.CarShopActivitySearcher;
import com.xiaomi.nr.promotion.api.dto.enums.ProductDepartmentEnum;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.OnsaleProductRuleDto;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.PromotionPriceNormProvider;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.flows.PromotionSceneFlowEngine;
import com.xiaomi.nr.promotion.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.xiaomi.nr.promotion.constant.ApplicationConstant.CONTEXT_PARAM_ORDER_TIME;
import static com.xiaomi.nr.promotion.constant.ApplicationConstant.CONTEXT_PARAM_USER_LEVEL;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class PromotionPriceServiceFacade {

    @Autowired
    private ActivityPool activityPool;
    @Autowired
    private PromotionSceneFlowEngine promotionSceneFlowEngine;

    @Autowired
    private CarActivitySearcher carActivitySearcher;
    @Autowired
    private CarShopActivitySearcher carShopActivitySearcher;


    public void handlePromotionPrice(Integer channel, GoodsDto goodsDto, Map<Long, PromotionPriceDTO> priceMap, String userLevel, Long orderTime) {
        ArrayList<String> skuPackageList = Lists.newArrayList(String.valueOf(goodsDto.getSsuId()), String.valueOf(goodsDto.getId()));
        // 团购渠道，且是主附品强绑定,将子节点中主附品sku加入检索列表
        if (isBindMainAccessory(goodsDto.getDepartment(), goodsDto.getBindMainAccessory())) {
            // 团购主附品强绑定场景下，一定有两个子品，前面已经校验
            for (CartItemChild child : goodsDto.getChilds()) {
                skuPackageList.add(child.getSku());
            }
        }
        // 活动检索
        List<ActivityTool> activityTools = getActivityTools(channel, goodsDto, skuPackageList);

        // 填充上下文参数
        Map<String, String> params = fillContextParams(userLevel, orderTime);

        // 活动处理
        for (ActivityTool activityTool : activityTools) {
            if (!(activityTool instanceof PromotionPriceNormProvider)) {
                continue;
            }
            PromotionPriceNormProvider priceNormProvider = (PromotionPriceNormProvider) activityTool;

            boolean valid = priceNormProvider.checkCondition(params);
            if (!valid) {
                continue;
            }
            Map<Long, PromotionPriceDTO> priceDTOMap = priceNormProvider.getGoodsPromotionPrice(Collections.singletonList(goodsDto), params);
            PromotionPriceDTO priceDTO = priceDTOMap.get(goodsDto.getSsuId());
            if (priceDTO == null) {
                return;
            }
            // 合并
            priceMap.merge(goodsDto.getSsuId(), priceDTO, (price1, price2) -> {
                List<PromotionInfoDTO> promotionInfos = new ArrayList<>();
                promotionInfos.addAll(Optional.ofNullable(price1.getPromotionInfos()).orElse(Collections.emptyList()));
                promotionInfos.addAll(Optional.ofNullable(price2.getPromotionInfos()).orElse(Collections.emptyList()));
                if (price1.getPrice() == null) {
                    price2.setPromotionInfos(promotionInfos);
                    return price2;
                }
                if (price2.getPrice() == null) {
                    price1.setPromotionInfos(promotionInfos);
                    return price1;
                }
                if (price1.getPrice() <= price2.getPrice()) {
                    price1.setPromotionInfos(promotionInfos);
                    return price1;
                }
                price2.setPromotionInfos(promotionInfos);
                return price2;
            });
        }
        // 过滤无效的直降活动优惠信息
        filterOnSalePromotionInfo(goodsDto, priceMap);
    }


    private List<ActivityTool> getActivityTools(Integer channel, GoodsDto goodsDto, ArrayList<String> skuPackageList) {
        List<ActivityTool> activityTools;
        BizPlatformEnum platform = BizPlatformEnum.findByChannel(channel);
        switch (platform) {
            case CAR:
            case CAR_SHOP:
                activityTools = getCarActivityToolList(channel, goodsDto);
                break;
            default:
                activityTools = activityPool.getCurrent(null, null, Collections.singletonList(channel), skuPackageList,
                        Collections.singletonList(goodsDto.getDepartment()));
                // 根据场景过滤
                activityTools = promotionSceneFlowEngine.filterSceneAct(activityTools, channel, goodsDto.getGoodsType(), goodsDto.getDepartment());
                break;
        }
        return activityTools;
    }

    private Map<String, String> fillContextParams(String userLevel, Long orderTime) {
        Map<String, String> params = new HashMap<>();
        if(StringUtils.isNotEmpty(userLevel)){
            params.put(CONTEXT_PARAM_USER_LEVEL, userLevel);
        }
        if (Objects.nonNull(orderTime) && orderTime > 0) {
            params.put(CONTEXT_PARAM_ORDER_TIME, String.valueOf(orderTime));
        }
        return params;
    }

    private List<ActivityTool> getCarActivityToolList(Integer channel, GoodsDto goodsDto) {
        List<ActivityTool> activityTools = new ArrayList<>();
        List<CartItem> cartItemList = new ArrayList<>();
        CartItem cartItem = new CartItem();
        cartItem.setSsuId(goodsDto.getSsuId());
        cartItemList.add(cartItem);
        if (BizPlatformEnum.findByChannel(channel).equals(BizPlatformEnum.CAR_SHOP)) {
            ActSearchParam param = new ActSearchParam()
                    .setChannel(channel)
                    .setGoodsList(carShopActivitySearcher.createSearchGoods(cartItemList));
            activityTools = carShopActivitySearcher.searchCarActivity(param);
        }
        if (BizPlatformEnum.findByChannel(channel).equals(BizPlatformEnum.CAR)) {
            ActSearchParam param = new ActSearchParam()
                    .setChannel(channel)
                    .setGoodsList(carActivitySearcher.createSearchGoods(cartItemList));
            activityTools = carActivitySearcher.searchCarActivity(param);
        }
        return activityTools;
    }

    private void filterOnSalePromotionInfo(GoodsDto goodsDto, Map<Long, PromotionPriceDTO> priceMap) {
        // 对于promotionInfos信息，商品B端只用到了promotionType、rule（promotionPrice），用不到 id、startTime、endTime
        // 对于直降活动，只保留最低价格的活动即可
        if (priceMap == null || priceMap.get(goodsDto.getSsuId()) == null) {
            return;
        }
        List<PromotionInfoDTO> promotionInfos = priceMap.get(goodsDto.getSsuId()).getPromotionInfos();

        // 获取直降价优惠信息
        List<PromotionInfoDTO> onSalePromotionList = promotionInfos.stream().filter(f -> f.getPromotionType().equals(PromotionTypeEnum.ONSALE.code)).collect(Collectors.toList());
        if (onSalePromotionList.size() > 1) {
            // 删除原有直降价优惠信息
            promotionInfos.removeIf(info -> info.getPromotionType().equals(PromotionTypeEnum.ONSALE.code));
            // 获取最小直降价
            Long minPromotionPrice = goodsDto.getPrice();
            for (PromotionInfoDTO promotionInfo : onSalePromotionList) {
                OnsaleProductRuleDto onSaleProductRule = GsonUtil.fromJson(promotionInfo.getRule(), OnsaleProductRuleDto.class);
                if (onSaleProductRule != null && onSaleProductRule.getPromotionPrice() != null) {
                    minPromotionPrice = Math.min(minPromotionPrice, onSaleProductRule.getPromotionPrice());
                }
            }
            // 重新构造直降优惠信息
            PromotionInfoDTO promotionInfoDTO = new PromotionInfoDTO();
            promotionInfoDTO.setPromotionType(PromotionTypeEnum.ONSALE.code);
            OnsaleProductRuleDto onsaleProductRuleDto = new OnsaleProductRuleDto();
            onsaleProductRuleDto.setPromotionPrice(minPromotionPrice);
            promotionInfoDTO.setRule(GsonUtil.toJson(onsaleProductRuleDto));
            // 填充直降优惠信息
            promotionInfos.add(promotionInfoDTO);
        }
    }

    private boolean isBindMainAccessory(Integer department, Boolean bindMainAccessory) {
        if (department == null || bindMainAccessory == null) {
            return false;
        }
        return department.equals(ProductDepartmentEnum.S2.getValue()) && bindMainAccessory;
    }
}
