package com.xiaomi.nr.promotion.domain.ecard.service.mishop;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.constant.EcardConstant;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.TbEcard;
import com.xiaomi.nr.promotion.entity.redis.*;
import com.xiaomi.nr.promotion.enums.EcardBindStateEnum;
import com.xiaomi.nr.promotion.enums.EcardTypeEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.domain.ecard.model.EcardInfos;
import com.xiaomi.nr.promotion.domain.ecard.model.UserEcard;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.external.*;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.provider.EcardProvider;
import com.xiaomi.nr.promotion.util.*;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 礼品卡结算实现
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
@Slf4j
@Service
public class EcardService {
    @Autowired
    private ResourceProviderFactory resourceProviderFactory;

    /**
     * 处理礼品卡
     * 先不处理北京消费券
     * <p>
     * 需要确认问题点
     * 1.抛异常问题，是否整个请求失败？
     * 2.小单号和订单号的关系
     * 3.ecardlog条数和ecard数是否一定相等，（注意EcardConsumeItem）
     * 4.缓存数据到对象的字段命名
     *
     * @param request  业务请求
     * @param response 业务响应
     * @param context  结算上下文
     * @throws BizError 业务异常
     */
    public void checkout(CheckoutPromotionRequest request, CheckoutPromotionResponse response, CheckoutContext context) throws BizError {
        // 没有传入礼品卡，直接返回 || 获取使用的是北京消费券
        if (CollectionUtils.isEmpty(request.getEcardIds()) || request.getUseBeijingcoupon()) {
            return;
        }

        long uid = request.getUserId();
        long startTime = System.currentTimeMillis();
        log.info("ecard service checkout ecard start. startTime:{} ecardIds:{} uid:{}", startTime, request.getEcardIds(), uid);
        try {
            // 获取礼品卡信息
            List<UserEcard> userEcardList = getUserEcards(context.getExternalDataMap());
            log.debug("ecard service get ecard info from external data. uid:{}, ecardIds:{}", uid, request.getEcardIds());
            if (CollectionUtils.isEmpty(userEcardList)) {
                log.warn("uid {} has no available ecards. ", request.getUserId());
                throw ExceptionHelper.create(ErrCode.ERR_INVALID_ECARD, "获取礼品卡失败");
            }

            // 获取clientId信息
            ClientStc clientStc = getClientInfo(context.getExternalDataMap());
            log.debug("ecard service get client info from external data. uid:{}, clientId:{}", uid, request.getClientId());

            // 检查礼品卡
            checkUserEcard(userEcardList, uid, clientStc);

            // 获取满足礼品卡信息数据
            List<GoodsIndex> indexList = isEcardCacheCondFill(userEcardList, request, context);

            if (CollectionUtils.isEmpty(indexList)) {
                log.warn("uid:{} has no available ecards.", uid);
                throw ExceptionHelper.create(ErrCode.ERR_INVALID_ECARD, "没有可以使用礼品卡的商品");
            }

            // 构建新礼品卡详情
            List<Ecard> eCardList = buildNewEcardList(uid, userEcardList, request.getEcardConsumeDetail());

            // 购物车
            List<CartItem> cartItemList = request.getSourceApi() == SourceApi.SUBMIT ? context.getCarts() :
                    request.getCartList();

            // 扣减试算改卡金额
            countIncomeInCheckout(eCardList, indexList, cartItemList, context);

            // 响应结果
            String ecardBaseInfo = generateEcardBaseInfo(userEcardList);
            context.setECardBaseInfo(ecardBaseInfo);
            List<String> fullGoodsItems = indexList.stream().map(GoodsIndex::getItemId).collect(Collectors.toList());
            context.setCardFullGoods(GsonUtil.toJson(fullGoodsItems));
            context.setECardInfoList(eCardList);
            context.setIsOnline(Objects.requireNonNull(clientStc).getExt().getIsOnline());

            // 下单且需要落库则初始化资源
            if (!request.getNoSaveDbSubmit() && request.getSourceApi() == SourceApi.SUBMIT) {
                initResource(request, context, eCardList);
            }

            log.info("checkout ecard end. ws:{} ecardIds:{} uid:{}", System.currentTimeMillis() - startTime, request.getEcardIds(), uid);
        } catch (Exception e) {
            log.error("ecard checkout error. request:{}", request, e);
            if (e instanceof BizError) {
                context.setEcardBaseInfoInvalid(((BizError) e).getMsg());
            }
            if (request.getSourceApi() == SourceApi.SUBMIT) {
                log.error("ecard submit error. request:{}", request, e);
                throw e;
            }
        }
    }

    private String generateEcardBaseInfo(List<UserEcard> userEcardList) {
        List<EcardInfos> ecardInfosList = new ArrayList<>();
        for (UserEcard userEcard : userEcardList) {
            EcardInfos ecardInfos = new EcardInfos();
            ecardInfos.setId(userEcard.getTbEcard().getCardId());
            ecardInfos.setBalance(userEcard.getTbEcard().getBalance());
            ecardInfos.setEcardBaseInfo(userEcard.getEcardTotalType().getBasetype());
            ecardInfosList.add(ecardInfos);
        }
        return GsonUtil.toJson(ecardInfosList);
    }

    private void checkUserEcard(List<UserEcard> userEcardList, Long uid, ClientStc clientStc) throws BizError {
        if (CollectionUtils.isEmpty(userEcardList)) {
            log.warn("check user ecard. user ecard list is empty. uid:{}", uid);
            return;
        }
        for (UserEcard itemA : userEcardList) {
            //校验礼品卡基本信息
            TbEcard tbEcard = itemA.getTbEcard();
            checkEcardBaseInfo(tbEcard, uid, clientStc);
            for (UserEcard itemB : userEcardList) {
                // 校验礼品卡类型信息类型
                checkEcardType(itemA.getEcardTotalType(), tbEcard.getCardId(), uid, itemB.getEcardTotalType(), clientStc);
            }
        }
    }

    private void checkEcardBaseInfo(TbEcard tbEcard, Long userId, ClientStc clientStc) throws BizError {
        if (tbEcard.getTypeId() == null) {
            log.error("check: invalid ecard {} for uid {} type_id IS NULL", tbEcard.getCardId(), userId);
            throw ExceptionHelper.create(ErrCode.ERR_ECARD_TYPE_ID, "礼品卡typeId为空");
        }
        // 类型
        if (EcardTypeEnum.TYPE_ID_EXPRESS.getVal() == tbEcard.getTypeId()) {
            log.error("check: invalid recycle ecard {} for uid {} 快递补偿卡", tbEcard.getCardId(), userId);
            throw ExceptionHelper.create(ErrCode.ERR_INVALID_ECARD, "礼品卡不能是快递补偿卡");
        }
        // 余额
        if (new BigDecimal(0).compareTo(tbEcard.getBalance()) > 0) {
            log.warn("check: invalid ecard {} for uid {} ", tbEcard.getCardId(), userId);
            throw ExceptionHelper.create(ErrCode.ERR_INVALID_ECARD, "礼品卡没有余额");
        }

        // 状态校验
        if (tbEcard.getStat() != EcardBindStateEnum.BIND.getValue()) {
            log.warn("check: invalid ecard {} for uid {} ", tbEcard.getCardId(), userId);
            throw ExceptionHelper.create(ErrCode.ERR_INVALID_ECARD, "礼品卡状态未绑定");
        }

        // 时间
        long nowTime = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        if (tbEcard.getStartTime() == null || tbEcard.getEndTime() == null
                || nowTime < tbEcard.getStartTime() || nowTime >= tbEcard.getEndTime()) {
            log.warn("check: invalid time {} for uid {} ", tbEcard.getCardId(), userId);
            throw ExceptionHelper.create(ErrCode.ERR_INVALID_ECARD_TIME, "礼品卡只能在指定时间段使用");
        }
        // 是否锁定
        if (tbEcard.getIsLocked()) {
            log.warn("check:  ecard {} is not unlock， uid {} ", tbEcard.getCardId(), userId);
            throw ExceptionHelper.create(ErrCode.ERR_INVALID_ECARD, "礼品卡已锁定");
        }

        // 线下(clientId==0)只能用临时卡(is_casual==0)
        if (clientStc.getExt().getIsOnline() == 0 && tbEcard.getIsCasual() != 0) {
            log.warn("client id {} should be offline, but ecard {} is_casual is {} ", clientStc.getClientId(), tbEcard.getCardId(), tbEcard.getIsCasual());
            throw ExceptionHelper.create(ErrCode.ERR_INVALID_CHANNEL, "clientID为线下，但临时卡状态错误");
        }
        // 线上商城不允许使用回收礼品卡
        Long typeId = tbEcard.getTypeId() == null ? 0L : tbEcard.getTypeId().longValue();
        if (clientStc.getExt().getIsOnline() != 0 && EcardConstant.STORE_RECYCLE_TYPES.contains(typeId)) {
            log.warn("client id {} should be offline, but ecard {} typeId is {} ", clientStc.getClientId(), tbEcard.getCardId(), tbEcard.getTypeId());
            throw ExceptionHelper.create(ErrCode.ERR_INVALID_CHANNEL, "当前换新券仅支持米家渠道使用");
        }
    }

    private void checkEcardType(EcardTotalType ecardTotalType, Long ecardId, Long userId, EcardTotalType firstEcardTotalType, ClientStc clientStc) throws BizError {
        EcardBaseType baseType = ecardTotalType.getBasetype();
        EcardConditionType conditionType = ecardTotalType.getCondition();
        if (baseType == null || conditionType == null) {
            log.error("baseType or conditionType is null ecardId:{}, typeId:{}, userId:{}", ecardId, baseType.getId(), userId);
            throw ExceptionHelper.create(ErrCode.ERR_ECARD_TYPE_INFO, "礼品卡类型信息错误");
        }

        List<CompareItem> goodsInclude = conditionType.getGoodsInclude();
        if (goodsInclude == null || goodsInclude.size() != 1) {
            log.error("invalid include goods for ecardId:{}, typeId:{}, userId:{}", ecardId, baseType.getId(), userId);
            throw ExceptionHelper.create(ErrCode.ERR_INVALID_ECARD_INCLUDEGOODS, "条件类型里包含商品只能是1组");
        }

        // 当前元素和第一个去比较
        EcardBaseType ecardType = baseType;
        EcardBaseType firstEcardType = firstEcardTotalType.getBasetype();
        if (!Objects.equals(ecardType.getEcardType(), firstEcardType.getEcardType())) {
            if (!canMixedUse(ecardType.getId(), clientStc.getExt().getIsOnline())|| !canMixedUse(firstEcardType.getId(), clientStc.getExt().getIsOnline())) {
                log.warn("uid {} exists invalid ecard id {}, ecardType:{} firstEcardType:{}", userId, ecardId, ecardType, firstEcardType);
                throw ExceptionHelper.create(ErrCode.ERR_SEVERAL_ECARDKIND, "仅支持同时使用一类礼品卡");
            }
        }
    }

    /**
     * 门店换新礼品卡可以混用
     * 51礼品卡在门店渠道下可以混用
     * @return
     */
    private boolean canMixedUse(Long ecardId, Integer isOnline) {
        if (EcardConstant.STORE_RECYCLE_TYPES.contains(ecardId)) {
            return true;
        }
        if (isOnline == 0 && EcardConstant.OFFLINE_MIXED_RECYCLE_TYPES.contains(ecardId)) {
            return true;
        }
        return false;
    }

    private List<GoodsIndex> isEcardCacheCondFill(List<UserEcard> userEcardList, CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        List<Integer> idxList = new ArrayList<>();

        for (UserEcard userEcard : userEcardList) {
            TbEcard tbEcard = userEcard.getTbEcard();
            EcardTotalType ecardTotalType = userEcard.getEcardTotalType();
            EcardConditionType ecardCondition = ecardTotalType.getCondition();

            // 获取当前礼品卡支持的物品在购物车的下标
            List<Integer> currentIdxList = getCurGoodsIndex(request, context, tbEcard, ecardCondition);
            if (idxList.isEmpty()) {
                idxList = currentIdxList;
                continue;
            }

            // 判断所有卡适用的商品index是否一致
            if (!isSameList(idxList, currentIdxList)) {
                log.error("uid:{} diff goods index for ecard id:{}, old:{}", request.getUserId(), tbEcard.getCardId(), idxList);
                throw ExceptionHelper.create(ErrCode.ERR_INVALID_ECARD, "礼品卡" + tbEcard.getCardId() + "的适用范围不一致");
            }
        }
        return idxList.stream()
                .map(idx -> new GoodsIndex(request.getCartList().get(idx).getItemId(), idx))
                .collect(Collectors.toList());
    }

    private List<Integer> getCurGoodsIndex(CheckoutPromotionRequest request, CheckoutContext context, TbEcard tbEcard, EcardConditionType ecardCondition) throws BizError {
        List<Integer> indexList = new ArrayList<>();
        List<CartItem> cartList = request.getCartList();
        for (int i = 0; i < cartList.size(); i++) {
            CartItem item = cartList.get(i);
            boolean fill = false;
            //SKU
            if (StringUtils.isNotBlank(item.getSku())) {
                fill = conditionCheckSKU(item, tbEcard, ecardCondition, context, request.getUserId());
            }
            //套装
            if (StringUtils.isNotBlank(item.getPackageId())) {
                fill = conditionCheckPackage(item, tbEcard, ecardCondition, context, request.getUserId());
            }
            if (fill) {
                indexList.add(i);
            }
        }
        return indexList;
    }

    //校验SKU condition
    private boolean conditionCheckSKU(CartItem item, TbEcard userEcard, EcardConditionType ecardCondition, CheckoutContext context, Long userId) throws BizError {
        Map<String, GoodsHierarchy> hierarchyMap = getGoodsHierarchy(context.getExternalDataMap());
        GoodsHierarchy cpc = hierarchyMap.get(item.getSku());
        if (cpc == null) {
            log.error("uid {} item {} unknown for ecard id {}", userId, item.getSku(), userEcard.getCardId());
            throw ExceptionHelper.create(ErrCode.ERR_UNKNOWN_GOODS, "购物车存在未知商品");
        }
        // 必须包含
        if (!CompareHelper.isFillInclude(cpc, ecardCondition.getGoodsNeed(), Boolean.FALSE)) {
            log.error("uid:{} item:{} not fill include goods need for ecard id:{}", userId, item.getSku(), userEcard.getCardId());
            throw ExceptionHelper.create(ErrCode.ERR_GOODS_NEED, "未满足必须包含商品条件");
        }
        // 包含
        if (!CompareHelper.isFillInclude(cpc, ecardCondition.getGoodsInclude().get(0), Boolean.FALSE)) {
            log.error("uid:{} item:{} not fill include goods for ecard id:{}", userId, item.getSku(), userEcard.getCardId());
            throw ExceptionHelper.create(ErrCode.ERR_ECARD_INCLUDE_GOODS, "sku: " + item.getSku());
        }
        // 排除
        if (CompareHelper.isFillInExclude(cpc, ecardCondition.getGoodsInexclude(), null, null)) {
            log.error("uid:{} item:{} fill exclude goods for ecard id:{}", userId, item.getSku(), userEcard.getCardId());
            throw ExceptionHelper.create(ErrCode.ERR_ECARD_INCLUDE_GOODS, "sku: " + item.getSku());
        }
        return true;
    }

    //校验package condition
    private boolean conditionCheckPackage(CartItem item, TbEcard userEcard, EcardConditionType ecardCondition, CheckoutContext context, Long userId) throws BizError {
        Map<String, GoodsHierarchy> hierarchyMap = getGoodsHierarchy(context.getExternalDataMap());
        GoodsHierarchy cpc = hierarchyMap.get(item.getPackageId());
        if (cpc == null) {
            log.error("uid {} item {} unknown for ecard id {} packageId:{}", userId, item.getSku(), userEcard.getCardId(), item.getPackageId());
            throw ExceptionHelper.create(ErrCode.ERR_UNKNOWN_GOODS, "购物车存在未知商品");
        }

        //校验套装的childs
        packgeChildsCheck(item, userEcard.getCardId(), userId);

        if (!CompareHelper.isFillInclude(cpc, ecardCondition.getGoodsNeed(), Boolean.FALSE)) {
            throw ExceptionHelper.create(ErrCode.ERR_GOODS_NEED, "未满足必须包含商品条件");
        }
        if (!CompareHelper.isFillInclude(cpc, ecardCondition.getGoodsInclude().get(0), Boolean.FALSE)
                || CompareHelper.isFillInExclude(cpc, ecardCondition.getGoodsInexclude(), null, null)) {
            throw ExceptionHelper.create(ErrCode.ERR_ECARD_INCLUDE_GOODS, "sku: " + item.getSku());
        }
        return true;
    }

    //校验套装的childs
    private void packgeChildsCheck(CartItem item, Long cardId, Long userId) throws BizError {
        if (CollectionUtils.isEmpty(item.getChilds())) {
            log.error("uid {} item {} empty Childs for ecard id{}", userId, item.getSku(), cardId);
            throw ExceptionHelper.create(ErrCode.ERR_INVALID_PACKAGE, "child不能为空");
        }
        Long childPrices = 0L;
        for (CartItemChild child : item.getChilds()) {
            if (StringUtils.isBlank(child.getSku())) {
                log.error("user {} empty package sku for ecard id{}", userId, cardId);
                throw ExceptionHelper.create(ErrCode.ERR_INVALID_PACKAGE, "child sku不能为空");
            }
            if (child.getCount() != 1) {
                log.error("user {} package sku {} is not 1 for ecard id{}", userId, child.getSku(), cardId);
                throw ExceptionHelper.create(ErrCode.ERR_INVALID_PACKAGE, "child count只能为1");
            }
            if (child.getSellPrice() < 0) {
                log.error("user {} package sku {} invalid sellprice for ecard id{}", userId, child.getSku(), cardId);
                throw ExceptionHelper.create(ErrCode.ERR_INVALID_PACKAGE, "child sellprice不能小于0");
            }
            childPrices += child.getSellPrice();
        }
        // 套装里所有child的sellprice之和要>0
        if (childPrices == 0) {
            log.error("user {} invalid child sellprice for ecard id{}", userId, cardId);
            throw ExceptionHelper.create(ErrCode.ERR_INVALID_PACKAGE, "套装里所有child的sellprice之和必须大于0");
        }
    }

    private List<Ecard> buildNewEcardList(Long uid, List<UserEcard> userEcardList, Map<String, EcardConsumeItemList> ecardConsumeDetail) throws BizError {

        List<Ecard> eCardVoList = new ArrayList<>();
        // 记录小单数量
        int csNum = 0;
        for (int k = 0; k < userEcardList.size(); k++) {
            UserEcard userEcard = userEcardList.get(k);
            TbEcard tbEcard = userEcard.getTbEcard();
            // 礼品卡余额，单位分
            Long balance = NumberUtil.amountConvertY2F(tbEcard.getBalance());
            Ecard eCardVo = new Ecard();
            eCardVo.setECardId(tbEcard.getCardId());
            eCardVo.setBalanceOld(balance);
            eCardVo.setBalanceNew(eCardVo.getBalanceOld());
            eCardVo.setTypeCode(userEcard.getEcardTotalType().getBasetype().getEcardType());
            eCardVo.setTypeId(userEcard.getEcardTotalType().getBasetype().getId());
            eCardVo.setInvoice(userEcard.getEcardTotalType().getBasetype().getInvoice());

            // 小单扣费详情不为空
            if (MapUtils.isNotEmpty(ecardConsumeDetail)) {
                EcardConsumeItemList consumeItemList = ecardConsumeDetail.get(String.valueOf(tbEcard.getCardId()));
                if (consumeItemList == null) {
                    log.error("EcardConsumeDetails is invalid. uid:{}, ecardId:{} detail is not exist.", uid, tbEcard.getCardId());
                    throw ExceptionHelper.create(ErrCode.ERR_INVALID_ECARD, "礼品卡小单扣费详情与礼品卡不一致");
                }

                // err（小单扣减 > 余额） ｜ （小单扣减 != 余额 && 不是最后一张礼品卡）
                Long consumeTotalAmount = consumeItemList.getTotalAmount();
                if (consumeTotalAmount > balance
                        || (!Objects.equals(consumeTotalAmount, balance) && k != userEcardList.size() - 1)) {
                    log.error("TotalCost of sOrderid is illegal [CardID: {}][TotalAmount: {}][EcardBalance: {}]",
                            tbEcard.getCardId(), consumeTotalAmount, balance);
                    throw ExceptionHelper.create(ErrCode.ERR_ECARD_CONSUME_BALANCE, "礼品卡小单扣费详情金额校验失败");
                }
                eCardVo.setConsumeList(consumeItemList.getConsumeList());
                csNum++;
            }

            eCardVoList.add(eCardVo);
        }
        // 礼品卡小单详情有多余的礼品卡
        if (MapUtils.isNotEmpty(ecardConsumeDetail) && ecardConsumeDetail.size() != csNum) {
            log.error("sorder: some of sorder detail is unnecessary. uid:{}", uid);
            throw ExceptionHelper.create(ErrCode.ERR_ECARD_SORDER_PARAM, "礼品卡小单扣费详情有未使用的礼品卡");
        }
        return eCardVoList;
    }

    private void countIncomeInCheckout(List<Ecard> ecardList, List<GoodsIndex> itemIdList, List<CartItem> cartList, CheckoutContext context) throws BizError {
        // 购物车可以使用礼品卡的金额
        Long income = 0L;
        for (GoodsIndex goodsIndex : itemIdList) {
            CartItem cartItem = cartList.get(goodsIndex.getIndex());
            if (!goodsIndex.getItemId().equals(cartItem.getItemId())) {
                log.error("the GoodsIndex is wrong! index:{}, itemId:{}", goodsIndex.getIndex(), goodsIndex.getItemId());
                throw ExceptionHelper.create(ErrCode.ERR_CART_ITEMID_MATCH, "礼品卡计算，可用商品购物车itemId不匹配");
            }
            if (cartItem.getCannotUseEcard()) {
                continue;
            }
            Long itemCurPrice = CartHelper.itemCurPrice(cartItem);
            if (cartItem.getMaxUseEcardAmount() > 0) {
                income += Math.min(cartItem.getMaxUseEcardAmount(), itemCurPrice);
            } else {
                income += itemCurPrice;
            }
        }
        // 礼品卡实际可以扣减的金额
        Long reduce = Math.min(income, EcardHelper.getEcardListTotalBalance(ecardList));
        for (Ecard eCard : ecardList) {
            eCard.setEcardGoodsAmount(income);
            if (eCard.getBalanceOld() <= 0 || reduce <= 0) {
                log.error("ecard balance is not enough or too many ecard. ecard:{}", eCard.getECardId());
                throw ExceptionHelper.create(ErrCode.ERR_ECARD_TOOMANY, "无需使用礼品卡：" + eCard.getECardId());
            }
            // 当前礼品卡可以扣减的金额
            Long cost;
            if (eCard.getBalanceOld() >= reduce) {
                cost = reduce;
            } else {
                cost = eCard.getBalanceOld();
            }
            reduce -= cost;
            eCardConsume(eCard, cost);
        }
        if (reduce > 0) {
            log.error("reduce is positive");
            throw ExceptionHelper.create(ErrCode.ERR_ECARD_SORDER_CAL, "礼品卡实际可扣减的金额还有剩余");
        }
    }

    //礼品卡试扣减
    private void eCardConsume(Ecard eCard, Long cost) throws BizError {
        // 礼品卡小单扣减计算
        if (CollectionUtils.isNotEmpty(eCard.getConsumeList())) {
            Long sum = 0L;
            for (EcardConsumeItem item : eCard.getConsumeList()) {
                sum += item.getMoney();
            }
            if (!sum.equals(cost)) {
                log.error("Sorder-Acount is different from Pulse-Acount [CardID: {}, SorderAcount: {}, PulseAcount: {}]",
                        eCard.getECardId(), sum, cost);
                throw ExceptionHelper.create(ErrCode.ERR_ECARD_SORDER_CAL, "礼品卡小单计算金额错误");
            }
        }
        eCard.setBalanceNew(eCard.getBalanceOld() - cost);
        eCard.setIncome(-cost);
    }

    private boolean isSameList(List<Integer> idxAll, List<Integer> curGoodsIndex) {
        if (idxAll.size() != curGoodsIndex.size()) {
            return false;
        }
        return idxAll.containsAll(curGoodsIndex);
    }

    private Map<String, GoodsHierarchy> getGoodsHierarchy(Map<ResourceExtType, ExternalDataProvider<?>> providerMap) throws BizError {
        GoodsHierarchyExternalProvider provider = (GoodsHierarchyExternalProvider) providerMap.get(ResourceExtType.GOODS_HIERARCHY);
        if (null == provider) {
            return new HashMap<>();
        }
        return provider.getData();
    }

    private void initResource(CheckoutPromotionRequest request, CheckoutContext context, List<Ecard> eCardList) throws BizError {
        EcardProvider.EcardContent ecardContent = buildEcardRes(request, context, eCardList);
        ResourceObject<EcardProvider.EcardContent> resourceObject = buildResource(ecardContent, request.getOrderId(), request.getUserId());
        EcardProvider ecardProvider = (EcardProvider) resourceProviderFactory.getProvider(ResourceType.ECARD);
        ecardProvider.initResource(resourceObject);
        context.getResourceHandlers().add(ecardProvider);
    }

    private EcardProvider.EcardContent buildEcardRes(CheckoutPromotionRequest request, CheckoutContext context, List<Ecard> eCardList) {
        EcardProvider.EcardContent ecardContent = new EcardProvider.EcardContent();
        ecardContent.setUserId(request.getUserId());
        ecardContent.setEcardList(eCardList);
        ecardContent.setOrderId(request.getOrderId());
        ecardContent.setClientId(request.getClientId());
        ecardContent.setOffline(context.getIsOnline());
        return ecardContent;
    }

    private ResourceObject<EcardProvider.EcardContent> buildResource(EcardProvider.EcardContent content, Long orderId, Long userId) {
        ResourceObject<EcardProvider.EcardContent> resourceObject = new ResourceObject<>();
        resourceObject.setContent(content);
        resourceObject.setResourceId(String.format("ecard_%s_%s", orderId, userId));
        resourceObject.setResourceType(ResourceType.ECARD);
        resourceObject.setPromotionId(-1L);
        resourceObject.setOrderId(orderId);
        resourceObject.setPid(-1L);
        return resourceObject;
    }

    private List<UserEcard> getUserEcards(Map<ResourceExtType, ExternalDataProvider<?>> providerMap) throws BizError {
        EcardExternalProvider provider = (EcardExternalProvider) providerMap.get(ResourceExtType.ECARD);
        if (null == provider) {
            return null;
        }
        return provider.getData();
    }

    private ClientStc getClientInfo(Map<ResourceExtType, ExternalDataProvider<?>> providerMap) throws BizError {
        ClientInfoExternalProvider provider = (ClientInfoExternalProvider) providerMap.get(ResourceExtType.CLIENT_INFO);
        return null == provider ? null : provider.getData();
    }
}
