package com.xiaomi.nr.promotion.componet.preparation;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.engine.componet.ConditionPreparation;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.GlobaExcludeExternalProvider;
import com.xiaomi.nr.promotion.resource.external.GlobalActExcludeExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 全局排除商品
 *
 * <AUTHOR>
 * @date 2021/4/1
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class GlobalExcludePreparation extends ConditionPreparation {

    @Override
    public void prepare(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        // 全局排除
        CompareItem globalInExclu = getGlobalExclude(context.getExternalDataMap());
        context.setGlobalInExclu(globalInExclu);

        // 全局活动排除
        CompareItem globalActCouponInExclu = getGlobalActExclude(context.getExternalDataMap());
        context.setGlobalActInExclu(globalActCouponInExclu);
    }

    private CompareItem getGlobalExclude(Map<ResourceExtType, ExternalDataProvider<?>> providerMap) throws BizError {
        GlobaExcludeExternalProvider provider = (GlobaExcludeExternalProvider) providerMap.get(ResourceExtType.GLOBAL_IN_EXCLUDE);
        return provider.getData();
    }

    private CompareItem getGlobalActExclude(Map<ResourceExtType, ExternalDataProvider<?>> providerMap) throws BizError {
        GlobalActExcludeExternalProvider provider = (GlobalActExcludeExternalProvider) providerMap.get(ResourceExtType.GLOBAL_ACT_EXCLUDE);
        return provider.getData();
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
    }
}
