package com.xiaomi.nr.promotion.entity.redis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 换新立减 RenewReduceInfo
 *
 * <AUTHOR>
 * @date 2021/3/26
 * @see BuyReduceInfo
 */
@Data
@ToString
public class BuyReduceInfo implements Serializable {
    private static final long serialVersionUID = 355449167249845669L;
    /**
     * sku/套装id
     */
    @SerializedName("sku_package")
    private Long skuPackage = 0L;
    /**
     * sku或package
     */
    private String level = "";
    /**
     * 直降后的价格（单位分）
     */
    @SerializedName("reduceAmount")
    private Long reduceAmount;

    /**
     * 活动数量限制
     */
    @SerializedName("limitNum")
    private Integer limitNum;
}
