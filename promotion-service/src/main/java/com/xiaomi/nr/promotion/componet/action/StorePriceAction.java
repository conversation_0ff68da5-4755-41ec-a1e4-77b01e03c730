package com.xiaomi.nr.promotion.componet.action;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.CartItemChild;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.StorePricePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class StorePriceAction extends AbstractAction {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 门店价信息Map
     * key: skuPackage val:ActPriceInfo
     */
    private Map<String, ActPriceInfo> storePriceInfoMap;

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        List<CartItem> cartList = request.getCartList();
        if (MapUtil.isEmpty(storePriceInfoMap)) {
            log.error("storePriceInfoMap is empty. actId:{} uid:{}", promotion, request.getUserId());
            return;
        }

        // 改价 (获取可以参加活动的购物车和参加的活动)
        List<GoodsIndex> indexList = context.getGoodIndex();
        changePrice(cartList, indexList);

        // 设置结果
        setResult(context, cartList, promotion);
    }

    private void changePrice(List<CartItem> cartList, List<GoodsIndex> indexList) {
        for (CartItem item : cartList) {
            doChangeCartPrice(item, indexList);
        }
    }

    private void doChangeCartPrice(CartItem item, List<GoodsIndex> goodIndex) {
        // 判断是否可以参加门店价
        boolean canJoin = canJoinStorePriceAct(item, goodIndex);
        if (!canJoin) {
            return;
        }

        // 获取可以参加的门店价活动(storePriceInfo == null代表没获取到)
        ActPriceInfo storePriceInfo = storePriceInfoMap.get(CartHelper.getSkuPackage(item));
        if (storePriceInfo == null) {
            log.error("storePriceInfo is null. cart:{}", item);
            return;
        }
        long cartItemStorePrice = storePriceInfo.getPrice();
        // 改价
        item.setCartPrice(cartItemStorePrice);

        boolean needStorePriceReduce = false;
        // 门店价<售价，记录门店价优惠金额
        if (cartItemStorePrice < item.getOriginalCartPrice()) {
            needStorePriceReduce = true;
            item.setStorepriceReduce(item.getOriginalCartPrice() - cartItemStorePrice);
        }
        // 套装内子商品改价
        if (StringUtils.isNotEmpty(item.getPackageId())) {
            calChildPrice(item, needStorePriceReduce);
        }
        // 参与门店价，不能参与直降
        item.setCannotJoinActTypes(Arrays.asList(Long.valueOf(ActivityTypeEnum.ONSALE.getValue())));
        item.setSourceCode(promotionId.toString());
        item.setChangePriceActType(ActivityTypeEnum.STORE_PRICE.getValue());
    }
    /**
     * 判断购物车cartItem是否可以参加门店价
     *
     * @param cartItem     购物车项
     * @param canJoinItems 能参加活动的项
     * @return true/false
     */
    private boolean canJoinStorePriceAct(CartItem cartItem, List<GoodsIndex> canJoinItems) {
        if (CollectionUtils.isEmpty(canJoinItems)) {
            return false;
        }
        // 任意匹配上就是匹配上了
        return canJoinItems.stream()
                .anyMatch(item -> item.getItemId().equals(cartItem.getItemId()));
    }

    /**
     * 计算套装内子商品的价格
     *
     * @param cartItem 购物车项
     * @param needReduce 是否有优惠
     */
    private void calChildPrice(CartItem cartItem, boolean needReduce) {
        List<CartItemChild> children = cartItem.getChilds();
        if (CollectionUtils.isEmpty(children)) {
            return;
        }

        long totalSellPrice = CartHelper.getPackageSellPrice(cartItem);
        long cartPrice = cartItem.getCartPrice();
        long actualCartPrice = 0L;
        // 孩子的门店价是按sellPrice比例做分摊
        for (CartItemChild child : cartItem.getChilds()) {
            long childPrice = Math.floorDiv(cartPrice * child.getSellPrice(), totalSellPrice);
            child.setCartPrice(childPrice);
            child.setLowerPrice(childPrice);
            if (needReduce) {
                // 门店价优惠的金额
                child.setStorepriceReduce(child.getOriginalSellPrice() - childPrice);
            }
            actualCartPrice += childPrice;
        }

        long priceDiff = cartPrice - actualCartPrice;
        if (priceDiff == 0L) {
            return;
        }
        // 有分摊余额, 一分钱一分钱分摊
        long moneyDiff = 1;
        for (CartItemChild child : cartItem.getChilds()) {
            if (priceDiff <= 0) {
                return;
            }
            child.setCartPrice(child.getCartPrice() + moneyDiff);
            child.setLowerPrice(child.getLowerPrice() + moneyDiff);
            if (needReduce) {
                child.setStorepriceReduce(child.getOriginalSellPrice() - child.getLowerPrice());
            }
            priceDiff -= moneyDiff;
        }
    }

    private void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool) throws BizError {
        List<GoodsIndex> goodsIndices = context.getGoodIndex();
        List<String> parentItemList = CartHelper.getParentItemIdList(goodsIndices);
        List<String> joinedItemIdList = CartHelper.getJoinedItemIdList(goodsIndices, cartList);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setJoinedItemId(joinedItemIdList);
        promotionInfo.setParentItemId(parentItemList);
        promotionInfo.setJoined(CollectionUtils.isNotEmpty(joinedItemIdList) ? 1 : 0);

        // 设置上下文结果
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof StorePricePromotionConfig)) {
            return;
        }
        StorePricePromotionConfig promotionConfig = (StorePricePromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.storePriceInfoMap = promotionConfig.getStorePriceInfoMap();
    }
}
