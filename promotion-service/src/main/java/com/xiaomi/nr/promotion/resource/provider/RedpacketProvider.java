package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.dao.mysql.promotionuser.RedpacketMapper;
import com.xiaomi.nr.promotion.domain.redpackage.service.mishop.MiShopRedPacketService;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.Redpacket;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.RedpacketLog;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.domain.redpackage.model.UpdateRedpacketBalance;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.domain.redpackage.service.common.RedpacketDBService;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 红包资源管理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class RedpacketProvider implements ResourceProvider<RedpacketProvider.RedpacketResource> {

    @Autowired
    private RedpacketMapper redpacketMapper;

    @Autowired
    private RedpacketDBService redpacketDBService;

    @Autowired
    private MiShopRedPacketService redpacketService;

    private ResourceObject<RedpacketResource> resourceObject;

    @Override
    public ResourceObject<RedpacketResource> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<RedpacketResource> object) {
        this.resourceObject = object;
    }

    /**
     * 锁定红包资源
     *
     * @throws BizError
     */
    @Override
    public void lock() throws BizError {
        log.info("lock redpacket resource. {}", resourceObject);
        long userId = resourceObject.getContent().getUid();
        long nowTime = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        List<Redpacket> redpackets = redpacketMapper.getAllAvailableRedpackets(userId, nowTime);
        // 更新数据库
        List<UpdateRedpacketBalance> updateRedpackets = resourceObject.getContent().getRedPackets();
        List<RedpacketLog> redpacketLogs = resourceObject.getContent().getRedpacketLogs();
        // 批量修改红包余额
        try {
            redpacketDBService.updateRedpacketBalance(userId, nowTime, updateRedpackets, redpacketLogs);
        } catch (Exception e) {
            log.error("updateRedpacketBalance resource data failed. userId:{}, orderId:{}, err:{}", userId, resourceObject.getOrderId(), e);
            throw ExceptionHelper.create(ErrCode.ERR_REDPACKET_DB_UPDATE, e.getMessage());
        }
        // 更新缓存
        redpacketService.setRedpacketRedis(userId, nowTime);
        log.info("lock redpacket resource success. {}", resourceObject);
    }

    @Override
    public void consume() {
        log.info("consume redpacket resource. {}", resourceObject);
    }

    /**
     * 回滚红包资源
     *
     * @throws BizError
     */
    @Override
    public void rollback() throws BizError {
        log.info("rollback redpacket resource. {}", resourceObject);
        long nowTime = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        long userId = resourceObject.getContent().getUid();
        long orderId = resourceObject.getOrderId();
        try {
            redpacketDBService.refundRedpacket(userId, orderId, nowTime);
        } catch (Exception e) {
            log.error("refundRedpacket resource data failed. userId:{}, orderId:{}, err:{}", userId, orderId, e);
            throw ExceptionHelper.create(ErrCode.ERR_REDPACKET_DB_UPDATE, e.getMessage());
        }
        // 更新缓存
        redpacketService.setRedpacketRedis(userId, nowTime);
        log.info("rollback redpacket resource success. {}", resourceObject);
    }

    @Override
    public String conflictText() {
        return "红包资源初始化异常";
    }

    @Data
    public static class RedpacketResource {
        private long uid;
        private List<UpdateRedpacketBalance> redPackets;
        private List<RedpacketLog> redpacketLogs;
    }

}
