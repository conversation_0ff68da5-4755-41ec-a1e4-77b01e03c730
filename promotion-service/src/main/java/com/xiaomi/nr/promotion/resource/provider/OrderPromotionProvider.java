package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.dao.mysql.promotionuser.OrderPromotionMapper;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.OrderPromotion;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OrderPromotionProvider implements ResourceProvider<OrderPromotionProvider.OrderPromotionResource> {
    private ResourceObject<OrderPromotionResource> resourceObject;
    @Autowired
    private OrderPromotionMapper orderPromotionMapper;
    @Override
    public ResourceObject<OrderPromotionResource> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<OrderPromotionResource> object) {
        this.resourceObject = object;
    }

    @Override
    public void lock() throws BizError {
        OrderPromotionResource orderPromotionResource = resourceObject.getContent();
        orderPromotionMapper.insertOrderPromotion(resourceConvertToOrderPromotion(orderPromotionResource));
    }

    @Override
    public void consume() throws BizError {

    }

    @Override
    public void rollback() throws BizError {

    }

    @Override
    public String conflictText() {
        return null;
    }

    @Data
    public static class OrderPromotionResource {
        /**
         * 订单id
         */
        private Long orderId;

        /**
         * 用户id
         */
        private Long userId;

        /**
         * 订单优惠明细json
         */
        private String promotionDetail;
    }

    private OrderPromotion resourceConvertToOrderPromotion(OrderPromotionResource orderPromotionResource) {
        OrderPromotion orderPromotion = new OrderPromotion();
        orderPromotion.setOrderId(orderPromotionResource.getOrderId());
        orderPromotion.setUserId(orderPromotionResource.getUserId());
        orderPromotion.setPromotionDetail(orderPromotionResource.getPromotionDetail());
        orderPromotion.setAddTime(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()));
        return orderPromotion;
    }
}
