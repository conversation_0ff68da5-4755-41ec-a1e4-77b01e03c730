package com.xiaomi.nr.promotion.util;

import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @author: 侯智川
 * @date: 2022/9/15 11:20
 */
@Slf4j
public class RequestParamPreHandle {

    /**
     * cartList 参数预处理
     * 1. 处理saleSource:  String saleSource => List<String> saleSources
     *
     * @param cartList cartList
     */
    public static void handleCartList(List<CartItem> cartList) {
        if (cartList.isEmpty()){
            return;
        }
        cartList.forEach(cartItem -> {
            if (cartItem.getSaleSources() == null){
                cartItem.setSaleSources(new ArrayList<>());
            }
            if (cartItem.getSaleSources().isEmpty() && StringUtils.isNotEmpty(cartItem.getSaleSource())){
                cartItem.getSaleSources().add(cartItem.getSaleSource());
            }
        });
    }
}
