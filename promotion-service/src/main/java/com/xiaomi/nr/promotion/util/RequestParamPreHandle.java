package com.xiaomi.nr.promotion.util;

import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.MaintenanceInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @author: 侯智川
 * @date: 2022/9/15 11:20
 */
@Slf4j
public class RequestParamPreHandle {

    /**
     * cartList 参数预处理
     * 1. 处理saleSource:  String saleSource => List<String> saleSources
     *
     * @param cartList cartList
     */
    public static void handleCartList(List<CartItem> cartList) {
        if (cartList.isEmpty()){
            return;
        }
        cartList.forEach(cartItem -> {
            if (cartItem.getSaleSources() == null){
                cartItem.setSaleSources(new ArrayList<>());
            }
            if (cartItem.getSaleSources().isEmpty() && StringUtils.isNotEmpty(cartItem.getSaleSource())){
                cartItem.getSaleSources().add(cartItem.getSaleSource());
            }

            // 维保兼容逻辑
            buildMaintenanceInfo(cartItem);
        });
    }

    /**
     * 维保券兼容逻辑，将老字段赋值给新字段，系统内部使用新字段进行计算
     * 注：后期会删除
     * @param cartItem
     * @return
     */
    @Deprecated
    private static void buildMaintenanceInfo(CartItem cartItem) {
        if (Objects.nonNull(cartItem.getMaintenanceInfo())) {
            return;
        }
        
        MaintenanceInfo maintenanceInfo = new MaintenanceInfo();

        if (Objects.nonNull(cartItem.getWorkHour())) {
            maintenanceInfo.setWorkHour(cartItem.getWorkHour());
        }

        if (Objects.nonNull(cartItem.getUnitPrice())) {
            maintenanceInfo.setUnitPrice(cartItem.getUnitPrice());
        }

        if (Objects.nonNull(cartItem.getPayType())) {
            maintenanceInfo.setPayType(cartItem.getPayType());
        }

        if (Objects.nonNull(cartItem.isCanAdjustPrice())) {
            maintenanceInfo.setCanAdjustPrice(cartItem.isCanAdjustPrice());
        }

        cartItem.setMaintenanceInfo(maintenanceInfo);
    }
}
