package com.xiaomi.nr.promotion.activity.pool;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xiaomi.nr.promotion.activity.ContractPhoneActivity;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.PreferentialInfo;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.dao.redis.StoreRedisDao;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.redis.OrgInfo;
import com.xiaomi.nr.promotion.enums.AreaEnum;
import com.xiaomi.nr.promotion.enums.OnOffLineEnum;
import com.xiaomi.nr.promotion.enums.OrgScopeEnum;
import com.xiaomi.nr.promotion.enums.OrgTypeEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.DateTimeUtil;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 在线的所有优惠活动数据池
 * 从这里获取对应的 ActivityTool {@link ActivityTool}
 *
 * <AUTHOR>
 * @date 2021/3/15
 */
@Slf4j
@Component
public class ActivityPool {
    /**
     * 活动Tool实例池
     */
    @Autowired(required = false)
    private PromotionInstancePool promotionInstancePool;
    /**
     * 活动缓存操作对象
     */
    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Autowired
    private StoreRedisDao storeRedisDao;

    /**
     * 获取促销活动Tool 列表
     *
     * @param request 请求对象
     * @return 活动工具列表
     */
    public List<ActivityTool> getCheckoutPromotions(CheckoutPromotionRequest request) {
        if (StringUtils.isEmpty(request.getOrgCode()) && request.getClientId() == null) {
            return Collections.emptyList();
        }

        String orgCode = request.getOrgCode();
        Long clientId = request.getClientId();
        // 购物车中所有的sku和套装id
        List<String> skuPackageList = CartHelper.getSkuPackageList(request.getCartList());
        // 获取后台配置的可参加活动工具
        List<ActivityTool> toolList = getCurrent(orgCode, clientId, skuPackageList);

        List<Long> actIdList = toolList.stream().map(ActivityTool::getId).collect(Collectors.toList());
        log.info("get actIdList. uid:{}, clientId:{}, orgCode:{}, list:{}", request.getUserId(), clientId, orgCode, actIdList);
        // 获取所有促销活动工具
        return getAllPromotionTools(toolList, request);
    }


    /**
     * 获取促销活动Tool 列表
     *
     * @param request 请求对象
     * @return 活动工具列表
     */
    public List<ActivityTool> getCheckoutPromotionsByChannel(CheckoutPromotionRequest request, Integer channel) {
        if (channel == null) {
            return Collections.emptyList();
        }

        // 购物车中所有的sku和套装id
        List<String> skuPackageList = CartHelper.getSkuPackageList(request.getCartList());
        // 获取后台配置的可参加活动工具
        List<ActivityTool> toolList = getCurrentV2(null, null, skuPackageList, Lists.newArrayList(channel));

        List<Long> actIdList = toolList.stream().map(ActivityTool::getId).collect(Collectors.toList());
        log.info("get actIdList. uid:{}, channel:{}, list:{}", request.getUserId(), channel, actIdList);
        return toolList;
    }

    /**
     * 获取可参加活动
     *
     * @param orgCode        门店Code
     * @param clientId       应用Id
     * @param skuPackageList sku 或 package 列表
     * @return 活动列表
     */
    public List<ActivityTool> getCurrent(String orgCode, Long clientId, List<String> skuPackageList) {
        return getCurrent(orgCode, clientId, null, skuPackageList, null);
    }

    /**
     * 获取可参加活动--新增渠道
     *
     * @param orgCode        门店Code
     * @param clientId       应用Id
     * @param skuPackageList sku 或 package 列表
     * @param channel        渠道
     * @return 活动列表
     */
    public List<ActivityTool> getCurrentV2(String orgCode, Long clientId, List<String> skuPackageList, List<Integer> channel) {
        return getCurrent(orgCode, clientId, channel, skuPackageList, null);
    }

    /**
     * 获取可参加活动
     *
     * @param orgCode        门店Code
     * @param clientId       应用Id
     * @param skuPackageList sku 或 package 列表
     * @return 活动列表
     */
    public List<ActivityTool> getCurrent(String orgCode, Long clientId, List<Integer> channel, List<String> skuPackageList,
                                         List<Integer> productDepartments) {
        // 获取skuPackage可参加活动
        Stopwatch started = Stopwatch.createStarted();
        Set<Long> actIdSet = promotionInstancePool.getCurrentActIds(skuPackageList);
        // 线下门店orgCode 或 clientId 获取可参加的活动Id列表
        List<Long> actIdList;
        if (StringUtils.isNotEmpty(orgCode)) {
            actIdList = activityRedisDao.listActivityIdByOrgCode(orgCode);
        } else if (clientId != null) {
            actIdList = activityRedisDao.listActivityIdByClientId(clientId);
        } else if (channel != null) {
            actIdList = promotionInstancePool.getCurrentChannelActIds(channel);
        } else {
            actIdList = new ArrayList<>(actIdSet);
        }
        // 部门
        List<Long> departmentActIdList = null;
        if (CollectionUtils.isNotEmpty(productDepartments)) {
            departmentActIdList = promotionInstancePool.getCurrentDepartmentActIds(productDepartments);
        }
        List<Long> actualActIdList = Lists.newArrayList(actIdList);
        log.info("getCurrent. orgCode:{}, clientId:{}, skuPackageList:{} actIdList:{} actIdSet:{}, departmentActIdList:{}, ws:{}",
                orgCode, clientId, skuPackageList, actualActIdList, actIdSet, departmentActIdList, started.elapsed(TimeUnit.MILLISECONDS));
        // 做交集. （重要！actIdList 为有序，必须为基准来做交集， 不要交换顺序）
        actualActIdList.retainAll(actIdSet);
        if (departmentActIdList != null) {
            actualActIdList.retainAll(departmentActIdList);
        }
        return getCurrent(actualActIdList);
    }

    /**
     * 获取门店可参加活动
     *
     * @param orgCode 门店Code
     * @return 活动列表
     */
    public List<ActivityTool> getCurrent(String orgCode) {
        // 线下门店orgCode 或 clientId 获取可参加的活动Id列表
        List<Long> actIdList = activityRedisDao.listActivityIdByOrgCode(orgCode);
        return getCurrent(actIdList);
    }

    /**
     * 根据活动id获取活动工具
     *
     * @param orgCode 门店id
     * @param actId   活动id
     * @return 活动工具
     */
    public ActivityTool getCurrentByActId(String orgCode, Long actId) throws BizError {
        // 线下门店orgCode获取可参加的活动Id列表
        List<Long> actIdList = activityRedisDao.listActivityIdByOrgCode(orgCode);
        if (actIdList.contains(actId)) {
            List<ActivityTool> activityTools = getCurrent(Collections.singletonList(actId));
            if (CollectionUtils.isNotEmpty(activityTools)) {
                return activityTools.get(0);
            } else {
                return null;
            }
        } else {
            throw ExceptionHelper.create(ErrCode.ERR_INVALID_ACT_EXIST, "此门店不可参与该活动");
        }
    }

    /**
     * 根据ID获取活动Tool 列表
     *
     * @param promotionId 优惠活动ID
     * @return 优惠活动
     */
    public ActivityTool getPromotionById(Long promotionId) {
        List<Long> actIdList = Collections.singletonList(promotionId);
        List<ActivityTool> toolList = getCurrent(actIdList);
        if (CollectionUtils.isEmpty(toolList)) {
            return null;
        }
        return toolList.get(0);
    }

    /**
     * 根据ID列表获取活动列表(进行中）
     *
     * @param actIdList 活动ID列表
     * @return 活动工具列表
     */
    public List<ActivityTool> getCurrent(List<Long> actIdList) {
        return getTools(actIdList, Boolean.FALSE);
    }

    /**
     * 获取未来可参加活动（当前为展示当前后七天）
     *
     * @param orgCode        门店Code
     * @param clientId       应用Id
     * @param skuPackageList sku 或 package 列表
     * @return 活动列表
     */
    public List<ActivityTool> getPreview(String orgCode, Long clientId, List<String> skuPackageList) {
        // 获取skuPackage可参加活动, 未来需要接入OrgCode 和 ClientId
        Set<Long> actIdSet = promotionInstancePool.getCurrentActIds(skuPackageList);
        List<ActivityTool> toolList = getPreview(Lists.newArrayList(actIdSet));
        // 注意：这里是临时这么做，因为clientId和OrgCode对应可参加活动Id Redis中
        if (StringUtils.isNotEmpty(orgCode)) {
            toolList = filterByOrgCode(toolList, orgCode);
        } else if (clientId != null) {
            toolList = filterByClientId(toolList, clientId);
        }
        return toolList;
    }

    /**
     * 根据ID列表获取活动列表（即将开始）
     *
     * @param actIdList 活动ID列表
     * @return 活动工具列表
     */
    public List<ActivityTool> getPreview(List<Long> actIdList) {
        return getTools(actIdList, Boolean.TRUE);
    }

    /**
     * 根据ID列表获取活动列表
     * <p>
     * 根据ID列表获取活动列表
     *
     * @param actIdList 活动ID列表
     * @return 活动列表
     */
    private List<ActivityTool> getTools(List<Long> actIdList, boolean isPreview) {
        return promotionInstancePool.getCurrentTools(actIdList).stream()
                .filter(tool -> checkTime(tool, isPreview)).collect(Collectors.toList());
    }

    /**
     * 检查活动时间
     *
     * @param tool 活动工具
     * @return 时间是否符合
     */
    private boolean checkTime(ActivityTool tool, boolean isPreview) {
        long now = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        // 预览即将开始

        if (isPreview) {
            long preTime = DateTimeUtil.getFutureTime(7);
            return now < tool.getUnixStartTime() && tool.getUnixStartTime() < preTime;
        }
        // 进行中
        return now >= tool.getUnixStartTime() && now < tool.getUnixEndTime();
    }

    /**
     * 根据客户ID过滤活动工具列表
     *
     * @param toolList 活动工具列表
     * @param clientId 客户ID
     * @return 过滤后的活动工具列表
     */
    private List<ActivityTool> filterByClientId(List<ActivityTool> toolList, Long clientId) {
        if (CollectionUtils.isEmpty(toolList)) {
            return Collections.emptyList();
        }
        List<ActivityTool> clientToolList = Lists.newArrayList();
        for (ActivityTool tool : toolList) {
            ActivityDetail detail = tool.getActivityDetail();
            if (detail == null) {
                continue;
            }
            if (OnOffLineEnum.OFFLINE == detail.getOffline()) {
                continue;
            }
            List<String> selectClients = Optional.ofNullable(detail.getSelectClients()).orElse(Collections.emptyList());
            if (selectClients.contains(String.valueOf(clientId))) {
                clientToolList.add(tool);
            }
        }
        return clientToolList;
    }

    /**
     * 根据组织代码过滤活动工具列表
     *
     * @param toolList 活动工具列表
     * @param orgCode  组织代码
     * @return 过滤后的活动工具列表
     */
    private List<ActivityTool> filterByOrgCode(List<ActivityTool> toolList, String orgCode) {
        OrgInfo orgInfo = storeRedisDao.getOrgInfo(orgCode);
        if (orgInfo == null) {
            log.warn("orgCode:{} is not fount.", orgCode);
            return Collections.emptyList();
        }

        // 门店信息做懒加载，因为大部分活动不会指定区域
        Map<String, List<OrgInfo>> areaStoresMap = Collections.emptyMap();
        List<ActivityTool> orgToolList = Lists.newArrayList();
        for (ActivityTool tool : toolList) {
            ActivityDetail detail = tool.getActivityDetail();
            if (detail == null) {
                continue;
            }
            if (OnOffLineEnum.ONLINE == detail.getOffline()) {
                continue;
            }
            OrgScopeEnum scopeEnum = detail.getOrgScope();
            if (scopeEnum == null) {
                continue;
            }
            // 全部门店
            if (OrgScopeEnum.ORG_ALL_STORE == scopeEnum
                    && (OrgTypeEnum.isDirect(orgInfo.getOrgType()) || OrgTypeEnum.isSpecialty(orgInfo.getOrgType()))) {
                orgToolList.add(tool);
                continue;
            }
            // 全部直营
            if (OrgScopeEnum.ORG_ALL_DIRECT_STORE == scopeEnum && OrgTypeEnum.isDirect(orgInfo.getOrgType())) {
                orgToolList.add(tool);
                continue;
            }
            // 全部专卖
            if (OrgScopeEnum.ORG_ALL_SPECIALTY_STORE == scopeEnum && OrgTypeEnum.isSpecialty(orgInfo.getOrgType())) {
                orgToolList.add(tool);
                continue;
            }
            // 全部授权
            if (OrgScopeEnum.ORG_ALL_AUTHORIZED_STORE == scopeEnum && OrgTypeEnum.isAuthorized(orgInfo.getOrgType())) {
                orgToolList.add(tool);
                continue;
            }
            // 指定门店
            List<String> selectOrgCodes = Optional.ofNullable(detail.getSelectOrgCodes()).orElse(Collections.emptyList());
            if (OrgScopeEnum.ORG_SPECIFY_STORE == scopeEnum || OrgScopeEnum.ORG_AUTHORIZED_STORE == scopeEnum) {
                if (selectOrgCodes.contains(orgCode)) {
                    orgToolList.add(tool);
                    continue;
                }
            }
            // 指定区域
            if (OrgScopeEnum.ORG_SPECIFY_AREA == scopeEnum || OrgScopeEnum.ORG_AUTHORIZED_AREA == scopeEnum) {
                if (MapUtil.isEmpty(areaStoresMap)) {
                    areaStoresMap = storeRedisDao.getAreaStoresMap();
                }
                Set<String> selectOrgCodeSet = getAreaOrgCodes(selectOrgCodes, areaStoresMap);
                if (selectOrgCodeSet.contains(orgCode)) {
                    orgToolList.add(tool);
                }
            }
        }
        return orgToolList;
    }

    /**
     * 获取区域组织代码集合
     *
     * @param selectOrgCodes 选中的组织代码列表
     * @param areaStoresMap  区域与店铺信息的映射关系
     * @return 区域组织代码集合
     */
    private Set<String> getAreaOrgCodes(List<String> selectOrgCodes, Map<String, List<OrgInfo>> areaStoresMap) {
        if (CollectionUtils.isEmpty(selectOrgCodes) || MapUtil.isEmpty(areaStoresMap)) {
            return Collections.emptySet();
        }
        Set<String> selectOrgCodeSet = Sets.newHashSet();
        selectOrgCodes.forEach(area -> {
            if (StringUtils.contains(area, AreaEnum.STORE.getPrefix())) {
                selectOrgCodeSet.add(area.substring(AreaEnum.STORE.getPrefix().length()));
                return;
            }
            List<OrgInfo> storeList = areaStoresMap.get(area);
            if (storeList == null) {
                return;
            }
            List<String> codes = storeList.stream().map(OrgInfo::getOrgCode).collect(Collectors.toList());
            selectOrgCodeSet.addAll(codes);
        });
        return selectOrgCodeSet;
    }

    /**
     * 获取所有促销活动工具（包含根据请求构造的活动工具）
     *
     * @param activityTools 活动工具
     * @param request       结算请求
     * @return 促销活动工具
     */
    private List<ActivityTool> getAllPromotionTools(List<ActivityTool> activityTools, CheckoutPromotionRequest request) {
        List<ActivityTool> allTools = new ArrayList<>();

        // 切分调价工具（门店价、直降）列表
        for (ActivityTool activityTool : activityTools) {
            if (activityTool.getType() != PromotionToolType.STORE_PRICE
                    && activityTool.getType() != PromotionToolType.ONSALE) {
                break;
            }
            allTools.add(activityTool);
        }
        List<CartItem> cartItemList = request.getCartList();

        // 非后台配置的促销工具列表
        I:
        for (CartItem item : cartItemList) {
            if (CollectionUtils.isEmpty(item.getPreferentialInfos())) {
                continue;
            }
            List<PreferentialInfo> preferentialInfos = item.getPreferentialInfos();
            for (PreferentialInfo preferentialInfo : preferentialInfos) {
                // 处理合约机
                if (preferentialInfo.getTypeCode().equals(PromotionConstant.CONTRACTPHONE)) {
                    ContractPhoneActivity contractPhone = new ContractPhoneActivity();
                    allTools.add(contractPhone);
                    break I;
                }
            }
        }
        // 促销工具列表
        for (ActivityTool activityTool : activityTools) {
            if (activityTool.getType() == PromotionToolType.STORE_PRICE
                    || activityTool.getType() == PromotionToolType.ONSALE) {
                continue;
            }
            allTools.add(activityTool);
        }
        return allTools;
    }
}
