package com.xiaomi.nr.promotion.activity.entity;

import com.xiaomi.nr.promotion.api.dto.enums.FetchTypeEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class GovernmentModeAct implements Serializable {

    @Serial
    private static final long serialVersionUID = 3727027735448984669L;

    /**
     * 是否命中
     */
    private boolean hitGovAct;

    /**
     * 活动id
     */
    private Long id;

    /**
     * 国补模式,枚举 com.xiaomi.nr.promotion.api.dto.enums.SubsidyModeEnum
     */
    private Integer subsidyMode;

    /**
     * 活动会场URL，极简模式存在
     */
    private String activityUrl;

    /**
     * 补贴上报城市
     */
    private Integer reportCity;

    /**
     * 国补优惠比例：15%，不存在则返回0
     */
    private Long reduceDiscount;

    /**
     * 最大优惠金额：单位分，不存在则返回0
     */
    private Long maxReduce;

    /**
     * 品类编码
     */
    private String cateCode;

    /**
     * 领取方式{@link FetchTypeEnum}
     */
    private Integer fetchType;

    /**
     * 使用指南
     */
    private String usageGuide;

    /**
     * 使用指南图片URL
     */
    private String usageGuideImgUrl;

}
