package com.xiaomi.nr.promotion.componet.action.carsale;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.componet.action.AbstractAction;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.PromotionExtend;
import com.xiaomi.nr.promotion.model.common.ReduceExtendInfo;
import com.xiaomi.nr.promotion.model.common.ReduceJoin;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.GoodsReduceInfo;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.tool.CheckoutCartToolV2;
import com.xiaomi.nr.promotion.tool.PromotionDescRuleTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 立减
 *
 * <AUTHOR>
 * @date 2025/7/22 19:32
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarBuyReduceAction extends AbstractAction {

    private Long promotionId;

    private Map<String, GoodsReduceInfo> buyReduceMap;

    @Autowired
    private PromotionDescRuleTool promotionDescRuleTool;

    @Autowired
    private CheckoutCartToolV2 checkoutCartToolV2;


    @Autowired
    private ResourceProviderFactory resourceProviderFactory;

    @Override
    public void execute(ActivityTool activityTool, CheckoutPromotionRequest request, LocalContext context)
            throws BizError {
        if (MapUtils.isEmpty(buyReduceMap)) {
            log.warn("carBuyReduce buyReduceMap is empty. actId:{} uid:{}", promotionId, request.getUserId());
            return;
        }
        List<CartItem> cartList = request.getCartList();
        List<GoodsIndex> goodIndex = context.getGoodIndex();
        List<CartItem> fillCartList = CartHelper.getIndexCartList(cartList, goodIndex);

        // 商品改价
        changePrice(fillCartList, buyReduceMap, context);
        // 处理响应promotionInfo
        setResult(context, activityTool, fillCartList);
    }

    private void changePrice(List<CartItem> fillCartList, Map<String, GoodsReduceInfo> buyReduceMap, LocalContext checkoutContext) throws BizError {
        for (CartItem item : fillCartList) {
            String skuPackage = CartHelper.getSkuPackage(item, checkoutContext);
            if (StringUtils.isEmpty(skuPackage)) {
                return;
            }
            GoodsReduceInfo reduceInfo = buyReduceMap.get(skuPackage);
            if (reduceInfo == null) {
                continue;
            }
            // 改价
            doChangeCartPrice(item, reduceInfo);
        }
    }

    private void doChangeCartPrice(CartItem item, GoodsReduceInfo reduceInfo) throws BizError {
        Long reduceAmount = reduceInfo.getReduceAmount();
        if (reduceAmount <= 0L) {
            log.warn("buyReduce reduceAmount <= 0, actId:{} reduceInfo:{} cart:{} ", promotionId, reduceInfo, item);
            return;
        }
        long curPrice = CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList());
        // 如果超出购物车本身金额，则保留一分钱
        long reduceActual = reduceAmount * item.getCount();
        if (curPrice <= reduceActual) {
            reduceActual = curPrice - PromotionConstant.CART_PRICE_REMAIN;
        }

        ReduceDetailItem reduceDetailItem = new ReduceDetailItem()
                .setPromotionId(promotionId)
                .setPromotionType(ActivityTypeEnum.BUY_REDUCE.getValue())
                .setSsuId(item.getSsuId())
                .setReduce(reduceActual)
                .setReduceSingle(reduceActual )
                .setBudgetApplyNo(reduceInfo.getBudgetApplyNo())
                .setLineNum(reduceInfo.getLineNum());
        item.getReduceItemList().add(reduceDetailItem);

        long finalPrice = CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList());
        if (finalPrice+ reduceActual  != curPrice){
            throw ExceptionHelper.create(GeneralCodes.InternalError, "优惠计算异常");
        }

    }

    private void setResult(LocalContext context, ActivityTool tool, List<CartItem> fillCartList) throws BizError {
        // 参与Item
        List<String> itemList = fillCartList.stream().map(CartItem::getItemId).collect(Collectors.toList());
        // 生成拓展信息
        String extend = generateActExpandInfo(fillCartList, context);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setJoinedItemId(itemList);
        promotionInfo.setParentItemId(itemList);
        promotionInfo.setExtend(extend);
        promotionInfo.setJoined(BooleanEnum.YES.getValue());

        // 设置上下文结果
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    private String generateActExpandInfo(List<CartItem> fillCartList, LocalContext checkoutContext) {
        List<ReduceJoin> joinList = fillCartList.stream() .map(cartItem -> initReduceJoin(cartItem, checkoutContext))
                .filter(Objects::nonNull).collect(Collectors.toList());

        ReduceExtendInfo reduceExtendInfo = new ReduceExtendInfo();
        reduceExtendInfo.setJoinExtend(joinList);


        PromotionExtend extend = new PromotionExtend();
        extend.setReduceExtend(reduceExtendInfo);
        return GsonUtil.toJson(extend);
    }

    private ReduceJoin initReduceJoin(CartItem item,LocalContext checkoutContext) {
        GoodsReduceInfo reduceInfo = buyReduceMap.get(CartHelper.getSkuPackage(item,checkoutContext));
        if (reduceInfo == null) {
            return null;
        }
        String ruleDesc = promotionDescRuleTool.generateBuyReduceItemRule(reduceInfo.getReduceAmount());
        ReduceJoin reduceJoin = new ReduceJoin();
        reduceJoin.setSkuOrPackage(reduceInfo.getSsuId());
        reduceJoin.setLevel(reduceInfo.getLevel());
        reduceJoin.setReduceAmount(reduceInfo.getReduceAmount());
        reduceJoin.setJoinCounts(item.getCount());
        reduceJoin.setRuleDesc(ruleDesc);
        reduceJoin.setLimitNum(reduceInfo.getLimitNum());
        return reduceJoin;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof BuyReducePromotionConfig)) {
            return;
        }
        BuyReducePromotionConfig promotionConfig = (BuyReducePromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.buyReduceMap = promotionConfig.getBuyReduceInfoMap();
    }
}
