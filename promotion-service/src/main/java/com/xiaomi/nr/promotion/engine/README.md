## engine 优惠引擎

你可以不用太纠结理解这里.

###目录结构
```
|--engine
    |--componet
    定义各个组件需要实现的方法--也可以简单理解成组件的能力    
    |--dsl领域语言;
        |--DSLEngine领域引擎概念，被dsload调用，每个Activity都会封装自己需要执行的组件
        |--DSLStream执行流;对当面请求的 sku 获取所有活动缓存，顺序执行组件内逻辑
        |--DSLGeneralPromotion--你需要关系个文件;继承了ActivityTool,对load、checkout 方法做了具体实现;
            如果今后有促销工具差异化实现类似checkout的新接口，可以ActivityTool增加新的抽象方法，这里实现。
            根据加载的组件Action 部分做差异化实现  
    |—-**Interface
        |-- 如果你的接口不适用与所有活动:比如获取商品直降价【PromotionPriceProvider】，
            单独定义一个Interface,由 activity中需要实现的促销工具继承单独实现即可;
```
###后续维护注意项
