package com.xiaomi.nr.promotion.componet.action;

import com.xiaomi.nr.promotion.componet.action.carmaintenance.MaintenanceDiscountAction;
import com.xiaomi.nr.promotion.componet.action.carmaintenance.MaintenanceItemFreeAction;
import com.xiaomi.nr.promotion.componet.action.carsale.CarOnSaleAction;
import com.xiaomi.nr.promotion.componet.action.carsale.CarRangeReduceAction;
import com.xiaomi.nr.promotion.componet.action.carshop.CarShopBuyReduceAction;
import com.xiaomi.nr.promotion.componet.action.carshop.CarShopOnsaleAction;
import com.xiaomi.nr.promotion.componet.action.carshop.CarShopBuyGiftAction;
import com.xiaomi.nr.promotion.componet.action.carshop.CarShopOnsaleAction;
import com.xiaomi.nr.promotion.componet.action.carshop.CarShopPostFreeAction;
import com.xiaomi.nr.promotion.componet.action.carshop.CarShopVipDiscountAction;
import com.xiaomi.nr.promotion.engine.componet.Action;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Action工厂
 *
 * <AUTHOR>
 * @date 2021/3/16
 */
@Component
public class ActionFactory {
    @Autowired
    private ObjectFactory<OnsaleAction> onsaleActionObjectFactory;
    @Autowired
    private ObjectFactory<CarShopOnsaleAction> carShopOnsaleActionObjectFactory;
    @Autowired
    private ObjectFactory<GiftAction> giftActionObjectFactory;
    @Autowired
    private ObjectFactory<BargainAction> bargianActionObjectFactory;
    @Autowired
    private ObjectFactory<DiscountAction> discountActionObjectFactory;
    @Autowired
    private ObjectFactory<ReduceAction> reduceActionObjectFactory;
    @Autowired
    private ObjectFactory<PostFreeAction> postFreeActionObjectFactory;
    @Autowired
    private ObjectFactory<CouponLimitAction> couponLimitActionObjectFactory;
    @Autowired
    private ObjectFactory<BuyGiftAction> buyGiftActionObjectFactory;
    @Autowired
    private ObjectFactory<StorePriceAction> storePriceActionObjectFactory;
    @Autowired
    private ObjectFactory<RenewReduceAction> renewReduceActionObjectFactory;
    @Autowired
    private ObjectFactory<BuyReduceAction> buyReduceActionObjectFactory;
    @Autowired
    private ObjectFactory<PartOnsaleAction> partOnsaleActionObjectFactory;
    @Autowired
    private ObjectFactory<CrmChannelPriceAction> crmChannelPriceActionObjectFactory;
    @Autowired
    private ObjectFactory<StepPriceAction> stepPriceActionObjectFactory;
    @Autowired
    private ObjectFactory<B2tVipDiscountAction> b2tVipDiscountActionObjectFactory;
    @Autowired
    private ObjectFactory<CarOnSaleAction> carOnSaleActionObjectFactory;
    @Autowired
    private ObjectFactory<CarRangeReduceAction> carRangeReduceActionObjectFactory;
    @Autowired
    private ObjectFactory<CarShopBuyGiftAction> carShopBuyGiftActionObjectFactory;
    @Autowired
    private ObjectFactory<CarShopVipDiscountAction> carShopVipDiscountActionObjectFactory;
    @Autowired
    private ObjectFactory<CarShopPostFreeAction> carShopPostFreeActionObjectFactory;

    @Autowired
    private ObjectFactory<CarShopBuyReduceAction> carShopBuyReduceActionObjectFactory;

    /* ----------  整车售后   ----------- */
    @Autowired
    private ObjectFactory<MaintenanceDiscountAction> maintenanceDiscountActionObjectFactory;
    @Autowired
    private ObjectFactory<MaintenanceItemFreeAction> maintenanceItemFreeActionObjectFactory;

    @Autowired
    private ObjectFactory<NewPurchaseSubsidyAction> newPurchaseSubsidyActionObjectFactory;
    @Autowired
    private ObjectFactory<UpgradePurchaseSubsidyAction> upgradePurchaseSubsidyActionObjectFactory;

    public Action getAction(Class<? extends Action> clz) throws BizError {
        if (clz == OnsaleAction.class) {
            return onsaleActionObjectFactory.getObject();
        }
        if (clz == GiftAction.class) {
            return giftActionObjectFactory.getObject();
        }
        if (clz == BargainAction.class) {
            return bargianActionObjectFactory.getObject();
        }
        if (clz == DiscountAction.class) {
            return discountActionObjectFactory.getObject();
        }
        if (clz == ReduceAction.class) {
            return reduceActionObjectFactory.getObject();
        }
        if (clz == PostFreeAction.class) {
            return postFreeActionObjectFactory.getObject();
        }
        if (clz == CouponLimitAction.class) {
            return couponLimitActionObjectFactory.getObject();
        }
        if (clz == BuyGiftAction.class) {
            return buyGiftActionObjectFactory.getObject();
        }
        if (clz == StorePriceAction.class) {
            return storePriceActionObjectFactory.getObject();
        }
        if (clz == RenewReduceAction.class) {
            return renewReduceActionObjectFactory.getObject();
        }
        if (clz == BuyReduceAction.class) {
            return buyReduceActionObjectFactory.getObject();
        }
        if (clz == PartOnsaleAction.class) {
            return partOnsaleActionObjectFactory.getObject();
        }
        if (clz == CrmChannelPriceAction.class) {
            return crmChannelPriceActionObjectFactory.getObject();
        }
        if (clz == StepPriceAction.class) {
            return stepPriceActionObjectFactory.getObject();
        }
        if (clz == B2tVipDiscountAction.class) {
            return b2tVipDiscountActionObjectFactory.getObject();
        }
        if (clz == CarOnSaleAction.class) {
            return carOnSaleActionObjectFactory.getObject();
        }
        if (clz == CarRangeReduceAction.class) {
            return carRangeReduceActionObjectFactory.getObject();
        }
        if (clz == CarShopOnsaleAction.class) {
            return carShopOnsaleActionObjectFactory.getObject();
        }
        if (clz == CarShopBuyGiftAction.class) {
            return carShopBuyGiftActionObjectFactory.getObject();
        }
        if (clz == NewPurchaseSubsidyAction.class) {
            return newPurchaseSubsidyActionObjectFactory.getObject();
        }
        if (clz == UpgradePurchaseSubsidyAction.class) {
            return upgradePurchaseSubsidyActionObjectFactory.getObject();
        }
        if (clz == CarShopVipDiscountAction.class) {
            return carShopVipDiscountActionObjectFactory.getObject();
        }
        if (clz == MaintenanceDiscountAction.class) {
            return maintenanceDiscountActionObjectFactory.getObject();
        }
        if (clz == MaintenanceItemFreeAction.class) {
            return maintenanceItemFreeActionObjectFactory.getObject();
        }
        if (clz == CarShopPostFreeAction.class) {
            return carShopPostFreeActionObjectFactory.getObject();
        }
        if (clz == CarShopBuyReduceAction.class) {
            return carShopBuyReduceActionObjectFactory.getObject();
        }

        throw ExceptionHelper.create(GeneralCodes.NotFound, String.format("Action %s not Found", clz.getName()));
    }
}
