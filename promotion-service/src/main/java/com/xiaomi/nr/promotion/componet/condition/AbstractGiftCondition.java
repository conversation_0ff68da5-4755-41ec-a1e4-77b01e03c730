package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.entity.redis.FillGoodsGroup;
import com.xiaomi.nr.promotion.entity.redis.QuotaEle;
import com.xiaomi.nr.promotion.entity.redis.SkuGroup;
import com.xiaomi.nr.promotion.enums.PolicyQuotaTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.common.ValidCondition;
import com.xiaomi.nr.promotion.tool.ConditionCheckTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 抽象赠品条件
 *
 * <AUTHOR>
 * @date 2022/8/11
 */
@Slf4j
public abstract class AbstractGiftCondition extends AbstractCondition {

    @Autowired
    private ConditionCheckTool conditionCheckTool;

    /**
     * 赠品限制是否满足
     *
     * @param skuGroupList 赠品组列表
     * @param activityId   活动后台
     * @return 是否满足
     */
    protected boolean checkGiftLimit(List<SkuGroup> skuGroupList, Long activityId) {
        if (CollectionUtils.isEmpty(skuGroupList)) {
            return false;
        }
        return skuGroupList.stream().anyMatch(skuGroup -> {
            Long groupId = skuGroup.getGroupId();
            return skuGroup.getListInfo().stream()
                    .anyMatch(item -> conditionCheckTool.checkActGiftLimit(item.getGiftLimitNum(), item.getGiftBaseNum(), activityId, groupId, item.getSku()));
        });
    }

    /**
     * 做每组主商品条件商品匹配
     *
     * @param cartList 购物车列表
     * @param uid      用户ID
     * @param online   是否线上
     * @param context  上下文
     * @return 符合组列表
     */
    protected List<List<GoodsIndex>> doGoodsListGroupMatch(List<FillGoodsGroup> goodsGroups, List<CartItem> cartList, LocalContext context, Long uid,
                                                           boolean online, Long promotionId, PromotionToolType toolType,
                                                           boolean checkPackage, List<String> saleSources, String accessCode) {
        // 构建组列表 进行[group] * [cartList] 比对
        return goodsGroups.stream().map(group -> {
            CompareItem includeGoods = group.getJoinGoods();
            return doGoodsGroupMatch(cartList, context, includeGoods, uid, online, promotionId, toolType.getTypeId(),
                    checkPackage, saleSources, accessCode);
        }).collect(Collectors.toList());
    }

    /**
     * 每组数据统计：统计数量和金额
     *
     * @param indexListGroup 符合商品下标组
     * @param cartList       购物车列表
     * @return 每组统计值
     */
    protected List<ValidCondition> doStaticGroups(List<FillGoodsGroup> includeGoodsGroups,
                                          List<List<GoodsIndex>> indexListGroup, List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(indexListGroup) || CollectionUtils.isEmpty(includeGoodsGroups)) {
            return Collections.emptyList();
        }
        if (indexListGroup.size() != includeGoodsGroups.size()) {
            return Collections.emptyList();
        }
        List<ValidCondition> includeList = new ArrayList<>();
        for (int gi = 0; gi < indexListGroup.size(); gi++) {
            List<GoodsIndex> indexList = indexListGroup.get(gi);
            FillGoodsGroup goodsGroup = includeGoodsGroups.get(gi);
            if (goodsGroup == null || goodsGroup.getQuota() == null) {
                continue;
            }
            // 汇总金额，数量
            ValidCondition validCondition = summarizeValidCondition(indexList, goodsGroup.getQuota(), cartList);
            includeList.add(validCondition);
        }
        return includeList;
    }


    private ValidCondition summarizeValidCondition(List<GoodsIndex> fillGoodsList, QuotaEle quotaEle, List<CartItem> cartList) {
        long validMoney = 0L;
        int validCount = 0;
        // 赠品加价购只有一个
        PolicyQuotaTypeEnum quotaTypeEnum = PolicyQuotaTypeEnum.getQuotaType(quotaEle.getType());
        // 统计money
        if (quotaTypeEnum == PolicyQuotaTypeEnum.POLICY_QUOTA_MONEY || quotaTypeEnum == PolicyQuotaTypeEnum.POLICY_QUOTA_PER_MONEY) {
            long money = fillGoodsList.stream().map(index -> cartList.get(index.getIndex())).filter(Objects::nonNull).mapToLong(item -> item.getCartPrice() * item.getCount() - item.getReduceAmount()).sum();
            validMoney += money;

            // 统计count
        } else if (quotaTypeEnum == PolicyQuotaTypeEnum.POLICY_QUOTA_NUM || quotaTypeEnum == PolicyQuotaTypeEnum.POLICY_QUOTA_PER_NUM) {
            int count = fillGoodsList.stream().map(index -> cartList.get(index.getIndex())).filter(Objects::nonNull).mapToInt(CartItem::getCount).sum();
            validCount += count;
        }

        // 统计packageIdList
        List<String> packageIdList = fillGoodsList.stream().map(index -> cartList.get(index.getIndex())).filter(Objects::nonNull).filter(CartHelper::isPackage).map(CartItem::getPackageId).collect(Collectors.toList());

        ValidCondition validCondition = new ValidCondition();
        validCondition.setMoney(validMoney);
        validCondition.setCount(validCount);
        validCondition.setTimes(0);
        validCondition.setPackageIdList(packageIdList);
        return validCondition;
    }


    /**
     * 计算可获得赠品数/加价购数
     *
     * @param includeList 统计组列表
     * @param uid         用户ID
     * @return 次数
     */
    protected long calculateFillTimes(List<FillGoodsGroup> includeGoodsGroups, List<ValidCondition> includeList, Long uid) {
        if (CollectionUtils.isEmpty(includeList)) {
            return 0L;
        }

        long fillTimes = Long.MAX_VALUE;
        for (int i = 0; i < includeList.size(); i++) {
            ValidCondition validCondition = includeList.get(i);
            QuotaEle quotaEle = includeGoodsGroups.get(i).getQuota();
            long validNum = calculateGroupFillTime(validCondition, quotaEle, fillTimes, uid);
            fillTimes = Math.min(fillTimes, validNum);
        }
//        log.info("fill. uid:{}, actId:{}, fillTime:{}", uid, promotionId, fillTimes);
        return fillTimes;
    }

    private long calculateGroupFillTime(ValidCondition validCondition, QuotaEle quotaEle, long fillTimes, Long uid) {
        Integer quotaType = quotaEle.getType();
        PolicyQuotaTypeEnum quotaTypeEnum = PolicyQuotaTypeEnum.getQuotaType(quotaType);
        // 满元
        Long quotaMoney = quotaEle.getMoney();
        if (PolicyQuotaTypeEnum.POLICY_QUOTA_MONEY == quotaTypeEnum) {
            if (validCondition.getMoney() < quotaMoney) {
                return 0L;
            }
            return 1L;
        }
        // 满件
        Integer quotaCount = quotaEle.getCount();
        if (PolicyQuotaTypeEnum.POLICY_QUOTA_NUM == quotaTypeEnum) {
            if (validCondition.getCount() < quotaCount) {
                return 0L;
            }
            return 1L;
        }
        // 每满元
        if (PolicyQuotaTypeEnum.POLICY_QUOTA_PER_MONEY == quotaTypeEnum) {
            // 校验quotaMoney是否为正整数
            if (quotaMoney <= 0) {
//                log.error("quotaMoney is invalid. user:{}, actId:{}", uid, promotionId);
                return 0L;
            }
            // 不满足每满元
            if (validCondition.getMoney() < quotaMoney) {
                return 0L;
            }
            long validTimes = Math.floorDiv(validCondition.getMoney(), quotaMoney);
            if (validTimes < fillTimes) {
                fillTimes = validTimes;
            }
            return fillTimes;
        }
        // 每满件
        if (PolicyQuotaTypeEnum.POLICY_QUOTA_PER_NUM == quotaTypeEnum) {
            // 校验quotaMoney是否为正整数
            if (quotaCount <= 0) {
//                log.error("quota count is invalid. user:{}, actId:{}", uid, promotionId);
                return 0L;
            }
            // 不满足每满元
            if (validCondition.getCount() < quotaCount) {
                return 0L;
            }
            long validTimes = Math.floorDiv(validCondition.getCount(), quotaCount);
            if (validTimes < fillTimes) {
                fillTimes = validTimes;
            }
            return fillTimes;
        }
        return 0L;
    }

    protected List<GoodsIndex> mergeListGroup(List<List<GoodsIndex>> indexGroupList) {
        if (CollectionUtils.isEmpty(indexGroupList)) {
            return Collections.emptyList();
        }
        // 只有一组， 直接返回
        if (indexGroupList.size() == 1) {
            return indexGroupList.get(0);
        }
        // 多组，进行匹配
        List<GoodsIndex> fillGoodsList = new ArrayList<>(indexGroupList.get(0));
        for (int idx = 1; idx < indexGroupList.size(); idx++) {
            List<GoodsIndex> indexList = indexGroupList.get(idx);
            if (CollectionUtils.isEmpty(indexList)) {
                continue;
            }
            List<GoodsIndex> noneMatchList = indexList.stream().filter(index -> fillGoodsList.stream().noneMatch(item -> item.getItemId().equals(index.getItemId()))).collect(Collectors.toList());
            fillGoodsList.addAll(noneMatchList);
        }
        return fillGoodsList;
    }

    /**
     * 阶梯匹配信息
     */
    @Data
    static class LevelMatchContext {
        /**
         * 匹配阶梯
         */
        private QuotaLevel quotaLevel;
        /**
         * 匹配列表
         */
        private List<GoodsIndex> indexList;
        /**
         * 匹配次数
         */
        private long fillTimes;
    }
}
