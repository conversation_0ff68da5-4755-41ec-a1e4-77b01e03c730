package com.xiaomi.nr.promotion.bizplatform.impl;

import com.xiaomi.nr.promotion.api.dto.CartPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Response;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.bizplatform.AbstractBaseBizPlatform;
import com.xiaomi.nr.promotion.domain.activity.impl.CarShopActivityDomain;
import com.xiaomi.nr.promotion.domain.coupon.impl.CarShopCouponDomain;
import com.xiaomi.nr.promotion.domain.point.impl.CarShopPointDomain;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import com.xiaomi.nr.promotion.enums.OrderTypeEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.nr.promotion.model.OrderPromotionDetailModel;
import com.xiaomi.nr.promotion.model.ProductShare;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.provider.CarOrderPromotionProvider;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 车商城
 *
 * <AUTHOR>
 * @date 2024/1/15
 */
@Component
@Slf4j
public class CarShopBizPlatform extends AbstractBaseBizPlatform {

    @Resource
    private ResourceProviderFactory resourceProviderFactory;

    private BaseDomainList baseDomainList;

    @Override
    public BaseDomainList checkBaseDomainList() {
        return baseDomainList;
    }

    @Override
    public BizPlatformEnum getBiz() {
        return BizPlatformEnum.CAR_SHOP;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        baseDomainList = super.instanceBaseDomainList();
        baseDomainList
                .addResourceList(CarShopActivityDomain.class)
                .addResourceList(CarShopCouponDomain.class)
                .addResourceList(CarShopPointDomain.class);
    }

    @Override
    public void generateResponse(DomainCheckoutContext domainCheckoutContext) throws BizError {
        FromInterfaceEnum fromInterface = domainCheckoutContext.getFromInterface();
        CheckoutContext context = domainCheckoutContext.getContext();
        List<CartItem> cartList = domainCheckoutContext.getRequest().getSourceApi() == SourceApi.SUBMIT ? context.getCarts() : domainCheckoutContext.getRequest().getCartList();



        // CHECKOUT_CART: del reduce item list
        if (Objects.equals(fromInterface, FromInterfaceEnum.CHECKOUT_CART)) {
            this.delNoSelectReduceItem(cartList);
        }

        // 计算checkoutPrice
        for (CartItem cartItem : cartList) {
            cartItem.setCheckoutPrice(CartHelper.itemChildSingleCurCheckoutPrice(cartItem));
            if (CollectionUtils.isEmpty(cartItem.getChilds())) {
                continue;
            }
            cartItem.getChilds().stream().filter(Objects::nonNull).forEach(child -> child.setCheckoutPrice(CartHelper.itemChildSingleCurCheckoutPrice(cartItem, child)));
        }

        if (fromInterface.equals(FromInterfaceEnum.CHECKOUT_CART)) {
            CartPromotionResponse response = domainCheckoutContext.getCartResponse();
            response.setCartList(cartList);
            if(Objects.isNull(response.getSummation())) {
                response.setSummation(new Summation());
            }
            response.setPromotions(context.getPromotion());
            response.setCouponList(context.getCouponList());
        }
        
        if (fromInterface.equals(FromInterfaceEnum.CHECKOUT_ORDER)) {
            CheckoutPromotionResponse response = domainCheckoutContext.getResponse();
            response.setCartList(cartList);
            if(Objects.isNull(response.getSummation())) {
                response.setSummation(new Summation());
            }
            response.getSummation().setValidUsePointCount(context.getUserValidPointCount());
            response.setPromotions(context.getPromotion());
            response.setCouponList(context.getCouponList());
        }

        if (fromInterface.equals(FromInterfaceEnum.CHECKOUT_PAGE)) {
            CheckoutPromotionV2Response responseV2 = domainCheckoutContext.getResponseV2();
            responseV2.setCartList(cartList);
            if(Objects.isNull(responseV2.getSummation())) {
                responseV2.setSummation(new Summation());
            }
            responseV2.getSummation().setValidUsePointCount(context.getUserValidPointCount());
            responseV2.getSummation().setCanUsePointCount(context.getCanUsePointCount());
            responseV2.getSummation().setCannotUsePointMsg(context.getCannotUsePointMsg());
            responseV2.setPromotions(context.getPromotion());
            responseV2.setCouponList(context.getCouponList());
        }
    }

    /**
     * 删除未选装商品的reduce item
     * @param cartItemList
     */
    private void delNoSelectReduceItem(List<CartItem> cartItemList) {
        Map<String, CartItem> cartItemMap =
                cartItemList.stream().collect(Collectors.toMap(OrderCartItem::getItemId, Function.identity()));
        for (CartItem item : cartItemList) {
            Boolean selected = item.getSelected();

            // 买赠，加价购寻找主品的 select 状态
            if (SourceEnum.isGiftBargain(item.getSource())) {
                CartItem parentItem = cartItemMap.get(item.getParentItemId());
                if (Objects.isNull(parentItem)) {
                    continue;
                }
                selected = parentItem.getSelected();
            }

            if (Objects.isNull(selected) || Objects.equals(selected, Boolean.FALSE)) {
                item.setReduceItemList(new ArrayList<>());
                item.setCheckoutPrice(item.getOriginalCartPrice());
            }
        }
    }

    @Override
    public void addResource(DomainCheckoutContext domainCheckoutContext) throws BizError {
        CheckoutContext context = domainCheckoutContext.getContext();
        CheckoutPromotionRequest request = domainCheckoutContext.getRequest();
        List<ResourceProvider<?>> resourceProviders = initCarOrderPromotionResource(request);
        context.getResourceHandlers().addAll(resourceProviders);
    }

    private List<ResourceProvider<?>> initCarOrderPromotionResource(CheckoutPromotionRequest request) throws BizError {
        List<ResourceProvider<?>> providerList = new ArrayList<>();
        Long orderId = request.getOrderId();
        Long userId = request.getUserId();

        // 汽车订单优惠
        CarOrderPromotionProvider.OrderPromotionDetailResource carOrderBenefitResource=new CarOrderPromotionProvider.OrderPromotionDetailResource();
        carOrderBenefitResource.setOrderId(orderId);
        carOrderBenefitResource.setOrderScene(BizPlatformEnum.CAR_SHOP.getValue());
        carOrderBenefitResource.setOrderType(OrderTypeEnum.NORMAL_ORDER.getOrderType());
        carOrderBenefitResource.setUserId(userId);
        List<CarOrderPromotionProvider.OrderPromotionDetailPo> promotionDetailList=new ArrayList<>();
        carOrderBenefitResource.setPromotionDetailList(promotionDetailList);
        // 分摊明细, 以promotionId+promotionType维度聚合
        List<CartItem> cartList = request.getCartList();
        Map<String, OrderPromotionDetailModel> promotionDetailMap=new HashMap<>();
        for (CartItem cartItem:cartList){
            List<ReduceDetailItem> reduceItemList = cartItem.getReduceItemList();
            if (reduceItemList==null|| reduceItemList.isEmpty()){
                continue;
            }
            for (ReduceDetailItem reduceItem:cartItem.getReduceItemList()){
                String key=reduceItem.getPromotionId()+"-"+reduceItem.getPromotionType();
                OrderPromotionDetailModel promotionDetail=promotionDetailMap.getOrDefault(key,new OrderPromotionDetailModel());

                promotionDetail.setPromotionId(reduceItem.getPromotionId());
                promotionDetail.setPromotionType(reduceItem.getPromotionType());
                promotionDetail.setBudgetApplyNo(reduceItem.getBudgetApplyNo());
                promotionDetail.setLineNum(reduceItem.getLineNum());
                long reduce = promotionDetail.getReduce() == null ? 0 : promotionDetail.getReduce();
                if (reduceItem.getReduce()!=null){
                    promotionDetail.setReduce(reduce+reduceItem.getReduce());
                }
                // 产品维度分摊
                List<ProductShare> productShareList = promotionDetail.getProductShareList();
                if (productShareList==null){
                    productShareList=new ArrayList<>();
                }
                ProductShare productShare=new ProductShare();
                productShare.setProductId(cartItem.getSsuId());
                productShare.setCount(cartItem.getCount());
                productShare.setReduce(reduceItem.getReduce());
                productShareList.add(productShare);

                promotionDetail.setProductShareList(productShareList);
                promotionDetailMap.put(key,promotionDetail);
            }
        }
        promotionDetailMap.forEach((k,v)->{
            CarOrderPromotionProvider.OrderPromotionDetailPo po=new CarOrderPromotionProvider.OrderPromotionDetailPo();
            po.setPromotionId(v.getPromotionId());
            po.setPromotionType(v.getPromotionType());
            po.setPromotionDetail(GsonUtil.toJson(v));
            promotionDetailList.add(po);
        });
//        if (promotionDetailList.isEmpty()){
//            return providerList;
//        }
        // 空优惠也记录
        carOrderBenefitResource.setPromotionDetailList(promotionDetailList);
        ResourceObject<CarOrderPromotionProvider.OrderPromotionDetailResource> resourceObject = buildCarOrderBenefitResourceObject(orderId, userId, carOrderBenefitResource);
        CarOrderPromotionProvider benefitProvider = (CarOrderPromotionProvider) resourceProviderFactory.getProvider(
                ResourceType.CAR_ORDER_BENEFIT);
        benefitProvider.initResource(resourceObject);
        log.debug("car order benefit provider:{}", benefitProvider);
        providerList.add(benefitProvider);
        return providerList;
    }


    private ResourceObject<CarOrderPromotionProvider.OrderPromotionDetailResource> buildCarOrderBenefitResourceObject(Long orderId, Long userId, CarOrderPromotionProvider.OrderPromotionDetailResource orderBenefit) {
        ResourceObject<CarOrderPromotionProvider.OrderPromotionDetailResource> resourceObject = new ResourceObject<>();
        resourceObject.setContent(orderBenefit);
        resourceObject.setPid(0L);
        resourceObject.setPromotionId(orderId);
        resourceObject.setOrderId(orderId);
        resourceObject.setResourceType(ResourceType.CAR_ORDER_BENEFIT);
        resourceObject.setResourceId(String.format("%s_%s", orderId, userId));
        return resourceObject;
    }

}
