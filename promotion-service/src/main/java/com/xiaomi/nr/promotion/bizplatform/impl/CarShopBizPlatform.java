package com.xiaomi.nr.promotion.bizplatform.impl;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Response;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Summation;
import com.xiaomi.nr.promotion.bizplatform.AbstractBaseBizPlatform;
import com.xiaomi.nr.promotion.domain.point.impl.CarShopPointDomain;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 车商城
 *
 * <AUTHOR>
 * @date 2024/1/15
 */
@Component
public class CarShopBizPlatform extends AbstractBaseBizPlatform {

    private BaseDomainList baseDomainList;

    @Override
    public BaseDomainList checkBaseDomainList() {
        return baseDomainList;
    }

    @Override
    public BizPlatformEnum getBiz() {
        return BizPlatformEnum.CAR_SHOP;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        baseDomainList = super.instanceBaseDomainList();
        baseDomainList.addResourceList(CarShopPointDomain.class);
    }

    @Override
    public void generateResponse(DomainCheckoutContext domainCheckoutContext) throws BizError {
        FromInterfaceEnum fromInterface = domainCheckoutContext.getFromInterface();
        CheckoutContext context = domainCheckoutContext.getContext();
        List<CartItem> cartList = context.getCarts();


        // 计算checkoutPrice
        for (CartItem cartItem : cartList) {
            cartItem.setCheckoutPrice(CartHelper.itemSingleCurCheckoutPrice(cartItem));
        }

        if (fromInterface.equals(FromInterfaceEnum.CHECKOUT_ORDER)) {
            CheckoutPromotionResponse response = domainCheckoutContext.getResponse();
            response.setCartList(cartList);
            if(Objects.isNull(response.getSummation())) {
                response.setSummation(new Summation());
            }
            response.getSummation().setValidUsePointCount(context.getUserValidPointCount());
        }

        if (fromInterface.equals(FromInterfaceEnum.CHECKOUT_PAGE)) {
            CheckoutPromotionV2Response responseV2 = domainCheckoutContext.getResponseV2();
            responseV2.setCartList(cartList);
            if(Objects.isNull(responseV2.getSummation())) {
                responseV2.setSummation(new Summation());
            }
            responseV2.getSummation().setValidUsePointCount(context.getUserValidPointCount());
        }
    }

    @Override
    public void addResource(DomainCheckoutContext domainCheckoutContext) throws BizError {

    }

}
