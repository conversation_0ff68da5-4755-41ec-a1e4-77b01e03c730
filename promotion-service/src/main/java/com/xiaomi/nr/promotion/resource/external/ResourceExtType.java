package com.xiaomi.nr.promotion.resource.external;

/**
 * 外部资源的类型
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
public enum ResourceExtType {
    /**
     * 商品层级关系
     */
    GOODS_HIERARCHY(1, "商品层级关系"),
    /**
     * 门店信息
     */
    ORG_INFO(2, "门店信息"),
    /**
     * 用户券信息
     */
    COUPON(3, "用户券信息"),
    /**
     * Client信息
     */
    CLIENT_INFO(4, "Client信息"),
    /**
     * 全局排除
     */
    GLOBAL_IN_EXCLUDE(5, "全局排除"),
    /**
     * 全局活动排除
     */
    GLOBAL_ACT_EXCLUDE(6, "全局活动排除"),
    /**
     * 全局券排除
     */
    GLOBAL_COUPON_EXCLUDE(7, "全局券排除"),
    /**
     * 礼品卡
     */
    ECARD(8, "礼品卡信息"),
    /**
     * 礼品卡-北京消费券
     */
    ECARD_BEIJING_COUPON(9, "礼品卡北京消费券信息"),
    /**
     * 三方资源-联通华盛
     */
    PHOENIX_HUASHENG(10, "联通华盛外部资源"),
    /**
     * 米家以旧换新
     */
    RECYCLE_ORDER(11, "米家以旧换新"),

    /**
     * 工时去重
     */
    UN_DUPLICATED_WORK_HOUR(12,"工时去重"),

    /**
     * 三方优惠资格码
     */
    PHOENIX_SUBSIDY_QUALIFICATION(13,"三方优惠资格码"),


    GIFT_GOODS_STOCK(14, "赠品库存"),

    /**
     * 用户标签：学生/F会员/……
     */
    USER_PROPERTY_RESULT(15,"用户标签"),

    /**
     * 用户标签：F会员
     */
    PRO_MEMBER_RESULT(16,"F会员"),

    /**
     * 政府三方优惠资格码
     */
    GOVERNMENT_PHOENIX_SUBSIDY_QUALIFICATION(17,"三方优惠资格码"),

    /**
     * VID Ultra 会员资格
     */
    VID_ULTRA_VIP_MEMBER(18, "Ultra会员资格-vid"),

    /**
     * MID Ultra 会员资格
     */
    MID_ULTRA_VIP_MEMBER(19, "Ultra会员资格-mid"),

    ;

    private final int value;

    private final String name;

    ResourceExtType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
