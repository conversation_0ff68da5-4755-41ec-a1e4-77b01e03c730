package com.xiaomi.nr.promotion.domain.coupon.service.base.processor;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Request;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import static com.xiaomi.nr.promotion.constant.CouponConstant.REASON_UNUSABLE;

import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.service.base.type.CouponFactory;
import com.xiaomi.nr.promotion.engine.BizPlatformComponent;
import com.xiaomi.nr.promotion.engine.CouponTool;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponOwnedInfo;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;

/**
 * Created by wangweiyi on 2023/2/1
 */
@Slf4j
public abstract class AbstractCouponProcessor implements CouponProcessorInterface {

    @Autowired
    protected CouponFactory couponFactory;

    /**
     * todo 扩展业务场景时继承此方法
     * @return
     */
    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.NEW_RETAIL;
    }

    /**
     * 优惠劵列表转优惠劵工具
     *
     * @param couponOwnedInfoList 优惠劵列表
     * @return 优惠劵工具
     */
    public List<CouponTool> loadCouponTools(List<CouponOwnedInfo> couponOwnedInfoList) {

        return null;
    }

    @Override
    public List<CouponTool> loadCouponToolsForCheckout(List<CheckoutCoupon> checkoutCouponList, BizPlatformEnum bizPlatformEnum) {
        List<CouponTool> toolList = new ArrayList<>();
        for (CheckoutCoupon checkoutCoupon : checkoutCouponList) {
            try {
                if (checkoutCoupon.getCouponType() != null && checkoutCoupon.getCouponType() != getGeneralType().getCode()) {
                    continue;
                }
                PromotionToolType promotionToolType = PromotionToolType.fromTypeId(checkoutCoupon.getType());
                if (promotionToolType == null) {
                    continue;
                }
                CouponTool couponTool = couponFactory.getByTypeAndBiz(promotionToolType, bizPlatformEnum);
                boolean success = couponTool.load(checkoutCoupon);
                if (success) {
                    toolList.add(couponTool);
                }
            } catch (Exception e) {
                log.error("loadCurrentCouponTools error. couponId:{}, info:{}, err:", checkoutCoupon.getCouponId(), GsonUtil.toJson(checkoutCoupon),  e);
            }
        }
        return toolList;
    }

    /**
     * 默认的选券逻辑，包括商品券和补贴券
     * @param request
     * @param validCouponList
     * @param invalidCouponList
     * @param map
     * @param checkoutContext
     * @return
     * @throws BizError
     */
    @Override
    public Coupon setSelectCoupon(CheckoutPromotionV2Request request, TreeSet<Coupon> validCouponList, List<Coupon> invalidCouponList, Map<Long, CouponCheckoutResult> map, CheckoutContext checkoutContext)
            throws BizError {
        if (CollectionUtils.isEmpty(validCouponList)) {
            return null;
        }
        Coupon selectCoupon = null;

        List<Long> couponIdList = request.getCouponIds();

        //我这层未勾选时，露出推荐券

        // 现金券，若面值大于可用劵商品总额不默认选
        if (CollectionUtils.isEmpty(couponIdList)) {
            if (request.getUseDefaultCoupon()) {
                Coupon bestCoupon = validCouponList.first();
                CouponCheckoutResult result = map.get(bestCoupon.getId());

                //如果实际扣减金额=0，不自动勾选
                if (result.getReduceAmount() == 0) {
                    return null;
                }

                //扣减金额大于可用商品总额，不自动勾选
                if (bestCoupon.getType() == CouponTypeEnum.CASH.getType()) {
                    if (result.getCouponReduce() >= result.getValidGoodsPrice()) {
                        return null;
                    }
                }
                selectCoupon = bestCoupon;
            }
        } else {
            List<Coupon> selectCouponList = new ArrayList<>();
            for (Coupon coupon : validCouponList) {
                if (couponIdList.contains(coupon.getId())) {
                    selectCouponList.add(coupon);
                }
            }

            if (CollectionUtils.isEmpty(selectCouponList)) {
                return null;
            }

            if (selectCouponList.size() > 1) {
                throw ExceptionHelper.create(ErrCode.ERR_TOO_MANY_COUPONS, "同种类型最多只能使用一张优惠券");
            }
            selectCoupon = selectCouponList.get(0);
            //todo 处理互斥


            for (Coupon coupon: invalidCouponList) {
                if (couponIdList.contains(coupon.getId())) {
                    selectCoupon = coupon;
                    String reason = selectCoupon.getExInfo().getOrDefault(REASON_UNUSABLE, "");
                    //选择了一张不存在的券
                    throw ExceptionHelper.create(ErrCode.ERR_COUPON_INVALID, reason);
                }
            }


        }

        if (selectCoupon == null) {
            return null;
        }
        selectCoupon.getExInfo().put("default_check", "true");
        selectCoupon.setChecked(true);
        return selectCoupon;
    }



    protected  <T> ResourceObject<T> buildResource(T content, Long orderId, String couponId) {
        ResourceObject<T> resourceObject = new ResourceObject<>();
        resourceObject.setContent(content);
        resourceObject.setResourceId(String.format("%s_%s", orderId, couponId));
        resourceObject.setResourceType(ResourceType.COUPON);
        resourceObject.setPromotionId(-1L);
        resourceObject.setOrderId(orderId);
        resourceObject.setPid(-1L);
        return resourceObject;
    }
}
