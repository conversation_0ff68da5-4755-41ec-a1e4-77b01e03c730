package com.xiaomi.nr.promotion.rpc.usertag;

import com.xiaomi.nr.promotion.config.yaml.PromotionTotalZkConfig;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.retail.user.tag.service.UserTagService;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 人群服务java版本
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserTagProxyService {

    @Reference(interfaceClass = UserTagService.class, version = "1.0", timeout = 200, group = "${user.dubbo.group}")
    private UserTagService userTagService;

    @Value("${userTagToken}")
    private String token;

    @Autowired
    private PromotionTotalZkConfig promotionTotalZkConfig;

    /**
     * 获取用户人群标签
     *
     * @param userId 用户ID
     * @return 人群标签
     * @throws BizError 业务异常
     */
    public List<String> getUserTagList(Long userId) throws BizError {
        // 如果服务降级
        if (promotionTotalZkConfig.isUserTagReqFallback()) {
            log.info("service userTag fallback switch on. userId:{}", userId);
            return Collections.emptyList();
        }
        // 开始请求
        Result<Map<String, Long>> response;
        try {
            response = userTagService.getUserTagList(token, String.valueOf(userId));
        } catch (Exception e) {
            log.error("invoke rpc getUserTagList error. userId:{}, token:{}, err", userId, token, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
        if (response.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc getUserTagList fail. userId:{}, token:{}, code:{} message:{}", userId, token, response.getCode(), response.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "获取用户人群信息失败");
        }
        return new ArrayList<>(response.getData().keySet());
    }
}
