package com.xiaomi.nr.promotion.enums;

import com.google.common.collect.ImmutableMap;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;

import java.util.Map;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 2023/8/16
 * 业务平台概念，用来分离检索、计算、规则流程
 */
public enum BizPlatformEnum {

    /**
     * 新零售
     */
    NEW_RETAIL(0),

    /**
     *
     */
    TUAN_GOU(1),

    /**
     * 融合三方
     */
    MERGE_THIRD(2),

    /**
     * 汽车
     */
    CAR(3),

    /**
     * 汽车维保售后
     */
    MAINTENANCE_REPAIR(4),

    /**
     * 车商城
     */
    CAR_SHOP(5);


    private final int value;

    BizPlatformEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    private static final Map<ChannelEnum, BizPlatformEnum> CHANNEL_TO_BIZ = ImmutableMap.<ChannelEnum, BizPlatformEnum> builder()
            //汽车
            .put(ChannelEnum.CAR_VEHICLE, CAR)
            //车商城
            .put(ChannelEnum.CAR_SHOP, CAR_SHOP)
            //团购
            .put(ChannelEnum.B2T_C_CUSTOMER, TUAN_GOU)
            .put(ChannelEnum.B2T_GOV_BIG_CUSTOMER, TUAN_GOU)
            .put(ChannelEnum.B2T_MIJIA_BIG_CUSTOMER, TUAN_GOU)
            //融合三方
            .put(ChannelEnum.JD_HOME_DIRECT, MERGE_THIRD)
            .put(ChannelEnum.JD_HOME_SPECIALTY, MERGE_THIRD)
            .put(ChannelEnum.JD_HOME_AUTHORIZED, MERGE_THIRD)
            .put(ChannelEnum.ELM_DIRECT, MERGE_THIRD)
            .put(ChannelEnum.ELM_SPECIALTY, MERGE_THIRD)
            .put(ChannelEnum.ELM_AUTHORIZED, MERGE_THIRD)
            .put(ChannelEnum.MT_DIRECT, MERGE_THIRD)
            .put(ChannelEnum.MT_SPECIALTY, MERGE_THIRD)
            .put(ChannelEnum.MT_AUTHORIZED, MERGE_THIRD)
            .put(ChannelEnum.DY_DIRECT, MERGE_THIRD)
            .put(ChannelEnum.DY_SPECIALTY, MERGE_THIRD)
            // 售后维保
            .put(ChannelEnum.CAR_MAINTENANCE_REPAIR,MAINTENANCE_REPAIR)
            .build();


    public static BizPlatformEnum findByChannel(Integer channel) {
        if (channel == null) {
            return NEW_RETAIL;
        }
        ChannelEnum channelEnum = ChannelEnum.getByValue(channel);
        return CHANNEL_TO_BIZ.getOrDefault(channelEnum, NEW_RETAIL);
    }







}
