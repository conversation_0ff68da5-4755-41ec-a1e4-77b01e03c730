package com.xiaomi.nr.promotion.flows;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.md.promotion.admin.api.constant.PromotionTypeEnum;
import com.xiaomi.nr.promotion.activity.ActivityRunner;
import com.xiaomi.nr.promotion.activity.pool.ActSearchParam;
import com.xiaomi.nr.promotion.activity.pool.ActivityPool;
import com.xiaomi.nr.promotion.activity.pool.CarActivitySearcher;
import com.xiaomi.nr.promotion.annotation.log.Log;
import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.nr.promotion.api.dto.enums.ProductDepartmentEnum;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.OnsaleProductRuleDto;
import com.xiaomi.nr.promotion.api.service.PromotionDubboService;
import com.xiaomi.nr.promotion.bizplatform.BaseBizPlatform;
import com.xiaomi.nr.promotion.bizplatform.BizPlatformFactory;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.domain.coupon.facade.CouponFacade;
import com.xiaomi.nr.promotion.domain.redpackage.service.mishop.MiShopRedPacketService;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.PromotionPriceNormProvider;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.nr.promotion.model.RequestContext;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.ExternalProviderFactory;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.resource.impl.ResourceManager;
import com.xiaomi.nr.promotion.resource.model.ResourceManageContext;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.util.CheckoutRequestHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.RequestParamPreHandle;
import com.xiaomi.nr.promotion.util.RequestParamValidator;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.xiaomi.nr.promotion.constant.ApplicationConstant.CONTEXT_PARAM_USER_LEVEL;

/**
 * 优惠中心主服务，提供中台用户端的优惠计算能力
 * <p>
 * 核心能力：
 * 1. 为购物车/结算页提供金额计算
 * </p>
 *
 * <AUTHOR>
 * @date 2021/3/22
 */
@Slf4j
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0", retries = 0)
@ApiModule(value = "优惠计算服务", apiInterface = PromotionDubboService.class)
public class PromotionDubboServiceImpl implements PromotionDubboService {
    @Autowired
    private ExternalProviderFactory externalProviderFactory;
    @Autowired
    private ActivityRunner activityRunner;
    @Autowired
    private ActivityPool activityPool;
    @Autowired
    private PromotionSceneFlowEngine promotionSceneFlowEngine;
    @Autowired
    private CouponFacade couponFacade;
    @Autowired
    private MiShopRedPacketService redpacketService;
    @Autowired
    private ResourceManager resourceManager;
    @Autowired
    private CarActivitySearcher carActivitySearcher;
    @Autowired
    private BizPlatformFactory bizPlatformFactory;
    @Autowired
    @Qualifier("checkoutAsyncTaskExecutor")
    private ThreadPoolTaskExecutor checkoutAsyncTaskExecutor;

    /**
     * 优惠计算服务
     * 仅订单有权调用
     * 请求来源分别来自购物车列表、结算页、提交订单
     * <p>
     * - 购物车请求(cart list)
     * 刷新购物车、勾选购物车商品等操作会调用结算接口，重新计算优惠 以及 购物车商品可用优惠信息
     * <p>
     * - 结算页请求（checkout）
     * 订单结算页会调用接口计算优惠金额
     * <p>
     * - 提交订单（submit）
     * 在结算页支付时，由订单调用。订单调用此接口重新结算优惠信息，优惠会锁定订单对应的优惠资源
     * 包括 限时购商品限购数量，优惠券，活动库存
     * <p>
     * 其他信息：
     * 结算请求结构和返回结构，以及它们是如何返回的，请在对应的类中查看
     * {@link CheckoutPromotionRequest}
     * {@link CheckoutPromotionResponse}
     *
     * @param request 请求参数
     * @return 相应参数
     */
    @Override
    @ApiDoc("结算下单接口")
    @Log(name = "PromotionDubboService#checkoutPromotion")
    public Result<CheckoutPromotionResponse> checkoutPromotion(CheckoutPromotionRequest request) {
        long startTime = System.currentTimeMillis();
        try {
            // 校验入参 to-err: throw BizErr
            RequestParamPreHandle.handleCartList(request.getCartList());
            RequestParamValidator.valid(request);

            RequestContext.getCurrent().setRequestTime(startTime);
            CheckoutPromotionResponse response = new CheckoutPromotionResponse();
            CheckoutContext checkoutContext = new CheckoutContext();
            checkoutContext.setBizPlatform(BizPlatformEnum.findByChannel(request.getChannel()));
            checkoutContext.setFromInterface(FromInterfaceEnum.CHECKOUT_ORDER);

            // 请求外部资源
            prepareExternalData(request, checkoutContext);

            // 网格化组合模式，计算各个促销领域
            BaseBizPlatform executor = bizPlatformFactory.getExecutor(checkoutContext.getBizPlatform());
            DomainCheckoutContext domainCheckoutContext=new DomainCheckoutContext();
            domainCheckoutContext.setFromInterface(FromInterfaceEnum.CHECKOUT_ORDER);
            domainCheckoutContext.setRequest(request);
            domainCheckoutContext.setResponse(response);
            domainCheckoutContext.setContext(checkoutContext);
            executor.checkout(domainCheckoutContext);

            // 添加资源
            executor.addResource(domainCheckoutContext);

            // 锁定优惠资源
            if (request.getSourceApi() == SourceApi.SUBMIT && !request.getNoSaveDbSubmit()) {
                ResourceManageContext manageContext = ResourceManageContext.fromCheckoutRequest(request, checkoutContext);
                resourceManager.lock(manageContext);
            }
            // 汇总返回数据
            executor.generateResponse(domainCheckoutContext);
            return Result.success(response);
        } catch (BizError e) {
            if (request.getSourceApi() == SourceApi.CHECKOUT && e.getCode() == ErrCode.ERR_EMPTY_CART.getCode()) {
                return Result.success(new CheckoutPromotionResponse());
            }
            log.warn("Biz Exception on checkoutPromotion. request:{} bizErr:", GsonUtil.toJson(request), e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on checkoutPromotion. request:{} err:", GsonUtil.toJson(request), e);
            return Result.fromException(e, "系统开小差，请稍后再试");
        }
    }




    /**
     * 提交优惠结算
     * 仅订单服务有权调用
     * notes: 调用前需要先进行结算调用 {@link #checkoutPromotion}，进行资源占用
     * <p>
     * - 提交订单（submit）
     * 在结算页支付时，由订单调用。订单调用此接口重新结算优惠信息，优惠会锁定订单对应的优惠资源
     * 包括 限时购商品限购数量，优惠券，活动库存
     *
     * @param request 提交请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("结算提交接口")
    @Log(name = "PromotionDubboService#submitPromotion")
    public Result<SubmitPromotionResponse> submitPromotion(SubmitPromotionRequest request) {
        try {
            SubmitPromotionResponse response = new SubmitPromotionResponse();
            ResourceManageContext context = ResourceManageContext.fromCommitRequest(request);
            resourceManager.commit(context);
            log.info("submit ok. request:{}", request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("Exception on submitPromotion. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 订单服务失败回滚优惠结算接口
     * 仅订单服务可用
     * <p>
     * - 占用资源回滚
     * 在流程异常或者资源释放时调用
     *
     * @param request 回滚请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("结算回滚接口")
    @Log(name = "PromotionDubboService#rollbackPromotion")
    public Result<RollbackPromotionResponse> rollbackPromotion(RollbackPromotionRequest request) {
        try {
            RollbackPromotionResponse response = new RollbackPromotionResponse();
            ResourceManageContext context = ResourceManageContext.fromRollbackRequest(request);
            resourceManager.rollback(context);
            response.setResult(true);
            log.info("rollback ok. request:{}", request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("Exception on rollbackPromotion. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 订单服务失败回滚优惠结算接口
     *
     * <p>
     * - 占用资源回滚（部分）
     * 在流程异常或者资源释放时调用
     *
     * @param request 回滚请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("部分资源回滚接口")
    @Log(name = "PromotionDubboService#rollbackPromotion")
    public Result<PartRollbackPromotionResponse> partRollbackPromotion(PartRollbackPromotionRequest request) {
        try {
            if (CollectionUtils.isEmpty(request.getResourceTypes())) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "参数resourceTypes不能为空");
            }
            List<ResourceType> handleResources = request.getResourceTypes().stream().map(ResourceType::valueOf)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            PartRollbackPromotionResponse response = new PartRollbackPromotionResponse();
            ResourceManageContext context = ResourceManageContext.fromMessage(request.getOrderId());
            context.setResourceTypes(handleResources);
            resourceManager.partRollback(context);
            response.setResult(true);
            log.info("part rollback ok. request:{}", request);
            return Result.success(response);
        } catch (Exception e) {
            log.error("Exception on partRollbackPromotion. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 获取商品当前价格接口
     * 价保服务使用，用于对比商品下单时价格和当前价格
     * 优惠劵和红包加入金额计算
     *
     * @param request 获取商品的当前价格请求参数
     * @return 响应
     */
    @Override
    @ApiDoc("价保接口")
    @Log(name = "PromotionDubboService#getProductCurrentPrice")
    public Result<GetProductCurrentPriceResponse> getProductCurrentPrice(GetProductCurrentPriceRequest request) {
        try {
            // 校验入参 to-err: throw BizErr
            RequestParamPreHandle.handleCartList(request.getCartList());
            RequestParamValidator.validProtectPriceRequest(request);

            GetProductCurrentPriceResponse response = new GetProductCurrentPriceResponse();
            CheckoutPromotionResponse checkoutResp = new CheckoutPromotionResponse();
            CheckoutPromotionRequest checkoutReq = CheckoutRequestHelper.convertProtectPriceRequest(request);
            checkoutReq.setFromPriceProtect(true);

            CheckoutContext checkoutContext = new CheckoutContext();
            checkoutContext.setIsProtectPrice(true);
            checkoutContext.setOrderId(request.getOrderId());
            checkoutContext.setFromInterface(FromInterfaceEnum.CHECKOUT_CART);

            // 请求外部资源
            prepareExternalData(checkoutReq, checkoutContext);

            // 1. 处理活动
            activityRunner.checkoutForProtectPrice(checkoutReq, checkoutResp, checkoutContext);

            // 2. 处理劵
            couponFacade.checkoutForProtectPrice(checkoutReq, checkoutResp, checkoutContext);

            // 3. 处理红包
            redpacketService.checkoutForProtectPrice(request.getUserId(), checkoutReq.getCartList(), request.getRedPacketUsedList(), checkoutContext);
            convertToGetProductCurrentPriceResponse(response, checkoutContext);
            return Result.success(response);
        } catch (BizError e) {
            log.warn("Biz Exception on getProductCurrentPrice. request:{}", request, e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on getProductCurrentPrice. request:{}", request, e);
            return Result.fromException(e);
        }
    }

    /**
     * 获取优惠价
     *
     * @param request 请求对象
     * @return 价格信息响应
     */
    @Override
    @ApiDoc("获取优惠价格接口")
    @Log(name = "PromotionDubboService#getPromotionPrice")
    public Result<GetPromotionPriceResponse> getPromotionPrice(GetPromotionPriceRequest request) {
        try {
            if (request.getChannel() == null) {
                log.warn("param channel is empty.");
                throw ExceptionHelper.create(GeneralCodes.ParamError, "channel不能为空");
            }
            if (CollectionUtils.isEmpty(request.getGoodsList())) {
                log.warn("param goodsList is empty. request:{}", request);
                throw ExceptionHelper.create(GeneralCodes.ParamError, "goodsList is empty");
            }
            for (GoodsDto goodsDto : request.getGoodsList()) {

                if (goodsDto.getSsuId() == null) {
                    log.warn("param ssuId is empty. item:{}", goodsDto);
                    throw ExceptionHelper.create(GeneralCodes.ParamError, "ssuId不能为空");
                }

                if (goodsDto.getId() == null) {
                    goodsDto.setId(goodsDto.getSsuId());
                }

                if (!BizPlatformEnum.findByChannel(request.getChannel()).equals(BizPlatformEnum.CAR)){
                    if (goodsDto.getGoodsType() == null) {
                        log.warn("param goodsType is empty. ssuId:{}", goodsDto.getSsuId());
                        throw ExceptionHelper.create(GeneralCodes.ParamError, "goodsType不能为空");
                    }
                    if (goodsDto.getDepartment() == null) {
                        log.warn("param department is empty. ssuId:{}", goodsDto.getSsuId());
                        throw ExceptionHelper.create(GeneralCodes.ParamError, "department不能为空");
                    }
                }
            }
            Integer channel = request.getChannel();
            String userLevel = request.getUserLevel();
            List<GoodsDto> goodsList = request.getGoodsList();
            Map<Long, PromotionPriceDTO> priceMap = Maps.newHashMap();
            goodsList.forEach(goodsDto ->
                    handlePromotionPrice(channel, goodsDto, priceMap, userLevel));
            GetPromotionPriceResponse response = new GetPromotionPriceResponse();
            response.setPriceMap(priceMap);
            return Result.success(response);
        } catch (Exception e) {
            log.error("Exception on getPromotionPrice. request:{}", request, e);
            return Result.fromException(e);
        }
    }


    private boolean isBindMainAccessory(Integer department, Boolean bindMainAccessory) {
        if (department == null || bindMainAccessory == null) {
            return false;
        }
        return department.equals(ProductDepartmentEnum.S2.getValue()) && bindMainAccessory;
    }

    private void handlePromotionPrice(Integer channel, GoodsDto goodsDto, Map<Long, PromotionPriceDTO> priceMap, String userLevel) {
        ArrayList<String> skuPackageList = Lists.newArrayList(String.valueOf(goodsDto.getSsuId()), String.valueOf(goodsDto.getId()));
        // 团购渠道，且是主附品强绑定,将子节点中主附品sku加入检索列表
        if (isBindMainAccessory(goodsDto.getDepartment(),goodsDto.getBindMainAccessory())){
            // 团购主附品强绑定场景下，一定有两个子品，前面已经校验
            for (CartItemChild child:goodsDto.getChilds()){
                skuPackageList.add(child.getSku());
            }
        }
        // 活动检索
        List<ActivityTool> activityTools;
        Map<String, String> params = new HashMap<>();
        if (BizPlatformEnum.findByChannel(channel).equals(BizPlatformEnum.CAR)){
            List<CartItem> cartItemList=new ArrayList<>();
            CartItem cartItem=new CartItem();
            cartItem.setSsuId(goodsDto.getSsuId());
            cartItemList.add(cartItem);
            ActSearchParam param = new ActSearchParam()
                    .setChannel(channel)
                    .setGoodsList(carActivitySearcher.createSearchGoods(cartItemList));
            activityTools = carActivitySearcher.searchActivity(param);
        }else {
            activityTools = activityPool.getCurrent(null, null, Collections.singletonList(channel),skuPackageList
                    , Collections.singletonList(goodsDto.getDepartment()));
            // 根据场景过滤
            activityTools = promotionSceneFlowEngine.filterSceneAct(activityTools, channel, goodsDto.getGoodsType(), goodsDto.getDepartment());
            params.put(CONTEXT_PARAM_USER_LEVEL, userLevel);
        }

        // 活动处理
        for (ActivityTool activityTool : activityTools) {
            if (!(activityTool instanceof PromotionPriceNormProvider)) {
                continue;
            }
            PromotionPriceNormProvider priceNormProvider = (PromotionPriceNormProvider) activityTool;
            Map<Long, PromotionPriceDTO> priceDTOMap = priceNormProvider.getGoodsPromotionPrice(Collections.singletonList(goodsDto), params);
            PromotionPriceDTO priceDTO = priceDTOMap.get(goodsDto.getSsuId());
            if (priceDTO == null) {
                return;
            }
            // 合并
            priceMap.merge(goodsDto.getSsuId(), priceDTO, (price1, price2) -> {
                List<PromotionInfoDTO> promotionInfos = new ArrayList<>();
                promotionInfos.addAll(Optional.ofNullable(price1.getPromotionInfos()).orElse(Collections.emptyList()));
                promotionInfos.addAll(Optional.ofNullable(price2.getPromotionInfos()).orElse(Collections.emptyList()));
                if (price1.getPrice() == null) {
                    price2.setPromotionInfos(promotionInfos);
                    return price2;
                }
                if (price2.getPrice() == null) {
                    price1.setPromotionInfos(promotionInfos);
                    return price1;
                }
                if (price1.getPrice() <= price2.getPrice()) {
                    price1.setPromotionInfos(promotionInfos);
                    return price1;
                }
                price2.setPromotionInfos(promotionInfos);
                return price2;
            });
        }
        // 过滤无效的直降活动优惠信息
        filterOnSalePromotionInfo(goodsDto,priceMap);
    }

    private void filterOnSalePromotionInfo(GoodsDto goodsDto, Map<Long, PromotionPriceDTO> priceMap){
        // 对于promotionInfos信息，商品B端只用到了promotionType、rule（promotionPrice），用不到 id、startTime、endTime
        // 对于直降活动，只保留最低价格的活动即可
        if (priceMap==null|| priceMap.get(goodsDto.getSsuId())==null){
            return;
        }
        List<PromotionInfoDTO> promotionInfos = priceMap.get(goodsDto.getSsuId()).getPromotionInfos();

        // 获取直降价优惠信息
        List<PromotionInfoDTO> onSalePromotionList = promotionInfos.stream().filter(f -> f.getPromotionType().equals(PromotionTypeEnum.ONSALE.code)).collect(Collectors.toList());
        if (onSalePromotionList.size()>1){
            // 删除原有直降价优惠信息
            promotionInfos.removeIf(info->info.getPromotionType().equals(PromotionTypeEnum.ONSALE.code));
            // 获取最小直降价
            Long minPromotionPrice = goodsDto.getPrice();
            for (PromotionInfoDTO promotionInfo:onSalePromotionList){
                OnsaleProductRuleDto onSaleProductRule = GsonUtil.fromJson(promotionInfo.getRule(), OnsaleProductRuleDto.class);
                if (onSaleProductRule!=null&&onSaleProductRule.getPromotionPrice()!=null){
                    minPromotionPrice=Math.min(minPromotionPrice,onSaleProductRule.getPromotionPrice());
                }
            }
            // 重新构造直降优惠信息
            PromotionInfoDTO promotionInfoDTO=new PromotionInfoDTO();
            promotionInfoDTO.setPromotionType(PromotionTypeEnum.ONSALE.code);
            OnsaleProductRuleDto onsaleProductRuleDto=new OnsaleProductRuleDto();
            onsaleProductRuleDto.setPromotionPrice(minPromotionPrice);
            promotionInfoDTO.setRule(GsonUtil.toJson(onsaleProductRuleDto));
            // 填充直降优惠信息
            promotionInfos.add(promotionInfoDTO);
        }
    }

    /**
     * 优惠结算服务V2
     * - 结算页请求（checkout）
     * 订单结算页会调用接口计算优惠金额
     * <p>
     * 其他信息：
     * 结算请求结构和返回结构，以及它们是如何返回的，请在对应的类中查看
     * {@link CheckoutPromotionV2Request}
     * {@link CheckoutPromotionV2Response}
     *
     * @param requestV2 结算请求参数
     * @return 结算返回数据
     */
    @Override
    @ApiDoc("结算V2接口")
    @Log(name = "PromotionDubboService#checkoutPromotionV2")
    public Result<CheckoutPromotionV2Response> checkoutPromotionV2(CheckoutPromotionV2Request requestV2) {
        long startTime = System.currentTimeMillis();
        try {
            // v2转换为原结算请求参数去处理劵之前的逻辑
            requestV2.setSourceApi(SourceApi.SUBMIT);
            CheckoutPromotionRequest request = CheckoutRequestHelper.convertV2Request(requestV2);

            // 校验入参 to-err: throw BizErr
            RequestParamPreHandle.handleCartList(request.getCartList());
            RequestParamValidator.valid(request);

            RequestContext.getCurrent().setRequestTime(startTime);
            CheckoutPromotionResponse response = new CheckoutPromotionResponse();
            CheckoutPromotionV2Response responseV2 = new CheckoutPromotionV2Response();
            CheckoutContext checkoutContext = new CheckoutContext();
            checkoutContext.setBizPlatform(BizPlatformEnum.findByChannel(request.getChannel()));
            checkoutContext.setFromInterface(FromInterfaceEnum.CHECKOUT_PAGE);

            // 请求外部资源
            prepareExternalData(request, checkoutContext);

            // 网格化组合模式，计算各个促销领域
            BaseBizPlatform executor = bizPlatformFactory.getExecutor(checkoutContext.getBizPlatform());
            DomainCheckoutContext domainCheckoutContext=new DomainCheckoutContext();
            domainCheckoutContext.setFromInterface(FromInterfaceEnum.CHECKOUT_PAGE);
            domainCheckoutContext.setRequest(request);
            domainCheckoutContext.setRequestV2(requestV2);
            domainCheckoutContext.setResponse(response);
            domainCheckoutContext.setContext(checkoutContext);
            domainCheckoutContext.setResponseV2(responseV2);
            executor.checkout(domainCheckoutContext);

            // 汇总返回数据
            executor.generateResponse(domainCheckoutContext);
            return Result.success(responseV2);
        } catch (BizError e) {
            if (e.getCode() == ErrCode.ERR_EMPTY_CART.getCode()) {
                return Result.success(new CheckoutPromotionV2Response());
            }
            log.warn("Biz Exception on checkoutPromotionV2. request:{} bizErr:", GsonUtil.toJson(requestV2), e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on checkoutPromotionV2. request:{} err:", GsonUtil.toJson(requestV2), e);
            return Result.fromException(e, "系统开小差，请稍后再试");
        }
    }

    /**
     * 购物车优惠结算服务
     * - 购物车请求（cart checkout）
     * 购物车列表会调用接口计算购物车商品可用优惠信息及优惠金额
     * <p>
     * 其他信息：
     * 结算请求结构和返回结构，以及它们是如何返回的，请在对应的类中查看
     * {@link CartPromotionRequest}
     * {@link CartPromotionResponse}
     *
     * @param request 结算请求参数
     * @return 结算返回数据
     */
    @Override
    @ApiDoc("购物车结算接口")
    @Log(name = "PromotionDubboService#cartCheckoutPromotion")
    public Result<CartPromotionResponse> cartCheckoutPromotion(CartPromotionRequest request) {
        RequestParamPreHandle.handleCartList(request.getCartList());
        try {
            // 校验入参 to-err: throw BizErr
            CartPromotionResponse cartResponse = doCartPromotionCheckout(request);
            return Result.success(cartResponse);
        } catch (BizError e) {
            if (e.getCode() == ErrCode.ERR_EMPTY_CART.getCode()) {
                return Result.success(new CartPromotionResponse());
            }
            log.warn("Biz Exception on cartCheckoutPromotion. request:{} b", GsonUtil.toJson(request), e);
            return Result.fromException(e, e.getMessage());
        } catch (Exception e) {
            log.error("Exception on cartCheckoutPromotion. request:{} ", GsonUtil.toJson(request), e);
            return Result.fromException(e, "系统开小差，请稍后再试");
        }
    }

    private CartPromotionResponse doCartPromotionCheckout(CartPromotionRequest request)
            throws Exception {

        long startTime = System.currentTimeMillis();

        CheckoutPromotionRequest checkoutRequest = CheckoutRequestHelper.generateRequestFromCart(request);
        CheckoutPromotionV2Request requestV2 = CheckoutRequestHelper.generateRequestV2FromCart(request);
        RequestParamValidator.valid(checkoutRequest);


        RequestContext.getCurrent().setRequestTime(startTime);
        CheckoutPromotionResponse response = new CheckoutPromotionResponse();
        CheckoutContext checkoutContext = new CheckoutContext();
        checkoutContext.setBizPlatform(BizPlatformEnum.findByChannel(request.getChannel()));
        checkoutContext.setFromInterface(FromInterfaceEnum.CHECKOUT_CART);

        // 请求外部资源
        prepareExternalData(checkoutRequest, checkoutContext);

        // 网格化组合模式，计算各个促销领域
        BaseBizPlatform executor = bizPlatformFactory.getExecutor(checkoutContext.getBizPlatform());
        DomainCheckoutContext domainCheckoutContext=new DomainCheckoutContext();
        domainCheckoutContext.setFromInterface(FromInterfaceEnum.CHECKOUT_CART);
        domainCheckoutContext.setRequest(checkoutRequest);
        domainCheckoutContext.setRequestV2(requestV2);
        domainCheckoutContext.setCartRequest(request);
        domainCheckoutContext.setResponse(response);
        domainCheckoutContext.setContext(checkoutContext);
        executor.checkout(domainCheckoutContext);

        // 汇总返回数据
        CartPromotionResponse cartResponse = new CartPromotionResponse();
        domainCheckoutContext.setCartResponse(cartResponse);
        executor.generateResponse(domainCheckoutContext);
        return cartResponse;
    }

    @Override
    @ApiDoc("购物车分组结算接口")
    @Log(name = "PromotionDubboService#cartCheckoutPromotionByGroup")
    public Result<CartPromotionResponse> cartCheckoutPromotionByGroup(CartPromotionRequest request) {
        try {
            //拆分分组
            List<CartPromotionRequest> requestList = splitCartGroupRequest(request);

            //按照分组结算

            List<Future<CartPromotionResponse>> futureList = new ArrayList<>();
            for (CartPromotionRequest cartPromotionRequest : requestList) {

                Future<CartPromotionResponse> future = checkoutAsyncTaskExecutor.submit(() -> doCartPromotionCheckout(cartPromotionRequest));
                futureList.add(future);

            }

            List<CartPromotionResponse> responseList = new ArrayList<>();
            for (Future<CartPromotionResponse> cartPromotionResponseFuture : futureList) {
                CartPromotionResponse cartPromotionResponse = cartPromotionResponseFuture.get(2000, TimeUnit.MILLISECONDS);
                responseList.add(cartPromotionResponse);
            }

            //合并分组
            return Result.success(mergeCartGroupResponse(responseList));


        }  catch (Exception e) {
            if (e instanceof BizError) {

                if (((BizError) e).getCode() == ErrCode.ERR_EMPTY_CART.getCode()) {
                    return Result.success(new CartPromotionResponse());
                }
                log.warn("Biz Exception on cartCheckoutPromotionByGroup. request:{}", GsonUtil.toJson(request), e);
                return Result.fromException(e, e.getMessage());
            }
            log.error("Exception on cartCheckoutPromotionByGroup. request:{} ", GsonUtil.toJson(request), e);
            return Result.fromException(e, "系统开小差，请稍后再试");
        }
    }

    private List<CartPromotionRequest> splitCartGroupRequest(CartPromotionRequest request) {

        Map<String, List<CartItem>> groupMap = new HashMap<>();

        //主商品列表
        Map<String, CartItem> mainUnitMap = new HashMap<>();
        for (CartItem cartItem : request.getCartList()) {
            if (StringUtils.isBlank(cartItem.getParentItemId())) {
                mainUnitMap.put(cartItem.getItemId(), cartItem);
            } else {
                List<CartItem> valueList = groupMap.getOrDefault(cartItem.getParentItemId(), new ArrayList<>());
                valueList.add(cartItem);
                groupMap.put(cartItem.getParentItemId(), valueList);

            }
        }

        List<CartPromotionRequest> splitRequestList = new ArrayList<>();

        for (Map.Entry<String, List<CartItem>> entry : groupMap.entrySet()) {
            CartItem mainUnit = mainUnitMap.get(entry.getKey());
            if (mainUnit == null) {
                continue;
            }
            List<CartItem> newCartList = new ArrayList<>();
            List<CartItem> subUnitList = entry.getValue();
            newCartList.add(mainUnit);
            newCartList.addAll(subUnitList);
            CartPromotionRequest cloneRequest = SerializationUtils.clone(request);
            cloneRequest.setCartList(newCartList);
            splitRequestList.add(cloneRequest);
        }
        return splitRequestList;

    }

    private CartPromotionResponse mergeCartGroupResponse(List<CartPromotionResponse> responseList) {
        List<CartItem> mergedItems = new ArrayList<>();
        Map<String, PromotionInfo> promotionInfoMap = new HashMap<>();
        for (CartPromotionResponse response : responseList) {
            for (PromotionInfo promotion : response.getPromotions()) {
                if (promotionInfoMap.containsKey(promotion.getPromotionId())) {

                    PromotionInfo oldPromotion = promotionInfoMap.get(promotion.getPromotionId());
                    oldPromotion.getJoinedItemId().addAll(promotion.getJoinedItemId());
                } else {

                    promotionInfoMap.put(promotion.getPromotionId(), promotion);
                }
            }
            mergedItems.addAll(response.getCartList());
        }
        CartPromotionResponse cartPromotionResponse = new CartPromotionResponse();
        cartPromotionResponse.setCartList(mergedItems);
        cartPromotionResponse.setPromotions(new ArrayList<>(promotionInfoMap.values()));
        return cartPromotionResponse;
    }


    /**
     * 进行外部资源的加载准备
     *
     * @param request         请求对象
     * @param checkoutContext 上下文
     */
    private void prepareExternalData(CheckoutPromotionRequest request, CheckoutContext checkoutContext) {
        List<ExternalDataProvider<?>> providers = externalProviderFactory.getAllExternalProviders(request, checkoutContext.getFromInterface());
        Map<ResourceExtType, ExternalDataProvider<?>> map = new HashMap<>(providers.size());
        for (ExternalDataProvider<?> provider : providers) {
            provider.prepare(request);
            map.put(provider.getResourceExtType(), provider);
        }
        checkoutContext.setExternalDataMap(map);
    }









    /**
     * 转换为价保返回结果
     *
     * @param response        价保返回结果
     * @param checkoutContext 上下文
     */
    private void convertToGetProductCurrentPriceResponse(GetProductCurrentPriceResponse response, CheckoutContext checkoutContext) {
        response.setCartList(checkoutContext.getCarts());
        response.setPromotions(checkoutContext.getPromotion());
        Map<String, String> exInfo = new HashMap<>();
        exInfo.put(PromotionConstant.COUPON_BASE_INFO_INVALID, checkoutContext.getCouponBaseInfoInvalid());
        exInfo.put(PromotionConstant.REDPACKET_BASE_INFO_INVALID, checkoutContext.getRedPacketBaseInfoInvalid());
        response.setExInfo(exInfo);
    }




}
