package com.xiaomi.nr.promotion.enums;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;

@Getter
@AllArgsConstructor
public enum PromotionSsuTypeEnum {

    SINGLE(0, "单品"),
    PACKAGE(1, "套装"),
    GROUP(2, "主附品"),
    ;

    /**
     * 新起枚举前，入参已经定义为Long类型
     */
    private final int code;

    private final String desc;

    private static final Map<Integer, PromotionSsuTypeEnum> ENUM_MAP = Maps.newHashMap();

    static {
        for (PromotionSsuTypeEnum promotionSsuTypeEnum : PromotionSsuTypeEnum.values()) {
            ENUM_MAP.put(promotionSsuTypeEnum.getCode(), promotionSsuTypeEnum);
        }
    }

    public static PromotionSsuTypeEnum valueOf(Integer code) {
        return ENUM_MAP.get(code);
    }
}
