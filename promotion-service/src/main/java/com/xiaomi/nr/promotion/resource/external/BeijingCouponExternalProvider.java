package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.domain.ecard.model.UserEcard;
import com.xiaomi.nr.promotion.domain.ecard.service.common.EcardInfoService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.List;
import java.util.Objects;

/**
 * 外部资源-北京消费券
 *
 * <AUTHOR>
 * @date 2022-06-06
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class BeijingCouponExternalProvider extends ExternalDataProvider<List<UserEcard>> {

    @Autowired
    private EcardInfoService ecardInfoService;

    private ListenableFuture<List<UserEcard>> future;

    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    /**
     * 准备外部资源的具体逻辑
     */
    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        Long userId = request.getUserId();
        if (userId == null || request.getClientId() == null) {
            return;
        }
        // 非结算不取
        List<String> ecardIds = request.getEcardIds();
        if (request.getUseBeijingcoupon() && Objects.equals(SourceApi.SUBMIT, request.getSourceApi()) && CollectionUtils.isNotEmpty(ecardIds)) {
            this.future = ecardInfoService.getEcardsInfoAsync(userId, ecardIds, request.getClientId());
        } else if (Objects.equals(SourceApi.CHECKOUT, request.getSourceApi())){
            this.future = ecardInfoService.getBeijingCouponEcardsInfoAsync(userId);
        }
    }

    @Override
    protected long getTimeoutMills() {
        return 1000L;
    }

    @Override
    protected ListenableFuture<List<UserEcard>> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.ECARD_BEIJING_COUPON;
    }
}
