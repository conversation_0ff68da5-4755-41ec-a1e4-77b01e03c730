package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.entity.redis.GiftBargainGroup;
import com.xiaomi.nr.promotion.entity.redis.Goods;
import com.xiaomi.nr.promotion.entity.redis.SkuGroup;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndexNew;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyGiftPromotionConfig;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 买赠基数检查
 *
 * <AUTHOR>
 * @date 2021/9/27
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class BuyGiftBaseNumCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 赠品信息
     */
    private Goods giftGoods;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        List<CartItem> cartList = request.getCartList();
        List<GoodsIndexNew> indexNewList = CartHelper.getCartsIndex(cartList, SourceEnum.SOURCE_GIFT.getSource(), String.valueOf(promotionId));
        if (CollectionUtils.isEmpty(indexNewList)) {
            return true;
        }
        return indexNewList.stream().allMatch(index -> checkItem(cartList, index));
    }

    private boolean checkItem(List<CartItem> cartList, GoodsIndexNew indexNew) {
        CartItem cartItem = CartHelper.getCartItem(cartList, indexNew);
        if (cartItem == null) {
            return false;
        }
        GiftBargainGroup giftGoods = getGiftGoods(cartItem);
        if (giftGoods == null) {
            return true;
        }
        if ((cartItem.getCount() % giftGoods.getGiftBaseNum()) != 0) {
            log.warn("act base is not fill. cartItem:{} baseNum:{}", GsonUtil.toJson(cartItem), giftGoods.getGiftBaseNum());
            return false;
        }
        return true;
    }

    private GiftBargainGroup getGiftGoods(CartItem cartItem) {
        if (giftGoods == null || CollectionUtils.isEmpty(giftGoods.getSkuGroupList())) {
            return null;
        }
        SkuGroup skuGroup = giftGoods.getSkuGroupList().stream()
                .filter(group -> Objects.equals(group.getGroupId(), cartItem.getGroupId()))
                .findAny().orElse(null);
        if (skuGroup == null || CollectionUtils.isEmpty(skuGroup.getListInfo())) {
            return null;
        }
        return skuGroup.getListInfo().stream()
                .filter(group -> Objects.equals(String.valueOf(group.getSku()), cartItem.getSku()))
                .findAny().orElse(null);
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof BuyGiftPromotionConfig)) {
            log.error("config is not instanceof BuyGiftPromotionConfig. config:{}", config);
            return;
        }
        BuyGiftPromotionConfig promotionConfig = (BuyGiftPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.giftGoods = promotionConfig.getGiftGoods();
    }
}
