package com.xiaomi.nr.promotion.enums;

/**
 * 来源服务
 *
 * <AUTHOR>
 * @date 2021/8/16
 */
public enum TradeFromEnum {
    /**
     * 商城
     */
    SHOP(0),
    /**
     * 门店
     */
    STORE(1),

    /**
     * 车商城，订单侧定义，促销暂时不用
     */
    CAR_SHOP(2),
    ;
    /**
     * 数据值
     */
    private final int value;

    TradeFromEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}
