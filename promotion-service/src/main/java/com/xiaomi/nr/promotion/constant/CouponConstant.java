package com.xiaomi.nr.promotion.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 券常量
 *
 * <AUTHOR>
 * @date 2021/4/22
 */
public class CouponConstant {
    /**
     * 旧版有码优惠券长度
     */
    public final static int OLDCODE_COUPON_LEN = 17;

    /**
     * NEWCODE_COUPON_LEN 新版有码优惠券长度
     */
    public final static int NEWCODE_COUPON_LEN = 16;

    /**
     * DISCOUNT_TOTAL 折扣计算的基数
     */
    public final static int DISCOUNT_TOTAL = 100;

    /**
     * EACH_DIFF 分摊余数时每个个体上加的钱
     */
    public final static int EACH_DIFF = 1;

    /**
     * EACH_REDUCE_NUM 赠品，加价购默认个数
     */
    public final static int EACH_REDUCE_NUM = 1;

    /**
     * 一分钱抵扣
     */
    public final static int TYPE_DEDUCT_PENNY = 1;

    /**
     * 仅线上可用
     */
    public final static int COUPON_TYPE_ONLY_ONLINE = 1;
    /**
     * 仅线下可用
     */
    public final static int COUPON_TYPE_ONLY_OFFLINE = 2;
    /**
     * 线上线下均可用
     */
    public final static int COUPON_TYPE_BOTH_ONLINE_OFFLINE = 3;
    /**
     * 线上线下均不可用
     */
    public final static int COUPON_TYPE_NOT_ONLINE_OFFLINE = 4;
    /**
     * 读取线上券
     */
    public final static int COUPON_ONLINE_READ = 1;
    /**
     * 读取线下券
     */
    public final static int COUPON_OFFLINE_READ = 0;
    /**
     * 写db线上券
     */
    public final static int COUPON_ONLINE_DB = 0;
    /**
     * 写db线下券
     */
    public final static int COUPON_OFFLINE_DB = 1;

    /**
     * 无码券
     */
    public final static int COUPON_CATEGORY_NOCODE = 1;

    /**
     * 旧版有码劵
     */
    public final static int COUPON_CATEGORY_OLDCODE = 2;

    /**
     * 新版有码劵
     */
    public final static int COUPON_CATEGORY_NEWCODE = 3;

    public final static int IMPOSSIBLE_INT = -9999;

    public final static String REASON_UNUSABLE = "reason_unusable";

    /**
     * 券类型对应的分类
     */
    public final static Map<Integer, Map<Integer, String>> COUPON_TYPE_CAT = new HashMap<>();
    static {
        COUPON_TYPE_CAT.put(1, new HashMap<>());
        COUPON_TYPE_CAT.get(1).put(1, "no_cash");
        COUPON_TYPE_CAT.get(1).put(2, "no_discount");
        COUPON_TYPE_CAT.get(1).put(3, "no_deduct");

        COUPON_TYPE_CAT.put(2, new HashMap<>());
        COUPON_TYPE_CAT.get(2).put(1, "old_cash");
        COUPON_TYPE_CAT.get(2).put(2, "old_discount");
        COUPON_TYPE_CAT.get(2).put(3, "old_deduct");

        COUPON_TYPE_CAT.put(3, new HashMap<>());
        COUPON_TYPE_CAT.get(3).put(1, "new_cash");
        COUPON_TYPE_CAT.get(3).put(2, "new_discount");
        COUPON_TYPE_CAT.get(3).put(3, "new_deduct");
    }
}
