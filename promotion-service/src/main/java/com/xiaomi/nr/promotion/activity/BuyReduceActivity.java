package com.xiaomi.nr.promotion.activity;


import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.GetProductActRequest;
import com.xiaomi.nr.promotion.api.dto.model.CheckGoodsItem;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.ReduceGoodsInfo;
import com.xiaomi.nr.promotion.componet.action.BuyReduceAction;
import com.xiaomi.nr.promotion.componet.condition.BuyReduceCondition;
import com.xiaomi.nr.promotion.componet.condition.FrequencyCondition;
import com.xiaomi.nr.promotion.componet.condition.FrequencyForProtectPriceCondition;
import com.xiaomi.nr.promotion.componet.condition.OrgCondition;
import com.xiaomi.nr.promotion.componet.preparation.GlobalExcludePreparation;
import com.xiaomi.nr.promotion.componet.preparation.GoodsHierarchyPreparation;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.ActRespConverter;
import com.xiaomi.nr.promotion.model.common.PromotionExtend;
import com.xiaomi.nr.promotion.model.common.ReduceExtendInfo;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.GoodsReduceInfo;
import com.xiaomi.nr.promotion.tool.PromotionDescRuleTool;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import lombok.extern.slf4j.Slf4j;


/**
 * 下单立减活动
 *
 * <AUTHOR>
 * @date 2021/05/09
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class BuyReduceActivity extends AbstractActivityTool implements ActivityTool {
    /**
     * 下单立减信息
     * key: skuPackage val:GoodsReduceInfo
     */
    private Map<String, GoodsReduceInfo> reduceInfoMap;
    @Autowired
    private PromotionDescRuleTool promotionDescRuleTool;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine.conditionPreparation(GoodsHierarchyPreparation.class)
                .conditionPreparation(GlobalExcludePreparation.class)
                .condition(OrgCondition.class)
                .condition(FrequencyForProtectPriceCondition.class)
                .condition(FrequencyCondition.class)
                .condition(BuyReduceCondition.class)
                .action(BuyReduceAction.class);
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.BUY_REDUCE;
    }

    /**
     * 构建优惠信息
     *
     * @param context 上下文
     * @return 优惠信息
     */
    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) {
        return buildDefaultPromotionInfo(context);
    }

    @Override
    public List<CheckGoodsItem> checkProductsAct(CheckProductsActRequest request, Map<String, GoodsHierarchy> goodsHierarchyMap) throws BizError {
        return null;
    }

    /**
     * 获取产品站信息
     *
     * @param clientId       应用ID
     * @param orgCode        门店Code
     * @param skuPackageList 活动类别
     */
    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) {
        // 不支持透出
        return Collections.emptyMap();
    }

    /**
     * 获取活动详情
     *
     * @return 活动详情
     */
    @Override
    public ActivityDetail getActivityDetail() {
        List<ReduceGoodsInfo> reduceGoods = reduceInfoMap.values().stream()
                .map(ActRespConverter::convertReduceInfo)
                .collect(Collectors.toList());
        ActivityDetail detail = getBasicActivityDetail();
        detail.setReduceGoods(reduceGoods);
        return detail;
    }

    /**
     * 获取产品站活动信息：获取PromotionInfo
     *
     * @param request   产品站信息
     * @param hierarchy 商品层级关系
     * @param isOrgTool 是否来源门店工具
     * @return 促销信息
     */
    @Override
    public PromotionInfo getProductAct(GetProductActRequest request, GoodsHierarchy hierarchy, boolean isOrgTool) throws BizError {
        String skuPackage = StringUtils.isNotEmpty(request.getSku()) ? request.getSku() : request.getCommodityId();
        GoodsReduceInfo reduceInfo = reduceInfoMap.get(skuPackage);
        if (reduceInfo == null) {
            log.warn("buyReduce config is not match item. request:{}", request);
            return null;
        }
        // 条件检查, 主品检查
        if (!checkProductActCondition(request, isOrgTool)) {
            log.warn("buyReduce condition is not match. request:{}", request);
            return null;
        }
        if (!checkActGoodsLimit(skuPackage, reduceInfo.getLimitNum())) {
            log.warn("buyReduce limit is all used. userId:{} skuPackage:{}", request.getUserId(), skuPackage);
            return null;
        }
        // 活动文案，立减特殊处理
        String descRule = promotionDescRuleTool.generateBuyReduceItemRule(reduceInfo.getReduceAmount());
        String descRuleIndex = promotionDescRuleTool.generateBuyReduceItemRuleIndex(reduceInfo.getReduceAmount());

        PromotionExtend extend = new PromotionExtend();
        extend.setReduceExtend(new ReduceExtendInfo());
        PromotionInfo promotionInfo = getDefaultProductAct();
        promotionInfo.setTitle(descRule);
        promotionInfo.setExtend(GsonUtil.toJson(extend));
        promotionInfo.setDescRule(Collections.singletonList(descRule));
        promotionInfo.setDescRuleIndex(descRuleIndex);
        return promotionInfo;
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof BuyReducePromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        BuyReducePromotionConfig promotionConfig = (BuyReducePromotionConfig) config;
        this.reduceInfoMap = promotionConfig.getBuyReduceInfoMap();
        return true;
    }
}
