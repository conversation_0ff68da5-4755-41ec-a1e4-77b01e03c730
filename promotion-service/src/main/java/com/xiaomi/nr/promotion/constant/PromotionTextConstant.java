package com.xiaomi.nr.promotion.constant;

/**
 * 促销文案常量
 *
 * <AUTHOR>
 */
public class PromotionTextConstant {
    // ------------------------ 满减文案 ------------------------
    /**
     * 满减-满1件/每满1件减
     */
    public static final String REDUCE_SINGLE_SHORT_TEXT = "下单减{reduce}";
    /**
     * 满减活动活动规则文案
     */
    public static final String REDUCE_MONEY_SHORT_TEXT = "满{condition}减{reduce}";
    /**
     * 满件减活动活动规则文案
     */
    public static final String REDUCE_COUNT_SHORT_TEXT = "满{condition}件减{reduce}";
    /**
     * 每满减活动规则文案
     */
    public static final String REDUCE_PRE_MONEY_SHORT_TEXT = "每满{condition}减{reduce}";
    /**
     * 每满减活动规则文案
     */
    public static final String REDUCE_PRE_MONEY_SHORT_MAX_TEXT = "每满{condition}减{reduce}，最高减{maxReduce}";
    /**
     * 每满减活动规则文案
     */
    public static final String REDUCE_PRE_NUM_SHORT_TEXT = "每满{condition}件减{reduce}";
    /**
     * 满减-满1件/每满1件减
     */
    public static final String REDUCE_SINGLE_TEXT = "下单减{reduce}元";
    /**
     * 满减活动活动规则文案
     */
    public static final String REDUCE_MONEY_TEXT = "满{condition}元减{reduce}元";

    /**
     * 满件减活动活动规则文案
     */
    public static final String REDUCE_COUNT_TEXT = "满{condition}件减{reduce}元";

    /**
     * 每满减活动规则文案
     */
    public static final String REDUCE_PRE_MONEY_TEXT = "每满{condition}元减{reduce}元";

    /**
     * 满减-最大金额
     */
    public static final String REDUCE_PRE_MONEY_MAX_TEXT = "每满{condition}元减{reduce}元，最高{maxReduce}元";

    /**
     * 每满减活动规则文案
     */
    public static final String REDUCE_PRE_NUM_TEXT = "每满{condition}件减{reduce}元";

    // ------------------------ 满折文案 ------------------------

    /**
     * 满一件折
     */
    public static final String DISCOUNT_ONE_SHORT_TEXT = "下单享{discount}折";
    /**
     * 满元
     */
    public static final String DISCOUNT_MONEY_SHORT_TEXT = "满{dount}享{discount}折";
    /**
     * 满多件折
     */
    public static final String DISCOUNT_COUNT_SHORT_TEXT = "满{dount}件享{discount}折";
    /**
     * 满一件折
     */
    public static final String DISCOUNT_ONE_TEXT = "下单享{discount}折";
    /**
     * 满元
     */
    public static final String DISCOUNT_MONEY_TEXT = "满{dount}元享{discount}折";
    /**
     * 满多件折
     */
    public static final String DISCOUNT_COUNT_TEXT = "满{dount}件享{discount}折";
    /**
     * 每满额折
     */
    public static final String DISCOUNT_PRE_MONEY_TEXT = "满{dount}享{discount}折";
    /**
     * 每满件折
     */
    public static final String DISCOUNT_PRE_NUM_TEXT = "满{dount}件享{discount}折";

    // ------------------------ 包邮文案 ------------------------
    /**
     * 满一件包邮
     */
    public static final String POST_FREE_COUNT_ONE_TEXT = "享包邮";
    /**
     * 满件包邮
     */
    public static final String POST_FREE_COUNT_SHORT_TEXT = "满{count}件包邮";
    /**
     * 满金额包邮
     */
    public static final String POST_FREE_MONEY_SHORT_TEXT = "满{money}元包邮";
    /**
     * 满件包邮
     */
    public static final String POST_FREE_COUNT_TEXT = "满{count}件享包邮";
    /**
     * 满金额包邮
     */
    public static final String POST_FREE_MONEY_TEXT = "满{money}元享包邮";

    // ------------------------ 下单立减文案 -----------------------

    /**
     * 下单立减Item文案规则
     */
    public static final String RULE_TEXT_BUY_REDUCE = "下单立减{money}元";

    /**
     * 下单立减Item文案规则
     */
    public static final String RULE_INDEX_TEXT_BUY_REDUCE = "下单立减{money}";

    // ------------------------ 买赠文案 ------------------------
    /**
     * 买赠短规则文案
     */
    public static final String BUY_GIFT_SHORT_TEXT = "赠品";

    /**
     * 赠品活动规则文案(单赠品)
     */
    public static final String BUY_GIFT_ONE_TEXT = "价值{money}元{name}x{count}，赠完即止";

    /**
     * 赠品动规则文案(多赠品)
     */
    public static final String BUY_GIFT_MORE_TEXT = "可任选以下一种赠品";


    public static final String BUY_GIFT_RULE_TEXT = "每满一件送赠品";

    /**
     * 赠品短规则文案 元
     */
    public static final String GIFT_MONEY_SHORT_TEXT = "满{money}送赠品";

    /**
     * 赠品短规则文案 件
     */
    public static final String GIFT_COUNT_SHORT_TEXT = "满{count}件送赠品";

    /**
     * 组内赠品文案
     */
    public static final String GIFT_TEXT = "价值{money}元{name}x{count}，赠完即止";

    /**
     * 满xx元赠x元XX（商品名称）x4，赠完即止
     */
     public static final String GIFT_MONEY_SINGLE_GROUP_TEXT = "赠{price}元{name}x{count}，赠完即止";

    /**
     * 满xx件赠x元XX（商品名称）x4，赠完即止
     */
     public static final String GIFT_COUNT_SINGLE_GROUP_TEXT = "满{num}件{price}元{name}x{count}，赠完即止";

    /**
     * 满199/299/399元，送对应赠品
     */
    public static final String GIFT_MONEY_LADDER_TEXT = "满{money}元，送对应赠品";
    /**
     * 满xx/xx/xx件，送对应赠品
     */
    public static final String GIFT_COUNT_LADDER_TEXT = "满{count}件，送对应赠品";

    /**
     * 赠品动规则文案(多赠品)
     */
    public static final String GIFT_MORE_ONE_TEXT = "满{money}元，可任选以下一种赠品";

    /**
     * 赠品动规则文案(多赠品)
     */
    public static final String GIFT_MORE_ALL_TEXT = "满{money}元，送全部赠品";

    /**
     * 赠一件
     */
    public static final String GIFT_ONE_TEXT = "可任选以下一种赠品";

    /**
     * 赠全部
     */
    public static final String GIFT_ALL_TEXT = "送以下全部赠品";

    /**
     * 赠品动规则文案(多赠品)
     */
    public static final String GIFT_MORE_COUNT_ONE_TEXT = "满{count}件，可任选以下一种赠品";

    public static final String GIFT_MORE_COUNT_ALL_TEXT = "满{count}件，可任选以下一种赠品";


    /**
     * quota 条件 元
     */
    public static final String QUOTA_MONEY_TEXT = "满{money}元";

    /**
     * quota 条件 件
     */
    public static final String QUOTA_COUNT_TEXT = "满{count}件";

    /**
     * quota 条件 每满元
     */
    public static final String QUOTA_PRE_MONEY_TEXT = "每满{money}元";

    /**
     * quota 条件 每满件
     */
    public static final String QUOTA_PRE_COUNT_TEXT = "每满{count}件";

    /**
     * 限时免费
     */
    public static final String ON_SALE_FREE="限时免费";

    /**
     * 限时直降
     */
    public static final String ON_SALE_REDUCE="限时立减";

    /**
     * 选装基金
     */
    public static final String CAR_RANGE_REDUCE="选装基金";
    
    /**
     * 买赠
     */
    public static final String CAR_SHOP_BUY_GIFT="买赠";

    /**
     * 会员折扣
     */
    public static final String CAR_SHOP_VIP_DISCOUNT="Club会员";
}
