package com.xiaomi.nr.promotion.mq.consumer;

import com.xiaomi.nr.promotion.activity.cache.RefreshableActivityConfigTask;
import com.xiaomi.nr.promotion.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.xiaomi.nr.promotion.activity.cache.RefreshableActivityConfigTask.BEAN_NAME_PREFIX;

@Slf4j
@Component
@RocketMQMessageListener(
        topic = "${rocketmq.topic.promotion.notify.broadcast.3c}",
        messageModel = MessageModel.BROADCASTING,
        nameServer = "${rocketmq.order.name-server}",
        accessKey = "${rocketmq.access-key}",
        secretKey = "${rocketmq.secret-key}",
        consumerGroup = "${rocketmq.consumer.group.promotion.notify.broadcast}")
public class PromotionChangeListener implements RocketMQListener<String> {

    @Autowired
    private Map<String, RefreshableActivityConfigTask> refreshableActivityConfigTaskMap;

    @Value("${biz.area.id.list}")
    private List<String> bizAreaIdList;

    @Override
    public void onMessage(String message) {
        log.info("PromotionChangeListener receive msg:{}", message);
        try {
            if (StringUtils.isEmpty(message)) {
                log.error("PromotionChangeListener error. message is empty");
                return;
            }
            MessageContent messageContent = GsonUtil.fromJson(message, MessageContent.class);
            if (messageContent == null) {
                log.error("PromotionChangeListener error. messageContent is null, message:{}", message);
                return;
            }
            MessageBody body = messageContent.getBody();
            if (body == null) {
                log.error("PromotionChangeListener error. body is null, message:{}", message);
                return;
            }
            Long sequenceId = body.getSequenceId();
            if (sequenceId == null || sequenceId <= 0) {
                log.error("PromotionChangeListener error. sequenceId is error, message:{}", message);
                return;
            }
            refreshableActivityConfigTaskMap.forEach((k ,v) -> {
                if (bizAreaIdList.contains(k.replace(BEAN_NAME_PREFIX, ""))) {
                    v.refreshCacheBySeqId(sequenceId);
                }
            });
        } catch (Exception e) {
            log.error("PromotionChangeListener error. message:{}", message, e);
        }
    }
}
