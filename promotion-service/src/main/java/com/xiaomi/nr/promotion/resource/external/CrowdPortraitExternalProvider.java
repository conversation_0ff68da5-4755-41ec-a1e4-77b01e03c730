package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.rpc.crowdportrait.CrowdPortraitServiceProxy;
import com.xiaomi.nr.promotion.rpc.crowdportrait.model.RuleResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

/**
 * <AUTHOR>
 * @date 2025/5/7 09:35
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class CrowdPortraitExternalProvider extends ExternalDataProvider<RuleResponse> {

    private ListenableFuture<RuleResponse> future;

    @Autowired
    private CrowdPortraitServiceProxy crowdPortraitServiceProxy;

    /**
     * 外部请求开关
     *
     * @return true/false
     */
    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    /**
     * 初始化异步请求
     *
     * @param request 结算请求对象
     */
    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        if (request.getUserId() == null || request.getUserId() <= 0 || CollectionUtils.isEmpty(request.getRuleIds())) {
            return;
        }

        future = crowdPortraitServiceProxy.batchRuleQuery("mid", String.valueOf(request.getUserId()), request.getRuleIds());
    }

    /**
     * 超时时间
     *
     * @return 超时时间
     */
    @Override
    protected long getTimeoutMills() {
        return 50;
    }

    /**
     * 获取数据
     *
     * @return 数据future对象
     */
    @Override
    protected ListenableFuture<RuleResponse> getFuture() {
        return future;
    }

    /**
     * 外部资源类型
     *
     * @return ResourceExtType
     */
    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.CROWD_PORTRAIT;
    }
}
