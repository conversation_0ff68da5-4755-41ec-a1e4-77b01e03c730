package com.xiaomi.nr.promotion.dao.mysql.nractivity;

import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.NrActScopePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 活动范围mapper
 *
 * <AUTHOR>
 * @date 2024/10/9
 */
@Mapper
@Component
public interface NrActScopeMapper {
    
    @Select("<script>" +
            " select id, activity_id, scope_type, relation, scope_value, is_deleted " +
            " from nr_act_scope " +
            " where is_deleted=0 and activity_id in " +
                "<foreach collection='actIds' item='item' separator=',' open='(' close=')'>" +
                    "#{item}" +
                "</foreach>" +
            "</script>")
    List<NrActScopePo> queryListById(@Param("actIds") List<Long> ids);
}
