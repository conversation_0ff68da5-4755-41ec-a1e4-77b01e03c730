package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 线上活动库存
 *
 * <AUTHOR>
 * @date 2021/4/21
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OnlineActLimitProvider implements ResourceProvider<OnlineActLimitProvider.ActOnlineLimit> {

    private ResourceObject<ActOnlineLimit> resourceObject;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Override
    public ResourceObject<ActOnlineLimit> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<ActOnlineLimit> object) {
        this.resourceObject = object;
    }

    @Override
    public void lock() throws BizError {
        log.info("lock act limit resource. {}", resourceObject);
        final long lockedCnt = activityRedisDao.incrActStatusLimitNum(resourceObject.getPromotionId(),
                resourceObject.getOrderId(), resourceObject.getContent().getCount(),
                resourceObject.getContent().getLimitNum());
        if (lockedCnt == 0L) {
            log.warn("lock stock fail.lockedCnt:{}", lockedCnt);
            throw ExceptionHelper.create(ErrCode.CODE_STOCK_LOCK_FAIL, "锁定库存失败，库存不足");
        }
        log.info("lock stock ok.lockedCnt:{}", lockedCnt);
    }

    @Override
    public void consume() {
        log.info("consume act limit resource. {}", resourceObject);
    }

    @Override
    public void rollback() {
        log.info("rollback act limit resource. {}", resourceObject);
        resetStock();
    }

    @Override
    public String conflictText() {
        return "扣减库存失败";
    }

    /**
     * 资源重置
     */
    private void resetStock() {
        activityRedisDao.decrActStatusLimitNum(resourceObject.getPromotionId(),
                resourceObject.getOrderId(), resourceObject.getContent().getCount());
    }

    /**
     * 线上活动参与次数
     */
    @Data
    public static class ActOnlineLimit {
        /**
         * 活动ID
         */
        private long actId;

        /**
         * 活动本次使用次数
         */
        private int count;

        /**
         * 活动总限制次数
         */
        private long limitNum;
    }
}
