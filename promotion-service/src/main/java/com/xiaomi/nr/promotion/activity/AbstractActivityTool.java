package com.xiaomi.nr.promotion.activity;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xiaomi.goods.gis.dto.stock.Region;
import com.xiaomi.nr.promotion.api.dto.GetProductActRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.api.dto.model.Policy;
import com.xiaomi.nr.promotion.api.dto.model.QuotaEle;
import com.xiaomi.nr.promotion.api.dto.model.SkuGroup;
import com.xiaomi.nr.promotion.config.yaml.PromotionTotalZkConfig;
import com.xiaomi.nr.promotion.constant.PromotionTextConstant;
import com.xiaomi.nr.promotion.engine.ProductGoodsActProvider;
import com.xiaomi.nr.promotion.engine.dsl.DSLGeneralPromotion;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.entity.redis.FillGoodsGroup;
import com.xiaomi.nr.promotion.entity.redis.GiftBargainGroup;
import com.xiaomi.nr.promotion.entity.redis.*;
import com.xiaomi.nr.promotion.enums.*;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.*;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MultiPromotionConfig;
import com.xiaomi.nr.promotion.rpc.gis.GoodsStockServiceProxy;
import com.xiaomi.nr.promotion.tool.ConditionCheckTool;
import com.xiaomi.nr.promotion.tool.PromotionDescRuleTool;
import com.xiaomi.nr.promotion.util.CompareHelper;
import com.xiaomi.nr.promotion.util.SsuParamUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.concurrent.ListenableFuture;

import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 活动工具的抽象实现
 * <p>
 *
 * <AUTHOR>
 * @date 2018/9/26
 */
@Slf4j
public abstract class AbstractActivityTool extends DSLGeneralPromotion implements ProductGoodsActProvider {
    /**
     * 可参加活动商品
     */
    protected Set<String> includeSkuPackages;
    // ---------- 范围限制 -------------
    /**
     * 是否线下可用 1仅线上使用，2仅线下使用 3均可使用
     */
    protected OnOffLineEnum offline;

    /**
     * 门店范围
     */
    protected OrgScopeEnum orgScope;

    /**
     * 指定门店或区域ID
     */
    protected List<String> selectOrgCodes;

    /**
     * 能参与的客户端列表
     */
    protected List<String> selectClients;

    /**
     * 能参与人群范围
     */
    protected UserGroupActionEnum groupAction;

    /**
     * 能参与的人群
     */
    protected List<String> selectGroups;

    /**
     * 销售来源
     */
    protected List<String> saleSources;

    /**
     * 活动密码
     */
    protected String accessCode;

    // ---------- 次数限制 -------------
    /**
     * 活动总数
     */
    protected long actLimitNum;

    /**
     * 是否限制商品数量
     */
    protected boolean numLimit;

    /**
     * 活动限制. 数据值：0-不限制， 没有限制就没有对应字段
     */
    protected ActNumLimitRule numLimitRule;

    /**
     * 频次限制 1不限制 2整个活动一次 3每天一次
     */
    protected ActFrequencyEnum frequency;

    // ---------- 活动互斥 -------------

    /**
     * 活动是否互斥 0-否 1-是 #新
     */
    protected boolean actMutexLimit;
    /**
     * 互斥活动优先级
     */
    protected List<String> actMutexes;

    // ---------- 周期 -------------

    /**
     * 是否周期性
     */
    protected boolean daily;

    /**
     * 每周期开始时间
     */
    protected LocalTime dailyStartTime;

    /**
     * 每周期结束时间
     */
    protected LocalTime dailyEndTime;

    // --------- 其他字段 -------------

    /**
     * 是否包邮
     */
    protected Integer postfree;

    /**
     * 是否限制包邮次数：true / false
     */
    protected Boolean postfreeIsnum;
    /**
     * 包邮次数
     */
    protected Integer postfreeNum;

    /**
     * 是否单品页展示
     */
    protected Integer showProductView;

    /**
     * 描述
     */
    protected List<String> descPolicy;
    /**
     * 描述
     */
    protected Boolean descIsShowAddOnItem;

    /**
     * 描述短名称
     */
    protected String descShortName;

    /**
     * 是否强制添加
     */
    protected Integer isRecommendForceAddPrice;

    /**
     * 活动规则文案
     */
    protected List<String> descRule;

    /**
     * 短规则文案
     */
    protected String descRuleIndex;


    @Autowired
    private ConditionCheckTool conditionCheckTool;

    @Autowired
    protected GoodsStockServiceProxy goodsStockServiceProxy;

    @Autowired
    protected PromotionDescRuleTool promotionDescRuleTool;

    @Autowired
    private PromotionTotalZkConfig promotionTotalZkConfig;

    /**
     * 实现默认PromotionInfo 信息
     *
     * @param context 上下文
     * @return OrderJoinActInfo
     */
    public PromotionInfo buildDefaultPromotionInfo(LocalContext context) {
        String title = CollectionUtils.isNotEmpty(descRule)? descRule.get(0) : name;
        PromotionInfo promotionInfo = new PromotionInfo();
        promotionInfo.setPromotionId(String.valueOf(id));
        promotionInfo.setType(String.valueOf(type.getValue()));
        promotionInfo.setTypeInfo(metaInfo.getDesc());
        promotionInfo.setTitle(title);
        promotionInfo.setTypeCode(metaInfo.getTypeCode());
        promotionInfo.setIsOnlyGoods(metaInfo.getIsOnlyGoods());
        return promotionInfo;
    }

    /**
     * 获取默认产品站活动优惠信息
     *
     * @return 优惠数据
     */
    protected PromotionInfo getDefaultProductAct() {
        String title = CollectionUtils.isNotEmpty(descRule)? descRule.get(0) : name;
        PromotionInfo promotionInfo = new PromotionInfo();
        promotionInfo.setPromotionId(String.valueOf(id));
        promotionInfo.setType(String.valueOf(type.getValue()));
        promotionInfo.setTypeInfo(type.getName());
        promotionInfo.setTitle(title);
        promotionInfo.setTypeCode(metaInfo.getTypeCode());
        promotionInfo.setPostfree(postfree);
        promotionInfo.setPostfreeIsnum(postfreeIsnum);
        promotionInfo.setPostfreeNum(postfreeNum);
        promotionInfo.setDescPolicy(descPolicy);
        promotionInfo.setDescShortName(descShortName);
        promotionInfo.setDescIsShowAddOnItem(descIsShowAddOnItem);
        promotionInfo.setIsRecommendForceAddPrice(isRecommendForceAddPrice);
        promotionInfo.setOffline(offline.getValue());
        promotionInfo.setDescRule(descRule);
        promotionInfo.setDescRuleIndex(descRuleIndex);
        promotionInfo.setEndTime(getUnixEndTime());
        return promotionInfo;
    }

    /**
     * 获取获取基本详情
     *
     * @return 活动详情
     */
    public ActivityDetail getBasicActivityDetail() {
        ActivityDetail detail = new ActivityDetail();
        detail.setPromotionId(id);
        detail.setName(name);
        detail.setType(type);
        detail.setUnixStartTime(unixStartTime);
        detail.setUnixEndTime(unixEndTime);
        detail.setIncludeSkuPackages(includeSkuPackages);
        detail.setActLimitNum(actLimitNum);
        detail.setNumLimit(numLimit);
        detail.setNumLimitRule(numLimitRule);
        detail.setOffline(offline);
        detail.setOrgScope(orgScope);
        detail.setSelectClients(selectClients);
        detail.setSelectOrgCodes(selectOrgCodes);
        detail.setDescRule(descRule);
        detail.setDescRuleIndex(descRuleIndex);
        detail.setChannels(channels);
        return detail;
    }

    /**
     * 构建线上产品站 PromotionExtend
     *
     * @return ActPromExtend
     */
    protected ActPromExtend buildPromotionExtend() {
        long endTime = getUnixEndTime();
        ActPromExtend e = new ActPromExtend();
        e.setEndTime(endTime);
        e.setAccessCode(accessCode);
        return e;
    }

    /**
     * 构建线上产品站 PromotionExtend
     *
     * @param skuGroupList       赠品
     * @param includeGoodsGroups 主商品
     * @param ignoreStock        是否忽略库存
     * @return ActPromExtend
     */
    protected ActPromExtend buildNewPromotionExtend(List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> skuGroupList,
                                                    List<FillGoodsGroup> includeGoodsGroups, Integer ignoreStock) {
        if (CollectionUtils.isEmpty(skuGroupList) || CollectionUtils.isEmpty(includeGoodsGroups)) {
            return null;
        }
        if (includeGoodsGroups.get(0).getQuota() == null) {
            return null;
        }
        //线上只能有一个阶梯
        List<GiftBargainGroup> listInfo = skuGroupList.get(0).getListInfo();
        Map<String, String> skuMap = listInfo.stream().map(item -> String.valueOf(item.getSku()))
                .collect(Collectors.toMap(sku -> sku, sku -> sku, (val1, val2) -> val1, LinkedHashMap::new));
        List<String> skuList = listInfo.stream().map(item -> String.valueOf(item.getSku())).collect(Collectors.toList());

        // perNum
        boolean perNum = false;
        if (Objects.equals(PolicyQuotaTypeEnum.POLICY_QUOTA_PER_NUM.getType(), includeGoodsGroups.get(0).getQuota().getType())) {
            perNum = true;
        }

        //线上只有一个组,且取第一个商品的信息（线上加价购商品的价格必须相同）
        ActPromExtend extend = new ActPromExtend();
        extend.setList(skuMap);
        extend.setSkuList(skuList);
        extend.setEndTime(getUnixEndTime());
        extend.setAccessCode(accessCode);
        extend.setCartPrice(listInfo.get(0).getCartPrice());
        extend.setRefundValue(listInfo.get(0).getRefundValue());
        extend.setIgnoreStock(ignoreStock);
        extend.setPerNum(perNum);
        return extend;
    }

    /**
     * 获取赠品GoodsIdSet
     *
     * @return goodsIdSet
     */
    protected Set<Long> getGroupGoodsIdSetFromBean(List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> skuGroupList) {
        if (CollectionUtils.isEmpty(skuGroupList)) {
            return Collections.emptySet();
        }
        Set<Long> goodsIdSet = Sets.newHashSet();
        skuGroupList.forEach(group -> group.getListInfo().forEach(item -> {
            if (item.getGoodsId() == null) {
                return;
            }
            goodsIdSet.add(item.getGoodsId());
        }));
        return goodsIdSet;
    }

    /**
     * 获取赠品GoodsIdSet
     *
     * @return goodsIdSet
     */
    protected Set<Long> getGroupGoodsIdSet(List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> skuGroupList) {
        if (CollectionUtils.isEmpty(skuGroupList)) {
            return Collections.emptySet();
        }
        Set<Long> goodsIdSet = Sets.newHashSet();
        skuGroupList.forEach(group -> group.getListInfo().forEach(item -> {
            if (item.getGoodsId() == null) {
                return;
            }
            goodsIdSet.add(item.getGoodsId());
        }));
        return goodsIdSet;
    }

    /**
     * 获取政策
     * @param includeGoodsGroups 主商品
     * @param stockMap 库存
     * @param checkStock 是否检查库存，true-是，false-否
     *
     * @return 政策
     */
    protected PolicyNew getPolicyNew(List<FillGoodsGroup> includeGoodsGroups, List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> skuGroupList, Map<Long, Long> stockMap, boolean checkStock) {
        if (CollectionUtils.isEmpty(includeGoodsGroups)) {
            return null;
        }
        PolicyNewGoods newGoods = new PolicyNewGoods();
        PolicyNewRule newRule = new PolicyNewRule();
        List<PolicyNewSkuGroup> skuGroupsList = getNewSkuGroupList(stockMap, skuGroupList, checkStock);
        if (CollectionUtils.isEmpty(skuGroupsList)) {
            log.info("sku group list is empty. actId:{}", id);
            return null;
        }
        newGoods.setSkuGroupsList(skuGroupsList);
        if (type == ActivityTypeEnum.BARGAIN) {
            newRule.setBarginGoods(newGoods);
        } else if (type == ActivityTypeEnum.GIFT || type == ActivityTypeEnum.BUY_GIFT){
            newRule.setGiftGoods(newGoods);
        } else {
            log.warn("act type is not support rule.");
        }

        List<PolicyNewFillGoodsGroup> includedGoodsGroup = new ArrayList<>();
        for (FillGoodsGroup fillGoodsGroup : includeGoodsGroups) {
            PolicyNewFillGoodsGroup goodsGroup = new PolicyNewFillGoodsGroup();
            goodsGroup.setQuota(ActRespConverter.convert(fillGoodsGroup.getQuota()));
            includedGoodsGroup.add(goodsGroup);
        }
        PolicyNewLevel policyItem = new PolicyNewLevel();
        policyItem.setIncludedGoodsGroup(includedGoodsGroup);
        policyItem.setRule(newRule);

        PolicyNew policyNew = new PolicyNew();
        policyNew.setPolicy(Lists.newArrayList(policyItem));
        policyNew.setType((long) type.getValue());
        return policyNew;
    }

    /**
     * 获取政策 -- 赠品阶梯专用
     *
     * @param stockMap   库存
     * @param checkStock 是否检查库存，true-是，false-否
     * @return 政策
     */
    protected PolicyNew getPolicyNewV2(List<QuotaLevel> quotaLevels, Map<Long, Long> stockMap, boolean checkStock) {
        if (CollectionUtils.isEmpty(quotaLevels)) {
            return null;
        }
        List<PolicyNewLevel> policyNewLevels = new ArrayList<>();
        for (QuotaLevel quotaLevel : quotaLevels) {
            PolicyNewGoods newGoods = new PolicyNewGoods();
            PolicyNewRule newRule = new PolicyNewRule();
            List<FillGoodsGroup> includeGoodsGroups = quotaLevel.getIncludeGoodsGroups();
            Goods giftGoods = quotaLevel.getGiftGoods();
            if (giftGoods == null) {
                log.info("gift group is null. actId:{}", id);
                return null;
            }
            if (type == ActivityTypeEnum.GIFT) {
                List<PolicyNewSkuGroup> skuGroupsList = getNewSkuGroupListFromBean(stockMap, giftGoods.getSkuGroupList(), checkStock);
                if (CollectionUtils.isEmpty(skuGroupsList)) {
                    log.info("gift sku group list is empty. actId:{}", id);
                    return null;
                }
                newGoods.setSkuGroupsList(skuGroupsList);
                newRule.setGiftGoods(newGoods);
            } else {
                log.warn("act type is not support rule.");
            }

            List<PolicyNewFillGoodsGroup> includedGoodsGroup = new ArrayList<>();
            for (FillGoodsGroup fillGoodsGroup : includeGoodsGroups) {
                PolicyNewFillGoodsGroup goodsGroup = new PolicyNewFillGoodsGroup();
                goodsGroup.setQuota(ActRespConverter.convert(fillGoodsGroup.getQuota()));
                includedGoodsGroup.add(goodsGroup);
            }
            PolicyNewLevel policyItem = new PolicyNewLevel();
            policyItem.setIncludedGoodsGroup(includedGoodsGroup);
            policyItem.setRule(newRule);
            policyNewLevels.add(policyItem);
        }
        PolicyNew policyNew = new PolicyNew();
        policyNew.setPolicy(Lists.newArrayList(policyNewLevels));
        policyNew.setType((long) type.getValue());
        return policyNew;
    }

    /**
     * 获取政策阶梯列表
     *
     * @param levelList 阶梯列表
     * @return 政策
     */
    protected List<Policy> getPolicyList(List<QuotaLevel> levelList) {
        if (CollectionUtils.isEmpty(levelList)) {
            return Collections.emptyList();
        }
        return levelList.stream().map(QuotaLevelConverter::convert)
                .collect(Collectors.toList());
    }

    /**
     * 满足配额列表
     *
     * @param levelList 阶梯列表
     * @return 满足配额列表
     */
    protected List<QuotaEle> getQuotaEleList(List<QuotaLevel> levelList) {
        if (CollectionUtils.isEmpty(levelList)) {
            return Collections.emptyList();
        }
        QuotaLevel level = levelList.get(0);
        if (CollectionUtils.isEmpty(level.getQuotas())) {
            return Collections.emptyList();
        }
        return level.getQuotas().stream()
                .map(ActRespConverter::convert).collect(Collectors.toList());
    }

    /**
     * 满足配额列表
     *
     * @param eleList 阶梯列表, redis结构
     * @return 满足配额列表
     */
    protected List<QuotaEle> getQuotaEleListFromRedis(List<com.xiaomi.nr.promotion.entity.redis.QuotaEle> eleList) {
        if (CollectionUtils.isEmpty(eleList)) {
            return Collections.emptyList();
        }
        return eleList.stream()
                .map(ActRespConverter::convert).collect(Collectors.toList());
    }


    /**
     * 满足配额列表
     *
     * @return 满足配额列表
     */
    protected List<QuotaEle> getNewQuotaEleList(List<FillGoodsGroup> includeGoodsGroups) {
        if (CollectionUtils.isEmpty(includeGoodsGroups)) {
            return Collections.emptyList();
        }
        return includeGoodsGroups.stream().map(FillGoodsGroup::getQuota).map(ActRespConverter::convert)
                .collect(Collectors.toList());
    }

    /**
     * 转化优惠商品信息
     *
     * @param skuGroupList       赠品或者加价购
     * @param fillGoodsGroupList 主商品组
     * @param checkActLimit      是否检查活动库存
     * @return ProductActGoods
     */
    protected ProductActGoods doConvertActGoods(List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> skuGroupList, List<FillGoodsGroup> fillGoodsGroupList, boolean checkActLimit) {
        List<com.xiaomi.nr.promotion.api.dto.model.SkuGroup> goodsSkuGroupList = Optional.ofNullable(skuGroupList).orElse(Collections.emptyList()).stream()
                .map(skuGroup -> this.convertSkuGroup(skuGroup, checkActLimit))
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(goodsSkuGroupList)) {
            return null;
        }
        List<IncludeGoodsGroup> includeGoodsGroupList = Optional.ofNullable(fillGoodsGroupList).orElse(Collections.emptyList()).stream()
                .map(this::convertGoodsGroup).filter(Objects::nonNull)
                .collect(Collectors.toList());

        ProductActGoods actGoods = new ProductActGoods();
        actGoods.setSkuGroupList(goodsSkuGroupList);
        actGoods.setIncludeGoodsGroupList(includeGoodsGroupList);
        return actGoods;
    }

    /**
     * 转化优惠商品信息
     *
     * @param quotaLevel    阶梯
     * @param checkActLimit 是否检查活动库存
     * @return ProductActGoods
     */
    protected ProductActGoods doConvertActGoodsFromLevel(QuotaLevel quotaLevel, boolean checkActLimit) {
        Goods giftGoods = quotaLevel.getGiftGoods();
        List<FillGoodsGroup> includeGoodsGroups = quotaLevel.getIncludeGoodsGroups();
        List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> skuGroupList = giftGoods.getSkuGroupList();
        List<SkuGroup> goodsSkuGroupList = Optional.ofNullable(skuGroupList).orElse(Collections.emptyList()).stream()
                .map(skuGroup -> this.convertSkuGroup(skuGroup, checkActLimit))
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(goodsSkuGroupList)) {
            return null;
        }
        ProductActGoods productActGoodsV2 = new ProductActGoods();
        productActGoodsV2.setSkuGroupList(goodsSkuGroupList);
        List<IncludeGoodsGroup> includeGoodsGroupList = getIncludeGoodsGroupList(includeGoodsGroups);
        productActGoodsV2.setIncludeGoodsGroupList(includeGoodsGroupList);
        return productActGoodsV2;
    }

    private List<IncludeGoodsGroup> getIncludeGoodsGroupList(List<FillGoodsGroup> includeGoodsGroups) {
        if (CollectionUtils.isEmpty(includeGoodsGroups)) {
            return Collections.emptyList();
        }
        return includeGoodsGroups.stream().map(fillGoodsGroupBean -> {
            IncludeGoodsGroup includeGoodsGroup = new IncludeGoodsGroup();
            includeGoodsGroup.setQuota(ActRespConverter.convert(fillGoodsGroupBean.getQuota()));
            return includeGoodsGroup;
        }).collect(Collectors.toList());
    }

    /**
     * 转化优惠品信息
     *
     * @param group 组
     * @return 优惠品信息
     */
    private com.xiaomi.nr.promotion.api.dto.model.SkuGroup convertSkuGroup(com.xiaomi.nr.promotion.entity.redis.SkuGroup group, boolean checkActLimit) {
        List<GiftBargainGroup> listInfo = Optional.ofNullable(group.getListInfo()).orElse(Collections.emptyList());
        List<GoodsInfo> goodsInfoList = listInfo.stream()
                .map(info -> convertGiftBargainGroup(group.getGroupId(), info, checkActLimit)).collect(Collectors.toList());
        boolean noneMatch = goodsInfoList.stream().noneMatch(goodsInfo -> Objects.equals(BooleanV2Enum.YES.getValue(), goodsInfo.getActNumLimit()));
        if (checkActLimit && noneMatch) {
            return null;
        }

        String sendType = StringUtils.isNotBlank(group.getSendType()) ? group.getSendType() : SendTypeEnum.ONE.getCode();
        com.xiaomi.nr.promotion.api.dto.model.SkuGroup skuGroup = new com.xiaomi.nr.promotion.api.dto.model.SkuGroup();
        skuGroup.setGroupId(group.getGroupId());
        skuGroup.setSendType(sendType);
        skuGroup.setGoodsInfoList(goodsInfoList);
        skuGroup.setDescGroupRule(group.getDescGroupRule());
        return skuGroup;
    }

    /**
     * 转化优惠品信息
     *
     * @param group 组
     * @return 优惠品信息
     */
    private com.xiaomi.nr.promotion.api.dto.model.SkuGroup convertSkuGroup(SkuGroupBean group, boolean checkActLimit) {
        List<GiftBargainBean> listInfo = Optional.ofNullable(group.getListInfo()).orElse(Collections.emptyList());

        List<GoodsInfo> goodsInfoList = listInfo.stream()
                .map(info -> convertGiftGroup(group.getGroupId(), info, checkActLimit)).collect(Collectors.toList());
        // TODO HZC
        boolean noneMatch = goodsInfoList.stream().noneMatch(goodsInfo -> Objects.equals(BooleanV2Enum.YES.getValue(), goodsInfo.getActNumLimit()));
        if (checkActLimit && noneMatch) {
            return null;
        }

        com.xiaomi.nr.promotion.api.dto.model.SkuGroup skuGroup = new com.xiaomi.nr.promotion.api.dto.model.SkuGroup();
        skuGroup.setGroupId(group.getGroupId());
        skuGroup.setGoodsInfoList(goodsInfoList);
        skuGroup.setSendType(group.getSendType());
        skuGroup.setDescGroupRule(group.getDescGroupRule());
        return skuGroup;
    }

    /**
     * 转化赠品加价购组
     *
     * @param info 赠品加价购组
     * @return 优惠品赠品加价购组信息
     */
    private GoodsInfo convertGiftGroup(Long groupId, GiftBargainBean info, boolean checkActLimit) {
        GoodsInfo goodsInfo = new GoodsInfo();
        goodsInfo.setSku(String.valueOf(info.getSku()));
        goodsInfo.setCartPrice(info.getCartPrice());
        goodsInfo.setMarketPrice(info.getMarketPrice());
        goodsInfo.setShipmentType(info.getShipmentType());
        goodsInfo.setGoodsId(info.getGoodsId());
        goodsInfo.setBaseNum(info.getBaseNum());
        goodsInfo.setDescRule(info.getDescRule());
        if (checkActLimit) {
            if (info.getLimitNum() == null) {
                goodsInfo.setActNumLimit(BooleanV2Enum.YES.getValue());
                return goodsInfo;
            }
            long remainNum = conditionCheckTool.getActGiftRemainNum(info.getLimitNum(), id, groupId, info.getSku());
            goodsInfo.setActNumLimit(BooleanV2Enum.NO.getValue());
            if (info.getBaseNum() <= remainNum) {
                goodsInfo.setActNumLimit(BooleanV2Enum.YES.getValue());
            }
            goodsInfo.setBaseNum(info.getBaseNum());
            goodsInfo.setRemainActNum(remainNum);
        }
        return goodsInfo;
    }

    /**
     * 转化赠品加价购组
     *
     * @param info 赠品加价购组
     * @return 优惠品赠品加价购组信息
     */
    private GoodsInfo convertGiftBargainGroup(Long groupId, GiftBargainGroup info, boolean checkActLimit) {
        GoodsInfo goodsInfo = new GoodsInfo();
        goodsInfo.setSku(String.valueOf(info.getSku()));
        goodsInfo.setCartPrice(info.getCartPrice());
        goodsInfo.setMarketPrice(info.getMarketPrice());
        goodsInfo.setShipmentType(info.getShipmentType());
        goodsInfo.setDescRule(info.getDescRule());
        if (checkActLimit) {
            if (info.getGiftLimitNum() == null) {
                goodsInfo.setActNumLimit(BooleanV2Enum.YES.getValue());
                goodsInfo.setBaseNum(info.getGiftBaseNum());
                return goodsInfo;
            }
            long remainNum = conditionCheckTool.getActGiftRemainNum(info.getGiftLimitNum(), id, groupId, info.getSku());
            goodsInfo.setActNumLimit(BooleanV2Enum.NO.getValue());
            if (info.getGiftBaseNum() <= remainNum) {
                goodsInfo.setActNumLimit(BooleanV2Enum.YES.getValue());
            }
            goodsInfo.setBaseNum(info.getGiftBaseNum());
            goodsInfo.setRemainActNum(remainNum);
        }
        return goodsInfo;
    }

    /**
     * 转化商品组信息
     *
     * @param fillGoodsGroup 商品组
     * @return 优惠品信息
     */
    private IncludeGoodsGroup convertGoodsGroup(FillGoodsGroup fillGoodsGroup) {
        if (fillGoodsGroup.getQuota() == null) {
            return null;
        }
        QuotaEle quota = ActRespConverter.convert(fillGoodsGroup.getQuota());
        IncludeGoodsGroup goodsGroup = new IncludeGoodsGroup();
        goodsGroup.setQuota(quota);
        return goodsGroup;
    }

    /**
     * 处理SkuGroup列表
     * <p>
     * 过滤有限制但是无次数组
     *
     * @param promotionId  活动ID
     * @param skuGroupList 从品组信息
     * @return SkuGroup列表
     */
    protected List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> handleSkuGroupList(Long promotionId, List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> skuGroupList) {
        if (CollectionUtils.isEmpty(skuGroupList)) {
            return Collections.emptyList();
        }

        // 处理从品组信息
        return skuGroupList.stream().map(skuGroup -> handleSlaveGroup(promotionId, skuGroup)).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 处理从品组
     *
     * @param promotionId 活动ID
     * @param skuGroup    组数据
     * @return 组
     */
    private com.xiaomi.nr.promotion.entity.redis.SkuGroup handleSlaveGroup(Long promotionId, com.xiaomi.nr.promotion.entity.redis.SkuGroup skuGroup) {
        Long groupId = skuGroup.getGroupId();

        // 检查策略
        Predicate<GiftBargainGroup> checkStrategy = info -> Boolean.TRUE;
        if (ActivityTypeEnum.BARGAIN == type) {
            checkStrategy = info -> checkBargainNumLimit(promotionId, groupId, info);
        } else if (ActivityTypeEnum.GIFT == type || ActivityTypeEnum.BUY_GIFT == type) {
            checkStrategy = info -> checkGiftNumLimit(promotionId, groupId, info);
        }

        // 开始检查，如果遇到不满足的直接过滤
        List<GiftBargainGroup> listInfo = Optional.ofNullable(skuGroup.getListInfo()).orElse(Collections.emptyList()).stream()
                .filter(checkStrategy).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(listInfo)) {
            return null;
        }
        com.xiaomi.nr.promotion.entity.redis.SkuGroup skuGroupNew = new com.xiaomi.nr.promotion.entity.redis.SkuGroup();
        skuGroupNew.setGroupId(groupId);
        skuGroupNew.setListInfo(listInfo);
        return skuGroupNew;
    }

    /**
     * 通用产品站请求
     *
     * @param clientId 应用ID
     * @param orgCode  orgCode
     * @return TRUE/FALSE
     * @throws BizError 业务错误
     */
    protected boolean checkProductGoodsActCondition(Long clientId, String orgCode) throws BizError {
        if (StringUtils.isNotEmpty(orgCode)) {
            return conditionCheckTool.checkActOfflineLimitNum(id, orgCode, numLimit, numLimitRule);
        } else if (clientId != null) {
            return conditionCheckTool.checkActOnlineLimitNum(id, actLimitNum);
        }
        return true;
    }

    /**
     * 通用产品站请求
     *
     * @param request   请求对象
     * @param isOrgTool 门店工具
     * @return TRUE/FALSE
     * @throws BizError 业务错误
     */
    protected boolean checkProductActCondition(GetProductActRequest request, boolean isOrgTool) throws BizError {
        // 买赠走特殊检查
        if (type == ActivityTypeEnum.BUY_GIFT) {
            return checkNewProductActCondition(request, isOrgTool);
        }
        // 检查门店
        if (isOrgTool && StringUtils.isNotBlank(request.getOrgCode())) {
            return checkOfflineProductActCondition(request);
        } else if (isOrgTool) {
            return false;
        }
        // 检查Client
        return checkOnlineProductActCondition(request);
    }

    /**
     * 检查活动主品数量
     *
     * @param skuPackage 商品
     * @param limitNum   限制数量
     * @return 是否满足
     */
    protected boolean checkActGoodsLimit(String skuPackage, Integer limitNum) {
        return conditionCheckTool.checkActGoodsLimit(id, skuPackage, limitNum);
    }

    /**
     * 检查新产品站条件
     *
     * @param request   请求对象
     * @param isOrgTool 是否来自门店tool
     * @return 是否符合条件
     * @throws BizError 业务异常
     */
    private boolean checkNewProductActCondition(GetProductActRequest request, boolean isOrgTool) throws BizError {
        List<String> accessCodeList = Collections.emptyList();
        if (StringUtils.isNotEmpty(request.getAccessCode())) {
            accessCodeList = Arrays.asList(StringUtils.split(request.getAccessCode(), ','));
        }
        // 检查活动密码
        if (!conditionCheckTool.checkAccessCode(accessCode, accessCodeList)) {
            return false;
        }

        // 销售来源
        if (!conditionCheckTool.checkSaleSource(saleSources, request.getSaleSource())) {
            return false;
        }
        // 检查门店限制
        String orgCode = request.getOrgCode();
        if (isOrgTool && !conditionCheckTool.checkActOfflineLimitNum(id, orgCode, numLimit, numLimitRule)) {
            return false;
        }
        // 检查人群
        Long userId = request.getUserId();
        if (!isOrgTool && !conditionCheckTool.checkUserGroup(groupAction, selectGroups, userId)) {
            return false;
        }
        return true;
    }

    /**
     * 检查产品站线下活动条件
     *
     * @param request 请求对象
     * @return 是否符合条件
     */
    private boolean checkOfflineProductActCondition(GetProductActRequest request) {
        // 检查每日周期
        if (!conditionCheckTool.checkDailyTime(daily, dailyStartTime, dailyEndTime)) {
            return false;
        }
        // 检查门店限制
        if (!conditionCheckTool.checkActOfflineLimitNum(id, request.getOrgCode(), numLimit, numLimitRule)) {
            return false;
        }
        return true;
    }

    /**
     * 检查产品站线上活动条件
     *
     * @param request 请求对象
     * @return 是否符合条件
     */
    private boolean checkOnlineProductActCondition(GetProductActRequest request) throws BizError {
        List<String> accessCodeList = Collections.emptyList();
        if (StringUtils.isNotEmpty(request.getAccessCode())) {
            accessCodeList = Arrays.asList(StringUtils.split(request.getAccessCode(), ','));
        }
        // 检查活动密码
        if (!conditionCheckTool.checkAccessCode(accessCode, accessCodeList)) {
            return false;
        }
        // 1 : 隐藏 2： 展示
        if (showProductView == BooleanV2Enum.YES.getValue()) {
            return false;
        }
        // 检查销售来源
        if (!conditionCheckTool.checkSaleSource(saleSources, request.getSaleSource())) {
            return false;
        }
        // 检查每日周期
        if (!conditionCheckTool.checkDailyTime(daily, dailyStartTime, dailyEndTime)) {
            return false;
        }
        // 检查线上次数
        if (!conditionCheckTool.checkActOnlineLimitNum(id, actLimitNum)) {
            return false;
        }
        // 检查频次
        Long userId = request.getUserId();
        if (!conditionCheckTool.checkActFrequency(frequency, userId, id)) {
            return false;
        }
        // 检查人群
        if (!conditionCheckTool.checkUserGroup(groupAction, selectGroups, userId)) {
            return false;
        }
        return true;
    }

    /**
     * 获取商品赠品库存库存
     *
     * @param masterId  主商品ID
     * @param request   请求对象
     * @param isOrgTool 是否来自活动工具
     * @return key sku：val:库存
     * @throws BizError 业务异常
     */
    protected Map<Long, Long> getGoodsStock(Long masterId, Set<Long> idList, GetProductActRequest request, boolean isOrgTool) throws BizError {
        if (masterId == null || CollectionUtils.isEmpty(idList)) {
            log.warn("masterId is null. or idList is empty. actId:{} masterId:{}, idList:{}", id, masterId, idList);
            return Collections.emptyMap();
        }

        // 区域信息
        Region region = new Region();
        region.setArea(request.getAreaId());
        region.setCity(request.getCityId());
        region.setDistrict(request.getDistrictId());
        region.setProvince(request.getProvinceId());

        // 请求数据
        String masterType;
        if (StringUtils.isNotBlank(request.getSku())) {
            masterType = "goods";
        } else if (SsuParamUtil.isNewPackage(request.getSsuType())) {
            masterType = "group";
        } else {
            masterType = "commodity";
        }
        String orgCode = request.getOrgCode();
        try {
            ListenableFuture<Map<Long, Long>> stockMapFuture = goodsStockServiceProxy.getGiftStockAsync(masterId, masterType,
                    idList, orgCode, region, isOrgTool, request.getShipmentType());
            return stockMapFuture.get(promotionTotalZkConfig.getGiftStockTimeout(), TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("request gis stock error or timeout", e);
            return Collections.emptyMap();
        }
    }

     /**
     * 构造赠品活动（赠品和买赠）的PolicyNew
     * @param request 产品站请求参数
     * @param goods 从品信息
     * @param isOrgTool 是否是门店工具，true-是，false-否
     * @param includeGoodsGroups 包含的主商品
     * @param hierarchy 商品层级关系
     * @return 政策信息
     * @throws BizError
     */
    protected PolicyNew buildPolicyNewForGiftAct(GetProductActRequest request, Goods goods, boolean isOrgTool, List<FillGoodsGroup> includeGoodsGroups, GoodsHierarchy hierarchy) throws BizError {
        Map<Long, Long> stockMap = Collections.emptyMap();
        // 门店或者线上非忽略库存， 请求库存
        boolean checkStock = isOrgTool || (!isOrgTool && BooleanV2Enum.isNo(goods.getIgnoreStock()));
        if (checkStock) {
            Set<Long> idList = getGroupGoodsIdSet(goods.getSkuGroupList());
            Long masterId = Long.valueOf(StringUtils.isNotBlank(request.getSku()) ? hierarchy.getGoodsId() : request.getCommodityId());
            stockMap = getGoodsStock(masterId, idList, request, isOrgTool);
        }
        // 构建PolicyNew
        return getPolicyNew(includeGoodsGroups, goods.getSkuGroupList(), stockMap, checkStock);
    }

    /**
     * 构造赠品活动的PolicyNew V2
     *
     * @param request   产品站请求参数
     * @param isOrgTool 是否是门店工具，true-是，false-否
     * @param hierarchy 商品层级关系
     * @return 政策信息
     * @throws BizError
     */
    protected PolicyNew buildPolicyNewForGiftActV2(GetProductActRequest request, boolean isOrgTool, List<QuotaLevel> quotaLevels, GoodsHierarchy hierarchy) throws BizError {
        Map<Long, Long> stockMap = Collections.emptyMap();
        // 门店或者线上非忽略库存， 请求库存
        boolean checkStock = isOrgTool;
        for (QuotaLevel quotaLevel : quotaLevels) {
            Goods giftGoods = quotaLevel.getGiftGoods();
            checkStock = isOrgTool || BooleanV2Enum.isNo(giftGoods.getIgnoreStock());
            if (checkStock) {
                Set<Long> idList = getGroupGoodsIdSetFromBean(giftGoods.getSkuGroupList());
                Long masterId = Long.valueOf(StringUtils.isNotBlank(request.getSku()) ? hierarchy.getGoodsId() : request.getCommodityId());
                stockMap = getGoodsStock(masterId, idList, request, isOrgTool);
            }
        }
        // 构建PolicyNew
        return getPolicyNewV2(quotaLevels, stockMap, checkStock);
    }

    /**
     * 获取赠品信息
     *
     * @param stockMap 库存Map
     * @param checkStock 是否检查库存，true-是，false-否
     * @return SKU信息
     */
    protected List<PolicyNewSkuGroup> getNewSkuGroupList(Map<Long, Long> stockMap, List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> skuGroupsList, boolean checkStock) {
        if (CollectionUtils.isEmpty(skuGroupsList)) {
            return Collections.emptyList();
        }
        return skuGroupsList.stream().map(item -> convertGoodsItem(item, stockMap, checkStock))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private PolicyNewSkuGroup convertGoodsItem(com.xiaomi.nr.promotion.entity.redis.SkuGroup skuGroups, Map<Long, Long> stockMap, boolean checkStock) {
        List<PolicyNewGroup> groupList = skuGroups.getListInfo().stream()
                .map(skuGroup -> convert(skuGroups.getGroupId(), skuGroup, stockMap, checkStock)).collect(Collectors.toList());
        boolean noneInStock = groupList.stream().noneMatch(item -> Objects.equals(BooleanV2Enum.YES.getValue(), item.getIsInStock()));
        if (noneInStock) {
            return null;
        }
        String sendType = StringUtils.isNotEmpty(skuGroups.getSendType())? skuGroups.getSendType() : SendTypeEnum.ONE.getCode();
        String descGroup = StringUtils.isNotEmpty(skuGroups.getDescGroupRule())? skuGroups.getDescGroupRule() : PromotionTextConstant.BUY_GIFT_MORE_TEXT;
        if (type == ActivityTypeEnum.BARGAIN) {
            sendType = skuGroups.getSendType();
            descGroup = skuGroups.getDescGroupRule();
        }

        PolicyNewSkuGroup group = new PolicyNewSkuGroup();
        group.setGroupId(skuGroups.getGroupId());
        group.setListInfo(groupList);
        group.setSendType(sendType);
        group.setDescGroupRule(descGroup);
        return group;
    }

    private PolicyNewGroup convert(Long groupId, GiftBargainGroup giftGroup, Map<Long, Long> stockMap, boolean checkStock) {
        // 标识是否有货
        int isInStock = BooleanV2Enum.NO.getValue();
        // 是否检查库存
        if (!checkStock) {
            isInStock = BooleanV2Enum.YES.getValue();
        } else {
            Long stockNum = stockMap.get(giftGroup.getGoodsId());
            if (type == ActivityTypeEnum.BUY_GIFT) {
                isInStock = stockNum != null && (stockNum / giftGroup.getGiftBaseNum()) > 0 ?
                        BooleanV2Enum.YES.getValue() : BooleanV2Enum.NO.getValue();
            } else if (type == ActivityTypeEnum.GIFT) {
                isInStock = stockNum != null && stockNum  > 0 ?
                        BooleanV2Enum.YES.getValue() : BooleanV2Enum.NO.getValue();
            } else if (type == ActivityTypeEnum.BARGAIN) {
                isInStock = stockNum != null && stockNum  > 0 ?
                        BooleanV2Enum.YES.getValue() : BooleanV2Enum.NO.getValue();
            }
        }
        // 买赠检查活动数量
        if (type == ActivityTypeEnum.BUY_GIFT || (type == ActivityTypeEnum.GIFT && giftGroup.getGiftLimitNum() != null && giftGroup.getGiftLimitNum() != 0) ) {
            boolean check = conditionCheckTool.checkActGiftLimit(giftGroup.getGiftLimitNum(), giftGroup.getGiftBaseNum(),
                    id, groupId, giftGroup.getSku());
            if (!check) {
                isInStock = BooleanV2Enum.NO.getValue();
            }
        }

        PolicyNewGroup newGroup = new PolicyNewGroup();
        newGroup.setSku(giftGroup.getSku());
        newGroup.setCartPrice(giftGroup.getCartPrice());
        newGroup.setMarketPrice(giftGroup.getMarketPrice());
        newGroup.setActNumLimit(isInStock);
        newGroup.setIsInStock(isInStock);
        newGroup.setBaseNum(giftGroup.getGiftBaseNum());
        newGroup.setDescRule(giftGroup.getDescRule());
        return newGroup;
    }

    /**
     * 获取赠品信息 -- 赠品活动V2
     *
     * @param stockMap   库存Map
     * @param checkStock 是否检查库存，true-是，false-否
     * @return SKU信息
     */
    protected List<PolicyNewSkuGroup> getNewSkuGroupListFromBean(Map<Long, Long> stockMap, List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> skuGroupsList, boolean checkStock) {
        if (CollectionUtils.isEmpty(skuGroupsList)) {
            return Collections.emptyList();
        }
        return skuGroupsList.stream().map(item -> convertGoodsItem(item, stockMap, checkStock))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 检查加价购从品数量
     *
     * @param promotionId 活动ID
     * @param groupId     组ID
     * @param info        从品信息
     * @return 是否符合
     */
    private boolean checkBargainNumLimit(Long promotionId, Long groupId, GiftBargainGroup info) {
        Long limitNum = info.getBargainLimitNum();
        // 表示没有限制
        if (limitNum == null || limitNum == 0L) {
            return true;
        }
        return conditionCheckTool.checkActBargainLimit(limitNum, promotionId, groupId, info.getSku());
    }

    /**
     * 检查赠品从品数量
     *
     * @param promotionId 活动ID
     * @param groupId     组ID
     * @param info        从品信息
     * @return 是否符合
     */
    private boolean checkGiftNumLimit(Long promotionId, Long groupId, GiftBargainGroup info) {
        Long limitNum = info.getGiftLimitNum();
        // 表示没有限制
        if (limitNum == null || limitNum == 0L) {
            return true;
        }
        Long baseNum = info.getGiftBaseNum();
        return conditionCheckTool.checkActGiftLimit(limitNum, baseNum, promotionId, groupId, info.getSku());
    }

    /**
     * 校验商品
     *
     * @param mainGoodsList 主品列表
     * @param includeGoodsGroups 活动配置主品列表
     * @param goodsHierarchyMap 商品层级关系map
     * @param attachedGoodsList 从品列表
     * @param skuGroups 活动配置从品列表
     * @param invalidGoodsList 不合法的商品列表
     */
    protected void checkIncludeGoods(List<CheckGoodsItem> mainGoodsList, List<FillGoodsGroup> includeGoodsGroups, Map<String, GoodsHierarchy> goodsHierarchyMap,
                                     List<CheckGoodsItem> attachedGoodsList,
                                     List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> skuGroups, List<CheckGoodsItem> invalidGoodsList) {
        // 检查主品是否包含在活动配置中
        checkIncludeMainGoods(mainGoodsList, includeGoodsGroups, goodsHierarchyMap, invalidGoodsList);
        // 有不合法的主品直接返回
        if (CollectionUtils.isNotEmpty(invalidGoodsList)) {
            return;
        }
        // 检查从品是否包含在活动配置中
        checkIncludeAttachedGoods(attachedGoodsList, skuGroups, invalidGoodsList);
    }

    /**
     * 校验主品是否包含在活动配置中
     *
     * @param mainGoodsList 主品列表
     * @param includeGoodsGroups 活动配置主品列表
     * @param goodsHierarchyMap 商品层级关系
     * @param invalidGoodsList 不合法的商品列表
     */
    private void checkIncludeMainGoods(List<CheckGoodsItem> mainGoodsList, List<FillGoodsGroup> includeGoodsGroups, Map<String, GoodsHierarchy> goodsHierarchyMap, List<CheckGoodsItem> invalidGoodsList) {
        // 检查主品
        for (CheckGoodsItem goodsItem : mainGoodsList) {
            Long skuPackageKey;
            if (goodsItem.getSku() == null || goodsItem.getSku() == 0L) {
                skuPackageKey = goodsItem.getPackageId();
            } else {
                skuPackageKey = goodsItem.getSku();
            }
            boolean isInclude = false;
            for (FillGoodsGroup fillGoodsGroup : includeGoodsGroups) {
                isInclude = CompareHelper.isFillInclude(goodsHierarchyMap.get(String.valueOf(skuPackageKey)), fillGoodsGroup.getJoinGoods(), true);
                if (isInclude) {
                    break;
                }
            }
            if (!isInclude) {
                goodsItem.setExtend("主品不在活动配置中");
                invalidGoodsList.add(goodsItem);
            }
        }
    }

    /**
     * 校验从品是否包含在活动配置中
     *
     * @param attachedGoodsList 从品列表
     * @param skuGroups 活动配置从品列表
     * @param invalidGoodsList 不合法的商品列表
     */
    private void checkIncludeAttachedGoods(List<CheckGoodsItem> attachedGoodsList, List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> skuGroups, List<CheckGoodsItem> invalidGoodsList) {
        if (CollectionUtils.isEmpty(attachedGoodsList)) {
            return;
        }
        // 检查从品
        for (CheckGoodsItem attachedGoods : attachedGoodsList) {
            boolean isInclude = skuGroups.stream().anyMatch(skuGroup -> {
                if (attachedGoods.getGroupId() == skuGroup.getGroupId().longValue()) {
                    return skuGroup.getListInfo().stream().anyMatch(giftBargain -> giftBargain.getSku() == attachedGoods.getSku().longValue());
                }
                return false;
            });
            if (!isInclude) {
                attachedGoods.setSource("attached");
                attachedGoods.setExtend("从品不在活动配置中");
                invalidGoodsList.add(attachedGoods);
            }
        }
    }

    protected PromotionInfoDTO initPromotionInfo() {
        PromotionInfoDTO infoDTO = new PromotionInfoDTO();
        infoDTO.setId(getId());
        infoDTO.setPromotionType(getPromotionType().getTypeId());
        infoDTO.setStartTime(getUnixStartTime());
        infoDTO.setEndTime(getUnixEndTime());
        return infoDTO;
    }

    protected PromotionPriceDTO buildPromotionPrice(String rule, Long price) {
        PromotionInfoDTO infoDTO = new PromotionInfoDTO();
        infoDTO.setId(getId());
        infoDTO.setPromotionType(getPromotionType().getTypeId());
        infoDTO.setStartTime(getUnixStartTime());
        infoDTO.setEndTime(getUnixEndTime());
        infoDTO.setRule(rule);

        PromotionPriceDTO priceDTO = new PromotionPriceDTO();
        priceDTO.setPrice(price);
        priceDTO.setPromotionInfos(Collections.singletonList(infoDTO));
        return priceDTO;
    }

    @Override
    protected void loadPromotionConfig(AbstractPromotionConfig config) {
        super.loadPromotionConfig(config);
        if (!(config instanceof MultiPromotionConfig)) {
            return;
        }
        MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
        this.includeSkuPackages = config.getIncludeSkuPackages();
        this.offline = config.getOffline();
        this.orgScope = config.getOrgScope();
        this.selectClients = config.getSelectClients();
        this.selectOrgCodes = config.getSelectOrgCodes();
        this.groupAction = config.getGroupAction();
        this.selectGroups = config.getSelectGroups();
        this.accessCode = promotionConfig.getAccessCode();
        this.saleSources = promotionConfig.getSaleSources();
        this.daily = config.isDaily();
        this.dailyEndTime = config.getDailyEndTime();
        this.dailyStartTime = config.getDailyStartTime();
        this.numLimit = promotionConfig.isNumLimit();
        this.numLimitRule = promotionConfig.getNumLimitRule();
        this.actLimitNum = promotionConfig.getActLimitNum();
        this.frequency = promotionConfig.getFrequency();
        this.actMutexLimit = promotionConfig.isActMutexLimit();
        this.actMutexes = promotionConfig.getActMutexes();
        this.postfree = promotionConfig.getPostfree();
        this.postfreeIsnum = promotionConfig.getPostfreeIsnum();
        this.postfreeNum = promotionConfig.getPostfreeNum();
        this.descPolicy = promotionConfig.getDescPolicy();
        this.descShortName = promotionConfig.getDescShortName();
        this.descRule = promotionConfig.getDescRule();
        this.descRuleIndex = promotionConfig.getDescRuleIndex();
        this.descIsShowAddOnItem = promotionConfig.getDescIsShowAddOnItem();
        this.isRecommendForceAddPrice = promotionConfig.getIsRecommendForceAddPrice();
        this.showProductView = promotionConfig.getShowProductView();
    }


}
