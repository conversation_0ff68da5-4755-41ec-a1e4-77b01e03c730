package com.xiaomi.nr.promotion.domain.coupon.service.common.impl;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.dao.mysql.fcode.XmQueueVcodeMapper;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.TbCodecouponLogMapper;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.TbCodecouponMapper;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.TbCouponLogMapper;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.TbCouponMapper;
import com.xiaomi.nr.promotion.dao.redis.CouponInfoRedisDao;
import com.xiaomi.nr.promotion.domain.coupon.model.RemoteCouponResponse;
import com.xiaomi.nr.promotion.entity.mysql.fcode.XmQueueVcode;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.TbCodecoupon;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.TbCodecouponLog;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.TbCoupon;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.TbCouponLog;
import com.xiaomi.nr.promotion.entity.redis.CouponInfo;
import com.xiaomi.nr.promotion.enums.CouponCategoryEnum;
import com.xiaomi.nr.promotion.enums.CouponGeneralType;
import com.xiaomi.nr.promotion.enums.CouponLogTypeEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponConverter;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponOwnedInfo;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.provider.CouponProvider;
import com.xiaomi.nr.promotion.rpc.coupon.CouponServiceProxy;
import com.xiaomi.nr.promotion.domain.coupon.service.common.CouponInfoService;
import com.xiaomi.nr.promotion.util.TestHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.util.concurrent.ListenableFuture;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.xiaomi.nr.promotion.util.CouponHelper.*;

/**
 * 券信息service
 *
 * <AUTHOR>
 * @date 2021/4/26
 */
@Slf4j
@Service
public class CouponInfoServiceImpl implements CouponInfoService {
    @Autowired
    private XmQueueVcodeMapper xmQueueVcodeMapper;
    @Autowired
    private TbCodecouponMapper tbCodecouponMapper;
    @Autowired
    private TbCouponMapper tbCouponMapper;
    @Autowired
    private CouponInfoRedisDao couponInfoRedisDao;
    @Autowired
    private TbCodecouponLogMapper tbCodecouponLogMapper;
    @Autowired
    private TbCouponLogMapper tbCouponLogMapper;
    @Autowired
    private CouponServiceProxy couponServiceProxy;

    /**
     * 根据券Code 获取券信息
     *
     * @param code 券码
     * @param uid  用户ID
     * @return 券信息
     */
    @Async("couponAsyncTaskExecutor")
    @Override
    public ListenableFuture<CouponOwnedInfo> getByCodeAsync(String code, Long uid) {
        try {
            CouponOwnedInfo cc = getByCode(code, uid);
            return AsyncResult.forValue(cc);
        } catch (Exception e) {
            log.error("get coupon info by code error. code:{} uid:{}", code, uid, e);
            return AsyncResult.forExecutionException(e);
        }
    }

    /**
     * 根据券Code 获取券信息
     *
     * @param code 券码
     * @param uid  用户ID
     * @return 券信息
     */
    @Async("couponAsyncTaskExecutor")
    @Override
    public ListenableFuture<List<CouponOwnedInfo>> getByCodeAsyncNew(String code, Long uid) {
        try {
            CouponOwnedInfo cc = getByCode(code, uid);
            return AsyncResult.forValue(Lists.newArrayList(cc));
        } catch (Exception e) {
            log.error("get coupon info by code error. code:{} uid:{}", code, uid, e);
            return AsyncResult.forExecutionException(e);
        }
    }

    /**
     * 根据券Code 获取券信息
     *
     * @param request 券码
     * @return 券信息
     */
    @Async("couponAsyncTaskExecutor")
    @Override
    public ListenableFuture<List<CouponOwnedInfo>> getByCodeAsyncNew(CheckoutPromotionRequest request) {
        try {
            Map<String, CouponOwnedInfo> couponInfoMap = couponServiceProxy.checkoutChecker(request);
            String couponCode = request.getCouponCodes().get(0);
            if (couponInfoMap.get(couponCode) == null) {
                log.warn("invalid code coupon {} for uid {}", request.getCouponCodes(), request.getUserId());
                throw ExceptionHelper.create(ErrCode.ERR_COUPON_INVALID, "无效的优惠券");
            }
            CouponOwnedInfo info = couponInfoMap.get(couponCode);
            info.setCode(couponCode);
            return AsyncResult.forValue(Lists.newArrayList(info));
        } catch (Exception e) {
            log.error("get coupon info by code error. code:{} uid:{}", request.getCouponCodes(), request.getUserId(), e);
            return AsyncResult.forExecutionException(e);
        }
    }


    /**
     * 根据券ID 获取券信息
     *
     * @param cid 券ID
     * @param uid 用户ID
     * @return 券信息
     */
    @Async("couponAsyncTaskExecutor")
    @Override
    public ListenableFuture<CouponOwnedInfo> getByIdAsync(Long cid, Long uid) {
        try {
            // 获取用户资源-无码券
            CouponOwnedInfo ncc = getNoCodeCoupon(cid, uid);
            if (ncc == null) {
                log.warn("invalid nocode coupon id {} for uid {}", cid, uid);
                throw ExceptionHelper.create(ErrCode.ERR_COUPON_INVALID, "DB里不存在券ID: " + cid);
            }
            // 获取券类型信息条件
            CouponInfo couponInfo = getCouponAllInfo(ncc.getTypeId(), CouponCategoryEnum.NOCODE.getType());
            if (couponInfo == null) {
                log.warn("check: get coupon info typeId:{}, uid:{}", ncc.getTypeId(), uid);
                throw ExceptionHelper.create(ErrCode.ERR_COUPON_TYPE_INVALID, "获取优惠券类型信息失败");
            }
            ncc.setCouponType(CouponCategoryEnum.NOCODE.getType());
            ncc.setCouponInfo(couponInfo);
            return AsyncResult.forValue(ncc);
        } catch (Exception e) {
            log.error("get coupon info by id error. cid:{} uid:{} ", cid, uid, e);
            return AsyncResult.forExecutionException(e);
        }
    }

    @Async("couponAsyncTaskExecutor")
    @Override
    public ListenableFuture<List<CouponOwnedInfo>> batchGetByIdAsync(List<Long> cidList, Long uid) {
        try {
            // 获取用户资源-无码券
            List<CouponOwnedInfo> couponList = batchGetNoCodeCoupon(cidList, uid);
            if (couponList.size() != cidList.size()) {
                List<Long> resultIdList = couponList.stream().map(coupon -> coupon.getId()).collect(Collectors.toList());
                log.warn("invalid nocode coupon id, request id:{}, resultIdList:{}, for uid {}", cidList, resultIdList, uid);
                throw ExceptionHelper.create(ErrCode.ERR_COUPON_INVALID, "DB里不存在券ID");
            }
            for (CouponOwnedInfo ncc : couponList) {

                // 获取券类型信息条件
                CouponInfo couponInfo = getCouponAllInfo(ncc.getTypeId(), CouponCategoryEnum.NOCODE.getType());
                if (couponInfo == null) {
                    log.warn("check: get coupon info typeId:{}, uid:{}", ncc.getTypeId(), uid);
                    throw ExceptionHelper.create(ErrCode.ERR_COUPON_TYPE_INVALID, "获取优惠券类型信息失败");
                }
                ncc.setCouponType(CouponCategoryEnum.NOCODE.getType());
                ncc.setCouponInfo(couponInfo);
                if (couponInfo.getBasetype().getCouponType() == null) {
                    ncc.setCouponTypeNew(CouponGeneralType.GOODS.getCode());
                } else {
                    ncc.setCouponTypeNew(couponInfo.getBasetype().getCouponType());
                }
                ncc.setAllow(BooleanUtils.toInteger(true));

            }
            return AsyncResult.forValue(couponList);
        } catch (Exception e) {
            log.error("get coupon info by id error. cid:{} uid:{} ", cidList, uid, e);
            return AsyncResult.forExecutionException(e);
        }
    }

    @Override
    public ListenableFuture<List<CheckoutCoupon>> getCouponForCheckout(CheckoutPromotionRequest request, Integer orgType, boolean getCouponList) {
        return null;
    }
    
    @Override
    public ListenableFuture<RemoteCouponResponse> getCouponAndGroupForCheckout(CheckoutPromotionRequest request,
            Integer orgType, boolean getCouponList) {
        return null;
    }
    
    @Override
    public ListenableFuture<List<CheckoutCoupon>> getCouponForProtectPrice(CheckoutPromotionRequest request, Integer orgType, boolean getCouponList) {
        return null;
    }

    /**
     * 锁定券资源信息
     *
     * @param resourceObject 资源对象
     * @throws BizError 业务异常
     */
    @Transactional(transactionManager = "promotionUserConfigTransactionManager", rollbackFor = {Exception.class, BizError.class})
    @Override
    public boolean lockCoupon(ResourceObject<CouponProvider.ResContent> resourceObject) throws BizError {
        CouponProvider.ResContent resContent = resourceObject.getContent();
        if (CouponCategoryEnum.isNewCodeCoupon(resContent.getCategoryType())) {
            // 券资源
            String md5Code = DigestUtils.md5DigestAsHex(resContent.getCode().getBytes());
            int lockedCnt = tbCodecouponMapper.updateStat(md5Code, resContent.getUserId(), resContent.getStatOld(), resContent.getStatNew(),
                    resourceObject.getOrderId(), resContent.getReplaceMoney(), resContent.getReduceExpress(), resContent.getUseTime(),
                    resContent.getOffline(), resContent.getOrgCode());
            if (lockedCnt <= 0) {
                log.warn("lock newCode fail. md5Code:{}", md5Code);
                throw ExceptionHelper.create(ErrCode.COUPON_LOCK_FAIL, "锁定券资源失败");
            }

            // 日志
            TbCodecouponLog codecouponLog = initCodeCouponLog(resContent, resourceObject.getOrderId());
            tbCodecouponLogMapper.insert(codecouponLog);
        } else if (CouponCategoryEnum.isOldCodeCoupon(resContent.getCategoryType())) {
            int lockedCnt = xmQueueVcodeMapper.updateStat(resContent.getCode(), resContent.getStatOld(), resContent.getUserId(), resourceObject.getOrderId(),
                    resContent.getStatNew(), Instant.now().getEpochSecond());
            if (lockedCnt <= 0) {
                log.warn("lock oldCode fail. code:{}", resContent.getCode());
                throw ExceptionHelper.create(ErrCode.COUPON_LOCK_FAIL, "锁定券资源失败");
            }
        } else {
            // 券资源
            int lockedCnt = tbCouponMapper.updateStat(resContent.getId(), resContent.getUserId(), resContent.getStatOld(), resContent.getStatNew(),
                    resourceObject.getOrderId(), resContent.getReplaceMoney(), resContent.getReduceExpress(), resContent.getUseTime(), resContent.getOffline());
            if (lockedCnt <= 0) {
                log.warn("lock noCode fail. resId:{}", resContent.getId());
                throw ExceptionHelper.create(ErrCode.COUPON_LOCK_FAIL, "锁定券资源失败");
            }

            // 日志
            TbCouponLog tbCouponLog = initCouponLog(resContent);
            tbCouponLogMapper.insert(tbCouponLog);
        }
        return true;
    }

    /**
     * 提交券资源信息
     *
     * @param resourceObject 资源对象
     */
    @Transactional(transactionManager = "promotionUserConfigTransactionManager", rollbackFor = {Exception.class, BizError.class})
    @Override
    public boolean consumeCoupon(ResourceObject<CouponProvider.ResContent> resourceObject) {
        return true;
    }

    /**
     * 回滚券资源信息
     *
     * @param resourceObject 资源对象
     * @throws BizError 业务异常
     */
    @Transactional(transactionManager = "promotionUserConfigTransactionManager", rollbackFor = {Exception.class, BizError.class})
    @Override
    public boolean rollbackCoupon(ResourceObject<CouponProvider.ResContent> resourceObject) throws BizError {
        CouponProvider.ResContent resContent = resourceObject.getContent();
        if (CouponCategoryEnum.isNewCodeCoupon(resContent.getCategoryType())) {
            // 回滚资源
            String md5Code = DigestUtils.md5DigestAsHex(resContent.getCode().getBytes());
            int rollbackCnt = tbCodecouponMapper.updateStat(md5Code, resContent.getUserId(), resContent.getStatNew(), resContent.getStatOld(),
                    resourceObject.getOrderId(), BigDecimal.valueOf(0.0), BigDecimal.valueOf(0),
                    Instant.now().getEpochSecond(), resContent.getOffline(), "");
            if (rollbackCnt <= 0) {
                log.warn("rollback newCode fail. resId:{}", md5Code);
                throw ExceptionHelper.create(ErrCode.COUPON_ROLLBACK_FAIL, "回滚券资源失败");
            }

            // 日志
            TbCodecouponLog codecouponLog = initRollbackCodeCouponLog(resContent, resourceObject.getOrderId());
            tbCodecouponLogMapper.insert(codecouponLog);
        } else if (CouponCategoryEnum.isOldCodeCoupon(resContent.getCategoryType())) {
            // 回滚资源
            int rollbackCnt = xmQueueVcodeMapper.updateStat(resContent.getCode(), resContent.getStatNew(), resContent.getUserId(), resourceObject.getOrderId(),
                    resContent.getStatOld(), Instant.now().getEpochSecond());
            if (rollbackCnt <= 0) {
                log.warn("rollback oldCode fail. coude:{}", resContent.getCode());
                throw ExceptionHelper.create(ErrCode.COUPON_ROLLBACK_FAIL, "回滚券资源失败");
            }
        } else {
            // 回滚资源
            int rollbackCnt = tbCouponMapper.updateStat(resContent.getId(), resContent.getUserId(), resContent.getStatNew(), resContent.getStatOld(),
                    resourceObject.getOrderId(), BigDecimal.valueOf(0.0), BigDecimal.valueOf(0), Instant.now().getEpochSecond(), resContent.getOffline());
            if (rollbackCnt <= 0) {
                log.warn("rollback noCode fail. resId:{}", resContent.getId());
                throw ExceptionHelper.create(ErrCode.COUPON_ROLLBACK_FAIL, "回滚券资源失败");
            }

            // 插入日志
            TbCouponLog tbCouponLog = initRollbackCouponLog(resContent);
            tbCouponLogMapper.insert(tbCouponLog);
        }
        return true;
    }

    @Override
    public boolean refundCoupon(int resourceStatus, ResourceObject<CouponProvider.ResContent> resourceObject) throws BizError {
        return false;
    }

    /**
     * 初始化明码券日志
     *
     * @param resContent 资源内容
     * @param orderId    订单ID
     * @return 日志
     */
    private TbCodecouponLog initCodeCouponLog(CouponProvider.ResContent resContent, Long orderId) {
        String couponIndex;
        Long userId = resContent.getUserId();
        if (TestHelper.isLoadRunnerUser(userId)) {
            couponIndex = String.valueOf(System.nanoTime());
        } else {
            couponIndex = DigestUtils.md5DigestAsHex(resContent.getCode().getBytes());
        }

        TbCodecouponLog log = new TbCodecouponLog();
        log.setCouponIndex(couponIndex);
        log.setReduceExpress(resContent.getReduceExpress());
        log.setReplaceMoney(resContent.getReplaceMoney());
        log.setOffline(resContent.getOffline());
        log.setOrderId(orderId);
        log.setUserId(resContent.getUserId());
        log.setUseDesc(PromotionConstant.COUPON_LOG_USED_DESC);
        log.setAddTime(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()));
        log.setAdminId(PromotionConstant.DB_ADMIN_ID);
        log.setAdminDesc(String.valueOf(resContent.getClientId()));
        log.setLogType(CouponLogTypeEnum.OK.getVal());
        return log;
    }

    /**
     * 初始化明码回滚券日志
     *
     * @param resContent 资源内容
     * @param orderId    订单ID
     * @return 日志
     */
    private TbCodecouponLog initRollbackCodeCouponLog(CouponProvider.ResContent resContent, Long orderId) {
        String couponIndex = DigestUtils.md5DigestAsHex(resContent.getCode().getBytes());
        TbCodecouponLog log = new TbCodecouponLog();
        log.setCouponIndex(couponIndex);
        log.setReduceExpress(resContent.getReduceExpress().negate());
        log.setReplaceMoney(resContent.getReplaceMoney().negate());
        log.setOffline(resContent.getOffline());
        log.setOrderId(orderId);
        log.setUserId(resContent.getUserId());
        log.setUseDesc(PromotionConstant.COUPON_LOG_REFUND_DESC);
        log.setAddTime(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()));
        log.setAdminId(PromotionConstant.DB_ADMIN_ID);
        log.setAdminDesc(String.valueOf(resContent.getClientId()));
        log.setLogType(CouponLogTypeEnum.RE_FOUND.getVal());
        return log;
    }

    /**
     * 初始化券使用日志
     *
     * @param resContent 资源内容
     * @return 日志
     */
    private TbCouponLog initCouponLog(CouponProvider.ResContent resContent) {
        TbCouponLog log = new TbCouponLog();
        log.setCouponId(resContent.getId());
        log.setUserId(resContent.getUserId());
        log.setType(resContent.getCouponType());
        log.setOldStat(resContent.getStatNew());
        log.setNewStat(resContent.getStatOld());
        log.setAdminId(PromotionConstant.DB_ADMIN_ID);
        log.setAdminName(String.valueOf(resContent.getClientId()));
        // 玉增要求 防止不能及时回滚
        log.setAddTime(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()) + 1);
        log.setOffline(resContent.getOffline());
        log.setCouponDesc(PromotionConstant.COUPON_LOG_REFUND_DESC);
        return log;
    }

    /**
     * 初始化券回滚日志
     *
     * @param resContent 资源内容
     * @return 日志
     */
    private TbCouponLog initRollbackCouponLog(CouponProvider.ResContent resContent) {
        // 不可能的数
        Long couponId;
        Long userId = resContent.getUserId();
        if (TestHelper.isLoadRunnerUser(userId)) {
            couponId = System.nanoTime();
        } else {
            couponId = resContent.getId();
        }

        TbCouponLog log = new TbCouponLog();
        log.setCouponId(couponId);
        log.setUserId(resContent.getUserId());
        log.setType(resContent.getCouponType());
        log.setOldStat(resContent.getStatOld());
        log.setNewStat(resContent.getStatNew());
        log.setAdminId(PromotionConstant.DB_ADMIN_ID);
        log.setAdminName(String.valueOf(resContent.getClientId()));
        log.setAddTime(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()) + 1L);
        log.setOffline(resContent.getOffline());
        log.setCouponDesc(PromotionConstant.COUPON_LOG_USED_DESC);
        return log;
    }

    /**
     * 获取有码券信息
     *
     * @param code 用户ID
     * @return 用户券
     */
    private CouponOwnedInfo getCodeCoupon(String code) {
        Integer codeType = getCodeCouponType(code);
        if (CouponCategoryEnum.isOldCodeCoupon(codeType)) {
            XmQueueVcode xqvc = xmQueueVcodeMapper.getByCode(code);
            if (xqvc == null) {
                return null;
            }
            return CouponConverter.convert(xqvc, code, codeType);
        } else {
            String md5Code = DigestUtils.md5DigestAsHex(code.getBytes());
            TbCodecoupon tbcc = tbCodecouponMapper.getByMd5Code(md5Code);
            if (tbcc == null) {
                return null;
            }
            return CouponConverter.convert(tbcc, code, codeType);
        }
    }

    /**
     * 获取无码券信息
     *
     * @param cid 券ID
     * @param uid 用户ID
     * @return 用户券
     */
    private CouponOwnedInfo getNoCodeCoupon(Long cid, Long uid) {
        TbCoupon tbCoupon = tbCouponMapper.getByIdAndUserId(cid, uid);
        if (tbCoupon == null) {
            return null;
        }
        return CouponConverter.convert(tbCoupon);
    }

    /**
     * 批量获取无码券信息
     *
     * @param cidList 券ID
     * @param uid 用户ID
     * @return 用户券
     */
    private List<CouponOwnedInfo> batchGetNoCodeCoupon(List<Long> cidList, Long uid) {
        List<CouponOwnedInfo> list = new ArrayList<>();
        for (Long cid : cidList) {
            TbCoupon tbCoupon = tbCouponMapper.getByIdAndUserId(cid, uid);
            if (tbCoupon == null) {
                continue;
            }
            list.add(CouponConverter.convert(tbCoupon));
        }

        return list;
    }

    /**
     * 从缓存获取券类型信息
     *
     * @param typeId         类型ID
     * @param couponCategory 券类型
     * @return 券类型信息
     */
    private CouponInfo getCouponAllInfo(Long typeId, int couponCategory) {
        if (couponCategory == CouponCategoryEnum.NEWCODE.getType()
                || couponCategory == CouponCategoryEnum.NOCODE.getType()) {
            return couponInfoRedisDao.getCouponInfo(typeId);
        } else if (couponCategory == CouponCategoryEnum.OLDCODE.getType()) {
            return couponInfoRedisDao.getOldCodeCouponInfo(typeId);
        }
        return null;
    }

    public CouponOwnedInfo getByCode(String code, Long uid) throws BizError {
        // 检查码正确性
        if (!checkSum(code)) {
            log.warn("invalid code coupon {} for uid {}", code, uid);
            throw ExceptionHelper.create(ErrCode.ERR_COUPON_INVALID, "明码券校验失败");
        }
        // 获取用户资源-有码券
        CouponOwnedInfo cc = getCodeCoupon(code);
        if (cc == null) {
            log.warn("invalid code coupon {} for uid {}", code, uid);
            throw ExceptionHelper.create(ErrCode.ERR_COUPON_INVALID, "DB里不存在券: " + code);
        }
        // 获取券类型信息条件
        CouponInfo couponInfo = getCouponAllInfo(cc.getTypeId(), getCodeCouponCategory(cc));
        if (couponInfo == null) {
            log.error("get code coupon {} info null for uid {}", code, uid);
            throw ExceptionHelper.create(ErrCode.ERR_COUPON_TYPE_INVALID, "获取优惠券类型信息失败");
        }
        cc.setCouponType(cc.getCodeType());
        cc.setCouponInfo(couponInfo);
        return cc;
    }
}
