package com.xiaomi.nr.promotion.error;

import com.xiaomi.youpin.infra.rpc.errors.ErrorCode;

/**
 * 错误码常量
 * <p>
 * 错误码值分三部分 400{scope}{internalCode},
 * 400:          固定值
 * scope:        定义级别 {@link com.xiaomi.youpin.infra.rpc.errors.Scopes}
 * internalCode：错误级别的确切问题
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2021/3/26
 */
public class ErrCode {

    //******************购物车100~110**************
    /**
     * 购物车为空
     */
    public static final ErrorCode ERR_EMPTY_CART = ErrorCode.createOnce(ErrScope.PROMOTION, 100);

    /**
     * 购物车存在未知商品
     */
    public static final ErrorCode ERR_UNKNOWN_GOODS = ErrorCode.createOnce(ErrScope.PROMOTION, 101);

    /**
     * 兑换场景只能兑换一个商品
     */
    public static final ErrorCode ERR_EXCHANGE_GOODS = ErrorCode.createOnce(ErrScope.PROMOTION, 102);

    //*******************活动110~130*********************
    /**
     * 活动未在有效期内
     */
    public static final ErrorCode ERR_INVALID_ACT_TIME = ErrorCode.createOnce(ErrScope.PROMOTION, 110);

    /**
     * 活动不存在
     */
    public static final ErrorCode ERR_INVALID_ACT_EXIST = ErrorCode.createOnce(ErrScope.PROMOTION, 111);

    //*******************优惠券130~150****************
    /**
     * 没有传入可使用的优惠券
     */
    public static final ErrorCode ERR_EMPTY_COUPONS = ErrorCode.createOnce(ErrScope.PROMOTION, 130);

    /**
     * 一次最多只能使用一张优惠券
     */
    public static final ErrorCode ERR_TOO_MANY_COUPONS = ErrorCode.createOnce(ErrScope.PROMOTION, 131);

    /**
     * 优惠券未在有效期内
     */
    public static final ErrorCode ERR_INVALID_COUPON_TIME = ErrorCode.createOnce(ErrScope.PROMOTION, 132);

    /**
     * 券对于购物车活动后的物品既不能减钱,又不能包邮，不能使用
     */
    public static final ErrorCode ERR_COUPON_NO_USE = ErrorCode.createOnce(ErrScope.PROMOTION, 133);

    /**
     * 未满足优惠券使用条件
     */
    public static final ErrorCode ERR_COUPON_MONEY_NUM = ErrorCode.createOnce(ErrScope.PROMOTION, 134);

    /**
     * 无可用商品
     */
    public static final ErrorCode ERR_COUPON_NO_VALID_GOODS = ErrorCode.createOnce(ErrScope.PROMOTION, 135);

    /**
     * 不匹配的履约方式
     */
    public static final ErrorCode ERR_COUPON_SHIPMENT_ID = ErrorCode.createOnce(ErrScope.PROMOTION, 136);

    /**
     * 券优惠类型不匹配
     */
    public static final ErrorCode ERR_COUPON_PROMOTION_TYPE_MISMATCH = ErrorCode.createOnce(ErrScope.PROMOTION, 137);

    /**
     * 组套id缺少
     */
    public static final ErrorCode ERR_SERVICE_PACK_ID_MISMATCH = ErrorCode.createOnce(ErrScope.PROMOTION, 138);


    //*******************红包150~160*********************

    //*******************礼品卡160~180*********************
    /**
     * 礼品卡只能在指定时间段使用
     */
    public static ErrorCode ERR_INVALID_ECARD_TIME = ErrorCode.createOnce(ErrScope.PROMOTION, 161);

    /**
     * 仅支持同时使用一类礼品卡
     */
    public static ErrorCode ERR_SEVERAL_ECARDKIND = ErrorCode.createOnce(ErrScope.PROMOTION, 162);

    /**
     * 无效使用礼品卡
     */
    public static ErrorCode ERR_ECARD_TOOMANY = ErrorCode.createOnce(ErrScope.PROMOTION, 163);

    //*******************订单180~190*********************
    /**
     * 订单不存在
     */
    public static ErrorCode ERR_ORDER_NOT_EXIST = ErrorCode.createOnce(ErrScope.PROMOTION, 180);

    //*******************其他190~200*********************
    /**
     * 未指定用户
     */
    public static final ErrorCode ERR_EMPTY_USER_ID = ErrorCode.createOnce(ErrScope.PROMOTION, 190);

    //******************promotion到pulse msg需要做映射的错误码 3××*********************
    // 注释为pulse需要输出的msg
    /**
     * 无效的优惠券
     */
    public static ErrorCode ERR_COUPON_INVALID = ErrorCode.createOnce(ErrScope.PROMOTION, 301);
    /**
     * 无效的优惠券类型
     */
    public static ErrorCode ERR_COUPON_TYPE_INVALID = ErrorCode.createOnce(ErrScope.PROMOTION, 302);
    /**
     * 订单中不含可用券商品
     */
    public static final ErrorCode ERR_COUPON_INCLUDE_GOODS = ErrorCode.createOnce(ErrScope.PROMOTION, 303);
    /**
     * 订单中包含不可用券商品，排除
     */
    public static final ErrorCode ERR_EXCLUDE_GOODS = ErrorCode.createOnce(ErrScope.PROMOTION, 304);

    /**
     * 订单中不含可用礼品卡商品
     */
    public static final ErrorCode ERR_ECARD_INCLUDE_GOODS = ErrorCode.createOnce(ErrScope.PROMOTION, 310);

    /**
     * 不满足礼品卡使用条件
     */
    public static final ErrorCode ERR_GOODS_NEED = ErrorCode.createOnce(ErrScope.PROMOTION, 311);

    /**
     * 无效的礼品卡
     */
    public static ErrorCode ERR_INVALID_ECARD = ErrorCode.createOnce(ErrScope.PROMOTION, 312);

    /**
     * 礼品卡金额异常
     */
    public static ErrorCode ERR_ECARD_CONSUME_BALANCE = ErrorCode.createOnce(ErrScope.PROMOTION, 313);
    /**
     * 工时去重异常
     */
    public static ErrorCode ERR_WORK_HOUR = ErrorCode.createOnce(ErrScope.PROMOTION, 314);

    //***********************购物车4××错误码**************************
    /**
     * 购物车错误，购物车条目为空或者不合法
     */
    public static final ErrorCode ERR_CART = ErrorCode.createOnce(ErrScope.PROMOTION, 401);

    /**
     * 购物车条目不是套装
     */
    public static final ErrorCode ERR_CART_PACKAGE = ErrorCode.createOnce(ErrScope.PROMOTION, 402);

    /**
     * 分摊计算错误,拆分sku前后统计数据价格不相等
     */
    public static final ErrorCode ERR_DIVIDE_CAL = ErrorCode.createOnce(ErrScope.PROMOTION, 403);

    /**
     * 购物车条目减免金额不能整除
     */
    public static final ErrorCode ERR_DIVIDE_EVENLY_CAL = ErrorCode.createOnce(ErrScope.PROMOTION, 404);

    /**
     * 分摊计算错误，优惠金额不能全部被分摊
     */
    public static final ErrorCode ERR_SHARE_DIFF_CAL = ErrorCode.createOnce(ErrScope.PROMOTION, 405);

    /**
     * 购物车拆分为订单条目的列表为空
     */
    public static final ErrorCode ERR_ORDER_ITEMS_EMPTY = ErrorCode.createOnce(ErrScope.PROMOTION, 406);

    /**
     * 订单条目为空或者不合法
     */
    public static final ErrorCode ERR_ORDER_ITEM = ErrorCode.createOnce(ErrScope.PROMOTION, 407);

    /**
     * 可用商品购物车itemId不匹配
     */
    public static final ErrorCode ERR_CART_ITEMID_MATCH = ErrorCode.createOnce(ErrScope.PROMOTION, 408);

    //***********************活动4××错误码**************************
    /**
     * 活动优惠信息为空
     */
    public static final ErrorCode ERR_ACT_PROMOTION_EMPTY = ErrorCode.createOnce(ErrScope.PROMOTION, 450);

    /**
     * 活动数量锁定失败
     */
    public static ErrorCode CODE_STOCK_LOCK_FAIL = ErrorCode.createOnce(ErrScope.PROMOTION, 451);

    /**
     * 设置活动用户参加记录失败
     */
    public static ErrorCode CODE_ACT_USER_RECORD_INSERT_FAIL = ErrorCode.createOnce(ErrScope.PROMOTION, 452);

    //************************系统错误5××～7××************************
    /**
     * 资源管理器，锁资源冲突
     */
    public static ErrorCode CODE_RESOURCE_ITEM_LOCK_CONFLICT = ErrorCode.createOnce(ErrScope.PROMOTION, 501);

    /**
     * 资源不存在
     */
    public static ErrorCode CODE_RESOURCE_CONVERT_FAIL = ErrorCode.createOnce(ErrScope.PROMOTION, 502);

    //***********************优惠劵8××错误码**************************
    /**
     * 优惠券类型不存在
     */
    public static final ErrorCode ERR_COUPON_TYPE = ErrorCode.createOnce(ErrScope.PROMOTION, 800);

    /**
     * 锁定券资源失败
     */
    public static final ErrorCode COUPON_LOCK_FAIL = ErrorCode.createOnce(ErrScope.PROMOTION, 801);
    /**
     * 回滚券资源失败
     */
    public static final ErrorCode COUPON_ROLLBACK_FAIL = ErrorCode.createOnce(ErrScope.PROMOTION, 802);

    /**
     * 券消费失败
     */
    public static final ErrorCode COUPON_CONSUME_FAIL = ErrorCode.createOnce(ErrScope.PROMOTION, 803);


    //***********************红包8××错误码**************************
    /**
     * 计算红包列表错误
     */
    public static ErrorCode ERR_REDPACKET_CAL = ErrorCode.createOnce(ErrScope.PROMOTION, 850);
    /**
     * 数据库和redis数据不一致
     */
    public static ErrorCode ERR_REDPACKET_DBANDREDISNOTEQUAL = ErrorCode.createOnce(ErrScope.PROMOTION, 851);
    /**
     * 红包余额计算错误
     */
    public static ErrorCode ERR_REDPACKET_BALANCE = ErrorCode.createOnce(ErrScope.PROMOTION, 852);
    /**
     * 红包DB操作失败
     */
    public static ErrorCode ERR_REDPACKET_DB_UPDATE = ErrorCode.createOnce(ErrScope.PROMOTION, 853);

    //***********************礼品卡9××错误码**************************
    /**
     * 礼品卡
     */
    public static ErrorCode ERR_INVALID_ECARD_INCLUDEGOODS = ErrorCode.createOnce(ErrScope.PROMOTION, 900);

    public static ErrorCode ERR_INVALID_CHANNEL = ErrorCode.createOnce(ErrScope.PROMOTION, 901);

    public static ErrorCode ERR_ECARD_TYPE_INFO = ErrorCode.createOnce(ErrScope.PROMOTION, 902);

    public static ErrorCode ERR_ECARD_DB_CONTENT = ErrorCode.createOnce(ErrScope.PROMOTION, 903);

    public static ErrorCode ERR_ECARD_TYPE_ID = ErrorCode.createOnce(ErrScope.PROMOTION, 904);

    public static ErrorCode ERR_ECARD_SORDER_CAL = ErrorCode.createOnce(ErrScope.PROMOTION, 905);

    /**
     * 礼品卡金额计算错误
     */
    public static ErrorCode ERR_ECARD_CAL = ErrorCode.createOnce(ErrScope.PROMOTION, 906);
    /**
     * 礼品卡余额更新失败
     */
    public static final ErrorCode ERR_ECARD_UPDATEBALANCE = ErrorCode.createOnce(ErrScope.PROMOTION, 907);

    public static ErrorCode ERR_ECARD_SORDER_PARAM = ErrorCode.createOnce(ErrScope.PROMOTION, 908);

    //***********************其他9××错误码**************************
    /**
     * 政策错误
     */
    public static final ErrorCode ERR_POLICY_CAL = ErrorCode.createOnce(ErrScope.PROMOTION, 950);

    /**
     * 错误的政策设置，规则为空
     */
    public static final ErrorCode ERR_POLICY_RULE_EMPTY = ErrorCode.createOnce(ErrScope.PROMOTION, 951);

    /**
     * 购物车校验错误
     */
    public static final ErrorCode ERR_CART_CHECK = ErrorCode.createOnce(ErrScope.PROMOTION, 952);

    /**
     * 重复订单错误
     */
    public static ErrorCode CODE_RESOURCE_ORDER_ID_CONFLICT = ErrorCode.createOnce(ErrScope.PROMOTION, 953);

    /**
     * 订单重复关闭错误
     */
    public static ErrorCode ERR_REFUND_REPEAT = ErrorCode.createOnce(ErrScope.PROMOTION, 954);
    /**
     * 套装信息错误
     */
    public static final ErrorCode ERR_INVALID_PACKAGE = ErrorCode.createOnce(ErrScope.PROMOTION, 955);

    /**
     * pulse内部输入参数错误
     */
    public static final ErrorCode ERR_FUNC_INPUT = ErrorCode.createOnce(ErrScope.PROMOTION, 956);

    // 调用外部服务错误
    /**
     * 请求Client信息失败
     */
    public static ErrorCode ERR_RPC_CLIENT_FETCH_FAIL = ErrorCode.createOnce(ErrScope.PROMOTION, 960);

    /**
     * 找不到Client信息
     */
    public static ErrorCode ERR_RPC_CLIENT_NOT_FOUND = ErrorCode.createOnce(ErrScope.PROMOTION, 961);

    /**
     * 请求人群信息失败
     */
    public static ErrorCode ERR_RPC_USERLIST_FETCH_FAIL = ErrorCode.createOnce(ErrScope.PROMOTION, 962);
    // ********************************************************************
    /**
     * 三方优惠套餐不存在
     */
    public static ErrorCode ERR_PHOENIX_PACKAGE = ErrorCode.createOnce(ErrScope.PROMOTION, 970);
    /**
     * 三方优惠套餐不存在
     */
    public static ErrorCode ERR_PHOENIX_AMOUNT = ErrorCode.createOnce(ErrScope.PROMOTION, 971);
    /**
     * 三方优惠套餐不存在
     */
    public static ErrorCode ERR_PHOENIX_USED = ErrorCode.createOnce(ErrScope.PROMOTION, 972);
    /**
     * 换新立减订单查询
     */
    public static ErrorCode ERR_PHOENIX_RENEW_QUERY = ErrorCode.createOnce(ErrScope.PROMOTION, 973);
    /**
     * 换新立减订单冻结
     */
    public static ErrorCode ERR_PHOENIX_RENEW_FREEZE = ErrorCode.createOnce(ErrScope.PROMOTION, 974);
    /**
     * 换新立减订单查询
     */
    public static ErrorCode ERR_PHOENIX_RENEW_UNFREEZE = ErrorCode.createOnce(ErrScope.PROMOTION, 975);

    //***********************积分10××错误码**************************
    /**
     * 积分实际可抵扣金额小于预期抵扣金额
     */
    public static final ErrorCode POINT_CHECKOUT_REDUCE_AMOUNT_LESS = ErrorCode.createOnce(ErrScope.PROMOTION, 980);
    /**
     * 券消费失败
     */
    public static final ErrorCode POINT_CONSUME_FAIL = ErrorCode.createOnce(ErrScope.PROMOTION, 981);
    /**
     * 回滚券资源失败
     */
    public static final ErrorCode POINT_ROLLBACK_FAIL = ErrorCode.createOnce(ErrScope.PROMOTION, 982);
    /**
     * 活动失效
     */
    public static final ErrorCode PROMOTION_ACTIVITY_INVALID = ErrorCode.createOnce(ErrScope.PROMOTION, 983);
    /**
     * 活动已提交
     */
    public static final ErrorCode PROMOTION_ACTIVITY_COMMIT = ErrorCode.createOnce(ErrScope.PROMOTION, 984);
    /**
     * 优惠券互斥，无法同时选择
     */
    public static final ErrorCode PROMOTION_GROUP_COUPON_MUTEX = ErrorCode.createOnce(ErrScope.PROMOTION, 985);
    /**
     * 北京以旧换新补贴，加购数量导致不可用
     */
    public static final ErrorCode PROMOTION_ACTIVITY_PURCHASE_SUBSIDY_NUM_LIMIT = ErrorCode.createOnce(ErrScope.PROMOTION, 986);
    /**
     * 北京以旧换新补贴，无可用资格码导致不可用
     */
    public static final ErrorCode PROMOTION_ACTIVITY_PURCHASE_SUBSIDY_NO_QUALIFY = ErrorCode.createOnce(ErrScope.PROMOTION, 987);


}
