package com.xiaomi.nr.promotion.componet.action;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.GoodsTypeEnum;
import com.xiaomi.nr.promotion.api.dto.enums.UserLevelEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.B2tVipDiscountRule;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.DiscountRate;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.B2tVipDiscountPromotionConfig;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.PriceUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 折扣价执行行为
 *
 * <AUTHOR>
 * @date 2023/2/14
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class B2tVipDiscountAction extends AbstractAction {
    /**
     * 优惠促销ID
     */
    private Long promotionId;
    /**
     * 优惠类型
     */
    private PromotionToolType promotionType;
    /**
     * 等级折扣
     * key: userLevel  ID val:Discount
     */
    private DiscountRate discountRate;

    private B2tVipDiscountRule.GoodsDiscountRate goodsDiscountRate;

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        List<CartItem> cartList = request.getCartList();
        List<GoodsIndex> indexList = context.getGoodIndex();
        if (CollectionUtils.isEmpty(indexList)) {
            return;
        }



        // 处理折扣价格
        changePrice(cartList, indexList, request.getUserLevel());

        // 设置结果
        setResult(context, promotion);
    }


    private int getDiscountByUserLevel(String userLevel) {
        UserLevelEnum levelEnum = UserLevelEnum.getByLevel(userLevel);
        if (UserLevelEnum.NORMAL == levelEnum) {
            return discountRate.getNormal();
        }
        if (UserLevelEnum.VIP == levelEnum) {
            return discountRate.getVip();
        }
        if (UserLevelEnum.SVIP == levelEnum) {
            return discountRate.getSvip();
        }
        return discountRate.getNormal();
    }
    private int getDiscountByGoodsType(Integer goodsType) {
        if (GoodsTypeEnum.NORM.getValue().equals(goodsType)) {
            return goodsDiscountRate.getNormalRate();
        } else if (GoodsTypeEnum.RECOM.getValue().equals(goodsType)) {
            return goodsDiscountRate.getRecomRate();
        }
        return 100;
    }


    private void changePrice(List<CartItem> cartList, List<GoodsIndex> indexList, String userLevel) {
        for (GoodsIndex index : indexList) {
            CartItem item = cartList.get(index.getIndex());
            if (item == null || !Objects.equals(item.getItemId(), index.getItemId())) {
                log.warn("vipDiscount idx not found in cats. idx:{}, item:{}", index, item);
                continue;
            }
            long finalPrice = item.getCartPrice();

            //不同渠道对应不同的折扣方式
            if (discountRate != null) {
                finalPrice = PriceUtil.multiDiscount(item.getCartPrice(), getDiscountByUserLevel(userLevel));
            } else if (goodsDiscountRate != null) {
                finalPrice = PriceUtil.multiDiscount(item.getCartPrice(), getDiscountByGoodsType(item.getGoodsType()));
            }
            changePriceWithReduceDetail(item, finalPrice, promotionId, promotionType);
        }
    }

    private void setResult(LocalContext context, ActivityTool tool) throws BizError {
        List<GoodsIndex> indexList = context.getGoodIndex();
        List<String> parentItemList = CartHelper.getParentItemIdList(indexList);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setJoinedItemId(parentItemList);
        promotionInfo.setParentItemId(parentItemList);
        promotionInfo.setJoined(BooleanEnum.YES.getValue());

        // 设置上下文结果
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof B2tVipDiscountPromotionConfig)) {
            log.error("config is not instanceof B2tVipDiscountPromotionConfig. class:{}", config.getName());
            return;
        }
        B2tVipDiscountPromotionConfig promotionConfig = (B2tVipDiscountPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        this.discountRate = promotionConfig.getDiscountRate();
        this.goodsDiscountRate = promotionConfig.getGoodsDiscountRate();
    }
}
