package com.xiaomi.nr.promotion.resource;

import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.provider.*;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 简单工厂模式
 * 根据resource的类型，生成相应的Provider实例,并设置相关数据
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
@Component
public class ResourceProviderFactory {
    @Autowired
    private ObjectFactory<OnlineActLimitProvider> onlineActLimitProviderObjectFactory;
    @Autowired
    private ObjectFactory<OnlineActUserRecordDailyProvider> onlineActUserRecordDailyProviderObjectFactory;
    @Autowired
    private ObjectFactory<OnlineActUserRecordTotalProvider> onlineActUserRecordTotalProviderObjectFactory;
    @Autowired
    private ObjectFactory<OfflineActAllStoreLimitDailyProvider> offlineActAllStoreLimitDailyProviderObjectFactory;
    @Autowired
    private ObjectFactory<OfflineActAllStoreLimitProvider> offlineActAllStoreLimitProviderObjectFactory;
    @Autowired
    private ObjectFactory<OfflineActPersonLimitDailyProvider> offlineActPersonLimitDailyProviderObjectFactory;
    @Autowired
    private ObjectFactory<OfflineActPersonLimitProvider> offlineActPersonLimitProviderObjectFactory;
    @Autowired
    private ObjectFactory<OfflineActStoreLimitDailyProvider> offlineActStoreLimitDailyProviderObjectFactory;
    @Autowired
    private ObjectFactory<OfflineActStoreLimitProvider> offlineActStoreLimitProviderObjectFactory;
    @Autowired
    private ObjectFactory<OfflineActUtypePersonLimitDailyProvider> offlineActUtypePersonLimitDailyProviderObjectFactory;
    @Autowired
    private ObjectFactory<OfflineActUtypePersonLimitProvider> offlineActUtypePersonLimitProviderObjectFactory;
    @Autowired
    private ObjectFactory<OnsaleActAllStoreLimitDailyProvider> onsaleActAllStoreLimitDailyProviderObjectFactory;
    @Autowired
    private ObjectFactory<OnsaleActAllStoreLimitProvider> onsaleActAllStoreLimitProviderObjectFactory;
    @Autowired
    private ObjectFactory<OnsaleActMobileUserLimitProvider> onsaleActMobileUserLimitProviderObjectFactory;
    @Autowired
    private ObjectFactory<OnsaleActStoreLimitDailyProvider> onsaleActStoreLimitDailyProviderObjectFactory;
    @Autowired
    private ObjectFactory<OnsaleActStoreLimitProvider> onsaleActStoreLimitProviderObjectFactory;
    @Autowired
    private ObjectFactory<OnsaleActUserLimitProvider> onsaleActUserLimitProviderObjectFactory;
    @Autowired
    private ObjectFactory<CouponProvider> couponProviderObjectFactory;
    @Autowired
    private ObjectFactory<EcardProvider> ecardProviderObjectFactory;
    @Autowired
    private ObjectFactory<RedpacketProvider> redpacketProviderObjectFactory;
    @Autowired
    private ObjectFactory<PointProvider> pointProviderObjectFactory;
    @Autowired
    private ObjectFactory<OrderBenefitProvider> orderBenefitProviderObjectFactory;
    @Autowired
    private ObjectFactory<GiftLimitProvider> giftLimitProviderObjectFactory;
    @Autowired
    private ObjectFactory<OrderPromotionProvider> orderPromotionProviderObjectFactory;
    @Autowired
    private ObjectFactory<BargainLimitProvider> bargainLimitProviderObjectFactory;
    @Autowired
    private ObjectFactory<RenewReduceResourceProvider> renewReduceResourceProviderObjectFactory;
    @Autowired
    private ObjectFactory<GoodsLimitResourceProvider> goodsLimitResourceProviderObjectFactory;
    @Autowired
    private ObjectFactory<ActUserStoreLimitProvider> partOnsaleActUserLimitProvider;
    @Autowired
    private ObjectFactory<ActGoodsStoreLimitResourceProvider> partOnsaleGoodsReduceResourceProvider;
    @Autowired
    private ObjectFactory<PhoenixHuashengProvider> phoenixHuashengProviderObjectFactory;
    @Autowired
    private ObjectFactory<PhoenixOperatorProvider> phoenixOperatorProviderObjectFactory;
    @Autowired
    private ObjectFactory<PhoenixRenewProvider> phoenixRenewProviderObjectFactory;
    @Autowired
    private ObjectFactory<CarOrderPromotionProvider> carOrderPromotionProviderObjectFactory;
    @Autowired
    private ObjectFactory<SubsidyPhoenixProvider> subsidyPhoenixProviderObjectFactory;
    @Autowired
    private ObjectFactory<GovernmentSubsidyPhoenixProvider> governmentSubsidyPhoenixProviderObjectFactory;
    @Autowired
    private ObjectFactory<UserActivityCountProvider> userActivityCountProviderObjectFactory;
    @Autowired
    private ObjectFactory<VidActivityCountProvider> vidActivityCountProviderObjectFactory;

    public ResourceProvider getProvider(ResourceType type) throws BizError {
        switch (type) {
            case ONLINE_ACT_LIMIT:
                return onlineActLimitProviderObjectFactory.getObject();
            case ONLINE_ACT_RECORD_DAILY:
                return onlineActUserRecordDailyProviderObjectFactory.getObject();
            case ONLINE_ACT_RECORD:
                return onlineActUserRecordTotalProviderObjectFactory.getObject();
            case OFFLINE_ACT_ALL_STORE_LIMIT_DAILY:
                return offlineActAllStoreLimitDailyProviderObjectFactory.getObject();
            case OFFLINE_ACT_ALL_STORE_LIMIT:
                return offlineActAllStoreLimitProviderObjectFactory.getObject();
            case OFFLINE_ACT_PERSON_LIMIT_DAILY:
                return offlineActPersonLimitDailyProviderObjectFactory.getObject();
            case OFFLINE_ACT_PERSON_LIMIT:
                return offlineActPersonLimitProviderObjectFactory.getObject();
            case OFFLINE_ACT_STORE_LIMIT_DAILY:
                return offlineActStoreLimitDailyProviderObjectFactory.getObject();
            case OFFLINE_ACT_STORE_LIMIT:
                return offlineActStoreLimitProviderObjectFactory.getObject();
            case OFFLINE_ACT_UTYPE_PERSON_LIMIT_DAILY:
                return offlineActUtypePersonLimitDailyProviderObjectFactory.getObject();
            case OFFLINE_ACT_UTYPE_PERSON_LIMIT:
                return offlineActUtypePersonLimitProviderObjectFactory.getObject();
            case ONSALE_ACT_USER_LIMIT:
                return onsaleActUserLimitProviderObjectFactory.getObject();
            case ONSALE_ACT_STORE_LIMIT:
                return onsaleActStoreLimitProviderObjectFactory.getObject();
            case ONSALE_ACT_STORE_LIMIT_DAILY:
                return onsaleActStoreLimitDailyProviderObjectFactory.getObject();
            case ONSALE_ACT_MOBILE_USER_LIMIT:
                return onsaleActMobileUserLimitProviderObjectFactory.getObject();
            case ONSALE_ACT_ALL_STORE_LIMIT:
                return onsaleActAllStoreLimitProviderObjectFactory.getObject();
            case ONSALE_ACT_ALL_STORE_LIMIT_DAILY:
                return onsaleActAllStoreLimitDailyProviderObjectFactory.getObject();
            case COUPON:
                return couponProviderObjectFactory.getObject();
            case ECARD:
                return ecardProviderObjectFactory.getObject();
            case RED_PACKET:
                return redpacketProviderObjectFactory.getObject();
            case POINT:
                return pointProviderObjectFactory.getObject();
            case ORDER_BENEFIT:
                return orderBenefitProviderObjectFactory.getObject();
            case GIFT_ACT_LIMIT:
                return giftLimitProviderObjectFactory.getObject();
            case ORDER_PROMOTION:
                return orderPromotionProviderObjectFactory.getObject();
            case BARGAIN_ACT_LIMIT:
                return bargainLimitProviderObjectFactory.getObject();
            case RENEW_REDUCE_ACT_LIMIT:
                return renewReduceResourceProviderObjectFactory.getObject();
            case GOODS_ACT_LIMIT:
                return goodsLimitResourceProviderObjectFactory.getObject();
            case PARTONSALE_USER_REDUCE_ACT_LIMIT:
                return partOnsaleActUserLimitProvider.getObject();
            case PARTONSALE_GOODS_REDUCE_ACT_LIMIT:
                return partOnsaleGoodsReduceResourceProvider.getObject();
            case PHOENIX_HUASHENG:
                return phoenixHuashengProviderObjectFactory.getObject();
            case PHOENIX_OPERATOR:
                return phoenixOperatorProviderObjectFactory.getObject();
            case PHOENIX_RENEW:
                return phoenixRenewProviderObjectFactory.getObject();
            case CAR_ORDER_BENEFIT:
                return carOrderPromotionProviderObjectFactory.getObject();
            case PURCHASE_SUBSIDY_ACT_LIMIT:
                return subsidyPhoenixProviderObjectFactory.getObject();
            case GOVERNMENT_SUBSIDY_ACT_LIMIT:
                return governmentSubsidyPhoenixProviderObjectFactory.getObject();
            case USER_JOIN_ACT_NUM:
                return userActivityCountProviderObjectFactory.getObject();
            case VID_JOIN_ACT_LIMIT:
                return vidActivityCountProviderObjectFactory.getObject();
            default:
                throw ExceptionHelper.create(GeneralCodes.InternalError, "Error ResourceType");
        }
    }
}
