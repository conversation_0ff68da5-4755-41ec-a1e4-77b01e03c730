package com.xiaomi.nr.promotion.domain.coupon.model;

import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 2024/3/25
 */
@Data
public class CouponListCheckoutResult {

    private List<Coupon> validCouponList = new ArrayList<>();

    private List<Coupon> invalidCouponList = new ArrayList<>();

    private Map<Long, CouponCheckoutResult> checkoutResultMap = new HashMap<>();


    public void addValidCoupon(Coupon coupon) {
        validCouponList.add(coupon);
    }

    public void addInvalidCoupon(Coupon coupon) {
        invalidCouponList.add(coupon);
    }

    public void addCheckoutResult(CouponCheckoutResult result) {
        checkoutResultMap.put(result.getCouponId(), result);
    }


}
