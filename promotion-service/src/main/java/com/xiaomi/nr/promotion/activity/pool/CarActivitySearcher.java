package com.xiaomi.nr.promotion.activity.pool;

import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by wangweiyi on 2023/8/16
 */
@Component
@Slf4j
public class CarActivitySearcher extends AbstractActivitySearcher {



    @Override
    public List<Long> doSearchIndex(ActSearchParam actSearchParam) {

        List<ActSearchParam.GoodsInSearch> goodsList = actSearchParam.getGoodsList();
        if (CollectionUtils.isEmpty(goodsList)) {
            return Collections.emptyList();
        }
        List<String> goodsIdList = goodsList.stream()
                .map(goods -> String.valueOf(goods.getSsuId()))
                .collect(Collectors.toList());

        // 获取skuPackage可参加活动
        Set<Long> actIdInGoods = promotionInstancePool.getCurrentActIds(goodsIdList);


        List<Long> actIdInChannel = promotionInstancePool.getCurrentChannelActIds(Lists.newArrayList(actSearchParam.getChannel()));



        // 做交集. （重要！actIdList 为有序，必须为基准来做交集， 不要交换顺序）
        actIdInGoods.retainAll(actIdInChannel);

        return new ArrayList<>(actIdInGoods);

    }

    @Override
    public List<ActivityTool> doFilter(ActSearchParam actSearchParam, List<ActivityTool> activityTools) {
        return activityTools.stream()
                .filter(tool -> checkTime(tool, Boolean.FALSE))
                .filter(activityTool -> activityTool.getBizPlatform() == getBizPlatform())
                .collect(Collectors.toList());
    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR;
    }

    public List<ActSearchParam.GoodsInSearch> createSearchGoods(List<CartItem> cartItemList) {
        List<ActSearchParam.GoodsInSearch> resultList = new ArrayList<>();

        for (CartItem item : cartItemList) {
            ActSearchParam.GoodsInSearch goodsInSearch = new ActSearchParam.GoodsInSearch();
            goodsInSearch.setSsuId(item.getSsuId());
            resultList.add(goodsInSearch);
        }
        return resultList;
    }
}
