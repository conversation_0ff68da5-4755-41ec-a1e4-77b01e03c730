package com.xiaomi.nr.promotion.activity.pool;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by wangweiyi on 2023/8/16
 */
@Component
@Slf4j
public class CarActivitySearcher extends AbstractActivitySearcher {

    @Autowired(required = false)
    private CarPromotionInstancePool carPromotionInstancePool;


	@Override
    public List<Long> doSearchIndex(ActSearchParam actSearchParam) {

        List<ActSearchParam.GoodsInSearch> goodsList = actSearchParam.getGoodsList();
        if (CollectionUtils.isEmpty(goodsList)) {
            return Collections.emptyList();
        }
        List<String> goodsIdList = goodsList.stream()
                .map(goods -> String.valueOf(goods.getSsuId()))
                .collect(Collectors.toList());

        // 获取skuPackage可参加活动
        Set<Long> actIdInGoods = carPromotionInstancePool.getCurrentActIds(goodsIdList);

        // 根据渠道可参与活动
        List<Long> actIdInChannel = carPromotionInstancePool.getCurrentChannelActIds(Lists.newArrayList(actSearchParam.getChannel()));

        // 做交集
        actIdInGoods.retainAll(actIdInChannel);

        // todo 后续会使用getSortedActIds作为排序基线进行交集操作
        return new ArrayList<>(actIdInGoods);

    }

    @Override
    public List<ActivityTool> doSearchAct(List<Long> actIds) {
        return carPromotionInstancePool.getCurrentTools(actIds);
    }

    @Override
    public ActivityTool doSearchAct(Long actId) {
        return carPromotionInstancePool.getCurrentTool(actId);
    }

    @Override
    public List<ActivityTool> doFilter(ActSearchParam actSearchParam, List<ActivityTool> activityTools) {
        return activityTools.stream()
                .filter(this::checkTime)
                .filter(activityTool -> activityTool.getBizPlatform() == getBizPlatform())
                .collect(Collectors.toList());
    }

    /**
     * 检查活动时间,闭区间
     *
     * @param tool 活动工具
     * @return 时间是否符合
     */
    private boolean checkTime(ActivityTool tool) {
        long now = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        // 进行中
        return now >= tool.getUnixStartTime() && now <= tool.getUnixEndTime();
    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR;
    }

    public List<ActSearchParam.GoodsInSearch> createSearchGoods(List<CartItem> cartItemList) {
        List<ActSearchParam.GoodsInSearch> resultList = new ArrayList<>();

        for (CartItem item : cartItemList) {
            ActSearchParam.GoodsInSearch goodsInSearch = new ActSearchParam.GoodsInSearch();
            goodsInSearch.setSsuId(item.getSsuId());
            resultList.add(goodsInSearch);
        }
        return resultList;
    }
}
