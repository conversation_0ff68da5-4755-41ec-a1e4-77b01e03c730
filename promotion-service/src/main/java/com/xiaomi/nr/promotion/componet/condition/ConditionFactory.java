package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.componet.condition.carmaintenance.MaintenanceDiscountCondition;
import com.xiaomi.nr.promotion.componet.condition.carmaintenance.MaintenanceItemFreeCondition;
import com.xiaomi.nr.promotion.componet.condition.carsale.CarExchangeSubsidyCondition;
import com.xiaomi.nr.promotion.componet.condition.carsale.CarOnsaleCondition;
import com.xiaomi.nr.promotion.componet.condition.carsale.CarOrderReduceCondition;
import com.xiaomi.nr.promotion.componet.condition.carsale.CarRangeReduceCondition;
import com.xiaomi.nr.promotion.componet.condition.carshop.CarShopOnsaleCondition;
import com.xiaomi.nr.promotion.componet.condition.carshop.CarShopBuyGiftCondition;
import com.xiaomi.nr.promotion.componet.condition.carshop.CarShopVipCondition;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 过滤条件组件
 *
 * <AUTHOR>
 * @date 2021/3/16
 */
@Component
public class ConditionFactory {
    @Autowired
    private ObjectFactory<OnsaleCondition> onsaleConditionObjectFactory;
    @Autowired
    private ObjectFactory<CarShopOnsaleCondition> carShopOnsaleConditionObjectFactory;
    @Autowired
    private ObjectFactory<GiftBargainCondition> giftBargainConditionObjectFactory;
    @Autowired
    private ObjectFactory<QuotaCondition> quotaConditionObjectFactory;
    @Autowired
    private ObjectFactory<ClientCondition> clientConditionObjectFactory;
    @Autowired
    private ObjectFactory<DailyTimeCondition> dailyTimeConditionObjectFactory;
    @Autowired
    private ObjectFactory<FrequencyCondition> frequencyConditionObjectFactory;
    @Autowired
    private ObjectFactory<OnlineJoinLimitCondition> onlineJoinLimitConditionObjectFactory;
    @Autowired
    private ObjectFactory<OfflineJoinLimitCondition> offlineJoinLimitConditionObjectFactory;
    @Autowired
    private ObjectFactory<OrgCondition> orgConditionObjectFactory;
    @Autowired
    private ObjectFactory<UserGroupCondition> userGroupConditionObjectFactory;
    @Autowired
    private ObjectFactory<BuyGiftBaseNumCondition> buyGiftBaseNumConditionObjectFactory;
    @Autowired
    private ObjectFactory<BuyGiftNumCondition> buyGiftNumConditionObjectFactory;
    @Autowired
    private ObjectFactory<StorePriceCondition> storePriceConditionObjectFactory;
    @Autowired
    private ObjectFactory<RenewReduceCondition> renewReduceConditionObjectFactory;
    @Autowired
    private ObjectFactory<FrequencyForProtectPriceCondition> frequencyForProtectPriceConditionObjectFactory;
    @Autowired
    private ObjectFactory<BuyReduceCondition> buyReduceConditionObjectFactory;
    @Autowired
    private ObjectFactory<GiftCondition> giftConditionObjectFactory;
    @Autowired
    private ObjectFactory<ChannelCondition> channelConditionObjectFactory;
    @Autowired
    private ObjectFactory<ActPersonStoreJoinLimitCondition> actPersonStoreJoinLimitConditionObjectFactory;
    @Autowired
    private ObjectFactory<PartOnsaleCondition> partOnsaleConditionObjectFactory;
    @Autowired
    private ObjectFactory<BusinessCondition> businessConditionObjectFactory;
    @Autowired
    private ObjectFactory<FmemberCondition> fmemberConditionObjectFactory;
    @Autowired
    private ObjectFactory<StepPriceCondition> stepPriceConditionObjectFactory;
    @Autowired
    private ObjectFactory<CrmChannelPriceCondition> crmChannelPriceConditionObjectFactory;
    @Autowired
    private ObjectFactory<B2tVipDiscountCondition> b2tVipDiscountConditionObjectFactory;

    @Autowired
    private ObjectFactory<CarOnsaleCondition> conditionObjectFactory;
    @Autowired
    private ObjectFactory<CarRangeReduceCondition> carRangeReduceConditionObjectFactory;
    @Autowired
    private ObjectFactory<CarExchangeSubsidyCondition> CarExchangeSubsidyConditionObjectFactory;
    @Autowired
    private ObjectFactory<CarOrderReduceCondition> carOrderReduceConditionObjectFactory;

    @Autowired
    private ObjectFactory<CarShopBuyGiftCondition> carShopBuyGiftConditionObjectFactory;
    @Autowired
    private ObjectFactory<NewPurchaseSubsidyCondition> newPurchaseSubsidyConditionObjectFactory;
    @Autowired
    private ObjectFactory<UpgradePurchaseSubsidyCondition> upgradePurchaseSubsidyConditionObjectFactory;
    @Autowired
    private ObjectFactory<CrowdCondition> crowdConditionObjectFactory;
    @Autowired
    private ObjectFactory<GovernmentSubsidyCondition> governmentSubsidyConditionObjectFactory;

    @Autowired
    private ObjectFactory<CarShopVipCondition> carShopVipConditionObjectFactory;

    @Autowired
    private ObjectFactory<MaintenanceDiscountCondition> maintenanceDiscountConditionObjectFactory;

    @Autowired
    private ObjectFactory<MaintenanceItemFreeCondition> maintenanceItemFreeConditionObjectFactory;

    public Condition getCondition(Class<? extends Condition> clz) throws BizError {
        if (clz == QuotaCondition.class) {
            return quotaConditionObjectFactory.getObject();
        } else if (clz == ClientCondition.class) {
            return clientConditionObjectFactory.getObject();
        } else if (clz == DailyTimeCondition.class) {
            return dailyTimeConditionObjectFactory.getObject();
        } else if (clz == FrequencyCondition.class) {
            return frequencyConditionObjectFactory.getObject();
        } else if (clz == GiftBargainCondition.class) {
            return giftBargainConditionObjectFactory.getObject();
        } else if (clz == OfflineJoinLimitCondition.class) {
            return offlineJoinLimitConditionObjectFactory.getObject();
        } else if (clz == OnlineJoinLimitCondition.class) {
            return onlineJoinLimitConditionObjectFactory.getObject();
        } else if (clz == OnsaleCondition.class) {
            return onsaleConditionObjectFactory.getObject();
        } else if (clz == OrgCondition.class) {
            return orgConditionObjectFactory.getObject();
        } else if (clz == UserGroupCondition.class) {
            return userGroupConditionObjectFactory.getObject();
        } else if (clz == BuyGiftBaseNumCondition.class) {
            return buyGiftBaseNumConditionObjectFactory.getObject();
        } else if (clz == BuyGiftNumCondition.class) {
            return buyGiftNumConditionObjectFactory.getObject();
        } else if (clz == StorePriceCondition.class) {
            return storePriceConditionObjectFactory.getObject();
        } else if (clz == FrequencyForProtectPriceCondition.class) {
            return frequencyForProtectPriceConditionObjectFactory.getObject();
        } else if (clz == RenewReduceCondition.class) {
            return renewReduceConditionObjectFactory.getObject();
        } else if (clz == BuyReduceCondition.class) {
            return buyReduceConditionObjectFactory.getObject();
        } else if (clz == GiftCondition.class) {
            return giftConditionObjectFactory.getObject();
        } else if (clz == ChannelCondition.class) {
            return channelConditionObjectFactory.getObject();
        } else if (clz == ActPersonStoreJoinLimitCondition.class) {
            return actPersonStoreJoinLimitConditionObjectFactory.getObject();
        }else if (clz == PartOnsaleCondition.class) {
            return partOnsaleConditionObjectFactory.getObject();
        } else if (clz == BusinessCondition.class) {
            return businessConditionObjectFactory.getObject();
        } else if (clz == FmemberCondition.class) {
            return fmemberConditionObjectFactory.getObject();
        } else if (clz == StepPriceCondition.class) {
            return stepPriceConditionObjectFactory.getObject();
        } else if (clz == CrmChannelPriceCondition.class) {
            return crmChannelPriceConditionObjectFactory.getObject();
        } else if (clz == B2tVipDiscountCondition.class) {
            return b2tVipDiscountConditionObjectFactory.getObject();
        } else if (clz == CarOnsaleCondition.class) {
            return conditionObjectFactory.getObject();
        } else if (clz == CarRangeReduceCondition.class) {
            return carRangeReduceConditionObjectFactory.getObject();
        } else if (clz == CarExchangeSubsidyCondition.class) {
            return CarExchangeSubsidyConditionObjectFactory.getObject();
        } else if (clz == CarOrderReduceCondition.class) {
            return carOrderReduceConditionObjectFactory.getObject();
        } else if (clz == CarShopBuyGiftCondition.class) {
            return carShopBuyGiftConditionObjectFactory.getObject();
        } else if (clz == CarShopOnsaleCondition.class) {
            return carShopOnsaleConditionObjectFactory.getObject();
        } else if (clz == NewPurchaseSubsidyCondition.class) {
            return newPurchaseSubsidyConditionObjectFactory.getObject();
        } else if (clz == UpgradePurchaseSubsidyCondition.class) {
            return upgradePurchaseSubsidyConditionObjectFactory.getObject();
        } else if (clz == CrowdCondition.class) {
            return crowdConditionObjectFactory.getObject();
        } else if (clz == GovernmentSubsidyCondition.class) {
            return governmentSubsidyConditionObjectFactory.getObject();
        } else if (clz == CarShopVipCondition.class) {
            return carShopVipConditionObjectFactory.getObject();
        } else if (clz == MaintenanceDiscountCondition.class) {
            return maintenanceDiscountConditionObjectFactory.getObject();
        } else if (clz == MaintenanceItemFreeCondition.class) {
            return maintenanceItemFreeConditionObjectFactory.getObject();
        }
        throw ExceptionHelper.create(GeneralCodes.NotFound, String.format("Condition %s not Found", clz.getName()));
    }
}
