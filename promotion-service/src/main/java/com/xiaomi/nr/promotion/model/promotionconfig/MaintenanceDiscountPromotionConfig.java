package com.xiaomi.nr.promotion.model.promotionconfig;

import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import lombok.ToString;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 售后维保 --- 售后折扣活动
 */
@ToString
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class MaintenanceDiscountPromotionConfig extends MultiPromotionConfig {

    /**
     * 优惠车辆身份：赛道尊享会员(SVIP)，赛道版车主
     */
    private Integer carIdentityType;

    /**
     * 会员ID、购车权益ID
     */
    private Integer carIdentityId;

    /**
     * 折扣阶梯
     */
    private List<QuotaLevel> levelList;

    /**
     * 支持工单类型 @WorkOrderTypeEnum
     */
    private Set<Integer> supportWorkOrderTypes;

    /**
     * 商品黑名单
     */
    private List<Long> invalidGoods;

    /**
     * 用户参与次数限制
     */
    private Integer userJoinNumLimit;

    /**
     * 活动说明
     */
    private String description;



    public Integer getCarIdentityType() {
        return carIdentityType;
    }

    public void setCarIdentityType(Integer carIdentityType) {
        this.carIdentityType = carIdentityType;
    }

    public Integer getCarIdentityId() {
        return carIdentityId;
    }

    public void setCarIdentityId(Integer carIdentityId) {
        this.carIdentityId = carIdentityId;
    }

    public List<QuotaLevel> getLevelList() {
        return levelList;
    }

    public void setLevelList(List<QuotaLevel> levelList) {
        this.levelList = levelList;
    }

    public Set<Integer> getSupportWorkOrderTypes() {
        return supportWorkOrderTypes;
    }

    public void setSupportWorkOrderTypes(Set<Integer> supportWorkOrderTypes) {
        this.supportWorkOrderTypes = supportWorkOrderTypes;
    }

    public Integer getUserJoinNumLimit() {
        return userJoinNumLimit;
    }

    public void setUserJoinNumLimit(Integer userJoinNumLimit) {
        this.userJoinNumLimit = userJoinNumLimit;
    }

    public List<Long> getInvalidGoods() {
        return invalidGoods;
    }

    public void setInvalidGoods(List<Long> invalidGoods) {
        this.invalidGoods = invalidGoods;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
