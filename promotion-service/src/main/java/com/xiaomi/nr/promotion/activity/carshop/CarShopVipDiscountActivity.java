package com.xiaomi.nr.promotion.activity.carshop;

import com.xiaomi.nr.promotion.activity.AbstractActivityTool;
import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.MultiProductGoodsActRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.componet.action.carshop.CarShopVipDiscountAction;
import com.xiaomi.nr.promotion.componet.condition.carshop.CarShopVipCondition;
import com.xiaomi.nr.promotion.constant.PromotionTextConstant;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.ProductDetailContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.CarShopVipDiscountPromotionConfig;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 折扣活动工具
 *
 * <AUTHOR>
 * @date 2021/4/9
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class CarShopVipDiscountActivity extends AbstractActivityTool implements ActivityTool {


    private Long promotionId;

    /**
     * 身份限制
     */
    private Boolean identityLimit;

    /**
     * 会员ID
     */
    private Integer vipLevel;

    /**
     * 条件阶梯
     */
    private QuotaLevel quotaLevel;

    /**
     * 参与商品信息
     */
    private Set<Long> joinGoods;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine
                .condition(CarShopVipCondition.class)
                .action(CarShopVipDiscountAction.class);
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.CAR_SHOP_VIP;
    }

    /**
     * 构建购物车优惠数据
     *
     * @param context 上下文
     * @return 内容
     */
    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) {
        Integer frequencyVal = frequency != null ? frequency.getValue() : null;

        PromotionInfo promotionInfo = buildDefaultPromotionInfo(context);
        promotionInfo.setFrequent(frequencyVal);
        promotionInfo.setTotalLimitNum(actLimitNum);
        return promotionInfo;
    }

    @Override
    public List<CheckGoodsItem> checkProductsAct(CheckProductsActRequest request, Map<String, GoodsHierarchy> goodsHierarchyMap) throws BizError {
        return null;
    }


    @Override
    public PromotionInfoDTO getMultiProductAct(MultiGoodItem goodItem, MultiProductGoodsActRequest request, ProductDetailContext context) throws BizError {
        if (this.identityLimit) {
            Integer vipLevel = request.getVipLevel();
            if (vipLevel == null || !Objects.equals(this.vipLevel, vipLevel)) {
                log.debug("condition is not satisfied. user is not match vipId. vipLevel:{} actId:{}", vipLevel, promotionId);
                return null;
            }
        }
        PromotionInfoDTO promotionInfo = initPromotionInfo();
        promotionInfo.setPromotionText(PromotionTextConstant.CAR_SHOP_VIP_DISCOUNT + "最高享" + quotaLevel.getReduceDiscount() + "折");
        if (joinGoods.contains(goodItem.getSsuId())) {
            long totalPrice = calculatePromotionPrice(goodItem);

            Map<Long, ProductPriceInfo> promotionPriceMap = context.getPromotionPriceMap();
            ProductPriceInfo productPriceInfo = promotionPriceMap.computeIfAbsent(goodItem.getSsuId(), k -> new ProductPriceInfo());
            productPriceInfo.setActPrice(totalPrice);

            promotionInfo.setPromotionPrice(totalPrice);
        }
        return promotionInfo;
    }

    private long calculatePromotionPrice(MultiGoodItem goodItem) {
        long totalPrice = Optional.ofNullable(goodItem.getMarketPrice()).orElse(0L);
        // 计算实际总折扣金额
        long reduceDiscount = quotaLevel.getReduceDiscount();
        long maxReducePrice = quotaLevel.getMaxReducePrice();
        //打折后的价格
        long newPrice = totalPrice * reduceDiscount / 100;
        long reduceMoney = totalPrice - newPrice;

        //最高可以减免的钱
        if (maxReducePrice > 0) {
            reduceMoney = Math.min(maxReducePrice, reduceMoney);
        }
        return totalPrice - reduceMoney;
    }


    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof CarShopVipDiscountPromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        CarShopVipDiscountPromotionConfig promotionConfig = (CarShopVipDiscountPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.vipLevel = promotionConfig.getVipLevel();
        List<Long> ssuIdList = Optional.ofNullable(promotionConfig.getJoinGoods())
                .map(CompareItem::getSsuId)
                .orElse(Collections.emptyList());
        this.joinGoods = new HashSet<>(ssuIdList);
        List<QuotaLevel> levelList = promotionConfig.getLevelList();
        this.quotaLevel = CollectionUtils.isNotEmpty(levelList) ? levelList.getFirst() : null;
        this.identityLimit = true;

        return true;
    }

    @Override
    public ActivityDetail getActivityDetail() {
        return null;
    }

    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        return Map.of();
    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR_SHOP;
    }
}
