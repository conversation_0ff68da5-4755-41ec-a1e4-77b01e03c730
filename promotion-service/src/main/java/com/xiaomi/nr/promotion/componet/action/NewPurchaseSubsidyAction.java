package com.xiaomi.nr.promotion.componet.action;

import com.xiaomi.nr.phoenix.api.dto.response.tradeInfo.TradeQualificationInfo;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.ReduceBearerEnum;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.domain.subsidyactivity.model.TradeInQualifyInfo;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.NewPurchaseSubsidyPromotionConfig;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.IdKeyHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class NewPurchaseSubsidyAction extends AbstractAction {

    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 优惠类型
     */
    private PromotionToolType promotionType;
    /**
     * 折扣打的折扣，9折就是90
     */
    private long reduceDiscount = 0;
    /**
     * 折扣最高可以减免的钱
     */
    private long maxReducePrice = 0;
    /**
     * 承担方分摊比例
     */
    private Map<Integer, Integer> bearerShareRatioMap;
    /**
     * sku-品类
     */
    private Map<String, String> goodsSpuGroupMap;
    /**
     * 身份证限购总量
     */
    private Integer limitTotal;

    /**
     * 品类限购最大数量
     */
    private Integer limitGroup;

    /**
     * 预售定金
     */
    public static final String TAILORDER = "tailorder";

    @Autowired
    private CheckoutCartTool checkoutCartTool;

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (MapUtil.isEmpty(goodsSpuGroupMap)) {
            log.warn("goodsSpuGroupMap is empty. actId:{} uid:{}", promotion, request.getUserId());
            return;
        }
        List<CartItem> cartItemList = request.getSourceApi() == SourceApi.SUBMIT ? context.getCarts() : request.getCartList();
        // 获取此次参加活动的商品
        List<GoodsIndex> indexList = context.getGoodIndex();
        // 改价，并且获取参与活动的itemId
        List<String> joinedItemIdList = changePriceAndGetJoinedItemIdList(request, cartItemList, indexList, context.getUsedQualifyMap());

        if (joinedItemIdList.isEmpty()) {
            return;
        }
        // 构造promotionInfo
        PromotionInfo promotionInfo = new PromotionInfo();
        promotionInfo.setPromotionId(promotionId.toString());
        promotionInfo.setType(String.valueOf(promotionType.getTypeId()));
        promotionInfo.setJoinedItemId(joinedItemIdList);
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());

        // 初始化资源
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            // 门店剔除强绑定商品中的子品，只按主品进行限购
            /*if (!StringUtils.isEmpty(request.getOrgCode())) {
                joinedItemIdList = filterOrgJoinedItemId(cartItemList, joinedItemIdList);
            }*/
            List<TradeInQualifyInfo> tradeInQualifyInfoList = initLimitResource(joinedItemIdList, cartItemList, context.getUsedQualifyMap());
            Map<String, TradeQualificationInfo> qualificationInfoMap = tradeInQualifyInfoList.stream()
                    .collect(Collectors.toMap(TradeInQualifyInfo::getItemId, TradeInQualifyInfo::getQualifyDetailInfo));

            context.setCartItemQualifyMap(qualificationInfoMap);

            log.info("newPurchaseSubsidyLimitInfo tradeInQualifyInfoList:{}", tradeInQualifyInfoList);
            initializePurchaseSubsidyLimit(request, promotionId, context, tradeInQualifyInfoList);

        }
    }

    private List<String> filterOrgJoinedItemId(List<CartItem> cartItemList, List<String> joinedItemIdList) {
        List<String> filterJoinedItemIdList = new ArrayList<>();
        Map<String, CartItem> cartItemMap = cartItemList.stream().collect(Collectors.toMap(CartItem::getItemId, p -> p, (p1, p2) -> p1));
        for (String joinedItemId : joinedItemIdList) {
            CartItem cartItem = cartItemMap.get(joinedItemId);
            if (cartItem == null) {
                continue;
            }
            // source=bind && parentItemId不为空时，证明为子品。子品不参与资源扣减
            if (SourceEnum.isBind(cartItem.getSource()) && !StringUtils.isEmpty(cartItem.getParentItemId())) {
                continue;
            }
            filterJoinedItemIdList.add(joinedItemId);
        }
        return filterJoinedItemIdList;
    }


    private List<TradeInQualifyInfo> initLimitResource(List<String> satisfyItemIdList, List<CartItem> cartList, Map<String, TradeQualificationInfo> qualificationInfoMap) {
        List<TradeInQualifyInfo> tradeInQualifyInfoList = new ArrayList<>();
        Map<String, CartItem> cartItemMap = cartList.stream().collect(Collectors.toMap(CartItem::getItemId, p -> p, (p1, p2) -> p1));
        for (String itemId : satisfyItemIdList) {
            CartItem cartItem = cartItemMap.get(itemId);
            if (cartItem == null) {
                continue;
            }
            String skuPackage = CartHelper.getSkuPackage(cartItem);
            String spuGroup = goodsSpuGroupMap.get(skuPackage);
            if (StringUtils.isEmpty(spuGroup)) {
                continue;
            }
            TradeQualificationInfo qualificationInfo = qualificationInfoMap.get(spuGroup);
            if (qualificationInfo != null) {
                TradeInQualifyInfo tradeInQualifyInfo = new TradeInQualifyInfo();
                tradeInQualifyInfo.setSku(skuPackage);
                tradeInQualifyInfo.setItemId(itemId);
                tradeInQualifyInfo.setQualifyDetailInfo(qualificationInfo);
                tradeInQualifyInfoList.add(tradeInQualifyInfo);
            }
        }
        return tradeInQualifyInfoList;
    }

    private List<String> changePriceAndGetJoinedItemIdList(CheckoutPromotionRequest request, List<CartItem> cartItemList, List<GoodsIndex> indexList,Map<String, TradeQualificationInfo> qualificationInfoMap) {
        List<Pair<Long, String>> curPriceItemIdPairList = new ArrayList<>();
        // 门店强绑定商品单独处理
        List<CartItem> orgBindItemList = new ArrayList<>();
        for (CartItem cartItem : cartItemList) {
            boolean match = indexList.stream().anyMatch(item -> item.getItemId().equals(cartItem.getItemId()));
            if (!match) {
                continue;
            }
            String skuPackage = CartHelper.getSkuPackage(cartItem);
            String spuGroup = goodsSpuGroupMap.get(skuPackage);
            if (StringUtils.isEmpty(spuGroup)) {
                log.warn("goodsSpuGroupMap not contain skuPackage:{}. actId:{} uid:{}", skuPackage, promotionId, request.getUserId());
                continue;
            }

            TradeQualificationInfo qualificationInfo = qualificationInfoMap.get(spuGroup);
            if (qualificationInfo == null) {
                log.warn("qualificationInfoMap not contain skuPackage:{}. spuGroup:{} actId:{} uid:{}", skuPackage, spuGroup, promotionId, request.getUserId());
                continue;
            }
            // 当前优惠后的价格
            long curPrice = cartItem.getCartPrice() * cartItem.getCount() - cartItem.getReduceAmount();
            if (curPrice <= 0) {
                log.warn("promotion price error. skuPackage:{}. actId:{} uid:{} curPrice:{}", skuPackage, promotionId, request.getUserId(), curPrice);
                continue;
            }
            // 折扣后的价格
            long reducePrice = 0;
            if (cartItem.getSaleSource().equals(TAILORDER)) {
                reducePrice = calculateReduceMoney(curPrice + cartItem.getPrePrice() * cartItem.getCount(), reduceDiscount, maxReducePrice);
                if (reducePrice >= curPrice) {
                    continue;
                }
            } else {
                reducePrice = calculateReduceMoney(curPrice, reduceDiscount, maxReducePrice);
            }
            if (reducePrice == 0) {
                continue;
            }
            // 门店强绑定需要单独处理
            if (!StringUtils.isEmpty(request.getOrgCode()) && SourceEnum.isBind(cartItem.getSource())) {
                orgBindItemList.add(cartItem);
                continue;
            }
            // 更新优惠信息
            updateCartItemReduceInfo(cartItem, request, reducePrice, curPrice, curPriceItemIdPairList);
        }
        if (!orgBindItemList.isEmpty()) {
            // 处理门店强绑定
            changOrgBindPrice(orgBindItemList, request, curPriceItemIdPairList);
        }
        // 按照优惠后价格降序排列
        curPriceItemIdPairList.sort((o1, o2) -> o2.getKey().compareTo(o1.getKey()));

        List<String> priceItemIdList = new ArrayList<>();
        curPriceItemIdPairList.forEach(price -> priceItemIdList.add(price.getValue()));
        return priceItemIdList;
    }

    private void changOrgBindPrice(List<CartItem> orgBindItemList, CheckoutPromotionRequest request, List<Pair<Long, String>> curPriceItemIdPairList) {
        // 找到成对的强绑定商品
        List<Pair<CartItem, CartItem>> bindItemPairList = getBindItemPairList(orgBindItemList);
        for (Pair<CartItem, CartItem> pair : bindItemPairList) {
            CartItem parentItem = pair.getKey();
            CartItem childItem = pair.getValue();
            // 当前优惠后的价格
            long parentCurPrice = parentItem.getCartPrice() * parentItem.getCount() - parentItem.getReduceAmount();
            long childCurPrice = childItem.getCartPrice() * childItem.getCount() - childItem.getReduceAmount();
            long totalCurPrice = parentCurPrice + childCurPrice;
            // 折扣后的价格
            long totalReducePrice = calculateReduceMoney(totalCurPrice, reduceDiscount, maxReducePrice);
            // 分摊
            long parentReducePrice = Math.min(Math.floorDiv(totalReducePrice * parentCurPrice, totalCurPrice), parentCurPrice);
            long childReducePrice = totalReducePrice - parentReducePrice;
            // 更新父品优惠信息
            updateCartItemReduceInfo(parentItem, request, parentReducePrice, parentCurPrice, curPriceItemIdPairList);
            // 更新子品优惠信息
            updateCartItemReduceInfo(childItem, request, childReducePrice, childCurPrice, curPriceItemIdPairList);
        }
    }

    private List<Pair<CartItem, CartItem>> getBindItemPairList(List<CartItem> orgBindItemList) {
        List<Pair<CartItem, CartItem>> bindItemPairList = new ArrayList<>();
        List<CartItem> parentItemList = new ArrayList<>();
        Map<String, CartItem> parentItemIdCartItemMap = new HashMap<>();
        for (CartItem item : orgBindItemList) {
            if (StringUtils.isEmpty(item.getParentItemId())) {
                parentItemList.add(item);
            } else {
                parentItemIdCartItemMap.put(item.getParentItemId(), item);
            }
        }
        for (CartItem parentItem : parentItemList) {
            CartItem childItem = parentItemIdCartItemMap.get(parentItem.getItemId());
            if (childItem == null) {
                log.warn("getBindItemPairList error, parentItem not contain childItem, parentItem:{}", parentItem);
                continue;
            }
            bindItemPairList.add(Pair.of(parentItem, childItem));
        }
        return bindItemPairList;
    }


    private void updateCartItemReduceInfo(CartItem cartItem, CheckoutPromotionRequest request, long reducePrice, long curPrice, List<Pair<Long, String>> curPriceItemIdPairList) {
        String idKey = IdKeyHelper.getGeneralActIdKey(promotionId);
        // 更新购物车优惠扣减：reduceList/reduceDetailList/reduceAmount
        checkoutCartTool.updateCartsItemReduce(cartItem, reducePrice, idKey, promotionType.getTypeId(), promotionId);
        Integer xiaomiShareRatio = bearerShareRatioMap.get(ReduceBearerEnum.XIAOMI.getCode());
        if (xiaomiShareRatio == null) {
            log.warn("promotion xiaomiShareRatio is null. actId:{} uid:{} ", promotionId, request.getUserId());
            return;
        }
        updateReduceShareMap(request, reducePrice, xiaomiShareRatio, cartItem, idKey);
        curPriceItemIdPairList.add(Pair.of(curPrice, cartItem.getItemId()));
    }

    private void updateReduceShareMap(CheckoutPromotionRequest request, long reducePrice, Integer xiaomiShareRatio, CartItem cartItem, String idKey) {
        // 小米承担优惠金额
        long xiaomiSharePrice = reducePrice * xiaomiShareRatio.longValue() / 100;
        // 北京政府承担优惠金额
        long governmentBjSharePrice = reducePrice - xiaomiSharePrice;
        // 更新承担方分摊reduceShareMap
        Map<String, List<ReduceShareInfo>> reduceShareMap = Optional.ofNullable(cartItem.getReduceShareMap()).orElse(new HashMap<>());
        cartItem.setReduceShareMap(reduceShareMap);
        List<ReduceShareInfo> reduceShareList = reduceShareMap.get(idKey);
        if (reduceShareList == null) {
            reduceShareList = new ArrayList<>();
        }
        ReduceShareInfo xiaomiShareInfo = new ReduceShareInfo();
        xiaomiShareInfo.setBearer(ReduceBearerEnum.XIAOMI.getCode());
        xiaomiShareInfo.setSharePrice(xiaomiSharePrice);
        xiaomiShareInfo.setBearerShareRatio(xiaomiShareRatio);
        reduceShareList.add(xiaomiShareInfo);
        ReduceShareInfo governmentBjShareInfo = new ReduceShareInfo();
        governmentBjShareInfo.setBearer(ReduceBearerEnum.GOVERNMENT_BJ.getCode());
        governmentBjShareInfo.setSharePrice(governmentBjSharePrice);
        governmentBjShareInfo.setBearerShareRatio(100 - xiaomiShareRatio);
        reduceShareList.add(governmentBjShareInfo);
        reduceShareMap.put(idKey, reduceShareList);
        // 门店场景，将reduceShareList更新到reduceDetailList中，为后续套装场景下的分摊做准备
        if (!StringUtils.isEmpty(request.getOrgCode())){
            Map<String, List<ReduceDetail>> reduceDetailMap = cartItem.getReduceDetailList();
            List<ReduceDetail> reduceDetailList = reduceDetailMap.get(PromotionConstant.NEW_PURCHASE_SUBSIDY_KEY);
            if (reduceDetailList!=null&&!reduceDetailList.isEmpty()){
                ReduceDetail reduceDetail = reduceDetailList.get(0);
                reduceDetail.setReduceShareList(reduceShareList);
            }
        }

    }


    private long calculateReduceMoney(long totalPrice, long reduceDiscount, long maxReducePrice) {
        //打折后的价格, 改为四舍五入方式
        //long reducePrice = totalPrice * reduceDiscount / 100;
        long reducePrice = Math.round((double) totalPrice * reduceDiscount / 100);

        reducePrice = Math.min(reducePrice, totalPrice);
        //最高可以减免的钱
        if (maxReducePrice > 0) {
            reducePrice = Math.min(maxReducePrice, reducePrice);
        }
        return reducePrice;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof NewPurchaseSubsidyPromotionConfig promotionConfig)) {
            log.error("config is not instanceof NewPurchaseSubsidyPromotionConfig. class:{}", config.getName());
            return;
        }
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        this.reduceDiscount = promotionConfig.getReduceDiscount();
        this.maxReducePrice = promotionConfig.getMaxReducePrice();
        this.bearerShareRatioMap = promotionConfig.getBearerShareRatioMap();
        this.goodsSpuGroupMap = promotionConfig.getGoodsSpuGroupMap();
        this.limitTotal = promotionConfig.getLimitTotal();
        this.limitGroup = promotionConfig.getLimitGroup();
    }
}
