package com.xiaomi.nr.promotion.disruptor;

import com.google.common.base.Stopwatch;
import com.lmax.disruptor.EventFactory;
import com.lmax.disruptor.YieldingWaitStrategy;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import com.xiaomi.nr.promotion.mq.producer.ActStockProducer;
import com.xiaomi.nr.promotion.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 活动库存Disruptor
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActStockManager {
    private Disruptor<StockEvent> disruptor;
    @Autowired
    private ActStockProducer actStockProducer;

    public ActStockManager() {
    }

    /**
     * 发布记录
     *
     * @param supplier 记录
     */
    public void publishEvent(Supplier<StockRecord> supplier) {
        try {
            this.disruptor.publishEvent((stockEvent, sequence) ->
                    stockEvent.setStockRecord(supplier.get()));
        } catch (Throwable var3) {
            System.err.println(var3.getMessage());
        }
    }

    /**
     * 数据启动
     */
    @PostConstruct
    public void startStockDisruptor() {
        start((stockRecord) -> {
            if (stockRecord == null) {
                return;
            }
            Stopwatch stopwatch = Stopwatch.createStarted();
            try {
                actStockProducer.sendMessage(stockRecord);
            } catch (Exception e) {
                log.error("sendMessage 请求失败 耗时:{}, 参数:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS), GsonUtil.toJson(stockRecord), e);
            }
        });
    }

    /**
     * 开始进行数据消费
     *
     * @param consumer 消费者
     *
     * @return 是否消费成功
     */
    private boolean start(Consumer<StockRecord> consumer) {
        try {
            EventFactory<StockEvent> eventFactory = StockEvent::new;
            int ringBufferSize = 1048576;
            this.disruptor = new Disruptor(eventFactory, ringBufferSize, Executors.defaultThreadFactory(), ProducerType.MULTI, new YieldingWaitStrategy());
            this.disruptor.handleEventsWith(new StockEventHandler(consumer));
            this.disruptor.start();
            return true;
        } catch (Throwable var5) {
            System.err.println(var5.getMessage());
            return false;
        }
    }
}
