package com.xiaomi.nr.promotion.util;

import com.xiaomi.nr.promotion.config.NacosConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

/**
 * Redis集群切换公共类
 *
 * <AUTHOR>
 * @date 2023/11/09 18:34
 **/
@Component
@Slf4j
public class RedisClusterAutoSwitchHelper {

    @Autowired
    private NacosConfig nacosConfig;

    /**
     * 缓存操作对象-老
     */
    @Autowired
    @Qualifier("stringPromotionRedisTemplate")
    private RedisTemplate<String, String> redisOriginTemplate;

    /**
     * 缓存操作对象-新
     */
    @Autowired
    @Qualifier("stringPromotionFinalRedisTemplate")
    private RedisTemplate<String, String> redisFinalTemplate;

    public ValueOperations<String, String> getOperations() {
        // nacos获取集群切换开关
        if (nacosConfig.isClusterSwitch()) {
            // 新集群
            log.info("RedisClusterAutoSwitchHelper.getOperations: This is final redis cluster");
            return redisFinalTemplate.opsForValue();
        }
        // 老集群
        log.info("RedisClusterAutoSwitchHelper.getOperations: This is origin redis cluster");
        return redisOriginTemplate.opsForValue();
    }

    public RedisTemplate<String, String> getRedisTemplate() {
        if (nacosConfig.isClusterSwitch()) {
            // 新集群
            log.info("RedisClusterAutoSwitchHelper.getRedisTemplate: This is final redis cluster");
            return redisFinalTemplate;
        }
        // 老集群
        log.info("RedisClusterAutoSwitchHelper.getRedisTemplate: This is origin redis cluster");
        return redisOriginTemplate;
    }
}
