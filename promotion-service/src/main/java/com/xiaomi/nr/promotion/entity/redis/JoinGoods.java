package com.xiaomi.nr.promotion.entity.redis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 能参与的主商品
 *
 * <AUTHOR>
 * @date 2021/4/8
 */
@Data
public class JoinGoods implements Serializable {
    private static final long serialVersionUID = -5485152479319134989L;

    /**
     * sku
     */
    private List<String> sku;

    /**
     * goods
     */
    private List<String> goods;

    /**
     * cat
     */
    private List<String> cat;

    /**
     * Product
     */
    private List<String> product;

    /**
     * Commodity
     */
    private List<String> commodity;

    /**
     * Group
     */
    private List<String> group;

    /**
     * 1 代表全场
     */
    private Integer all = 0;

    /**
     * 套装
     */
    @SerializedName("package")
    private List<String> packages;

    /**
     * 活动的修改时间
     */
    @SerializedName("modify_index")
    private Long modifyIndex;
}
