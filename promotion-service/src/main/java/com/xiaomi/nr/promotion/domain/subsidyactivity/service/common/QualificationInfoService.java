package com.xiaomi.nr.promotion.domain.subsidyactivity.service.common;

import com.xiaomi.nr.phoenix.api.dto.response.tradeInfo.TradeInfoQueryListResp;
import org.springframework.util.concurrent.ListenableFuture;

/**
 * 礼品卡信息服务接口
 *
 * <AUTHOR>
 * @date 2021/6/17
 */
public interface QualificationInfoService {
    /**
     * 异步获取三方优惠资格码列表信息
     *
     * @param userId   用户ID
     * @return 三方优惠资格码列表
     */
    ListenableFuture<TradeInfoQueryListResp> getQualificationInfoAsync(Long userId);

}
