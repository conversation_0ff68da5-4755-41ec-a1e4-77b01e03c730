package com.xiaomi.nr.promotion.model.promotionconfig;

import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import lombok.ToString;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 包邮活动配置
 *
 * <AUTHOR>
 * @date 2021/5/31
 */
@ToString
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class PostFreePromotionConfig extends MultiPromotionConfig {
    /**
     * 包邮阶梯
     */
    private List<QuotaLevel> levelList;

    public List<QuotaLevel> getLevelList() {
        return levelList;
    }

    public void setLevelList(List<QuotaLevel> levelList) {
        this.levelList = levelList;
    }
}
