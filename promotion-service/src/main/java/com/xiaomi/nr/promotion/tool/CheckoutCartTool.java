package com.xiaomi.nr.promotion.tool;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import com.xiaomi.nr.promotion.enums.PhoenixTypeEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.common.StatisticsSimple;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.stereotype.Component;

import java.util.*;


/**
 * 结算处理购物车工具
 * - 购物车优惠均摊 divideCartsReduce
 * - 订单层面分摊   divideOrderCarts
 *
 * <AUTHOR>
 * @date 2021/6/9
 */
@Slf4j
@Component
public class CheckoutCartTool {

    /**
     * 对商品进行优惠分摊
     */
    public void divideCartsReduceBySsu(long totalReduceMoney, List<CartItem> fillCartList, String idKey, int promotionType, Long promotionId, String budgetApplyNo, Long lineNum) {
        // 计算所有商品的当前总价（去掉前置优惠）
        long originalTotal = CartHelper.getTotalPriceBySsu(fillCartList);

        // 购物车金额为0， 没有可均摊的
        if (originalTotal == 0L) {
            log.warn("CheckoutCartTool divideCartsReduceBySsu carts originalTotal is 0. goods:{} promotionId:{}", fillCartList, promotionId);
            return;
        }

        // 分摊到各个商品, 返回实际分摊总额
        long totalActualReduce = updateCartsReduceBySsu(fillCartList, totalReduceMoney, originalTotal, idKey, promotionType, promotionId, budgetApplyNo, lineNum);

        // 处理没分摊完的部分
        long diffReduce = totalReduceMoney - totalActualReduce;
        if (diffReduce == 0L) {
            return;
        }
        updateCartsReduceDiffBySsu(fillCartList, diffReduce, idKey, promotionType, promotionId, budgetApplyNo, lineNum);
    }

    /**
     * 更新商品的均摊金额
     */
    private long updateCartsReduceBySsu(List<CartItem> fillCartList, long totalReduceMoney, long originalTotal, String idKey, int promotionType, Long promotionId, String budgetApplyNo, Long lineNum) {
        long totalActualReduce = 0L;
        for (CartItem cartItem : fillCartList) {
            long curPrice = CartHelper.itemCurPrice(cartItem.getOriginalCartPrice(), cartItem.getReduceItemList()) * cartItem.getCount();//该商品的当前总价（去掉前置优惠）
            long reduceMoney = Math.floorDiv(curPrice * totalReduceMoney, originalTotal);//该券可分摊到该商品的金额
            if (reduceMoney > 0L) {
                //新优惠分摊字段
                if (CartHelper.isPackage(cartItem)) {
                    updateCartsSsuPackageReduce(cartItem, reduceMoney, promotionType, promotionId, budgetApplyNo, lineNum);
                } else {
                    updateCartsSsuReduce(cartItem, reduceMoney, promotionType, promotionId, budgetApplyNo, lineNum);
                }
                //老优惠分摊字段
//                updateCartsItemReduce(cartItem, reduceMoney, idKey, promotionType, promotionId);
            }
            totalActualReduce += reduceMoney;
        }
        return totalActualReduce;
    }

    /**
     * 更新商品的均摊金额到reduceItemList列表中
     */
    public void updateCartsSsuReduce(CartItem item, long reduceMoney, int promotionType, Long promotionId, String budgetApplyNo, Long lineNum) {
        Long reduceSingle = reduceMoney / item.getCount();
        List<ReduceDetailItem> reduceDetailItems = Optional.ofNullable(item.getReduceItemList()).orElseGet(() -> new ArrayList<>());
        boolean isExist = false;
        for (ReduceDetailItem reduceDetailItem : reduceDetailItems) {
            if (reduceDetailItem.getPromotionId().equals(promotionId) && reduceDetailItem.getSsuId().equals(item.getSsuId())) {
                reduceDetailItem.setReduce(reduceDetailItem.getReduce() + reduceMoney);
                reduceDetailItem.setReduceSingle(reduceDetailItem.getReduceSingle() + reduceSingle);
                isExist = true;
                break;
            }
        }
        if (!isExist) {
            ReduceDetailItem detailItem = new ReduceDetailItem()
                    .setPromotionId(promotionId)
                    .setPromotionType(promotionType)
                    .setSsuId(item.getSsuId())
                    .setReduce(reduceMoney)
                    .setReduceSingle(reduceSingle)
                    .setBudgetApplyNo(budgetApplyNo)
                    .setLineNum(lineNum);
            reduceDetailItems.add(detailItem);
        }

    }

    /**
     * 更新购物车中套装子商品的优惠分摊信息
     *
     * @param item          购物车项
     * @param reduceMoney   总优惠金额
     * @param promotionType 促销类型
     * @param promotionId   促销ID
     * @param budgetApplyNo 预算申请编号
     * @param lineNum       行号
     */
    public void updateCartsSsuPackageReduce(CartItem item, long reduceMoney, int promotionType, Long promotionId, String budgetApplyNo, Long lineNum) {
        List<ReduceDetailItem> reduceDetailItems = Optional.ofNullable(item.getReduceItemList()).orElseGet(ArrayList::new);

        long ratioTotalReduceMoney = 0;
        long curPrice = CartHelper.itemCheckoutAmount(item);
        long reducedPrice = curPrice - reduceMoney;

        // 计算子商品的当前价格并存储在Map中
        Map<Long, Long> childCurPriceMap = new HashMap<>();
        for (CartItemChild child : item.getChilds()) {
            childCurPriceMap.put(child.getSsuId(), CartHelper.itemChildCheckoutAmount(item, child));
        }

        // 按照当前价格对子商品进行排序
        List<CartItemChild> sortedChildren = item.getChilds();
        sortedChildren.sort((child1, child2) -> Long.compare(childCurPriceMap.get(child2.getSsuId()), childCurPriceMap.get(child1.getSsuId())));

        // 对子品的优惠详情按照ssuId进行分类
        Map<Long, ReduceDetailItem> reduceDetailItemMap = new HashMap<>();
        for (ReduceDetailItem detailItem : reduceDetailItems) {
            if (Objects.equals(promotionId, detailItem.getPromotionId()) && Objects.equals(promotionType, detailItem.getPromotionType())) {
                reduceDetailItemMap.put(detailItem.getSsuId(), detailItem);
            }
        }

        // 计算每个子商品的分摊优惠
        for (int i = 0; i < sortedChildren.size() - 1; i++) {
            if (ratioTotalReduceMoney >= reduceMoney) {
                break;
            }
            CartItemChild child = sortedChildren.get(i);
            long itemChildAmount = childCurPriceMap.get(child.getSsuId());
            long subMoney = itemChildAmount - ((itemChildAmount * reducedPrice) / curPrice);
            long reduceSingle = subMoney / child.getCount();

            ReduceDetailItem reduceDetailItem = reduceDetailItemMap.get(child.getSsuId());
            if (reduceDetailItem == null) {
                reduceDetailItem = new ReduceDetailItem()
                        .setPromotionId(promotionId)
                        .setPromotionType(promotionType)
                        .setSsuId(child.getSsuId())
                        .setReduce(subMoney)
                        .setReduceSingle(reduceSingle)
                        .setBudgetApplyNo(budgetApplyNo)
                        .setLineNum(lineNum);
                reduceDetailItems.add(reduceDetailItem);
                reduceDetailItemMap.put(child.getSsuId(), reduceDetailItem);
            } else {
                reduceDetailItem.setReduce(reduceDetailItem.getReduce() + subMoney);
                reduceDetailItem.setReduceSingle(reduceDetailItem.getReduceSingle() + reduceSingle);
            }
            ratioTotalReduceMoney += subMoney;
        }

        long lastChildReduce = reduceMoney - ratioTotalReduceMoney;
        if (lastChildReduce > 0) {
            // 计算最后一个子商品的分摊优惠
            CartItemChild lastChild = sortedChildren.getLast();
            ReduceDetailItem lastDetailItem = new ReduceDetailItem()
                    .setPromotionId(promotionId)
                    .setPromotionType(promotionType)
                    .setSsuId(lastChild.getSsuId())
                    .setReduce(lastChildReduce)
                    .setReduceSingle(lastChildReduce / lastChild.getCount())
                    .setBudgetApplyNo(budgetApplyNo)
                    .setLineNum(lineNum);
            reduceDetailItems.add(lastDetailItem);
        }
        // lastChildReduce < 0 说明前n-1个子品分摊多了,从第一个子品中扣减
        if (lastChildReduce < 0) {
            CartItemChild firstChild = sortedChildren.getFirst();
            ReduceDetailItem reduceDetailItem = reduceDetailItemMap.get(firstChild.getSsuId());
            if (reduceDetailItem != null) {
                reduceDetailItem.setReduce(reduceDetailItem.getReduce() + lastChildReduce);
                reduceDetailItem.setReduceSingle(reduceDetailItem.getReduce() / firstChild.getCount());
            } else {
                log.error("CheckoutCartTool updateCartsSsuPackageReduce error. lastChildReduce < 0 but reduceDetailItemMap not contains firstChild. lastChildReduce = {}, reduceDetailItemMap:{}", lastChildReduce, reduceDetailItemMap);
            }
        }
    }


    /**
     * 对于差额部分更新购物车均摊金额，执行逻辑一分钱进行迭代扣减
     */
    private void updateCartsReduceDiffBySsu(List<CartItem> fillCartList, long diffMoney, String idKey, int promotionType, Long promotionId, String budgetApplyNo, Long lineNum) {
        long eachDiff = 1L;
        for (CartItem cartItem : fillCartList) {
            if (diffMoney <= 0L) {
                return;
            }
            long curPrice = CartHelper.itemCurPrice(cartItem.getOriginalCartPrice(), cartItem.getReduceItemList()) * cartItem.getCount();
            if (curPrice <= 0L) {
                continue;
            }
            if (CartHelper.isPackage(cartItem)) {
                updateCartsSsuPackageReduce(cartItem, eachDiff, promotionType, promotionId, budgetApplyNo, lineNum);
            } else {
                //新优惠分摊字段
                updateCartsSsuReduce(cartItem, eachDiff, promotionType, promotionId, budgetApplyNo, lineNum);
            }
            //老优惠分摊字段
//            updateCartsItemReduce(cartItem, eachDiff, idKey, promotionType, promotionId);
            diffMoney -= eachDiff;
        }
        // 异常情况
        if (diffMoney > 0L) {
            log.error("CheckoutCartTool updateCartsReduceDiffBySsu after loop the diffMoney is not zero. goods:{} promotionId:{}", fillCartList, promotionId);
        }
    }

    /**
     * 优惠均摊. 对得到的减钱金额均摊到满足的商品中，详细记录每个活动优惠
     *
     * @param totalReduceMoney 总优惠金额
     * @param indexList        满足商品下标
     * @param cartList         购物车列表
     * @param idKey            reduceKey, 放入ReduceList中，活动为act_{actId} 券为coupon_{couponId}
     * @param promotionType    优惠类型
     * @param promotionId      优惠id
     */
    public void divideCartsReduce(long totalReduceMoney, List<Integer> indexList, List<CartItem> cartList, String idKey, int promotionType, Long promotionId) {
        // 符合购物车列表 并 计算总价格
        List<CartItem> fillCartList = CartHelper.getCartList(cartList, indexList);
        divideCartsReduce(totalReduceMoney, fillCartList, idKey, promotionType, promotionId);
    }


    /**
     * 优惠均摊. 对得到的减钱金额均摊到满足的商品中，详细记录每个活动优惠
     *
     * @param totalReduceMoney 总优惠金额
     * @param fillCartList     满足商品购物车列表
     * @param idKey            reduceKey, 放入ReduceList中，活动为act_{actId} 券为coupon_{couponId}
     * @param promotionType    优惠类型
     * @param promotionId      优惠id
     */
    public void divideCartsReduce(long totalReduceMoney, List<CartItem> fillCartList, String idKey, int promotionType, Long promotionId) {
        long originalTotal = CartHelper.getTotalPrice(fillCartList);
        // 购物车金额为0， 没有可均摊的
        if (originalTotal == 0L) {
            log.warn("carts originalTotal is 0. idKey:{}", idKey);
            return;
        }

        // 分摊到各个CartItem, 返回实际分摊总额
        long totalActualReduce = updateCartsReduce(fillCartList, totalReduceMoney, originalTotal, idKey, promotionType, promotionId);

        // 处理没分摊完的部分
        long diffReduce = totalReduceMoney - totalActualReduce;
        if (diffReduce == 0L) {
            return;
        }
        updateCartsReduceDiff(fillCartList, diffReduce, idKey, promotionType, promotionId);
    }

    /**
     * 把优惠分摊到一个商品上
     *
     * @param totalReduceMoney 总优惠金额
     * @param indexList        满足商品下标
     * @param cartList         购物车列表
     * @param idKey            reduceKey, 放入ReduceList中，活动为act_{actId} 券为coupon_{couponId}
     * @param promotionType    优惠类型
     * @param promotionId      优惠id
     */
    public void divideCartsReduceToSingleItem(long totalReduceMoney, List<Integer> indexList, List<CartItem> cartList, String idKey, int promotionType, Long promotionId) {
        // 符合购物车列表 并 计算总价格
        List<CartItem> fillCartList = CartHelper.getCartList(cartList, indexList);
        long originalTotal = CartHelper.getTotalPrice(fillCartList);
        // 购物车金额为0， 没有可均摊的
        if (originalTotal == 0L) {
            log.warn("carts originalTotal is 0. idKey:{}", idKey);
            return;
        }

        // 分摊一个CartItem, 返回实际分摊总额
        updateCartsReduceToSingleItem(fillCartList, totalReduceMoney, originalTotal, idKey, promotionType, promotionId);

    }

    /**
     * 更新购物车均摊金额
     *
     * @param fillCartList     满足条件购物车列表
     * @param totalReduceMoney 总减金额
     * @param originalTotal    购物车总金额
     * @param idKey            reduceKey
     * @param promotionType    优惠类型
     * @param promotionId      优惠id
     * @return
     */
    private long updateCartsReduceToSingleItem(List<CartItem> fillCartList, long totalReduceMoney, long originalTotal, String idKey, int promotionType, Long promotionId) {
        long totalActualReduce = 0L;
        CartItem singleItem = null;
        long maxPrice = 0;
        for (CartItem cartItem : fillCartList) {
            if (cartItem.getCartPrice() > maxPrice) {
                maxPrice = cartItem.getCartPrice();
                singleItem = cartItem;
            }
        }
        if (singleItem == null) {
            return 0;
        }

        if (totalReduceMoney > 0L) {
            updateCartsItemReduce(singleItem, totalReduceMoney, idKey, promotionType, promotionId);
        }
        totalActualReduce += totalReduceMoney;
        return totalActualReduce;
    }


    /**
     * 更新购物车均摊金额
     *
     * @param fillCartList     满足条件购物车列表
     * @param totalReduceMoney 总减金额
     * @param originalTotal    购物车总金额
     * @param idKey            reduceKey
     * @param promotionType    优惠类型
     * @param promotionId      优惠id
     * @return
     */
    private long updateCartsReduce(List<CartItem> fillCartList, long totalReduceMoney, long originalTotal, String idKey, int promotionType, Long promotionId) {
        long totalActualReduce = 0L;
        for (CartItem cartItem : fillCartList) {
            long curPrice = CartHelper.itemCurPrice(cartItem);
            long reduceMoney = Math.floorDiv(curPrice * totalReduceMoney, originalTotal);
            if (reduceMoney > 0L) {
                updateCartsItemReduce(cartItem, reduceMoney, idKey, promotionType, promotionId);
            }
            totalActualReduce += reduceMoney;
        }
        return totalActualReduce;
    }

    /**
     * 对于差额部分更新购物车均摊金额
     * 执行逻辑一分钱进行迭代扣减
     *
     * @param fillCartList  满足条件购物车列表
     * @param diffMoney     差额金额
     * @param idKey         reduceKey
     * @param promotionType 优惠类型
     * @param promotionId   优惠id
     */
    private void updateCartsReduceDiff(List<CartItem> fillCartList, long diffMoney, String idKey, int promotionType, Long promotionId) {
        long eachDiff = 1L;
        for (CartItem cartItem : fillCartList) {
            if (diffMoney <= 0L) {
                return;
            }
            long curPrice = CartHelper.itemCurPrice(cartItem);
            if (curPrice <= 0L) {
                continue;
            }
            updateCartsItemReduce(cartItem, eachDiff, idKey, promotionType, promotionId);
            diffMoney -= eachDiff;
        }
        // 异常情况
        if (diffMoney > 0L) {
            log.error("after loop the diffMoney is not zero. idKey:{} diffMoney:{}", idKey, diffMoney);
        }
    }

    /**
     * 更新购物车优惠扣减
     *
     * @param item          购物车项
     * @param reduceMoney   优惠金额
     * @param idKey         reduceKey
     * @param promotionType 优惠类型
     * @param promotionId   优惠id
     */
    public void updateCartsItemReduce(CartItem item, long reduceMoney, String idKey, int promotionType, Long promotionId) {
        if (item.getReduceList() == null) {
            item.setReduceList(Maps.newHashMap());
        }
        Map<String, Long> reduceList = item.getReduceList();
        // 相同扣减来源，做合并
        reduceList.merge(idKey, reduceMoney, Long::sum);
        item.setReduceAmount(item.getReduceAmount() + reduceMoney);

        Map<String, List<ReduceDetail>> reduceDetailList = item.getReduceDetailList();

        if (promotionType == ActivityTypeEnum.REDUCE.getValue()) {
            // 满减活动
            updateCartsItemReduceDetails(reduceDetailList, PromotionConstant.SHARE_ACTIVITY_REDUCE_KEY, promotionId, reduceMoney);
        } else if (promotionType == ActivityTypeEnum.DISCOUNT.getValue()) {
            // 满折活动
            updateCartsItemReduceDetails(reduceDetailList, PromotionConstant.SHARE_ACTIVITY_DISCOUNT_KEY, promotionId, reduceMoney);
        } else if (promotionType == ActivityTypeEnum.RENEW_REDUCE.getValue()) {
            // 换新立减活动
            updateCartsItemReduceDetails(reduceDetailList, PromotionConstant.SHARE_ACTIVITY_RENEWREDUCE_KEY, promotionId, reduceMoney);
        } else if (promotionType == ActivityTypeEnum.BUY_REDUCE.getValue()) {
            // 下单立减活动
            updateCartsItemReduceDetails(reduceDetailList, PromotionConstant.SHARE_ACTIVITY_BUYREDUCE_KEY, promotionId, reduceMoney);
        } else if (promotionType == ActivityTypeEnum.PARTONSALE.getValue()) {
            // 指定门店降价活动
            updateCartsItemReduceDetails(reduceDetailList, PromotionConstant.SHARE_ACTIVITY_PARTONSALE_KEY, promotionId, reduceMoney);
        } else if (promotionType == CouponTypeEnum.CASH.getType()) {
            // 满减劵
            updateCartsItemReduceDetails(reduceDetailList, PromotionConstant.SHARE_COUPON_REDUCE_KEY, promotionId, reduceMoney);
        } else if (promotionType == CouponTypeEnum.DISCOUNT.getType()) {
            // 满折劵
            updateCartsItemReduceDetails(reduceDetailList, PromotionConstant.SHARE_COUPON_DISCOUNT_KEY, promotionId, reduceMoney);
        } else if (promotionType == PhoenixTypeEnum.HUASHENG.getType()) {
            // 三方优惠-华盛联通
            updateCartsItemReduceDetails(reduceDetailList, PromotionConstant.SHARE_PHOENIX_HUASHENG_KEY, promotionId, reduceMoney);
        } else if (promotionType == PhoenixTypeEnum.UNICOM.getType()) {
            // 三方优惠-联通
            updateCartsItemReduceDetails(reduceDetailList, PromotionConstant.SHARE_PHOENIX_UNICOM_KEY, promotionId, reduceMoney);
        } else if (promotionType == PhoenixTypeEnum.TELECOM.getType()) {
            // 三方优惠-电信
            updateCartsItemReduceDetails(reduceDetailList, PromotionConstant.SHARE_PHOENIX_TELECOM_KEY, promotionId, reduceMoney);
        } else if (promotionType == PhoenixTypeEnum.MOBILE.getType()) {
            // 三方优惠-移动
            updateCartsItemReduceDetails(reduceDetailList, PromotionConstant.SHARE_PHOENIX_MOBILE_KEY, promotionId, reduceMoney);
        } else if (promotionType == PhoenixTypeEnum.RENEW_SUBSIDY.getType()) {
            // 三方优惠-米家以旧换新补贴
            updateCartsItemReduceDetails(reduceDetailList, PromotionConstant.SHARE_PHOENIX_RENEW_SUBSIDY_KEY, promotionId, reduceMoney);
        } else if (promotionType == PhoenixTypeEnum.RENEW_DEDUCT.getType()) {
            // 三方优惠-米家以旧换新补贴
            updateCartsItemReduceDetails(reduceDetailList, PromotionConstant.SHARE_PHOENIX_RENEW_DEDUCT_KEY, promotionId, reduceMoney);
        } else if (promotionType == ActivityTypeEnum.NEW_PURCHASE_SUBSIDY.getValue()) {
            // 购新补贴
            updateCartsItemReduceDetails(reduceDetailList, PromotionConstant.NEW_PURCHASE_SUBSIDY_KEY, promotionId, reduceMoney);
        } else if (promotionType == ActivityTypeEnum.UPGRADE_PURCHASE_SUBSIDY.getValue()) {
            // 换新补贴
            updateCartsItemReduceDetails(reduceDetailList, PromotionConstant.UPGRADE_PURCHASE_SUBSIDY_KEY, promotionId, reduceMoney);
        } else {
            log.warn("promotion type is invalid for update cartItem share reduce. promotion type:{}, id:{}", promotionType, promotionId);
        }
    }

    /**
     * 更新购物车中商品的减免详情
     *
     * @param reduceDetailList 减免详情的映射表
     * @param key              商品的键
     * @param promotionId      促销活动的ID
     * @param reduceAmount     减免的金额
     */
    private void updateCartsItemReduceDetails(Map<String, List<ReduceDetail>> reduceDetailList, String
            key, Long promotionId, long reduceAmount) {
        List<ReduceDetail> reduceDetails = reduceDetailList.getOrDefault(key, new ArrayList<>());
        boolean findPromotion = false;
        for (ReduceDetail reduceDetail : reduceDetails) {
            if (reduceDetail.getId().equals(promotionId)) {
                reduceDetail.setAmount(reduceDetail.getAmount() + reduceAmount);
                findPromotion = true;
                break;
            }
        }
        if (!findPromotion) {
            ReduceDetail reduceDetail = new ReduceDetail();
            reduceDetail.setId(promotionId);
            reduceDetail.setAmount(reduceAmount);
            reduceDetails.add(reduceDetail);
            reduceDetailList.put(key, reduceDetails);
        }
    }

    /**
     * 做购物车订单层面分摊，拆分成最细粒度，count=1 package-> sku
     *
     * @param cartList 购物车列表
     * @return 订单sku拆分列表
     * @throws BizError 业务异常
     */
    public List<OrderCartItem> divideOrderCarts(List<CartItem> cartList) throws BizError {
        if (CollectionUtils.isEmpty(cartList)) {
            return Collections.emptyList();
        }
        log.info("divideCarts start. before divide, cartList:{}", cartList);

        // 获取旧的统计数据
        StatisticsSimple statisticsOld = checkSimpleAndStatistics(cartList);
        Long totalPriceOld = statisticsOld.getTotalPrice();
        Long totalReduceOld = statisticsOld.getTotalReduce();

        // 获取购物车粒度下的列表（拆分为数量为1的购物车列表）
        List<CartItem> divideCartList = divideCartList(cartList);
        // 获取订单粒度（sku）下的列表
        List<OrderCartItem> orderItemList = divideCartItemListToOrderItemList(divideCartList);

        // 获取新的统计数据
        StatisticsSimple statisticsNew = checkOrderAndStatistics(orderItemList);
        Long totalPriceNew = statisticsNew.getTotalPrice();
        Long totalReduceNew = statisticsNew.getTotalReduce();

        // 注意 statistics后来又加了标准价,但没有校验,总的标准价仅为了最后统计 不需要校验
        if (!Objects.equals(totalPriceOld, totalPriceNew) || !Objects.equals(totalReduceOld, totalReduceNew)) {
            log.warn("divideCarts error. before divide, statistic:{}, after divide, statistic:{}, orderItemList:{}, carList:{}", statisticsOld, statisticsNew, orderItemList, cartList);
            throw ExceptionHelper.create(ErrCode.ERR_DIVIDE_CAL, "分摊计算错误，拆分sku前后统计数据价格不相等");
        }
        return orderItemList;
    }

    /**
     * 进一步拆分cartItem，将原cartItem拆为数量为1的多个cartItem
     *
     * @param cartList 购物车列表
     * @return 均摊后的购物车列表
     */
    private List<CartItem> divideCartList(List<CartItem> cartList) throws BizError {
        List<CartItem> divideCartList = new ArrayList<>();
        for (CartItem cartItem : cartList) {
            List<CartItem> eachDivide = divideCartItemToCount1CartItem(cartItem);
            divideCartList.addAll(eachDivide);
        }
        return divideCartList;
    }

    /**
     * 拆分购物车项cartItem，拆分成数量为1
     *
     * @param item 购物车项
     * @return 1-n拆分后的购物车列表
     * @throws BizError 业务异常
     */
    private List<CartItem> divideCartItemToCount1CartItem(CartItem item) throws BizError {
        List<CartItem> cartItemList = new ArrayList<>();
        if (item.getCount() == 1) {
            cartItemList.add(item);
            return cartItemList;
        }
        long moneyDiff = 1L;
        divideCartItem(item, cartItemList, moneyDiff);
        // 优惠明细拆分到每一个cartItem
        // 指定门店降价活动优惠明细
        if (CollectionUtils.isNotEmpty(item.getReduceDetailList().get(PromotionConstant.SHARE_ACTIVITY_PARTONSALE_KEY))) {
            updateCartItemShareDetail(item, cartItemList, PromotionConstant.SHARE_ACTIVITY_PARTONSALE_KEY, moneyDiff);
        }
        // 换新立减活动优惠明细
        if (CollectionUtils.isNotEmpty(item.getReduceDetailList().get(PromotionConstant.SHARE_ACTIVITY_RENEWREDUCE_KEY))) {
            updateCartItemShareDetail(item, cartItemList, PromotionConstant.SHARE_ACTIVITY_RENEWREDUCE_KEY, moneyDiff);
        }
        // 立减活动优惠明细
        if (CollectionUtils.isNotEmpty(item.getReduceDetailList().get(PromotionConstant.SHARE_ACTIVITY_BUYREDUCE_KEY))) {
            updateCartItemShareDetail(item, cartItemList, PromotionConstant.SHARE_ACTIVITY_BUYREDUCE_KEY, moneyDiff);
        }
        // 满减活动优惠明细
        if (CollectionUtils.isNotEmpty(item.getReduceDetailList().get(PromotionConstant.SHARE_ACTIVITY_REDUCE_KEY))) {
            updateCartItemShareDetail(item, cartItemList, PromotionConstant.SHARE_ACTIVITY_REDUCE_KEY, moneyDiff);
        }
        // 满折活动优惠明细
        if (CollectionUtils.isNotEmpty(item.getReduceDetailList().get(PromotionConstant.SHARE_ACTIVITY_DISCOUNT_KEY))) {
            updateCartItemShareDetail(item, cartItemList, PromotionConstant.SHARE_ACTIVITY_DISCOUNT_KEY, moneyDiff);
        }
        // 满减劵优惠明细
        if (CollectionUtils.isNotEmpty(item.getReduceDetailList().get(PromotionConstant.SHARE_COUPON_REDUCE_KEY))) {
            updateCartItemShareDetail(item, cartItemList, PromotionConstant.SHARE_COUPON_REDUCE_KEY, moneyDiff);
        }
        // 满折劵优惠明细
        if (CollectionUtils.isNotEmpty(item.getReduceDetailList().get(PromotionConstant.SHARE_COUPON_DISCOUNT_KEY))) {
            updateCartItemShareDetail(item, cartItemList, PromotionConstant.SHARE_COUPON_DISCOUNT_KEY, moneyDiff);
        }
        // 红包优惠金额
        if (CollectionUtils.isNotEmpty(item.getReduceDetailList().get(PromotionConstant.SHARE_REDPACKET_KEY))) {
            updateCartItemShareDetail(item, cartItemList, PromotionConstant.SHARE_REDPACKET_KEY, moneyDiff);
        }
        // 三方
        if (CollectionUtils.isNotEmpty(item.getReduceDetailList().get(PromotionConstant.SHARE_PHOENIX_HUASHENG_KEY))) {
            updateCartItemShareDetail(item, cartItemList, PromotionConstant.SHARE_PHOENIX_HUASHENG_KEY, moneyDiff);
        }
        // 三方联通
        if (CollectionUtils.isNotEmpty(item.getReduceDetailList().get(PromotionConstant.SHARE_PHOENIX_UNICOM_KEY))) {
            updateCartItemShareDetail(item, cartItemList, PromotionConstant.SHARE_PHOENIX_UNICOM_KEY, moneyDiff);
        }
        // 三方电信
        if (CollectionUtils.isNotEmpty(item.getReduceDetailList().get(PromotionConstant.SHARE_PHOENIX_TELECOM_KEY))) {
            updateCartItemShareDetail(item, cartItemList, PromotionConstant.SHARE_PHOENIX_TELECOM_KEY, moneyDiff);
        }
        // 三方移动
        if (CollectionUtils.isNotEmpty(item.getReduceDetailList().get(PromotionConstant.SHARE_PHOENIX_MOBILE_KEY))) {
            updateCartItemShareDetail(item, cartItemList, PromotionConstant.SHARE_PHOENIX_MOBILE_KEY, moneyDiff);
        }
        // 计算优惠总金额
        calCartItemReduceAmount(cartItemList);
        return cartItemList;
    }

    /**
     * 拆分原购物车项 1到n个，n为原购物车项商品数量
     *
     * @param item         原购物车项
     * @param cartItemList 拆分后的购物车列表
     */
    private void divideCartItem(CartItem item, List<CartItem> cartItemList, long moneyDiff) {
        Integer count = item.getCount();
        for (int i = 0; i < count; i++) {
            CartItem newCartItem = SerializationUtils.clone(item);
            String newItemId = newCartItem.getItemId() + PromotionConstant.ITEMID_ITEM_NEW + "_" + i;
            newCartItem.setItemId(newItemId);
            newCartItem.setCount(1);
            newCartItem.getReduceDetailList().clear();
            cartItemList.add(newCartItem);
        }
    }

    /**
     * 计算购物车优惠总金额
     *
     * @param cartItemList 购物车项
     */
    private void calCartItemReduceAmount(List<CartItem> cartItemList) {
        for (CartItem cartItem : cartItemList) {
            Map<String, List<ReduceDetail>> reduceListMap = cartItem.getReduceDetailList();
            long reduceAmount = 0L;
            for (Map.Entry<String, List<ReduceDetail>> entry : reduceListMap.entrySet()) {
                List<ReduceDetail> reduceDetails = entry.getValue();
                for (ReduceDetail reduceDetail : reduceDetails) {
                    reduceAmount += reduceDetail.getAmount();
                }
            }
            cartItem.setReduceAmount(reduceAmount);
        }
    }

    private void calOrderCartItemReduceAmount(List<OrderCartItem> cartItemList) {
        for (OrderCartItem cartItem : cartItemList) {
            Map<String, List<ReduceDetail>> reduceListMap = cartItem.getOrderItemReduceList();
            long reduceAmount = 0L;
            for (Map.Entry<String, List<ReduceDetail>> entry : reduceListMap.entrySet()) {
                List<ReduceDetail> reduceDetails = entry.getValue();
                for (ReduceDetail reduceDetail : reduceDetails) {
                    reduceAmount += reduceDetail.getAmount();
                }
            }
            cartItem.setReduceAmount(reduceAmount);
        }
    }

    /**
     * 更新购物车项优惠明细分摊，具体到每个优惠项
     *
     * @param originalCartItem 原购物车项
     * @param cartItemList     拆分后的购物车列表
     * @param key              优惠明细key
     * @param moneyDiff        补差金额
     */
    private void updateCartItemShareDetail(CartItem originalCartItem, List<CartItem> cartItemList, String key,
                                           long moneyDiff) throws BizError {
        List<ReduceDetail> totalReduceDetails = originalCartItem.getReduceDetailList().get(key);
        if (totalReduceDetails == null) {
            return;
        }
        for (ReduceDetail reduceDetail : totalReduceDetails) {
            long reduceDetailActId = reduceDetail.getId();
            long reduceDetailAmount = reduceDetail.getAmount();
            long eachReduceDetailAmount = Math.floorDiv(reduceDetailAmount, originalCartItem.getCount());
            long realReduceDetailAmount = 0L;
            for (CartItem cartItem : cartItemList) {
                long cartItemCurrentPrice = CartHelper.cartItemCurPrice(cartItem);
                if (cartItemCurrentPrice <= 0L) {
                    continue;
                }
                long eachRealReduceAmount = Math.min(cartItemCurrentPrice, eachReduceDetailAmount);
                List<ReduceDetail> reduceDetails = cartItem.getReduceDetailList().getOrDefault(key, new ArrayList<>());
                ReduceDetail eachCartItemReduceDetail = new ReduceDetail();
                eachCartItemReduceDetail.setId(reduceDetailActId);
                eachCartItemReduceDetail.setAmount(eachRealReduceAmount);
                reduceDetails.add(eachCartItemReduceDetail);
                cartItem.getReduceDetailList().put(key, reduceDetails);
                realReduceDetailAmount += eachRealReduceAmount;
            }
            long diffReduceDetailAmount = reduceDetailAmount - realReduceDetailAmount;
            boolean allShare = false;
            while (!allShare && diffReduceDetailAmount > 0L) {
                long allCartCurrentPrice = 0L;
                for (CartItem cartItem : cartItemList) {
                    if (diffReduceDetailAmount == 0L) {
                        break;
                    }
                    long cartItemCurrentPrice = CartHelper.cartItemCurPrice(cartItem);
                    if (cartItemCurrentPrice <= 0) {
                        continue;
                    }
                    allCartCurrentPrice += cartItemCurrentPrice;
                    for (ReduceDetail reduceDetailAdd : cartItem.getReduceDetailList().get(key)) {
                        if (reduceDetailAdd.getId() == reduceDetailActId) {
                            reduceDetailAdd.setAmount(reduceDetailAdd.getAmount() + moneyDiff);
                            diffReduceDetailAmount--;
                            break;
                        }
                    }
                }
                if (allCartCurrentPrice == 0L) {
                    allShare = true;
                }
            }
            if (diffReduceDetailAmount > 0L) {
                log.warn("updateCartItemShareDetail. the diffReduceDetailAmount:{} is wrong, cartItem:{}", diffReduceDetailAmount, originalCartItem);
                throw ExceptionHelper.create(ErrCode.ERR_SHARE_DIFF_CAL, "分摊计算错误，优惠金额不能全部被分摊");
            }
        }
    }

    /**
     * cartItem拆解为orderItem
     *
     * @param cartList 购物车列表
     * @return 订单列表信息
     */
    private List<OrderCartItem> divideCartItemListToOrderItemList(List<CartItem> cartList) throws BizError {
        List<OrderCartItem> orderCartList = new ArrayList<>();
        // key: packageId val: count
        Map<String, Integer> packageIdxMap = Maps.newHashMap();
        for (CartItem item : cartList) {
            if (!CartHelper.isPackage(item)) {
                // 处理单品
                Map<String, List<ReduceDetail>> orderItemReduceList = item.getOrderItemReduceList();
                orderItemReduceList.putAll(item.getReduceDetailList());
                // 普通
                // 门店价分摊
                if (item.getStorepriceReduce() > 0L) {
                    List<ReduceDetail> reduceDetails = new ArrayList<>();
                    ReduceDetail reduceDetail = new ReduceDetail();
                    reduceDetail.setId(Long.valueOf(item.getSourceCode()));
                    reduceDetail.setAmount(item.getStorepriceReduce());
                    reduceDetails.add(reduceDetail);
                    orderItemReduceList.put(PromotionConstant.SHARE_ACTIVITY_STOREPRICE_KEY, reduceDetails);
                }
                // 直降分摊
                if (item.isJoinOnsale()) {
                    List<ReduceDetail> reduceDetails = new ArrayList<>();
                    ReduceDetail reduceDetail = new ReduceDetail();
                    reduceDetail.setId(Long.valueOf(item.getSourceCode()));
                    reduceDetail.setAmount(item.getOnsaleReduce());
                    reduceDetails.add(reduceDetail);
                    orderItemReduceList.put(PromotionConstant.SHARE_ACTIVITY_ONSALE_KEY, reduceDetails);
                }
                // 赠品分摊
                if (SourceEnum.isGift(item.getSource())) {
                    List<ReduceDetail> reduceDetails = new ArrayList<>();
                    ReduceDetail reduceDetail = new ReduceDetail();
                    reduceDetail.setId(Long.valueOf(item.getSourceCode()));
                    reduceDetail.setAmount(item.getStandardPrice());
                    reduceDetails.add(reduceDetail);
                    orderItemReduceList.put(PromotionConstant.SHARE_ACTIVITY_GIFT_KEY, reduceDetails);
                }
                // 赠品分摊
                if (SourceEnum.isGift(item.getSource())) {
                    List<ReduceDetail> reduceDetails = new ArrayList<>();
                    ReduceDetail reduceDetail = new ReduceDetail();
                    reduceDetail.setId(Long.valueOf(item.getSourceCode()));
                    reduceDetail.setAmount(item.getStandardPrice());
                    reduceDetails.add(reduceDetail);
                    orderItemReduceList.put(PromotionConstant.SHARE_ACTIVITY_GIFT_KEY, reduceDetails);
                }
                // 加价购商品分摊
                if (SourceEnum.isBargain(item.getSource()) && item.getStandardPrice() - item.getCartPrice() > 0L) {
                    List<ReduceDetail> reduceDetails = new ArrayList<>();
                    ReduceDetail reduceDetail = new ReduceDetail();
                    reduceDetail.setId(Long.valueOf(item.getSourceCode()));
                    reduceDetail.setAmount(item.getStandardPrice() - item.getCartPrice());
                    reduceDetails.add(reduceDetail);
                    orderItemReduceList.put(PromotionConstant.SHARE_ACTIVITY_BARGAIN_KEY, reduceDetails);
                }
                orderCartList.add(item);
                continue;
            }

            String packageId = item.getPackageId();
            packageIdxMap.putIfAbsent(packageId, 0);
            Integer packageIdx = packageIdxMap.get(packageId);
            // 处理套装, 套装按SKU拆分开
            List<OrderCartItem> orderCartItems = dividePackageItem(item, packageIdx);
            orderCartList.addAll(orderCartItems);
            packageIdxMap.put(packageId, packageIdx + item.getCount());
        }
        return orderCartList;
    }

    /**
     * 拆分套装，分摊购物车价格和要减免的钱，购物车价格全局一种分法，要减免的钱才能确定
     *
     * @param cartItem   购物车条目
     * @param packageIdx 套装id索引
     * @return 订单购物车列表
     */
    private List<OrderCartItem> dividePackageItem(CartItem cartItem, int packageIdx) throws BizError {
        // 套装检查错误
        if (!CartHelper.rightInPackage(cartItem)) {
            log.warn("dividePackageItem. the cart item of package is invalid. cartItem:{}", cartItem);
            throw ExceptionHelper.create(ErrCode.ERR_CART_PACKAGE, "拆分购物车套装错误，购物车条目不是套装");
        }
        // 套装的数量检查
        if (cartItem.getCount() != 1) {
            log.error("dividePackageItem. the cart item of package count is not 1. cartItem:{}", cartItem);
            throw ExceptionHelper.create(ErrCode.ERR_DIVIDE_EVENLY_CAL, "拆分购物车套装错误，购物车数量不为1");
        }
        // 未设置孩子的cartPrice，按sellPrice的比例设置
        if (cartItem.getChilds().get(0).getCartPrice() == 0L) {
            updateChildCartPrice(cartItem);
        }

        List<OrderCartItem> orderCartList = dividePackageChild(cartItem, packageIdx);
        return orderCartList;
    }

    /**
     * 更新套装下商品cartPrice
     *
     * @param cartItem 购物车
     */
    private void updateChildCartPrice(CartItem cartItem) {
        long totalSellPrice = CartHelper.getPackageSellPrice(cartItem);
        long cartPrice = cartItem.getCartPrice();

        long actualCartPrice = 0L;
        // 孩子的cartPrice是按sellPrice比例做分摊
        for (CartItemChild child : cartItem.getChilds()) {
            long childCartPrice = Math.floorDiv(cartPrice * child.getSellPrice(), totalSellPrice);
            child.setCartPrice(childCartPrice);
            actualCartPrice += childCartPrice;
        }

        long priceDiff = cartPrice - actualCartPrice;
        if (priceDiff == 0L) {
            return;
        }

        // 有分摊余额, 一分钱一分钱分摊
        long moneyDiff = 1;
        for (CartItemChild child : cartItem.getChilds()) {
            if (priceDiff <= 0L) {
                return;
            }
            child.setCartPrice(child.getCartPrice() + moneyDiff);
            priceDiff -= moneyDiff;
        }
    }

    /**
     * 套装拆分结果
     *
     * @param cartItem   购物车
     * @param packageIdx 下标
     * @return 列表
     */
    private List<OrderCartItem> dividePackageChild(CartItem cartItem, int packageIdx) throws BizError {
        List<OrderCartItem> orderCartList = new ArrayList<>();
        long moneyDiff = 1L;
        // 套装子商品拆分
        dividePackageChildToOrderItem(cartItem, packageIdx, orderCartList);

        // 拆分指定门店降价活动优惠到子商品orderItem
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.SHARE_ACTIVITY_PARTONSALE_KEY, moneyDiff);
        // 拆分换新立减活动优惠到子商品orderItem
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.SHARE_ACTIVITY_RENEWREDUCE_KEY, moneyDiff);
        // 拆分换新立减活动优惠到子商品orderItem
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.SHARE_ACTIVITY_BUYREDUCE_KEY, moneyDiff);
        // 拆分满减活动优惠到子商品orderItem
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.SHARE_ACTIVITY_REDUCE_KEY, moneyDiff);
        // 拆分满折活动优惠到子商品orderItem
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.SHARE_ACTIVITY_DISCOUNT_KEY, moneyDiff);
        // 拆分满减劵优惠到子商品orderItem
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.SHARE_COUPON_REDUCE_KEY, moneyDiff);
        // 拆分满折劵优惠到子商品orderItem
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.SHARE_COUPON_DISCOUNT_KEY, moneyDiff);
        // 拆分抵扣劵优惠到子商品orderItem
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.SHARE_COUPON_DEDUCT_KEY, moneyDiff);
        // 拆分红包优惠到子商品orderItem
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.SHARE_REDPACKET_KEY, moneyDiff);
        // 拆分三方优惠到子商品orderItem
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.SHARE_PHOENIX_HUASHENG_KEY, moneyDiff);
        // 拆分三方联通优惠到子商品orderItem
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.SHARE_PHOENIX_UNICOM_KEY, moneyDiff);
        // 拆分三方电信优惠到子商品orderItem
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.SHARE_PHOENIX_TELECOM_KEY, moneyDiff);
        // 拆分三方移动优惠到子商品orderItem
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.SHARE_PHOENIX_MOBILE_KEY, moneyDiff);
        // 拆分三方米家换新优惠到子商品orderItem
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.SHARE_PHOENIX_RENEW_SUBSIDY_KEY, moneyDiff);
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.SHARE_PHOENIX_RENEW_DEDUCT_KEY, moneyDiff);
        // 拆分以旧换新-购新补贴优惠到子商品orderItem
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.NEW_PURCHASE_SUBSIDY_KEY, moneyDiff);
        // 拆分以旧换新-换新补贴优惠到子商品orderItem
        updateOrderItemReduceDetailForPackageChild(cartItem, orderCartList, PromotionConstant.UPGRADE_PURCHASE_SUBSIDY_KEY, moneyDiff);
        calOrderCartItemReduceAmount(orderCartList);
        // 拆分门店价活动优惠到子商品orderItem
        updateOrderItemStorePriceReduceAmountForPackageChild(cartItem, orderCartList);
        // 拆分直降活动优惠到子商品orderItem
        updateOrderItemOnsaleReduceAmountForPackageChild(cartItem, orderCartList);
        return orderCartList;
    }

    /**
     * 套装子商品拆orderItem
     *
     * @param cartItem      购物车项
     * @param packageIdx    下标
     * @param orderCartList 订单（sku粒度）列表
     */
    private void dividePackageChildToOrderItem(CartItem cartItem, int packageIdx, List<OrderCartItem> orderCartList) {
        // 对套装进行子商品拆分
        for (int i = 0; i < cartItem.getChilds().size(); i++) {
            CartItemChild child = cartItem.getChilds().get(i);
            String itemId = getPackageItemId(cartItem.getItemId(), packageIdx, i);
            OrderCartItem newItem = SerializationUtils.clone(cartItem);
            newItem.setItemId(itemId);
            newItem.setCount(1);
            newItem.setCartPrice(child.getCartPrice());
            newItem.setStandardPrice(child.getOriginalSellPrice());
            newItem.setSku(child.getSku());
            newItem.getOrderItemReduceList().clear();
            newItem.setUnitId(child.getUnitId());
            orderCartList.add(newItem);
        }
    }

    /**
     * 更新套装子商品的门店价优惠明细
     *
     * @param originalCartItem 购物车项
     * @param orderCartList    订单（sku粒度）列表
     */
    private void updateOrderItemStorePriceReduceAmountForPackageChild(CartItem
                                                                              originalCartItem, List<OrderCartItem> orderCartList) {
        if (originalCartItem.getStorepriceReduce() == 0L) {
            return;
        }
        for (int i = 0; i < originalCartItem.getChilds().size(); i++) {
            CartItemChild child = originalCartItem.getChilds().get(i);
            OrderCartItem cartItem = orderCartList.get(i);
            Map<String, List<ReduceDetail>> reduceInfoMap = cartItem.getOrderItemReduceList();
            List<ReduceDetail> reduceDetails = new ArrayList<>();
            ReduceDetail reduceDetail = new ReduceDetail();
            reduceDetail.setId(Long.valueOf(originalCartItem.getSourceCode()));
            reduceDetail.setAmount(child.getStorepriceReduce());
            reduceDetails.add(reduceDetail);
            reduceInfoMap.put(PromotionConstant.SHARE_ACTIVITY_STOREPRICE_KEY, reduceDetails);
            cartItem.setOrderItemReduceList(reduceInfoMap);
        }
    }

    /**
     * 更新套装子商品的直降优惠明细
     *
     * @param originalCartItem 购物车项
     * @param orderCartList    订单（sku粒度）列表
     */
    private void updateOrderItemOnsaleReduceAmountForPackageChild(CartItem
                                                                          originalCartItem, List<OrderCartItem> orderCartList) {
        if (!originalCartItem.isJoinOnsale()) {
            return;
        }
        for (int i = 0; i < originalCartItem.getChilds().size(); i++) {
            CartItemChild child = originalCartItem.getChilds().get(i);
            OrderCartItem cartItem = orderCartList.get(i);
            Map<String, List<ReduceDetail>> reduceInfoMap = cartItem.getOrderItemReduceList();
            List<ReduceDetail> reduceDetails = new ArrayList<>();
            ReduceDetail reduceDetail = new ReduceDetail();
            reduceDetail.setId(Long.valueOf(originalCartItem.getSourceCode()));
            reduceDetail.setAmount(child.getOnsaleReduce());
            reduceDetails.add(reduceDetail);
            reduceInfoMap.put(PromotionConstant.SHARE_ACTIVITY_ONSALE_KEY, reduceDetails);
            cartItem.setOrderItemReduceList(reduceInfoMap);
        }
    }

    /**
     * 更新套装子商品的优惠明细
     *
     * @param originalCartItem 购物车项
     * @param orderCartList    订单（sku粒度）列表
     * @param key              优惠明细key
     * @param moneyDiff        补差金额
     * @throws BizError
     */
    private void updateOrderItemReduceDetailForPackageChild(CartItem
                                                                    originalCartItem, List<OrderCartItem> orderCartList, String key, long moneyDiff) throws BizError {
        List<ReduceDetail> totalReduceDetails = originalCartItem.getReduceDetailList().get(key);
        if (totalReduceDetails == null) {
            return;
        }
        long packageSellPrice = CartHelper.getPackageSellPrice(originalCartItem);
        for (ReduceDetail reduceDetail : totalReduceDetails) {
            long reduceDetailActId = reduceDetail.getId();
            long reduceDetailAmount = reduceDetail.getAmount();

            long realReduceDetailAmount = 0L;
            for (int i = 0; i < originalCartItem.getChilds().size(); i++) {
                CartItemChild child = originalCartItem.getChilds().get(i);
                OrderCartItem orderCartItem = orderCartList.get(i);
                long currentPrice = CartHelper.itemCurPrice2(orderCartItem);
                if (currentPrice <= 0L) {
                    continue;
                }
                long reduceAmount = Math.min(Math.floorDiv(reduceDetailAmount * child.getSellPrice(), packageSellPrice), currentPrice);
                List<ReduceDetail> reduceDetails = orderCartItem.getOrderItemReduceList().getOrDefault(key, new ArrayList<>());
                ReduceDetail eachCartItemReduceDetail = new ReduceDetail();
                eachCartItemReduceDetail.setId(reduceDetailActId);
                eachCartItemReduceDetail.setAmount(reduceAmount);
                reduceDetails.add(eachCartItemReduceDetail);
                orderCartItem.getOrderItemReduceList().put(key, reduceDetails);
                realReduceDetailAmount += reduceAmount;
            }

            long diffReduceDetailAmount = reduceDetailAmount - realReduceDetailAmount;
            // 是否购物车所有的分摊完，不能再分摊了
            boolean isAllShare = false;
            while (diffReduceDetailAmount > 0L && !isAllShare) {
                long allCartCurrentPrice = 0L;
                for (OrderCartItem cartItem : orderCartList) {
                    if (diffReduceDetailAmount == 0L) {
                        break;
                    }
                    long itemCurrentPrice = CartHelper.itemCurPrice2(cartItem);
                    if (itemCurrentPrice <= 0L) {
                        continue;
                    }
                    allCartCurrentPrice += itemCurrentPrice;
                    for (ReduceDetail reduceDetailAdd : cartItem.getOrderItemReduceList().get(key)) {
                        if (reduceDetailAdd.getId() == reduceDetailActId) {
                            reduceDetailAdd.setAmount(reduceDetailAdd.getAmount() + moneyDiff);
                            diffReduceDetailAmount--;
                            break;
                        }
                    }
                }
                if (allCartCurrentPrice == 0L) {
                    isAllShare = true;
                }
            }
            if (diffReduceDetailAmount > 0L) {
                log.warn("updateOrderItemReduceDetailForPackageChild. the diffReduceDetailAmount:{} is wrong, cartItem:{}", diffReduceDetailAmount, originalCartItem);
                throw ExceptionHelper.create(ErrCode.ERR_SHARE_DIFF_CAL, "分摊计算错误，优惠金额不能全部被分摊");
            }
        }
    }


    /**
     * 获取套装下商品新itemId
     *
     * @param itemId     套装ItemId
     * @param packageIdx 套装下标
     * @param childIdx   子商品下标
     * @return newItemId
     */
    private String getPackageItemId(String itemId, Integer packageIdx, Integer childIdx) {
        return itemId + PromotionConstant.ITEMID_PACKAGE_NEW + "_" + packageIdx + "_" + childIdx;
    }

    /**
     * 检验并统计，针对OrderCartItem
     *
     * @param orderCartList 订单购物车列表
     * @return 统计信息
     * @throws BizError 业务异常
     */
    private StatisticsSimple checkOrderAndStatistics(List<OrderCartItem> orderCartList) throws BizError {
        long totalCount = 0L;
        long totalPrice = 0L;
        long totalReduce = 0L;
        for (OrderCartItem item : orderCartList) {
            if (item == null || !CartHelper.filterOrderOk(item)) {
                log.warn("the cart's data is wrong, cart:{}", item);
                throw ExceptionHelper.create(GeneralCodes.InternalError, "inner.invalidCalculate");
            }
            totalCount += item.getCount();
            totalPrice += item.getCartPrice() * item.getCount();
            totalReduce += item.getReduceAmount();
        }
        StatisticsSimple statistics = new StatisticsSimple();
        statistics.setTotalCount(totalCount);
        statistics.setTotalPrice(totalPrice);
        statistics.setTotalReduce(totalReduce);
        return statistics;
    }

    /**
     * 获取统计简单信息
     *
     * @param cartItemList 购物车项
     * @return 统计信息
     * @throws BizError 异常
     */
    public static StatisticsSimple checkSimpleAndStatistics(List<CartItem> cartItemList) throws BizError {
        // 迭代购物车， 追加汇总数据
        long totalCount = 0L;
        long totalPrice = 0L;
        long totalReduce = 0L;
        long totalStandardPrice = 0L;
        cartItemList = Optional.ofNullable(cartItemList).orElse(Collections.emptyList());
        for (CartItem cartItem : cartItemList) {
            if (cartItem == null || !CartHelper.filterSimpleOk(cartItem)) {
                log.warn("the cart's data is wrong, cart:{}", cartItem);
                throw ExceptionHelper.create(GeneralCodes.InternalError, "inner.invalidCalculate");
            }
            totalCount += cartItem.getCount();
            totalPrice += cartItem.getCartPrice() * cartItem.getCount();
            totalReduce += cartItem.getReduceAmount();
            totalStandardPrice += cartItem.getStandardPrice();
        }
        StatisticsSimple statistics = new StatisticsSimple();
        statistics.setTotalCount(totalCount);
        statistics.setTotalPrice(totalPrice);
        statistics.setTotalReduce(totalReduce);
        statistics.setTotalStandardPrice(totalStandardPrice);
        return statistics;
    }

    public static boolean validCouponId(Long couponId) {
        if (couponId == null || couponId <= 0) {
            return false;
        }
        return true;
    }
}
