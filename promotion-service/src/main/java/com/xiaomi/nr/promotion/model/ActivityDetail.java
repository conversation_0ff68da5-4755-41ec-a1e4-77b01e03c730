package com.xiaomi.nr.promotion.model;

import com.xiaomi.nr.promotion.api.dto.model.ReduceGoodsInfo;
import com.xiaomi.nr.promotion.api.dto.model.Policy;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.entity.redis.FillGoodsGroup;
import com.xiaomi.nr.promotion.entity.redis.Goods;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.OnOffLineEnum;
import com.xiaomi.nr.promotion.enums.OrgScopeEnum;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 活动详情
 *
 * <AUTHOR>
 * @date 2021/6/25
 */
@Data
public class ActivityDetail {
    /**
     * 活动ID
     * alias promotionId
     */
    private Long promotionId;

    /**
     * 活动类型
     */
    private ActivityTypeEnum type;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动开始时间（秒）
     */
    private long unixStartTime;

    /**
     * 活动结束时间（秒）
     */
    private long unixEndTime;

    /**
     * 参与的sku 或 package商品列表
     */
    private Set<String> includeSkuPackages;

    /**
     * 是否线下可用 1仅线上使用，2仅线下使用 3均可使用
     */
    private OnOffLineEnum offline;
    /**
     * 门店范围
     */
    private OrgScopeEnum orgScope;

    /**
     * 指定门店或区域ID
     */
    private List<String> selectOrgCodes;

    /**
     * 能参与的客户端列表
     */
    private List<String> selectClients;
    /**
     * 活动规则
     */
    private List<String> descRule;
    /**
     * 规则短链
     */
    private String descRuleIndex;

    /**
     * 改价信息
     * key: skuPackage val:ActPriceInfo
     */
    private Map<String, ActPriceInfo> priceInfoMap;

    /**
     * 主商品列表
     */
    private List<FillGoodsGroup> includeGoodsGroups;

    /**
     * 政策列表
     */
    private List<Policy> policys;

    /**
     * 加价购商品信息
     */
    private Goods bargainGoods;

    /**
     * 赠品商品信息
     */
    private Goods giftGoods;
    /**
     * 扣减商品
     */
    private List<ReduceGoodsInfo> reduceGoods;

    /**
     * 活动总数
     */
    private long actLimitNum;

    /**
     * 是否限制商品数量
     */
    private boolean numLimit;

    /**
     * 活动限制. 数据值：0-不限制， 没有限制就没有对应字段
     */
    private ActNumLimitRule numLimitRule;


    /**
     * 渠道列表
     */
    private List<Integer> channels;
}
