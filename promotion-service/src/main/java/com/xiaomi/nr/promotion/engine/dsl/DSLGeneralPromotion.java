package com.xiaomi.nr.promotion.engine.dsl;

import com.xiaomi.nr.phoenix.api.dto.response.tradeInfo.TradeQualificationInfo;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.componet.action.ActionFactory;
import com.xiaomi.nr.promotion.componet.condition.ConditionFactory;
import com.xiaomi.nr.promotion.componet.preparation.ConditionPreparationFactory;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.componet.Action;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.engine.componet.ConditionPreparation;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ConfigMetaInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 通用的基于dsl的优惠工具模板<br>
 * 使用者只需要定义init的条件和操作流就可以自动生成一个新的优惠工具
 * <p>
 *
 * <AUTHOR>
 * @date 2018/8/14
 */
@Slf4j
@Component
public abstract class DSLGeneralPromotion implements ActivityTool {

    /**
     * 初始化方法，必须定义一组dsl流程
     *
     * @return 定义流
     */
    protected abstract DSLStream getDSLDefinition();


    @Override
    public BizPlatformEnum getBizPlatform() {
        //TODO 默认所有工具为新零售业务，后续逐渐拆分
        return BizPlatformEnum.NEW_RETAIL;
    }

    /**
     * 活动id
     */
    protected long id;

    /**
     * 活动名称
     */
    protected String name;

    /**
     * 活动类型
     */
    protected ActivityTypeEnum type;

    /**
     * 开始时间
     */
    protected long unixStartTime;

    /**
     * 结束时间
     */
    protected long unixEndTime;

    /**
     * 渠道列表
     */
    protected List<Integer> channels;

    /**
     * 门店区域范围
     */
    protected List<String> selectOrgList;

    /**
     * 端列表
     */
    protected List<String> selectClientList;

    /**
     * 配置元信息
     */
    protected ConfigMetaInfo metaInfo;

    /**
     * condition preparation组件
     */
    private final List<ConditionPreparation> conditionPreparations = new ArrayList<>();

    /**
     * condition组件
     */
    private final List<Condition> conditionList = new ArrayList<>();

    /**
     * action组件
     */
    private final List<Action> actionList = new ArrayList<>();

    protected AbstractPromotionConfig promotionConfig;

    @Autowired
    public ConditionPreparationFactory conditionPreparationFactory;

    @Autowired
    public ConditionFactory conditionFactory;

    @Autowired
    public ActionFactory actionFactory;

    /**
     * 读取根据配置统一加载每一个组件
     * Caller activity.load
     */
    protected boolean DSLLoad(AbstractPromotionConfig config) throws BizError {
        DSLStream stream = getDSLDefinition();
        createConditionPreparations(stream.getConditionPreparations(), config);
        createConditions(stream.getConditionList(), config);
        createActions(stream.getActionList(), config);
        loadPromotionConfig(config);
        this.promotionConfig = config;
        return true;
    }

    @Override
    public AbstractPromotionConfig getPromotionConfig() {
        return promotionConfig;
    }

    /**
     * 进行优惠计算
     * <p>
     * 1. doConditionPrepares - 执行所有条件准备，进行资源预加载
     * 2. checkCondition - 进行条件检查
     * 3. doActions - 执行行为， 如直降的减价行为
     * 4. 合并组件上下文LocalContext数据到流程全局上下文CheckoutContext中
     * </p>
     *
     * @param request         参数
     * @param checkoutContext 流程上下文
     * @throws BizError 业务异常
     */
    @Override
    public void doCheckout(CheckoutPromotionRequest request, CheckoutContext checkoutContext) throws BizError {
        // 构造localContext
        LocalContext context = getLocalContext(checkoutContext);

        // 准备用户限购，活动库存等信息放到localContext中
        doConditionPrepares(request, context);

        // 条件检查
        boolean condValid = checkCondition(request, context);
        if (!condValid) {
            log.info("request cannot join act. uid:{} actId:{}", request.getUserId(), id);
            mergeInvalidLocalContext(request, context, checkoutContext);
            return;
        }

        // 执行结算逻辑
        doActions(request, context);

        // 合并localContext到checkoutContext
        mergeLocalContext(context, checkoutContext);
    }

    private void mergeInvalidLocalContext(CheckoutPromotionRequest request, LocalContext localContext, CheckoutContext checkoutContext) {
        if (ActivityTypeEnum.isGiftBargainAct(type.getValue())) {
            CartHelper.delGiftBargain(request.getCartList(), id);
        }
        if (ActivityTypeEnum.NEW_PURCHASE_SUBSIDY.equals(type)) {
            // 合并补贴提示
            if (!StringUtils.isEmpty(localContext.getPurchaseSubsidyInvalid())) {
                checkoutContext.setPurchaseSubsidyInvalid(localContext.getPurchaseSubsidyInvalid());
            }
            // 合并活动信息
            PromotionInfo promotionInfo = localContext.getPromotion();
            if (promotionInfo != null) {
                List<PromotionInfo> promotionInfoList = Optional.ofNullable(checkoutContext.getPromotion()).orElse(new ArrayList<>());
                promotionInfoList.add(promotionInfo);
                checkoutContext.setPromotion(promotionInfoList);
            }
        }
    }


    /**
     * 创建condition组件
     *
     * @param clazzList ConditionPreparation类列表
     * @param config    优惠配置信息
     * @throws BizError 业务异常
     */
    private void createConditionPreparations(List<Class<? extends ConditionPreparation>> clazzList,
                                             AbstractPromotionConfig config) throws BizError {
        for (Class<? extends ConditionPreparation> clz : clazzList) {
            final ConditionPreparation conditionPreparation = conditionPreparationFactory.getConditionLoader(clz);
            if (conditionPreparation == null) {
                throw ExceptionHelper.create(GeneralCodes.InternalError,
                        String.format("DSLGeneralPromotion err：Have no the class:[%s]", clz.getName()));
            }

            conditionPreparation.loadConfig(config);
            this.conditionPreparations.add(conditionPreparation);
        }
    }

    /**
     * 创建condition组件
     *
     * @param clazzList Condition类列表
     * @param config    优惠配置信息
     * @throws BizError 业务异常
     */
    private void createConditions(List<Class<? extends Condition>> clazzList, AbstractPromotionConfig config) throws BizError {
        for (Class<? extends Condition> clz : clazzList) {
            final Condition condition = conditionFactory.getCondition(clz);
            if (condition == null) {
                throw ExceptionHelper.create(GeneralCodes.InternalError, String.format("DSLGeneralPromotion err：Have no the class:[%s]", clz.getName()));
            }
            condition.loadConfig(config);
            this.conditionList.add(condition);
        }
    }

    /**
     * 创建action组件
     *
     * @param clazzList Action类列表
     * @param config    优惠配置信息
     * @throws BizError 业务异常
     */
    private void createActions(List<Class<? extends Action>> clazzList, AbstractPromotionConfig config) throws BizError {
        for (Class<? extends Action> clz : clazzList) {
            Action action = actionFactory.getAction(clz);
            if (action == null) {
                throw ExceptionHelper.create(GeneralCodes.InternalError, String.format("DSLGeneralPromotion err：Have no the class:[%s]", clz.getName()));
            }
            action.loadConfig(config);
            this.actionList.add(action);
        }
    }

    /**
     * 处理上下文
     *
     * @param checkoutContext 流程上下文
     * @return 局部上下文
     */
    private LocalContext getLocalContext(CheckoutContext checkoutContext) {
        LocalContext localContext = new LocalContext();
        localContext.setFromInterface(checkoutContext.getFromInterface());
        localContext.setExternalDataMap(checkoutContext.getExternalDataMap());
        localContext.setIsProtectPrice(checkoutContext.getIsProtectPrice());
        localContext.setSceneActIdsMap(checkoutContext.getGoodsActIdsMap());
        localContext.setOrderId(checkoutContext.getOrderId());
        localContext.setBizPlatform(checkoutContext.getBizPlatform());
        localContext.setCarts(checkoutContext.getCarts());
        localContext.setUsedQualifyMap(checkoutContext.getUsedQualifyMap());
        return localContext;
    }

    /**
     * 执行条件的前置准备工作
     *
     * @param request checkout参数
     * @param context 上下文
     */
    private void doConditionPrepares(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (CollectionUtils.isEmpty(conditionPreparations)) {
            return;
        }
        for (ConditionPreparation conditionPreparation : conditionPreparations) {
            conditionPreparation.prepare(request, context);
        }
    }

    /**
     * checkout方法所需条件组件
     *
     * @param request checkout参数
     * @param context 上下文
     * @return 是否符合全部条件 true/false
     * @throws BizError 业务异常
     */
    private boolean checkCondition(final CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (CollectionUtils.isEmpty(conditionList)) {
            return true;
        }

        for (Condition condition : conditionList) {
             if (!condition.isSatisfied(request, context)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 执行action组件
     *
     * @param request checkout参数
     * @param context 上下文
     * @throws BizError 业务异常
     */
    private void doActions(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        //执行action
        for (Action action : actionList) {

            action.execute(this, request, context);
        }
    }

    /**
     * 将localContext中的信息合并到CheckoutContext中
     *
     * @param localContext    tool内上下文
     * @param checkoutContext tool间上下文
     */
    private void mergeLocalContext(LocalContext localContext, CheckoutContext checkoutContext) {
        // 合并资源信息
        checkoutContext.appendResourceHandler(localContext.getHandlers());
        // 合并活动信息
        PromotionInfo promotionInfo = localContext.getPromotion();
        Express express = localContext.getExpress();
        Long finTime = localContext.getFinTime();
        if (promotionInfo == null || express == null || finTime == null) {
            log.error("data error. promotionInfo:{}, express:{}, finTime:{}", promotionInfo, express, finTime);
            return;
        }
        List<PromotionInfo> promotionInfoList = Optional.ofNullable(checkoutContext.getPromotion()).orElse(new ArrayList<>());
        promotionInfoList.add(promotionInfo);
        checkoutContext.setPromotion(promotionInfoList);

        List<Express> expressList = Optional.ofNullable(checkoutContext.getExpress()).orElse(new ArrayList<>());
        expressList.add(express);
        checkoutContext.setExpress(expressList);

        List<Long> finTimeList = Optional.ofNullable(checkoutContext.getFinTime()).orElse(new ArrayList<>());
        finTimeList.add(finTime);
        checkoutContext.setFinTime(finTimeList);

        Map<String, TradeQualificationInfo> qualifyMap = Optional.ofNullable(localContext.getCartItemQualifyMap()).orElse(new HashMap<>());
        if (MapUtils.isEmpty(checkoutContext.getCartItemQualifyMap())) {
            checkoutContext.setCartItemQualifyMap(qualifyMap);
        } else {
            checkoutContext.getCartItemQualifyMap().putAll(qualifyMap);
        }

        Map<String, TradeQualificationInfo> usedQualifyMap = Optional.ofNullable(localContext.getUsedQualifyMap()).orElse(new HashMap<>());
        checkoutContext.setUsedQualifyMap(usedQualifyMap);

    }

    /**
     * 获取优惠ID
     *
     * @return ID
     */
    @Override
    public long getId() {
        return id;
    }

    /**
     * 获取活动类型
     *
     * @return 类型
     */
    public PromotionToolType getPromotionType() {
        return PromotionToolType.fromTypeId(type.getValue());
    }

    /**
     * 获取优惠类型名称
     *
     * @return 名称
     */
    private String getPromotionTypeString() {
        return type.getName();
    }

    /**
     * 获取活动开始时间 （秒）
     *
     * @return 时间
     */
    @Override
    public long getUnixStartTime() {
        return unixStartTime;
    }

    /**
     * 获取活动结束时间 （秒）
     *
     * @return 时间
     */
    @Override
    public long getUnixEndTime() {
        return unixEndTime;
    }

    /**
     * 加载活动配置
     *
     * @param config 优惠活动配置类
     */
    protected void loadPromotionConfig(AbstractPromotionConfig config) {
        this.id = config.getPromotionId();
        this.name = config.getName();
        this.type = ActivityTypeEnum.getByValue(config.getPromotionType().getTypeId());
        this.unixStartTime = config.getUnixStartTime();
        this.unixEndTime = config.getUnixEndTime();
        this.metaInfo = config.getMetaInfo();
        this.channels = config.getChannels();
        this.selectClientList = config.getSelectClients();
        this.selectOrgList = config.getSelectOrgCodes();
    }

}
