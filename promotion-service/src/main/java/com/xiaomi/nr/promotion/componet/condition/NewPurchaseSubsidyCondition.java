package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.phoenix.api.dto.response.tradeInfo.TradeInfoQueryListResp;
import com.xiaomi.nr.phoenix.api.dto.response.tradeInfo.TradeQualificationInfo;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.NewPurchaseSubsidyPromotionConfig;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.resource.external.SubsidyPhoenixExternalProvider;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class NewPurchaseSubsidyCondition extends AbstractGiftCondition {

    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 优惠类型
     */
    private PromotionToolType promotionType;
    /**
     * sku-品类
     */
    private Map<String, String> goodsSpuGroupMap;

    /**
     * 品类限购最大数量
     */
    private Integer limitTotal;

    /**
     * 品类限购最大数量
     */
    private Integer limitGroup;


    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {

        /*Long uid = request.getUserId();
        String idCard = request.getIdCard();
        if (MapUtil.isEmpty(goodsSpuGroupMap)) {
            log.warn("goodsSpuGroupMap is empty. actId:{} uid:{}", promotionId, uid);
            return false;
        }
        // 如果没有身份证，默认不使用优惠，但是要把满足补贴的活动信息返回交易，用于引导认证
        if (StringUtils.isEmpty(idCard)) {
            noIdCardConstructPromotionInfo(request, context);
            return false;
        } else {
            return hasIdCardMatchGoodsIndex(request, context);
        }*/

        if (MapUtil.isEmpty(goodsSpuGroupMap)) {
            log.warn("goodsSpuGroupMap is empty. actId:{} uid:{}", promotionId, request.getUserId());
            return false;
        }

        // 是否有商品能参加购新补贴
        boolean cannotJoinSubsidy = request.getCartList().stream()
                .allMatch(po -> po.getCannotJoinActTypes().contains((long) ActivityTypeEnum.NEW_PURCHASE_SUBSIDY.getValue()));
        if (cannotJoinSubsidy){
            return false;
        }

        return hasIdCardMatchGoodsIndex(request, context);
    }


    /**
     * 实名认证,构造promotionInfo，用于引导用户认证
     */
    private boolean hasIdCardMatchGoodsIndex(CheckoutPromotionRequest request, LocalContext context) throws BizError {

        List<CartItem> cartList = request.getSourceApi() == SourceApi.SUBMIT ? context.getCarts() : request.getCartList();

        List<TradeQualificationInfo> qualifyDetailInfoList = getQualificationInfos(context);
        if (CollectionUtils.isEmpty(qualifyDetailInfoList)) {
            context.setPurchaseSubsidyInvalid(String.valueOf(ErrCode.PROMOTION_ACTIVITY_PURCHASE_SUBSIDY_NO_QUALIFY.getCode()));
            return false;
        }

        int maxUseNum = qualifyDetailInfoList.size();
        // 标记：整单都不可用补贴，但是由于某个商品加购了多件导致
        boolean purchaseSubsidyInvalid = false;
        // 复制购物车、按优惠后价格排序
        List<CartItem> cartCopyList =new ArrayList<>(cartList);
        cartCopyList.sort(((o1, o2) -> {
                Long o1Price = o1.getCartPrice() * o1.getCount() - o1.getReduceAmount();
                Long o2Price = o2.getCartPrice() * o2.getCount() - o2.getReduceAmount();
                return o2Price.compareTo(o1Price);
            }));


        // 品类-数量Map, 用于判断本次购买同品类限购
        Map<String, Integer> spuGroupCountMap = new HashMap<>();

        List<CartItem> orgBindCartItemList = new ArrayList<>();
        // 记录可参与补贴活动的itemId
        List<String> itemIdList = new ArrayList<>();

        Map<String, TradeQualificationInfo> qualificationInfoMap = new HashMap<>();
        for (TradeQualificationInfo qualificationInfo : qualifyDetailInfoList) {
            qualificationInfoMap.put(qualificationInfo.getQualificationType(), qualificationInfo);
        }
        // 记录已经计算过的资格码品类
        Map<String, TradeQualificationInfo> usedQualifyInfoMap = context.getUsedQualifyMap();
        Map<String, TradeQualificationInfo> bindQualifyInfoMap = new HashMap<>();

        for (CartItem item : cartCopyList) {
            // 1、赠品、加价购、不能参加的活动类型过滤
            boolean filterResult = CartHelper.checkItemActQualify(item, promotionType.getTypeId(), StringUtils.isEmpty(request.getOrgCode()), null, null);
            if (!filterResult) {
                continue;
            }
            // 2、活动池中不存在的商品过滤
            String skuPackage = CartHelper.getSkuPackage(item);
            String spuGroup = goodsSpuGroupMap.get(skuPackage);
            if (StringUtils.isEmpty(spuGroup)) {
                log.warn("NewPurchaseSubsidyCondition spuGroup is null. promotionId:{} skuPackage:{}", promotionId, skuPackage);
                continue;
            }
            // 3、过滤资格码中不存在的商品品类
            TradeQualificationInfo qualificationInfo = qualificationInfoMap.get(spuGroup);
            if (qualificationInfo == null) {
                log.warn("NewPurchaseSubsidyCondition spuGroup is not contain. promotionId:{} skuPackage:{}", promotionId, skuPackage);
                continue;
            }
            // 4、过滤资格码已经使用的品类
            if(usedQualifyInfoMap.containsKey(spuGroup)){
                log.warn("NewPurchaseSubsidyCondition spuGroup has used. promotionId:{} skuPackage:{}", promotionId, skuPackage);
                continue;
            }
            // 5、同品类只能有1件参加，用于判断上次迭代购物车时，是否已经将同品类纳入可参与活动中
            Integer spuGroupCount = spuGroupCountMap.get(spuGroup);
            if (spuGroupCount != null && spuGroupCount >= limitGroup) {
                continue;
            }
            // 6、商品数量是否大于1
            if (item.getCount() > 1) {
                purchaseSubsidyInvalid = true;
                log.info("spuGroup has qualification. but goods count > 1, uid:{} skuPackage:{} spuGroup:{} count{}", request.getUserId(), skuPackage, spuGroup, item.getCount());
                continue;
            }

            log.info("newPurchaseSubsidyLimitInfo spuGroup:{}, qualificationInfo:{}", spuGroup, qualificationInfo);
            // 门店&&强绑定需要单独处理
            if (!StringUtils.isEmpty(request.getOrgCode()) && SourceEnum.isBind(item.getSource())) {
                orgBindCartItemList.add(item);
                bindQualifyInfoMap.put(spuGroup, qualificationInfo);
                continue;
            }
            itemIdList.add(item.getItemId());
            spuGroupCountMap.put(spuGroup, spuGroupCountMap.getOrDefault(spuGroup, 0) + 1);
            usedQualifyInfoMap.put(spuGroup, qualificationInfo);
            // 7、达到最大使用数量后，后续商品不处理
            if (itemIdList.size() >= maxUseNum) {
                break;
            }
        }

        // 如果因为用户在相同商品加购多件导致不可用优惠，需要给出提示
        if (itemIdList.isEmpty() && orgBindCartItemList.isEmpty() && purchaseSubsidyInvalid) {
            context.setPurchaseSubsidyInvalid(String.valueOf(ErrCode.PROMOTION_ACTIVITY_PURCHASE_SUBSIDY_NUM_LIMIT.getCode()));
            return false;
        }

        // 合并强绑定使用的资格码品类
        if(MapUtils.isNotEmpty(bindQualifyInfoMap)){
            usedQualifyInfoMap.putAll(bindQualifyInfoMap);
        }
        context.setUsedQualifyMap(usedQualifyInfoMap);

        // 门店强绑定特殊处理、每个品类只取一个最高价的强绑定
        if (!StringUtils.isEmpty(request.getOrgCode()) && !orgBindCartItemList.isEmpty()) {
            List<String> newItemIdList = addBindToItemIdList(orgBindCartItemList, itemIdList, cartCopyList, maxUseNum);
            return updatePromotionItemIndex(request, context, cartList, newItemIdList);
        }
        // 更新可参与活动的cartItem索引、并返回更新状态
        return updatePromotionItemIndex(request, context, cartList, itemIdList);
    }

    /**
     * 将强绑定商品加入到itemIdList中
     * @param orgBindItemList
     * @param itemIdList
     * @param cartCopyList
     * @param maxUseNum
     * @return
     */
	private List<String> addBindToItemIdList(List<CartItem> orgBindItemList, List<String> itemIdList, List<CartItem> cartCopyList, int maxUseNum) {
        // 找到主次品对,只取价格最高的1对
        Map<String, Pair<CartItem, CartItem>> bindItemPairMap = getMaxPriceBindItemPair(orgBindItemList);

        List<String> newItemIdList = new ArrayList<>();

        int suplyCnt = maxUseNum - itemIdList.size();
        Set<String> itemIdSet = new HashSet<>(itemIdList.size());

        for (String itemId : itemIdList) {
            Optional<CartItem> first = cartCopyList.stream().filter(c -> c.getItemId().equals(itemId)).findFirst();
            if (first.isPresent()) {
                CartItem item = first.get();
                String spuGroup = goodsSpuGroupMap.get(CartHelper.getSkuPackage(item));
                itemIdSet.add(spuGroup);

                Pair<CartItem, CartItem> bindItemPair = bindItemPairMap.get(spuGroup);
                if (bindItemPair == null) {
                    newItemIdList.add(itemId);
                    continue;
                }

                if (isTotalPriceHigher(bindItemPair, item)) {
                    newItemIdList.add(bindItemPair.getKey().getItemId());
                    newItemIdList.add(bindItemPair.getValue().getItemId());
                }
            }
        }
        if (suplyCnt <= 0) {
            return newItemIdList;
        }
        for (Map.Entry<String, Pair<CartItem, CartItem>> pairEntry : bindItemPairMap.entrySet()) {
            if (itemIdSet.contains(pairEntry.getKey())) {
                continue;
            }

            Pair<CartItem, CartItem> bindItemPair = pairEntry.getValue();
            newItemIdList.add(bindItemPair.getKey().getItemId());
            newItemIdList.add(bindItemPair.getValue().getItemId());

            suplyCnt--;
            if (suplyCnt == 0) {
                break;
            }
        }
        return newItemIdList;
    }

    /**
     * 获取每个品类最高价强绑定商品
     * @param orgBindItemList
     * @return
     */
	private Map<String, Pair<CartItem, CartItem>> getMaxPriceBindItemPair(List<CartItem> orgBindItemList) {
        List<CartItem> parentItemList = new ArrayList<>();
        Map<String, CartItem> parentItemIdCartItemMap = new HashMap<>();
        for (CartItem item : orgBindItemList) {
            if (StringUtils.isEmpty(item.getParentItemId())) {
                parentItemList.add(item);
            } else {
                parentItemIdCartItemMap.put(item.getParentItemId(), item);
            }
        }

        Map<String, Pair<CartItem, CartItem>> resultMap = new HashMap<>();
        Map<String, Long> maxPriceMap = new HashMap<>();

        for (CartItem parentItem : parentItemList) {
            CartItem childItem = parentItemIdCartItemMap.get(parentItem.getItemId());
            if (childItem == null) {
                log.warn("getMaxPriceBindItemPair error, parentItem not contain childItem, parentItem:{}", parentItem);
                continue;
            }
            long parentPrice = parentItem.getCartPrice() * parentItem.getCount() - parentItem.getReduceAmount();
            long childPrice = childItem.getCartPrice() * childItem.getCount() - childItem.getReduceAmount();
            long totalPrice = parentPrice + childPrice;

            String skuPackage = CartHelper.getSkuPackage(parentItem);
            String spuGroup = goodsSpuGroupMap.get(skuPackage);

            if (!maxPriceMap.containsKey(spuGroup) || totalPrice > maxPriceMap.get(spuGroup)) {
                resultMap.put(spuGroup, Pair.of(parentItem, childItem));
                maxPriceMap.put(spuGroup, totalPrice);
            }
        }

        return resultMap;
    }

    /**
     * 比较绑定商品对的总价格和单个商品的价格，判断绑定商品对的总价格是否更高
     *
     * @param bindItemPair 绑定的商品对，包含两个CartItem对象
     * @param item         单个商品的CartItem对象
     * @return 如果绑定商品对的总价格高于单个商品的价格，返回true；否则返回false
     */
    private boolean isTotalPriceHigher(Pair<CartItem, CartItem> bindItemPair, CartItem item) {
        long itemPrice = item.getCartPrice() * item.getCount() - item.getReduceAmount();
        long parentPrice = bindItemPair.getKey().getCartPrice() * bindItemPair.getKey().getCount() - bindItemPair.getKey().getReduceAmount();
        long childPrice = bindItemPair.getValue().getCartPrice() * bindItemPair.getValue().getCount() - bindItemPair.getValue().getReduceAmount();
        return (parentPrice + childPrice) > itemPrice;
    }

    /**
     * 获取交易资格信息列表
     *
     * @param context 本地上下文，包含外部数据映射
     * @return 交易资格信息列表，如果提供者为空则返回空列表
     * @throws BizError 业务异常
     */
    private List<TradeQualificationInfo> getQualificationInfos(LocalContext context) throws BizError {
        Map<ResourceExtType, ExternalDataProvider<?>> externalDataMap = context.getExternalDataMap();
        SubsidyPhoenixExternalProvider provider = (SubsidyPhoenixExternalProvider) externalDataMap.get(ResourceExtType.PHOENIX_SUBSIDY_QUALIFICATION);
        if (null == provider || null == provider.getData()) {
            return new ArrayList<>();
        }
        TradeInfoQueryListResp resp = provider.getData();

        return resp.getQualificationInfoList();
    }

    /*private void addBindToItemIdList(List<CartItem> orgBindItemList,List<String> itemIdList,List<CartItem> cartCopyList,int maxUseNum){
        // 找到主次品对,只取价格最高的1对
        Pair<CartItem, CartItem> bindItemPair = getMaxPriceBindItemPair(orgBindItemList);
        // 如果找到一对空调，和单品放到一起排序，取前maxUseNum名
        if (bindItemPair!=null){
            // 如果单品已经占满名额, 当强绑定总价大于 itemIdList中价格最小的商品时，将最小价格的itemId移除，将强绑定商品加入其中
            if (itemIdList.size()>=maxUseNum){
                String minPriceItemId = itemIdList.get(itemIdList.size() - 1);
                Optional<CartItem> first = cartCopyList.stream().filter(c -> c.getItemId().equals(minPriceItemId)).findFirst();
                if (first.isPresent()){
                    CartItem item = first.get();
                    long itemPrice = item.getCartPrice() * item.getCount() - item.getReduceAmount();
                    CartItem parentItem = bindItemPair.getKey();
                    CartItem childItem = bindItemPair.getValue();
                    long parentPrice = parentItem.getCartPrice() * parentItem.getCount() - parentItem.getReduceAmount();
                    long childPrice = childItem.getCartPrice() * childItem.getCount() - childItem.getReduceAmount();
                    long totalPrice=parentPrice+childPrice;
                    if (totalPrice>itemPrice){
                        itemIdList.remove(itemIdList.size()-1);
                        itemIdList.add(parentItem.getItemId());
                        itemIdList.add(childItem.getItemId());
                    }
                }
            }else {
                itemIdList.add(bindItemPair.getKey().getItemId());
                itemIdList.add(bindItemPair.getValue().getItemId());
            }
        }
    }*/

    /*private Pair<CartItem, CartItem> getMaxPriceBindItemPair(List<CartItem> orgBindItemList) {
        List<CartItem> parentItemList = new ArrayList<>();
        Map<String, CartItem> parentItemIdCartItemMap = new HashMap<>();
        for (CartItem item : orgBindItemList) {
            if (StringUtils.isEmpty(item.getParentItemId())) {
                parentItemList.add(item);
            } else {
                parentItemIdCartItemMap.put(item.getParentItemId(), item);
            }
        }
        long maxPrice=0L;
        Pair<CartItem, CartItem> resPair=null;
        for (CartItem parentItem : parentItemList) {
            CartItem childItem = parentItemIdCartItemMap.get(parentItem.getItemId());
            if (childItem == null) {
                log.warn("getMaxPriceBindItemPair error, parentItem not contain childItem, parentItem:{}", parentItem);
                continue;
            }
            long parentPrice = parentItem.getCartPrice() * parentItem.getCount() - parentItem.getReduceAmount();
            long childPrice = childItem.getCartPrice() * childItem.getCount() - childItem.getReduceAmount();
            long totalPrice=parentPrice+childPrice;
            if (totalPrice>maxPrice){
                resPair=Pair.of(parentItem,childItem);
                maxPrice=totalPrice;
            }
        }
        return resPair;
    }*/


/*    private void addBindToItemIdList(List<CartItem> cartCopyList,List<String> itemIdList, String idCard) {
        for (CartItem item : cartCopyList) {
            // 已经添加过的itemId过滤
            if (itemIdList.contains(item.getItemId())){
                continue;
            }
            // 不是强绑定的itemId过滤
            if (!SourceEnum.isBind(item.getSource())){
                continue;
            }
            // 活动池中不存在的商品过滤
            String skuPackage = CartHelper.getSkuPackage(item);
            Integer spuGroup = goodsSpuGroupMap.get(skuPackage);
            if (spuGroup == null) {
                log.warn("NewPurchaseSubsidyCondition spuGroup is null. promotionId:{} skuPackage:{}",promotionId,skuPackage);
                continue;
            }
            // 未超过品类限购&&数量为1的强绑定商品，可再次归为可参加补贴活动item
            Integer groupNum = activityRedisDao.getActPurchaseSubsidyLimitGroup(idCard, spuGroup);
            if (groupNum < limitGroup && item.getCount()==1) {
                itemIdList.add(item.getItemId());
            }
        }
    }*/

    private boolean updatePromotionItemIndex(CheckoutPromotionRequest request, LocalContext context,List<CartItem> cartList, List<String> itemIdList){
        if (itemIdList.isEmpty()){
            return false;
        }
        // 如果前端勾选使用优惠，构造可参与活动的商品索引
        if (request.getUsePurchaseSubsidy()) {
            List<GoodsIndex> goodsIndexList = new ArrayList<>();
            for (int idx = 0; idx < cartList.size(); idx++) {
                CartItem item = cartList.get(idx);
                if (itemIdList.contains(item.getItemId())) {
                    goodsIndexList.add(new GoodsIndex(item.getItemId(), idx));
                }
            }
            context.setGoodIndex(goodsIndexList);
            return true;
        } else {
            //如果不使用优惠，构造promotionInfo，仅用于展示
            PromotionInfo promotionInfo = constructPromotionInfo(itemIdList);
            context.setPromotion(promotionInfo);
            return false;
        }
    }


    private PromotionInfo constructPromotionInfo(List<String> joinedItemId) {
        PromotionInfo promotion = new PromotionInfo();
        promotion.setPromotionId(promotionId.toString());
        promotion.setType(String.valueOf(promotionType.getTypeId()));
        promotion.setJoinedItemId(joinedItemId);
        return promotion;
    }


    /**
     * 未实名认证,构造promotionInfo，用于引导用户认证
     */
    /*private Boolean checkItemPromotionInfo(CheckoutPromotionRequest request, LocalContext context) {
        List<CartItem> cartList = request.getSourceApi() == SourceApi.SUBMIT ? context.getCarts() : request.getCartList();
        List<GoodsIndex> goodsIndexList = new ArrayList<>();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            // 1、赠品、加价购、不能参加的活动类型过滤
            boolean online = StringUtils.isEmpty(request.getOrgCode());
            boolean filterResult = CartHelper.checkItemActQualify(item, promotionType.getTypeId(), online, null, null);
            if (!filterResult) {
                continue;
            }
            // 2、活动池中不存在的商品过滤
            String skuPackage = CartHelper.getSkuPackage(item);
            Integer spuGroup = goodsSpuGroupMap.get(skuPackage);
            if (spuGroup == null) {
                continue;
            }
            goodsIndexList.add(new GoodsIndex(item.getItemId(), idx));

        }
        if (!goodsIndexList.isEmpty()) {
            // 构造promotionInfo，默认不使用优惠，用于引导用户认证
            List<String> joinedItemId = new ArrayList<>();
            goodsIndexList.forEach(goodsIndex -> {
                joinedItemId.add(goodsIndex.getItemId());
            });
            PromotionInfo promotionInfo = constructPromotionInfo(joinedItemId);
            context.setPromotion(promotionInfo);
        }
    }*/

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof NewPurchaseSubsidyPromotionConfig promotionConfig)) {
            log.error("config is not instanceof NewPurchaseSubsidyPromotionConfig. config:{}", config);
            return;
        }
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        this.goodsSpuGroupMap = promotionConfig.getGoodsSpuGroupMap();
        this.limitTotal = promotionConfig.getLimitTotal();
        this.limitGroup = promotionConfig.getLimitGroup();
    }
}
