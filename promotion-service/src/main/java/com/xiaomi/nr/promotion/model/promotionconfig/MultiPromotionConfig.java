package com.xiaomi.nr.promotion.model.promotionconfig;

import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.enums.ActFrequencyEnum;
import com.xiaomi.nr.promotion.enums.OnOffLineEnum;
import com.xiaomi.nr.promotion.enums.OrgScopeEnum;
import com.xiaomi.nr.promotion.enums.UserGroupActionEnum;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ConfigMetaInfo;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalTime;
import java.util.List;
import java.util.Set;

/**
 * 默认配置实现
 *
 * <AUTHOR>
 * @date 2021/5/31
 */
@Slf4j
public class MultiPromotionConfig implements AbstractPromotionConfig {
    /**
     * 活动ID
     * alias promotionId
     */
    private Long promotionId;

    /**
     * 活动类型
     * {@link PromotionToolType#getTypeId()}
     */
    private PromotionToolType promotionType;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动开始时间（秒）
     */
    private long unixStartTime;

    /**
     * 活动结束时间（秒）
     */
    private long unixEndTime;

    /**
     * 是否周期性
     */
    private boolean daily;

    /**
     * 每周期开始时间
     */
    private LocalTime dailyStartTime;

    /**
     * 每周期结束时间
     */
    private LocalTime dailyEndTime;

    /**
     * 是否线下可用 1仅线上使用，2仅线下使用 3均可使用
     */
    private OnOffLineEnum offline;

    /**
     * 门店范围
     */
    private OrgScopeEnum orgScope;

    /**
     * 指定门店或区域ID
     */
    private List<String> selectOrgCodes;

    /**
     * 能参与的客户端列表
     */
    private List<String> selectClients;

    /**
     * 能参与人群范围
     */
    private UserGroupActionEnum groupAction;

    /**
     * 能参与的人群
     */
    private List<String> selectGroups;

    /**
     * 满足商品组列表(商品+条件）
     */
    private List<String> includeGoods;

    /**
     * 参与商品信息
     */
    private CompareItem joinGoods;

    /**
     * 满足商品组列表(sku or package）
     */
    private Set<String> includeSkuPackages;

    /**
     * 套装是否允许部分商品参加活动(套装是否拆分) 1允许 2不允许
     */
    private boolean checkPackage;

    /**
     * 销售来源（交易定义的，对应活动后台配置规则里的订单来源）
     */
    private List<String> saleSources;

    /**
     * 活动密码
     */
    private String accessCode;

    /**
     * 是否包邮
     */
    private Integer postfree;

    /**
     * 是否限制包邮次数：true / false
     */
    private Boolean postfreeIsnum;

    /**
     * 是否单品页展示
     */
    private Integer showProductView;

    /**
     * 包邮次数
     */
    private Integer postfreeNum;

    /**
     * 描述
     */
    private List<String> descPolicy;

    /**
     * 描述
     */
    private Boolean descIsShowAddOnItem;

    /**
     * 描述短名称
     */
    private String descShortName;

    /**
     * 是否强制添加
     */
    private Integer isRecommendForceAddPrice;

    /**
     * 频次限制 1不限制 2整个活动一次 3每天一次
     */
    private ActFrequencyEnum frequency;

    /**
     * 活动总数
     */
    private long actLimitNum;

    /**
     * 是否可使用优惠券
     */
    private boolean couponLimit;

    /**
     * 是否限制商品数量
     */
    private boolean numLimit;

    /**
     * 活动限制. 数据值：0-不限制， 没有限制就没有对应字段
     */
    private ActNumLimitRule numLimitRule;

    /**
     * 活动是否互斥 0-否 1-是 #新
     */
    private boolean actMutexLimit;

    /**
     * 互斥活动优先级
     */
    private List<String> actMutexes;

    /**
     * 基本信息
     */
    private ConfigMetaInfo metaInfo;

    /**
     * 渠道
     */
    private List<Integer> channels;

    /**
     * 活动规则文案
     */
    protected List<String> descRule;

    /**
     * 短规则文案
     */
    protected String descRuleIndex;

    /**
     * 渠道范围,
     * 仅channelCondition使用，后续废除
     */
    @Deprecated
    private List<Integer> channel;

    /**
     * 活动版本
     */
    private Integer version;


    @Override
    public Long getPromotionId() {
        return promotionId;
    }

    @Override
    public void setPromotionId(Long promotionId) {
        this.promotionId = promotionId;
    }

    @Override
    public PromotionToolType getPromotionType() {
        return promotionType;
    }

    @Override
    public void setPromotionType(PromotionToolType promotionType) {
        this.promotionType = promotionType;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public long getUnixStartTime() {
        return unixStartTime;
    }

    @Override
    public void setUnixStartTime(long unixStartTime) {
        this.unixStartTime = unixStartTime;
    }

    @Override
    public long getUnixEndTime() {
        return unixEndTime;
    }

    @Override
    public void setUnixEndTime(long unixEndTime) {
        this.unixEndTime = unixEndTime;
    }

    @Override
    public boolean isDaily() {
        return daily;
    }

    @Override
    public void setDaily(boolean daily) {
        this.daily = daily;
    }

    @Override
    public LocalTime getDailyStartTime() {
        return dailyStartTime;
    }

    @Override
    public void setDailyStartTime(LocalTime dailyStartTime) {
        this.dailyStartTime = dailyStartTime;
    }

    @Override
    public LocalTime getDailyEndTime() {
        return dailyEndTime;
    }

    @Override
    public void setDailyEndTime(LocalTime dailyEndTime) {
        this.dailyEndTime = dailyEndTime;
    }

    @Override
    public OnOffLineEnum getOffline() {
        return offline;
    }

    @Override
    public void setOffline(OnOffLineEnum offline) {
        this.offline = offline;
    }

    @Override
    public OrgScopeEnum getOrgScope() {
        return orgScope;
    }

    @Override
    public void setOrgScope(OrgScopeEnum orgScope) {
        this.orgScope = orgScope;
    }

    @Override
    public List<String> getSelectOrgCodes() {
        return selectOrgCodes;
    }

    @Override
    public void setSelectOrgCodes(List<String> selectOrgCodes) {
        this.selectOrgCodes = selectOrgCodes;
    }

    @Override
    public List<String> getSelectClients() {
        return selectClients;
    }

    @Override
    public void setSelectClients(List<String> selectClients) {
        this.selectClients = selectClients;
    }

    @Override
    public UserGroupActionEnum getGroupAction() {
        return groupAction;
    }

    @Override
    public void setGroupAction(UserGroupActionEnum groupAction) {
        this.groupAction = groupAction;
    }

    @Override
    public List<String> getSelectGroups() {
        return selectGroups;
    }

    @Override
    public void setSelectGroups(List<String> selectGroups) {
        this.selectGroups = selectGroups;
    }

    public List<String> getIncludeGoods() {
        return includeGoods;
    }

    public CompareItem getJoinGoods() {
        return joinGoods;
    }

    public void setJoinGoods(CompareItem joinGoods) {
        this.joinGoods = joinGoods;
    }

    @Override
    public Set<String> getIncludeSkuPackages() {
        return includeSkuPackages;
    }

    @Override
    public void setIncludeSkuPackages(Set<String> includeSkuPackages) {
        this.includeSkuPackages = includeSkuPackages;
    }

    public void setIncludeGoods(List<String> includeGoods) {
        this.includeGoods = includeGoods;
    }

    public boolean isCheckPackage() {
        return checkPackage;
    }

    public void setCheckPackage(boolean checkPackage) {
        this.checkPackage = checkPackage;
    }

    public List<String> getSaleSources() {
        return saleSources;
    }

    public void setSaleSources(List<String> saleSources) {
        this.saleSources = saleSources;
    }

    public String getAccessCode() {
        return accessCode;
    }

    public void setAccessCode(String accessCode) {
        this.accessCode = accessCode;
    }

    public ActFrequencyEnum getFrequency() {
        return frequency;
    }

    public void setFrequency(ActFrequencyEnum frequency) {
        this.frequency = frequency;
    }

    public long getActLimitNum() {
        return actLimitNum;
    }

    public void setActLimitNum(long actLimitNum) {
        this.actLimitNum = actLimitNum;
    }

    public boolean isCouponLimit() {
        return couponLimit;
    }

    public void setCouponLimit(boolean couponLimit) {
        this.couponLimit = couponLimit;
    }

    public boolean isNumLimit() {
        return numLimit;
    }

    public void setNumLimit(boolean numLimit) {
        this.numLimit = numLimit;
    }

    public ActNumLimitRule getNumLimitRule() {
        return numLimitRule;
    }

    public void setNumLimitRule(ActNumLimitRule numLimitRule) {
        this.numLimitRule = numLimitRule;
    }

    public boolean isActMutexLimit() {
        return actMutexLimit;
    }

    public void setActMutexLimit(boolean actMutexLimit) {
        this.actMutexLimit = actMutexLimit;
    }

    public List<String> getActMutexes() {
        return actMutexes;
    }

    public void setActMutexes(List<String> actMutexes) {
        this.actMutexes = actMutexes;
    }


    public Integer getPostfree() {
        return postfree;
    }

    public void setPostfree(Integer postfree) {
        this.postfree = postfree;
    }

    public Boolean getPostfreeIsnum() {
        return postfreeIsnum;
    }

    public void setPostfreeIsnum(Boolean postfreeIsnum) {
        this.postfreeIsnum = postfreeIsnum;
    }

    public Integer getPostfreeNum() {
        return postfreeNum;
    }

    public void setPostfreeNum(Integer postfreeNum) {
        this.postfreeNum = postfreeNum;
    }

    public List<String> getDescPolicy() {
        return descPolicy;
    }

    public void setDescPolicy(List<String> descPolicy) {
        this.descPolicy = descPolicy;
    }

    public Boolean getDescIsShowAddOnItem() {
        return descIsShowAddOnItem;
    }

    public void setDescIsShowAddOnItem(Boolean descIsShowAddOnItem) {
        this.descIsShowAddOnItem = descIsShowAddOnItem;
    }

    public String getDescShortName() {
        return descShortName;
    }

    public void setDescShortName(String descShortName) {
        this.descShortName = descShortName;
    }

    public Integer getIsRecommendForceAddPrice() {
        return isRecommendForceAddPrice;
    }

    public void setIsRecommendForceAddPrice(Integer isRecommendForceAddPrice) {
        this.isRecommendForceAddPrice = isRecommendForceAddPrice;
    }

    public Integer getShowProductView() {
        return showProductView;
    }

    public void setShowProductView(Integer showProductView) {
        this.showProductView = showProductView;
    }

    @Override
    public ConfigMetaInfo getMetaInfo() {
        return metaInfo;
    }

    @Override
    public void setMetaInfo(ConfigMetaInfo metaInfo) {
        this.metaInfo = metaInfo;
    }

    @Override
    public List<Integer> getChannels() {
        return this.channels;
    }

    @Override
    public void setChannels(List<Integer> channels) {
        this.channels = channels;
    }

    public List<String> getDescRule() {
        return descRule;
    }

    public void setDescRule(List<String> descRule) {
        this.descRule = descRule;
    }

    public String getDescRuleIndex() {
        return descRuleIndex;
    }

    public void setDescRuleIndex(String descRuleIndex) {
        this.descRuleIndex = descRuleIndex;
    }

    @Override
    @Deprecated
    public void setChannel(List<Integer> channel) {
        this.channel = channel;
    }

    @Override
    @Deprecated
    public List<Integer> getChannel() {
        return channel;
    }

    @Override
    public void setVersion(Integer version) {
        this.version = version;
    }

    @Override
    public Integer getVersion() {
        return version;
    }
}
