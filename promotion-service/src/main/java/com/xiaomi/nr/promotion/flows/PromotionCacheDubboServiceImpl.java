package com.xiaomi.nr.promotion.flows;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
//import com.xiaomi.nr.promotion.activity.pool.CarPromotionInstancePool;
import com.xiaomi.nr.promotion.activity.pool.PromotionInstancePool;
import com.xiaomi.nr.promotion.annotation.log.Log;
import com.xiaomi.nr.promotion.api.dto.PromotionCacheRequest;
import com.xiaomi.nr.promotion.api.dto.PromotionCacheResponse;
import com.xiaomi.nr.promotion.api.service.PromotionCacheDubboService;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.util.StringUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service(timeout = 3000, group = "${dubbo.group}", version = "1.0", retries = 0)
@ApiModule(value = "优惠缓存工具接口", apiInterface = PromotionCacheDubboService.class)
public class PromotionCacheDubboServiceImpl implements PromotionCacheDubboService {

    @Autowired
    private PromotionInstancePool carPromotionInstancePool;

    @Autowired
    private PromotionInstancePool promotionInstancePool;

    private static final String ACT_INDEX_KEY = "promotionId:{promotionId},promotionType:{promotionType},promotionName:{promotionName}";

    @ApiDoc("手动刷新汽车缓存")
    @Override
    @Log(name = "PromotionCacheDubboService#refreshLocalCache")
    public Result<String> refreshLocalCache(PromotionCacheRequest request) throws Exception {
        Integer bizPlatform = request.getBizPlatform();
        if (bizPlatform == null) {
            return Result.fail(GeneralCodes.ParamError, "bizPlatform is null");
        }

        try {
            log.info("refreshLocalCache, bizPlatform:{}", bizPlatform);
            if (Objects.equals(bizPlatform, BizPlatformEnum.NEW_RETAIL.getValue())) {
                // 3c
                promotionInstancePool.rebuildCacheTask();
            } else if (Objects.equals(bizPlatform, BizPlatformEnum.CAR.getValue())) {
                // car
                carPromotionInstancePool.rebuildCacheTask();
            } else if (Objects.equals(bizPlatform, BizPlatformEnum.MAINTENANCE_REPAIR.getValue())) {
                // maintenance
                carPromotionInstancePool.rebuildCacheTask();
            } else if (Objects.equals(bizPlatform, BizPlatformEnum.CAR_SHOP.getValue())) {
                // car_shop
                carPromotionInstancePool.rebuildCacheTask();
            } else {
                log.info("no support bizPlatform:{}", bizPlatform);
            }
            return Result.success("success");
        } catch (Exception e) {
            log.error("refreshLocalCache error", e);
            return Result.fromException(e, e.getMessage());
        }
    }

    @ApiDoc("缓存数据查询")
    @Override
    @Log(name = "PromotionCacheDubboService#searchLocalCache")
    public Result<PromotionCacheResponse> searchLocalCache(PromotionCacheRequest request) throws Exception {
        Integer bizPlatform = request.getBizPlatform();
        if (bizPlatform == null) {
            return Result.fail(GeneralCodes.ParamError, "bizPlatform is null");
        }

        try {
            log.info("searchLocalCache, bizPlatform:{}", bizPlatform);
            PromotionCacheResponse response = new PromotionCacheResponse();

            if (Objects.equals(bizPlatform, BizPlatformEnum.NEW_RETAIL.getValue())) {
                // 3c
                process3cResponse(request, response);
            } else if (Objects.equals(bizPlatform, BizPlatformEnum.CAR.getValue())) {
                // car
                processCarResponse(request, response);
            } else if (Objects.equals(bizPlatform, BizPlatformEnum.MAINTENANCE_REPAIR.getValue())) {
                // maintenance
                processCarResponse(request, response);
            } else if (Objects.equals(bizPlatform, BizPlatformEnum.CAR_SHOP.getValue())) {
                // car_shop
                processCarResponse(request, response);
            } else {
                log.info("no support bizPlatform:{}", bizPlatform);
            }
            return Result.success(response);
        } catch (Exception e) {
            log.error("refreshLocalCache error", e);
            return Result.fromException(e, e.getMessage());
        }
    }

    private void process3cResponse(PromotionCacheRequest request, PromotionCacheResponse response) {
        if (request.getFilterParam().isQueryActivityIndex()) {
            Map<Long, String> activityIdList = promotionInstancePool.getActivityIdListForTool(request.getActivityIds()).entrySet()
                    .stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> StringUtil.formatContent(ACT_INDEX_KEY,
                                    entry.getValue().getPromotionConfig().getPromotionId().toString(),
                                    String.valueOf(entry.getValue().getPromotionConfig().getPromotionType().getTypeId()),
                                    entry.getValue().getPromotionConfig().getName())
                    ));
            response.setActIndexList(activityIdList);
        }

        if (request.getFilterParam().isQueryActivityInfo() && CollectionUtil.isNotEmpty(request.getActivityIds())) {
            Map<Long, String> activityIdList = promotionInstancePool.getActivityIdListForTool(request.getActivityIds()).entrySet()
                    .stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> JSONUtil.toJsonStr(entry.getValue().getPromotionConfig())
                    ));
            response.setActsList(activityIdList);
        }

        if (request.getFilterParam().isQueryGoodsInfo()) {
            Map<String, List<Long>> sku2ActMapForTool = promotionInstancePool.getSku2ActMapForTool(request.getGoodsIds());
            response.setSku2ActMapForTool(sku2ActMapForTool);
        }

        if (request.getFilterParam().isQueryChannelsInfo()) {
            Map<Integer, List<Long>> channel2ActMapForTool = promotionInstancePool.getChannel2ActMapForTool(request.getChannelIds());
            response.setChannel2actsMapForTool(channel2ActMapForTool);
        }

        if (request.getFilterParam().isQueryDepartmentInfo()) {
            Map<Integer, List<Long>> department2ActMapForTool = promotionInstancePool.getDepartment2ActMapForTool(request.getDepartmentIds());
            response.setDepartment2actsMapForTool(department2ActMapForTool);
        }

        if (request.getFilterParam().isQueryOrgCodesInfo()) {
            Map<String, List<Long>> org2ActMapForTool = promotionInstancePool.getOrg2ActMapForTool(request.getOrgCodes());
            response.setOrgCode2actsMapForTool(org2ActMapForTool);
        }

        if (request.getFilterParam().isQueryCommonActivityInfo()) {
            List<Long> goodsCommonActivityIdsForTool = promotionInstancePool.getGoodsCommonActivityIdsForTool();
            response.setCommonActListForTool(goodsCommonActivityIdsForTool);
        }
    }

    private void processCarResponse(PromotionCacheRequest request, PromotionCacheResponse response) {
//        if (request.getFilterParam().isQueryActivityIndex()) {
//            Map<Long, String> activityIdList = carPromotionInstancePool.getActivityIdListForTool(request.getActivityIds()).entrySet()
//                    .stream()
//                    .collect(Collectors.toMap(
//                            Map.Entry::getKey,
//                            entry -> StringUtil.formatContent(ACT_INDEX_KEY,
//                                    entry.getValue().getPromotionConfig().getPromotionId().toString(),
//                                    String.valueOf(entry.getValue().getPromotionConfig().getPromotionType().getTypeId()),
//                                    entry.getValue().getPromotionConfig().getName())
//                    ));
//            response.setActIndexList(activityIdList);
//        }
//
//        if (request.getFilterParam().isQueryActivityInfo() && CollectionUtil.isNotEmpty(request.getActivityIds())) {
//            Map<Long, String> activityIdList = carPromotionInstancePool.getActivityIdListForTool(request.getActivityIds()).entrySet()
//                    .stream()
//                    .collect(Collectors.toMap(
//                            Map.Entry::getKey,
//                            entry -> JSONUtil.toJsonStr(entry.getValue().getPromotionConfig())
//                    ));
//            response.setActsList(activityIdList);
//        }
//
//        if (request.getFilterParam().isQueryGoodsInfo()) {
//            Map<String, List<Long>> sku2ActsMap = carPromotionInstancePool.getSku2ActsMapForTool(request.getGoodsIds());
//            response.setSku2ActMapForTool(sku2ActsMap);
//        }
//
//        if (request.getFilterParam().isQueryChannelsInfo()) {
//            Map<Integer, List<Long>> channel2ActsMap = carPromotionInstancePool.getChannel2ActsMapForTool(request.getChannelIds());
//            response.setChannel2actsMapForTool(channel2ActsMap);
//        }
//
//        if (request.getFilterParam().isQueryCommonActivityInfo()) {
//            List<Long> commonActList = carPromotionInstancePool.getCommonActListForTool();
//            response.setCommonActListForTool(commonActList);
//        }
//
//        if (request.getFilterParam().isQuerySortedActivityInfo()) {
//            List<Long> sortedActivityList = carPromotionInstancePool.getSortedActivityListForTool();
//            response.setSortActListForTool(sortedActivityList);
//        }
    }
}
