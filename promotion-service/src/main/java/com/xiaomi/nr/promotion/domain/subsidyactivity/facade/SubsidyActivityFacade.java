package com.xiaomi.nr.promotion.domain.subsidyactivity.facade;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.activity.pool.PromotionInstancePool;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.domain.subsidyactivity.service.mishop.MiShopSubsidyActivityService;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SubsidyActivityFacade {

    @Autowired
    private MiShopSubsidyActivityService service;
    
    @Autowired(required = false)
    private PromotionInstancePool promotionInstancePool;

    public void checkout(DomainCheckoutContext domainCheckoutContext) throws Exception{
        // 活动索引检索
        List<ActivityTool> activityTools = activitySearcher(domainCheckoutContext.getRequest());
        // 活动执行
        service.executeActivity(domainCheckoutContext,activityTools);

        //活动互斥：北京和广东国补只能参加一个，否则抛异常
        List<PromotionInfo> promotions = domainCheckoutContext.getContext().getPromotion();
        if (CollUtil.isNotEmpty(promotions)) {
            //判断40、42是否同时存在，若存在抛异常
            List<String> governmentTypes = Lists.newArrayList(String.valueOf(ActivityTypeEnum.GOVERNMENT_SUBSIDY.getValue()), String.valueOf(ActivityTypeEnum.NEW_PURCHASE_SUBSIDY.getValue()));
            List<String> collect = promotions.stream().map(PromotionInfo::getType).toList();
            boolean isMatchAll = collect.containsAll(governmentTypes);
            if (isMatchAll) {
                throw ExceptionHelper.create(GeneralCodes.InternalError, "订单:[" + domainCheckoutContext.getContext().getOrderId() + "]国补活动配置异常,同时命中多个国补活动");
            } else {
                Map<String, List<PromotionInfo>> promotionMap = promotions.stream().collect(Collectors.groupingBy(PromotionInfo::getType));
                List<PromotionInfo> governments = promotionMap.get(String.valueOf(ActivityTypeEnum.GOVERNMENT_SUBSIDY.getValue()));
                List<PromotionInfo> purchases = promotionMap.get(String.valueOf(ActivityTypeEnum.NEW_PURCHASE_SUBSIDY.getValue()));
                if ((CollUtil.isNotEmpty(governments) && governments.size() != 1) || (CollUtil.isNotEmpty(purchases) && purchases.size() != 1)) {
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "订单:[" + domainCheckoutContext.getContext().getOrderId() + "]国补活动配置异常,同时命中多个国补活动");
                }
            }
        }

    }
    
    private List<ActivityTool> activitySearcher(CheckoutPromotionRequest request) {
        return promotionInstancePool.activitySearcher(request.getChannel(), request.getOrgCode(), CartHelper.getSkuPackageList(request.getCartList()));
    }
}
