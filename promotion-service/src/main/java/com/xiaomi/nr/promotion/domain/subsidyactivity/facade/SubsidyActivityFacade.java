package com.xiaomi.nr.promotion.domain.subsidyactivity.facade;

import com.xiaomi.nr.promotion.activity.pool.PromotionInstancePool;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.domain.subsidyactivity.service.mishop.MiShopSubsidyActivityService;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.nr.promotion.util.CartHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class SubsidyActivityFacade {

    @Autowired
    private MiShopSubsidyActivityService service;
    
    @Autowired(required = false)
    private PromotionInstancePool promotionInstancePool;


    public void checkout(DomainCheckoutContext domainCheckoutContext) throws Exception{
        // 活动索引检索
        List<ActivityTool> activityTools = activitySearcher(domainCheckoutContext.getRequest());
        // 活动执行
        service.executeActivity(domainCheckoutContext,activityTools);


    }
    
    private List<ActivityTool> activitySearcher(CheckoutPromotionRequest request) {
        return promotionInstancePool.activitySearcher(request.getChannel(), request.getOrgCode(), CartHelper.getSkuPackageList(request.getCartList()));
    }
}
