package com.xiaomi.nr.promotion.bizplatform.impl;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.bizplatform.AbstractBaseBizPlatform;
import com.xiaomi.nr.promotion.domain.activity.impl.GroupBuyActivityDomain;
import com.xiaomi.nr.promotion.domain.coupon.impl.MiShopCouponDomain;
import com.xiaomi.nr.promotion.domain.ecard.impl.MiShopECardDomain;
import com.xiaomi.nr.promotion.domain.phoenix.impl.MiShopPhoenixDomain;
import com.xiaomi.nr.promotion.domain.redpackage.impl.MiShopRedPackageDomain;
import com.xiaomi.nr.promotion.domain.shoppingcoupon.impl.MiShopShoppingCouponDomain;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.springframework.stereotype.Component;

@Component
public class GroupBuyBizPlatform extends AbstractBaseBizPlatform {

    private BaseDomainList baseDomainList;

    @Override
    public BaseDomainList checkBaseDomainList() {
        return baseDomainList;
    }

    @Override
    public BizPlatformEnum getBiz() {
        return BizPlatformEnum.TUAN_GOU;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        baseDomainList=super.instanceBaseDomainList();
        baseDomainList.addResourceList(GroupBuyActivityDomain.class)
                .addResourceList(MiShopCouponDomain.class)//待删除
                .addResourceList(MiShopRedPackageDomain.class)//待删除
                .addResourceList(MiShopPhoenixDomain.class)//待删除
                .addResourceList(MiShopShoppingCouponDomain.class)//待删除
                .addResourceList(MiShopECardDomain.class);//待删除
    }

    @Override
    public void generateResponse(DomainCheckoutContext domainCheckoutContext) throws BizError {
        super.originalGenerateResponse(domainCheckoutContext);
    }

    @Override
    public void addResource(DomainCheckoutContext domainCheckoutContext) throws BizError {

    }

    @Override
    public void postProcessing(CheckoutPromotionRequest request, DomainCheckoutContext domainCheckoutContext) {

    }
}
