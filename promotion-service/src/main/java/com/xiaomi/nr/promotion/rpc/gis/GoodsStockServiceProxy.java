package com.xiaomi.nr.promotion.rpc.gis;

import cn.hutool.core.collection.CollUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.goods.gis.api.GoodsStockService;
import com.xiaomi.goods.gis.api.SkuInfoService;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoDTO;
import com.xiaomi.goods.gis.dto.sku.SkuMultiInfoV2Request;
import com.xiaomi.goods.gis.dto.sku.SkuMultiInfoV2Response;
import com.xiaomi.goods.gis.dto.stock.*;
import com.xiaomi.nr.promotion.activity.pool.ActSearchParam;
import com.xiaomi.nr.promotion.api.dto.enums.SsuIdTypeEnum;
import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.config.yaml.PromotionTotalZkConfig;
import com.xiaomi.nr.promotion.enums.MasterTypeEnum;
import com.xiaomi.nr.promotion.enums.ShipmentTypeEnum;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 商品库存
 *
 * <AUTHOR>
 * @date 2021/9/9
 */
@Component
@Slf4j
public class GoodsStockServiceProxy {

    @Reference(interfaceClass = GoodsStockService.class, version = "${gis.dubbo.version}", timeout = 500, group = "${gis.dubbo.group}", retries = 0)
    private GoodsStockService goodsStockService;

    @Reference(interfaceClass = SkuInfoService.class, version = "${gis.dubbo.version}", timeout = 2000, group = "${gis.dubbo.group}", retries = 0)
    private SkuInfoService skuInfoService;

    @Autowired
    private PromotionTotalZkConfig promotionTotalZkConfig;

    @Autowired
    private NacosConfig nacosConfig;

    @Autowired
    @Qualifier("gisStockAsyncTaskExecutor")
    private Executor gisStockAsyncTaskExecutor;

    //无地址库存缓存
    private static final Cache<String, GiftStockRespParam> STOCK_CACHE = CacheBuilder.newBuilder()
            .maximumSize(3000)
            .expireAfterWrite(3, TimeUnit.SECONDS)
            .build();

    /**
     * 赠品库存查询---异步接口
     * @param masterIdList
     * @param slaveIdList
     * @param region
     * @param queryCache
     * @return
     */
    @Async("gisStockAsyncTaskExecutor")
    public ListenableFuture<Map<String, GiftStockRespParam>> getGiftStockBatchAsync(List<ActSearchParam.GoodsInSearch> masterIdList,
                                                                             List<Long> slaveIdList, Region region,
                                                                             String stockChannel,
                                                                             boolean queryCache) {
        try {
            List<GiftStockRespParam> giftStocks = this.getGiftStockBatch(masterIdList, slaveIdList, region, stockChannel, queryCache);
            Map<String, GiftStockRespParam> giftStockMap = Maps.newHashMap();
            giftStocks.forEach(giftStock -> giftStockMap.put(giftStock.getMasterId() + "_" + giftStock.getId(), giftStock));
            return AsyncResult.forValue(giftStockMap);
        } catch (Exception e) {
            log.error("invoke gis stock error", e);
            return AsyncResult.forExecutionException(e);
        }
    }

    /**
     * 赠品库存查询---新接口
     * 将master 分为 3个为1个桶， 将slave 分为 30个为1桶
     * @param masterIdList
     * @param slaveIdList
     * @param region
     * @param queryCache
     * @return
     */
    public List<GiftStockRespParam> getGiftStockBatch(List<ActSearchParam.GoodsInSearch> masterIdList,
                                                      List<Long> slaveIdList, Region region,
                                                      String stockChannel, boolean queryCache) {
        List<GiftStockRespParam> responseList = Collections.synchronizedList(new ArrayList<>());

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(masterIdList) || org.apache.commons.collections4.CollectionUtils.isEmpty(slaveIdList)) {
            log.warn("getGiftStockByBatch masterId or idList is null. masterId:{}, slaveId:{}", masterIdList,
                    slaveIdList);
            return responseList;
        }

        // split list
        List<List<ActSearchParam.GoodsInSearch>> masterBlock = CollUtil.split(masterIdList, 3);
        List<List<Long>> slaveBlock = CollUtil.split(slaveIdList, 30);

        int i = 0;
        CompletableFuture<?>[] futures = new CompletableFuture[masterBlock.size() * slaveBlock.size()];
        for (List<ActSearchParam.GoodsInSearch> masterIds : masterBlock) {
            for (List<Long> slaveIds : slaveBlock) {
                if (i > futures.length - 1) {
                    log.error("getGiftStockBatch loop exceed limit {}", futures.length);
                    break;
                }
                futures[i++] = CompletableFuture.runAsync(() -> {
                    try {
                        responseList.addAll(this.getGiftStockByBatch(masterIds, slaveIds,
                                region, stockChannel,
                                queryCache));
                    } catch (Exception e) {
                        log.error("invoke gis stock error", e);
                    }
                }, gisStockAsyncTaskExecutor);
            }
        }
        CompletableFuture.allOf(futures).join();

        return responseList;
    }


    /**
     * 异步获取赠品库存状态
     *
     * @param masterId  主品GoodsId
     * @param isGoods   是否Goods true goods, false: commodity
     * @param idList    赠品GoodsId列表
     * @param orgCode   门店Code
     * @param region    区域信息
     * @param isOrgTool 是否门店信息
     * @return 结果信息
     */
    @Async("gisStockAsyncTaskExecutor")
    public ListenableFuture<Map<Long, Long>> getGiftStockAsync(Long masterId, boolean isGoods, Set<Long> idList, String orgCode, Region region, boolean isOrgTool, Integer shipmentType) {
        try {
            List<GiftStockRespParam> respParamList = getGiftStock(masterId, isGoods, idList, orgCode, region, isOrgTool, shipmentType);
            Map<Long, Long> stockMap = Optional.ofNullable(respParamList).orElse(Collections.emptyList()).stream()
                    .collect(Collectors.toMap(GiftStockRespParam::getId, GiftStockRespParam::getStockNum, (val1, val2) -> val1));
            return AsyncResult.forValue(stockMap);
        } catch (Exception e) {
            log.error("invoke gis stock error. error", e);
            return AsyncResult.forExecutionException(e);
        }
    }

    /**
     * 异步获取赠品库存状态--根据区域
     *
     * @return 结果信息
     */
    @Async("gisStockAsyncTaskExecutor")
    public ListenableFuture<Map<Long, Long>> getGoodsStockNumAsync(List<Long> goodsIds, Region region) {
        try {
            // List<GiftStockRespParam> respParamList = getGiftStock(masterId, isGoods, idList, orgCode, region, isOrgTool, shipmentType);
            List<QueryStockNumResult> goodsStockNum = getGoodsStockNum(goodsIds, region);
            Map<Long, Long> stockMap = Optional.ofNullable(goodsStockNum).orElse(Collections.emptyList()).stream()
                    .collect(Collectors.toMap(QueryStockNumResult::getGoodsId, QueryStockNumResult::getStockNum, (val1, val2) -> val1));
            return AsyncResult.forValue(stockMap);
        } catch (Exception e) {
            log.error("invoke gis stock error. error", e);
            return AsyncResult.forExecutionException(e);
        }
    }

    public List<GiftStockRespParam> getGiftStockByBatch(List<ActSearchParam.GoodsInSearch> masterIdList,
                                                        List<Long> slaveIdList, Region region,
                                                        String stockChannel,
                                                        boolean queryCache) throws BizError {

        if (CollectionUtils.isEmpty(masterIdList) || CollectionUtils.isEmpty(slaveIdList)) {
            log.warn("getGiftStockByBatch masterId or idList is null. masterId:{}, idList:{}", masterIdList, slaveIdList);
            return Lists.newArrayList();
        }

        // build result list and build request list
        List<GiftStockRespParam> resultList = Lists.newArrayList();
        List<GiftStockReqParam> requestList = Lists.newArrayList();
        for (ActSearchParam.GoodsInSearch master : masterIdList) {
            for (Long slave : slaveIdList) {

                boolean queryCacheSuccess = false;
                // query cache
                if (queryCache && nacosConfig.isGiftStockCacheSwitch()) {
                    log.debug("giftStockCache enable.");
                    if (!hasRegion(region)) {
                        log.debug("use giftStockCache");
                        String cacheKey = this.generateCacheKey(master.getSsuId(), slave);
                        GiftStockRespParam resp = STOCK_CACHE.getIfPresent(cacheKey);
                        if (Objects.nonNull(resp)) {
                            resultList.add(resp);
                            queryCacheSuccess = true;
                        }
                    }
                }

                // 缓存查询不成功或不查询缓存 则进行rpc
                if (!queryCacheSuccess) {
                    requestList.add(buildGiftRequest(master, slave, stockChannel, region));
                }
            }
        }


        List<GiftStockRespParam> remoteResponse = doGetGiftStockByReqList(requestList);


        if (queryCache && nacosConfig.isGiftStockCacheSwitch()) {
            //仅缓存region=null, orgCode == null的库存,
            if (!hasRegion(region)) {
                refreshStockCache(remoteResponse);
            }
        }

        resultList.addAll(remoteResponse);
        return resultList;
    }

    private GiftStockReqParam buildGiftRequest(ActSearchParam.GoodsInSearch masterId, Long slaveId, String stockChannel , Region region) {
        GiftStockReqParam request = new GiftStockReqParam();
        request.setMasterType(MasterTypeEnum.GOODS.getType());
        if (SsuIdTypeEnum.PACKAGE.code.equals(masterId.getSsuType())) {
            request.setMasterType(MasterTypeEnum.GROUP.getType());
        }
        request.setMasterId(masterId.getSsuId());
        request.setId(slaveId);
        request.setStockChannel(stockChannel);
        request.setRegion(region);
        return request;
    }

    /**
     *
     * @param reqParamList
     * @return
     * @throws BizError
     */
    public List<GiftStockRespParam> doGetGiftStockByReqList(List<GiftStockReqParam> reqParamList) throws BizError {
        long startTime = System.currentTimeMillis();
        // 如果服务降级
        if (nacosConfig.isGiftStockReqFallback()) {
            log.info("service giftStock fallback switch on. currentTime:{}", GsonUtil.toJson(startTime));
            return Lists.newArrayList();
        }

        GoodsGiftStockRequest request = new GoodsGiftStockRequest();
        request.setGiftStockRequest(reqParamList);
        // 请求数据
        Result<GoodsGiftStockResponse> result;
        try {
            result = goodsStockService.getGiftStock(request);
        } catch (Exception e) {
            log.error("invoke rpc getGiftStock error. request:{}, err", GsonUtil.toJson(request), e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
        // 处理响应
        log.info("invoke rpc getGiftStock. request:{}, result:{} ws:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), System.currentTimeMillis() - startTime);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc getGiftStock fail. request:{}, code:{} message:{}", request, result.getCode(), result.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "获取赠品是否有库存失败");
        }
        if (result.getData() == null) {
            log.error("invoke rpc getGiftStock data error. request:{}, response:{}", request, result);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "获取库存数据异常");
        }
        return result.getData().getGiftStockResponse();
    }



    /**
     * 获取赠品库存状态
     *
     * @param masterId  主品GoodsId
     * @param isGoods   是否Goods true goods, false: commodity
     * @param idList    赠品GoodsId列表
     * @param orgCode   门店Code
     * @param region    区域信息
     * @param isOrgTool 是否门店信息
     * @return 结果信息
     * @throws BizError 业务异常
     */
    public List<GiftStockRespParam> doGetGiftStock(Long masterId, boolean isGoods, Set<Long> idList, String orgCode, Region region, boolean isOrgTool, Integer shipmentType) throws BizError {
        long startTime = System.currentTimeMillis();
        if (masterId == null || CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        // 如果服务降级
        if (promotionTotalZkConfig.isGiftStockReqFallback()) {
            log.info("service giftStock fallback switch on. masterId:{}", masterId);
            return Lists.newArrayList();
        }
        // 封装参数
        List<GiftStockReqParam> giftStockRequestList = idList.stream()
                .map(id -> initStockReqParam(masterId, isGoods, id, orgCode, region, isOrgTool, shipmentType)).collect(Collectors.toList());

        GoodsGiftStockRequest request = new GoodsGiftStockRequest();
        request.setGiftStockRequest(giftStockRequestList);
        // 请求数据
        Result<GoodsGiftStockResponse> result;
        try {
            result = goodsStockService.getGiftStock(request);
        } catch (Exception e) {
            log.error("invoke rpc getGiftStock error. request:{}, err", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
        // 处理响应
        log.info("invoke rpc getGiftStock. request:{}, result:{} ws:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), System.currentTimeMillis() - startTime);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc getGiftStock fail. request:{}, code:{} message:{}", request, result.getCode(), result.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "获取赠品是否有库存失败");
        }
        if (result.getData() == null) {
            log.error("invoke rpc getGiftStock data error. request:{}, response:{}", request, result);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "获取库存数据异常");
        }
        return result.getData().getGiftStockResponse();
    }

    public List<GiftStockRespParam> getGiftStock(Long masterId, boolean isGoods, Set<Long> idList, String orgCode, Region region, boolean isOrgTool, Integer shipmentType) throws BizError {

        if (masterId == null || CollectionUtils.isEmpty(idList)) {
            log.warn("getGiftStock masterId or idList is null. masterId:{}, idList:{}", masterId, idList);
            return Lists.newArrayList();
        }

        //缓存降级 缓存region=null, orgCode == null的库存,
        Set<Long> remoteQueryList = new HashSet<>(idList);

        Map<Long, GiftStockRespParam> cacheMap = new HashMap<>();
        if (promotionTotalZkConfig.isGiftStockCacheSwitch()) {
            log.debug("giftStockCache enable.");
            if (!hasRegion(region) && StringUtils.isEmpty(orgCode)) {
                cacheMap = queryStockFromLocalCache(masterId, idList);
                log.debug("giftStockCache. cacheMap:{}", GsonUtil.toJson(cacheMap));

                Map<Long, GiftStockRespParam> checkMap = cacheMap;
                remoteQueryList.removeIf(id -> checkMap.containsKey(id));
            }
        } else {
            log.debug("giftStockCache disable");
        }

        List<GiftStockRespParam> remoteResponse = doGetGiftStock(masterId, isGoods, remoteQueryList, orgCode, region, isOrgTool, shipmentType);

        if (promotionTotalZkConfig.isGiftStockCacheSwitch()) {
            //仅缓存region=null, orgCode == null的库存,
            if (!hasRegion(region) && StringUtils.isEmpty(orgCode)) {
                refreshStockCache(remoteResponse);
            }
        }

        log.info("giftStockCache masterId:{} allSize:{}, cacheSize:{}, remoteSize:{}.", masterId, idList.size(), cacheMap.size(), remoteResponse.size());

        remoteResponse.addAll(new ArrayList<>(cacheMap.values()));
        return remoteResponse;

    }

    private Map<Long, GiftStockRespParam> queryStockFromLocalCache(Long masterId, Set<Long> slaveIds) {
        Map<Long, GiftStockRespParam> result = new HashMap<>();
        try {

            Set<String> keyList = slaveIds.stream()
                    .map(id -> generateCacheKey(masterId, id)).collect(Collectors.toSet());
            Map<String, GiftStockRespParam> cacheHitMap = STOCK_CACHE.getAllPresent(keyList);

            for (GiftStockRespParam cacheItem : cacheHitMap.values()) {
                result.put(cacheItem.getId(), cacheItem);
            }
        } catch (Exception e) {
            log.error("queryStockFromLocalCache error", e);
        }
        return result;
    }

    private void refreshStockCache(List<GiftStockRespParam> respParamList) {
        try {
            Map<String, GiftStockRespParam> refreshMap = new HashMap<>();

            for (GiftStockRespParam giftStockResp : respParamList) {
                String key = generateCacheKey(giftStockResp.getMasterId(), giftStockResp.getId());
                refreshMap.put(key, giftStockResp);
            }
            log.debug("giftStockCache. refreshMap:{}", GsonUtil.toJson(refreshMap));
            STOCK_CACHE.putAll(refreshMap);
        } catch (Exception e) {
            log.error("refreshStockCache error", e);
        }

    }

    private String generateCacheKey (Long masterId, Long salveId) {
        return String.join("_", String.valueOf(masterId),  String.valueOf(salveId));
    }

    /**
     * 获取赠品库存状态
     * TODO HZC
     *
     * @return 结果信息
     * @throws BizError 业务异常
     */
    public List<QueryStockNumResult> getGoodsStockNum(List<Long> goodsIds, Region region) throws BizError {
        long startTime = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(goodsIds) || region == null) {
            return null;
        }
        GoodsStockNumRequest request = new GoodsStockNumRequest();
        List<QueryStockNumParam> queryStockNumParams = new ArrayList<>();
        for (Long goodsId : goodsIds) {
            QueryStockNumParam queryStockNumParam = new QueryStockNumParam();
            queryStockNumParam.setGoodsId(goodsId);
            queryStockNumParam.setRegion(region);
            queryStockNumParam.setStockType(1);
            queryStockNumParams.add(queryStockNumParam);
        }
        request.setQueryStockNumParamList(queryStockNumParams);
        request.setStockChannel("DELIVERY");
        request.setClientId(180100031052L);
        request.setUseRegion(true);
        Result<GoodsStockNumResponse> result;
        try {
            result = goodsStockService.getGoodsStockNum(request);
        } catch (Exception e) {
            log.error("invoke rpc getGoodsStockNum error. request:{}, err", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
        // 处理响应
        log.info("invoke rpc getGoodsStockNum. request:{}, result:{} ws:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), System.currentTimeMillis() - startTime);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc getGoodsStockNum fail. request:{}, code:{} message:{}", request, result.getCode(), result.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "获取赠品是否有库存失败");
        }
        if (result.getData() == null) {
            log.error("invoke rpc getGoodsStockNum data error. request:{}, response:{}", request, result);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "获取库存数据异常");
        }
        return result.getData().getResponse();
    }

    /**
     * 初始化请求对象
     *
     * @param masterId  主品ID
     * @param id        赠品ID
     * @param orgCode   门店Code
     * @param region    区域信息
     * @param isOrgTool 是否门店信息
     * @return 请求对象
     */
    private GiftStockReqParam initStockReqParam(Long masterId, boolean isGoods, Long id, String orgCode, Region region, boolean isOrgTool, Integer shipmentType) {
        String masterType = isGoods ? "goods" : "commodity";
        boolean hasRegion = hasRegion(region);

        GiftStockReqParam reqParam = new GiftStockReqParam();
        reqParam.setMasterId(masterId);
        reqParam.setMasterType(masterType);
        reqParam.setId(id);
        if (!isOrgTool) {
            if (hasRegion) {
                reqParam.setRegion(region);
            }
        } else {
            // region + orgId cps大仓库存
            // orgId 门店库存
            if (Objects.equals(ShipmentTypeEnum.CPS.getVal(), shipmentType)) {
                reqParam.setRegion(region);
                reqParam.setOrgId(orgCode);
            } else if (Objects.equals(ShipmentTypeEnum.STORE.getVal(), shipmentType)) {
                reqParam.setOrgId(orgCode);
            }
        }
        return reqParam;
    }

    private boolean hasRegion(Region region) {
        return Objects.nonNull(region) && !Objects.equals(region.getProvince(), 0) && !Objects.equals(region.getCity(), 0)
                && !Objects.equals(region.getArea(), 0) && !Objects.equals(region.getDistrict(), 0);
    }

    public Map<Long, GoodsMultiInfoDTO> getSkuBaseInfoByStrList(List<String> skuList) throws BizError {
        List<Long> stringList = skuList.stream().map(Long::valueOf).collect(Collectors.toList());
        return getSkuBaseInfo(stringList);
    }

    public Map<Long, GoodsMultiInfoDTO> getSkuBaseInfo(List<Long> skuList) throws BizError {
        if (CollectionUtils.isEmpty(skuList) ) {
            return Collections.emptyMap();
        }
        HashSet<Long> skuSet = new HashSet<>(skuList);
        if (CollectionUtils.isEmpty(skuSet)){
            return Collections.emptyMap();
        }
        skuList = new ArrayList<>(skuSet);
        long startTime = System.currentTimeMillis();
        SkuMultiInfoV2Request request = new SkuMultiInfoV2Request();
        request.setSkuList(skuList);
        request.setNeedPrice(false);
        request.setNeedSpec(false);
        Result<SkuMultiInfoV2Response> result;
        try {
            result = skuInfoService.getSkuMultiInfoV2(request);
        } catch (Exception e) {
            log.error("invoke rpc getSkuBaseInfo error. request:{}, err", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
        // 处理响应
        log.info("invoke rpc getGiftStock. request:{}, result:{} ws:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), System.currentTimeMillis() - startTime);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc getSkuBaseInfo fail. request:{}, code:{} message:{}", request, result.getCode(), result.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "获取商品名称信息失败");
        }
        if (result.getData() == null || result.getData().getSkuMap().size() != skuList.size()) {
            log.error("invoke rpc getSkuBaseInfo data error. request:{}, response:{}", request, result);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "获取商品名称数据异常");
        }
        return result.getData().getSkuMap();
    }


}
