package com.xiaomi.nr.promotion.domain.coupon.service.mishop;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.service.base.type.AbstractCashCoupon;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.enums.CouponQuotaTypeEnum;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import com.xiaomi.nr.promotion.enums.OrderShipmentIdEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 现金券
 *
 * <AUTHOR>
 * @date 2022/3/3
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class CashCoupon extends AbstractCashCoupon {


    @Override
    public Long getCouponId() {
        return id;
    }

    @Override
    public CouponTypeEnum getCouponType() {
        return CouponTypeEnum.CASH;
    }

    @Override
    public Coupon generateCartCoupon() {
        return initCoupon();
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.COUPON_CASH;
    }

    @Override
    public CouponCheckoutResult checkoutCoupon(CheckoutPromotionRequest request, CheckoutContext context) throws BizError{
        // 筛选满足商品列表
        List<CartItem> cartList = request.getCartList();
        List<GoodsIndex> indexList = matchCartList(cartList, context, joinGoods, (long) getCouponType().getType());

        if (CollectionUtils.isEmpty(indexList)) {
            CouponCheckoutResult result = new CouponCheckoutResult();
            result.setAllow(false);
            result.setCouponId(this.id);
            if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                result.setCouponCode(checkoutCoupon.getCouponCode());
            }
            result.setUnusableCode(ErrCode.ERR_COUPON_NO_VALID_GOODS.getCode());
            result.setUnusableReason("订单中不含可用券商品");
            result.setKeyDataUnusable("rdata_invalid_coupon_reason");
        }
        // 计算符合总价
        ValidGoods validGoods = CartHelper.buildValidGoods(cartList, indexList);


        // 判断
        CouponCheckoutResult result = new CouponCheckoutResult();
        Pair<Boolean, String> pair = isSatisfiedQuota(validGoods);
        long cartTotalPrice = CartHelper.getTotalPrice(cartList);


        // 构建结果
        // 条件满足
        if (pair.getLeft()) {
            result.setCouponId(this.id);
            if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                result.setCouponCode(checkoutCoupon.getCouponCode());
            }
            result.setAllow(true);
            result.setValidGoodsPrice(validGoods.getValidPrice());
            result.setValidGoods(indexList);
            // 计算减免金额
            Long reduceMoney = getReduce(cartTotalPrice, validGoods);


            result.setReduceAmount(reduceMoney);
            result.setCouponReduce(checkoutCoupon.getPromotionValue());
        } else {
            result.setCouponId(this.id);
            if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                result.setCouponCode(checkoutCoupon.getCouponCode());
            }
            result.setAllow(false);
            result.setUnusableReason("未满足优惠券使用条件");
            result.setUnusableCode(ErrCode.ERR_COUPON_MONEY_NUM.getCode());
            result.setKeyDataUnusable(pair.getRight());
            result.setCouponReduce(checkoutCoupon.getPromotionValue());
        }

        return result;
    }

    protected Long getReduce(long cartTotalPrice, ValidGoods validGoods) {
        CouponQuotaTypeEnum quotaType = CouponQuotaTypeEnum.valueOf(checkoutCoupon.getType());
        if (quotaType == null) {
            return 0L;
        }
        long reduceMoney = checkoutCoupon.getPromotionValue();

        reduceMoney = Math.min(reduceMoney, validGoods.getValidPrice());

        // 减的金额需小于当前总价，消除0元订单
        if (reduceMoney >= 1 && reduceMoney == validGoods.getValidPrice() && cartTotalPrice == validGoods.getValidPrice()) {
            reduceMoney -= 1L;
        }
        return reduceMoney;
    }

    @Override
    protected ValidGoods buildValidGoods(List<CartItem> cartList, List<GoodsIndex> goodsInd) {
        return null;
    }

    @Override
    protected List<GoodsIndex> matchCartList(List<CartItem> cartList, CheckoutContext context) throws BizError {
        return null;
    }

    @Override
    protected long getCartTotalPrice(List<CartItem> cartList) {
        return 0;
    }

    public void updateCartsReduce(CheckoutPromotionRequest request, CheckoutContext context, CouponCheckoutResult result) {
        //处理商品分摊
        updateItemReduce(request.getCartList(), result);

        //处理邮费
        if (request.getShipmentId() != OrderShipmentIdEnum.FLASH_POST.val) {

            updateExpressInfo(context, result);
        }
        //更新context
        updateCommonInfo(request, context);
    }

    @Override
    public boolean load(CheckoutCoupon checkoutCoupon) throws BizError {
        this.id = checkoutCoupon.getCouponId();
        this.checkoutCoupon = checkoutCoupon;
        CompareItem validGoods = new CompareItem();
        if (checkoutCoupon.getValidSkuList() != null) {

            List<String> skuList = checkoutCoupon.getValidSkuList()
                    .stream().map(String::valueOf)
                    .collect(Collectors.toList());
            validGoods.setSku(skuList);
        }
        if (checkoutCoupon.getValidPackageList() != null) {
            List<String> packageList = checkoutCoupon.getValidPackageList()
                    .stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());

            validGoods.setPackages(packageList);
        }
        if (checkoutCoupon.getValidGoodsList() != null) {
            List<String> ssuList = checkoutCoupon.getValidGoodsList()
                    .stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());

            validGoods.setCommodity(ssuList);
        }

        this.joinGoods = validGoods;
        return true;

    }
}
