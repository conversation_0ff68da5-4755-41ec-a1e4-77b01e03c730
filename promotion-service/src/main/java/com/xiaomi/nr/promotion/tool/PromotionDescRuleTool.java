package com.xiaomi.nr.promotion.tool;


import com.google.common.collect.Lists;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoDTO;
import com.xiaomi.nr.promotion.constant.PromotionTextConstant;
import com.xiaomi.nr.promotion.entity.redis.*;
import com.xiaomi.nr.promotion.enums.PolicyQuotaTypeEnum;
import com.xiaomi.nr.promotion.enums.SendTypeEnum;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyGiftPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.GiftPromotionConfig;
import com.xiaomi.nr.promotion.util.NumberUtil;
import com.xiaomi.nr.promotion.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.xiaomi.nr.promotion.enums.PolicyQuotaTypeEnum.*;

/**
 * 促销文案工具
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PromotionDescRuleTool {
    /**
     * 生成下单立减规则文案(短规则）
     *
     * @param reduceAmount 立减金额（分）
     * @return 规则文案
     */
    public String generateBuyReduceItemRuleIndex(Long reduceAmount) {
        String money = NumberUtil.amountConvertF2YStr(reduceAmount);
        return StringUtil.formatContent(PromotionTextConstant.RULE_INDEX_TEXT_BUY_REDUCE, money);
    }

    /**
     * 生成满减活动规则(短规则）
     *
     * @param levelList 阶梯列表
     * @return 活动规则
     */
    public String generateReduceDescRuleIndex(List<QuotaLevel> levelList) {
        if (CollectionUtils.isEmpty(levelList)) {
            return "";
        }
        // 最高阶梯
        QuotaLevel firstLevel = levelList.get(0);
        if (CollectionUtils.isEmpty(firstLevel.getQuotas())) {
            return "";
        }
        long reduceMoney = firstLevel.getReduceMoney();
        long maxReducePrice = firstLevel.getMaxReducePrice();
        QuotaEle quotaEle = firstLevel.getQuotas().get(0);
        PolicyQuotaTypeEnum quotaType = PolicyQuotaTypeEnum.getQuotaType(quotaEle.getType());
        Integer count = quotaEle.getCount();
        Long money = quotaEle.getMoney();
        // 普通满减
        if (POLICY_QUOTA_MONEY == quotaType) {
            return StringUtil.formatContent(PromotionTextConstant.REDUCE_MONEY_SHORT_TEXT, NumberUtil.amountConvertF2YStr(money), NumberUtil.amountConvertF2YStr(reduceMoney));
        }
        // 满件
        if (POLICY_QUOTA_NUM == quotaType) {
            if (levelList.size() == 1 && count == 1) {
                return StringUtil.formatContent(PromotionTextConstant.REDUCE_SINGLE_SHORT_TEXT, NumberUtil.amountConvertF2YStr(reduceMoney));
            }
            return StringUtil.formatContent(PromotionTextConstant.REDUCE_COUNT_SHORT_TEXT, String.valueOf(count), NumberUtil.amountConvertF2YStr(reduceMoney));
        }
        // 每满额
        if (POLICY_QUOTA_PER_MONEY == quotaType) {
            if (maxReducePrice > 0L) {
                return StringUtil.formatContent(PromotionTextConstant.REDUCE_PRE_MONEY_SHORT_MAX_TEXT, NumberUtil.amountConvertF2YStr(money), NumberUtil.amountConvertF2YStr(reduceMoney), NumberUtil.amountConvertF2YStr(maxReducePrice));
            }
            return StringUtil.formatContent(PromotionTextConstant.REDUCE_PRE_MONEY_SHORT_TEXT, NumberUtil.amountConvertF2YStr(money), NumberUtil.amountConvertF2YStr(reduceMoney), NumberUtil.amountConvertF2YStr(maxReducePrice));
        }
        // 每满件
        if (POLICY_QUOTA_PER_NUM == quotaType) {
            if (levelList.size() == 1 && count == 1) {
                return StringUtil.formatContent(PromotionTextConstant.REDUCE_SINGLE_SHORT_TEXT, NumberUtil.amountConvertF2YStr(reduceMoney));
            }
            return StringUtil.formatContent(PromotionTextConstant.REDUCE_PRE_NUM_SHORT_TEXT, String.valueOf(count), NumberUtil.amountConvertF2YStr(reduceMoney));
        }
        return "";
    }

    /**
     * 生成满折活动规则(短规则）
     *
     * @param levelList 阶梯列表
     * @return 活动规则
     */
    public String generateDiscountDescRuleIndex(List<QuotaLevel> levelList) {
        if (CollectionUtils.isEmpty(levelList)) {
            return "";
        }
        // 最高阶梯
        QuotaLevel firstLevel = levelList.get(0);
        if (CollectionUtils.isEmpty(firstLevel.getQuotas())) {
            return "";
        }
        long reduceDiscount = firstLevel.getReduceDiscount();
        QuotaEle quotaEle = firstLevel.getQuotas().get(0);
        PolicyQuotaTypeEnum quotaType = PolicyQuotaTypeEnum.getQuotaType(quotaEle.getType());
        Integer count = quotaEle.getCount();
        Long money = quotaEle.getMoney();
        if (POLICY_QUOTA_NUM == quotaType || POLICY_QUOTA_PER_NUM == quotaType) {
            // 满一件
            if (levelList.size() == 1 && count == 1) {
                return StringUtil.formatContent(PromotionTextConstant.DISCOUNT_ONE_SHORT_TEXT, NumberUtil.disCountConvertStr(reduceDiscount));
            } else {
                // 满/每满多件
                return StringUtil.formatContent(PromotionTextConstant.DISCOUNT_COUNT_SHORT_TEXT, String.valueOf(count), NumberUtil.disCountConvertStr(reduceDiscount));
            }
        }
        // 每满额
        if (POLICY_QUOTA_MONEY == quotaType || POLICY_QUOTA_PER_MONEY == quotaType) {
            return StringUtil.formatContent(PromotionTextConstant.DISCOUNT_MONEY_SHORT_TEXT, NumberUtil.amountConvertF2YStr(money), NumberUtil.disCountConvertStr(reduceDiscount));
        }
        return "";
    }

    /**
     * 生成包邮活动规则(短规则）
     *
     * @param levelList 阶梯列表
     * @return 活动规则
     */
    public String generatePostFreeDescRuleIndex(List<QuotaLevel> levelList) {
        if (CollectionUtils.isEmpty(levelList)) {
            return "";
        }
        // 最高阶梯
        QuotaLevel firstLevel = levelList.get(0);
        if (CollectionUtils.isEmpty(firstLevel.getQuotas())) {
            return "";
        }
        QuotaEle quotaEle = firstLevel.getQuotas().get(0);
        Integer count = quotaEle.getCount();
        Long money = quotaEle.getMoney();
        PolicyQuotaTypeEnum quotaType = PolicyQuotaTypeEnum.getQuotaType(quotaEle.getType());
        // 满件包邮
        if (POLICY_QUOTA_NUM == quotaType || POLICY_QUOTA_PER_NUM == quotaType) {
            if (count == 0 || count == 1) {
                return PromotionTextConstant.POST_FREE_COUNT_ONE_TEXT;
            } else {
                return StringUtil.formatContent(PromotionTextConstant.POST_FREE_COUNT_SHORT_TEXT, String.valueOf(count));
            }
        }
        // 满额包邮
        if (POLICY_QUOTA_MONEY == quotaType || POLICY_QUOTA_PER_MONEY == quotaType) {
            return StringUtil.formatContent(PromotionTextConstant.POST_FREE_MONEY_SHORT_TEXT, NumberUtil.amountConvertF2YStr(money));
        }
        return "";
    }
    // ------------------------ 伟大的分割线 --------------------------

    /**
     * 生成下单立减规则文案
     *
     * @param reduceAmount 立减金额（分）
     * @return 规则文案
     */
    public String generateBuyReduceItemRule(Long reduceAmount) {
        String money = NumberUtil.amountConvertF2YStr(reduceAmount);
        return StringUtil.formatContent(PromotionTextConstant.RULE_TEXT_BUY_REDUCE, money);
    }


    /**
     * 生成满减活动规则
     *
     * @param levelList 阶梯列表
     * @return 活动规则
     */
    public List<String> generateReduceDescRule(List<QuotaLevel> levelList) {
        if (CollectionUtils.isEmpty(levelList)) {
            return Collections.emptyList();
        }
        // 替换顺序，改为递增
        List<String> descList = Lists.newArrayList();
        levelList = Lists.reverse(levelList);
        for (QuotaLevel level : levelList) {
            long reduceMoney = level.getReduceMoney();
            long maxReducePrice = level.getMaxReducePrice();
            QuotaEle quotaEle = level.getQuotas().get(0);
            PolicyQuotaTypeEnum quotaType = PolicyQuotaTypeEnum.getQuotaType(quotaEle.getType());
            Integer count = quotaEle.getCount();
            Long money = quotaEle.getMoney();
            //  满1件/每满1件减 一个阶梯
            if (POLICY_QUOTA_NUM == quotaType || POLICY_QUOTA_PER_NUM == quotaType) {
                if (levelList.size() == 1 && quotaEle.getCount() == 1) {
                    String desc = StringUtil.formatContent(PromotionTextConstant.REDUCE_SINGLE_TEXT, NumberUtil.amountConvertF2YStr(reduceMoney));
                    descList.add(desc);
                    continue;
                }
            }
            // 满件
            if (POLICY_QUOTA_NUM == quotaType) {
                String desc = StringUtil.formatContent(PromotionTextConstant.REDUCE_COUNT_TEXT, String.valueOf(count), NumberUtil.amountConvertF2YStr(reduceMoney));
                descList.add(desc);
                continue;
            }
            // 每满件
            if (POLICY_QUOTA_PER_NUM == quotaType) {
                String desc = StringUtil.formatContent(PromotionTextConstant.REDUCE_PRE_NUM_TEXT, String.valueOf(count), NumberUtil.amountConvertF2YStr(reduceMoney));
                descList.add(desc);
                continue;
            }
            // 满额
            if (POLICY_QUOTA_MONEY == quotaType) {
                String desc = StringUtil.formatContent(PromotionTextConstant.REDUCE_MONEY_TEXT, NumberUtil.amountConvertF2YStr(money), NumberUtil.amountConvertF2YStr(reduceMoney));
                descList.add(desc);
                continue;
            }
            // 每满额
            if (POLICY_QUOTA_PER_MONEY == quotaType) {
                String desc;
                if (maxReducePrice > 0L) {
                    desc = StringUtil.formatContent(PromotionTextConstant.REDUCE_PRE_MONEY_MAX_TEXT, NumberUtil.amountConvertF2YStr(money), NumberUtil.amountConvertF2YStr(reduceMoney), NumberUtil.amountConvertF2YStr(maxReducePrice));
                } else {
                    desc = StringUtil.formatContent(PromotionTextConstant.REDUCE_PRE_MONEY_TEXT, NumberUtil.amountConvertF2YStr(money), NumberUtil.amountConvertF2YStr(reduceMoney));
                }
                descList.add(desc);
            }
        }
        String descRule = StringUtils.join(descList, ";");
        return Collections.singletonList(descRule);
    }

    /**
     * 生成满折活动规则
     *
     * @param levelList 阶梯列表
     * @return 活动规则
     */
    public List<String> generateDiscountDescRule(List<QuotaLevel> levelList) {
        if (CollectionUtils.isEmpty(levelList)) {
            return Collections.emptyList();
        }
        // 替换顺序，改为递增
        List<String> descList = Lists.newArrayList();
        levelList = Lists.reverse(levelList);
        for (QuotaLevel level : levelList) {
            long reduceDiscount = level.getReduceDiscount();
            QuotaEle quotaEle = level.getQuotas().get(0);
            PolicyQuotaTypeEnum quotaType = PolicyQuotaTypeEnum.getQuotaType(quotaEle.getType());
            Integer count = quotaEle.getCount();
            Long money = quotaEle.getMoney();

            // 满件
            if (POLICY_QUOTA_NUM == quotaType || POLICY_QUOTA_PER_NUM == quotaType) {
                String desc;
                // 满一件 且一个阶梯
                if (levelList.size() == 1 && count == 1) {
                    desc = StringUtil.formatContent(PromotionTextConstant.DISCOUNT_ONE_TEXT, NumberUtil.disCountConvertStr(reduceDiscount));
                } else {
                    desc = StringUtil.formatContent(PromotionTextConstant.DISCOUNT_COUNT_TEXT, String.valueOf(count), NumberUtil.disCountConvertStr(reduceDiscount));
                }
                descList.add(desc);
            }
            // 满/每满额
            if (POLICY_QUOTA_MONEY == quotaType || POLICY_QUOTA_PER_MONEY == quotaType) {
                String desc = StringUtil.formatContent(PromotionTextConstant.DISCOUNT_MONEY_TEXT, NumberUtil.amountConvertF2YStr(money), NumberUtil.disCountConvertStr(reduceDiscount));
                descList.add(desc);
            }
        }
        String descRule = StringUtils.join(descList, ";");
        return Collections.singletonList(descRule);
    }


    /**
     * 生成包邮活动规则
     *
     * @param levelList 阶梯列表
     * @return 活动规则
     */
    public List<String> generatePostFreeDescRule(List<QuotaLevel> levelList) {
        if (CollectionUtils.isEmpty(levelList) || CollectionUtils.isEmpty(levelList.get(0).getQuotas())) {
            return Collections.emptyList();
        }
        // 取一个阶梯
        QuotaLevel firstLevel = levelList.get(0);
        QuotaEle quotaEle = firstLevel.getQuotas().get(0);
        PolicyQuotaTypeEnum quotaType = PolicyQuotaTypeEnum.getQuotaType(quotaEle.getType());
        Integer count = quotaEle.getCount();
        Long money = quotaEle.getMoney();
        if (POLICY_QUOTA_NUM == quotaType || POLICY_QUOTA_PER_NUM == quotaType) {
            String desc = StringUtil.formatContent(PromotionTextConstant.POST_FREE_COUNT_TEXT, String.valueOf(count));
            return Collections.singletonList(desc);
        }
        if (POLICY_QUOTA_MONEY == quotaType || POLICY_QUOTA_PER_MONEY == quotaType) {
            String desc = StringUtil.formatContent(PromotionTextConstant.POST_FREE_MONEY_TEXT, NumberUtil.amountConvertF2YStr(money));
            return Collections.singletonList(desc);
        }
        return Collections.emptyList();
    }

    /**
     * 生成满赠活动规则
     *
     * @param levelList 阶梯列表
     * @return 活动规则
     */
    public List<String> generateGiftDescRule(List<QuotaLevel> levelList) {
        return null;
    }

    /**
     * 生成满赠品活动规则(短规则）
     *
     * @param levelList 阶梯列表
     * @return 活动规则
     */
    public String generateGiftDescRuleIndex(List<QuotaLevel> levelList) {
        return null;
    }

    /**
     * 生成买赠活动规则
     *
     * @param levelList 阶梯列表
     * @return 活动规则
     */
    public List<String> generateBuyGiftDescRule(List<QuotaLevel> levelList) {
        return null;
    }

    /**
     * 生成买赠活动规则(短规则）
     *
     * @param levelList 阶梯列表
     * @return 活动规则
     */
    public String generateBuyGiftDescRuleIndex(List<QuotaLevel> levelList) {
        return null;
    }

    /**
     * 赠品活动文案
     *
     * @param promotionConfig promotionConfig
     * @param skuBaseInfo     skuInfo map
     */
    public static void composePromotionText(GiftPromotionConfig promotionConfig, Map<Long, GoodsMultiInfoDTO> skuBaseInfo) {
        LinkedList<String> descRule = new LinkedList<>();
        promotionConfig.setDescRule(descRule);
        List<QuotaLevel> levelList = promotionConfig.getLevelList();

        // 生成Quota文案
        generateQuotaListDesc(levelList);

        // 生成短规则文案
        generateGiftDescRuleIndex(promotionConfig, levelList);

        // 汇总文案
        generateGiftLadderText(descRule, levelList, skuBaseInfo);

        // 单阶梯文案
        generateSingleRuleText(skuBaseInfo, descRule, levelList);
    }

    /**
     * 生成quota文案 quotaList
     *
     * @param levelList 赠品阶梯
     */
    private static void generateQuotaListDesc(List<QuotaLevel> levelList) {
        levelList.forEach(quotaLevel -> quotaLevel.getIncludeGoodsGroups().forEach(fillGoodsGroupBean -> generateQuotaDesc(fillGoodsGroupBean.getQuota())));
    }

    /**
     * 生成quota文案 quota
     *
     * @param quota 条件
     */
    private static void generateQuotaDesc(QuotaEle quota) {
        Integer type = quota.getType();
        Integer count = quota.getCount();
        Long money = quota.getMoney();
        StringBuilder builder = new StringBuilder();
        if (POLICY_QUOTA_MONEY.getType() == type) {
            builder.append(StringUtil.formatContent(PromotionTextConstant.QUOTA_MONEY_TEXT, NumberUtil.amountConvertF2YStr(money)));
        }
        if (POLICY_QUOTA_NUM.getType() == type) {
            builder.append(StringUtil.formatContent(PromotionTextConstant.QUOTA_COUNT_TEXT, String.valueOf(count)));
        }
        if (POLICY_QUOTA_PER_MONEY.getType() == type) {
            builder.append(StringUtil.formatContent(PromotionTextConstant.QUOTA_PRE_MONEY_TEXT, NumberUtil.amountConvertF2YStr(money)));
        }
        if (POLICY_QUOTA_PER_NUM.getType() == type) {
            builder.append(StringUtil.formatContent(PromotionTextConstant.QUOTA_PRE_COUNT_TEXT, String.valueOf(count)));
        }
        quota.setDesc(String.valueOf(builder));
    }

    /**
     * 生成阶梯文案
     *
     * @param descRule    弹层列表
     * @param levelList   阶梯列表
     * @param skuBaseInfo 商品信息map
     */
    private static void generateGiftLadderText(List<String> descRule, List<QuotaLevel> levelList, Map<Long, GoodsMultiInfoDTO> skuBaseInfo) {
        if (levelList.size() <= 1) {
            return;
        }
        Integer quotaType = levelList.get(0).getIncludeGoodsGroups().get(0).getQuota().getType();
        StringBuilder ladder = getLadderBuilder(levelList);

        if (POLICY_QUOTA_MONEY.getType() == quotaType) {
            descRule.add(StringUtil.formatContent(PromotionTextConstant.GIFT_MONEY_LADDER_TEXT, String.valueOf(ladder)));
        }
        if (POLICY_QUOTA_NUM.getType() == quotaType) {
            descRule.add(StringUtil.formatContent(PromotionTextConstant.GIFT_COUNT_LADDER_TEXT, String.valueOf(ladder)));
        }

        for (QuotaLevel quotaLevel : levelList) {
            for (SkuGroup skuGroup : quotaLevel.getGiftGoods().getSkuGroupList()) {
                // 生成组文案
                generateSkuGroupText(skuGroup);
                // 生成产品文案
                for (GiftBargainGroup giftBargain : skuGroup.getListInfo()) {
                    String rule = generateGiftBeanText(skuBaseInfo, giftBargain);
                    giftBargain.setDescRule(rule);
                }
            }
        }

    }

    /**
     * 获取赠品文案
     *
     * @param skuBaseInfo     sku商品map
     * @param giftBargainBean giftBean
     * @return 文案
     */
    private static String generateGiftBeanText(Map<Long, GoodsMultiInfoDTO> skuBaseInfo, GiftBargainGroup giftBargainBean) {
        Long baseNum = giftBargainBean.getGiftBaseNum() != null && giftBargainBean.getGiftBaseNum() != 0L ? giftBargainBean.getGiftBaseNum() : 1L;
        Long marketPrice = giftBargainBean.getMarketPrice();
        GoodsMultiInfoDTO infoDTO = skuBaseInfo.get(giftBargainBean.getSku());
        String goodsName = infoDTO != null ? infoDTO.getGoodsName() : "";
        return StringUtil.formatContent(PromotionTextConstant.GIFT_TEXT, NumberUtil.amountConvertF2YStr(marketPrice), goodsName, String.valueOf(baseNum));
    }

    /**
     * 拼接阶梯文案
     *
     * @param levelList 阶梯列表
     * @return 阶梯文案
     */
    private static StringBuilder getLadderBuilder(List<QuotaLevel> levelList) {
        StringBuilder ladder = new StringBuilder();
        for (int i = levelList.size() - 1; i >= 0; i--) {
            QuotaLevel quotaLevel = levelList.get(i);
            List<FillGoodsGroup> includeGoodsGroups = quotaLevel.getIncludeGoodsGroups();
            // 优惠弹层 多阶梯
            for (FillGoodsGroup includeGoodsGroup : includeGoodsGroups) {
                QuotaEle quota = includeGoodsGroup.getQuota();
                Integer type = quota.getType();
                Integer count = quota.getCount();
                String money = NumberUtil.amountConvertF2YStr(quota.getMoney());
                if (POLICY_QUOTA_MONEY.getType() == type) {
                    if (i == levelList.size() - 1) {
                        ladder.append(money);
                    } else {
                        ladder.append("/").append(money);
                    }
                }
                if (POLICY_QUOTA_NUM.getType() == type) {
                    if (i == levelList.size() - 1) {
                        ladder.append(count);
                    } else {
                        ladder.append("/").append(count);
                    }
                }
            }
        }
        return ladder;
    }


    private static void generateSingleRuleText(Map<Long, GoodsMultiInfoDTO> skuBaseInfo, List<String> descRule, List<QuotaLevel> levelList) {
        if (levelList.size() > 1) {
            return;
        }
        QuotaLevel quotaLevel = levelList.get(0);
        List<SkuGroup> skuGroupList = quotaLevel.getGiftGoods().getSkuGroupList();
        SkuGroup skuGroupBean = skuGroupList.get(0);
        List<GiftBargainGroup> listInfo = skuGroupBean.getListInfo();
        QuotaEle quota = quotaLevel.getIncludeGoodsGroups().get(0).getQuota();
        Long money = quota.getMoney();

        // 生成赠品组文案
        generateSkuGroupText(skuGroupBean);

        for (GiftBargainGroup giftBargainBean : listInfo) {
            String rule = generateGiftBeanText(skuBaseInfo, giftBargainBean);
            giftBargainBean.setDescRule(rule);
        }
        // 生成短规则文案
        descRule.add(quota.getDesc() + "，" + skuGroupBean.getDescGroupRule());
    }


    private static void generateSkuGroupText(SkuGroup skuGroupBean) {
        String sendType = skuGroupBean.getSendType();
        if (Objects.equals(sendType, SendTypeEnum.ALL.getCode())) {
            skuGroupBean.setDescGroupRule(PromotionTextConstant.GIFT_ALL_TEXT);
            return;
        }
        skuGroupBean.setDescGroupRule(PromotionTextConstant.GIFT_ONE_TEXT);
    }

    /**
     * 生成赠品活动
     *
     * @param promotionConfig GiftPromotionConfig
     * @param levelList       赠品阶梯
     */
    private static void generateGiftDescRuleIndex(GiftPromotionConfig promotionConfig, List<QuotaLevel> levelList) {
        // 优惠栏文案 最低门槛
        QuotaLevel quotaLevel = levelList.get(levelList.size() - 1);
        FillGoodsGroup fillGoodsGroupBean = quotaLevel.getIncludeGoodsGroups().get(0);
        QuotaEle quota = fillGoodsGroupBean.getQuota();
        Integer type = quota.getType();
        Integer count = quota.getCount();
        Long money = quota.getMoney();
        if (POLICY_QUOTA_MONEY.getType() == type || POLICY_QUOTA_PER_MONEY.getType() == type) {
            promotionConfig.setDescRuleIndex(StringUtil.formatContent(PromotionTextConstant.GIFT_MONEY_SHORT_TEXT, NumberUtil.amountConvertF2YStr(money)));
        }
        if (POLICY_QUOTA_NUM.getType() == type || POLICY_QUOTA_PER_NUM.getType() == type) {
            promotionConfig.setDescRuleIndex(StringUtil.formatContent(PromotionTextConstant.GIFT_COUNT_SHORT_TEXT, String.valueOf(count)));
        }
    }

    /**
     * 买赠活动文案
     *
     * @param promotionConfig promotionConfig
     * @param skuBaseInfo     skuInfo map
     */
    public static void composePromotionText(BuyGiftPromotionConfig promotionConfig, Map<Long, GoodsMultiInfoDTO> skuBaseInfo) {
        List<String> descRule = new ArrayList<>();
        promotionConfig.setDescRuleIndex(PromotionTextConstant.BUY_GIFT_SHORT_TEXT);
        promotionConfig.setDescRule(descRule);
        Goods giftGoods = promotionConfig.getGiftGoods();
        FillGoodsGroup includeGoodsGroup = promotionConfig.getIncludeGoodsGroup();
        QuotaEle quota = includeGoodsGroup.getQuota();
        generateQuotaDesc(quota);
        for (SkuGroup skuGroup : giftGoods.getSkuGroupList()) {
            if (skuGroup.getListInfo().size() > 1) {
                skuGroup.setDescGroupRule(PromotionTextConstant.BUY_GIFT_MORE_TEXT);
            }
            for (GiftBargainGroup giftBargainGroup : skuGroup.getListInfo()) {
                GoodsMultiInfoDTO goodsMultiInfoDTO = skuBaseInfo.get(giftBargainGroup.getSku());
                String rule = StringUtil.formatContent(PromotionTextConstant.GIFT_TEXT, NumberUtil.amountConvertF2YStr(giftBargainGroup.getMarketPrice()), goodsMultiInfoDTO.getGoodsName(), String.valueOf(giftBargainGroup.getGiftBaseNum()));
                giftBargainGroup.setDescRule(rule);
            }
        }
        descRule.add(PromotionTextConstant.BUY_GIFT_RULE_TEXT);
    }

}
