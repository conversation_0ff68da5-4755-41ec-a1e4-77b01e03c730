package com.xiaomi.nr.promotion.domain.activity.facade;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.activity.pool.ActSearchParam;
import com.xiaomi.nr.promotion.activity.pool.CarActivitySearcher;
import com.xiaomi.nr.promotion.activity.pool.CarShopActivitySearcher;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.ChannelsHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/6/19
 */
@Component
@Slf4j
public class CarShopActivityFacade {
    @Autowired
    private CarShopActivitySearcher carShopActivitySearcher;
    
    /**
     * 活动检索
     * @param domainCheckoutContext 促销领域上下文
     * @return 可参与活动工具
     */
    public List<ActivityTool> activitySearch(DomainCheckoutContext domainCheckoutContext) {
        ActSearchParam param = new ActSearchParam()
                .setChannel(domainCheckoutContext.getRequest().getChannel())
                .setGoodsList(carShopActivitySearcher.createSearchGoods(domainCheckoutContext.getRequest().getCartList()));
        return carShopActivitySearcher.searchCarActivity(param);
    }

    public void checkSourceAndSourceCode(List<CartItem> cartList, List<ActivityTool> activityTools) throws BizError {
        if (CollectionUtils.isEmpty(cartList)) {
            return;
        }
        // 获取所有活动id字符串列表
        final Set<String> actIdSet = activityTools.stream().map(ActivityTool::getId).filter(id -> id != 0L).map(String::valueOf)
                .collect(Collectors.toSet());

        // 遍历购物车进行检查, 查找不能参加活动的购物车item
        final List<CartItem> excludedItemList = new ArrayList<>();
        for (CartItem cartItem : cartList) {
            boolean valid = checkItemSource(cartItem, actIdSet);
            if (!valid) {
                excludedItemList.add(cartItem);
            }
        }
        // 移除不能参加活动的购物车item
        cartList.removeAll(excludedItemList);
    }

    /**
     * 校验单个购物车购物车sourceCode
     *
     * @param cartItem 购物车项
     * @param actIdSet 活动Id字符串列表
     * @return true/false false的item需要从购物车中删除
     */
    private boolean checkItemSource(CartItem cartItem, Set<String> actIdSet) throws BizError {
        String source = cartItem.getSource();
        if (!SourceEnum.isGiftBargain(source)) {
            return true;
        }

        // source是赠品或者加价购活动的，如果sourcecode不符合活动列表，删除
        String sourceCode = cartItem.getSourceCode();
        if (!actIdSet.contains(sourceCode)) {
            log.info("sourcecode invalid, will delete. itemId:{} sourcecode:{}", cartItem.getItemId(), sourceCode);
            return false;
        }

        // 获取活动
        List<ActivityTool> activityTools = carShopActivitySearcher.doSearchAct(Lists.newArrayList(Long.valueOf(sourceCode)));
        if (CollectionUtils.isEmpty(activityTools)) {
            log.info("get activity info failed, act is not invalid. itemId:{} sourcecode:{}", cartItem.getItemId(), sourceCode);
            return false;
        }
        ActivityTool activityTool = activityTools.get(0);
        if (Objects.isNull(activityTool)) {
            log.info("get activity info failed, act is not invalid. itemId:{} sourcecode:{}", cartItem.getItemId(), sourceCode);
            return false;
        }

        int actType = activityTool.getType().getTypeId();
        // 赠品 校验逻辑，判断购物车中类型是否和活动本身类型一样
        if (SourceEnum.isGift(source) && !ActivityTypeEnum.isGiftAct(actType) && !ActivityTypeEnum.isBuyGiftAct(actType)) {
            log.warn("source is gift while act is not gift. itemId:{} actId:{}, actType:{}", cartItem.getItemId(), sourceCode, actType);
            throw ExceptionHelper.create(GeneralCodes.ParamError, String.format("赠品对应的活动%s不是赠品活动", sourceCode));
            // 加价购 校验逻辑，判断购物车中类型是否和活动本身类型一样
        } else if (SourceEnum.isBargain(source) && !ActivityTypeEnum.isBargainAct(actType)) {
            log.warn("source is bargain while act is not bargain. itemId:{} actId:{}, actType:{}", cartItem.getItemId(), sourceCode, actType);
            throw ExceptionHelper.create(GeneralCodes.ParamError, String.format("加价购对应的活动%s不是加价购活动", sourceCode));
        }
        return true;
    }
}
