package com.xiaomi.nr.promotion.constant;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 优惠全局常量
 *
 * <AUTHOR>
 * @date 2021/3/30
 */
public class PromotionConstant {
    /**
     * 购物车item_list里reduce_list的前缀
     */
    public static final String CARTLIST_COUPON_PREFIX = "coupon_";

    /**
     * 购物车item_list里reduce_list的前缀
     */
    public static final String CARTLIST_ACTIVITY_PREFIX = "act_";

    /**
     * 购物车红包reduce_list的前缀
     */
    public static final String CARTLIST_REDPACKET_PREFIX = "redpacket_";

    /**
     * phoenix reduce_list的前缀
     */
    public static final String CARTLIST_PHOENIX_PREFIX = "phoenix_";

    /**
     * 直降reduce_list的前缀
     */
    public static final String CARTLIST_ONSALE_PREFIX = "onsale_";

    // ----------------------------- 华丽的分割线 --------------------------------
    /**
     * 分摊-直降活动key
     */
    public static final String SHARE_ACTIVITY_ONSALE_KEY = "onsale_Act";
    /**
     * 分摊-满折活动key
     */
    public static final String SHARE_ACTIVITY_DISCOUNT_KEY = "discount_Act";
    /**
     * 分摊-满减活动key
     */
    public static final String SHARE_ACTIVITY_REDUCE_KEY = "reduce_Act";
    /**
     * 分摊-以旧换新购新补贴key
     */
    public static final String NEW_PURCHASE_SUBSIDY_KEY = "new_purchase_subsidy_Act";
    /**
     * 分摊-以旧换新换新补贴key
     */
    public static final String UPGRADE_PURCHASE_SUBSIDY_KEY = "upgrade_purchase_subsidy_Act";
    /**
     * 分摊-政府以旧换新购新补贴key
     */
    public static final String GOVERNMENT_PURCHASE_SUBSIDY_KEY = "government_purchase_subsidy_Act";
    /**
     * 分摊-赠品活动key
     */
    public static final String SHARE_ACTIVITY_GIFT_KEY = "gift_Act";
    /**
     * 分摊-加价购活动key
     */
    public static final String SHARE_ACTIVITY_BARGAIN_KEY = "bargain_Act";
    /**
     * 分摊-门店价活动key
     */
    public static final String SHARE_ACTIVITY_STOREPRICE_KEY = "storeprice_Act";
    /**
     * 分摊-换新立减活动key
     */
    public static final String SHARE_ACTIVITY_RENEWREDUCE_KEY = "renew_reduce_Act";
    /**
     * 分摊-下单立减活动key
     */
    public static final String SHARE_ACTIVITY_BUYREDUCE_KEY = "buy_reduce_Act";
    /**
     * 分摊-指定门店降价活动key
     */
    public static final String SHARE_ACTIVITY_PARTONSALE_KEY = "partonsale_Act";
    /**
     * 分摊-满减劵key
     */
    public static final String SHARE_COUPON_REDUCE_KEY = "reduce_Coupon";
    /**
     * 分摊-满折劵key
     */
    public static final String SHARE_COUPON_DISCOUNT_KEY = "discount_Coupon";
    /**
     * 分摊-抵扣劵key
     */
    public static final String SHARE_COUPON_DEDUCT_KEY = "deduct_Coupon";
    /**
     * 分摊-红包key
     */
    public static final String SHARE_REDPACKET_KEY = "redpacket";
    /**
     *  分摊-联通华盛key
     */
    public static final String SHARE_PHOENIX_HUASHENG_KEY = "huasheng_Phoenix";
    /**
     *  分摊-联通key
     */
    public static final String SHARE_PHOENIX_UNICOM_KEY = "unicom_Phoenix";
    /**
     *  分摊-电信key
     */
    public static final String SHARE_PHOENIX_TELECOM_KEY = "telecom_Phoenix";
    /**
     *  分摊-移动key
     */
    public static final String SHARE_PHOENIX_MOBILE_KEY = "mobile_Phoenix";
    /**
     *  分摊-米家以旧换新key-补贴
     */
    public static final String SHARE_PHOENIX_RENEW_SUBSIDY_KEY = "renewsubsidy_Phoenix";
    /**
     *  分摊-米家以旧换新key-旧机抵扣
     */
    public static final String SHARE_PHOENIX_RENEW_DEDUCT_KEY = "renewdeduct_Phoenix";
    // ----------------------------- 华丽的分割线 --------------------------------

    /**
     * 拆item,新的item后缀中的前缀 0000_I1
     */
    public static final String ITEMID_ITEM_NEW = "_I";

    /**
     * 拆package,新的item后缀中的前缀0000_P1
     */
    public static final String ITEMID_PACKAGE_NEW = "_P";

    /**
     * ITEMID_DEDUCT_NEW ..
     */
    public static final String ITEMID_DEDUCT_NEW = "_D";

    // ----------------------------- 华丽的分割线 --------------------------------

    /**
     * COUPON_BASE_INFO 返回输出的key
     */
    public static final String COUPON_BASE_INFO = "coupon_base_info";

    /**
     * COUPON_BASE_INFO 返回输出的key,新版本适配超级补贴券
     */
    public static final String COUPON_BASE_INFO_V2 = "coupon_base_info_v2";
    /**
     * COUPON_BASE_INFO 返回输出的key 无效
     */
    public static final String COUPON_BASE_INFO_INVALID = "coupon_base_info_invalid";

    /**
     * 区域消费券可用的区域，用逗号分隔区域ID
     */
    public static final String COUPON_USE_REGION = "coupon_limit_use_region";
    /**
     * ECARD_BASE_INFO 返回输出的key
     */
    public static final String ECARD_BASE_INFO = "ecard_base_info";
    /**
     * ECARD_BASE_INFO 返回输出的key 无效
     */
    public static final String ECARD_BASE_INFO_INVALID = "ecard_base_info_invalid";
    /**
     * 礼品卡可使用商品Item范围
     */
    public static final String ECARD_ITEM_LIST = "ecard_item_list";
    /**
     * 红包基本信息
     */
    public static final String REDPACKET_BASE_INFO = "redpacket_base_info";
    /**
     * 红包基本信息 无效
     */
    public static final String REDPACKET_BASE_INFO_INVALID = "redpacket_base_info_invalid";
    /**
     * 三方优惠-错误信息
     */
    public static final String PHOENIX_BASE_INFO_INVALID = "phoenix_base_info_invalid";
    /**
     * 北京以旧换新补贴-错误信息
     */
    public static final String PURCHASE_SUBSIDY_INVALID = "purchase_subsidy_invalid";

    // ----------------------------- 华丽的分割线 --------------------------------

    /**
     * 下单使用
     */
    public static final String COUPON_LOG_USED_DESC = "下单使用";
    /**
     * 关单恢复
     */
    public static final String COUPON_LOG_REFUND_DESC = "关单恢复";

    /**
     * 数据库写日志时的admin_id
     */
    public static final Long DB_ADMIN_ID = 0L;

    /**
     * 销售模式
     * "booking",                //定金预售
     * 	"booking_bigtap",         //定金预售过大秒
     * 	"second_booking",         //定金预售　一品多售的模式
     * 	"second_booking_bigtap",  //定金预售过大秒 一品多售的模式
     * 	"presales",               //预售
     * 	"presales_bigtap",        //预售过大秒
     * 	"second_presales",        //预售　一品多售的模式
     * 	"second_presales_bigtap", //预售过大秒　一品多售的模式
     * 	"tailorder",              //盲约/定金预售的尾款
     */
    public static final List<String> SALE_SOURCE_EXCLUDE = Arrays.asList(
            "second_booking",
            "second_booking_bigtap",
            "second_presales",
            "second_presales_bigtap",
            "tailorder"
    );

    /**
     * 合约机
     */
    public static final String CONTRACTPHONE = "contract";

    /**
     * 购物车保留一分钱
     */
    public static final int CART_PRICE_REMAIN = 1;

    /**
     * 活动版本>=1，活动来自promotion-admin创建
     */
    public static final Integer PROMOTION_ADMIN = 1;

    public static final Integer VERSION_BD = 0;

    /**
     * 云店
     */
    public static final String ESTORE = "estore";

    public static final Map<Integer, String> SUBSIDY_CATE_CODE_MAP = new HashMap<Integer, String>();

    static {
        SUBSIDY_CATE_CODE_MAP.put(1, "01");
        SUBSIDY_CATE_CODE_MAP.put(2, "02");
        SUBSIDY_CATE_CODE_MAP.put(3, "03");
        SUBSIDY_CATE_CODE_MAP.put(4, "04");
        SUBSIDY_CATE_CODE_MAP.put(5, "05");
        SUBSIDY_CATE_CODE_MAP.put(6, "06");
        SUBSIDY_CATE_CODE_MAP.put(7, "07");
        SUBSIDY_CATE_CODE_MAP.put(8, "08");
    }
}
