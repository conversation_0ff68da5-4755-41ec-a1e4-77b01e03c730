package com.xiaomi.nr.promotion.bizplatform;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

public interface BaseBizPlatform {


    /**
     *
     * @return 返回业务标志
     */
    BizPlatformEnum getBiz();

    /**
     * 促销领域通用结算接口
     * @param domainCheckoutContext 结算上下文
     */
    void checkout(DomainCheckoutContext domainCheckoutContext) throws Exception;

    /**
     * 汇总返回数据
     * @param domainCheckoutContext 结算上下文
     * @throws BizError 异常
     */
    void generateResponse(DomainCheckoutContext domainCheckoutContext) throws BizError;


    /**
     * 添加资源
     * @param domainCheckoutContext 结算上下文
     * @throws BizError
     */
    void addResource(DomainCheckoutContext domainCheckoutContext) throws BizError;


    /**
     * hera监控打点
     */
    void postProcessing(CheckoutPromotionRequest request, DomainCheckoutContext domainCheckoutContext);


}
