package com.xiaomi.nr.promotion.model.promotionconfig.loader;

import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoDTO;
import com.xiaomi.nr.promotion.entity.redis.*;
import com.xiaomi.nr.promotion.enums.PolicyQuotaTypeEnum;
import com.xiaomi.nr.promotion.model.common.*;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.GiftPromotionConfig;
import com.xiaomi.nr.promotion.rpc.gis.GoodsStockServiceProxy;
import com.xiaomi.nr.promotion.tool.ConditionCheckTool;
import com.xiaomi.nr.promotion.tool.PromotionDescRuleTool;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 买赠赠品加载
 *
 * <AUTHOR>
 * @date 2021/6/3
 */
@Slf4j
@Component
public class GiftPromotionConfigLoader implements PromotionConfigLoader {

    @Autowired
    private GoodsStockServiceProxy goodsStockServiceProxy;
    @Autowired
    private ConditionCheckTool conditionCheckTool;

    @Override
    public boolean support(AbstractPromotionConfig promotionConfig) {
        if (promotionConfig instanceof GiftPromotionConfig) {
            return true;
        }
        return false;
    }

    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityInfo activityInfo) throws BizError {
        if (activityInfo == null || promotionConfig == null) {
            log.error("policy gift activityInfo is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "activityInfo is null");
        }
        // 基本数据和条件
        TypeBase typeBase = activityInfo.getBasetype();
        Condition condition = activityInfo.getCondition();
        Policy policy = activityInfo.getPolicy();
        if (typeBase == null || condition == null || policy == null) {
            log.error("policy gift baseType or policy or policy is null. actInfo:{}", activityInfo);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "baseType or policy or policy is null");
        }
        Long id = typeBase.getId();

        // 配置转化
        GiftPromotionConfig giftConfig = (GiftPromotionConfig) promotionConfig;
        if (CollectionUtils.isEmpty(policy.getPolicies())) {
            log.error("policy gift is invalid. actId:{} policy:{} ", id, policy);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "policy is invalid");
        }
        // 缓存数据校验
        List<PolicyLevel> levelList = policy.getPolicies();
        validatePolicyLevel(id, levelList);
        if (CollectionUtils.isEmpty(levelList)) {
            log.error("none match gift level is invalid. actId:{} policy:{} ", id, policy);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "policy is invalid");
        }

        // 主品列表
        Set<String> skuPackageSet = getSkuPackageSet(levelList);
        giftConfig.setLevelList(getQuotaLevels(levelList));
        giftConfig.setIncludeSkuPackages(skuPackageSet);

        // 活动文案
        List<Long> skuList = getGiftSkuList(levelList);
        Map<Long, GoodsMultiInfoDTO> skuBaseInfo = goodsStockServiceProxy.getSkuBaseInfo(skuList);
        PromotionDescRuleTool.composePromotionText(giftConfig, skuBaseInfo);
    }


    /**
     * 校验缓存中数据是否有缺失
     *
     * @param id        活动id
     * @param levelList 赠品阶梯
     */
    private void validatePolicyLevel(Long id, List<PolicyLevel> levelList) throws BizError {
        if (CollectionUtils.isEmpty(levelList)) {
            log.error("policy levelList is empty. actId:{} levelList:{} ", id, levelList);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "policy levelList is empty");
        }
        List<PolicyLevel> removeLevelList = Lists.newArrayList();
        for (PolicyLevel policyLevel : levelList) {
            // 校验阶梯
            if (policyLevel == null || CollectionUtils.isEmpty(policyLevel.getIncludedGoodsGroup()) || policyLevel.getRule() == null) {
                log.error("policy levelList is empty. actId:{} levelList:{} ", id, levelList);
                throw ExceptionHelper.create(GeneralCodes.InternalError, "policy levelList is empty");
            }

            // 校验主品与条件
            List<FillGoodsGroup> includedGoodsGroup = policyLevel.getIncludedGoodsGroup();
            for (FillGoodsGroup fillGoodsGroup : includedGoodsGroup) {
                if (fillGoodsGroup == null || fillGoodsGroup.getJoinGoods() == null || fillGoodsGroup.getQuota() == null) {
                    log.error("policy levelList is empty. actId:{} levelList:{} ", id, levelList);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "policy levelList is empty");
                }
                QuotaEle quota = fillGoodsGroup.getQuota();
                if (quota.getType() == null || PolicyQuotaTypeEnum.getQuotaType(quota.getType()) == null) {
                    log.error("policy levelList is empty. actId:{} levelList:{} ", id, levelList);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "policy levelList is empty");
                }
            }

            // 校验从品
            RuleEle rule = policyLevel.getRule();
            Goods giftGoods = rule.getGiftGoods();
            if (giftGoods == null || CollectionUtils.isEmpty(giftGoods.getSkuGroupList())) {
                log.error("policy levelList is empty. actId:{} levelList:{} ", id, levelList);
                throw ExceptionHelper.create(GeneralCodes.InternalError, "policy levelList is empty");
            }
            List<SkuGroup> skuGroupList = giftGoods.getSkuGroupList();
            List<SkuGroup> removeSkuGroupList = Lists.newArrayList();
            for (SkuGroup skuGroup : skuGroupList) {
                Long groupId = skuGroup.getGroupId();
                boolean groupCheck = false;
                if (skuGroup == null || CollectionUtils.isEmpty(skuGroup.getListInfo())) {
                    log.error("policy levelList is empty. actId:{} levelList:{} ", id, levelList);
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "policy levelList is empty");
                }
                List<GiftBargainGroup> listInfo = skuGroup.getListInfo();
                for (GiftBargainGroup giftBargainGroup : listInfo) {
                    if (giftBargainGroup.getGiftLimitNum() != null && giftBargainGroup.getGiftLimitNum() != 0L) {
                        boolean check = conditionCheckTool.checkActGiftLimit(giftBargainGroup.getGiftLimitNum(),
                                giftBargainGroup.getGiftBaseNum(), id, groupId, giftBargainGroup.getSku());
                        if (check) {
                            groupCheck = true;
                        }
                    } else {
                        groupCheck = true;
                    }
                    if (giftBargainGroup.getGoodsId() == null || giftBargainGroup.getSku() == null) {
                        log.error("policy levelList is empty. actId:{} levelList:{} ", id, levelList);
                        throw ExceptionHelper.create(GeneralCodes.InternalError, "policy levelList is empty");
                    }
                }
                if (!groupCheck) {
                    removeSkuGroupList.add(skuGroup);
                }
            }
            skuGroupList.removeAll(removeSkuGroupList);
            if (CollectionUtils.isEmpty(skuGroupList)) {
                removeLevelList.add(policyLevel);
            }
        }
        levelList.removeAll(removeLevelList);
    }

    private List<Long> getGiftSkuList(List<PolicyLevel> levelList) {
        List<Long> res = new ArrayList<>();
        for (PolicyLevel policyLevel : levelList) {
            List<SkuGroup> skuGroupList = policyLevel.getRule().getGiftGoods().getSkuGroupList();
            if (CollectionUtils.isEmpty(skuGroupList)) {
                continue;
            }
            for (SkuGroup skuGroup : skuGroupList) {
                List<GiftBargainGroup> listInfo = skuGroup.getListInfo();
                res.addAll(listInfo.stream().map(GiftBargainGroup::getSku).collect(Collectors.toList()));
            }
        }
        return res;
    }

    /**
     * 根据PolicyLevel 获取 QuotaLevel
     *
     * @param levelList PolicyLevel
     * @return QuotaLevel
     */
    private List<QuotaLevel> getQuotaLevels(List<PolicyLevel> levelList) {
        List<QuotaLevel> quotaLevels = new ArrayList<>();
        for (PolicyLevel policyLevel : levelList) {
            RuleEle rule = policyLevel.getRule();
            Goods giftGoods = rule.getGiftGoods();

            QuotaLevel quotaLevel = new QuotaLevel();
            quotaLevel.setIncludeGoodsGroups(policyLevel.getIncludedGoodsGroup());
            quotaLevel.setGiftGoods(giftGoods);
            quotaLevels.add(quotaLevel);
        }
        return quotaLevels;
    }

    private Set<String> getSkuPackageSet(List<PolicyLevel> levelList) {
        Set<String> skuPackageSet = new HashSet<>();
        for (PolicyLevel policyLevel : levelList) {
            List<FillGoodsGroup> fillGoodsGroupList = policyLevel.getIncludedGoodsGroup();
            if (CollectionUtils.isEmpty(fillGoodsGroupList)) {
                continue;
            }
            for (FillGoodsGroup fillGoodsGroup : fillGoodsGroupList) {
                List<String> skuList = Optional.ofNullable(fillGoodsGroup.getJoinGoods().getSku()).orElse(Collections.emptyList());
                List<String> packageList = Optional.ofNullable(fillGoodsGroup.getJoinGoods().getPackages()).orElse(Collections.emptyList());
                skuPackageSet.addAll(skuList);
                skuPackageSet.addAll(packageList);
            }
        }
        return skuPackageSet;
    }
}
