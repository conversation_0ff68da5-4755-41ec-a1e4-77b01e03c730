package com.xiaomi.nr.promotion.componet.action.carshop;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.componet.action.AbstractAction;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.CarShopVipDiscountPromotionConfig;
import com.xiaomi.nr.promotion.tool.CheckoutCartToolV2;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * 折扣活动：计算扣减金额和分摊
 *
 * <AUTHOR>
 * @date 2021/4/9
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarShopVipDiscountAction extends AbstractAction {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 会员ID
     */
    private Integer vipLevel;

    /**
     * 用户参与次数限制
     */
    private Integer userJoinNumLimit;

    @Autowired
    private CheckoutCartToolV2 checkoutCartToolV2;

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        QuotaLevel level = context.getQuotaLevel();
        List<GoodsIndex> indexList = context.getGoodIndex();
        if (CollectionUtils.isEmpty(indexList) || level == null) {
            log.error("discount context indexList is empty or level is null. actId:{}, uid:{} indexList:{}", promotionId, request.getUserId(), indexList);
            return;
        }
        List<CartItem> cartList = request.getCartList();
        ValidGoods validGoods = CartHelper.buildValidGoods(cartList, indexList);

        // 计算实际总折扣金额
        long reduceDiscount = level.getReduceDiscount();
        long maxReducePrice = level.getMaxReducePrice();
        long realTotalReduceMoney = calculateDiscountMoney(validGoods.getValidPrice(), reduceDiscount, maxReducePrice);

        // 处理分摊，分摊购物车减免金额
        List<Integer> cartIndexList = getCartIndexList(indexList);
        checkoutCartToolV2.divideCartsReduce(realTotalReduceMoney, cartIndexList, cartList, ActivityTypeEnum.CAR_SHOP_VIP.getValue(), promotionId);

        // 活动互斥，不可参加直降和其他会员折扣活动
        activityMutex(cartList, indexList);

        // 组织优惠信息promotionInfo，填充到context
        setResult(context, cartList, promotion);

        // 初始化资源
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            initUidActivityCountResource(request, context, promotionId, vipLevel, userJoinNumLimit);
        }
    }


    /**
     * 活动互斥
     * 会员折扣和直降互斥，活动加载时根据活动weight进行排序，优先走会员折扣，会员折扣抵扣完，限制不可参与直降活动
     *  @param cartList
     * @param goodsInd
     */
    private void activityMutex(List<CartItem> cartList, List<GoodsIndex> goodsInd) {
        int len = cartList.size();
        for (GoodsIndex index : goodsInd) {
            int idx = index.getIndex();
            if (idx >= len) {
                continue;
            }
            CartItem item = cartList.get(idx);
            if (Objects.isNull(item)) {
                continue;
            }
            List<Long> cannotJoinActTypes = Optional.ofNullable(item.getCannotJoinActTypes()).orElse(Lists.newArrayList());
            cannotJoinActTypes.add((long) ActivityTypeEnum.CAR_SHOP_VIP.getValue());
            cannotJoinActTypes.add((long) ActivityTypeEnum.ONSALE.getValue());
            item.setCannotJoinActTypes(cannotJoinActTypes);
        }
    }


    private long calculateDiscountMoney(long totalPrice, long reduceDiscount, long maxReducePrice) {
        //打折后的价格
        long newPrice = totalPrice * reduceDiscount / 100;
        long reduceMoney = totalPrice - newPrice;
        reduceMoney = Math.min(reduceMoney, totalPrice);

        //最高可以减免的钱
        if (maxReducePrice > 0) {
            reduceMoney = Math.min(maxReducePrice, reduceMoney);
        }
        return reduceMoney;
    }

    private void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool) throws BizError {
        List<GoodsIndex> indexList = context.getGoodIndex();
        List<String> joinedItemIdList = CartHelper.getJoinedItemIdList(indexList, cartList, promotionId);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setParentItemId(CartHelper.getParentItemIdList(indexList));
        promotionInfo.setJoinedItemId(joinedItemIdList);
        promotionInfo.setExtend(Strings.EMPTY);
        promotionInfo.setJoined(CollectionUtils.isNotEmpty(joinedItemIdList) ? 1 : 0);

        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof CarShopVipDiscountPromotionConfig)) {
            return;
        }
        CarShopVipDiscountPromotionConfig promotionConfig = (CarShopVipDiscountPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.vipLevel = promotionConfig.getVipLevel();
        this.userJoinNumLimit = promotionConfig.getUserJoinNumLimit();
    }
}
