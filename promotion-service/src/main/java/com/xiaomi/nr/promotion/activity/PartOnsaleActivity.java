package com.xiaomi.nr.promotion.activity;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.GetProductActRequest;
import com.xiaomi.nr.promotion.api.dto.model.CheckGoodsItem;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.ProductActOnsaleGoods;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.componet.action.PartOnsaleAction;
import com.xiaomi.nr.promotion.componet.condition.*;
import com.xiaomi.nr.promotion.componet.preparation.GoodsHierarchyPreparation;
import com.xiaomi.nr.promotion.componet.preparation.OrgInfoPreparation;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.ActRespConverter;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.PartOnsalePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.tool.ConditionCheckTool;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 指定门店降价活动
 *
 * <AUTHOR>
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class PartOnsaleActivity extends AbstractActivityTool implements ActivityTool {
    /**
     * 直降信息Map
     * key: skuPackage val:ActPriceInfo
     */
    private Map<String, ActPriceInfo> onsaleInfoMap;

    @Autowired
    private ConditionCheckTool conditionCheckTool;


    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine
                .conditionPreparation(GoodsHierarchyPreparation.class)
                .conditionPreparation(OrgInfoPreparation.class)
                .condition(BusinessCondition.class)
                .condition(ChannelCondition.class)
                .condition(ActPersonStoreJoinLimitCondition.class)
                .condition(PartOnsaleCondition.class)
                .action(PartOnsaleAction.class);
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof PartOnsalePromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        PartOnsalePromotionConfig promotionConfig = (PartOnsalePromotionConfig) config;
        this.onsaleInfoMap = promotionConfig.getOnsaleInfoMap();
        return true;
    }

    @Override
    public ActivityDetail getActivityDetail() {
        ActivityDetail detail = super.getBasicActivityDetail();
        detail.setPriceInfoMap(onsaleInfoMap);
        return detail;
    }

    @Override
    public PromotionInfo getProductAct(GetProductActRequest request, GoodsHierarchy hierarchy, boolean isOrgTool) throws BizError {
        return null;
    }

    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) throws BizError {
        PromotionInfo promotionInfo = buildDefaultPromotionInfo(context);
        promotionInfo.setFrequent(frequency != null ? frequency.getValue() : null);
        promotionInfo.setNumLimitRule(ActRespConverter.convert(this.numLimitRule));
        return promotionInfo;
    }

    @Override
    public List<CheckGoodsItem> checkProductsAct(CheckProductsActRequest request, Map<String, GoodsHierarchy> goodsHierarchyMap) throws BizError {
        return null;
    }

    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        List<String> joinedSkuPackageList = Lists.newArrayList(includeSkuPackages);
        joinedSkuPackageList.retainAll(skuPackageList);
        if (CollectionUtils.isEmpty(joinedSkuPackageList)) {
            return Collections.emptyMap();
        }
        Map<String, ProductActInfo> actMap = Maps.newHashMap();
        for (String skuPackage : joinedSkuPackageList) {
            ActPriceInfo onsaleInfo = onsaleInfoMap.get(skuPackage);
            if (onsaleInfo == null || onsaleInfo.getLimitRule() == null) {
                log.warn("onsaleInfo or getLimitRule is null. actId:{} skuPackage:{}", id, skuPackage);
                continue;
            }

            int skuUsedNum = conditionCheckTool.getPartOnsaleSkuLimitNum(onsaleInfo, id, orgCode);
            int validSkuLimitNum = onsaleInfo.getLimitRule().getActivityLimitOne() - skuUsedNum;
            if (validSkuLimitNum <= 0) {
                log.warn("validSkuLimitNum not match. actId:{} skuPackage:{}", id, skuPackage);
                continue;
            }

            ProductActOnsaleGoods onsaleGoods = new ProductActOnsaleGoods();
            onsaleGoods.setLowerPrice(onsaleInfo.getPrice());
            onsaleGoods.setLimitNum(validSkuLimitNum);
            onsaleGoods.setIsNumLimit(onsaleInfo.getIsLimit());
            onsaleGoods.setPersonLimit(numLimitRule.getPersonLimitOne().intValue());
            if (BooleanEnum.isYes(onsaleInfo.getIsLimit())) {
                onsaleGoods.setLimitRule(ActRespConverter.convert(onsaleInfo.getLimitRule()));
            }

            ProductActInfo productActInfo = new ProductActInfo();
            productActInfo.setType(type.getValue());
            productActInfo.setId(id);
            productActInfo.setName(name);
            productActInfo.setUnixStartTime(getUnixStartTime());
            productActInfo.setUnixEndTime(getUnixEndTime());
            productActInfo.setOnsaleGoods(onsaleGoods);
            actMap.put(skuPackage, productActInfo);
        }
        return actMap;
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.PARTONSALE;
    }
}
