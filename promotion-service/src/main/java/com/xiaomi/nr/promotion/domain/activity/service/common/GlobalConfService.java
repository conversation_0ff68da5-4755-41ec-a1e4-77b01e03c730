package com.xiaomi.nr.promotion.domain.activity.service.common;

import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import org.springframework.util.concurrent.ListenableFuture;

/**
 * 全局配置服务
 *
 * <AUTHOR>
 * @date 2021/5/18
 */
public interface GlobalConfService {

    /**
     * 获取全局排除
     *
     * @return CompareItem全局排除
     */
    ListenableFuture<CompareItem> getGlobalInExclude();

    /**
     * 获取全局活动排除
     *
     * @return CompareItem全局排除
     */
    ListenableFuture<CompareItem> getGlobalActInExclude();

    /**
     * 获取全局券排除
     *
     * @return CompareItem全局排除
     */
    ListenableFuture<CompareItem> getGlobalCouponInExclude();
}
