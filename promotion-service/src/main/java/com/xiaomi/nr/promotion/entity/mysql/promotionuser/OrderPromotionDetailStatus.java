package com.xiaomi.nr.promotion.entity.mysql.promotionuser;

import lombok.Data;

import java.sql.Timestamp;

@Data
public class OrderPromotionDetailStatus {
    /**
     * id
     */
    private Long id;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 下单类型：普通交易、追加交易，OrderTypeEnum
     */
    private Integer orderType;
    /**
     * 下单场景：BizPlatformEnum
     */
    private Integer orderScene;
    /**
     * 促销id
     */
    private Long promotionId;
    /**
     * 下单场景：PromotionType
     */
    private Integer promotionType;
    /**
     * 优惠明细分摊json
     */
    private String promotionDetail;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 状态：0:失效 1：生效 2：待失效（改配中间状态）
     */
    private Integer status;
    /**
     * 添加时间
     */
    private Timestamp addTime;

    public OrderPromotionDetail statusConvertDetail(){
        OrderPromotionDetail detail=new OrderPromotionDetail();
        detail.setOrderId(orderId);
        detail.setOrderScene(orderScene);
        detail.setOrderType(orderType);
        detail.setUserId(userId);
        detail.setPromotionId(promotionId);
        detail.setPromotionType(promotionType);
        detail.setPromotionDetail(promotionDetail);
        return detail;
    }
}
