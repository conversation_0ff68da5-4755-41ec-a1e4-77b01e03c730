package com.xiaomi.nr.promotion.domain.phoenix.service.base;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.api.dto.model.ThirdPromotion;
import com.xiaomi.nr.promotion.constant.PhoenixParamConstant;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.enums.BusinessTypeEnum;
import com.xiaomi.nr.promotion.enums.PhoenixTypeEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.RecycleOrderExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.provider.PhoenixRenewProvider;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.nr.promotion.util.ThirdPromotionHelper;
import com.xiaomi.nr.recycle.api.activity.dto.ActivityQueryKeyDto;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 米家以旧换新补贴
 *
 * <AUTHOR>
 * @date 2022/10/10
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class PhoenixRenew extends AbstractPhoenixTool {
    @Autowired
    private CheckoutCartTool checkoutCartTool;

    /**
     * 条件是否满足
     *
     * @param request 请求参数
     * @param context 上下文
     * @return 是否满足 true/false
     * @throws BizError 异常情况业务异常
     */
    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        ThirdPromotion thirdPromotion = ThirdPromotionHelper.getThirdPromotion(request.getThirdPromotions(), BusinessTypeEnum.RENEW);
        if (thirdPromotion == null) {
            return false;
        }
        return true;
    }

    /**
     * 优惠结算
     * 1. 获取套餐内金额
     * 2. 进行金额计算，计算购物车总金额，如果大于券金额，则扣减全部优惠金额，如果小于券金额，则使用购物车金额
     * 3. 进行金额分摊
     * 4. 如果是submit 则进行金额冻结资源操作
     *
     * @param request         请求参数
     * @param checkoutContext 上下文
     * @throws BizError 业务异常
     */
    @Override
    public void doCheckout(CheckoutPromotionRequest request, CheckoutContext checkoutContext) throws BizError {
        ThirdPromotion thirdPromotion = ThirdPromotionHelper.getThirdPromotion(request.getThirdPromotions(), BusinessTypeEnum.RENEW);
        Long renewOrderId = ThirdPromotionHelper.getLongParamValue(thirdPromotion, PhoenixParamConstant.RENEW_ORDER_ID, 0L);
        ActivityQueryKeyDto queryKeyDto = getRecycleOrderDetail(checkoutContext);
        int renewSubsidyBalance = queryKeyDto.getActivityAmount() != null ? queryKeyDto.getActivityAmount().intValue() : 0;
        int renewDeductBalance = queryKeyDto.getFinalAmount() != null ? queryKeyDto.getFinalAmount().intValue() : 0;
        long renewBalance = renewSubsidyBalance + renewDeductBalance;

        // 计算优惠进行
        List<CartItem> cartList = getCartList(request, checkoutContext);
        long reducePrice = calculateReducePrice(cartList, (int) renewBalance, checkoutContext);

        // 补贴扣减 优先使用掉补贴， 优惠分摊
        long subsidyAmount = Math.min(reducePrice, renewSubsidyBalance);
        PhoenixTypeEnum subsidyType = PhoenixTypeEnum.RENEW_SUBSIDY;
        String subsidyIdKey = PromotionConstant.CARTLIST_PHOENIX_PREFIX + subsidyType.getType();
        checkoutCartTool.divideCartsReduce(subsidyAmount, cartList, subsidyIdKey, subsidyType.getType(), (long) subsidyType.getType());

        // 剩余使用旧机折价， 优惠分摊
        long deductAmount = reducePrice > subsidyAmount ? (reducePrice - subsidyAmount) : 0;
        PhoenixTypeEnum deductType = PhoenixTypeEnum.RENEW_DEDUCT;
        String deductIdKey = PromotionConstant.CARTLIST_PHOENIX_PREFIX + deductType.getType();
        checkoutCartTool.divideCartsReduce(deductAmount, cartList, deductIdKey, deductType.getType(), (long) deductType.getType());
        log.info("phoenix renew reduce. userId:{}, subsidyAmount:{} deductAmount:{}", request.getUserId(), subsidyAmount, deductAmount);

        // 如果是submit 则进行资源构建
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            initRenewResource(request, checkoutContext, renewOrderId, subsidyAmount, deductAmount);
        }
    }

    protected void initRenewResource(CheckoutPromotionRequest request, CheckoutContext checkoutContext, Long renewOrderId,
                                     long subsidyAmount, long deductAmount) throws BizError {
        Long orderId = request.getOrderId();
        PromotionToolType toolType = getType();
        PhoenixRenewProvider.ResContent resContent = buildResContent(request, renewOrderId, subsidyAmount, deductAmount);
        ResourceObject<PhoenixRenewProvider.ResContent> resourceObjectObject = buildResource(resContent, orderId, toolType, ResourceType.PHOENIX_RENEW);
        PhoenixRenewProvider couponProvider = (PhoenixRenewProvider) resourceProviderFactory.getProvider(ResourceType.PHOENIX_RENEW);
        couponProvider.initResource(resourceObjectObject);
        checkoutContext.getResourceHandlers().add(couponProvider);
    }

    private PhoenixRenewProvider.ResContent buildResContent(CheckoutPromotionRequest request, Long renewOrderId,
                                                            long subsidyAmount, long deductAmount) {
        PhoenixRenewProvider.ResContent resContent = new PhoenixRenewProvider.ResContent();
        resContent.setUserId(request.getUserId());
        resContent.setRenewOrderId(renewOrderId);
        resContent.setSubsidyAmount(subsidyAmount);
        resContent.setOldPhoneAmount(deductAmount);
        return resContent;
    }

    public ActivityQueryKeyDto getRecycleOrderDetail(CheckoutContext context) throws BizError {
        Map<ResourceExtType, ExternalDataProvider<?>> providerMap = Optional.ofNullable(context.getExternalDataMap()).orElse(Collections.emptyMap());
        RecycleOrderExternalProvider provider = (RecycleOrderExternalProvider) providerMap.get(ResourceExtType.RECYCLE_ORDER);
        if (provider == null || provider.getData() == null) {
            log.error("provider is null or data is null.");
            return null;
        }
        return provider.getData();
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.PHOENIX_RENEW;
    }
}
