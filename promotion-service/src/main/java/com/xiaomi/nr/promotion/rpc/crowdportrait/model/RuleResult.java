package com.xiaomi.nr.promotion.rpc.crowdportrait.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/27 16:13
 */
@Data
public class RuleResult {
    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 请求用户ID
     */
    private String idValue;

    /**
     * 是否匹配
     */
    private boolean ruleMatch;

    /**
     * 未匹配原因  (1-没有配置点查询, 2-用户缺少该人群包的标签, 3-用户有该人群包涉及的标签, 但是不匹配)
     */
    private int misMatchCode;
}
