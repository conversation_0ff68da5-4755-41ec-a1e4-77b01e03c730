package com.xiaomi.nr.promotion.constant;

import com.xiaomi.nr.md.promotion.admin.api.constant.ChannelEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.PromotionTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.SourceAppEnum;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/9 15:07
 */
public interface PromotionCacheConstant {

    interface Car {

        /**
         * 汽车来源
         */
        List<Integer> CAR_SOURCE = Arrays.asList(SourceAppEnum.CAR.code);

        /**
         * 汽车促销类型
         */
        List<Integer> CAR_PROMOTION_TYPE = Arrays.asList(
                PromotionTypeEnum.RANGE_REDUCE.code,
                PromotionTypeEnum.ONSALE.code,
                PromotionTypeEnum.EXCHANGE_SUBSIDY.code,
                PromotionTypeEnum.ORDER_REDUCE.code,
                PromotionTypeEnum.MAINTENANCE_REPAIR_DISCOUNT.code,
                PromotionTypeEnum.MAINTENANCE_ITEM_FREE.code,
                //本次新增现车立减
                PromotionTypeEnum.BUY_REDUCE.code);

        /**
         * 汽车渠道
         */
        List<Integer> CAR_CHANNEL = Arrays.asList(ChannelEnum.CAR_VEHICLE.value, ChannelEnum.CAR_MAINTENANCE_REPAIR.value);

        /**
         * 车商城：来源、活动类型、渠道
         */
        List<Integer> CAR_SHOP_SOURCE = Arrays.asList(SourceAppEnum.PROMOTION_ADMIN.code, SourceAppEnum.CAR_SHOP.code);

        /**
         * 车商城促销类型
         */
        List<Integer> CAR_SHOP_PROMOTION_TYPE = Arrays.asList(
                PromotionTypeEnum.ONSALE.code,
                PromotionTypeEnum.BUY_REDUCE.code,
                PromotionTypeEnum.BUY_GIFT.code,
                PromotionTypeEnum.CAR_SHOP_VIP.code,
                PromotionTypeEnum.POST_FREE.code);

        /**
         * 车商城渠道
         */
        List<Integer> CAR_SHOP_CHANNEL = Arrays.asList(ChannelEnum.CAR_SHOP.value);

    }

    interface MiShop {

        List<Integer> MI_SHOP_SOURCE = Arrays.asList(SourceAppEnum.CRM.code, SourceAppEnum.B2T_ADMIN.code,
                SourceAppEnum.CAR.code);

        List<Integer> MI_SHOP_PROMOTION_TYPE = Arrays.asList(PromotionTypeEnum.B2T_CHANNEL_PRICE.code,
                PromotionTypeEnum.B2T_STEP_PRICE.code,
                PromotionTypeEnum.B2T_VIP_DISCOUNT.code,
                PromotionTypeEnum.RANGE_REDUCE.code,
                PromotionTypeEnum.ONSALE.code,
                PromotionTypeEnum.EXCHANGE_SUBSIDY.code);

        List<Integer> MI_SHOP_CHANNEL = Arrays.asList(ChannelEnum.B2T_C_CUSTOMER.value,
                ChannelEnum.B2T_GOV_BIG_CUSTOMER.value,
                ChannelEnum.B2T_MIJIA_BIG_CUSTOMER.value,
                ChannelEnum.CAR_VEHICLE.value);
    }

    interface Gov {

        List<Integer> GOV_SOURCE = Arrays.asList(SourceAppEnum.PROMOTION_ADMIN.code);

        List<Integer> GOV_PROMOTION_TYPE = Arrays.asList(PromotionTypeEnum.GOVERNMENT_SUBSIDY.code);

        List<Integer> GOV_CHANNEL = Arrays.asList(ChannelEnum.MISHOP.value, ChannelEnum.DIRECT.value);

    }

    interface Area {

        String MISHOP_BIZ_AREA = "MISHOP";

        String CAR = "CAR";
    }

}
