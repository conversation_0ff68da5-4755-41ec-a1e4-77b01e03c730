package com.xiaomi.nr.promotion.componet.action.carshop;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.componet.action.AbstractBuyGiftAction;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.redis.GiftBargainGroup;
import com.xiaomi.nr.promotion.entity.redis.SkuGroup;
import com.xiaomi.nr.promotion.enums.BooleanV2Enum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GiftBargainBean;
import com.xiaomi.nr.promotion.model.common.GoodsIndexNew;
import com.xiaomi.nr.promotion.model.common.GroupCurCount;
import com.xiaomi.nr.promotion.model.common.PromotionExtend;
import com.xiaomi.nr.promotion.model.common.SkuGroupBean;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyGiftPromotionConfig;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024/6/18
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarShopBuyGiftAction extends AbstractBuyGiftAction {

    @Resource
    private ActivityRedisDao activityRedisDao;

    private PromotionToolType promotionToolType;
    
    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context)
            throws BizError {
        // 购物车中来源当前活动的赠品
        List<CartItem> cartList = request.getCartList();
        List<GoodsIndexNew> indexNewList = CartHelper.getCartsIndex(cartList, SourceEnum.SOURCE_GIFT.getSource(), String.valueOf(promotionId));
        
        
        // 获取赠品规则， 对赠品进行分组 (key: itemId, val: 信息）
        Map<String, GiftBargainGroup> groupMap = doGiftMatch(cartList, indexNewList);
        Map<Long, List<CartItem>> cartGroup = groupByGroupId(cartList, groupMap);
        
        // 对每组赠品进行检查， 如果超出最大，需要递减到合适值. key: groupId, val: cartList
        for (Map.Entry<Long, List<CartItem>> entry : cartGroup.entrySet()) {
            List<CartItem> itemList = entry.getValue();
            checkAndReduceGroupCount(itemList, groupMap, context.getFillTimes());
        }
        
        // 删除购物车 CART_DEL_FLAG
        delInvalidCarts(cartList);
        
        // calc reduce item
        calcReduceItemList(cartList);
        
        // 构建promotionInfo
        setResult(context, cartList, promotion, indexNewList, cartGroup, groupMap);
        
        // 初始化资源
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            initGiftResource(request, promotionId, context, cartGroup, null);
        }
    }
    
    /**
     *
     * @param cartGroup
     * @return
     */
    protected Map<Long, Map<String, Integer>> buildNumLimitMap(Map<Long, List<CartItem>> cartGroup) {
        Map<Long, Map<String, Integer>> countMap = Maps.newHashMap();
        cartGroup.forEach((groupId, giftList) -> {
            if (org.apache.dubbo.common.utils.CollectionUtils.isEmpty(giftList)) {
                return;
            }
            Map<String, Integer> groupGiftMap = countMap.getOrDefault(groupId, Maps.newHashMap());
            giftList.stream()
                    .filter(item -> !Objects.equals(CART_DEL_FLAG, item.getItemId()))
                    .forEach(item -> groupGiftMap.put(item.getSsuId().toString(), item.getCount()));
            countMap.put(groupId, groupGiftMap);
        });
        return countMap;
    }
    
    /**
     * 生成reduceItemList
     * @param cartList
     */
    private void calcReduceItemList(List<CartItem> cartList) {
        
        for (int i = 0; i < cartList.size(); i++) {
            CartItem item = cartList.get(i);
            if (Objects.isNull(item)) {
                continue;
            }
            if (!SourceEnum.isGift(item.getSource()) || !Objects.equals(item.getSourceCode(), String.valueOf(promotionId))) {
                continue;
            }
            
            List<ReduceDetailItem> reduceItemList = Optional.ofNullable(item.getReduceItemList()).orElse(Lists.newArrayList());
            ReduceDetailItem reduceDetailItem = initReduceDetailItem(promotionId, promotionToolType, item.getSsuId(), item.getOriginalCartPrice(), item.getCartPrice(), item.getCount());
            reduceItemList.add(reduceDetailItem);
        }
        
    }
    
    /**
     * 获取赠品信息
     *
     * @param cartItem 购物车Item
     * @return 赠品信息
     */
    @Override
    protected GiftBargainGroup getGiftGoods(CartItem cartItem) {
        if (giftGoods == null || CollectionUtils.isEmpty(giftGoods.getSkuGroupList())) {
            log.error("data giftGoods error. promotionId:{}", promotionId);
            return null;
        }
        SkuGroup skuGroup = giftGoods.getSkuGroupList().stream()
                .filter(group -> Objects.equals(group.getGroupId(), cartItem.getGroupId()))
                .findAny().orElse(null);
        if (skuGroup == null || CollectionUtils.isEmpty(skuGroup.getListInfo())) {
            log.warn("data giftGoods error, no match group. promotionId:{} groupId:{}", promotionId, cartItem.getGroupId());
            return null;
        }
        return skuGroup.getListInfo().stream()
                .filter(group -> Objects.equals(group.getSsuId(), cartItem.getSsuId()))
                .findAny().orElse(null);
    }
    
    /**
     * 设置PromotionInfo
     *
     * @param context     结果
     * @param cartList    购物车列表
     * @param tool        工具
     * @param goodsIndNew 索引信息
     * @param cartGroup   组信息
     * @throws BizError 业务异常
     */
    private void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool, List<GoodsIndexNew> goodsIndNew,
            Map<Long, List<CartItem>> cartGroup, Map<String, GiftBargainGroup> groupMap) throws BizError {
        // 总赠品数 和 每组之间参加活动的最大值
        Map<Long, Integer> countMap = getCountMap(cartGroup, groupMap);
        
        // 生成活动拓展信息
        Integer maxCount = context.getFillTimes();
        int validCountAll = countMap.values().stream().mapToInt(Integer::intValue).sum();
        List<GroupCurCount> groupCurCount = buildIncrCountMap(cartGroup, countMap);
        String activityInfo = generateExtendInfo(maxCount, validCountAll, groupCurCount);
        
        // 活动优惠信息
        int joinCount = countMap.values().stream().mapToInt(Integer::intValue).max().orElse(0);
        List<String> joinedItemIdList = CartHelper.getJoinedItemIdListNew(goodsIndNew, cartList);
        
        // 组织promotion数据输出
        context.setCarts(cartList);
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        if (Objects.nonNull(promotionInfo)) {
            promotionInfo.setJoinedItemId(joinedItemIdList);
            promotionInfo.setParentItemId(CartHelper.getParentItemIdList(context.getGoodIndex()));
            promotionInfo.setExtend(activityInfo);
            promotionInfo.setJoinCounts(joinCount);
            promotionInfo.setJoined(CollectionUtils.isNotEmpty(joinedItemIdList) ? 1 : 0);
            // 设置上下文结果
            context.setPromotion(promotionInfo);
            context.setExpress(new Express());
        }
    }
    
    @Override
    protected GiftBargainBean convert(GiftBargainGroup item, Integer maxCount, Long groupId) {
        Integer limitNum = activityRedisDao.getActBuyGiftLimitNum(promotionId, String.valueOf(item.getSku()), groupId);
        int actNumLimit = BooleanV2Enum.NO.getValue();
        if ((item.getGiftLimitNum() - limitNum) >= maxCount * item.getGiftBaseNum()) {
            actNumLimit = BooleanV2Enum.YES.getValue();
        }
        GiftBargainBean bean = new GiftBargainBean();
        bean.setSku(item.getSku());
        bean.setMarketPrice(item.getMarketPrice());
        bean.setCartPrice(item.getCartPrice());
        bean.setShipmentType(item.getShipmentType());
        bean.setRefundValue(item.getRefundValue());
        bean.setActNumLimit(actNumLimit);
        bean.setBaseNum(item.getGiftBaseNum());
        return bean;
    }
    
    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof BuyGiftPromotionConfig)) {
            log.error("config is not instanceof BuyGiftPromotionConfig. class:{}", config.getName());
            return;
        }
        BuyGiftPromotionConfig promotionConfig = (BuyGiftPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.giftGoods = promotionConfig.getGiftGoods();
        this.promotionToolType = promotionConfig.getPromotionType();
    }
}
