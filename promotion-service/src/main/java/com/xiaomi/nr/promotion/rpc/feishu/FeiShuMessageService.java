package com.xiaomi.nr.promotion.rpc.feishu;

import com.xiaomi.nr.promotion.util.IPAddressUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/2 20:52
 */
@Component
@Slf4j
public class FeiShuMessageService {

    // todo
    @Value("${feishu.cache.diff.webhook.url:https://open.f.mioffice.cn/open-apis/bot/v2/hook/b0a44460-2b0d-4458-8a6e-3bf3328c980d}")
    private String CACHE_DIFF_WEBHOOK_URL;

    private static final String CACHE_DIFF_TEMPLATE_ID = "AAqRCf5kWL1Bs";

    @Value("${spring.profiles.active}")
    private String profileName;

    /**
     * 发送缓存不一致的飞书消息
     *
     * @param moreIds
     * @param lessIds
     */
    public void sendCacheDiffMessage(List<Long> moreIds,
                                     List<Long> lessIds) {
        if (CollectionUtils.isEmpty(moreIds) && CollectionUtils.isEmpty(lessIds)) {
            return;
        }
        Map<String, String> params = new HashMap<>();
        params.put("moreIds", StringUtils.join(ListUtils.emptyIfNull(moreIds), ","));
        params.put("lessIds", StringUtils.join(ListUtils.emptyIfNull(lessIds), ","));
        params.put("ip", IPAddressUtil.getLocalIPv4Address());
        params.put("env", profileName);
        sendCardMessage(CACHE_DIFF_WEBHOOK_URL, CACHE_DIFF_TEMPLATE_ID, params);
    }

    /**
     * 发送飞书卡片消息
     *
     * @param url
     * @param params
     * @param templateId
     * @return
     */
    public boolean sendCardMessage(String url,
                                   String templateId,
                                   Map<String, String> params) {
        if (StringUtils.isBlank(templateId) || StringUtils.isBlank(url)) {
            log.error("send feishu message params error");
            return false;
        }
        try (CloseableHttpClient httpClient = HttpClients.createDefault();) {
            HttpPost httpPost = new HttpPost(url);
            JSONObject json = new JSONObject();
            json.put("msg_type", "interactive");
            JSONObject card = new JSONObject();
            card.put("type", "template");
            JSONObject data = new JSONObject(params);
            data.put("template_id", templateId);
            JSONObject templateVariable = new JSONObject(params);
            data.put("template_variable", templateVariable);
            card.put("data", data);
            json.put("card", card);
            StringEntity request = new StringEntity(json.toString());
            log.info("send feishu message request:{}", request);
            httpPost.setEntity(request);
            httpPost.setHeader("Content-Type", "application/json");
            CloseableHttpResponse ret = httpClient.execute(httpPost);
            String responseString = EntityUtils.toString(ret.getEntity());
            log.info("send feishu message response:{}", responseString);
        } catch (Exception e) {
            log.error("send feishu message error:", e);
            return false;
        }
        return true;
    }

}
