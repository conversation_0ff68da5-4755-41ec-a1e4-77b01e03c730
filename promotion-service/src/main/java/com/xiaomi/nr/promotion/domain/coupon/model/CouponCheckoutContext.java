package com.xiaomi.nr.promotion.domain.coupon.model;

import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.entity.redis.OrgInfo;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by  wangweiyi on 2024/3/2
 * 优惠券域内的上下文，逐渐替换掉CheckoutPromotionRequest和CheckoutContext
 */
@Data
public class CouponCheckoutContext {



    private List<Long> selectCouponIds;

    /**
     * 优惠券内部更新的购物车
     */
    private List<CartItem> cartItemList;

    /**
     * 资源处理类接口集
     */
    private List<ResourceProvider<?>> resourceHandlers = new ArrayList<>();

    /**
     * 门店信息
     */
    private OrgInfo orgInfo;

    /**
     * 结算请求
     */
    private CheckoutPromotionRequest request;

    /**
     * 前端选择的可用券列表
     */
    private List<Coupon> selectedCouponList = new ArrayList<>();


    /**
     * groupId 组内券使用量
     * key：groupId
     * value：count
     */
    private Map<String, Integer> counterMap = Maps.newHashMap();

}
