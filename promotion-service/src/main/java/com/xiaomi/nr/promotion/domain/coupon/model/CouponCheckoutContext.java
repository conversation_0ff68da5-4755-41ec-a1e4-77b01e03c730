package com.xiaomi.nr.promotion.domain.coupon.model;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.entity.redis.OrgInfo;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by  wang<PERSON><PERSON> on 2024/3/2
 */
@Data
public class CouponCheckoutContext {

    private CheckoutPromotionRequest request;

    private CheckoutContext context;

    private OrgInfo orgInfo;

    private List<CartItem> cartItemList;

    /**
     * 资源处理类接口集
     */
    private List<ResourceProvider<?>> resourceHandlers = new ArrayList<>();



}
