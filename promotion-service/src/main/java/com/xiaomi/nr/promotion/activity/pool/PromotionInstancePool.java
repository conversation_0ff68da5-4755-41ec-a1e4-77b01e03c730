package com.xiaomi.nr.promotion.activity.pool;

import com.alibaba.nacos.common.utils.JacksonUtils;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Maps;
import com.xiaomi.nr.md.promotion.admin.api.constant.ScopeTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.promotion.activity.ActivityFactory;
import com.xiaomi.nr.promotion.activity.cache.NrPromotionConfigCache;
import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.dao.mysql.promotion.V3ActivityOnlineMapper;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.dsl.DSLGeneralPromotion;
import com.xiaomi.nr.promotion.entity.redis.ActivityInfo;
import com.xiaomi.nr.promotion.entity.redis.TypeBase;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.*;
import com.xiaomi.nr.promotion.model.promotionconfig.loader.PromotionConfigLoaderAdapter;
import com.xiaomi.nr.promotion.rpc.mdpromotionadmin.PromotionAdminCustomServiceProxy;
import com.xiaomi.nr.promotion.util.DateTimeUtil;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xiaomi.nr.promotion.activity.cache.RefreshableActivityConfigTask.BEAN_NAME_PREFIX;

/**
 * 在线的所有优惠活动数据池
 *
 * <AUTHOR>
 * @date 2021/03/22
 */
@Slf4j
@Component
@DependsOn(BEAN_NAME_PREFIX + "MISHOP")
public class PromotionInstancePool implements InitializingBean {

    /**
     * 本地锁
     */
    private final Lock lock = new ReentrantLock();

    /**
     * 当前活动列表
     * key：activityId val:tool {@link ActivityTool}
     */
    private Map<Long, ActivityTool> activityToolCacheMap = new ConcurrentHashMap<>();

    /**
     * sku package可以参加的活动列表
     * key：skuPackage(注：规划之后给gid用) val:act Id List
     */
    private Map<String, List<Long>> skuPackageToActivityIdsCacheMap = new ConcurrentHashMap<>();

    /**
     * 指定channel可以参加的活动列表
     * key：skuPackage(注：规划之后给gid用) val:act Id List
     */
    private Map<Integer, List<Long>> channelToActivityIdsCacheMap = new ConcurrentHashMap<>();
    /**
     * 指定部门可以参加的活动列表
     * key：productDepartment val:actId List
     */
    private Map<Integer, List<Long>> productDepartmentToActivityIdsCacheMap = new ConcurrentHashMap<>();
    /**
     * 指定门店可以参加的活动列表(目前只有2024北京换新补贴date：20240525)
     * key：orgCode val:actId List
     */
    private Map<String, List<Long>> orgCodeToActivityIdsCacheMap = new ConcurrentHashMap<>();

    private volatile List<Long> goodsCommonActivityIds = new ArrayList<>();

    @Autowired
    private ActivityRedisDao activityRedisDao;
    @Autowired
    private V3ActivityOnlineMapper v3ActivityOnlineMapper;
    @Autowired
    private ActivityFactory activityFactory;
    @Autowired
    private PromotionConfigFactory promotionConfigFactory;
    @Autowired
    private PromotionConfigLoaderAdapter promotionConfigLoaderAdapter;
    @Autowired
    private PromotionAdminCustomServiceProxy promotionAdminCustomServiceProxy;
    @Autowired
    private NrPromotionConfigCache nrPromotionConfigCache;
    @Autowired
    private NacosConfig nacosConfig;

    /**
     * 提供给缓存查询工具使用，正常业务场景勿用
     * @return 活动工具列表
     */
    public Map<Long, ActivityTool> getActivityIdListForTool(List<Long> activityIdList) {
        if (CollectionUtils.isEmpty(activityIdList)) {
            return new HashMap<>(activityToolCacheMap);
        }
        return activityIdList.stream().map(activityToolCacheMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ActivityTool::getId, Function.identity(), (a, b) -> b));
    }

    /**
     * 提供给缓存查询工具使用，正常业务场景勿用
     * @return 活动工具列表
     */
    public Map<String, List<Long>> getSku2ActMapForTool(List<String> skuIdList) {
        if (CollectionUtils.isEmpty(skuIdList)) {
            return new HashMap<>(skuPackageToActivityIdsCacheMap);
        }
        return skuIdList.stream().collect(Collectors.toMap(Function.identity(), x -> skuPackageToActivityIdsCacheMap.getOrDefault(x, new ArrayList<>())));
    }

    /**
     * 提供给缓存查询工具使用，正常业务场景勿用
     * @return 活动工具列表
     */
    public Map<Integer, List<Long>> getChannel2ActMapForTool(List<Integer> channelIdList) {
        if (CollectionUtils.isEmpty(channelIdList)) {
            return new HashMap<>(channelToActivityIdsCacheMap);
        }
        return channelIdList.stream().collect(Collectors.toMap(Function.identity(), x -> channelToActivityIdsCacheMap.getOrDefault(x, new ArrayList<>())));
    }

    /**
     * 提供给缓存查询工具使用，正常业务场景勿用
     * @return 活动工具列表
     */
    public Map<Integer, List<Long>> getDepartment2ActMapForTool(List<Integer> departmentIdList) {
        if (CollectionUtils.isEmpty(departmentIdList)) {
            return new HashMap<>(productDepartmentToActivityIdsCacheMap);
        }
        return departmentIdList.stream().collect(Collectors.toMap(Function.identity(), x -> productDepartmentToActivityIdsCacheMap.getOrDefault(x, new ArrayList<>())));
    }

    /**
     * 提供给缓存查询工具使用，正常业务场景勿用
     * @return 活动工具列表
     */
    public Map<String, List<Long>> getOrg2ActMapForTool(List<String> orgCodeList) {
        if (CollectionUtils.isEmpty(orgCodeList)) {
            return new HashMap<>(orgCodeToActivityIdsCacheMap);
        }
        return orgCodeList.stream().collect(Collectors.toMap(Function.identity(), x -> orgCodeToActivityIdsCacheMap.getOrDefault(x, new ArrayList<>())));
    }

    /**
     * 提供给缓存查询工具使用，正常业务场景勿用
     * @return 活动工具列表
     */
    public List<Long> getGoodsCommonActivityIdsForTool() {
        return new ArrayList<>(goodsCommonActivityIds);
    }

    /**
     * 根据ID列表获取活动列表
     *
     * @param actIdList 活动ID列表
     * @return 活动工具列表
     */
    public List<ActivityTool> getCurrentTools(List<Long> actIdList) {
        if (CollectionUtils.isEmpty(actIdList)) {
            return Collections.emptyList();
        }
        // 迭代获取
        return actIdList.stream().map(activityToolCacheMap::get)
                .filter(Objects::nonNull)
                .filter(activityTool -> !PromotionToolType.purchaseToolList.contains(activityTool.getType()))
                .collect(Collectors.toList());
    }
    
    /**
     *
     * @param actIdList
     * @return
     */
    private List<ActivityTool> getSubsityCurrentTools(List<Long> actIdList) {
        if (CollectionUtils.isEmpty(actIdList)) {
            return Collections.emptyList();
        }
        // 迭代获取
        return actIdList.stream().map(activityToolCacheMap::get).filter(Objects::nonNull)
                .filter(this::checkTime)
                .filter(activityTool -> PromotionToolType.purchaseToolList.contains(activityTool.getType()))
                .sorted(Comparator.comparing(
                        tool -> Optional.ofNullable(ActivityTypeEnum.getByValue(tool.getType().getTypeId()))
                                .map(ActivityTypeEnum::getWeight).orElse(0)))
                .sorted(Comparator.comparing(ActivityTool::getId))
                .collect(Collectors.toList());
    }
    
    public List<ActivityTool> activitySearcher(final Integer channel, final String orgCode, final List<String> skuPackageList) {
        Stopwatch started = Stopwatch.createStarted();
        
        // skuActList
        Set<Long> actIdSet = this.getCurrentActIds(skuPackageList);
        // orgActList
        List<Long> orgActList = com.google.common.collect.Lists.newArrayList();
        if (StringUtils.isNotEmpty(orgCode)) {
            orgActList = this.getCurrentOrgCodeActIds(com.google.common.collect.Lists.newArrayList(orgCode));
        }
        
        // channelActList
        List<Long> channelActList = com.google.common.collect.Lists.newArrayList();
        if (Objects.nonNull(channel)) {
            channelActList = this.getCurrentChannelActIds(com.google.common.collect.Lists.newArrayList(channel));
        }
        
        List<Long> actualActIdList = com.google.common.collect.Lists.newArrayList(actIdSet);
        if (StringUtils.isNotEmpty(orgCode)) {
            actualActIdList.retainAll(orgActList);
        }
        if (Objects.nonNull(channel)) {
            actualActIdList.retainAll(channelActList);
        }
        log.info("getCurrent. orgCode:{}, channel:{}, skuPackageList:{} actIdList:{} actIdSet:{}, orgActList:{}, channelActList:{} ws:{}",
                orgCode, channel, skuPackageList, actualActIdList, actIdSet, orgActList, channelActList, started.elapsed(
                        TimeUnit.MILLISECONDS));
        
        
        return this.getSubsityCurrentTools(actualActIdList);
        
    }

    public List<ActivityTool> subsidyActivitySearcher(final Integer channel, final List<String> skuPackageList) {
        Stopwatch started = Stopwatch.createStarted();
        try {
            // 商品参加的活动
            Set<Long> actIdSet = getCurrentActIds(skuPackageList);
            if (CollectionUtils.isEmpty(actIdSet)) {
                return Collections.emptyList();
            }
            //根据渠道进行筛选
            List<Long> channelActList = new ArrayList<>();
            if (Objects.nonNull(channel)) {
                channelActList = getCurrentChannelActIds(Collections.singletonList(channel));
            }
            if (CollectionUtils.isEmpty(channelActList)) {
                return Collections.emptyList();
            }
            List<Long> actualActIdList = new ArrayList<>(actIdSet);
            actualActIdList.retainAll(channelActList);
            log.info("queryGovernmentModeSubsidy method, getCurrent. channel:{}, skuPackageList:{} actIdList:{} actIdSet:{}, channelActList:{} ws:{}",
                    channel, skuPackageList, actualActIdList, actIdSet, channelActList, started.elapsed(
                            TimeUnit.MILLISECONDS));
            return getAllSubsidyTools(actualActIdList);
        } finally {
            started.stop();
        }
    }

    public List<ActivityTool> nationalActivitySearcher(final Integer channel, final List<String> skuPackageList, List<Long> whiteIdList) {
        if (CollectionUtils.isEmpty(whiteIdList)) {
            return Collections.emptyList();
        }
        Stopwatch started = Stopwatch.createStarted();
        try {
            // 商品参加的活动
            Set<Long> actIdSet = getCurrentActIds(skuPackageList);
            if (CollectionUtils.isEmpty(actIdSet)) {
                return Collections.emptyList();
            }
            //根据渠道进行筛选
            List<Long> channelActList = new ArrayList<>();
            if (Objects.nonNull(channel)) {
                channelActList = getCurrentChannelActIds(Collections.singletonList(channel));
            }
            if (CollectionUtils.isEmpty(channelActList)) {
                return Collections.emptyList();
            }
            List<Long> actualActIdList = new ArrayList<>(actIdSet);
            actualActIdList.retainAll(channelActList);
            actualActIdList.retainAll(whiteIdList);
            log.info("queryGovernmentModeSubsidy method, getCurrent. channel:{}, skuPackageList:{} actIdList:{} actIdSet:{}, channelActList:{} ws:{}",
                    channel, skuPackageList, actualActIdList, actIdSet, channelActList, started.elapsed(
                            TimeUnit.MILLISECONDS));
            return getAllSubsidyTools(actualActIdList);
        } finally {
            started.stop();
        }
    }

    public List<ActivityTool> getAllSubsidyTools(List<Long> actIdList) {
        if (CollectionUtils.isEmpty(actIdList)) {
            return Collections.emptyList();
        }
        // 迭代获取
        return actIdList.stream().map(activityToolCacheMap::get).filter(Objects::nonNull)
                .filter(this::checkTime)
                .filter(activityTool -> PromotionToolType.subsidyToolList.contains(activityTool.getType()))
                .sorted(Comparator.comparing(ActivityTool::getId))
                .collect(Collectors.toList());
    }

    
    private boolean checkTime(ActivityTool tool) {
        long now = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        
        // 进行中
        return now >= tool.getUnixStartTime() && now < tool.getUnixEndTime();
    }

    /**
     * 获取对应sku 或packageId 能参加的活动列表
     *
     * @param skuPackageList 列表
     * @return 活动ID集
     */
    public Set<Long> getCurrentActIds(List<String> skuPackageList) {
        if (CollectionUtils.isEmpty(skuPackageList)) {
            return Collections.emptySet();
        }
        // 迭代获取
        final Set<Long> actIdSet = new HashSet<>();

        skuPackageList.forEach(skuPackage -> {
            List<Long> idList = skuPackageToActivityIdsCacheMap.getOrDefault(skuPackage, goodsCommonActivityIds);
            actIdSet.addAll(idList);
        });
        return actIdSet;
    }

    /**
     * 获取对应sku 或packageId 能参加的活动列表
     *
     * @param channels 列表
     * @return 活动ID集
     */
    public List<Long> getCurrentChannelActIds(List<Integer> channels) {
        if (CollectionUtils.isEmpty(channels)) {
            return Collections.emptyList();
        }
        // 迭代获取
        final Set<Long> actIdSet = new HashSet<>();
        channels.forEach(channel -> {
            List<Long> idList = channelToActivityIdsCacheMap.getOrDefault(channel, Collections.emptyList());
            actIdSet.addAll(idList);
        });
        return new ArrayList<>(actIdSet);
    }

    /**
     * 获取对应sku 或packageId 能参加的活动列表
     *
     * @param productDepartments 列表
     * @return 活动ID集
     */
    public List<Long> getCurrentDepartmentActIds(List<Integer> productDepartments) {
        if (CollectionUtils.isEmpty(productDepartments)) {
            return Collections.emptyList();
        }
        // 迭代获取
        final Set<Long> actIdSet = new HashSet<>();
        productDepartments.forEach(productDepartment -> {
            List<Long> idList = productDepartmentToActivityIdsCacheMap.getOrDefault(productDepartment, Collections.emptyList());
            actIdSet.addAll(idList);
        });
        return new ArrayList<>(actIdSet);
    }
    
    /**
     * 获取门店code对应的活动列表
     * @param orgCodeList
     * @return
     */
    public List<Long> getCurrentOrgCodeActIds(List<String> orgCodeList) {
        if (CollectionUtils.isEmpty(orgCodeList)) {
            return Collections.emptyList();
        }
        // 迭代获取
        final Set<Long> actIdSet = new HashSet<>();
        orgCodeList.forEach(orgCode -> {
            List<Long> idList = orgCodeToActivityIdsCacheMap.getOrDefault(orgCode, Collections.emptyList());
            actIdSet.addAll(idList);
        });
        return new ArrayList<>(actIdSet);
    }

    /**
     * 重新构建活动Tools 和 做倒排
     * <p>
     * 1. 从redis拉取活动列表 ActivityInfo List <br>
     * 2. 活动列表 convertTo 为对应PromotionConfig <br>
     * 3. 对应PromotionConfig convertTo 对应 ActivityTool <br>
     * 4. 增加或替换 activityToolMap 中Tool, 删除已经不存在的活动
     */
    public void rebuildCacheTask() throws Exception {
        try {
            long startTime = System.currentTimeMillis();
            log.info("rebuild activity pool start. startTime:{}", startTime);
            // 从Redis获取活动数据
            List<ActivityInfo> activityInfoList = getListFromCache();
            List<AbstractPromotionConfig> pulsePromotionConfigList = convertList(activityInfoList);

            List<AbstractPromotionConfig> promotionConfigList = Lists.newArrayList();
            promotionConfigList.addAll(pulsePromotionConfigList);

            if (nacosConfig.isActConfigCacheEnable()) {
                // CRM、国补、团购活动
                promotionConfigList.addAll(convertActivityConfigList(nrPromotionConfigCache.getAllFromCache()));

                // 排序
                promotionConfigList.sort(Comparator.comparing(config ->
                        Optional.ofNullable(ActivityTypeEnum.getByValue(config.getPromotionType().getTypeId()))
                                .map(ActivityTypeEnum::getWeight).orElse(0)));

                // 更新活动Tools，删除无效活动
                updatePromotionToolsV2(promotionConfigList);
                // 更新sku package 能参加的活动Id列表，删除无效sku package
                updateGidToActIdsV2(promotionConfigList);
                // 更新channel 能参加的活动列表
                updateChannelToActIdsV2(promotionConfigList);
                // 更新department 能参加活动列表
                updateProductDepartmentActIdsV2(promotionConfigList);
                // 更新orgCode 能参加的活动列表
                updateOrgCodeToActIdsV2(promotionConfigList);
            } else { // 上线后删除
                // 从新后台获取活动数据
                List<ActivityConfig> activityConfigList = promotionAdminCustomServiceProxy.queryActivityList();

                // 获取国补活动
                List<ActivityConfig> subsityActivityList = promotionAdminCustomServiceProxy.pageQuerySubsityActivityList(
                        DateTimeUtil.getCurrentTimes(TimeUnit.SECONDS), 1, 100);
                List<AbstractPromotionConfig> mdPromotionConfigList = convertActivityConfigList(activityConfigList);

                List<AbstractPromotionConfig> subsityPromotionList = convertActivityConfigList(subsityActivityList);
                promotionConfigList.addAll(mdPromotionConfigList);
                promotionConfigList.addAll(subsityPromotionList);

                // 排序
                promotionConfigList.sort(Comparator.comparing(config ->
                        Optional.ofNullable(ActivityTypeEnum.getByValue(config.getPromotionType().getTypeId()))
                                .map(ActivityTypeEnum::getWeight).orElse(0)));

                // 更新活动Tools，返回活动Id列表
                List<Long> currentIdList = updatePromotionTools(promotionConfigList);
                // 更新sku package 能参加的活动Id列表
                List<String> currentGidList = updateGidToActIds(promotionConfigList);
                // 更新channel 能参加的活动列表
                List<Integer> currentChannelList = updateChannelToActIds(promotionConfigList);
                // 更新department 能参加活动列表
                updateProductDepartmentActIds(promotionConfigList);
                // 更新orgCode 能参加的活动列表
                updateOrgCodeToActIds(promotionConfigList);

                // 删除无效活动 和 无效skuPackage能参加的活动列表
                removeInvalidTools(currentIdList);
                removeInvalidSkuPackageToActIds(currentGidList);
                removeInvalidChannelToActIds(currentChannelList);
            }

            // 删除无效活动 和 无效skuPackage能参加的活动列表
            long endTime = System.currentTimeMillis();
            log.info("rebuild activity pool end. endTime:{} ws:{}", endTime, endTime - startTime);
            String logPrefix = nacosConfig.isActConfigCacheEnable() ? "new" : "old";
            log.debug("{} activityToolCacheMap: {}", logPrefix, JacksonUtils.toJson(activityToolCacheMap));
            log.debug("{} skuPackageToActivityIdsCacheMap: {}", logPrefix, JacksonUtils.toJson(skuPackageToActivityIdsCacheMap));
            log.debug("{} channelToActivityIdsCacheMap: {}", logPrefix, JacksonUtils.toJson(channelToActivityIdsCacheMap));
            log.debug("{} productDepartmentToActivityIdsCacheMap: {}", logPrefix, JacksonUtils.toJson(productDepartmentToActivityIdsCacheMap));
            log.debug("{} orgCodeToActivityIdsCacheMap: {}", logPrefix, JacksonUtils.toJson(orgCodeToActivityIdsCacheMap));
        } catch (Exception e) {
            log.error("activity pool update error.", e);
            throw e;
        }
    }

    /**
     * 从缓存中获取活动列表
     * 1. 获取活动ID列表
     * 2. 获取活动ID对应活动缓存
     *
     * @return 活动缓存列表
     */
    private List<ActivityInfo> getListFromCache() {
        Long mills = System.currentTimeMillis();
        // 获取活动ID列表
        Long dateTime = TimeUnit.MILLISECONDS.toSeconds(mills);

        String dateTimeStr = DateTimeUtil.formatByPattern(mills, "yyyy-MM-dd HH:mm");

        List<Long> actIdList = v3ActivityOnlineMapper.listIdApprovedByDateStr(dateTimeStr);
        if (CollectionUtils.isEmpty(actIdList)) {
            return Collections.emptyList();
        }

        // 获取活动ID对应活动缓存
        log.info("refresh load count:{} actIdList:{} dateTime:{}", actIdList.size(), actIdList, dateTime);
        return multiGetActCache(actIdList);
    }

    private List<ActivityInfo> multiGetActCache(List<Long> actIdList) {
        List<ActivityInfo> activityInfoList = activityRedisDao.multiGetActivityByIdList(actIdList);
        if (CollectionUtils.isEmpty(activityInfoList)) {
            log.warn("actIds: {}", JacksonUtils.toJson(actIdList));
            return Collections.emptyList();
        }
        log.info("multi get act info ok. actIds:{}", JacksonUtils.toJson(actIdList));
        return activityInfoList;
    }

    /**
     * 将活动缓存转化为活动配置列表
     *
     * @param activityInfoList 缓存活动信息
     * @return 配置信息
     */
    private List<AbstractPromotionConfig> convertList(List<ActivityInfo> activityInfoList) {
        if (CollectionUtils.isEmpty(activityInfoList)) {
            return Collections.emptyList();
        }
        return activityInfoList.stream().map(this::convertItem)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 将活动缓存转化为活动配置列表
     *
     * @param activityInfoList 缓存活动信息
     * @return 配置信息
     */
    private List<AbstractPromotionConfig> convertActivityConfigList(List<ActivityConfig> activityInfoList) {
        if (CollectionUtils.isEmpty(activityInfoList)) {
            return Collections.emptyList();
        }
        return activityInfoList.stream().map(this::convertItem)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 转化具体活动配置
     *
     * @param cacheInfo 活动缓存信息
     * @return 优惠信息
     */
    private AbstractPromotionConfig convertItem(ActivityInfo cacheInfo) {
        log.info("convert cacheInfo to PromotionConfig. cacheInfo:{}", cacheInfo);
        if (cacheInfo == null || cacheInfo.getBasetype() == null) {
            return null;
        }
        TypeBase base = cacheInfo.getBasetype();
        PromotionToolType toolType = PromotionToolType.fromTypeId(base.getType());
        if (toolType == null) {
            log.error("get PromotionToolType failed. type:{}", base.getType());
            return null;
        }

        // 根据type 获取基本Config对象
        AbstractPromotionConfig promotionConfig;
        try {
            promotionConfig = promotionConfigFactory.getConfig(toolType);
            promotionConfigLoaderAdapter.load(promotionConfig, cacheInfo);
        } catch (BizError bizError) {
            log.error("convertItem err. err", bizError);
            return null;
        }
        return promotionConfig;
    }

    /**
     * 转化具体活动配置
     *
     * @param activityConfig 活动配置信息
     * @return 优惠信息
     */
    private AbstractPromotionConfig convertItem(ActivityConfig activityConfig) {
        log.info("convert activityConfig to PromotionConfig. cacheInfo:{}", activityConfig);
        if (activityConfig == null) {
            return null;
        }
        Integer promotionType = activityConfig.getPromotionType();
        PromotionToolType toolType = PromotionToolType.fromTypeId(promotionType);
        if (toolType == null) {
            log.error("get PromotionToolType failed. type:{}",promotionType);
            return null;
        }
        // 根据type 获取基本Config对象
        AbstractPromotionConfig promotionConfig;
        try {
            promotionConfig = promotionConfigFactory.getConfig(toolType);
            promotionConfigLoaderAdapter.load(promotionConfig, activityConfig);
        } catch (BizError bizError) {
            log.error("convertItem err. err", bizError);
            return null;
        }
        return promotionConfig;
    }

    /**
     * 更新活动列表 activityToolCacheMap
     *
     * @param promotionConfigList 优惠配置列表
     * @return 有效活动ID
     */
    private List<Long> updatePromotionTools(List<AbstractPromotionConfig> promotionConfigList) throws Exception {
        if (CollectionUtils.isEmpty(promotionConfigList)) {
            return Collections.emptyList();
        }
        List<Long> currentIdList = new ArrayList<>();
        try {
            lock.lock();
            // 加载新tools
            List<DSLGeneralPromotion> newTools = loadDSLPromotionFromConfig(promotionConfigList);
            // 更新或新增 并且记录成功的活动
            newTools.forEach(newTool -> {
                Long newPromotionId = newTool.getId();
                activityToolCacheMap.put(newTool.getId(), newTool);
                currentIdList.add(newPromotionId);
            });
        }finally {
            lock.unlock();
        }
        return currentIdList;
    }

    /**
     * 更新skuPackage对应活动列表 goodsActivityIdsMap
     *
     * @param promotionConfigList 优惠配置列表
     * @return 有效活动SkuPacakge
     */
    private List<String> updateGidToActIds(List<AbstractPromotionConfig> promotionConfigList) {
        if (CollectionUtils.isEmpty(promotionConfigList)) {
            return Collections.emptyList();
        }
        // 共同可参与活动
        List<Long> commonIdList = new ArrayList<>();
        promotionConfigList.forEach(config -> {
            if (config instanceof B2tVipDiscountPromotionConfig) {
                commonIdList.add(config.getPromotionId());
            }
        });
        // 生成新skuPackage 列表对应活动列表
        final Map<String, List<Long>> skuPackageActIdMap = new HashMap<>();
        promotionConfigList.forEach(config -> {
            if (!(config instanceof MultiPromotionConfig)) {
                return;
            }
            MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
            Set<String> skuPackages  = Optional.ofNullable(promotionConfig.getIncludeSkuPackages()).orElse(Collections.emptySet());
            Long actId = config.getPromotionId();
            skuPackages.forEach(skuPackage -> {
                List<Long> actIdList = skuPackageActIdMap.getOrDefault(skuPackage, new ArrayList<>(commonIdList));
                actIdList.add(actId);
                skuPackageActIdMap.put(skuPackage, actIdList);
            });
        });

        List<String> skuPackageList = new ArrayList<>();
        // 更新或新增 并且记录成功的活动
        try{
            lock.lock();
            skuPackageActIdMap.forEach((skuPackage, actIds) -> {
                skuPackageToActivityIdsCacheMap.put(skuPackage, actIds);
                skuPackageList.add(skuPackage);
            });
            goodsCommonActivityIds = commonIdList;
        }finally {
            lock.unlock();
        }
        return skuPackageList;
    }

    private List<Integer> updateChannelToActIds(List<AbstractPromotionConfig> promotionConfigList) {
        if (CollectionUtils.isEmpty(promotionConfigList)) {
            return Collections.emptyList();
        }
        // 生成新skuPackage 列表对应活动列表
        final Map<Integer, List<Long>> channelActIdMap = new HashMap<>();
        promotionConfigList.forEach(config -> {
            if (!(config instanceof MultiPromotionConfig)) {
                return;
            }
            MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
            List<Integer> channels = Optional.ofNullable(promotionConfig.getChannels()).orElse(Collections.emptyList());
            Long actId = config.getPromotionId();
            channels.forEach(channel -> {
                List<Long> actIdSet = channelActIdMap.getOrDefault(channel, new ArrayList<>());
                actIdSet.add(actId);
                channelActIdMap.put(channel, actIdSet);
            });
        });

        List<Integer> channelActIds = new ArrayList<>();
        // 更新或新增 并且记录成功的活动
        try {
            lock.lock();
            channelActIdMap.forEach((channel, actIds) -> {
                channelToActivityIdsCacheMap.put(channel, new ArrayList<>(actIds));
                channelActIds.add(channel);
            });
        }finally {
            lock.unlock();
        }
        return channelActIds;
    }

    private void updateProductDepartmentActIds(List<AbstractPromotionConfig> promotionConfigList) {
        if (CollectionUtils.isEmpty(promotionConfigList)) {
            return;
        }
        // 共同可参与活动
        List<Long> commonIdList = new ArrayList<>();
        promotionConfigList.forEach(config -> {
            if (!(config instanceof B2tVipDiscountPromotionConfig)) {
                commonIdList.add(config.getPromotionId());
            }
        });

        // 生成新department 列表对应活动列表
        final Map<Integer, List<Long>> departmentActIdMap = Maps.newHashMap();
        for (ScopeTypeEnum.ProductDepartment value : ScopeTypeEnum.ProductDepartment.values()) {
            List<Long> actIds = new ArrayList<>(commonIdList);
            departmentActIdMap.put(value.value,  actIds);
        }
        promotionConfigList.forEach(config -> {
            if (!(config instanceof B2tVipDiscountPromotionConfig)) {
                return;
            }
            try {

                B2tVipDiscountPromotionConfig promotionConfig = (B2tVipDiscountPromotionConfig) config;
                Integer department = promotionConfig.getDepartment();
                List<Long> actIds = departmentActIdMap.get(department);
                actIds.add(config.getPromotionId());
                departmentActIdMap.put(department, actIds);
            }catch (Exception e) {
                log.info("updateProductDepartmentActIds error. actId:{}", config.getPromotionId());
            }
        });

        // 更新或新增 并且记录成功的活动
        try {
            lock.lock();
            productDepartmentToActivityIdsCacheMap.putAll(departmentActIdMap);
        }finally {
            lock.unlock();
        }
    }

    /**
     * 更新orgCode索引
     * @param promotionConfigList
     * @return
     */
    private void updateOrgCodeToActIds(List<AbstractPromotionConfig> promotionConfigList) {
        if (CollectionUtils.isEmpty(promotionConfigList)) {
            return;
        }
        // 生成新orgCode 列表对应活动列表
        final Map<String, List<Long>> orgActIdMap = new HashMap<>();
        promotionConfigList.forEach(config -> {
            // 只有2024北京换新活动加载门店活动索引
            if (config instanceof GovernmentSubsidyPromotionConfig) {
                MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
                for (String orgCode : promotionConfig.getSelectOrgCodes()) {
                    List<Long> actIdSet = orgActIdMap.getOrDefault(orgCode, new ArrayList<>());
                    actIdSet.add(config.getPromotionId());
                    orgActIdMap.put(orgCode, actIdSet);
                }
            }
        });

        // 更新索引
        try {
            lock.lock();
            this.orgCodeToActivityIdsCacheMap = orgActIdMap;
        }finally {
            lock.unlock();
        }
    }

    /**
     * 更新活动列表 activityToolCacheMap
     *
     * @param promotionConfigList 优惠配置列表
     * @return 有效活动ID
     */
    private void updatePromotionToolsV2(List<AbstractPromotionConfig> promotionConfigList) throws Exception {
        if (CollectionUtils.isEmpty(promotionConfigList)) {
            return;
        }

        // 加载新tools
        List<DSLGeneralPromotion> newTools = loadDSLPromotionFromConfig(promotionConfigList);
        Map<Long, ActivityTool> tmpMap = new ConcurrentHashMap<>(newTools.size());
        // 更新或新增 并且记录成功的活动
        newTools.forEach(newTool -> {
            tmpMap.put(newTool.getId(), newTool);
        });
        activityToolCacheMap = tmpMap;
    }

    /**
     * 更新skuPackage对应活动列表 goodsActivityIdsMap
     *
     * @param promotionConfigList 优惠配置列表
     * @return 有效活动SkuPacakge
     */
    private void updateGidToActIdsV2(List<AbstractPromotionConfig> promotionConfigList) {
        if (CollectionUtils.isEmpty(promotionConfigList)) {
            return;
        }
        // 共同可参与活动
        List<Long> commonIdList = new ArrayList<>();
        promotionConfigList.forEach(config -> {
            if (config instanceof B2tVipDiscountPromotionConfig) {
                commonIdList.add(config.getPromotionId());
            }
        });
        // 生成新skuPackage 列表对应活动列表
        final Map<String, List<Long>> skuPackageActIdMap = new ConcurrentHashMap<>();
        promotionConfigList.forEach(config -> {
            if (!(config instanceof MultiPromotionConfig promotionConfig)) {
                return;
            }
            Set<String> skuPackages  = Optional.ofNullable(promotionConfig.getIncludeSkuPackages()).orElse(Collections.emptySet());
            Long actId = config.getPromotionId();
            skuPackages.forEach(skuPackage -> {
                if (skuPackageActIdMap.containsKey(skuPackage)) {
                    skuPackageActIdMap.get(skuPackage).add(actId);
                } else {
                    List<Long> actIdList = new ArrayList<>(commonIdList);
                    actIdList.add(actId);
                    skuPackageActIdMap.put(skuPackage, actIdList);
                }
            });
        });

        // 更新或新增 并且记录成功的活动
        skuPackageToActivityIdsCacheMap = skuPackageActIdMap;
        goodsCommonActivityIds = commonIdList;
    }

    private void updateChannelToActIdsV2(List<AbstractPromotionConfig> promotionConfigList) {
        if (CollectionUtils.isEmpty(promotionConfigList)) {
            return;
        }
        // 生成新skuPackage 列表对应活动列表
        final Map<Integer, List<Long>> channelActIdMap = new ConcurrentHashMap<>();
        promotionConfigList.forEach(config -> {
            if (!(config instanceof MultiPromotionConfig promotionConfig)) {
                return;
            }
            List<Integer> channels = Optional.ofNullable(promotionConfig.getChannels()).orElse(Collections.emptyList());
            Long actId = config.getPromotionId();
            channels.forEach(channel -> {
                if (channelActIdMap.containsKey(channel)) {
                    channelActIdMap.get(channel).add(actId);
                } else {
                    List<Long> actIdList = new ArrayList<>();
                    actIdList.add(actId);
                    channelActIdMap.put(channel, actIdList);
                }
            });
        });

        channelToActivityIdsCacheMap = channelActIdMap;
    }

    private void updateProductDepartmentActIdsV2(List<AbstractPromotionConfig> promotionConfigList) {
        if (CollectionUtils.isEmpty(promotionConfigList)) {
            return;
        }
        // 共同可参与活动
        List<Long> commonIdList = new ArrayList<>();
        promotionConfigList.forEach(config -> {
            if (!(config instanceof B2tVipDiscountPromotionConfig)) {
                commonIdList.add(config.getPromotionId());
            }
        });

        // 生成新department 列表对应活动列表
        final Map<Integer, List<Long>> departmentActIdMap = new ConcurrentHashMap<>();
        for (ScopeTypeEnum.ProductDepartment value : ScopeTypeEnum.ProductDepartment.values()) {
            List<Long> actIds = new ArrayList<>(commonIdList);
            departmentActIdMap.put(value.value,  actIds);
        }
        promotionConfigList.forEach(config -> {
            if (!(config instanceof B2tVipDiscountPromotionConfig)) {
                return;
            }
            try {

                B2tVipDiscountPromotionConfig promotionConfig = (B2tVipDiscountPromotionConfig) config;
                Integer department = promotionConfig.getDepartment();
                List<Long> actIds = departmentActIdMap.get(department);
                actIds.add(config.getPromotionId());
                departmentActIdMap.put(department, actIds);
            }catch (Exception e) {
                log.info("updateProductDepartmentActIds error. actId:{}", config.getPromotionId());
            }
        });

        // 更新或新增 并且记录成功的活动
        productDepartmentToActivityIdsCacheMap = departmentActIdMap;
    }
    
    /**
     * 更新orgCode索引
     * @param promotionConfigList
     * @return
     */
    private void updateOrgCodeToActIdsV2(List<AbstractPromotionConfig> promotionConfigList) {
        if (CollectionUtils.isEmpty(promotionConfigList)) {
            return;
        }
        // 生成新orgCode 列表对应活动列表
        final Map<String, List<Long>> orgActIdMap = new ConcurrentHashMap<>();
        promotionConfigList.forEach(config -> {
            // 只有2024北京换新活动加载门店活动索引
            if (config instanceof GovernmentSubsidyPromotionConfig) {
                MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
                for (String orgCode : promotionConfig.getSelectOrgCodes()) {
                    Long promotionId = config.getPromotionId();
                    if (orgActIdMap.containsKey(orgCode)) {
                        orgActIdMap.get(orgCode).add(promotionId);
                    } else {
                        List<Long> actIdList = new ArrayList<>();
                        actIdList.add(promotionId);
                        orgActIdMap.put(orgCode, actIdList);
                    }
                }
            }
        });
        
        // 更新索引
        this.orgCodeToActivityIdsCacheMap = orgActIdMap;
    }

    /**
     * 从Config加载活动
     *
     * @param configList 活动配置数据
     * @return 加载成功的优惠工具
     * @throws Exception 异常
     */
    private List<DSLGeneralPromotion> loadDSLPromotionFromConfig(List<AbstractPromotionConfig> configList) throws Exception {
        final ArrayList<DSLGeneralPromotion> tools = new ArrayList<>();
        final HashSet<Long> loadSuccessIdList = new HashSet<>();


        for (AbstractPromotionConfig config : configList) {

            //根据channel初始化不同平台的活动
            Set<BizPlatformEnum> platforms = config.getChannels().stream()
                    .map(channel -> BizPlatformEnum.findByChannel(channel))
                    .collect(Collectors.toSet());
            for (BizPlatformEnum bizPlatform : platforms) {
                DSLGeneralPromotion tool = getDSLGeneralPromotionByType(config.getPromotionType(), bizPlatform);
                if (tool == null) {
                    continue;
                }
                final boolean loadSuccess = tool.load(config);
                if (loadSuccess) {
                    tools.add(tool);
                    loadSuccessIdList.add(config.getPromotionId());
                } else {
                    log.error("load tool fail, config:{}", config);
                }
            }
        }

        log.info("new config load ok:{}", loadSuccessIdList);
        return tools;
    }

    /**
     * 根据活动类型获取优惠工具的定义
     * 注：分期免息活动没有优惠工具定义，所以要跳过
     *
     * @param promotionType 优惠工具类型
     * @return 优惠工具
     */
    private DSLGeneralPromotion getDSLGeneralPromotionByType(PromotionToolType promotionType, BizPlatformEnum bizPlatform) {
        try {
            return activityFactory.getByTypeAndBiz(promotionType, bizPlatform);
        } catch (Exception e) {
            log.warn("getDSLGeneralPromotionByType promotionType={},do not support DSLGeneralPromotion!!!", promotionType);
            return null;
        }
    }

    /**
     * 删除无效的活动tools<br>
     * 1. 筛选除无效活动ID列表
     * 2. 从Map中删除无效活动
     *
     * @param currentIdList 有效活动
     */
    private void removeInvalidTools(List<Long> currentIdList) {
        if (MapUtil.isEmpty(activityToolCacheMap)) {
            log.warn("activityToolCacheMap is empty.");
            return;
        }
        // invalidOldActIdList = diff(oldIdList, newIdList)
        Set<Long> oldActIdSet = activityToolCacheMap.keySet();
        List<Long> invalidOldActIdList = oldActIdSet.stream()
                .filter(itemId -> !currentIdList.contains(itemId))
                .collect(Collectors.toList());

        // 迭代删除无效活动Tool
        try {
            lock.lock();
            invalidOldActIdList.forEach(activityToolCacheMap::remove);
        }finally {
            lock.unlock();
        }
    }

    /**
     * 删除无效的skuPackage活动列表
     *
     * @param skuPackageList 列表
     */
    private void removeInvalidSkuPackageToActIds(List<String> skuPackageList) {
        if (MapUtil.isEmpty(skuPackageToActivityIdsCacheMap)) {
            log.warn("gidToActivityIdsCacheMap is empty.");
            return;
        }
        // oldSkuPackageSet = diff(oldSkuPackageSet, skuPackageList)
        Set<String> oldSkuPackageSet = skuPackageToActivityIdsCacheMap.keySet();
        List<String> invalidSkuPackageList = oldSkuPackageSet.stream()
                .filter(itemId -> !skuPackageList.contains(itemId))
                .collect(Collectors.toList());

        // 迭代删除无效活动Tool
        try {
            lock.lock();
            invalidSkuPackageList.forEach(skuPackageToActivityIdsCacheMap::remove);
        }finally {
            lock.unlock();
        }
    }

    /**
     * 删除无效的Channel活动列表
     *
     * @param channelIdList 列表
     */
    private void removeInvalidChannelToActIds(List<Integer> channelIdList) {
        if (MapUtil.isEmpty(channelToActivityIdsCacheMap)) {
            log.warn("gidToActivityIdsCacheMap is empty.");
            return;
        }
        Set<Integer> oldChannelSet = channelToActivityIdsCacheMap.keySet();
        List<Integer> invalidChannelList = oldChannelSet.stream()
                .filter(itemId -> !channelIdList.contains(itemId))
                .collect(Collectors.toList());

        // 迭代删除无效活动Tool
        try {
            lock.lock();
            invalidChannelList.forEach(channelToActivityIdsCacheMap::remove);
        }finally {
            lock.unlock();
        }
    }

	@Override
	public void afterPropertiesSet() throws Exception {
		log.info("begin to rebuild activity pool on server Started.");
        try {
            rebuildCacheTask();
        } catch (Exception e) {
            log.error("活动缓存初次刷新失败，停止启动");
            throw e;
        }
    }
}
