package com.xiaomi.nr.promotion.entity.redis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 商品层级关系缓存实体
 *
 * <AUTHOR>
 * @date 2021/5/14
 */
@Data
public class GoodsHierarchy implements Serializable {
    private static final long serialVersionUID = -4745927657340461442L;

    /**
     * sku
     */
    private String sku = "";

    /**
     * goodsId
     */
    @SerializedName("goods_id")
    private String goodsId = "";

    /**
     * commodity_id
     */
    @SerializedName("commodity_id")
    private String commodityId = "";

    /**
     * group
     */
    private List<String> group;

    /**
     * group_version
     */
    @SerializedName("group_version")
    private Map<String, Long> groupVersion;
}
