package com.xiaomi.nr.promotion.model.promotionconfig.loader;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.goods.gis.dto.goodsInfoNew.GoodsMultiInfoDTO;
import com.xiaomi.nr.md.promotion.admin.api.constant.ProductIdTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.ProductLevelEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ProductPolicy;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.BuyGiftRule;
import com.xiaomi.nr.promotion.entity.redis.*;
import com.xiaomi.nr.promotion.enums.BooleanV2Enum;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyGiftPromotionConfig;
import com.xiaomi.nr.promotion.rpc.gis.GoodsStockServiceProxy;
import com.xiaomi.nr.promotion.tool.PromotionDescRuleTool;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 买赠配置数据加载
 *
 * <AUTHOR>
 * @date 2021/6/3
 */
@Slf4j
@Component
public class BuyGiftPromotionConfigLoader implements PromotionConfigLoader {

    @Autowired
    private GoodsStockServiceProxy goodsStockServiceProxy;

    @Override
    public boolean support(AbstractPromotionConfig promotionConfig) {
        if (promotionConfig instanceof BuyGiftPromotionConfig) {
            return true;
        }
        return false;
    }

    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityInfo activityInfo) throws BizError {
        if (activityInfo == null || promotionConfig == null) {
            log.error("[BuyGiftPromotionConfigLoader] activityInfo is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "activityInfo is null");
        }
        TypeBase typeBase = activityInfo.getBasetype();
        Condition condition = activityInfo.getCondition();
        Policy policy = activityInfo.getPolicy();
        if (typeBase == null || condition == null || policy == null) {
            log.error("[BuyGiftPromotionConfigLoader] baseType or policy or policy is null. actInfo:{}", activityInfo);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "baseType or policy or policy is null");
        }
        Long id = typeBase.getId();
        BuyGiftPromotionConfig giftConfig = (BuyGiftPromotionConfig) promotionConfig;
        if (CollectionUtils.isEmpty(policy.getPolicies()) || policy.getPolicies().get(0) == null) {
            log.error("policy gift is invalid. actId:{} policy:{} ", id, policy);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "policy is invalid");
        }
        PolicyLevel policyLevel = policy.getPolicies().get(0);
        List<FillGoodsGroup> fillGoodsGroupList = policyLevel.getIncludedGoodsGroup();
        if (CollectionUtils.isEmpty(fillGoodsGroupList)) {
            log.error("policy gift fillGoodsGroupList empty. actId:{} policy:{} ", id, policy);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "policy fillGoodsGroupList empty");
        }
        // 买赠只有一组
        Set<String> skuPackageSet = new HashSet<>();
        FillGoodsGroup fillGoodsGroup = fillGoodsGroupList.get(0);
        if (fillGoodsGroup.getJoinGoods() == null || fillGoodsGroup.getQuota() == null) {
            log.error("policy gift fillGoodsGroupList joinGoods or Quota is empty. actId:{} goodsGroup:{} ", id, fillGoodsGroup);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "policy fillGoodsGroupList joinGoods or Quota is empty");
        }
        List<String> skuList = Optional.ofNullable(fillGoodsGroup.getJoinGoods().getSku()).orElse(Collections.emptyList());
        List<String> packageList = Optional.ofNullable(fillGoodsGroup.getJoinGoods().getPackages()).orElse(Collections.emptyList());
        skuPackageSet.addAll(skuList);
        skuPackageSet.addAll(packageList);
        // 主商品
        giftConfig.setIncludeGoodsGroup(fillGoodsGroup);
        giftConfig.setIncludeSkuPackages(skuPackageSet);

        // 赠品信息
        RuleEle ruleEle = policyLevel.getRule();
        if (ruleEle == null || ruleEle.getGiftGoods() == null) {
            log.error("policy gift ruleEle err. actId:{} goodsGroup:{} ", id, ruleEle);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "policy ruleEle err");
        }
        giftConfig.setGiftGoods(ruleEle.getGiftGoods());
        List<Long> buyGiftSkuIds = getSkuIds(ruleEle);
        Map<Long, GoodsMultiInfoDTO> skuBaseInfo = goodsStockServiceProxy.getSkuBaseInfo(buyGiftSkuIds);
        PromotionDescRuleTool.composePromotionText(giftConfig, skuBaseInfo);
        // F会员
        if (condition.getIsFMember() == null) {
            log.error("condition isFMember err. actId:{} ", id);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "condition isFMember err");
        }
        giftConfig.setIsFMember(condition.getIsFMember());
    }

    private List<Long> getSkuIds(RuleEle ruleEle) {
        Goods giftGoods = ruleEle.getGiftGoods();
        List<SkuGroup> skuGroupList = giftGoods.getSkuGroupList();
        List<Long> skuList = new ArrayList<>();
        for (SkuGroup skuGroup : skuGroupList) {
            skuList.addAll(skuGroup.getListInfo().stream().map(GiftBargainGroup::getSku).collect(Collectors.toList()));
        }
        return skuList;
    }
    
    
    /**
     * 车商城阶段   ---  按需加载
     * @param promotionConfig 配置对象
     * @param activityConfig  活动缓存
     * @throws BizError
     */
    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityConfig activityConfig) throws BizError {
        if (activityConfig == null || promotionConfig == null) {
            log.error("[BuyGiftPromotionConfigLoader] activityConfig is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "activityConfig is null");
        }
        
        BuyGiftPromotionConfig giftConfig = (BuyGiftPromotionConfig) promotionConfig;
        
        List<Long> masterList = Lists.newArrayList();
        Map<Long, List<GiftBargainGroup>> slaveGroupMap = Maps.newHashMap();
        for (ProductPolicy productPolicy : activityConfig.getProductPolicyList()) {
            // ssu id
            if (Objects.equals(productPolicy.getProductIdType(), ProductIdTypeEnum.SSU.code)) {
                if (Objects.equals(productPolicy.getProductLevel(), ProductLevelEnum.MASTER.code)) {
                    masterList.add(productPolicy.getProductId());
                }
                
                if (Objects.equals(productPolicy.getProductLevel(), ProductLevelEnum.SLAVE.code)) {
                    List<GiftBargainGroup> groupList = slaveGroupMap.getOrDefault(productPolicy.getProductGroup().longValue(), new ArrayList<>());
                    GiftBargainGroup giftBargainGroup = new GiftBargainGroup();
                    giftBargainGroup.setSsuId(productPolicy.getProductId());
                    giftBargainGroup.setGiftLimitNum(productPolicy.getStock().longValue());
                    
                    BuyGiftRule.ProductRule productRule = GsonUtil.fromJson(productPolicy.getRule(),
                            BuyGiftRule.ProductRule.class);
                    if (Objects.nonNull(productRule)) {
                        giftBargainGroup.setGiftBaseNum(Optional.ofNullable(productRule.getGiftBaseNum()).orElse(0).longValue());
                    }
                    
                    groupList.add(giftBargainGroup);
                    slaveGroupMap.put(productPolicy.getProductGroup().longValue(), groupList);
                    
                }
            }
        }
        
        FillGoodsGroup fillGoodsGroup = new FillGoodsGroup();
        CompareItem item = new CompareItem();
        item.setSsuId(masterList);
        fillGoodsGroup.setJoinGoods(item);
        giftConfig.setIncludeGoodsGroup(fillGoodsGroup);
        
        Goods giftGoods = new Goods();
        BuyGiftRule buyGiftRule = GsonUtil.fromJson(activityConfig.getRule(), BuyGiftRule.class);
        if (buyGiftRule != null) {
            giftGoods.setIgnoreStock(buyGiftRule.isIgnoreStock() ? BooleanV2Enum.YES.getValue() : BooleanV2Enum.NO.getValue());
        }
        giftConfig.setConfigUrl(buyGiftRule.getConfigUrl());
        List<SkuGroup> skuGroupList = Lists.newArrayList();
        slaveGroupMap.forEach((groupId, listInfo) -> {
            SkuGroup skuGroup = new SkuGroup();
            skuGroup.setGroupId(groupId);
            skuGroup.setListInfo(listInfo);
            skuGroupList.add(skuGroup);
        });
        
        giftGoods.setSkuGroupList(skuGroupList);
        giftConfig.setGiftGoods(giftGoods);
    }
}
