package com.xiaomi.nr.promotion.engine;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.api.dto.model.CouponInfo;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutContext;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponOwnedInfo;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.List;

/**
 * 促销工具-优惠劵
 *
 */
public interface CouponTool extends PromotionTool, BizPlatformComponent {

    /**
     * 获取券ID
     * @return
     */
    Long getCouponId();

    /**
     * 获取优惠券类型
     * @return
     */
    CouponTypeEnum getCouponType();


    /**
     *
     * @param request 结算V2请求
     * @return
     */
    CouponCheckoutResult checkoutCoupon(CheckoutPromotionRequest request, CheckoutContext context) throws BizError;

    /**
     *
     * @param couponCheckoutContext 上下文
     * @return
     */
    CouponCheckoutResult checkoutCoupon(CouponCheckoutContext couponCheckoutContext) throws BizError;



    /**
     * 更新分摊信息
     * @param request
     * @param context
     * @param result
     */
    void updateCartsReduce(CheckoutPromotionRequest request, CheckoutContext context, CouponCheckoutResult result);

    /**
     * 更新商品分摊
     * @param cartItemList
     * @param result
     */
    void updateItemReduce(List<CartItem> cartItemList, CouponCheckoutResult result);

    /**
     * 更新商品分摊
     * @param cartItemList
     * @param result
     */
    void updateItemReduce(CouponCheckoutContext context, CouponCheckoutResult result);

    /**
     * 生成购物车的展示信息
     * @return
     */
    Coupon generateCartCoupon();


    /**
     * 生成结算优惠信息
     * @param result
     */
    CouponInfo generateCouponInfo(CouponCheckoutResult result);

    /**
     * 加载券信息
     * @param couponOwnedInfo
     * @return
     * @throws BizError
     */
    @Deprecated
    boolean load(CouponOwnedInfo couponOwnedInfo) throws BizError;

    /**
     * 从加载券信息，新优惠券结算
     * @param checkoutCoupon
     * @return
     * @throws BizError
     */
    boolean load(CheckoutCoupon checkoutCoupon) throws BizError;

}
