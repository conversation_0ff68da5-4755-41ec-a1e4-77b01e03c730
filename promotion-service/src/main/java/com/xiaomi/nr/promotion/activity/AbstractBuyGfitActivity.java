package com.xiaomi.nr.promotion.activity;

import com.xiaomi.nr.promotion.api.dto.model.NumLimitRule;
import com.xiaomi.nr.promotion.api.dto.model.PolicyNew;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.FillGoodsGroup;
import com.xiaomi.nr.promotion.entity.redis.Goods;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.ActRespConverter;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/18
 */
public abstract class AbstractBuyGfitActivity extends AbstractActivityTool {
    
    /**
     * 主商品列表
     */
    protected List<FillGoodsGroup> includeGoodsGroups;
    /**
     * 赠品信息
     */
    protected Goods giftGoods;
    
    
    @Override
    public abstract boolean load(AbstractPromotionConfig config) throws BizError;
    
    @Override
    public abstract ActivityDetail getActivityDetail();
    
    @Override
    public abstract PromotionToolType getType();
    
    @Override
    protected abstract DSLStream getDSLDefinition();
    
    /**
     * 构建购物车优惠数据
     *
     * @param context 上下文
     * @return 优惠促销信息
     */
    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) {
        return null;
    }
    
}
