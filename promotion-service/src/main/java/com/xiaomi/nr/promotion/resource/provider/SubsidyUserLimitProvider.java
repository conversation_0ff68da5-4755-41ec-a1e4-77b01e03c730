package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.dao.redis.ActUserLimitPo;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SubsidyUserLimitProvider implements ResourceProvider<SubsidyUserLimitProvider.ResContent> {
    private ResourceObject<SubsidyUserLimitProvider.ResContent> resourceObject;
    @Autowired
    private ActivityRedisDao activityRedisDao;


    @Override
    public ResourceObject<ResContent> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<SubsidyUserLimitProvider.ResContent> object) {
        this.resourceObject = object;
    }

    @Override
    public void lock() throws BizError {
        log.info("lock subsidy limit resource. orderId:{}, promotionId:{} countMap:{}", resourceObject.getOrderId(), resourceObject.getPromotionId(), GsonUtil.toJson(resourceObject));
        activityRedisDao.batchConsumeSubsidyStockV2(this.getResource().getContent().actUserLimitPoList);
        log.info("lock subsidy limit ok. resourceObject:{}", GsonUtil.toJson(resourceObject));



    }

    @Override
    public void consume() throws BizError {
        log.info("consume subsidy limit resource. resourceObject:{}",  GsonUtil.toJson(resourceObject));
    }

    @Override
    public void rollback() throws BizError {
        log.info("rollback subsidy limit resource. orderId:{}, promotionId:{} countMap:{}", resourceObject.getOrderId(), resourceObject.getPromotionId(), GsonUtil.toJson(resourceObject));
        activityRedisDao.batchRollbackSubsidyLimitV2(this.getResource().getContent().actUserLimitPoList);
        log.info("rollback subsidy limit ok. resourceObject:{}", GsonUtil.toJson(resourceObject));

    }

    @Override
    public String conflictText() {
        return "处理补贴限购失败";
    }

    @Data
    public static class ResContent {
        private Long uid;

        private String idCard;

        private List<ActUserLimitPo> actUserLimitPoList;

    }



}
