package com.xiaomi.nr.promotion.resource.impl;

import com.xiaomi.nr.promotion.api.dto.enums.SubmitTypeEnum;
import com.xiaomi.nr.promotion.constant.SystemConstant;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionResourceDetailMapper;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionResourceStatusMapper;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.PromotionResourceDetail;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.PromotionResourceStatus;
import com.xiaomi.nr.promotion.enums.TradeFromEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.mq.consumer.entity.OrderData;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.model.*;
import com.xiaomi.nr.promotion.resource.provider.CouponProvider;
import com.xiaomi.nr.promotion.resource.provider.PointProvider;
import com.xiaomi.nr.promotion.rpc.order.SubmitOrderServiceProxy;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 资源管理器，负责优惠资源的一致性调度
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
@Slf4j
@Service
public class ResourceManager {
    private static final Logger statisticsLogger = LoggerFactory.getLogger("timeStatisticLogger");
    /**
     * 强假设：15秒内订单的事务已经结束，主从同步已完成，如果太大，容易误报优惠券不存在的问题
     */
    private final static long KEEP_INTERVAL = 30 * 60;
    /**
     * 没有处理的过期时间,需要报警
     */
    private final static long EXPIRE_INTERVAL = 60 * 60;

    @Autowired
    private ResourceProviderFactory providerFactory;
    @Autowired
    private ResourceManagerTransactions transactions;
    @Autowired
    private PromotionResourceDetailMapper promotionResourceDetailMapper;
    @Autowired
    private PromotionResourceStatusMapper promotionResourceStatusMapper;
    @Autowired
    private SubmitOrderServiceProxy submitOrderServiceProxy;

    /**
     * 锁定资源，资源处理try阶段
     *
     * @param resourceManageContext 资源管理上下文
     * @throws Exception 异常
     */
    public void lock(ResourceManageContext resourceManageContext) throws Exception {
        final long start = System.currentTimeMillis();
        final long orderId = resourceManageContext.getOrderId();
        List<ResourceProvider<?>> providers = resourceManageContext.getProviders();
        log.info("ResourceManager lock start orderId={} providerSize={}", orderId, providers.size());
        if (CollectionUtils.isEmpty(providers)) {
            return;
        }

        //合并资源
        List<ResourceProvider<?>> mergeResourceProviders = TransactionResourceTool.mergeResourceProviders(providers);
        resourceManageContext.setProviders(mergeResourceProviders);

        // 初始化资源表，锁资源id
        List<PromotionResourceDetail> details = mergeResourceProviders.stream().map(provider -> PromotionResourceConverter.toDetail(provider.getResource())).collect(Collectors.toList());


        try {
            transactions.initResourceData(resourceManageContext, details);
        } catch (BizError e) {
            log.error("Init resource data failed. orderId:{} items:{} BizErr:{}", orderId, GsonUtil.toJson(details), e);
            throw e;
        } catch (Exception e) {
            log.error("Init resource data failed. orderId:{} items:{} err:{}", orderId, GsonUtil.toJson(details), e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, makeErrorMessage(e), e);
        }
        long point1 = System.currentTimeMillis();
        statisticsLogger.info("[timeStatistic],[lock],initTime={}", (point1 - start));

        // 调用资源对象锁定具体资源，reduce规约逻辑：有一个失败则认为锁定失败
        // pair<是否成功， 错误消息>

        //合并优惠券请求
        mergeCouponProvider(mergeResourceProviders);

        Pair<Boolean, Exception> resultPair = mergeResourceProviders.parallelStream().map(provider -> {
            // 并发锁资源, 并修改资源状态
            try {
                provider.lock();
                return Pair.of(true, (Exception) null);
            } catch (Exception e) {
                log.error("Lock resource failed. orderId:{} items:{}", orderId, GsonUtil.toJson(details), e);
                return Pair.of(false, e);
            }
        }).reduce((pair1, pair2) -> (pair1.getLeft() ? pair2 : pair1)).orElse(Pair.of(false, null));
        if (!resultPair.getLeft()) {
            throw resultPair.getRight();
        }
        long point2 = System.currentTimeMillis();
        statisticsLogger.info("[timeStatistic],[lock],providerLockTime={}", (point2 - point1));

        // 修改资源表，改为锁定状态
        try {
            transactions.updateResourceStatus(resourceManageContext, ResourceStatus.LOCKED);
        } catch (Exception e) {
            log.error("Update resource status to locked failed. orderId:{} items:{} err:{}", orderId, GsonUtil.toJson(details), e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, makeErrorMessage(e), e);
        }
        long point3 = System.currentTimeMillis();
        statisticsLogger.info("[timeStatistic],[lock],updateSourceTime={}", (point3 - point2));

        statisticsLogger.info("[timeStatistic],[lock],initTime={},providerLockTime={},updateSourceTime={},totalTime={}", (point1 - start), (point2 - point1), (point3 - point2), (point3 - start));
    }

    private void mergeCouponProvider(List<ResourceProvider<?>> mergeResourceProviders) throws BizError {
        List<ResourceProvider<?>> couponProviders = mergeResourceProviders.stream()
                .filter(provider -> provider.getResource().getResourceType() == ResourceType.COUPON)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(couponProviders)) {
            mergeResourceProviders.removeIf(provider -> provider.getResource().getResourceType() == ResourceType.COUPON);
            ResourceProvider<?> mergeCouponProvider = generateMergeCouponProvider(couponProviders);
            mergeResourceProviders.add(mergeCouponProvider);
        }
    }

    /**
     * 优惠券合并provider，减少调用次数
     *
     * @param couponProviders
     * @return
     * @throws BizError
     */
    private ResourceProvider<?> generateMergeCouponProvider(List<ResourceProvider<?>> couponProviders) throws BizError {

        ResourceProvider<?> singleProvider = couponProviders.get(0);

        ResourceObject<CouponProvider.ResContent> singleResource = (ResourceObject<CouponProvider.ResContent>) singleProvider.getResource();
        CouponProvider.ResContent singleContent = singleResource.getContent();

        ResourceObject<CouponProvider.ResContent> resourceObject = new ResourceObject<>();
        resourceObject.setResourceType(singleResource.getResourceType());
        resourceObject.setOrderId(singleResource.getOrderId());
        resourceObject.setPid(-1L);

        CouponProvider.MultiResContent multiResContent = new CouponProvider.MultiResContent();
        List<CouponProvider.CouponItem> couponItemList = new ArrayList<>();
        multiResContent.setCouponItems(couponItemList);
        multiResContent.setOffline(singleContent.getOffline());
        multiResContent.setOrgCode(singleContent.getOrgCode());
        multiResContent.setUserId(singleContent.getUserId());
        multiResContent.setClientId(singleContent.getClientId());
        multiResContent.setSubmitType(singleContent.getSubmitType());
        multiResContent.setBizPlatform(singleContent.getBizPlatform());
        multiResContent.setVid(singleContent.getVid());


        for (ResourceProvider<?> couponProvider : couponProviders) {
            ResourceObject<CouponProvider.ResContent> resource = (ResourceObject<CouponProvider.ResContent>) couponProvider.getResource();
            CouponProvider.ResContent originContent = resource.getContent();
            //获取需要解锁的券
            if (CheckoutCartTool.validCouponId(originContent.getUsedCouponId())) {
                multiResContent.setUsedCouponId(originContent.getUsedCouponId());
            }

            if (!CheckoutCartTool.validCouponId(originContent.getId())) {
                continue;
            }

            CouponProvider.CouponItem couponItem = new CouponProvider.CouponItem();
            couponItem.setCode(originContent.getCode());
            couponItem.setId(originContent.getId());
            couponItem.setReduceExpress(originContent.getReduceExpress() == null ? new BigDecimal(0) : originContent.getReduceExpress());
            couponItem.setReplaceMoney(originContent.getReplaceMoney() == null ? new BigDecimal(0) : originContent.getReplaceMoney());
            couponItemList.add(couponItem);

        }
        resourceObject.setContent(multiResContent);
        CouponProvider multiCouponProvider = (CouponProvider) providerFactory.getProvider(ResourceType.COUPON);
        multiCouponProvider.initResource(resourceObject);
        return multiCouponProvider;
    }

    /**
     * 资源提交
     *
     * @param context 资源管理上下文
     * @throws BizError 业务异常
     */
    public void commit(ResourceManageContext context) throws BizError {
        long start = System.currentTimeMillis();
        long orderId = context.getOrderId();
        log.info("ResourceManager commit start orderId={} ", orderId);
        PromotionResourceStatus resourceStatus = promotionResourceStatusMapper.getStatusByOrderId(orderId);
        if (resourceStatus == null) {
            // 这里为临时，后续一段时间后需要报错 2021-12-10
            log.warn("notice resource-status is null. orderId:{}", orderId);
            return;
        }
        if (Objects.equals(ResourceStatus.COMMITED.getValue(), resourceStatus.getStatus())) {
            log.warn("Resource status is committed. orderId:{}", orderId);
            return;
        }

        // 获取订单的资源，恢复provider数据
        List<PromotionResourceDetail> resources = promotionResourceDetailMapper.getDetailByOrderId(orderId);
        if (CollectionUtils.isEmpty(resources)) {
            return;
        }
        List<ResourceProvider<?>> providers = recoverProviders(resources);
        long point1 = System.currentTimeMillis();
        statisticsLogger.info("[timeStatistic],[commit],initTime={}", (point1 - start));

        //合并优惠券请求
        mergeCouponProvider(providers);


        // 调用请求资源提供者锁定具体资源
        try {
            for (ResourceProvider provider : providers) {
                provider.consume();
            }
        } catch (Exception e) {
            log.error("Commit resource failed. orderId:{} items:{}", orderId, resources, e);
            if (e instanceof BizError) {
                throw e;
            } else {
                throw ExceptionHelper.create(GeneralCodes.InternalError, makeErrorMessage(e), e);
            }
        }
        long point2 = System.currentTimeMillis();
        statisticsLogger.info("[timeStatistic],[commit],consumeTime={}", (point2 - point1));

        // 修改资源表，改为提交状态
        try {
            transactions.updateResourceStatus(context, ResourceStatus.COMMITED);
        } catch (Exception e) {
            log.error("Update resource status to locked failed. orderId:{} items:{} err:{}", orderId, resources, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, makeErrorMessage(e), e);
        }
        long point3 = System.currentTimeMillis();
        statisticsLogger.info("[timeStatistic],[commit],initTime={},consumeTime={}, updateTime={},totalTime={}", (point1 - start), (point2 - point1), (point3 - point2), (point3 - start));
    }

    /**
     * 资源回滚
     *
     * @param context 资源管理上下文
     * @throws BizError 业务异常
     */
    public void rollback(ResourceManageContext context) throws BizError {
        long start = System.currentTimeMillis();
        long orderId = context.getOrderId();
        log.info("ResourceManager rollback start orderId={} ", orderId);
        PromotionResourceStatus resourceStatus = promotionResourceStatusMapper.getStatusByOrderId(orderId);
        if (resourceStatus == null) {
            // 这里为临时，后续一段时间后需要报错 2021-12-10
            log.warn("notice resource-status is null. orderId:{}", orderId);
            return;
        }
        if (Objects.equals(ResourceStatus.ROLLBACKED.getValue(), resourceStatus.getStatus())) {
            log.warn("Resource status is rollback. orderId:{}", orderId);
            return;
        }

        // 获取订单的资源，恢复provider数据
        List<PromotionResourceDetail> resources = promotionResourceDetailMapper.getDetailByOrderId(orderId);
        if (CollectionUtils.isEmpty(resources)) {
            return;
        }
        List<ResourceProvider<?>> providers = recoverProviders(resources);
        long point1 = System.currentTimeMillis();
        statisticsLogger.info("[timeStatistic],[rollback],initTime={}", (point1 - start));

        //合并优惠券请求
        mergeCouponProvider(providers);

        // 调用请求资源提供者锁定具体资源
        try {
            for (ResourceProvider provider : providers) {
                int returnStatus = provider.getResource().getReturnStatus();
                // 过滤已经返还或者 取消返还资源 或者初始化状态的
                if (Objects.equals(ReturnStatus.RETURNED.getValue(), returnStatus)
                        || Objects.equals(ReturnStatus.CANCEL_RETURN.getValue(), returnStatus)) {
                    continue;
                }
                provider.rollback();
            }
        } catch (Exception e) {
            log.error("rollback resource failed. orderId:{} items:{}", orderId, resources, e);
            if (e instanceof BizError) {
                throw e;
            } else {
                throw ExceptionHelper.create(GeneralCodes.InternalError, makeErrorMessage(e), e);
            }
        }
        long point2 = System.currentTimeMillis();
        statisticsLogger.info("[timeStatistic],[rollback],rollBackTime={}", (point2 - point1));

        // 修改资源表，改为回滚状态
        try {
            transactions.updateResourceStatus(context, ResourceStatus.ROLLBACKED);
            log.info("update resource rollback status ok.{}", orderId);
        } catch (Exception e) {
            log.error("Update resource status to locked failed. orderId:{} items:{} err:{}", orderId, resources, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, makeErrorMessage(e), e);
        }
        long point3 = System.currentTimeMillis();
        statisticsLogger.info("[timeStatistic],[rollback],updateTime={}", (point3 - point2));

        statisticsLogger.info("[timeStatistic],[rollback],initTime={},rollBackTime={}, updateTime={},totalTime={}", (point1 - start), (point2 - point1), (point3 - point2), (point3 - start));
    }

    /**
     * 资源回滚
     *
     * @param context 资源管理上下文
     * @throws BizError 业务异常
     */
    public void partRollback(ResourceManageContext context) throws BizError {
        long start = System.currentTimeMillis();
        long orderId = context.getOrderId();
        log.info("ResourceManager partRollback start orderId={} ", orderId);
        PromotionResourceStatus resourceStatus = promotionResourceStatusMapper.getStatusByOrderId(orderId);
        if (resourceStatus == null) {
            log.warn("notice resource-status is null. orderId:{}", orderId);
            return;
        }
        if (Objects.equals(ResourceStatus.ROLLBACKED.getValue(), resourceStatus.getStatus())) {
            log.warn("Resource status is partRollback. orderId:{}", orderId);
            return;
        }
        List<ResourceType> resourceTypes = context.getResourceTypes();
        if (CollectionUtils.isEmpty(resourceTypes)) {
            return;
        }

        // 获取订单的资源，恢复provider数据
        List<PromotionResourceDetail> resources = promotionResourceDetailMapper.getDetailByOrderId(orderId);
        if (CollectionUtils.isEmpty(resources)) {
            return;
        }
        List<ResourceProvider<?>> providers = recoverProviders(resources, context.getOrderData());
        long point1 = System.currentTimeMillis();
        statisticsLogger.info("[timeStatistic],[partRollback],initTime={}", (point1 - start));

        //合并优惠券请求
        mergeCouponProvider(providers);

        // 调用请求资源提供者锁定具体资源
        try {
            for (ResourceProvider provider : providers) {
                int returnStatus = provider.getResource().getReturnStatus();
                // 过滤已经返还或者 取消返还资源 或者初始化状态的
                if (Objects.equals(ReturnStatus.RETURNED.getValue(), returnStatus)
                        || Objects.equals(ReturnStatus.CANCEL_RETURN.getValue(), returnStatus)) {
                    continue;
                }

                // 只处理需要处理的
                if (resourceTypes.contains(provider.getResource().getResourceType())) {
                    provider.rollback();
                }
            }
        } catch (Exception e) {
            log.error("rollback resource failed. orderId:{} items:{}", orderId, resources, e);
            if (e instanceof BizError) {
                throw e;
            } else {
                throw ExceptionHelper.create(GeneralCodes.InternalError, makeErrorMessage(e), e);
            }
        }
        long point2 = System.currentTimeMillis();
        statisticsLogger.info("[timeStatistic],[rollback],rollBackTime={}", (point2 - point1));
    }

    /**
     * 资源退还 (目前只支持退无码券)
     *
     * @param context 资源管理上下文
     * @throws BizError 业务异常
     */
    public void refund(ResourceManageContext context) throws BizError {
        long start = System.currentTimeMillis();
        long orderId = context.getOrderId();
        log.info("resource refund start orderId={} ", orderId);

        PromotionResourceStatus resourceStatus = promotionResourceStatusMapper.getStatusByOrderId(orderId);
        if (resourceStatus == null) {
            log.warn("resource refund notice resource-status is null. orderId:{}", orderId);
            return;
        }

        //后面还会依赖这个状态进行逻辑执行判断
        if (!Objects.equals(ResourceStatus.COMMITED.getValue(), resourceStatus.getStatus()) && !Objects.equals(ResourceStatus.ROLLBACKED.getValue(), resourceStatus.getStatus())) {
            log.warn("resource status is not meeting the refund condition. orderId:{}", orderId);
            return;
        }

        List<ResourceType> resourceTypes = context.getResourceTypes();
        if (CollectionUtils.isEmpty(resourceTypes)) {
            return;
        }

        // 获取订单的资源，恢复provider数据
        List<PromotionResourceDetail> resources = promotionResourceDetailMapper.getDetailByOrderId(orderId);
        if (CollectionUtils.isEmpty(resources)) {
            return;
        }

        List<ResourceProvider<?>> providers = recoverProviders(resources);
        long point1 = System.currentTimeMillis();
        statisticsLogger.info("[timeStatistic],[resource refund],initTime={}", (point1 - start));

        //合并优惠券请求
        mergeCouponProvider(providers);

        // 调用请求资源提供者锁定具体资源
        try {
            for (ResourceProvider provider : providers) {
                int returnStatus = provider.getResource().getReturnStatus();
                // 过滤已经返还或者 取消返还资源 或者初始化状态的
                if (Objects.equals(ReturnStatus.RETURNED.getValue(), returnStatus)
                        || Objects.equals(ReturnStatus.CANCEL_RETURN.getValue(), returnStatus)) {
                    continue;
                }

                // 只处理需要处理的
                if (resourceTypes.contains(provider.getResource().getResourceType())) {
                    provider.refund(resourceStatus.getStatus());
                }
            }
        } catch (Exception e) {
            log.error("resource refund failed. orderId:{}, resources:{}", orderId, resources, e);
            if (e instanceof BizError) {
                throw e;
            } else {
                throw ExceptionHelper.create(GeneralCodes.InternalError, makeErrorMessage(e), e);
            }
        }

        long point2 = System.currentTimeMillis();
        statisticsLogger.info("[timeStatistic],[resource refund],refundedTime={}", (point2 - point1));

        List<ResourceType> containResource = providers.stream().map(resource -> resource.getResource().getResourceType()).collect(Collectors.toList());
        if (resourceTypes.containsAll(containResource)) {
            // 修改资源表，改为回滚状态
            try {
                transactions.updateResourceStatus(context, ResourceStatus.ROLLBACKED);
                log.info("update car resource rollback status ok.{}", orderId);
            } catch (Exception e) {
                log.error("Update car resource status to locked failed. orderId:{} items:{} err:{}", orderId, resources, e);
                throw ExceptionHelper.create(GeneralCodes.InternalError, makeErrorMessage(e), e);
            }
            long point3 = System.currentTimeMillis();
            statisticsLogger.info("[timeStatistic],[status refund],updateTime={}", (point3 - point2));
            statisticsLogger.info("[timeStatistic],[status refund],initTime={},rollBackTime={}, updateTime={},totalTime={}", (point1 - start), (point2 - point1), (point3 - point2), (point3 - start));
        } else {
            log.warn("skip resource refund. orderId:{}", orderId);
        }

    }

    /**
     * 做资源补偿
     */
    public void remedial() {
        long start = System.currentTimeMillis();
        log.info("remedial started.");
        long nowTime = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        final List<PromotionResourceStatus> orderIdAndCreateTimes = promotionResourceStatusMapper.getTimeoutStatus(nowTime - KEEP_INTERVAL);
        if (orderIdAndCreateTimes.size() > 0) {
            log.warn("remedial start. count:{}", orderIdAndCreateTimes.size());
        }

        int successCount = 0;
        int failCount = 0;

        // 处理所有订单
        for (PromotionResourceStatus promotionResourceStatus : orderIdAndCreateTimes) {
            // 不是商城需要跳过
            if (!Objects.equals(promotionResourceStatus.getTradeFrom(), TradeFromEnum.SHOP.getValue())) {
                continue;
            }
            long orderId = promotionResourceStatus.getOrderId();
            Long createTime = promotionResourceStatus.getCreateTime();
            try {
                OrderStatus status = submitOrderServiceProxy.getOrderStatus(orderId);
                ResourceManageContext context = ResourceManageContext.fromTask(orderId);
                if (status == OrderStatus.NOT_FOUND) {
                    // 未成单，回滚
                    rollback(context);
                    log.info("remedial rollback ok. orderId:{}", orderId);
                } else if (status == OrderStatus.COMMIT) {
                    // 已成单，提交
                    commit(context);
                    log.info("remedial commit ok. orderId:{}", orderId);
                } else {
                    //其他状况既不回滚也不提交，等待下次修复
                    log.warn("remedial locking. orderId:{}", orderId);
                }
                successCount++;
            } catch (Throwable e) {
                log.error("remedial failed. orderId:{} createTime:{}", orderId, createTime, e);
                // 如果开启记录超时没有解决的资源 && 时间超过资源，执行记录标记
                if (SystemConstant.REMEDIAL_UNSOLVED_RECORD && (nowTime - createTime) > SystemConstant.REMEDIAL_UNSOLVED_RECORD_TIME) {
                    recordUnsolved(orderId);
                }
                failCount++;
            }
        }
        long end = System.currentTimeMillis();
        log.info("remedial finished. success:{} fail:{}, ws:{}", successCount, failCount, (end - start));
    }

    /**
     * 修改资源表，改为未解决状态
     *
     * @param orderId 订单ID
     */
    public void recordUnsolved(long orderId) {
        try {
            ResourceManageContext context = ResourceManageContext.fromTask(orderId);
            transactions.updateResourceStatus(context, ResourceStatus.UNSOLVE);
            log.info("unsolved resource status. orderId:{}", orderId);
        } catch (Exception e) {
            log.error("Update resource status to unsolved failed. orderId:{}", orderId);
        }
    }

    private List<ResourceProvider<?>> recoverProviders(List<PromotionResourceDetail> resources) throws BizError {
        if (CollectionUtils.isEmpty(resources)) {
            return Collections.emptyList();
        }

        List<ResourceProvider<?>> providers = new ArrayList<>();
        for (PromotionResourceDetail promotionResource : resources) {
            ResourceObject resourceObject = PromotionResourceConverter.fromDetail(promotionResource);
            if (resourceObject == null) {
                log.error("resourceObject convert failed. resource:{}", GsonUtil.toJson(promotionResource));
                throw ExceptionHelper.create(ErrCode.CODE_RESOURCE_CONVERT_FAIL, "资源对象不存在");
            }
            ResourceProvider provider = providerFactory.getProvider(resourceObject.getResourceType());
            provider.initResource(resourceObject);
            providers.add(provider);
        }
        return providers;
    }

    private List<ResourceProvider<?>> recoverProviders(List<PromotionResourceDetail> resources, OrderDataInfo orderData) throws BizError {
        if (CollectionUtils.isEmpty(resources)) {
            return Collections.emptyList();
        }
        List<ResourceProvider<?>> providers = new ArrayList<>();
        for (PromotionResourceDetail promotionResource : resources) {
            ResourceObject resourceObject = PromotionResourceConverter.fromDetail(promotionResource);
            if (resourceObject == null) {
                log.error("resourceObject convert failed. resource:{}", GsonUtil.toJson(promotionResource));
                throw ExceptionHelper.create(ErrCode.CODE_RESOURCE_CONVERT_FAIL, "资源对象不存在");
            }
            ResourceProvider provider = providerFactory.getProvider(resourceObject.getResourceType());
            if (Objects.equals(resourceObject.getResourceType(), ResourceType.POINT)) {
                provider.initResource(resourceObject, orderData);
            } else {
                provider.initResource(resourceObject);
            }
            providers.add(provider);
        }
        return providers;
    }

    private String makeErrorMessage(Exception e) {
        if (e instanceof BizError) {
            return ((BizError) e).getMsg();
        } else {
            return "优惠结算出错";
        }
    }
}
