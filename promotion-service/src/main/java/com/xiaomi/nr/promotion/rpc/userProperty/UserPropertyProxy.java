package com.xiaomi.nr.promotion.rpc.userProperty;


import com.google.common.base.Stopwatch;
import com.xiaomi.cnzone.xiaomishop.user.property.api.UserPropertyApi;
import com.xiaomi.cnzone.xiaomishop.user.property.api.request.UserPropertyReq;
import com.xiaomi.cnzone.xiaomishop.user.property.api.response.UserPropertyResp;
import com.xiaomi.nr.promotion.resource.model.UserPropertyResult;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 人群服务
 */
@Component
@Slf4j
public class UserPropertyProxy {

    private static final String SYSTEM_SOURCE = "promotion";

    @Reference(interfaceClass = UserPropertyApi.class, version = "${user.property.dubbo.version}", timeout = 50, group = "${user.property.dubbo.group}", retries = 0)
    private UserPropertyApi userPropertyApi;

    private static final List<String> USER_TAG_LIST = Arrays.asList("COLLEGE_STUDENT", "HIGH_SCHOOL_END");

    private static final List<String> STUDENT_TAG_LIST = Arrays.asList("COLLEGE_STUDENT", "HIGH_SCHOOL_END");

    private static final List<String> F_MEMBER_TAG_LIST = Arrays.asList("F_MEMBER");

    /**
     * 获取用户标签
     *
     * @param uid mid
     * @return 结果
     */
    @Async("userPropertyAsyncTaskExecutor")
    public ListenableFuture<UserPropertyResult> userProperty(Long uid) {
        UserPropertyResult userPropertyResult = new UserPropertyResult();
        UserPropertyReq request = new UserPropertyReq();
        request.setUid(uid);
        request.setChannel(SYSTEM_SOURCE);
        request.setTags(USER_TAG_LIST);
        try {
            Result<UserPropertyResp> result = userPropertyApi.userProperty(request);
            log.info("UserPropertyApi userProperty request:{}, result:{}", request, result);
            // 汇总结果
            if (result.getCode() == 0 && result.getData() != null && result.getData().getTags() != null) {
                Map<String, UserPropertyResp.TagResp> tagsMap = result.getData().getTags();
                for (String tag : USER_TAG_LIST) {
                    UserPropertyResp.TagResp tagResp = tagsMap.get(tag);
                    if (tagResp != null && tagResp.getStatus() == 1) {
                        userPropertyResult.setStudent(true);
                    }
                }
            }
        } catch (Exception e) {
            log.error("UserPropertyApi userProperty error, request:{}, result:{}", request, e);
        }
        return AsyncResult.forValue(userPropertyResult);
    }


    /**
     * 用户是否命中 学生、F会员、海葵标签
     *
     * @param uid         用户ID
     * @param fMemberFlag 是否为F会员标志
     * @param studentFlag 是否为学生标志
     * @param haikuiIds       海葵ID列表
     * @return 包含用户属性结果的ListenableFuture对象
     */
    @Async("userPropertyAsyncTaskExecutor")
    public ListenableFuture<UserPropertyResult> userPropertyV2(Long uid, boolean fMemberFlag, boolean studentFlag, List<String> haikuiIds) {
        Stopwatch stopwatch = Stopwatch.createStarted();

        UserPropertyResult userPropertyResult = new UserPropertyResult();
        UserPropertyReq req = new UserPropertyReq();

        List<String> tags = Lists.newArrayList();
        if (fMemberFlag) {
            // 查询用户是否属于F会员
            tags.addAll(F_MEMBER_TAG_LIST);
        }

        if (studentFlag) {
            // 查询用户是否属于学生
            tags.addAll(STUDENT_TAG_LIST);
        }

        if (CollectionUtils.isNotEmpty(haikuiIds)) {
            // 查询用户是否属于海葵人群包
            tags.addAll(haikuiIds.stream()
                    .map(id -> "HAIKUI:" + id)
                    .collect(Collectors.toList()));
        }

        req.setTags(tags);
        req.setUid(uid);
        req.setChannel(SYSTEM_SOURCE);

        try {
            // 调用米网用户标签，判断用户是否在 海葵/学生/F会员 标签中
            Result<UserPropertyResp> resp = userPropertyApi.userProperty(req);

            log.info("UserPropertyProxy.userPropertyV2 finished, req = {}, resp = {}, cost = {}", GsonUtil.toJson(req), GsonUtil.toJson(resp), stopwatch.elapsed(TimeUnit.MILLISECONDS));

            if (resp != null && resp.getCode() == 0 && resp.getData() != null && MapUtils.isNotEmpty(resp.getData().getTags())) {
                Map<String, UserPropertyResp.TagResp> tagRespMap = resp.getData().getTags();
                if (studentFlag) {
                    for (String tag : STUDENT_TAG_LIST) {
                        UserPropertyResp.TagResp tagResp = tagRespMap.get(tag);
                        if (tagResp != null && tagResp.getStatus() == 1) {
                            // 命中学生标签
                            userPropertyResult.setStudent(true);
                        }
                    }
                }

                if (fMemberFlag) {
                    for (String tag : F_MEMBER_TAG_LIST) {
                        UserPropertyResp.TagResp tagResp = tagRespMap.get(tag);
                        if (tagResp != null && tagResp.getStatus() == 1) {
                            // 命中f会员标签
                            userPropertyResult.setFMember(true);
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(haikuiIds)) {
                    for (String hkId : haikuiIds) {
                        UserPropertyResp.TagResp tagResp = tagRespMap.get("HAIKUI:" + hkId);
                        if (tagResp != null && tagResp.getStatus() == 1) {
                            // 命中海葵id
                            userPropertyResult.getHitHaikuiIds().add(hkId);
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("UserPropertyApi userPropertyV2 error, req = {}, e = ", GsonUtil.toJson(req), e);
        }

        return AsyncResult.forValue(userPropertyResult);
    }

}
