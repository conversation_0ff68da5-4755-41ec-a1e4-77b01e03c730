package com.xiaomi.nr.promotion.rpc.userProperty;


import com.xiaomi.cnzone.xiaomishop.user.property.api.UserPropertyApi;
import com.xiaomi.cnzone.xiaomishop.user.property.api.request.UserPropertyReq;
import com.xiaomi.cnzone.xiaomishop.user.property.api.response.UserPropertyResp;
import com.xiaomi.nr.promotion.resource.model.UserPropertyResult;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 人群服务
 */
@Component
@Slf4j
public class UserPropertyProxy {

    private static final String SYSTEM_SOURCE = "promotion";

    @Reference(interfaceClass = UserPropertyApi.class, version = "${user.property.dubbo.version}", timeout = 50, group = "${user.property.dubbo.group}")
    private UserPropertyApi userPropertyApi;

    private static final List<String> USER_TAG_LIST = Arrays.asList("COLLEGE_STUDENT", "HIGH_SCHOOL_END");

    /**
     * 获取用户标签
     * @param uid mid
     * @return 结果
     */
    @Async("userPropertyAsyncTaskExecutor")
    public ListenableFuture<UserPropertyResult> userProperty(Long uid) {
        UserPropertyResult userPropertyResult=new UserPropertyResult();
        UserPropertyReq request = new UserPropertyReq();
        request.setUid(uid);
        request.setChannel(SYSTEM_SOURCE);
        request.setTags(USER_TAG_LIST);
        try {
            Result<UserPropertyResp> result = userPropertyApi.userProperty(request);
            log.info("UserPropertyApi userProperty request:{}, result:{}",request,result);
            // 汇总结果
            if (result.getCode()==0&&result.getData()!=null&&result.getData().getTags()!=null){
                Map<String, UserPropertyResp.TagResp> tagsMap = result.getData().getTags();
                for (String tag:USER_TAG_LIST){
                    UserPropertyResp.TagResp tagResp = tagsMap.get(tag);
                    if (tagResp!=null&&tagResp.getStatus()==1){
                        userPropertyResult.setStudent(true);
                    }
                }
            }
        }catch (Exception e){
            log.error("UserPropertyApi userProperty error, request:{}, result:{}",request,e);
        }
        return AsyncResult.forValue(userPropertyResult);
    }

}
