package com.xiaomi.nr.promotion.componet.condition;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.GoodsReduceInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 立减条件判断
 *
 * <AUTHOR>
 * @date 2022/07/11
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class BuyReduceCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 活动类型
     */
    private PromotionToolType promotionType;
    /**
     * 下单立减信息
     * key: skuPackage val:GoodsReduceInfo
     */
    private Map<String, GoodsReduceInfo> reduceInfoMap;
    /**
     * 销售来源
     */
    private List<String> saleSources;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    /**
     * - 遍历购物车,合并同一个sku或套装的item
     * - 过滤不能参加直降的. 查找是否找到对应的直降信息， 以及限购数是否符合
     * - 筛选购物车中可以参加直降的列表
     *
     * @param request 请求参数
     * @param context 活动内上下文
     * @return 是否满足
     */
    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) {
        if (MapUtil.isEmpty(reduceInfoMap)) {
            log.error("reduceInfoMap is empty. actId:{} uid:{}", promotionId, request.getUserId());
            return false;
        }
        List<CartItem> cartList = request.getCartList();

        // 筛选满足数据
        List<GoodsIndex> indexList = goodsActMatch(cartList);

        // 没有符合的商品，不满足活动
        if (CollectionUtils.isEmpty(indexList)) {
            return false;
        }
        context.setGoodIndex(indexList);
        return true;
    }

    private List<GoodsIndex> goodsActMatch(List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(cartList)) {
            return Collections.emptyList();
        }
        List<GoodsIndex> indexList = Lists.newArrayList();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            String skuPackage = CartHelper.getSkuPackage(item);
            // 是否匹配
            GoodsReduceInfo reduceInfo = reduceInfoMap.get(skuPackage);
            if (reduceInfo == null) {
                continue;
            }
            // 规则判断
            boolean itemCheck = checkReduceItem(item, reduceInfo, saleSources);
            if (!itemCheck) {
                continue;
            }
            indexList.add(new GoodsIndex(item.getItemId(), idx));
        }
        return indexList;
    }

    private boolean checkReduceItem(CartItem item, GoodsReduceInfo reduceInfo, List<String> saleSources) {
        boolean isMiShop = true;
        // 检查订单来源
        boolean quotaCheck = CartHelper.checkItemActQualify(item, promotionType.getTypeId(), isMiShop, saleSources, null);
        if (!quotaCheck) {
            return false;
        }
        // 检查活动数量
        String skuPackage = CartHelper.getSkuPackage(item);
        Integer usedNum = activityRedisDao.getActGoodsLimitNum(promotionId, skuPackage);
        long remainNum = reduceInfo.getLimitNum() - usedNum;
        if (remainNum <= 0) {
            return false;
        }
        return true;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof BuyReducePromotionConfig)) {
            log.error("config is not instanceof BuyReducePromotionConfig. config:{}", config);
            return;
        }
        BuyReducePromotionConfig buyReduceConfig = (BuyReducePromotionConfig) config;
        this.promotionId = buyReduceConfig.getPromotionId();
        this.promotionType = buyReduceConfig.getPromotionType();
        this.reduceInfoMap = buyReduceConfig.getBuyReduceInfoMap();
        this.saleSources = buyReduceConfig.getSaleSources();
    }
}
