package com.xiaomi.nr.promotion.bizplatform.impl;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.bizplatform.AbstractBaseBizPlatform;
import com.xiaomi.nr.promotion.constant.CouponConstant;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.domain.activity.impl.MiShopActivityDomain;
import com.xiaomi.nr.promotion.domain.coupon.impl.MiShopCouponDomain;
import com.xiaomi.nr.promotion.domain.ecard.impl.MiShopECardDomain;
import com.xiaomi.nr.promotion.domain.phoenix.impl.MiShopPhoenixDomain;
import com.xiaomi.nr.promotion.domain.redpackage.impl.MiShopRedPackageDomain;
import com.xiaomi.nr.promotion.domain.shoppingcoupon.impl.MiShopShoppingCouponDomain;
import com.xiaomi.nr.promotion.domain.subsidyactivity.impl.SubsidyActivityDomain;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.nr.promotion.model.common.BasicInfo;
import com.xiaomi.nr.promotion.model.common.OrderJoinActInfo;
import com.xiaomi.nr.promotion.model.common.Statistics;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.provider.OrderBenefitProvider;
import com.xiaomi.nr.promotion.resource.provider.OrderPromotionProvider;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.nr.promotion.tool.CheckoutResponseTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.NumberUtil;
import com.xiaomi.nr.promotion.util.StringUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.xiaomi.hera.metrics.api.BusinessMetrics;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class MiShopBizPlatform extends AbstractBaseBizPlatform {

    @Autowired
    private ResourceProviderFactory resourceProviderFactory;

    @Autowired
    private CheckoutCartTool checkoutCartTool;

    @Autowired
    private CheckoutResponseTool checkoutResponseTool;

    private BaseDomainList baseDomainList;

    @Override
    public BaseDomainList checkBaseDomainList() {
        return baseDomainList;
    }

    @Override
    public BizPlatformEnum getBiz() {
        return BizPlatformEnum.NEW_RETAIL;
    }


    @Override
    public void afterPropertiesSet() {
        baseDomainList=super.instanceBaseDomainList();
        baseDomainList.addResourceList(MiShopActivityDomain.class)
                .addResourceList(MiShopCouponDomain.class)
                .addResourceList(MiShopRedPackageDomain.class)
                .addResourceList(SubsidyActivityDomain.class)
                .addResourceList(MiShopPhoenixDomain.class)
                .addResourceList(MiShopShoppingCouponDomain.class)
                .addResourceList(MiShopECardDomain.class);
    }

    @Override
    public void generateResponse(DomainCheckoutContext domainCheckoutContext) throws BizError {
        super.originalGenerateResponse(domainCheckoutContext);
    }

    @Override
    public void addResource(DomainCheckoutContext domainCheckoutContext) throws BizError {
        CheckoutPromotionRequest request = domainCheckoutContext.getRequest();
        CheckoutContext checkoutContext = domainCheckoutContext.getContext();
        // 做订单层面分摊 和 添加全局性资源
        if (request.getSourceApi() == SourceApi.SUBMIT && request.getChannel() == null) {
            List<OrderCartItem> orderCartItemList = checkoutCartTool.divideOrderCarts(checkoutContext.getCarts());
            checkoutContext.setOrderCartItems(orderCartItemList);
            List<OrderItem> orderItems = convertToOrderItemList(request.getOrderId(), request.getUserId(), checkoutContext);
            checkoutContext.setOrderItems(orderItems);
            checkoutContext.getResourceHandlers().addAll(initResource(checkoutContext, request));
        }
    }

    @Override
    public void postProcessing(CheckoutPromotionRequest request, DomainCheckoutContext domainCheckoutContext) {

    }

    private List<ResourceProvider<?>> initResource(CheckoutContext checkoutContext, CheckoutPromotionRequest request) throws BizError {
        List<ResourceProvider<?>> providerList = new ArrayList<>();
        Long orderId = request.getOrderId();
        Long userId = request.getUserId();
        // 订单优惠
        OrderBenefitProvider.OrderBenefitResource benefitResource = buildOrderBenefitResourceV2(request, checkoutContext);
        ResourceObject<OrderBenefitProvider.OrderBenefitResource> resourceObject =
                buildOrderBenefitResourceObject(orderId, userId, benefitResource);
        OrderBenefitProvider benefitProvider = (OrderBenefitProvider) resourceProviderFactory.getProvider(ResourceType.ORDER_BENEFIT);
        benefitProvider.initResource(resourceObject);
        log.debug("order benefit provider:{}", benefitProvider);
        providerList.add(benefitProvider);

        // 订单优惠明细
        OrderPromotionProvider.OrderPromotionResource orderPromotionResource = buildOrderPromotionResource(orderId, userId, checkoutContext.getOrderItems());
        ResourceObject<OrderPromotionProvider.OrderPromotionResource> orderPromotionResourceObject =
                buildOrderPromotionResourceObject(orderId, userId, orderPromotionResource);
        OrderPromotionProvider orderPromotionProvider = (OrderPromotionProvider) resourceProviderFactory.getProvider(ResourceType.ORDER_PROMOTION);
        orderPromotionProvider.initResource(orderPromotionResourceObject);
        providerList.add(orderPromotionProvider);
        return providerList;
    }

    private OrderPromotionProvider.OrderPromotionResource buildOrderPromotionResource(Long orderId, Long userId, List<OrderItem> orderCartItemList) {
        OrderPromotionProvider.OrderPromotionResource orderPromotionResource = new OrderPromotionProvider.OrderPromotionResource();
        orderPromotionResource.setOrderId(orderId);
        orderPromotionResource.setUserId(userId);
        orderPromotionResource.setPromotionDetail(GsonUtil.toJson(orderCartItemList));
        return orderPromotionResource;
    }

    private ResourceObject<OrderPromotionProvider.OrderPromotionResource> buildOrderPromotionResourceObject(Long orderId, Long userId, OrderPromotionProvider.OrderPromotionResource orderPromotionResource) {
        ResourceObject<OrderPromotionProvider.OrderPromotionResource> resourceObject = new ResourceObject<>();
        resourceObject.setContent(orderPromotionResource);
        resourceObject.setPid(0L);
        resourceObject.setPromotionId(orderId);
        resourceObject.setOrderId(orderId);
        resourceObject.setResourceType(ResourceType.ORDER_PROMOTION);
        resourceObject.setResourceId(String.format("%s_%s_order_promotion", orderId, userId));
        return resourceObject;
    }

    private ResourceObject<OrderBenefitProvider.OrderBenefitResource> buildOrderBenefitResourceObject(Long orderId, Long userId, OrderBenefitProvider.OrderBenefitResource orderBenefit) {
        ResourceObject<OrderBenefitProvider.OrderBenefitResource> resourceObject = new ResourceObject<>();
        resourceObject.setContent(orderBenefit);
        resourceObject.setPid(0L);
        resourceObject.setPromotionId(orderId);
        resourceObject.setOrderId(orderId);
        resourceObject.setResourceType(ResourceType.ORDER_BENEFIT);
        resourceObject.setResourceId(String.format("%s_%s", orderId, userId));
        return resourceObject;
    }

    private OrderBenefitProvider.OrderBenefitResource buildOrderBenefitResourceV2(CheckoutPromotionRequest request, CheckoutContext checkoutContext) throws BizError {
        List<CartItem> cartList = request.getCartList();
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            cartList = checkoutContext.getCarts();
        }
        // 汇总信息
        Statistics statistics = CartHelper.checkAndStatistics(cartList);
        // 购物车信息
        String cartListJson = GsonUtil.toJson(cartList);
        // 活动信息
        String joinActJson = getActJoinJson(cartList, checkoutContext);


        // 加载资源
        OrderBenefitProvider.OrderBenefitResource orderBenefit = new OrderBenefitProvider.OrderBenefitResource();
        orderBenefit.setUid(request.getUserId());
        orderBenefit.setOrderId(request.getOrderId());
        // 涉及到金额，单位从分转换为元
        orderBenefit.setProductMoney(NumberUtil.amountConvertF2Y(statistics.getTotalStandardPrice()));
        orderBenefit.setCartMoney(NumberUtil.amountConvertF2Y(statistics.getTotalPrice()));
        orderBenefit.setAllReduceMoney(NumberUtil.amountConvertF2Y(statistics.getTotalReduce()));
        orderBenefit.setOrderMoney(NumberUtil.amountConvertF2Y(statistics.getTotalPrice() - statistics.getTotalReduce()));
        orderBenefit.setItems(cartListJson);
        orderBenefit.setJoinedActs(joinActJson);
        orderBenefit.setAppId(request.getClientId());
        orderBenefit.setLockStatus(1);
        orderBenefit.setOrgCode(request.getOrgCode());
        orderBenefit.setUidType(request.getUidType());

        // 优惠劵

        BasicInfo baseInfo = checkoutContext.getBaseInfo();
        if (baseInfo != null) {
            //couponId -> coupon properties(name\typ)
            Map<String, Map<String, String>> couponPropertiesMap = new HashMap<>();

            //有码券
            CouponInfo codeCouponInfo = baseInfo.getCodeCouponInfo();
            if (codeCouponInfo != null) {

                Map<String, String> couponProps = new HashMap<>();
                String cat = CouponConstant.COUPON_TYPE_CAT.get(2).get(codeCouponInfo.getCouponType());
                couponProps.put("name", codeCouponInfo.getCouponName());
                couponProps.put("typ", cat);
                couponPropertiesMap.put(codeCouponInfo.getCouponCode(), couponProps);
                orderBenefit.setCodecouponIds(codeCouponInfo.getCouponCode());
                orderBenefit.setCodecouponReduceMoney(NumberUtil.amountConvertF2Y(codeCouponInfo.getReduceMoney()));

            }

            //无码券
            List<CouponInfo> couponInfoList = baseInfo.getCouponInfoList();
            List<String> couponIdList = new ArrayList<>();

            if (CollectionUtils.isNotEmpty(couponInfoList)) {
                //商品券和补贴券
                long totalCouponReduce = 0;
                long totalExpressReduce = 0;


                for (CouponInfo couponInfo : couponInfoList) {
                    totalCouponReduce += couponInfo.getReduceMoney();
                    totalExpressReduce += couponInfo.getReduceExpress();
                    couponIdList.add(String.valueOf(couponInfo.getCouponId()));

                    Map<String, String> couponProps = new HashMap<>();
                    String cat = CouponConstant.COUPON_TYPE_CAT.get(CouponConstant.COUPON_CATEGORY_NOCODE).get(couponInfo.getCouponType());
                    couponProps.put("name", couponInfo.getCouponName());
                    couponProps.put("typ", cat);
                    couponPropertiesMap.put(String.valueOf(couponInfo.getCouponId()), couponProps);

                }
                orderBenefit.setCouponReduceMoney(NumberUtil.amountConvertF2Y(totalCouponReduce));
                orderBenefit.setCouponExpressReduceMoney(NumberUtil.amountConvertF2Y(totalExpressReduce));
            }

            //包邮券
            CouponInfo shipmentCouponInfo = baseInfo.getShipmentCouponInfo();
            if (shipmentCouponInfo != null) {
                Map<String, String> couponProps = new HashMap<>();
                String cat = CouponConstant.COUPON_TYPE_CAT.get(CouponConstant.COUPON_CATEGORY_NOCODE).get(shipmentCouponInfo.getCouponType());
                couponProps.put("name", shipmentCouponInfo.getCouponName());
                couponProps.put("typ", cat);
                couponPropertiesMap.put(String.valueOf(shipmentCouponInfo.getCouponId()), couponProps);

                //覆盖扣减运费
                orderBenefit.setCouponExpressReduceMoney(NumberUtil.amountConvertF2Y(shipmentCouponInfo.getReduceExpress()));
            }

            if (CollectionUtils.isNotEmpty(couponIdList)) {
                orderBenefit.setCouponIds(StringUtils.join(couponIdList, ","));
            }
            orderBenefit.setUsedCoupons(GsonUtil.toJson(couponPropertiesMap));
        }


        // 红包
        if (CollectionUtils.isNotEmpty(checkoutContext.getUsedRedpacketList())) {
            orderBenefit.setUsedRedpacket(GsonUtil.toJson(checkoutContext.getUsedRedpacketList()));
            orderBenefit.setRedpacketReduceMoney(NumberUtil.amountConvertF2Y(statistics.getTotalRedPacketReduce()));
        }
        return orderBenefit;
    }

    private String getActJoinJson(List<CartItem> cartList, CheckoutContext checkoutContext) throws BizError {
        Map<String, OrderJoinActInfo> joinActMap = new HashMap<>();
        List<PromotionInfo> promotionInfos = checkoutContext.getPromotion();
        for (CartItem cartItem : cartList) {
            // 减钱的
            for (String idKey : cartItem.getReduceList().keySet()) {
                String prefix = StringUtil.getKeyPrefix(idKey);
                if (prefix.equals(PromotionConstant.CARTLIST_ACTIVITY_PREFIX)) {
                    String actId = StringUtil.getKeyCode(idKey);
                    joinActMap.put(actId, null);
                }
            }
            // 加价购或赠品
            if (!StringUtils.isBlank(cartItem.getSourceCode())) {
                joinActMap.put(cartItem.getSourceCode(), null);
            }
        }
        // 包邮活动
        for (Express express : checkoutContext.getExpress()) {
            if (StringUtils.isEmpty(express.getIdKey())) {
                continue;
            }
            String prefix = StringUtil.getKeyPrefix(express.getIdKey());
            if (prefix.equals(PromotionConstant.CARTLIST_ACTIVITY_PREFIX)) {
                String actId = StringUtil.getKeyCode(express.getIdKey());
                joinActMap.put(actId, null);
            }
        }

        // 填充优惠信息
        for (PromotionInfo promotionInfo : promotionInfos) {
            if (promotionInfo == null) {
                log.error("promotion info is null.");
                throw ExceptionHelper.create(ErrCode.ERR_ACT_PROMOTION_EMPTY, "活动优惠信息为空");
            }
            if (joinActMap.containsKey(promotionInfo.getPromotionId())) {
                joinActMap.put(promotionInfo.getPromotionId(), convert(promotionInfo));
            }
        }
        return GsonUtil.toJson(joinActMap);
    }

    private OrderJoinActInfo convert(PromotionInfo promotionInfo) {
        OrderJoinActInfo joinActInfo = new OrderJoinActInfo();
        joinActInfo.setPromotionId(promotionInfo.getPromotionId());
        joinActInfo.setIcon(promotionInfo.getIcon());
        joinActInfo.setType(promotionInfo.getType());
        joinActInfo.setTypeInfo(promotionInfo.getTypeInfo());
        joinActInfo.setTypeCode(promotionInfo.getTypeCode());
        joinActInfo.setTitle(promotionInfo.getTitle());
        joinActInfo.setLimit(promotionInfo.getLimit());
        joinActInfo.setExtend(promotionInfo.getExtend());
        joinActInfo.setJoined(promotionInfo.getJoined());
        joinActInfo.setIsOnlyGoods(promotionInfo.getIsOnlyGoods());
        joinActInfo.setParentItemId(promotionInfo.getParentItemId());
        joinActInfo.setJoinedItemId(promotionInfo.getJoinedItemId());
        joinActInfo.setPostfree(promotionInfo.getPostfree());
        joinActInfo.setPostfreeIsnum(promotionInfo.getPostfreeIsnum());
        joinActInfo.setPostfreeNum(promotionInfo.getPostfreeNum());
        joinActInfo.setUrlSetting(promotionInfo.getUrlSetting());
        joinActInfo.setUrlContent(promotionInfo.getUrlContent());
        joinActInfo.setDescTitle(promotionInfo.getDescTitle());
        joinActInfo.setDescUrl(promotionInfo.getDescUrl());
        joinActInfo.setQuotaEles(promotionInfo.getQuotaEles());
        joinActInfo.setDescPolicy(promotionInfo.getDescPolicy());
        joinActInfo.setDescShortName(promotionInfo.getDescShortName());
        joinActInfo.setDescIsShowAddOnItem(promotionInfo.getDescIsShowAddOnItem());
        joinActInfo.setIsRecommendForceAddPrice(promotionInfo.getIsRecommendForceAddPrice());
        joinActInfo.setPolicyNew(promotionInfo.getPolicyNew());
        joinActInfo.setJoinCounts(promotionInfo.getJoinCounts());
        joinActInfo.setActivityMutexLimit(promotionInfo.getActivityMutexLimit());
        joinActInfo.setActivityMutex(promotionInfo.getActivityMutex());
        joinActInfo.setFrequent(promotionInfo.getFrequent());
        joinActInfo.setTotalLimitNum(promotionInfo.getTotalLimitNum());

        NumLimitRule numLimitRule = promotionInfo.getNumLimitRule();
        if (numLimitRule == null) {
            return joinActInfo;
        }
        ActNumLimitRule limitRule = new ActNumLimitRule();
        limitRule.setOrderLimit(numLimitRule.getOrderLimit());
        limitRule.setPersonLimit(numLimitRule.getPersonLimit());
        limitRule.setDayLimitOne(numLimitRule.getDayLimitOne());
        limitRule.setDayLimitAll(numLimitRule.getDayLimitAll());
        limitRule.setActivityLimitAll(numLimitRule.getActivityLimitAll());
        limitRule.setActivityLimitOne(numLimitRule.getActivityLimitOne());
        limitRule.setPersonLimitOne(numLimitRule.getPersonLimitOne());
        joinActInfo.setNumLimitRule(limitRule);
        return joinActInfo;
    }

    /**
     * orderCartItem转orderItem
     *
     * @param orderId 订单号
     * @param userId  用户id
     * @param context 结算上下文
     * @return 订单item列表
     * @throws BizError 业务异常
     */
    private List<OrderItem> convertToOrderItemList(Long orderId, Long userId, CheckoutContext context) throws BizError {
        List<OrderCartItem> orderCartItemList = context.getOrderCartItems();
        if (CollectionUtils.isEmpty(orderCartItemList)) {
            log.error("orderCartItem is empty. uid: {}, orderId: {}", userId, orderId);
            throw ExceptionHelper.create(ErrCode.ERR_ORDER_ITEMS_EMPTY, "购物车拆分为订单条目的列表为空");
        }

        // 订单项列表数据
        List<OrderItem> orderItems = new ArrayList<>();
        for (OrderCartItem orderCartItem : orderCartItemList) {
            if (orderCartItem == null || !CartHelper.filterOrderOk(orderCartItem)) {
                log.warn("formatSubmitOut error. orderCartItem:{}", orderCartItem);
                throw ExceptionHelper.create(ErrCode.ERR_ORDER_ITEM, "订单条目为空或者不合法");
            }
            OrderItem orderItem = checkoutResponseTool.convertOrderItem(orderCartItem, userId, orderId);
            orderItems.add(orderItem);
        }
        return orderItems;
    }
}
