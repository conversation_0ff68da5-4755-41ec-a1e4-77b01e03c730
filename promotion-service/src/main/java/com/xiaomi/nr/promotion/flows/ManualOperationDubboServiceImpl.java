package com.xiaomi.nr.promotion.flows;

import cn.hutool.crypto.digest.DigestUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.promotion.api.dto.ManualQueryResourceResponse;
import com.xiaomi.nr.promotion.api.dto.ManualResourceRequest;
import com.xiaomi.nr.promotion.api.service.ManualOperationDubboService;
import com.xiaomi.nr.promotion.constant.CacheKeyConstant;
import com.xiaomi.nr.promotion.dao.redis.ActUserLimitPo;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.StringUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.common.utils.Log;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/5/26
 */
@Slf4j
@ApiModule(value = "手动后门接口", apiInterface = ManualOperationDubboService.class)
@DubboService(group = "${dubbo.group}", interfaceClass = ManualOperationDubboService.class, timeout = 50000, version = "1.0")
public class ManualOperationDubboServiceImpl implements ManualOperationDubboService {

    @Resource
    private ActivityRedisDao activityRedisDao;

    private static final String saltValue = "identity_id_card";


    @ApiDoc("手动退资源")
    @Override
    public Result<String> manualRefundResource(ManualResourceRequest request) throws BizError {
        try {
            if (CollectionUtils.isEmpty(request.getSpuGroup())) {
                return Result.success("empty spuGroup");
            }
            if (request.getSpuGroup().size() > 3) {
                return Result.success("spuGroup size over 3");
            }
            request.setIdCardForMD5(DigestUtil.md5Hex(saltValue + request.getIdCard()));

            List<ActUserLimitPo> actUserLimitPoList = Lists.newArrayList();
            request.getSpuGroup().forEach(spu -> {
                ActUserLimitPo po = new ActUserLimitPo();
                String lockKey = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_PURCHASE_SUBSIDY_LIMIT_GROUP,
                        String.valueOf(request.getIdCardForMD5()), String.valueOf(spu));
                po.setLockKey(lockKey);
                po.setTarget(1);
                actUserLimitPoList.add(po);
            });

            ActUserLimitPo totalPo = new ActUserLimitPo();
            String lockKey = StringUtil.formatContent(CacheKeyConstant.KEY_ACT_PURCHASE_SUBSIDY_LIMIT_TOTAL,
                    String.valueOf(request.getIdCardForMD5()));
            totalPo.setLockKey(lockKey);
            totalPo.setTarget(request.getSpuGroup().size());
            actUserLimitPoList.add(totalPo);

            activityRedisDao.batchRollbackSubsidyLimitV2(actUserLimitPoList);
            log.info("ManualOperationDubboService.manualRefundResource request is {}, response is success", GsonUtil.toJson(request));
            return Result.success("success");
        } catch (Exception e) {
            log.info("ManualOperationDubboService.manualRefundResource request is {}, response is fail", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        }
    }


    @ApiDoc("手动查资源")
    @Override
    public Result<ManualQueryResourceResponse> manualQueryResource(ManualResourceRequest request) {
        try {
            if (Objects.isNull(request.getIdCard())) {
                return Result.success(new ManualQueryResourceResponse());
            }
            request.setIdCardForMD5(DigestUtil.md5Hex(saltValue + request.getIdCard()));

            ManualQueryResourceResponse response = new ManualQueryResourceResponse();
            Integer total = activityRedisDao.getActPurchaseSubsidyLimitTotal(String.valueOf(request.getIdCardForMD5()));

            Map<Integer, Integer> spuGroupMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(request.getSpuGroup())) {
                for (Integer spu : request.getSpuGroup()) {
                    Integer spulimit = activityRedisDao.getActPurchaseSubsidyLimitGroup(
                            String.valueOf(request.getIdCardForMD5()), spu);
                    spuGroupMap.put(spu, spulimit);
                }
            }

            response.setTotalLimit(total);
            response.setSpuGroupLimitMap(spuGroupMap);
            log.info("ManualOperationDubboService.manualQueryResource request is {}, response is {}", GsonUtil.toJson(request), GsonUtil.toJson(response));
            return Result.success(response);
        } catch (Exception e) {
            log.info("ManualOperationDubboService.manualQueryResource is error, request is {}", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        }
    }
}
