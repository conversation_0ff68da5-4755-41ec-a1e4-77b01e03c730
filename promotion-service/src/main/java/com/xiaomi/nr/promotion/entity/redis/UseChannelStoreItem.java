package com.xiaomi.nr.promotion.entity.redis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 门店渠道实体
 *
 * <AUTHOR>
 */
@Data
public class UseChannelStoreItem implements Serializable {
    private static final long serialVersionUID = 3374760267088391179L;
    /**
     * 门店范围 1:全部门店 2:部分门店
     */
    private Integer scope;

    /**
     * 门店ID列表 如果门店范围是全部门店，则此列表为空
     */
    @SerializedName("store_ids")
    private List<String> storeIds;
}
