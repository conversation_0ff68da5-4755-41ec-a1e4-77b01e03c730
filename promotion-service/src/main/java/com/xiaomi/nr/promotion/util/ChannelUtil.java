package com.xiaomi.nr.promotion.util;

import com.xiaomi.nr.md.promotion.admin.api.constant.ChannelEnum;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/14 19:17
 */
public class ChannelUtil {

    private ChannelUtil() {
    }

    /**
     * 判断是不是车商城
     *
     * @param channels
     * @return
     */
    public static boolean isCarShop(List<Integer> channels) {
        if (CollectionUtils.isEmpty(channels)) {
            return false;
        }
        return channels.size() == 1 && ChannelEnum.CAR_SHOP.value == channels.getFirst();
    }


}
