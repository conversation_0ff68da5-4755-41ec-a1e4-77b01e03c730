package com.xiaomi.nr.promotion.mq.consumer.entity;

import com.google.gson.annotations.SerializedName;
import com.xiaomi.nr.promotion.mq.consumer.entity.OrderData;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023/1/9
 */
@Data
public class OrderRefundBody {
    /**
     * 消息内容
     */
    @SerializedName("order_data")
    private OrderData orderData;

    /**
     * 消息内容
     */
    @SerializedName("item_detail")
    private List<ItemDetail> itemDetail;
}
