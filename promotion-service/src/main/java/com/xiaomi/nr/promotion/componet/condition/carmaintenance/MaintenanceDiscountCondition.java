package com.xiaomi.nr.promotion.componet.condition.carmaintenance;

import com.google.common.collect.Lists;
import com.xiaomi.micar.club.api.model.member.MemberInfo;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionUserActivityCountMapper;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.UserActivityCount;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MaintenanceDiscountPromotionConfig;
import com.xiaomi.nr.promotion.resource.external.CarVidVipExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @description 汽车维保会员折扣活动
 * @date 2025-01-03 15:09
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class MaintenanceDiscountCondition extends Condition {

    /**
     * 活动ID
     */
    private Long promotionId;

    /**
     * 优惠类型
     */
    private PromotionToolType promotionType;

    /**
     * 身份限制
     */
    private Boolean identityLimit;

    /**
     * 会员ID
     */
    private Integer vipLevel;

    /**
     * 支持的工单类型
     */
    private Set<Integer> supportWorkOrderTypes;

    /**
     * 黑名单商品
     */
    private List<Long> invalidGoods;

    /**
     * 用户参与次数限制
     */
    private Integer userJoinNumLimit;

    @Autowired
    private PromotionUserActivityCountMapper userActivityCountMapper;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {

        // bizPlatform校验
        if (!Objects.equals(context.getBizPlatform(), BizPlatformEnum.MAINTENANCE_REPAIR)) {
            return false;
        }

        // 工单类型校验
        if (!supportWorkOrderTypes.contains(request.getWorkOrderType())) {
            return false;
        }

        // 会员身份校验
        if (this.identityLimit) {
            MemberInfo vipIdentityInfo = getVipLevelFromContext(context);
            if (vipIdentityInfo == null || !Objects.equals(this.vipLevel, vipIdentityInfo.getLevel())) {
                log.debug("condition is not satisfied. user is not match vipId. uid:{} actId:{} vipLevel:{}", request.getUserId(), promotionId, vipLevel);
                return false;
            }
        }

        // 用户参与次数校验 0为不限次
        if (userJoinNumLimit > 0) {
            UserActivityCount userActivityCount = userActivityCountMapper.getByUserIdAndPromotionId(request.getUserId(), promotionId);
            Integer userJoinNum = Optional.ofNullable(userActivityCount).map(UserActivityCount::getNum).orElse(0);
            if (userJoinNum >= userJoinNumLimit) {
                log.debug("condition is not satisfied. user join num is over limit. uid:{} actId:{} userJoinNumLimit:{}", request.getUserId(), promotionId, userJoinNumLimit);
                return false;
            }
        }

        // 商品校验
        if (!checkGoods(request, context)) {
            return false;
        }

        return true;
    }

    private boolean checkGoods(CheckoutPromotionRequest request, LocalContext context) {
        List<CartItem> cartList = request.getCartList();

        // 筛选满足数据
        Pair<Boolean, List<GoodsIndex>> goodsPair = goodsActMatch(cartList);

        if (!goodsPair.getLeft()) {
            return false;
        }

        context.setGoodIndex(goodsPair.getRight());
        return true;
    }

    private Pair<Boolean, List<GoodsIndex>> goodsActMatch(List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(cartList)) {
            return Pair.of(Boolean.FALSE, Collections.emptyList());
        }

        boolean canJoinAct = false;
        List<GoodsIndex> indexList = Lists.newArrayList();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem cartItem = cartList.get(idx);
            if (Objects.isNull(cartItem)) {
                continue;
            }
            Long ssuId = cartItem.getSsuId();

            // 工时和配件为自费才能使用
            if (Objects.isNull(cartItem.getMaintenanceInfo().getPayType())
                    || !Objects.equals(cartItem.getMaintenanceInfo().getPayType(), 1)) {
                continue;
            }

            // 黑名单
            if (invalidGoods.contains(ssuId)) {
                continue;
            } else {
                canJoinAct = true;
            }

            // 商品是否能参加活动
            if (!CartHelper.checkItemActQualifyCommon(cartItem, promotionType.getTypeId())) {
                continue;
            }

            // 商品有效性
            Long curPrice = CartHelper.itemCurPrice(cartItem.getOriginalCartPrice(), cartItem.getReduceItemList());
            if (Objects.equals(curPrice, 0L)) {
                continue;
            }

            indexList.add(new GoodsIndex(cartItem.getItemId(), idx));
        }
        return Pair.of(canJoinAct, indexList);
    }

    private MemberInfo getVipLevelFromContext(LocalContext context) {
        Map<ResourceExtType, ExternalDataProvider<?>> externalDataMap = context.getExternalDataMap();
        CarVidVipExternalProvider provider = (CarVidVipExternalProvider) externalDataMap.get(ResourceExtType.VID_ULTRA_VIP_MEMBER);
        try {
            return provider.getData();
        } catch (BizError e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof MaintenanceDiscountPromotionConfig)) {
            log.error("config is not instanceof MaintenanceDiscountPromotionConfig. config:{}", config);
            return;
        }
        MaintenanceDiscountPromotionConfig promotionConfig = (MaintenanceDiscountPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        this.vipLevel = promotionConfig.getCarIdentityId();
        this.identityLimit = true;
        this.supportWorkOrderTypes = promotionConfig.getSupportWorkOrderTypes();
        this.userJoinNumLimit = promotionConfig.getUserJoinNumLimit();
        this.invalidGoods = promotionConfig.getInvalidGoods();
    }
}
