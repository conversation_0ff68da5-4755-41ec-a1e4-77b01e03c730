package com.xiaomi.nr.promotion.componet.action;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.constant.PriceConstant;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.enums.ActFrequencyEnum;
import com.xiaomi.nr.promotion.enums.PostFreeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.PostFreePromotionConfig;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.ExpressHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 包邮活动
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class PostFreeAction extends AbstractAction {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 频次限制 1不限制 2整个活动一次 3每天一次
     */
    private ActFrequencyEnum frequency;
    /**
     * 活动总数
     */
    private long actLimitNum;
    /**
     * 活动限制. 数据值：0-不限制， 没有限制就没有对应字段
     */
    private ActNumLimitRule numLimitRule;

    /**
     * 执行包邮信息
     *
     * @param promotion 优惠工具
     * @param request   请求参数
     * @param context   请求上下文，活动组件间
     * @throws BizError 业务异常
     */
    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (CollectionUtils.isEmpty(context.getGoodIndex()) || context.getQuotaLevel() == null) {
            log.error("context indexList is empty or level is null. actId:{}, uid:{}", promotionId, request.getUserId());
            return;
        }

        List<CartItem> cartList = request.getCartList();
        setResult(context, cartList, promotion);

        // 初始化资源
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            initResource(request, promotionId, 1, context, actLimitNum, frequency, numLimitRule);
        }
    }

    private void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool) throws BizError {
        List<GoodsIndex> indexList = context.getGoodIndex();
        List<String> parentItemList = CartHelper.getParentItemIdList(indexList);
        List<String> joinedItemIdList = CartHelper.getJoinedItemIdList(indexList, cartList);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setJoinedItemId(joinedItemIdList);
        promotionInfo.setParentItemId(parentItemList);
        promotionInfo.setJoined(CollectionUtils.isNotEmpty(joinedItemIdList) ? 1 : 0);

        // 包邮信息
        String idKey = PromotionConstant.CARTLIST_ACTIVITY_PREFIX + promotionId;
        Express express = ExpressHelper.buildNew(idKey, PostFreeEnum.POST_FREE.getVal(), parentItemList,
                PriceConstant.POST_FREE_PRICE);

        // 设置上下文结果
        context.setPromotion(promotionInfo);
        context.setExpress(express);
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof PostFreePromotionConfig)) {
            return;
        }
        PostFreePromotionConfig promotionConfig = (PostFreePromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.frequency = promotionConfig.getFrequency();
        this.actLimitNum = promotionConfig.getActLimitNum();
        this.numLimitRule = promotionConfig.getNumLimitRule();
    }
}



