package com.xiaomi.nr.promotion.resource.external;

import com.google.common.collect.Lists;
import com.xiaomi.nr.phoenix.api.dto.response.tradeInfo.TradeInfoQueryListResp;
import com.xiaomi.nr.promotion.activity.pool.PromotionInstancePool;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.GovernmentQualificationEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.domain.subsidyactivity.service.common.QualificationInfoService;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.model.promotionconfig.GovernmentSubsidyPromotionConfig;
import com.xiaomi.nr.promotion.util.CartHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/21 15:36
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SubsidyPhoenixExternalProvider extends ExternalDataProvider<TradeInfoQueryListResp> {

    @Autowired
    private QualificationInfoService qualificationInfoService;

    @Autowired
    private PromotionInstancePool promotionInstancePool;

    private ListenableFuture<TradeInfoQueryListResp> future;

    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        if (request.getUserId() == null || request.getUserId() <= 0) {
            return;
        }
        List<CartItem> cartList = request.getCartList();
        boolean cannotJoinSubsidy = cartList.stream()
                .allMatch(po -> po.getCannotJoinActTypes().contains((long) ActivityTypeEnum.GOVERNMENT_SUBSIDY.getValue()));
        if (cannotJoinSubsidy) {
            return;
        }
        List<ActivityTool> activityTools = promotionInstancePool.activitySearcher(request.getChannel(), request.getOrgCode(), CartHelper.getSkuPackageList(request.getCartList()));
        boolean canJoinAct = activityTools.stream()
                .anyMatch(activityTool -> activityTool.getType().getTypeId() == ActivityTypeEnum.GOVERNMENT_SUBSIDY.getValue());
        if (!canJoinAct) {
            return;
        }
        //北京：32   ----目前只有北京调此三方优惠获取资格码接口
        List<Integer> activityReportCity = Lists.newArrayList(GovernmentQualificationEnum.BEIJING.id);
        //获取国补活动
        List<ActivityTool> governmentActivity = activityTools.stream().filter(activityTool -> activityTool.getType().getTypeId() == ActivityTypeEnum.GOVERNMENT_SUBSIDY.getValue()).toList();
        //获取国补活动配置信息
        List<GovernmentSubsidyPromotionConfig> governmentConfigs = governmentActivity.stream().map(activity -> (GovernmentSubsidyPromotionConfig) activity.getPromotionConfig()).toList();
        //判断是否匹配
        boolean isMatch = governmentConfigs.stream().anyMatch(config -> activityReportCity.contains(config.getReportCity()));
        if (isMatch) {
            future = qualificationInfoService.getQualificationInfoAsync(request.getUserId());
        }
    }

    @Override
    protected long getTimeoutMills() {
        return DEFAULT_TIMEOUT;
    }

    @Override
    public ListenableFuture<TradeInfoQueryListResp> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.PHOENIX_SUBSIDY_QUALIFICATION;
    }
}
