package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.nr.phoenix.api.dto.response.tradeInfo.TradeInfoQueryListResp;
import com.xiaomi.nr.promotion.activity.pool.PromotionInstancePool;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.domain.subsidyactivity.service.common.QualificationInfoService;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.util.CartHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/21 15:36
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SubsidyPhoenixExternalProvider extends ExternalDataProvider<TradeInfoQueryListResp> {

    @Autowired
    private QualificationInfoService qualificationInfoService;

    @Autowired(required = false)
    private PromotionInstancePool promotionInstancePool;

    private ListenableFuture<TradeInfoQueryListResp> future;

    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        if (request.getUserId() == null || request.getUserId() <= 0) {
            return;
        }
        List<CartItem> cartList = request.getCartList();
        boolean cannotJoinSubsidy = cartList.stream()
                .allMatch(po -> po.getCannotJoinActTypes().contains((long) ActivityTypeEnum.NEW_PURCHASE_SUBSIDY.getValue()));
        if (cannotJoinSubsidy) {
            return;
        }
        List<ActivityTool> activityTools = promotionInstancePool.activitySearcher(request.getChannel(), request.getOrgCode(), CartHelper.getSkuPackageList(request.getCartList()));
        boolean canJoinAct = activityTools.stream()
                .anyMatch(activityTool -> activityTool.getType().getTypeId() == ActivityTypeEnum.NEW_PURCHASE_SUBSIDY.getValue());
        if (!canJoinAct) {
            return;
        }
        future = qualificationInfoService.getQualificationInfoAsync(request.getUserId());
    }

    @Override
    protected long getTimeoutMills() {
        return DEFAULT_TIMEOUT;
    }

    @Override
    public ListenableFuture<TradeInfoQueryListResp> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.PHOENIX_SUBSIDY_QUALIFICATION;
    }
}
