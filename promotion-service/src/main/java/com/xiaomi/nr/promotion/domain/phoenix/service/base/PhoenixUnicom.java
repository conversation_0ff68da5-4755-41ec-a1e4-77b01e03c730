package com.xiaomi.nr.promotion.domain.phoenix.service.base;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.api.dto.model.ThirdPromotion;
import com.xiaomi.nr.promotion.constant.PhoenixParamConstant;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.enums.BusinessTypeEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.nr.promotion.util.ThirdPromotionHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 三方优惠-联通运营商入驻
 *
 * <AUTHOR>
 * @date 2022/9/20
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class PhoenixUnicom extends AbstractPhoenixTool {
    @Autowired
    private CheckoutCartTool checkoutCartTool;

    /**
     * 条件是否满足
     *
     * @param request 请求参数
     * @param context 上下文
     * @return 是否满足 true/false
     * @throws BizError 异常情况业务异常
     */
    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        ThirdPromotion thirdPromotion = ThirdPromotionHelper.getThirdPromotion(request.getThirdPromotions(), BusinessTypeEnum.UNICOM);
        int discount = ThirdPromotionHelper.getIntParamValue(thirdPromotion, PhoenixParamConstant.DISCOUNT, 0);
        if (discount == 0) {
            log.warn("phoenix unicom packageAmount=0. userId:{} discount:{}", request.getUserId(), discount);
            return false;
        }
        return checkoutPhoenixPackage(thirdPromotion, request, context, discount);
    }

    /**
     * 优惠结算
     * 1. 获取套餐内金额
     * 2. 进行金额计算，计算购物车总金额，如果大于券金额，则扣减全部优惠金额，如果小于券金额，则使用购物车金额
     * 3. 进行金额分摊
     * 4. 如果是submit 则进行金额冻结资源操作
     *
     * @param request         请求参数
     * @param checkoutContext 上下文
     * @throws BizError 业务异常
     */
    @Override
    public void doCheckout(CheckoutPromotionRequest request, CheckoutContext checkoutContext) throws BizError {
        // 优惠金额计算
        ThirdPromotion thirdPromotion = ThirdPromotionHelper.getThirdPromotion(request.getThirdPromotions(), BusinessTypeEnum.UNICOM);
        int balance = ThirdPromotionHelper.getIntParamValue(thirdPromotion, PhoenixParamConstant.DISCOUNT, 0);

        // 计算优惠进行
        List<CartItem> cartList = getCartList(request, checkoutContext);
        long reducePrice = calculateReducePrice(cartList, balance);
        if (reducePrice == 0L) {
            log.warn("phoenix unicom reducePrice=0. userId:{} balance:{}", request.getUserId(), balance);
            return;
        }

        // 进行优惠分摊
        String idKey = PromotionConstant.CARTLIST_PHOENIX_PREFIX + getType().getTypeId();
        checkoutCartTool.divideCartsReduce(reducePrice, cartList, idKey, getType().getTypeId(), (long) getType().getTypeId());
        log.info("phoenix unicom reduce. userId:{}, reduceAmount:{}", request.getUserId(), reducePrice);

        // 如果是submit 则进行资源构建
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            initOperatorResource(request, checkoutContext, thirdPromotion, reducePrice);
        }
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.PHOENIX_UNICOM;
    }
}
