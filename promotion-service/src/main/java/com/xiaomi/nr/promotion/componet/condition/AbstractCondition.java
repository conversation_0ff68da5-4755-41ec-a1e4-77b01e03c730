package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.CartItemChild;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.CompareHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 所有活动条件判断基类
 *
 * <AUTHOR>
 * @date 2021/3/22
 */
@Slf4j
public abstract class AbstractCondition extends Condition {

    /**
     * 单组符合列表
     *
     * @param cartList  购物车列表
     * @param context   上下文
     * @param joinGoods 参与商品
     * @param uid       用户id
     * @param online    是否线上
     * @return 符合列表
     */
    protected List<GoodsIndex> doGoodsGroupMatch(List<CartItem> cartList, LocalContext context, CompareItem joinGoods,
                                                 Long uid, boolean online, Long activityId, Integer activityType,
                                                 boolean checkPackage, List<String> saleSources, String accessCode) {
        Map<String, GoodsHierarchy> hierarchyMap = context.getGoodsHierarchyMap();
        CompareItem globalInExclude = context.getGlobalInExclu();
        CompareItem globalActInExclude = context.getGlobalActInExclu();

        // 查找符合包含条件的购物车item列表
        List<GoodsIndex> indexList = new ArrayList<>();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            //处理当前item是否可参加活动
            boolean itemQualify = CartHelper.checkItemActQualify(item, activityType, online, saleSources, accessCode);
            if (!itemQualify) {
                continue;
            }
            // 做单品或套装的匹配
            boolean isMatched = false;
            if (StringUtils.isNotBlank(item.getSku())) {
                isMatched = doSingleMatch(item, hierarchyMap, globalInExclude, globalActInExclude, joinGoods, uid, activityId);
            } else if (StringUtils.isNotBlank(item.getPackageId())) {
                isMatched = doPackageMatch(item, hierarchyMap, globalInExclude, globalActInExclude, joinGoods, checkPackage);
            }
            if (!isMatched) {
                continue;
            }
            indexList.add(new GoodsIndex(item.getItemId(), idx));
        }
        return indexList;
    }

    private boolean doSingleMatch(CartItem item, Map<String, GoodsHierarchy> hierarchyMap, CompareItem globalInExclude,
                                  CompareItem globalActInExclude, CompareItem joinGoods, Long uid, Long activityId) {
        GoodsHierarchy goodsHierarchy = hierarchyMap.get(item.getSku());
        if (goodsHierarchy == null) {
            log.warn("uid:{}, item:{}, actId:{} goodsHierarchy not found", uid, item.getItemId(), activityId);
            return false;
        }
        //是否满足全局排除
        if (CompareHelper.isFillInclude(goodsHierarchy, globalInExclude, true)) {
            return false;
        }
        // 满足全局活动排除的
        if (CompareHelper.isFillInclude(goodsHierarchy, globalActInExclude, true)) {
            return false;
        }
        //是否属于活动配置中的包含商品
        return CompareHelper.isFillInclude(goodsHierarchy, joinGoods, true);
    }


    private boolean doPackageMatch(CartItem item, Map<String, GoodsHierarchy> hierarchyMap, CompareItem globalInExclude,
                                   CompareItem globalActInExclude, CompareItem joinGoods, boolean checkPackage) {
        GoodsHierarchy goodsHierarchy = hierarchyMap.get(item.getPackageId());
        if (goodsHierarchy == null) {
            log.warn("item:{}, packageId:{} goodsHierarchy not found", item.getItemId(), item.getPackageId());
            return false;
        }

        //是否满足全局排除
        if (CompareHelper.isFillInclude(goodsHierarchy, globalInExclude, true)) {
            return false;
        }
        // 满足全局活动排除的
        if (CompareHelper.isFillInclude(goodsHierarchy, globalActInExclude, true)) {
            return false;
        }
        // 是否属于活动配置中的包含商品
        boolean isFillInclude = false;
        if (CompareHelper.isFillInclude(goodsHierarchy, joinGoods, true)) {
            isFillInclude = true;
        }
        // 套装满足
        if (!isFillInclude && checkPackage) {
            isFillInclude = isChildAnyMatch(joinGoods, item.getChilds(), hierarchyMap);
        }
        return isFillInclude;
    }

    private boolean isChildAnyMatch(CompareItem joinGoods, List<CartItemChild> children, Map<String, GoodsHierarchy> hierarchyMap) {
        if (CollectionUtils.isEmpty(children)) {
            return false;
        }
        for (CartItemChild child : children) {
            GoodsHierarchy childCpc = hierarchyMap.get(child.getSku());
            if (childCpc == null) {
                log.warn("child item goodsHierarchy not found. child:{}", child);
                return false;
            }
            // 是否满足条件，是否在符合的主商品里面
            if (CompareHelper.isFillInclude(childCpc, joinGoods, true)) {
                return true;
            }
        }
        return false;
    }
}
