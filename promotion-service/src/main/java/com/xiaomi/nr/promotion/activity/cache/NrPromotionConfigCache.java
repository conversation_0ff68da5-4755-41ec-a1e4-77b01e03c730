package com.xiaomi.nr.promotion.activity.cache;

import com.google.common.collect.Lists;
import com.xiaomi.nr.md.promotion.admin.api.constant.ChannelEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.PromotionTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.SourceAppEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityFilterTip;
import com.xiaomi.nr.md.promotion.admin.api.dto.params.ActivityConfigForPromotionResponse;
import com.xiaomi.nr.promotion.rpc.mdpromotionadmin.PromotionAdminCustomServiceProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import static com.xiaomi.nr.promotion.activity.cache.RefreshableActivityConfigTask.BEAN_NAME_PREFIX;

/**
 * @author: zhangliwei6
 * @date: 2025/3/18 16:51
 * @description:
 */
@Slf4j
@Component(BEAN_NAME_PREFIX + "MISHOP")
public class NrPromotionConfigCache implements RefreshableActivityConfigTask, InitializingBean {

    @Autowired
    private PromotionAdminCustomServiceProxy promotionAdminCustomServiceProxy;

    // 活动配置缓存
    private List<ActivityConfig> activityConfigCache = new ArrayList<>();

    // <活动id, 该活动在activityConfigCache中的index>
    private final Map<Long, Integer> cacheIdxMap = new HashMap<>();

    private final AtomicLong latestSeqId = new AtomicLong(0);

    /**
     * 本地锁
     */
    private final Lock lock = new ReentrantLock(true);

    @Override
    public void refreshCacheBySeqId(Long seqId) {
        try {
            lock.lock();
            long curVersion = latestSeqId.get();
            // 刚启动，已全量刷新，所以以最新seqId为准
            if (curVersion == 0) {
                curVersion = seqId;
            } else if (seqId < curVersion) {
                // 当前版本大于变更版本则忽略
                log.warn("current version-{} greater than seqId-{}, will be ignored", curVersion, seqId);
                return;
            }
            long startTime = System.currentTimeMillis();
            log.info("start to refresh actConfig cache by seqId-{}", seqId);

            Map<Integer, ActivityFilterTip> activityFilterTips = new HashMap<>();
            // CRM
            ActivityFilterTip crm = new ActivityFilterTip();
            crm.setPromotionTypes(Lists.newArrayList(
                    PromotionTypeEnum.B2T_CHANNEL_PRICE.code,
                    PromotionTypeEnum.B2T_STEP_PRICE.code,
                    PromotionTypeEnum.B2T_VIP_DISCOUNT.code));
            crm.setChannels(Lists.newArrayList(
                    ChannelEnum.B2T_C_CUSTOMER.value,
                    ChannelEnum.B2T_GOV_BIG_CUSTOMER.value,
                    ChannelEnum.B2T_MIJIA_BIG_CUSTOMER.value));
            activityFilterTips.put(SourceAppEnum.CRM.code, crm);

            // 团购
            ActivityFilterTip b2tAdmin = new ActivityFilterTip();
            b2tAdmin.setPromotionTypes(Lists.newArrayList(
                    PromotionTypeEnum.B2T_CHANNEL_PRICE.code,
                    PromotionTypeEnum.B2T_STEP_PRICE.code,
                    PromotionTypeEnum.B2T_VIP_DISCOUNT.code));
            b2tAdmin.setChannels(Lists.newArrayList(
                    ChannelEnum.B2T_C_CUSTOMER.value,
                    ChannelEnum.B2T_GOV_BIG_CUSTOMER.value,
                    ChannelEnum.B2T_MIJIA_BIG_CUSTOMER.value));
            activityFilterTips.put(SourceAppEnum.B2T_ADMIN.code, b2tAdmin);

            // 国补
            ActivityFilterTip govSubsidy = new ActivityFilterTip();
            govSubsidy.setPromotionTypes(Lists.newArrayList(
                    PromotionTypeEnum.GOVERNMENT_SUBSIDY.code));
            govSubsidy.setChannels(Lists.newArrayList(
                    ChannelEnum.MISHOP.value,
                    ChannelEnum.DIRECT.value));
            activityFilterTips.put(SourceAppEnum.PROMOTION_ADMIN.code, govSubsidy);

            // 地补
            ActivityFilterTip areaSubsidy = new ActivityFilterTip();
            areaSubsidy.setPromotionTypes(Lists.newArrayList(
                    PromotionTypeEnum.AREA_SUBSIDY.code));
            areaSubsidy.setChannels(Lists.newArrayList(
                    ChannelEnum.MISHOP.value));
            activityFilterTips.put(SourceAppEnum.AREA_SUBSIDY_ADMIN.code, areaSubsidy);

            ActivityConfigForPromotionResponse response = promotionAdminCustomServiceProxy.queryActivityBySeqIdV2(curVersion, activityFilterTips);
            if (response == null) {
                return;
            }

            // 返回的最大id小于当前id则忽略
            long remoteVersion = response.getSeqId();
            if (remoteVersion < curVersion) {
                log.warn("current version-{} greater than remote maxSeqId-{}, will be ignored", curVersion, remoteVersion);
                return;
            }

            // copyOnWrite
            List<ActivityConfig> copyCache = this.getAllFromCache();
            // 删除无效活动
            if (CollectionUtils.isNotEmpty(response.getInvalidActivityIds())) {
                response.getInvalidActivityIds().forEach(invalidId -> {
                    Integer idx = cacheIdxMap.get(invalidId);
                    if (idx != null) {
                        cacheIdxMap.remove(invalidId);
                        copyCache.remove(idx.intValue());
                    }
                });
            }

            // 更新新活动及索引
            if (CollectionUtils.isNotEmpty(response.getActivityConfigs())) {
                response.getActivityConfigs().forEach(activityConfig -> {
                    Long id = activityConfig.getId();
                    Integer idx = cacheIdxMap.get(id);
                    if (idx != null) {
                        copyCache.remove(idx.intValue());
                    }
                    copyCache.add(activityConfig);
                    cacheIdxMap.put(id, copyCache.size() - 1);
                });
            }

            activityConfigCache = copyCache;
            latestSeqId.getAndSet(remoteVersion);
            log.info("finish refresh actConfig cache, latestSeqId-{}, new actConfig size-{}, cost-{}ms", remoteVersion, response.getActivityConfigs().size(), System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("refresh actConfig cache error", e);
        } finally {
            lock.unlock();
        }
    }

    public void rebuildCache() {
        try {
            lock.lock();
            long startTime = System.currentTimeMillis();
            log.info("rebuild full actConfig cache start. startTime:{}", startTime);
            // 获取CRM、团购活动
            List<ActivityConfig> activityConfigList = promotionAdminCustomServiceProxy.queryActivityListV2();

            // 获取国补活动
            List<ActivityConfig> subsityActivityList = promotionAdminCustomServiceProxy.querySubsityActivityListV2();

            activityConfigList.addAll(subsityActivityList);
            subsityActivityList.clear();

            if (CollectionUtils.isNotEmpty(activityConfigList)) {
                activityConfigCache = activityConfigList;
                cacheIdxMap.clear();
                for (int i = 0; i < activityConfigCache.size(); i++) {
                    cacheIdxMap.put(activityConfigCache.get(i).getId(), i);
                }
            } else {
                throw new RuntimeException("activityConfig is empty");
            }
            long endTime = System.currentTimeMillis();
            log.info("rebuild full actConfig cache end. endTime:{} ws:{}", endTime, endTime - startTime);
        } catch (Exception e) {
            log.error("update full actConfig cache error.", e);
            throw new RuntimeException(e);
        } finally {
            lock.unlock();
        }
    }

    public List<ActivityConfig> getAllFromCache() {
        return new ArrayList<>(activityConfigCache);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("begin to rebuild full actConfig cache on server started");
        rebuildCache();
    }
}