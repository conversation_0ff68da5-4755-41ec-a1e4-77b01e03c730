package com.xiaomi.nr.promotion.componet.condition.carshop;

import com.google.common.collect.Lists;
import com.xiaomi.goods.gis.dto.stock.GiftStockRespParam;
import com.xiaomi.nr.md.promotion.admin.api.constant.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.OrderCartItem;
import com.xiaomi.nr.promotion.componet.condition.AbstractBuyGiftCondition;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.entity.redis.GiftBargainGroup;
import com.xiaomi.nr.promotion.entity.redis.SkuGroup;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.BooleanV2Enum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.GoodsIndexNew;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyGiftPromotionConfig;
import com.xiaomi.nr.promotion.resource.external.GoodsStockExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.tool.ConditionCheckTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024/6/19
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarShopBuyGiftCondition extends AbstractBuyGiftCondition {

    @Resource
    private ActivityRedisDao activityRedisDao;
    
    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (!Objects.equals(context.getBizPlatform(), BizPlatformEnum.CAR_SHOP)) {
            return false;
        }
        
        if (!Objects.equals(request.getChannel(), ChannelEnum.CAR_SHOP.value)) {
            return false;
        }
        
        // check master
        if (!checkMasterGoods(request, context)) {
            return false;
        }
        
        // check gift
        if (!checkGiftGoods(request.getCartList())) {
            return false;
        }

        // check stock 存在改购物车操作
        if (!checkGoodsStock(request, context)) {
            return false;
        }
        
        return true;
    }

    /**
     * 1. 校验 活动库存 + 实物库存
     * 2. 清除无效赠品
     * @param request
     * @param context
     * @return
     */
    private boolean checkGoodsStock(CheckoutPromotionRequest request, LocalContext context) {
        if (giftGoods == null || CollectionUtils.isEmpty(giftGoods.getSkuGroupList())) {
            return false;
        }

        try {
            Integer maxCount = context.getFillTimes();
            List<GoodsIndex> goodIndex = context.getGoodIndex();
            GoodsStockExternalProvider provider =
                    (GoodsStockExternalProvider) context.getExternalDataMap().get(ResourceExtType.GIFT_GOODS_STOCK);
            Map<String, GiftStockRespParam> goodsStockMap;
            // fixme giftGoods.ignoreCheck时不用加载库存
            goodsStockMap = provider.getData();
            boolean anyMatch = false;
            for (SkuGroup skuGroup : giftGoods.getSkuGroupList()) {
                if (checkGroupNum(skuGroup, maxCount, request.getCartList(), goodIndex, goodsStockMap)) {
                    anyMatch = true;
                }
            }
            return anyMatch;
        } catch (BizError e) {
            log.error("gift stock map is error, skip promotion is {}", GsonUtil.toJson(promotionId));
            return false;
        }
    }

    /**
     * check group
     *
     * @param group
     * @param maxCount
     * @param cartList
     * @param goodIndex
     * @param goodsStockMap
     * @return
     */
    private boolean checkGroupNum(SkuGroup group, Integer maxCount, List<CartItem> cartList, List<GoodsIndex> goodIndex,
                                  Map<String, GiftStockRespParam> goodsStockMap) {
        if (CollectionUtils.isEmpty(group.getListInfo())) {
            return false;
        }
        Long groupId = group.getGroupId();
        boolean anyMatch = false;
        for (GiftBargainGroup gift : group.getListInfo()) {
            if (checkGiftGoods(gift, groupId, maxCount, cartList, goodIndex, goodsStockMap)) {
                anyMatch = true;
            }
        }
        return anyMatch;
    }

    /**
     * 校验每个从品是否满足库存条件， 如不满足 则删除购物车中该商品
     *
     * @param gift
     * @param groupId
     * @param maxCount
     * @param cartList
     * @param goodIndex
     * @param goodsStockMap
     * @return
     */
    private boolean checkGiftGoods(GiftBargainGroup gift, Long groupId, Integer maxCount, List<CartItem> cartList,
                                   List<GoodsIndex> goodIndex, Map<String, GiftStockRespParam> goodsStockMap) {

        // check activity limit
        boolean checkLimit = true;
        Integer limitNum = activityRedisDao.getActBuyGiftLimitNum(promotionId, String.valueOf(gift.getSsuId()), groupId);
        if ((gift.getGiftLimitNum() - limitNum) < maxCount * gift.getGiftBaseNum()) {
            checkLimit = false;
        }

        // check goods stock
        boolean checkStock = true;
        if (Objects.equals(giftGoods.getIgnoreStock(), BooleanV2Enum.NO.getValue())) {
            Map<String, CartItem> cartMap = cartList.stream()
                    .collect(Collectors.toMap(OrderCartItem::getItemId, Function.identity(), (x, y) -> x));
            for (GoodsIndex index : goodIndex) {
                CartItem item = cartMap.get(index.getItemId());
                if (Objects.isNull(item)) {
                    continue;
                }
                String stockKey = item.getSsuId() + "_" + gift.getSsuId();
                GiftStockRespParam resp = goodsStockMap.get(stockKey);
                if (Objects.isNull(resp) || Objects.isNull(resp.getStockNum())
                        || resp.getStockNum() / gift.getGiftBaseNum() < maxCount) {
                    checkStock = false;
                }
            }
        }

        if (checkLimit && checkStock) {
            return true;
        }

        // 处理是否存在有在购物车里但是没有库存的， 有就需要给删了，让重新选
        CartItem cartItem = cartList.stream()
                .filter(item -> SourceEnum.isGift(item.getSource()))
                .filter(item -> Objects.equals(String.valueOf(promotionId), item.getSourceCode()))
                .filter(item -> Objects.equals(gift.getSsuId(), item.getSsuId())).findAny().orElse(null);
        if (cartItem != null) {
            CartHelper.delGiftBargain(cartList, promotionId, cartItem.getSsuId());
        }
        return false;
    }

    /**
     * 1. check 有效主品
     * 2. activity master list VS cart item list
     * 3. fill context (fillTimes  &&  goodIndex)
     * @param request
     * @param context
     * @return
     */
    private boolean checkMasterGoods(CheckoutPromotionRequest request, LocalContext context) {
        List<CartItem> cartList = request.getCartList();
        Long uid = request.getUserId();
        
        if (Objects.isNull(includeGoodsGroup)) {
            log.error("condition is not satisfied. includedGoodsGroup is null. actId:{} uid:{}", promotionId, uid);
            return false;
        }
        
        Integer fillTimes = 0;
        List<GoodsIndex> indexList = Lists.newArrayList();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            //处理当前item是否可参加活动
            boolean itemQualify = this.checkItemActQualify(item, promotionToolType.getTypeId());
            if (!itemQualify) {
                continue;
            }
            // 商品匹配
            boolean isMatched = doMatchMasterGoods(item);
            if (!isMatched) {
                continue;
            }
            fillTimes += item.getCount();
            indexList.add(new GoodsIndex(item.getItemId(), idx));
        }
        
        if (fillTimes <= 0) {
            log.debug("condition is not satisfied. validCondition is not match groups. actId:{}, uid:{},fillTimes:{}", promotionId, uid, fillTimes);
            return false;
        }
        // 没有符合的商品，不满足活动
        if (CollectionUtils.isEmpty(indexList)) {
            log.debug("condition is not satisfied. fillGoodsList is empty. actId:{} uid:{}", promotionId, uid);
            return false;
        }
        
        
        context.setFillTimes(fillTimes);
        context.setGoodIndex(indexList);
        return true;
    }
    
    /**
     * ssu id match
     * @param cartItem
     * @return
     */
    private boolean doMatchMasterGoods(CartItem cartItem) {
        return includeGoodsGroup.getJoinGoods().getSsuId().contains(cartItem.getSsuId());
    }

    /**
     * 1. 筛选出本活动的赠品信息
     * @param cartList
     * @return
     */
    private boolean checkGiftGoods(List<CartItem> cartList) {
        List<GoodsIndexNew> indexNewList = CartHelper.getCartsIndex(cartList, SourceEnum.SOURCE_GIFT.getSource(), String.valueOf(promotionId));
        if (CollectionUtils.isEmpty(indexNewList)) {
            return true;
        }
        return indexNewList.stream().allMatch(index -> checkItem(cartList, index));
    }
    
    
    private boolean checkItem(List<CartItem> cartList, GoodsIndexNew indexNew) {
        CartItem cartItem = CartHelper.getCartItem(cartList, indexNew);
        if (cartItem == null) {
            return false;
        }
        GiftBargainGroup giftGoods = getGiftGoods(cartItem);
        if (giftGoods == null) {
            return false;
        }
        if ((cartItem.getCount() % giftGoods.getGiftBaseNum()) != 0) {
            log.warn("act base is not fill. cartItem:{} baseNum:{}", GsonUtil.toJson(cartItem), giftGoods.getGiftBaseNum());
            return false;
        }
        return true;
    }
    
    private GiftBargainGroup getGiftGoods(CartItem cartItem) {
        if (giftGoods == null || CollectionUtils.isEmpty(giftGoods.getSkuGroupList())) {
            return null;
        }
        SkuGroup skuGroup = giftGoods.getSkuGroupList().stream()
                .filter(group -> Objects.equals(group.getGroupId(), cartItem.getGroupId()))
                .findAny().orElse(null);
        if (skuGroup == null || CollectionUtils.isEmpty(skuGroup.getListInfo())) {
            return null;
        }
        return skuGroup.getListInfo().stream()
                .filter(group -> Objects.equals(group.getSsuId(), cartItem.getSsuId()))
                .findAny().orElse(null);
    }
    
    
    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof BuyGiftPromotionConfig)) {
            log.error("config is not instanceof BuyGiftPromotionConfig. config:{}", config);
            return;
        }
        BuyGiftPromotionConfig promotionConfig = (BuyGiftPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.giftGoods = promotionConfig.getGiftGoods();
        this.includeGoodsGroup = promotionConfig.getIncludeGoodsGroup();
        this.promotionToolType = promotionConfig.getPromotionType();
    }
}
