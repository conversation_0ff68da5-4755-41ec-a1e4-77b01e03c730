package com.xiaomi.nr.promotion.model.common;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 活动商品
 *
 * <AUTHOR>
 * @date 2021/9/6
 */
@Data
public class ActGoods {

    /**
     * 参与列表
     */
    @SerializedName("list")
    private Map<String, String> list;

    /**
     * SKU 列表
     */
    @SerializedName("sku_list")
    private List<String> skuList;

    /**
     * 当前次数
     */
    @SerializedName("current_count")
    private Long curCount = 0L;

    /**
     * 最多次数
     */
    @SerializedName("max_count")
    private Long maxCount = 0L;

    /**
     * 购物车价格
     */
    @SerializedName("cart_price")
    private Long cartPrice = 0L;

    /**
     * 选择类型
     */
    @SerializedName("select_type")
    private String selectType = "";

    /**
     * 忽略库存
     */
    @SerializedName("ignore_stock")
    private Integer ignoreStock = 0;

    /**
     * 退款时赠品、加价购价值
     */
    @SerializedName("refund_deduction")
    private Long refundValue = 0L;

    /**
     * 1:强制顺序挂靠主商品
     */
    @SerializedName("force_parent")
    private Integer forceParent = 0;

    /**
     * 活动码
     */
    @SerializedName("access_code")
    private String accessCode = "";
}
