package com.xiaomi.nr.promotion.domain.coupon.service.carshop;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.model.DeductedInfo;
import com.xiaomi.nr.promotion.domain.coupon.service.base.type.AbstractGiftCoupon;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.xiaomi.nr.promotion.error.ClientSideErr.COMMON_SYSTEM_ERR_MSG;

/**
 * 车商城-礼品券
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class CarShopGiftCoupon extends AbstractGiftCoupon {

    @Autowired
    private transient CheckoutCartTool checkoutCartTool;

    protected Set<Long> includeGoods;

    @Override
    public boolean load(CheckoutCoupon checkoutCoupon) throws BizError {
        this.id = checkoutCoupon.getCouponId();
        this.checkoutCoupon = checkoutCoupon;
        if (checkoutCoupon.getValidGoodsList() != null) {
            this.includeGoods = new HashSet<>(checkoutCoupon.getValidGoodsList());
        } else {
            this.includeGoods = new HashSet<>();
        }
        return true;
    }

    @Override
    public Coupon generateCartCoupon() {
        return initCoupon();
    }

    @Override
    public CouponCheckoutResult checkoutCoupon(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        // 筛选适用该券的商品列表
        List<CartItem> cartList = request.getCartList();
        List<GoodsIndex> matchGoodsList = matchCartList(cartList, context);
        // 筛选商品数量（N元券限制商品数量为1）
        List<GoodsIndex> goodsList = matchGoodsList.stream().filter(good -> cartList.get(good.getIndex()).getCount() == 1).collect(Collectors.toList());
        if (matchGoodsList.size() != 0 && goodsList.size() == 0) {
            CouponCheckoutResult result = new CouponCheckoutResult();
            result.setCouponId(id);
            if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                result.setCouponCode(checkoutCoupon.getCouponCode());
            }
            result.setAllow(false);
            result.setUnusableReason("未满足优惠券使用条件");
            result.setUnusableCode(ErrCode.ERR_COUPON_MONEY_NUM.getCode());
            result.setKeyDataUnusable("仅单件商品加购时可用");
            return result;
        }
        // 计算适用商品列表的总价（去掉前置优惠）
        ValidGoods validGoods = buildValidGoods(cartList, goodsList);
        List<Integer> indexList = goodsList.stream().map(GoodsIndex::getIndex).collect(Collectors.toList());

        // 判断适用商品列表是否满足券的使用条件：满额/满件
        Pair<Boolean, String> satisfiedResult = isSatisfiedQuota(validGoods);
        // 构建结果
        // 条件满足
        if (satisfiedResult.getLeft()) {
            // 筛选所有适用商品中所有可作为抵扣商品的
            List<DeductedInfo> deductedInfoList = getDeductedInfoList(cartList, indexList);

            // 选择最终的抵扣商品、构建结果
            if (CollectionUtils.isNotEmpty(deductedInfoList)) {

                // 排序后选择第一个商品
                deductedInfoList.sort((info1, info2) -> (int) (info2.getGoodPrice() - info1.getGoodPrice()));
                DeductedInfo deductedGood = deductedInfoList.get(0);
                log.debug("deductedGood.goodPrice {}", deductedGood.getGoodPrice());

                long deductMoney = 0L;//券的减免金额
                deductMoney = Math.max(deductedGood.getGoodPrice() - checkoutCoupon.getPromotionValue(), 0L);//<被抵扣商品的当前单价-券的抵扣金额，0>

                CouponCheckoutResult result = new CouponCheckoutResult();
                result.setCouponId(id);
                if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                    result.setCouponCode(checkoutCoupon.getCouponCode());
                }
                result.setAllow(true);
                result.setReduceAmount(deductMoney);//该券的可减免金额
                result.setDeductedInfo(deductedGood);//被抵扣商品
                result.setValidGoods(goodsList);//购物车中适用该券的所有商品
                result.setValidGoodsPrice(validGoods.getValidPrice());//购物车中适用该券的所有商品的总价（SD去掉前置优惠）
                return result;
            } else {
                CouponCheckoutResult result = new CouponCheckoutResult();
                result.setCouponId(id);
                if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                    result.setCouponCode(checkoutCoupon.getCouponCode());
                }
                result.setAllow(false);
                result.setUnusableReason("未满足优惠券使用条件");
                result.setUnusableCode(ErrCode.ERR_COUPON_MONEY_NUM.getCode());
                result.setKeyDataUnusable(satisfiedResult.getRight());
                return result;
            }
        } else {
            CouponCheckoutResult result = new CouponCheckoutResult();

            result.setAllow(false);
            result.setUnusableReason("未满足优惠券使用条件");
            result.setUnusableCode(ErrCode.ERR_COUPON_MONEY_NUM.getCode());
            result.setKeyDataUnusable(satisfiedResult.getRight());
            return result;
        }
    }

    // 筛选适用该券的商品列表
    protected List<GoodsIndex> matchCartList(List<CartItem> cartList, CheckoutContext context)
            throws BizError {
        List<GoodsIndex> indexList = new ArrayList<>();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);

            if (SourceEnum.isGiftBargain(item.getSource())) {
                continue;
            }
            //车商城加购页未选中商品跳过
            Boolean selected = item.getSelected();
            if (context.getFromInterface().equals(FromInterfaceEnum.CHECKOUT_CART) && (Objects.isNull(selected) || Objects.equals(selected, Boolean.FALSE))) {
                continue;
            }
            // 判断当前商品是否适用该券类型
            boolean itemQualify = CartHelper.isCouponQualifyItem(item, (long) getCouponType().getType());
            if (!itemQualify) {
                log.debug("coupon:{}  matchCartList itemQualify item:{}", getCouponId(), GsonUtil.toJson(item));
                continue;
            }
            // 判断当前商品是否适是该券的适用商品
            boolean isMatched;
            isMatched = includeGoods.contains(getJoinGoods(item));
            if (!isMatched) {
                log.debug("coupon:{} matchCartList isMatched item:{}", getCouponId(), GsonUtil.toJson(item));
                continue;
            }
            indexList.add(new GoodsIndex(item.getItemId(), idx));
        }
        return indexList;
    }

    public Long getJoinGoods(CartItem cartItem) {
        return cartItem.getSsuId();
    }

    // 计算适用商品列表的总价（去掉前置优惠）
    protected ValidGoods buildValidGoods(List<CartItem> cartList, List<GoodsIndex> goodsInd) {
        long validPrice = 0L;
        long includeCount = 0L;
        int len = cartList.size();
        for (GoodsIndex index : goodsInd) {
            int idx = index.getIndex();
            if (idx >= len) {
                continue;
            }
            CartItem item = cartList.get(idx);
            if (item == null) {
                continue;
            }

            Long curPrice = CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList());

            validPrice += (curPrice * item.getCount());
            includeCount += item.getCount();
        }
        ValidGoods validGoods = new ValidGoods();
        validGoods.setValidNum(includeCount);
        validGoods.setValidPrice(validPrice);
        return validGoods;
    }

    // 筛选所有适用商品中所有可作为抵扣商品的
    private List<DeductedInfo> getDeductedInfoList(List<CartItem> cartList, List<Integer> indexes) throws BizError {

        if (CollectionUtils.isEmpty(indexes)) {
            return Collections.emptyList();
        }

        List<DeductedInfo> deductedGoodsInfo = new ArrayList<>();
        // 判断各个适用商品
        for (Integer index : indexes) {
            if (index > cartList.size()) {
                log.error("UpdateCartsReduceDeductCoupon {}th cart is wrong", index);
                throw ExceptionHelper.create(ErrCode.ERR_FUNC_INPUT, COMMON_SYSTEM_ERR_MSG);
            }
            CartItem cartItem = cartList.get(index);

            // 是否在券的可抵扣商品列表中
            if (cartItem.getSsuId() == null || !this.includeGoods.contains(cartItem.getSsuId())) {
                continue;
            }
            // 如果当前价格已经为０则跳过
            long curPrice = CartHelper.itemCurPrice(cartItem.getOriginalCartPrice(), cartItem.getReduceItemList());
            if (curPrice <= 0) {
                continue;
            }
            // 收集
            DeductedInfo deductedGood = new DeductedInfo(index, curPrice);
            deductedGoodsInfo.add(deductedGood);
        }
        return deductedGoodsInfo;
    }

    @Override
    public void updateCartsReduce(CheckoutPromotionRequest request, CheckoutContext context, CouponCheckoutResult result) {
        //处理商品分摊
        updateItemReduce(request.getCartList(), result);

        //更新context
        updateCommonInfo(request, context);

    }

    @Override
    public void updateItemReduce(List<CartItem> cartList, CouponCheckoutResult result) {
        if (CollectionUtils.isEmpty(cartList) || result == null) {
            return ;
        }
        CartItem cartItem = cartList.get(result.getDeductedInfo().getIndex().getInCarts());//被抵扣商品
        if (cartItem == null) {
            log.error("CarShopDeductCoupon updateItemReduce cartItem is nil, CouponId:{}", result.getCouponId());
            return;
        }

        long deductMoney = result.getReduceAmount();//券的减免金额

        if (deductMoney < 0 || (CartHelper.itemCurPrice(cartItem.getOriginalCartPrice(), cartItem.getReduceItemList()) * cartItem.getCount()) < deductMoney) {
            log.error("CarShopDeductCoupon updateItemReduce price error CouponId:{}, cartItem is {}, reduce is {}, ItemCurPrice is {}", result.getCouponId(), cartItem, deductMoney,
                    CartHelper.itemCurPrice(cartItem.getOriginalCartPrice(), cartItem.getReduceItemList()));
            return;
        }
        //新优惠分摊字段
        checkoutCartTool.updateCartsSsuReduce(cartItem, deductMoney, getCouponType().getType(), result.getCouponId(), result.getBudgetApplyNo(), result.getLineNum());

        //老优惠分摊字段
//        String idKey = CouponHelper.getCartListCouponKey(String.valueOf(result.getCouponId()));
//        long reduceVal = Optional.ofNullable(cartItem.getReduceList().get(idKey)).orElse(0L);
//        long reduceAmount = Optional.ofNullable(cartItem.getReduceAmount()).orElse(0L);
//        cartItem.getReduceList().put(idKey, reduceVal + deductMoney);
//        cartItem.setReduceAmount(reduceAmount + deductMoney);
//        // 抵扣劵的优惠明细
//        List<ReduceDetail> reduceDetails = cartItem.getReduceDetailList().getOrDefault(PromotionConstant.SHARE_COUPON_DEDUCT_KEY, new ArrayList<>());
//        ReduceDetail reduceDetail = new ReduceDetail();
//        reduceDetail.setId(result.getCouponId());
//        reduceDetail.setAmount(deductMoney);
//        reduceDetails.add(reduceDetail);
//        cartItem.getReduceDetailList().put(PromotionConstant.SHARE_COUPON_DEDUCT_KEY, reduceDetails);
    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR_SHOP;
    }
}
