package com.xiaomi.nr.promotion.componet.action;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.Specification;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.StepPriceProductRule;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.PromotionExtend;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.StepPricePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 阶梯价执行行为
 *
 * <AUTHOR>
 * @date 2023/2/14
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class StepPriceAction extends AbstractAction {
    /**
     * 优惠促销ID
     */
    private Long promotionId;
    /**
     * 优惠类型
     */
    private PromotionToolType promotionType;
    /**
     * 阶梯信息
     * key: SSU ID val:ActPriceInfo
     */
    private Map<Long, ActPriceInfo> stepPriceInfoMap;

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (MapUtil.isEmpty(stepPriceInfoMap)) {
            log.error("stepPriceInfoMap is empty. actId:{} uid:{}", promotionId, request.getUserId());
            return;
        }
        List<GoodsIndex> indexList = context.getGoodIndex();
        List<CartItem> cartList = request.getCartList();
        if (CollectionUtils.isEmpty(indexList) || CollectionUtils.isEmpty(cartList)) {
            log.warn("stepPrice indexList or carts is empty. actId:{} uid:{}", promotionId, indexList);
            return;
        }

        // 进行改价
        changePrice(cartList, indexList);

        // 设置结果
        setResult(context, cartList, promotion);
    }

    private void changePrice(List<CartItem> cartList, List<GoodsIndex> indexList) {
        for (GoodsIndex index : indexList) {
            CartItem item = cartList.get(index.getIndex());
            if (item == null || !Objects.equals(item.getItemId(), index.getItemId())) {
                log.warn("stepPrice idx not found in cats. idx:{}, item:{}", index, item);
                continue;
            }
            Long ssuId = item.getSsuId();
            // 获取价格
            ActPriceInfo priceInfo = stepPriceInfoMap.get(ssuId);
            if (priceInfo == null) {
                continue;
            }
            // 获取阶梯
            List<Specification> specList = priceInfo.getSpecificationList();
            if (CollectionUtils.isEmpty(specList)) {
                continue;
            }
            // 因为阶梯已经从大到小，所以直接取列表符合第一个就行
            Specification specification = specList.stream()
                    .filter(spec -> item.getCount() >= spec.getThreshold())
                    .findFirst().orElse(null);
            if (specification == null) {
                continue;
            }
            long finalPrice = Math.min(item.getCartPrice(), specification.getPrice());
            if (finalPrice < item.getCartPrice()) {
                // 目前阶梯价只会和米家直降价做对比，即直客、二部场景下取 min(阶梯价，直降价)
                // 如果阶梯价小于上次活动的直降价，清除上次直降的 reduceItemList, 恢复sellPrice为原价
                clearOnSaleInfo(item);
                changePriceWithReduceDetail(item, finalPrice, promotionId, promotionType);
            }
        }
    }

    private void clearOnSaleInfo(CartItem item){
        List<ReduceDetailItem> reduceItemList = Optional.ofNullable(item.getReduceItemList()).orElse(Lists.newArrayList());
        reduceItemList.clear();
        // 恢复子品
        for (CartItemChild child:item.getChilds()){
            child.setSellPrice(child.getOriginalSellPrice());
            child.setLowerPrice(0L);
            child.setOnsaleReduce(0L);
        }
        // 恢复套装
        item.getOnSalePromotionIdMap().clear();
        item.setCartPrice(item.getOriginalCartPrice());
        item.setOnsaleReduce(0L);
        item.setJoinOnsale(false);
        item.setChangePriceActType(0);
    }

    private void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool) throws BizError {
        List<String> parentItemList = CartHelper.getParentItemIdList(context.getGoodIndex());
        List<Long> ssuIdList = cartList.stream()
                .filter(item -> parentItemList.contains(item.getItemId()))
                .map(CartItem::getSsuId)
                .distinct()
                .collect(Collectors.toList());
        List<StepPriceProductRule> ruleList = ssuIdList.stream()
                .map(stepPriceInfoMap::get).filter(Objects::nonNull)
                .map(this::convert)
                .collect(Collectors.toList());
        PromotionExtend extend = new PromotionExtend();
        extend.setSteppriceExtend(ruleList);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setJoinedItemId(parentItemList);
        promotionInfo.setParentItemId(parentItemList);
        promotionInfo.setJoined(BooleanEnum.YES.getValue());
        promotionInfo.setExtend(GsonUtil.toJson(extend));

        // 设置上下文结果
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    private StepPriceProductRule convert(ActPriceInfo priceInfo) {
        StepPriceProductRule priceRule = new StepPriceProductRule();
        priceRule.setRuleType(priceInfo.getRuleType());
        priceRule.setCashBackPrice(priceInfo.getCashBackPrice());
        priceRule.setSpecificationList(priceInfo.getSpecificationList());
        return priceRule;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof StepPricePromotionConfig)) {
            log.error("config is not instanceof StepPricePromotionConfig. class:{}", config.getName());
            return;
        }
        StepPricePromotionConfig promotionConfig = (StepPricePromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        this.stepPriceInfoMap = promotionConfig.getStepPriceInfoMap();
    }
}
