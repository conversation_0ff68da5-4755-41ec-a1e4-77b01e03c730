package com.xiaomi.nr.promotion.componet.preparation;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.engine.componet.ConditionPreparation;
import com.xiaomi.nr.promotion.entity.redis.OrgInfo;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.RecycleOrderExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.recycle.api.activity.dto.ActivityQueryKeyDto;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;

/**
 * 米家以旧换新单查询
 *
 * <AUTHOR>
 * @date 2022/10/12
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class RecycleOrderPreparation extends ConditionPreparation {

    @Override
    public void prepare(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        Map<ResourceExtType, ExternalDataProvider<?>> providerMap = Optional.ofNullable(context.getExternalDataMap()).orElse(Collections.emptyMap());
        RecycleOrderExternalProvider provider = (RecycleOrderExternalProvider) providerMap.get(ResourceExtType.RECYCLE_ORDER);
        if (provider == null || provider.getData() == null) {
            return;
        }
        // 获取档位KEY
        ActivityQueryKeyDto keyDto = provider.getData();
        context.setRenewLevelKey(keyDto.getKey());
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
    }
}
