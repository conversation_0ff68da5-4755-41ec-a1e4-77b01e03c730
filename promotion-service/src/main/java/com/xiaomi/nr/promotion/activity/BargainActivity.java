package com.xiaomi.nr.promotion.activity;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.CheckProductsActResponse;
import com.xiaomi.nr.promotion.api.dto.GetProductActRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.componet.action.BargainAction;
import com.xiaomi.nr.promotion.componet.action.CouponLimitAction;
import com.xiaomi.nr.promotion.componet.condition.*;
import com.xiaomi.nr.promotion.componet.preparation.GlobalExcludePreparation;
import com.xiaomi.nr.promotion.componet.preparation.GoodsHierarchyPreparation;
import com.xiaomi.nr.promotion.componet.preparation.OrgInfoPreparation;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.ProductActGoodsProvider;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.*;
import com.xiaomi.nr.promotion.entity.redis.SkuGroup;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.enums.BooleanV2Enum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.ActPromExtend;
import com.xiaomi.nr.promotion.model.common.ActRespConverter;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BargainPromotionConfig;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 加价购活动
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class BargainActivity extends AbstractActivityTool implements ActivityTool, ProductActGoodsProvider {
    /**
     * 主商品列表
     */
    private List<FillGoodsGroup> includeGoodsGroups;
    /**
     * 加价购商品信息
     */
    private Goods bargainGoods;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine
                .conditionPreparation(GoodsHierarchyPreparation.class)
                .conditionPreparation(GlobalExcludePreparation.class)
                .conditionPreparation(OrgInfoPreparation.class)
                .condition(DailyTimeCondition.class)
                .condition(OrgCondition.class)
                .condition(ChannelCondition.class)
                .condition(ClientCondition.class)
                .condition(GiftBargainCondition.class)
                .condition(UserGroupCondition.class)
                .condition(OnlineJoinLimitCondition.class)
                .condition(OfflineJoinLimitCondition.class)
                .condition(FrequencyCondition.class)
                .action(CouponLimitAction.class)
                .action(BargainAction.class);
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.BARGAIN;
    }

    /**
     * 构建购物车优惠数据
     *
     * @param context 上下文
     * @return 内容
     */
    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) {
        NumLimitRule numLimitRule = ActRespConverter.convert(this.numLimitRule);
        int mutexLimit = actMutexLimit ? BooleanEnum.YES.getValue() : BooleanEnum.NO.getValue();
        Integer frequencyVal = frequency != null ? frequency.getValue() : null;
        PolicyNew policyNew = getPolicyNew(includeGoodsGroups, bargainGoods.getSkuGroupList(), null, false);

        PromotionInfo promotionInfo = buildDefaultPromotionInfo(context);
        promotionInfo.setFrequent(frequencyVal);
        promotionInfo.setTotalLimitNum(actLimitNum);
        promotionInfo.setActivityMutexLimit(mutexLimit);
        promotionInfo.setActivityMutex(actMutexes);
        promotionInfo.setNumLimitRule(numLimitRule);
        promotionInfo.setPolicyNew(policyNew);
        return promotionInfo;
    }

    /**
     * 加购时校验商品参加活动的有效性
     *
     * @param request 商品信息
     * @param goodsHierarchyMap 商品层级关系map
     * @return 不合法的商品列表
     * @throws BizError 业务异常
     */
    @Override
    public List<CheckGoodsItem> checkProductsAct(CheckProductsActRequest request, Map<String, GoodsHierarchy> goodsHierarchyMap) throws BizError {
        List<CheckGoodsItem> invalidGoodsList = new ArrayList<>();
        // 校验商品是否在活动配置中
        checkIncludeGoods(request.getMainGoodsList(), includeGoodsGroups, goodsHierarchyMap, request.getAttachedGoodsList(), bargainGoods.getSkuGroupList(), invalidGoodsList);
        return invalidGoodsList;
    }

    /**
     * 获取产品站信息
     *
     * @param clientId       应用ID
     * @param orgCode        门店Code
     * @param skuPackageList 活动类别
     * @return key: skuPackage val: ProductActInfo 活动信息
     */
    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        if (!checkProductGoodsActCondition(clientId, orgCode)) {
            log.debug("bargain condition is not match. actId:{} clientId:{} orgCode:{}", id, clientId, orgCode);
            return Collections.emptyMap();
        }
        List<String> joinedSkuPackageList = Lists.newArrayList(includeSkuPackages);
        joinedSkuPackageList.retainAll(skuPackageList);
        if (CollectionUtils.isEmpty(joinedSkuPackageList)) {
            log.debug("bargain joinedSkuPackageList retain empty. actId:{} clientId:{} orgCode:{}", id, clientId, orgCode);
            return Collections.emptyMap();
        }
        // 处理赠品组，主要过滤次数不满足的
        List<SkuGroup> skuGroupList = handleSkuGroupList(id, bargainGoods.getSkuGroupList());
        if (CollectionUtils.isEmpty(skuGroupList)) {
            log.debug("bargain skuGroupList is not empty. actId:{} clientId:{} orgCode:{}", id, clientId, orgCode);
            return Collections.emptyMap();
        }
        ProductActGoods bargainGoodsInfo = doConvertActGoods(skuGroupList, includeGoodsGroups, Boolean.FALSE);
        if (bargainGoodsInfo == null) {
            return Collections.emptyMap();
        }

        ProductActInfo productActInfo = new ProductActInfo();
        productActInfo.setId(id);
        productActInfo.setName(name);
        productActInfo.setType(type.getValue());
        productActInfo.setChannels(channels);
        productActInfo.setSelectClientList(selectClientList);
        productActInfo.setSelectOrgList(selectOrgList);
        productActInfo.setUnixStartTime(getUnixStartTime());
        productActInfo.setUnixEndTime(getUnixEndTime());
        productActInfo.setBargainGoods(bargainGoodsInfo);
        return joinedSkuPackageList.stream().collect(Collectors.toMap(skuPackage -> skuPackage, skuPackage -> productActInfo));
    }

    /**
     * 获取活动详情
     *
     * @return 活动详情
     */
    @Override
    public ActivityDetail getActivityDetail() {
        ActivityDetail detail = getBasicActivityDetail();
        detail.setBargainGoods(bargainGoods);
        detail.setIncludeGoodsGroups(includeGoodsGroups);
        return detail;
    }

    /**
     * 获取产品站活动优惠信息
     *
     * @param request   产品站信息
     * @param hierarchy 商品层级关系
     * @param isOrgTool 是否来自门店
     * @return 优惠数据
     */
    @Override
    public PromotionInfo getProductAct(GetProductActRequest request, GoodsHierarchy hierarchy, boolean isOrgTool) throws BizError {
        if (!checkProductActCondition(request, isOrgTool)) {
            log.debug("bargain condition is not match. request:{}", request);
            return null;
        }
        // 处理和过滤sku 组信息
        List<SkuGroup> skuGroupList = handleSkuGroupList(id, bargainGoods.getSkuGroupList());
        if (CollectionUtils.isEmpty(skuGroupList)) {
            log.debug("bargain skuGroupList is not empty. actId:{}", id);
            return null;
        }
        // 构建extend
        ActPromExtend extend = buildNewPromotionExtend(skuGroupList, includeGoodsGroups, bargainGoods.getIgnoreStock());
        extend.setPerNum(false);
    
        // 非忽略库存， 请求库存
        Set<Long> idList = getGroupGoodsIdSet(skuGroupList);
        Long masterId = Long.valueOf(StringUtils.isNotBlank(request.getSku()) ? hierarchy.getGoodsId() : request.getCommodityId());
        Map<Long, Long> stockMap = getGoodsStock(masterId, idList, request, isOrgTool);

        // 构建promotionInfo
        PromotionInfo promotionInfo = getDefaultProductAct();
        promotionInfo.setExtend(GsonUtil.toJson(extend));
        promotionInfo.setQuotaEles(getNewQuotaEleList(includeGoodsGroups));
        promotionInfo.setPolicyNew(getPolicyNew(includeGoodsGroups, skuGroupList, stockMap, true));

        promotionInfo.setPolicys(null);
        return promotionInfo;
    }

    /**
     * bargain goods
     *
     * @return ProductActGoods
     */
    @Override
    public ProductActGoods getAdditionalGoods() {
        // 处理和过滤sku 组信息
        List<SkuGroup> skuGroupList = handleSkuGroupList(id, bargainGoods.getSkuGroupList());
        if (CollectionUtils.isEmpty(skuGroupList)) {
            log.debug("bargain skuGroupList is not empty. actId:{}", id);
            return null;
        }
        return doConvertActGoods(skuGroupList, includeGoodsGroups, Boolean.FALSE);
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof BargainPromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        BargainPromotionConfig promotionConfig = (BargainPromotionConfig) config;
        this.bargainGoods = promotionConfig.getBargainGoods();
        this.includeGoodsGroups = promotionConfig.getIncludeGoodsGroups();
        return true;
    }
}
