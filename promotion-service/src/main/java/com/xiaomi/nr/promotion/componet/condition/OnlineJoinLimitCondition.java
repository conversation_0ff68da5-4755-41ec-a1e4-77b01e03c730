package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MultiPromotionConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 线上活动限制条件
 *
 * <AUTHOR>
 * @date 2021/6/1
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class OnlineJoinLimitCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 活动总数
     */
    private long actLimitNum;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) {
        if (StringUtils.isNotBlank(request.getOrgCode())) {
            return true;
        }
        // 不限制. 0 表示不限制
        if (actLimitNum == 0L) {
            return true;
        }
        // 查询已参与和限制比较
        Long uid = request.getUserId();
        Integer joinedNum = activityRedisDao.getActStatusLimitNum(promotionId);
        // 已参与次数达到限制
        if (joinedNum != null && joinedNum >= actLimitNum) {
            return false;
        }
        return true;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof MultiPromotionConfig)) {
            log.error("config is not instanceof MultiPromotionConfig. config:{}", config);
            return;
        }
        MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.actLimitNum = promotionConfig.getActLimitNum();
    }
}
