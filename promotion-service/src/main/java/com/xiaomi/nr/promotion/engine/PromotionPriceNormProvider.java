package com.xiaomi.nr.promotion.engine;

import com.xiaomi.nr.promotion.api.dto.model.GoodsDto;
import com.xiaomi.nr.promotion.api.dto.model.PromotionPriceDTO;

import java.util.List;
import java.util.Map;

/**
 * 优惠价通用接口
 *
 * <AUTHOR>
 * @date 2023/2/16
 */
public interface PromotionPriceNormProvider {

    /**
     * 检查条件是否满足
     *
     * @param contextParams 包含上下文参数的Map，其中应包含订单时间
     * @return 如果条件满足返回true，否则返回false
     */
    default boolean checkCondition(Map<String, String> contextParams){
        return true;
    }

    /**
     * 获取优惠价格
     *
     * @param goodsList     详情信息
     * @param contextParams 上下文参数
     * @return key: SSU val: 价格信息
     */
    Map<Long, PromotionPriceDTO> getGoodsPromotionPrice(List<GoodsDto> goodsList, Map<String, String> contextParams);
}
