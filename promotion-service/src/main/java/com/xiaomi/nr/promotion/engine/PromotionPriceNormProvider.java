package com.xiaomi.nr.promotion.engine;

import com.xiaomi.nr.promotion.api.dto.model.GoodsDto;
import com.xiaomi.nr.promotion.api.dto.model.PromotionPriceDTO;

import java.util.List;
import java.util.Map;

/**
 * 优惠价通用接口
 *
 * <AUTHOR>
 * @date 2023/2/16
 */
public interface PromotionPriceNormProvider {

    /**
     * 获取优惠价格
     *
     * @param goodsList     详情信息
     * @param contextParams 上下文参数
     * @return key: SSU val: 价格信息
     */
    Map<Long, PromotionPriceDTO> getGoodsPromotionPrice(List<GoodsDto> goodsList, Map<String, String> contextParams);
}
