package com.xiaomi.nr.promotion.domain.coupon.model;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 扣减信息
 *
 * <AUTHOR>
 * @date 2021/4/26
 */
@Data
@NoArgsConstructor
public class DeductedInfo {
    /**
     * 价格:单价
     */
    private Long goodPrice;

    /**
     * 下标
     */
    private DeduceIndex index;
    
    /**
     * 优先级
     */
    private Integer priority;

    public DeductedInfo(Integer index, Long goodPrice) {
        DeduceIndex deduceIndex = new DeduceIndex();
        deduceIndex.setInCarts(index);
        this.goodPrice = goodPrice;
        this.index = deduceIndex;
    }
    
    public void setPriority(boolean canAdjustPrice) {
        if (canAdjustPrice) {
            this.priority = 0;
        } else {
            this.priority = Integer.MAX_VALUE;
        }
    }
}
