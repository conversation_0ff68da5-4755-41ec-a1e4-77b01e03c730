package com.xiaomi.nr.promotion.componet.action;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.*;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.GoodsReduceInfo;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.nr.promotion.tool.PromotionDescRuleTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 下单立减减价
 *
 * <AUTHOR>
 * @date 2022/7/14
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class BuyReduceAction extends AbstractAction {
    private Long promotionId;
    private Map<String, GoodsReduceInfo> buyReduceMap;
    @Autowired
    private CheckoutCartTool checkoutCartTool;
    @Autowired
    private PromotionDescRuleTool promotionDescRuleTool;

    /**
     * 执行减价
     *
     * @param activityTool 优惠工具
     * @param request      请求参数
     * @param context      请求上下文，活动组件间
     * @throws BizError 业务异常
     */
    @Override
    public void execute(ActivityTool activityTool, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (CollectionUtils.isEmpty(context.getGoodIndex())) {
            log.error("buyReduce goodsIndex is empty. actId:{} uid:{}", promotionId, request.getUserId());
            return;
        }

        List<CartItem> cartList = request.getCartList();
        List<GoodsIndex> goodIndex = context.getGoodIndex();
        List<CartItem> fillCartList = CartHelper.getIndexCartList(cartList, goodIndex);

        // 商品改价
        changePrice(fillCartList, buyReduceMap);

        // 处理响应promotionInfo
        setResult(context, activityTool, fillCartList);

        // 初始化资源
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            List<String> skuPackageList = CartHelper.getSkuPackageList(fillCartList).stream().distinct().collect(Collectors.toList());
            initReduceResourceResource(request, promotionId, skuPackageList, context);
        }
    }

    private void changePrice(List<CartItem> fillCartList, Map<String, GoodsReduceInfo> buyReduceMap) {
        for (CartItem item : fillCartList) {
            String skuPackage = CartHelper.getSkuPackage(item);
            GoodsReduceInfo reduceInfo = buyReduceMap.get(skuPackage);
            if (reduceInfo == null) {
                continue;
            }
            // 改价
            doChangeCartPrice(item, reduceInfo);
        }
    }

    private void doChangeCartPrice(CartItem item, GoodsReduceInfo reduceInfo) {
        Long reduceAmount = reduceInfo.getReduceAmount();
        if (reduceAmount <= 0L) {
            log.warn("buyReduce reduceAmount <= 0, actId:{} reduceInfo:{} cart:{} ", promotionId, reduceInfo, item);
            return;
        }
        long curPrice = CartHelper.itemCurPrice(item);
        // 如果超出购物车本身金额，则保留一分钱
        long reduceActual = reduceAmount * item.getCount();
        if (curPrice <= reduceActual) {
            reduceActual = curPrice - PromotionConstant.CART_PRICE_REMAIN;
        }
        // 分摊购物车减免金额
        String idKey = getIdKey(promotionId);
        List<CartItem> fillCartList = Collections.singletonList(item);
        checkoutCartTool.divideCartsReduce(reduceActual, fillCartList, idKey, ActivityTypeEnum.BUY_REDUCE.getValue(), promotionId);
    }

    private void setResult(LocalContext context, ActivityTool tool, List<CartItem> fillCartList) throws BizError {
        // 参与Item
        List<String> itemList = fillCartList.stream().map(CartItem::getItemId).collect(Collectors.toList());
        // 生成拓展信息
        String extend = generateActExpandInfo(fillCartList);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setJoinedItemId(itemList);
        promotionInfo.setParentItemId(itemList);
        promotionInfo.setExtend(extend);
        promotionInfo.setJoined(BooleanEnum.YES.getValue());

        // 设置上下文结果
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    private String generateActExpandInfo(List<CartItem> fillCartList) {
        List<ReduceJoin> joinList = fillCartList.stream().map(this::initReduceJoin)
                .filter(Objects::nonNull).collect(Collectors.toList());

        ReduceExtendInfo reduceExtendInfo = new ReduceExtendInfo();
        reduceExtendInfo.setJoinExtend(joinList);

        PromotionExtend extend = new PromotionExtend();
        extend.setReduceExtend(reduceExtendInfo);
        return GsonUtil.toJson(extend);
    }

    private ReduceJoin initReduceJoin(CartItem item) {
        GoodsReduceInfo reduceInfo = buyReduceMap.get(CartHelper.getSkuPackage(item));
        if (reduceInfo == null) {
            return null;
        }
        String ruleDesc = promotionDescRuleTool.generateBuyReduceItemRule(reduceInfo.getReduceAmount());
        ReduceJoin reduceJoin = new ReduceJoin();
        reduceJoin.setSkuOrPackage(reduceInfo.getSkuPackage());
        reduceJoin.setLevel(reduceInfo.getLevel());
        reduceJoin.setReduceAmount(reduceInfo.getReduceAmount());
        reduceJoin.setJoinCounts(item.getCount());
        reduceJoin.setRuleDesc(ruleDesc);
        reduceJoin.setLimitNum(reduceInfo.getLimitNum());
        return reduceJoin;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof BuyReducePromotionConfig)) {
            return;
        }
        BuyReducePromotionConfig promotionConfig = (BuyReducePromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.buyReduceMap = promotionConfig.getBuyReduceInfoMap();
    }
}