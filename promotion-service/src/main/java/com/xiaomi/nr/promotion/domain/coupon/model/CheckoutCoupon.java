package com.xiaomi.nr.promotion.domain.coupon.model;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 2023/1/10
 */
@Data
public class CheckoutCoupon {


    private String couponCode;

    private Long couponId;

    private String couponName;

    private Long configId;

    private String status;

    private Long startTime;

    private Long endTime;

    private Integer couponType;

    private String couponRangeDesc;

    private String couponRuleDesc;

    /**
     * 劵类型使用渠道描述
     */
    private String useChannelDesc;


    /**
     * 优惠类型
     * 满减: 1->(cash,现金券)
     * 满折: 2 ->(discount,折扣券)
     * 兑换券: 3->(deductible,抵扣券)
     * 立减: 4->(cash,现金券)
     */
    private Integer promotionType;

    private Long promotionValue;

    private Long maxReduce;

    /**
     * 门槛类型
     * 1满元
     * 2满件
     * 3每满元
     * 4每满件
     */
    private Integer bottomType;

    private Integer bottomPrice;

    private Integer bottomCount;

    private Integer postageFree;

    private Integer share;

    /**
     * 优惠券类型 @see CouponTypeEnum
     */
    private Integer type;

    private String useChannel;

    private String sendChannel;

    /**
     * 类型码(老模型)
     * cash-现金劵
     * discount-折扣劵
     * deduct-抵扣劵
     */
    private String typeCode;

    /**
     * 使用区域限制
     */
    private List<String> limitUseRegion;

    /**
     * 结算叠加分组
     */
    private String couponGroupNo;
    
    /**
     * 抵扣规则：1 全部抵扣 2 部分抵扣
     */
    private Integer deductRule;
    
    /**
     * 互斥规则：1 互斥 2 叠加
     */
    private Integer mutualRule;
    
    /**
     * coupon 商品扩展信息
     * key ---> ssuId
     * value ---> CouponItemExtendInfoDO
     */
    private Map<Long, CouponRangeGoodsDO> couponRangeGoodsList;

    /**
     * 同：com.xiaomi.nr.coupon.api.dto.coupon.CouponBaseInfo#serviceScene
     */
    private Integer serviceType;

    /**
     * 漆面修复券使用，抵扣标准面
     */
    private Integer workHourStandardPage;

    //结算信息
    /**
     * 是否可用code码
     */
    private Integer validCode;

    /**
     * 不可用原因
     */
    private String invalidReason;

    /**
     * 不可用原因
     */
    private String invalidData;


    /**
     * 券可用sku
     */
    private List<Long> validSkuList;

    /**
     * 券可用package
     */
    private List<Long> validPackageList;

    /**
     * 券可用的商品
     */
    private List<Long> validGoodsList;
    /**
     * BR单据
     */
    private String budgetApplyNo;

    /**
     * BR行号
     */
    private Long lineNum;

    /**
     * 标签列表
     */
    private List<String> tags;
    /**
     * 结算阶段 1. 定金 2. 尾款
     */
    private Integer checkoutStage;

    /**
     * 单双年度类型：1-单年度，2-双年度
     */
    private Integer annualType;


}
