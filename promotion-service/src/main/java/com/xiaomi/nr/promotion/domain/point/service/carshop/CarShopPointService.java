package com.xiaomi.nr.promotion.domain.point.service.carshop;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.PromotionIdEnum;
import com.xiaomi.nr.promotion.api.dto.enums.PromotionType;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.domain.point.model.PointCheckResult;
import com.xiaomi.nr.promotion.domain.point.model.PointUnusableExt;
import com.xiaomi.nr.promotion.domain.point.model.UserValidBalance;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.model.ReturnType;
import com.xiaomi.nr.promotion.resource.provider.PointProvider;
import com.xiaomi.nr.promotion.rpc.aries.PointServiceProxy;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.aries.model.UnusablePid;
import com.xiaomi.youpin.aries.model.nr.RedpacketCheckResult;
import com.xiaomi.youpin.aries.model.nr.UserValidBalanceResponse;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PointCarShopService
 *
 * <AUTHOR>
 * @date 2024/1/16
 */
@Slf4j
@Service
public class CarShopPointService {

    @Resource
    private PointServiceProxy pointServiceProxy;

    @Resource
    private ResourceProviderFactory resourceProviderFactory;

    /**
     * 积分可抵扣的最小金额（10分钱 = 1角）
     */
    private final static long MIN_DEDUCT_AMOUNT = 10L;


    public void checkoutForPage(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        checkout(request, context);
    }

    public void checkoutForSubmit(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        if(Objects.isNull(request.getPointReduceAmount())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "必要参数不符合要求");
        }

        if (!checkout(request, context)) {
            return;
        }

        loadResource(request, context);
    }

    public void consume(long userId, long orderId, long amount) throws BizError {
        pointServiceProxy.consume(userId, orderId, amount);
    }

    public void rollback(long userId, long orderId) throws BizError {
        pointServiceProxy.rollback(userId, orderId);
    }

    public void refund(long userId, long orderId, long refundNo, long amount) throws BizError {
        pointServiceProxy.refund(userId, orderId, refundNo, amount);
    }

    private void loadResource(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        long reduceAmount = getPointRealReduceTotalAmount(context.getCarts());
        if (reduceAmount <= 0L) {
            return;
        }

        // 加载积分资源
        PointProvider.PointResource pointResource = new PointProvider.PointResource();
        pointResource.setUserId(request.getUserId());
        pointResource.setAmount(reduceAmount);
        pointResource.setRefundType(ReturnType.ROLLBACK.getValue());
        ResourceObject<PointProvider.PointResource> resourceObject = new ResourceObject<>();
        resourceObject.setContent(pointResource);
        resourceObject.setPid(0L);
        resourceObject.setPromotionId(PromotionIdEnum.POINT.getPromotionId());
        resourceObject.setOrderId(request.getOrderId());
        resourceObject.setResourceType(ResourceType.POINT);
        resourceObject.setResourceId(String.format("%s_%s", request.getOrderId(), request.getUserId()));
        ResourceProvider provider = resourceProviderFactory.getProvider(ResourceType.POINT);
        provider.initResource(resourceObject);
        log.debug("===point provider:{}", provider);
        context.getResourceHandlers().add(provider);
    }

    private boolean checkout(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        long userId = request.getUserId();
        List<CartItem> cartList = context.getCarts();
        if (CollectionUtils.isEmpty(cartList)) {
            log.error("carts is empty. userId:{}", userId);
            throw ExceptionHelper.create(ErrCode.ERR_EMPTY_CART, "购物车为空");
        }

        Set<Long> ssuIdList = request.getCartList().stream().filter(e -> !e.getCannotUsePoint() && CartHelper.itemCheckoutAmount(e) > 0).map(CartItem::getSsuId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(ssuIdList)) {
            return false;
        }

        // 获取可用积分余额
        UserValidBalance pointResp = getUserValidBalance(userId, ssuIdList);
        if (CollectionUtils.isEmpty(pointResp.getPointCheckResult().getUsableSsuIds()) || pointResp.getPointCheckResult().getDiscountPrice() < 0L) {
            log.info("point usable ssu isEmpty. uid:{} carts:{}, pointResp:{}", userId, GsonUtil.toJson(cartList), GsonUtil.toJson(pointResp));
            return false;
        }

        // 用户可用总积分数量
        context.setUserValidPointCount(pointResp.getTotalBalance());

        // 不使用积分，不处理下面的计算
        if (Objects.isNull(request.getUsePoint()) || !request.getUsePoint()) {
            return true;
        }

        // 过滤可用积分的购物车条目
        List<GoodsIndex> validGoodsIndexList = fillValidGoodsList(cartList, pointResp.getPointCheckResult().getUsableSsuIds());
        if (CollectionUtils.isEmpty(validGoodsIndexList)) {
            log.info("validGoodsIndexList isEmpty. uid:{} carts:{}, pointResp:{}", userId, GsonUtil.toJson(cartList), GsonUtil.toJson(pointResp));
            return false;
        }

        // 分摊
        long realTotalReducedAmount = sharePointAmount(cartList, validGoodsIndexList, pointResp.getPointCheckResult().getDiscountPrice());

        // 结算页展示给用户的那个扣减金额小于实际可抵扣金额
        if (realTotalReducedAmount < request.getPointReduceAmount()) {
            log.info("point real reduce amount is change, realTotalReducedAmount:{} < requestPointUseTotalAmount:{}", realTotalReducedAmount, request.getPointReduceAmount());
            throw ExceptionHelper.create(ErrCode.POINT_CHECKOUT_REDUCE_AMOUNT_LESS, "积分实际可抵扣金额小于预期抵扣金额");
        }
        return true;
    }

    /**
     * 分摊积分
     *
     * @param cartList                   购物车列表
     * @param goodsIndexList             可用积分的购物车item index列表
     * @param pointCanUseTotalAmount     购物车可用积分总金额（从aries服务获取的金额）
     * @return long 积分实际可抵扣金额（分）
     * @throws BizError .
     */
    public long sharePointAmount(List<CartItem> cartList, List<GoodsIndex> goodsIndexList, long pointCanUseTotalAmount) throws BizError {
        log.info("point reduce amount share start, MIN_DEDUCT_AMOUNT:{}, pointCanUseTotalAmount:{}, carts:{}, goodsIndexList:{}", MIN_DEDUCT_AMOUNT, pointCanUseTotalAmount, GsonUtil.toJson(cartList), GsonUtil.toJson(goodsIndexList));

        // 购物车可参与用积分的总金额（处理过后的购物车总金额）
        long cartTotalAmount = getCartTotalAmount(cartList, goodsIndexList);
        if (cartTotalAmount <= 0L || pointCanUseTotalAmount <= 0L) {
            return 0L;
        }

        // 购物车可参与用积分的总金额（订单总额以角为单位结算，低于1角钱的不能用积分）
        long canUsePointCartTotalAmount = cartTotalAmount / MIN_DEDUCT_AMOUNT * MIN_DEDUCT_AMOUNT;

        if (pointCanUseTotalAmount % MIN_DEDUCT_AMOUNT > 0L) {
            log.error("point amount is err, MIN_DEDUCT_AMOUNT:{}, cartTotalAmount:{}, canUsePointCartTotalAmount:{}, pointCanUseTotalAmount:{}", MIN_DEDUCT_AMOUNT, cartTotalAmount, canUsePointCartTotalAmount, pointCanUseTotalAmount);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "参与积分扣减分摊的金额有误");
        }

        // 计算可抵扣总积分金额
        long canReduceTotalAmount = Math.min(canUsePointCartTotalAmount, pointCanUseTotalAmount);

        // 总的优惠金额（不含剩余可分摊金额）
        long ratioTotalReduceMoneyReal = 0L;

        // 遍历符合条件的item (符合可用积分条件的商品的index（比如过滤商品黑名单、商品是否可用积分）)
        for (GoodsIndex goodsIndex : goodsIndexList) {
            CartItem cartItem = cartList.get(goodsIndex.getIndex());
            long itemCheckoutAmount = CartHelper.itemCheckoutAmount(cartItem);

            // 可抵扣的金额（分）
            long money = (itemCheckoutAmount * canReduceTotalAmount) / cartTotalAmount;
            log.info("###### index:{}, money:{}, itemCheckoutAmount:{} * canReduceTotalAmount:{} / cartTotalAmount:{}", goodsIndex, money, itemCheckoutAmount, canReduceTotalAmount, cartTotalAmount);

            //小于0分钱的跳过
            if (money <= 0L) {
                continue;
            }

            // 计算每个item上总优惠金额（累加积分扣减金额）
            updateReduceDetailList(cartItem, itemCheckoutAmount, money);
            ratioTotalReduceMoneyReal += money;
        }

        // 剩余可分摊金额（没分摊完的部分）
        long diffReduce = canReduceTotalAmount - ratioTotalReduceMoneyReal;
        if (diffReduce > 0L) {
            shareDiffPointAmount(cartList, goodsIndexList, diffReduce);
        }

        // 校验分摊总额是否符合基本要求
        long realTotalReducedAmount = getPointRealReduceTotalAmount(cartList);
        if (realTotalReducedAmount != (ratioTotalReduceMoneyReal + diffReduce) || realTotalReducedAmount < 0L || realTotalReducedAmount != canReduceTotalAmount || realTotalReducedAmount % MIN_DEDUCT_AMOUNT > 0L) {
            log.error("point reduce amount share err, MIN_DEDUCT_AMOUNT:{}, (realTotalReducedAmount:{} = ratioTotalReduceMoneyReal:{} + diffReduce:{}) <= canReduceTotalAmount:{}, carts:{}, goodsIndexList:{}", MIN_DEDUCT_AMOUNT, realTotalReducedAmount, ratioTotalReduceMoneyReal, diffReduce, canReduceTotalAmount, GsonUtil.toJson(cartList), GsonUtil.toJson(goodsIndexList));
            throw ExceptionHelper.create(GeneralCodes.InternalError, "分摊计算出错，积分扣减金额不符合基本要求");
        }

        log.info("point reduce amount share end, MIN_DEDUCT_AMOUNT:{}, (realTotalReducedAmount:{} = ratioTotalReduceMoneyReal:{} + diffReduce:{}) <= canReduceTotalAmount:{}, carts:{}, goodsIndexList:{}", MIN_DEDUCT_AMOUNT, realTotalReducedAmount, ratioTotalReduceMoneyReal, diffReduce, canReduceTotalAmount, GsonUtil.toJson(cartList), GsonUtil.toJson(goodsIndexList));
        return realTotalReducedAmount;
    }

    /**
     * 获取购物车实际积分扣减总金额
     *
     * @param cartList 购物车列表
     * @return long（分）
     */
    private long getPointRealReduceTotalAmount(List<CartItem> cartList) {
        return cartList.stream().mapToLong(e ->
                e.getReduceItemList().stream()
                        .filter(f -> f.getPromotionType().equals(PromotionType.POINT.getValue()))
                        .mapToLong(ReduceDetailItem::getReduce)
                        .sum()
        ).sum();
    }

    /**
     * 分摊剩余的积分抵扣金额
     *
     * @param cartList       购物车列表
     * @param goodsIndexList 可用积分的购物车item index列表
     * @param diffReduce     剩余的积分可分摊金额（没分摊完的部分）
     */
    private void shareDiffPointAmount(List<CartItem> cartList, List<GoodsIndex> goodsIndexList, long diffReduce) throws BizError {
        long remainder = diffReduce;

        // 分摊剩余可抵扣金额（循环一分一分的扣）
        long eachDiff = 1L;
        while (true) {
            boolean updated = false;
            for (GoodsIndex goodsIndex : goodsIndexList) {
                if (remainder <= 0L) {
                    break;
                }

                CartItem cartItem = cartList.get(goodsIndex.getIndex());
                long itemCheckoutAmount = CartHelper.itemCheckoutAmount(cartItem);

                log.info("------ index:{}, itemCheckoutAmount:{}, eachDiff:{}, remainder:{}", goodsIndex, itemCheckoutAmount, eachDiff, remainder);

                if (itemCheckoutAmount <= 0L) {
                    continue;
                }

                // 更新购物车优惠扣减信息（累加积分扣减最小单位金额）
                updateReduceDetailList(cartItem, itemCheckoutAmount, eachDiff);

                // 剩余的可分摊金额
                remainder -= eachDiff;
                updated = true;
            }
            if (!updated) {
                break;
            }
        }

        if (remainder != 0L) {
            log.error("point after loop the diffReduce is greater zero, remainder:{}, diffReduce:{}, carts:{}", remainder, diffReduce, GsonUtil.toJson(cartList));
            throw ExceptionHelper.create(GeneralCodes.InternalError, "分摊计算出错，积分扣减金额无法全部被分摊");
        }
    }


    /**
     * 更新购物车优惠扣减信息
     *
     * @param cartItem 购物车item
     * @param money    优惠扣减金额（分）
     */
    private void updateReduceDetailList(CartItem cartItem, long itemCheckoutAmount, long money) throws BizError {
        if (money <= 0L) {
            return;
        }
        if (itemCheckoutAmount < money) {
            log.error("cart item reduce money calc error, itemCheckoutAmount:{}, reduceAmount:{}, cartItem: {}", itemCheckoutAmount, money, GsonUtil.toJson(cartItem));
            throw ExceptionHelper.create(GeneralCodes.InternalError, "分摊计算出错，商品金额不能低于要扣减的金额");
        }

        cartItem.setReduceAmount(cartItem.getReduceAmount() + money);

        if (CollectionUtils.isEmpty(cartItem.getReduceItemList())) {
            ReduceDetailItem item = new ReduceDetailItem()
                    .setSsuId(cartItem.getSsuId())
                    .setPromotionId(PromotionIdEnum.POINT.getPromotionId())
                    .setPromotionType(PromotionType.POINT.getValue())
                    .setReduce(money);
            cartItem.getReduceItemList().add(item);
            return;
        }

        for (ReduceDetailItem val : cartItem.getReduceItemList()) {
            if (!PromotionIdEnum.POINT.getPromotionId().equals(val.getPromotionId())) {
                continue;
            }
            if (Objects.isNull(val.getPromotionType()) || !val.getPromotionType().equals(PromotionType.POINT.getValue())) {
                continue;
            }
            val.setReduce(val.getReduce() + money);
        }
    }

    /**
     * 过滤出最终可用积分的ssu列表
     *
     * @param cartList      购物车列表
     * @param usableSsuList 积分根据购物车ssu匹配出的可用积分ssu列表
     * @return 取交集后的ssu列表
     */
    private List<GoodsIndex> fillValidGoodsList(List<CartItem> cartList, Set<Long> usableSsuList) {
        List<GoodsIndex> goodsIndexList = new ArrayList<>();
        for (int i = 0; i < cartList.size(); i++) {
            CartItem item = cartList.get(i);
            if (item.getCannotUsePoint() || !usableSsuList.contains(item.getSsuId())) {
                continue;
            }
            GoodsIndex goodsIndex = new GoodsIndex();
            goodsIndex.setIndex(i);
            goodsIndex.setItemId(item.getItemId());
            goodsIndexList.add(goodsIndex);
        }
        return goodsIndexList;
    }

    /**
     * 计算整个购物车实际金额（去掉已优惠金额）
     *
     * @param cartList       购物车列表
     * @param goodsIndexList 可用积分的购物车item index列表
     * @return 购物车总cart_price
     */
    private long getCartTotalAmount(List<CartItem> cartList, List<GoodsIndex> goodsIndexList) {
        long cartTotalAmount = 0L;
        for (GoodsIndex goodsIndex : goodsIndexList) {
            if (goodsIndex.getIndex() >= cartList.size()) {
                log.error("goods index is over carts length.");
                return 0L;
            }
            long itemCheckoutAmount = CartHelper.itemCheckoutAmount(cartList.get(goodsIndex.getIndex()));
            cartTotalAmount += itemCheckoutAmount;
        }
        return cartTotalAmount;
    }

    /**
     * 计算购物车某个item当前实际结算总金额
     *
     * @param item 购物车item
     * @return long
     */
    private long calcCartItemCheckoutAmount(CartItem item) {
        long itemCheckoutAmount = CartHelper.itemCheckoutAmount(item);
        // 低于1角钱的不能用积分
        return itemCheckoutAmount / MIN_DEDUCT_AMOUNT * MIN_DEDUCT_AMOUNT;
    }

    /**
     * 获取用户可用积分总数
     *
     * @param userId    小米ID
     * @param ssuIdList ssuId列表
     * @return UserValidBalanceResponse
     */
    private UserValidBalance getUserValidBalance(long userId, Set<Long> ssuIdList) throws BizError {
        try {
            UserValidBalanceResponse resp = pointServiceProxy.getUserValidBalance(userId, ssuIdList);
            return convertUserValidBalanceResponse(resp);
        } catch (Exception e) {
            throw ExceptionHelper.create(GeneralCodes.InternalError, makeErrorMessage(e), e);
        }
    }

    private String makeErrorMessage(Exception e) {
        if (e instanceof BizError) {
            return ((BizError) e).getMsg();
        } else {
            return "优惠结算出错";
        }
    }

    private UserValidBalance convertUserValidBalanceResponse(UserValidBalanceResponse p) {
        PointCheckResult rc = new PointCheckResult();
        RedpacketCheckResult pc = p.getRedpacketCheckResult();
        rc.setDiscountPrice(pc.getMaxDiscountPirce());
        rc.setUsableSsuIds(pc.getUsablePids());
        rc.setUnUsableSsuIds(convertAriesUnusedExt(pc.getUnusablePids()));

        UserValidBalance r = new UserValidBalance();
        r.setUserId(p.getUserId());
        r.setTotalBalance(p.getTotalBalance());
        r.setPointCheckResult(rc);
        return r;
    }

    private Map<Long, PointUnusableExt> convertAriesUnusedExt(Map<Long, UnusablePid> m) {
        return m.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> PointUnusableExt.builder().invalidReason(e.getValue().getInvalidReason()).build()
                ));
    }

}
