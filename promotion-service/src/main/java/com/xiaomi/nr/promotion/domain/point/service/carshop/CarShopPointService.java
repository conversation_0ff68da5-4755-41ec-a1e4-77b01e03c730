package com.xiaomi.nr.promotion.domain.point.service.carshop;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.PromotionIdEnum;
import com.xiaomi.nr.promotion.api.dto.enums.PromotionType;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.CartItemChild;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.domain.point.model.PointCheckResult;
import com.xiaomi.nr.promotion.domain.point.model.PointUnusableExt;
import com.xiaomi.nr.promotion.domain.point.model.UserValidBalance;
import com.xiaomi.nr.promotion.enums.PointDisabledEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.model.ReturnType;
import com.xiaomi.nr.promotion.resource.provider.PointProvider;
import com.xiaomi.nr.promotion.rpc.aries.PointServiceProxy;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.aries.model.UnusablePid;
import com.xiaomi.youpin.aries.model.nr.RedpacketCheckResult;
import com.xiaomi.youpin.aries.model.nr.UserValidBalanceResponse;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * PointCarShopService
 *
 * <AUTHOR>
 * @date 2024/1/16
 */
@Slf4j
@Service
public class CarShopPointService {

    @Resource
    private PointServiceProxy pointServiceProxy;

    @Resource
    private ResourceProviderFactory resourceProviderFactory;

    /**
     * 积分可抵扣的最小金额（10分钱 = 1角）
     */
    private final static long MIN_DEDUCT_AMOUNT = 10L;


    public void checkoutForPage(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        checkout(request, context);
    }

    public void checkoutForSubmit(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        if (Objects.isNull(request.getPointReduceAmount())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "必要参数不符合要求");
        }

        if (!checkout(request, context)) {
            return;
        }

        loadResource(request, context);
    }

    public void consume(long userId, long orderId, long amount) throws BizError {
        pointServiceProxy.consume(userId, orderId, amount);
    }

    public void rollback(long userId, long orderId) throws BizError {
        pointServiceProxy.rollback(userId, orderId);
    }

    public void refund(long userId, long orderId, long refundNo, long amount) throws BizError {
        pointServiceProxy.refund(userId, orderId, refundNo, amount);
    }

    private void loadResource(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        long reduceAmount = getPointRealReduceTotalAmount(context.getCarts());
        if (reduceAmount <= 0L) {
            return;
        }

        // 加载积分资源
        PointProvider.PointResource pointResource = new PointProvider.PointResource();
        pointResource.setUserId(request.getUserId());
        pointResource.setAmount(reduceAmount);
        pointResource.setRefundType(ReturnType.ROLLBACK.getValue());
        ResourceObject<PointProvider.PointResource> resourceObject = new ResourceObject<>();
        resourceObject.setContent(pointResource);
        resourceObject.setPid(0L);
        resourceObject.setPromotionId(PromotionIdEnum.POINT.getPromotionId());
        resourceObject.setOrderId(request.getOrderId());
        resourceObject.setResourceType(ResourceType.POINT);
        resourceObject.setResourceId(String.format("%s_%s", request.getOrderId(), request.getUserId()));
        ResourceProvider provider = resourceProviderFactory.getProvider(ResourceType.POINT);
        provider.initResource(resourceObject);
        log.debug("===point provider:{}", provider);
        context.getResourceHandlers().add(provider);
    }

    private boolean checkout(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        long userId = request.getUserId();
        List<CartItem> cartList = request.getSourceApi() == SourceApi.SUBMIT ? context.getCarts() : request.getCartList();
        if (CollectionUtils.isEmpty(cartList)) {
            log.error("carts is empty. userId:{}", userId);
            throw ExceptionHelper.create(ErrCode.ERR_EMPTY_CART, "购物车为空");
        }

        Set<Long> ssuIdList = request.getCartList().stream()
                .filter(e -> !SourceEnum.isGiftBargain(e.getSource()))
                .filter(e -> !e.getCannotUsePoint() && CartHelper.itemCheckoutAmount(e) > 0)
                .flatMap(e -> {
                    if (CartHelper.isPackage(e)) {
                        return Stream.concat(e.getChilds().stream().map(CartItemChild::getSsuId), Stream.of(e.getSsuId()));
                    } else {
                        return Stream.of(e.getSsuId());
                    }
                }).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(ssuIdList)) {
            return false;
        }

        // 获取可用积分余额
        UserValidBalance pointResp = getUserValidBalance(userId, ssuIdList);
        Set<Long> usableSsuIds = pointResp.getPointCheckResult().getUsableSsuIds();
        long pointCanUseTotalAmount = pointResp.getPointCheckResult().getDiscountPrice();
        if (CollectionUtils.isEmpty(usableSsuIds) || pointCanUseTotalAmount < 0L) {
            log.info("point usable ssu isEmpty. uid:{} carts:{}, pointResp:{}", userId, GsonUtil.toJson(cartList), GsonUtil.toJson(pointResp));
            return false;
        }

        // 购物车中的有效商品索引
        List<GoodsIndex> validGoodsIndexList = fillValidGoodsList(cartList, usableSsuIds);
        // 购物车中的有效套装子品索引
        Map<Integer, List<GoodsIndex>> subGoodsIndexMap = fillValidSubGoodsList(cartList, usableSsuIds);

        // 用户可用总积分数量
        context.setUserValidPointCount(pointResp.getTotalBalance());
        context.setCanUsePointCount(getCanUsePointCount(cartList, pointCanUseTotalAmount, validGoodsIndexList, subGoodsIndexMap));
        if (pointResp.getTotalBalance() != 0 && context.getCanUsePointCount() == 0) {
            context.setCannotUsePointMsg(PointDisabledEnum.ORDERMONEYLOW.getMsg());
        }

        // 不使用积分，不处理下面的计算
        if (Objects.isNull(request.getUsePoint()) || !request.getUsePoint()) {
            return true;
        }

        if (CollectionUtils.isEmpty(validGoodsIndexList)) {
            log.info("validGoodsIndexList isEmpty. uid:{} carts:{}, pointResp:{}", userId, GsonUtil.toJson(cartList), GsonUtil.toJson(pointResp));
            return false;
        }

        // 分摊
        long realTotalReducedAmount = sharePointAmount(cartList, validGoodsIndexList, pointResp.getPointCheckResult().getDiscountPrice(), subGoodsIndexMap);

        // 结算页展示给用户的那个扣减金额小于实际可抵扣金额
        if (realTotalReducedAmount < request.getPointReduceAmount()) {
            log.info("point real reduce amount is change, realTotalReducedAmount:{} < requestPointUseTotalAmount:{}", realTotalReducedAmount, request.getPointReduceAmount());
            throw ExceptionHelper.create(ErrCode.POINT_CHECKOUT_REDUCE_AMOUNT_LESS, "积分实际可抵扣金额小于预期抵扣金额");
        }
        return true;
    }

    /**
     * 分摊积分
     *
     * @param cartList               购物车列表
     * @param goodsIndexList         可用积分的购物车item index列表
     * @param pointCanUseTotalAmount 购物车可用积分总金额（从aries服务获取的金额）
     * @return long 积分实际可抵扣金额（分）
     * @throws BizError .
     */
    public long sharePointAmount(List<CartItem> cartList, List<GoodsIndex> goodsIndexList, long pointCanUseTotalAmount, Map<Integer, List<GoodsIndex>> subGoodsIndexMap) throws BizError {
        log.info("point reduce amount share start, MIN_DEDUCT_AMOUNT:{}, pointCanUseTotalAmount:{}, carts:{}, goodsIndexList:{}", MIN_DEDUCT_AMOUNT, pointCanUseTotalAmount, GsonUtil.toJson(cartList), GsonUtil.toJson(goodsIndexList));

        // 购物车可参与用积分的总金额（处理过后的购物车总金额）
        long cartTotalAmount = getCartTotalAmount(cartList, goodsIndexList, subGoodsIndexMap);
        if (cartTotalAmount <= 0L || pointCanUseTotalAmount <= 0L) {
            return 0L;
        }

        // 购物车可参与用积分的总金额（订单总额以角为单位结算，低于1角钱的不能用积分）
        long canUsePointCartTotalAmount = cartTotalAmount / MIN_DEDUCT_AMOUNT * MIN_DEDUCT_AMOUNT;

        if (pointCanUseTotalAmount % MIN_DEDUCT_AMOUNT > 0L) {
            log.error("point amount is err, MIN_DEDUCT_AMOUNT:{}, cartTotalAmount:{}, canUsePointCartTotalAmount:{}, pointCanUseTotalAmount:{}", MIN_DEDUCT_AMOUNT, cartTotalAmount, canUsePointCartTotalAmount, pointCanUseTotalAmount);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "参与积分扣减分摊的金额有误");
        }

        // 计算可抵扣总积分金额
        long canReduceTotalAmount = Math.min(canUsePointCartTotalAmount, pointCanUseTotalAmount);

        // 当订单可全部被积分抵扣时，积分减1角（=10分），避免出现0元单
        long allCartAmount = cartList.stream().mapToLong(CartHelper::itemCheckoutAmount).sum();
        if (canReduceTotalAmount >= 10 && canReduceTotalAmount == cartTotalAmount && cartTotalAmount == allCartAmount) {
            canReduceTotalAmount -= 10L;
        }

        // 总的优惠金额（不含剩余可分摊金额）
        long ratioTotalReduceMoneyReal = 0L;

        // 遍历符合条件的item (符合可用积分条件的商品的index（比如过滤商品黑名单、商品是否可用积分）)
        for (GoodsIndex goodsIndex : goodsIndexList) {
            CartItem cartItem = cartList.get(goodsIndex.getIndex());

            long itemCheckoutAmount = CartHelper.itemCheckoutAmount(cartItem);

            if (subGoodsIndexMap.containsKey(goodsIndex.getIndex())) {
                itemCheckoutAmount = 0;
                for (GoodsIndex subGoodsIndex : subGoodsIndexMap.get(goodsIndex.getIndex())) {
                    if (subGoodsIndex.getIndex() >= cartItem.getChilds().size()) {
                        log.warn("subGoods index is over cartItem Children length.");
                        continue;
                    }
                    CartItemChild child = cartItem.getChilds().get(subGoodsIndex.getIndex());
                    long itemChildAmount = CartHelper.itemChildCheckoutAmount(cartItem, child);
                    itemCheckoutAmount += itemChildAmount;
                }
            }

            // 可抵扣的金额（分）
            long money = (itemCheckoutAmount * canReduceTotalAmount) / cartTotalAmount;
            log.info("###### index:{}, money:{}, itemCheckoutAmount:{} * canReduceTotalAmount:{} / cartTotalAmount:{}", goodsIndex, money, itemCheckoutAmount, canReduceTotalAmount, cartTotalAmount);

            //小于0分钱的跳过
            if (money <= 0L) {
                continue;
            }

            if (!CartHelper.isPackage(cartItem)) {
                // 分摊到非套装item 计算每个item上总优惠金额（累加积分扣减金额）
                updateReduceDetailList(cartItem, itemCheckoutAmount, money);
            } else {
                List<GoodsIndex> subGoodsIndexList = subGoodsIndexMap.get(goodsIndex.getIndex());
                // 分摊到套装子品 计算每个子品的优惠金额
                updateChildReduceDetailList(cartItem, subGoodsIndexList, itemCheckoutAmount, money);
            }
            ratioTotalReduceMoneyReal += money;
        }

        // 剩余可分摊金额（没分摊完的部分）
        long diffReduce = canReduceTotalAmount - ratioTotalReduceMoneyReal;
        if (diffReduce > 0L) {
            shareDiffPointAmount(cartList, goodsIndexList, subGoodsIndexMap, diffReduce);
        }

        // 校验分摊总额是否符合基本要求
        long realTotalReducedAmount = getPointRealReduceTotalAmount(cartList);
        if (realTotalReducedAmount != (ratioTotalReduceMoneyReal + diffReduce) || realTotalReducedAmount < 0L || realTotalReducedAmount != canReduceTotalAmount || realTotalReducedAmount % MIN_DEDUCT_AMOUNT > 0L) {
            log.error("point reduce amount share err, MIN_DEDUCT_AMOUNT:{}, (realTotalReducedAmount:{} = ratioTotalReduceMoneyReal:{} + diffReduce:{}) <= canReduceTotalAmount:{}, carts:{}, goodsIndexList:{}", MIN_DEDUCT_AMOUNT, realTotalReducedAmount, ratioTotalReduceMoneyReal, diffReduce, canReduceTotalAmount, GsonUtil.toJson(cartList), GsonUtil.toJson(goodsIndexList));
            throw ExceptionHelper.create(GeneralCodes.InternalError, "分摊计算出错，积分扣减金额不符合基本要求");
        }

        log.info("point reduce amount share end, MIN_DEDUCT_AMOUNT:{}, (realTotalReducedAmount:{} = ratioTotalReduceMoneyReal:{} + diffReduce:{}) <= canReduceTotalAmount:{}, carts:{}, goodsIndexList:{}", MIN_DEDUCT_AMOUNT, realTotalReducedAmount, ratioTotalReduceMoneyReal, diffReduce, canReduceTotalAmount, GsonUtil.toJson(cartList), GsonUtil.toJson(goodsIndexList));
        return realTotalReducedAmount;
    }

    /**
     * 获取购物车实际积分扣减总金额
     *
     * @param cartList 购物车列表
     * @return long（分）
     */
    private long getPointRealReduceTotalAmount(List<CartItem> cartList) {
        return cartList.stream().mapToLong(e ->
                e.getReduceItemList().stream()
                        .filter(f -> f.getPromotionType().equals(PromotionType.POINT.getValue()))
                        .mapToLong(ReduceDetailItem::getReduce)
                        .sum()
        ).sum();
    }

    /**
     * 分摊剩余的积分抵扣金额
     *
     * @param cartList       购物车列表
     * @param goodsIndexList 可用积分的购物车item index列表
     * @param diffReduce     剩余的积分可分摊金额（没分摊完的部分）
     */
    private void shareDiffPointAmount(List<CartItem> cartList, List<GoodsIndex> goodsIndexList, Map<Integer, List<GoodsIndex>> subGoodsIndexMap, long diffReduce) throws BizError {
        long remainder = diffReduce;

        // 分摊剩余可抵扣金额（循环一分一分的扣）
        long eachDiff = 1L;
        while (true) {
            boolean updated = false;
            for (GoodsIndex goodsIndex : goodsIndexList) {
                if (remainder <= 0L) {
                    break;
                }

                CartItem cartItem = cartList.get(goodsIndex.getIndex());
                long itemCheckoutAmount = CartHelper.itemCheckoutAmount(cartItem);

                log.info("------ index:{}, itemCheckoutAmount:{}, eachDiff:{}, remainder:{}", goodsIndex, itemCheckoutAmount, eachDiff, remainder);

                if (itemCheckoutAmount <= 0L) {
                    continue;
                }

                if (!CartHelper.isPackage(cartItem)) {
                    // 更新购物车优惠扣减信息（累加积分扣减最小单位金额）
                    updateReduceDetailList(cartItem, itemCheckoutAmount, eachDiff);
                } else {
                    updateChildReduceDetailList(cartItem, subGoodsIndexMap.get(goodsIndex.getIndex()), itemCheckoutAmount, eachDiff);
                }


                // 剩余的可分摊金额
                remainder -= eachDiff;
                updated = true;
            }
            if (!updated) {
                break;
            }
        }

        if (remainder != 0L) {
            log.error("point after loop the diffReduce is greater zero, remainder:{}, diffReduce:{}, carts:{}", remainder, diffReduce, GsonUtil.toJson(cartList));
            throw ExceptionHelper.create(GeneralCodes.InternalError, "分摊计算出错，积分扣减金额无法全部被分摊");
        }
    }


    /**
     * 更新购物车优惠扣减信息
     *
     * @param cartItem 购物车item
     * @param itemCheckoutAmount 购物车item价格
     * @param money    优惠扣减金额（分）
     */
    private void updateReduceDetailList(CartItem cartItem, long itemCheckoutAmount, long money) throws BizError {
        if (money <= 0L) {
            return;
        }
        if (itemCheckoutAmount < money) {
            log.error("cart item reduce money calc error, itemCheckoutAmount:{}, reduceAmount:{}, cartItem: {}", itemCheckoutAmount, money, GsonUtil.toJson(cartItem));
            throw ExceptionHelper.create(GeneralCodes.InternalError, "分摊计算出错，商品金额不能低于要扣减的金额");
        }

        cartItem.setReduceAmount(cartItem.getReduceAmount() + money);

        updateCarItemReduceItemList(cartItem, null, money);

    }


    /**
     * 更新子项的扣减详情列表
     *
     * @param subGoodsIndexList  可用的SSU索引列表
     * @param itemCheckoutAmount 商品结算金额
     * @param money              要扣减的金额
     * @param cartItem           购物车项
     * @throws BizError 业务错误，当商品金额低于要扣减的金额时抛出
     */
    private void updateChildReduceDetailList(CartItem cartItem, List<GoodsIndex> subGoodsIndexList, long itemCheckoutAmount, long money) throws BizError {
        if (itemCheckoutAmount < money) {
            log.error("cart item reduce money calc error, itemCheckoutAmount:{}, reduceAmount:{}, cartItem: {}", itemCheckoutAmount, money, GsonUtil.toJson(cartItem));
            throw ExceptionHelper.create(GeneralCodes.InternalError, "分摊计算出错，商品金额不能低于要扣减的金额");
        }

        // 子商品按价格从大到小排序
        List<CartItemChild> childList = subGoodsIndexList.stream()
                .map(GoodsIndex::getIndex)
                .filter(index -> index < cartItem.getChilds().size())
                .map(cartItem.getChilds()::get)
                .sorted(Comparator.comparing(childItem -> CartHelper.itemChildCheckoutAmount(cartItem, (CartItemChild) childItem)).reversed())
                .toList();

        // 套装分摊积分后的整体价格
        long itemAmountAfterReduce = itemCheckoutAmount - money;

        long ratioTotalReduceMoney = 0;
        // 价格较高的前n-1个子品先分摊
        for (int i = 0; i < childList.size() - 1; i++) {
            CartItemChild child = childList.get(i);
            long itemChildAmount = CartHelper.itemChildCheckoutAmount(cartItem, child);
            // 子商品分摊积分后的售价
            long priceAfterReduce = (itemChildAmount * itemAmountAfterReduce) / itemCheckoutAmount;
            // 子商品实际分摊积分金额
            long subMoney = itemChildAmount - priceAfterReduce;

            // 更新子商品优惠活动信息
            updateCarItemReduceItemList(cartItem, child, subMoney);
            // 记录当前已分摊积分金额总数
            ratioTotalReduceMoney += subMoney;
        }

        // 价格最低的子品分摊剩余所有积分金额
        long lastSubMoney = money - ratioTotalReduceMoney;
        if (lastSubMoney > 0) {
            CartItemChild lastChild = childList.getLast();
            updateCarItemReduceItemList(cartItem, lastChild, lastSubMoney);
        }

        // lastSubMoney < 0 说明前n-1项多分摊了 此时最后一项（价格最低的子品）不在进行分摊，同时将前n-1项多分摊的积分金额全部补偿到价格最高的子品上
        if (lastSubMoney < 0) {
            CartItemChild firstChild = childList.getFirst();
            ReduceDetailItem reduceDetailItem = Optional.ofNullable(cartItem.getReduceItemList()).orElse(new ArrayList<>())
                    .stream().filter(x -> Objects.equals(x.getSsuId(), firstChild.getSsuId())
                            && Objects.nonNull(x.getPromotionId())
                            && Objects.nonNull(x.getPromotionType())
                            && Objects.equals(PromotionType.POINT.getValue(), x.getPromotionType()))
                    .findAny().orElse(null);

            if (Objects.nonNull(reduceDetailItem)) {
                // 将多分摊的积分金额从价格最高的子品上扣减，已保证总的分摊积分金额正确
                reduceDetailItem.setReduce(reduceDetailItem.getReduce() + lastSubMoney);
            } else {
                log.error("CarShopPointService.updateChildReduceDetailList error. The maximum priced child reduceDetailItem is null. cartItem: {}, child:{}, reduce: {}", GsonUtil.toJson(cartItem), GsonUtil.toJson(firstChild), lastSubMoney);
                throw ExceptionHelper.create(GeneralCodes.InternalError, "积分分摊计算出错");
            }
        }

        cartItem.setReduceAmount(cartItem.getReduceAmount() + money);
    }

    /**
     * 更新购物车项优惠信息列表（更新优惠价格）
     * child为空时，  更新非套装商品优惠价格
     * child不为空时，更新套装子商品优惠价格
     *
     * @param cartItem 套装商品
     * @param child    套装子商品
     * @param reduce   优惠金额
     */
    private void updateCarItemReduceItemList(CartItem cartItem, CartItemChild child, long reduce) throws BizError {
        if (Objects.isNull(cartItem) || reduce < 0L) {
            log.error("CarShopPointService.updateCarItemReduceItemList params error. cartItem: {}, child:{}, reduce: {}", GsonUtil.toJson(cartItem), GsonUtil.toJson(child), reduce);
            throw ExceptionHelper.create(GeneralCodes.ParamError, "子品分摊金额不能小于零");
        }

        if (reduce == 0L) {
            return;
        }

        if (Objects.nonNull(child) && Objects.nonNull(child.getSellPrice()) && CartHelper.itemChildCheckoutAmount(cartItem, child) < reduce) {
            log.error("CarShopPointService.updateCarItemReduceItemList error. The discount amount cannot be greater than the price of cart item child. cartItem: {}, child:{}, reduce: {}", GsonUtil.toJson(cartItem), GsonUtil.toJson(child), reduce);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "优惠金额不能大于子商品的价格");
        }

        long ssuId = Objects.isNull(child) ? cartItem.getSsuId() : child.getSsuId();

        ReduceDetailItem reduceDetailItem = Optional.ofNullable(cartItem.getReduceItemList()).orElse(new ArrayList<>())
                .stream().filter(x -> Objects.equals(x.getSsuId(), ssuId)
                        && Objects.nonNull(x.getPromotionId())
                        && Objects.nonNull(x.getPromotionType())
                        && Objects.equals(PromotionType.POINT.getValue(), x.getPromotionType()))
                .findAny().orElse(null);

        if (Objects.isNull(reduceDetailItem)) {
            ReduceDetailItem item = new ReduceDetailItem()
                    .setSsuId(ssuId)
                    .setPromotionId(PromotionIdEnum.POINT.getPromotionId())
                    .setPromotionType(PromotionType.POINT.getValue())
                    .setReduce(reduce);
            List<ReduceDetailItem> reduceDetailItems = Optional.ofNullable(cartItem.getReduceItemList()).orElse(new ArrayList<>());
            reduceDetailItems.add(item);
            cartItem.setReduceItemList(reduceDetailItems);
        } else {
            reduceDetailItem.setReduce(reduceDetailItem.getReduce() + reduce);
        }
    }

    /**
     * 过滤出最终可用积分的ssu列表
     *
     * @param cartList      购物车列表
     * @param usableSsuList 积分根据购物车ssu匹配出的可用积分ssu列表
     * @return 取交集后的ssu列表
     */
    public List<GoodsIndex> fillValidGoodsList(List<CartItem> cartList, Set<Long> usableSsuList) {
        List<GoodsIndex> goodsIndexList = new ArrayList<>();
        for (int i = 0; i < cartList.size(); i++) {
            CartItem item = cartList.get(i);
            if (item.getCannotUsePoint() || !usableSsuList.contains(item.getSsuId())) {
                continue;
            }
            GoodsIndex goodsIndex = new GoodsIndex();
            goodsIndex.setIndex(i);
            goodsIndex.setItemId(item.getItemId());
            goodsIndexList.add(goodsIndex);
        }
        return goodsIndexList;
    }

    /**
     * 过滤出最终可用积分的子商品列表最终可用积分的子商品列表 key：商品索引， value：可用积分子商品索引列表
     *
     * @param cartList      购物车列表
     * @param usableSsuList 可用积分ssu列表
     * @return 取交集后的子商品列表
     */
    public Map<Integer, List<GoodsIndex>> fillValidSubGoodsList(List<CartItem> cartList, Set<Long> usableSsuList) {
        Map<Integer, List<GoodsIndex>> goodsIndexMap = new HashMap<>();
        for (int i = 0; i < cartList.size(); i++) {
            CartItem item = cartList.get(i);
            if (item.getCannotUsePoint() || !CartHelper.isPackage(item) || !usableSsuList.contains(item.getSsuId())) {
                continue;
            }
            List<GoodsIndex> subGoodsIndexList = new ArrayList<>();
            for (int j = 0; j < item.getChilds().size(); j++) {
                CartItemChild child = item.getChilds().get(j);
                if (!usableSsuList.contains(child.getSsuId())) {
                    continue;
                }
                GoodsIndex subGoodsIndex = new GoodsIndex();
                subGoodsIndex.setIndex(j);
                subGoodsIndex.setItemId(String.valueOf(child.getSsuId()));
                subGoodsIndexList.add(subGoodsIndex);
            }
            goodsIndexMap.put(i, subGoodsIndexList);
        }
        return goodsIndexMap;
    }

    /**
     * 计算整个购物车实际金额（去掉已优惠金额）
     *
     * @param cartList       购物车列表
     * @param goodsIndexList 可用积分的购物车item index列表
     * @return 购物车总cart_price
     */
    private long getCartTotalAmount(List<CartItem> cartList, List<GoodsIndex> goodsIndexList, Map<Integer, List<GoodsIndex>> subGoodsIndexMap) {
        long cartTotalAmount = 0L;
        for (GoodsIndex goodsIndex : goodsIndexList) {
            if (goodsIndex.getIndex() >= cartList.size()) {
                log.error("goods index is over carts length.");
                return 0L;
            }
            CartItem cartItem = cartList.get(goodsIndex.getIndex());
            if (!CartHelper.isPackage(cartItem)) {
                long itemCheckoutAmount = CartHelper.itemCheckoutAmount(cartItem);
                cartTotalAmount += itemCheckoutAmount;
            } else {
                for (GoodsIndex subGoodsIndex : subGoodsIndexMap.get(goodsIndex.getIndex())) {
                    if (subGoodsIndex.getIndex() >= cartItem.getChilds().size()) {
                        log.error("subGoods index is over cartItem Children length.");
                        return 0L;
                    }
                    CartItemChild cartItemChild = cartItem.getChilds().get(subGoodsIndex.getIndex());
                    long itemCheckoutAmount = CartHelper.itemChildCheckoutAmount(cartItem, cartItemChild);
                    cartTotalAmount += itemCheckoutAmount;
                }
            }
        }
        return cartTotalAmount;
    }

    /**
     * 计算购物车某个item当前实际结算总金额
     *
     * @param item 购物车item
     * @return long
     */
    private long calcCartItemCheckoutAmount(CartItem item) {
        long itemCheckoutAmount = CartHelper.itemCheckoutAmount(item);
        // 低于1角钱的不能用积分
        return itemCheckoutAmount / MIN_DEDUCT_AMOUNT * MIN_DEDUCT_AMOUNT;
    }

    /**
     * 获取用户可用积分总数
     *
     * @param userId    小米ID
     * @param ssuIdList ssuId列表
     * @return UserValidBalanceResponse
     */
    private UserValidBalance getUserValidBalance(long userId, Set<Long> ssuIdList) throws BizError {
        try {
            UserValidBalanceResponse resp = pointServiceProxy.getUserValidBalance(userId, ssuIdList);
            return convertUserValidBalanceResponse(resp);
        } catch (Exception e) {
            throw ExceptionHelper.create(GeneralCodes.InternalError, makeErrorMessage(e), e);
        }
    }

    private String makeErrorMessage(Exception e) {
        if (e instanceof BizError) {
            return ((BizError) e).getMsg();
        } else {
            return "优惠结算出错";
        }
    }

    private UserValidBalance convertUserValidBalanceResponse(UserValidBalanceResponse p) {
        PointCheckResult rc = new PointCheckResult();
        RedpacketCheckResult pc = p.getRedpacketCheckResult();
        rc.setDiscountPrice(pc.getMaxDiscountPirce());
        rc.setUsableSsuIds(pc.getUsablePids());
        rc.setUnUsableSsuIds(convertAriesUnusedExt(pc.getUnusablePids()));

        UserValidBalance r = new UserValidBalance();
        r.setUserId(p.getUserId());
        r.setTotalBalance(p.getTotalBalance());
        r.setPointCheckResult(rc);
        return r;
    }

    private Map<Long, PointUnusableExt> convertAriesUnusedExt(Map<Long, UnusablePid> m) {
        return m.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> PointUnusableExt.builder().invalidReason(e.getValue().getInvalidReason()).build()
                ));
    }

    /**
     * 获取该订单用户可用积分总数量
     *
     * @param cartList               购物车列表
     * @param pointCanUseTotalAmount 用户最大积分抵扣金额
     * @param validGoodsIndexList    可参与积分的商品列表
     * @param subGoodsIndexMap       可参与积分的套装子商品列表
     * @return 用户可使用积分数量
     */
    public long getCanUsePointCount(List<CartItem> cartList, long pointCanUseTotalAmount, List<GoodsIndex> validGoodsIndexList, Map<Integer, List<GoodsIndex>> subGoodsIndexMap) throws BizError {
        if (CollectionUtils.isEmpty(validGoodsIndexList)) {
            log.info("CarShopPointService getCanUsePointCount validGoodsIndexList isEmpty  carts:{}", GsonUtil.toJson(cartList));
            return 0L;
        }
        // 可参与积分的购物车商品的总金额
        long cartTotalAmount = getCartTotalAmount(cartList, validGoodsIndexList, subGoodsIndexMap);
        if (cartTotalAmount <= 0L || pointCanUseTotalAmount <= 0L) {
            return 0L;
        }

        // 可参与积分的购物车商品的总金额能使用的最大积分金额（截取到角，订单总额以角为单位结算，低于1角钱的不能用积分）
        long canUsePointCartTotalAmount = cartTotalAmount / MIN_DEDUCT_AMOUNT * MIN_DEDUCT_AMOUNT;

        if (pointCanUseTotalAmount % MIN_DEDUCT_AMOUNT > 0L) {
            log.error("point amount is err, MIN_DEDUCT_AMOUNT:{}, cartTotalAmount:{}, canUsePointCartTotalAmount:{}, pointCanUseTotalAmount:{}", MIN_DEDUCT_AMOUNT, cartTotalAmount, canUsePointCartTotalAmount, pointCanUseTotalAmount);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "参与积分扣减分摊的金额有误");
        }

        // 计算可抵扣总积分金额
        long canReduceTotalAmount = Math.min(canUsePointCartTotalAmount, pointCanUseTotalAmount);

        // 当订单可全部被积分抵扣时，积分减1角（=10分），避免出现0元单
        long allCartAmount = cartList.stream().mapToLong(CartHelper::itemCheckoutAmount).sum();
        if (canReduceTotalAmount >= 10 && canReduceTotalAmount == cartTotalAmount && cartTotalAmount == allCartAmount) {
            canReduceTotalAmount -= 10L;
        }
        return canReduceTotalAmount / 10;
    }

}
