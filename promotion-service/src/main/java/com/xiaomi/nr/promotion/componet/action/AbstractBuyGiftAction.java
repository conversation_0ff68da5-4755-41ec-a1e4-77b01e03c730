package com.xiaomi.nr.promotion.componet.action;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.entity.redis.GiftBargainGroup;
import com.xiaomi.nr.promotion.entity.redis.Goods;
import com.xiaomi.nr.promotion.entity.redis.SkuGroup;
import com.xiaomi.nr.promotion.enums.BooleanV2Enum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.model.common.GiftBargainBean;
import com.xiaomi.nr.promotion.model.common.GoodsIndexNew;
import com.xiaomi.nr.promotion.model.common.GroupCurCount;
import com.xiaomi.nr.promotion.model.common.PromotionExtend;
import com.xiaomi.nr.promotion.model.common.SkuGroupBean;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/6/18
 */
@Slf4j
public abstract class AbstractBuyGiftAction extends AbstractAction {
    
    /**
     * 促销活动ID
     */
    protected Long promotionId;
    /**
     * 赠品信息
     */
    protected Goods giftGoods;
    
    protected abstract GiftBargainGroup getGiftGoods(CartItem cartItem);
    
    
    protected abstract GiftBargainBean convert(GiftBargainGroup item, Integer maxCount, Long groupId);
    
    /**
     * 获取组赠品信息（信息列表）
     *
     * @param cartList     购物车信息
     * @param indexNewList 索引信息列表
     * @return 组赠品信息
     */
    protected Map<String, GiftBargainGroup> doGiftMatch(List<CartItem> cartList, List<GoodsIndexNew> indexNewList) {
        if (CollectionUtils.isEmpty(indexNewList)) {
            return Collections.emptyMap();
        }
        
        Map<String, GiftBargainGroup> groupMap = Maps.newHashMap();
        indexNewList.forEach(indexNew -> {
            CartItem item = CartHelper.getCartItem(cartList, indexNew);
            if (item == null) {
                return;
            }
            GiftBargainGroup giftInfo = getGiftGoods(item);
            if (giftInfo == null) {
                item.setItemId(CART_DEL_FLAG);
                indexNew.setItemId(CART_DEL_FLAG);
                return;
            }
            groupMap.put(indexNew.getItemId(), giftInfo);
        });
        return groupMap;
    }
    
    
    /**
     * 对赠品进行分组
     *
     * @param cartList 购物车列表
     * @param groupMap 赠品信息组
     * @return 赠品分组信息 key: groupId val: 赠品组信息
     */
    protected Map<Long, List<CartItem>> groupByGroupId(List<CartItem> cartList, Map<String, GiftBargainGroup> groupMap) {
        if (CollectionUtils.isEmpty(cartList)) {
            return Collections.emptyMap();
        }
        Map<Long, List<CartItem>> cartGroupMap = new HashMap<>(groupMap.size());
        for (CartItem item : cartList) {
            if (!SourceEnum.isGift(item.getSource())) {
                continue;
            }
            if (groupMap.get(item.getItemId()) == null) {
                continue;
            }
            cartGroupMap.merge(item.getGroupId(), Lists.newArrayList(item), (list1, list2) -> {
                list1.addAll(list2);
                return list1;
            });
        }
        return cartGroupMap;
    }
    
    /**
     * 处理每组赠品组信息
     *
     * @param cartItemGroup 购物车赠品商品组
     * @param groupMap      赠品信息
     * @param maxCount      服务
     */
    protected void checkAndReduceGroupCount(List<CartItem> cartItemGroup, Map<String, GiftBargainGroup> groupMap, Integer maxCount) {
        if (CollectionUtils.isEmpty(cartItemGroup)) {
            return;
        }
        
        long curCount = 0L;
        for (CartItem item : cartItemGroup) {
            if (CART_DEL_FLAG.equals(item.getItemId())) {
                continue;
            }
            if (curCount >= maxCount) {
                item.setItemId(CART_DEL_FLAG);
                continue;
            }
            GiftBargainGroup giftBargainGroup = groupMap.get(item.getItemId());
            if (giftBargainGroup == null) {
                item.setItemId(CART_DEL_FLAG);
                continue;
            }
            // 检查次数
            long count = (item.getCount() / giftBargainGroup.getGiftBaseNum());
            if (curCount + count > maxCount) {
                count = maxCount - curCount;
                item.setCount((int) (count * giftBargainGroup.getGiftBaseNum()));
            }
            curCount += count;
            item.setCartPrice(giftBargainGroup.getCartPrice());
        }
    }
    
    protected Map<Long, Integer> getCountMap(Map<Long, List<CartItem>> cartGroup, Map<String, GiftBargainGroup> groupMap) {
        Map<Long, Integer> countMap = Maps.newHashMap();
        for (Map.Entry<Long, List<CartItem>> entry : cartGroup.entrySet()) {
            Long groupId = entry.getKey();
            List<CartItem> itemList = entry.getValue();
            if (CollectionUtils.isEmpty(itemList)) {
                countMap.put(groupId, 0);
            }
            long count = itemList.stream()
                    .filter(item -> !Objects.equals(item.getItemId(), CART_DEL_FLAG))
                    .filter(item -> Objects.nonNull(groupMap.get(item.getItemId())))
                    .mapToInt(item -> (int) (item.getCount() / groupMap.get(item.getItemId()).getGiftBaseNum()))
                    .sum();
            countMap.put(groupId, (int) count);
        }
        return countMap;
    }
    
    protected String generateExtendInfo(Integer maxCount, Integer curCount, List<GroupCurCount> countList) {
        if (giftGoods == null || CollectionUtils.isEmpty(giftGoods.getSkuGroupList())) {
            log.error("giftGoods is empty. promotionId:{}", promotionId);
            return null;
        }
        List<SkuGroup> skuGroupsList = giftGoods.getSkuGroupList();
        List<GiftBargainGroup> listInfo = skuGroupsList.get(0).getListInfo();
        if (CollectionUtils.isEmpty(listInfo)) {
            return null;
        }
        List<SkuGroupBean> skuGroupBeanList = checkAndConvertGroup(skuGroupsList, maxCount);
        
        // 获取skulist和list,兼容旧逻辑
        Map<String, String> skuMap = listInfo.stream().map(GiftBargainGroup::getSku).map(String::valueOf)
                .collect(Collectors.toMap(sku -> sku, sku -> sku, (val1, val2) -> val2));
        List<String> skuList = new ArrayList<>(skuMap.keySet());
        
        // 返回活动信息,对应promotion结构里的extend字段
        PromotionExtend expandInfo = new PromotionExtend();
        expandInfo.setCartPrice(listInfo.get(0).getCartPrice());
        expandInfo.setMaxCount(maxCount);
        expandInfo.setCurCount(curCount);
        expandInfo.setGroupCurCount(countList);
        expandInfo.setList(skuMap);
        expandInfo.setSkuList(skuList);
        expandInfo.setSelectType(giftGoods.getSelectType());
        expandInfo.setIgnoreStock(giftGoods.getIgnoreStock());
        expandInfo.setRefundValue(listInfo.get(0).getRefundValue());
        expandInfo.setAccessCode("");
        expandInfo.setSkuGroupsList(skuGroupBeanList);
        return GsonUtil.toJson(expandInfo);
    }
    
    protected List<SkuGroupBean> checkAndConvertGroup(List<SkuGroup> skuGroupsList, Integer maxCount) {
        if (CollectionUtils.isEmpty(skuGroupsList)) {
            return Collections.emptyList();
        }
        List<SkuGroupBean> skuGroupBeanList = Lists.newArrayList();
        for (SkuGroup skuGroup : skuGroupsList) {
            Long groupId = skuGroup.getGroupId();
            List<GiftBargainGroup> listInfo = Optional.ofNullable(skuGroup.getListInfo()).orElse(Collections.emptyList());
            List<GiftBargainBean> giftBeanList = listInfo.stream().map(item -> convert(item, maxCount, groupId)).collect(Collectors.toList());
            boolean noneMatch = giftBeanList.stream().noneMatch(bean -> Objects.equals(BooleanV2Enum.YES.getValue(), bean.getActNumLimit()));
            if (noneMatch) {
                continue;
            }
            SkuGroupBean groupBean = new SkuGroupBean();
            groupBean.setGroupId(groupId);
            groupBean.setListInfo(giftBeanList);
            skuGroupBeanList.add(groupBean);
        }
        return skuGroupBeanList;
    }
    
}
