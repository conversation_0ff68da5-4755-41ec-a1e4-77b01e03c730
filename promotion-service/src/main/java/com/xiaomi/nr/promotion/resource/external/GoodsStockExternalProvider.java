package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.goods.gis.dto.stock.GiftStockRespParam;
import com.xiaomi.goods.gis.dto.stock.Region;
import com.xiaomi.nr.promotion.activity.pool.ActSearchParam;
import com.xiaomi.nr.promotion.activity.pool.CarShopActivitySearcher;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.MultiProductGoodsActRequest;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.model.MultiGoodItem;
import com.xiaomi.nr.promotion.api.dto.model.OrderCartItem;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.redis.GiftBargainGroup;
import com.xiaomi.nr.promotion.entity.redis.SkuGroup;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyGiftPromotionConfig;
import com.xiaomi.nr.promotion.rpc.gis.GoodsStockServiceProxy;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024/8/14
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class GoodsStockExternalProvider extends ExternalDataProvider<Map<String, GiftStockRespParam>> {


    private static final String CAR_SHOP_GOODS_STOCK_CHANNEL = "car_shop";

    @Resource
    private CarShopActivitySearcher carShopActivitySearcher;

    @Resource
    private GoodsStockServiceProxy goodsStockServiceProxy;

    private ListenableFuture<Map<String, GiftStockRespParam>> future;

    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        if (!Objects.equals(request.getChannel(), ChannelEnum.CAR_SHOP.getValue())) {
            return;
        }

        this.future = this.doPrepareAsync(request);
    }

    @Override
    public void doProductDetailPrepare(MultiProductGoodsActRequest request) {
        if (!Objects.equals(request.getChannel(), ChannelEnum.CAR_SHOP.getValue())) {
            return;
        }
        this.future = this.getCarShopProductStockAsync(request);
    }

    @Override
    protected long getTimeoutMills() {
        return DEFAULT_TIMEOUT;
    }

    @Override
    public ListenableFuture<Map<String, GiftStockRespParam>> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.GIFT_GOODS_STOCK;
    }

    private ListenableFuture<Map<String, GiftStockRespParam>> doPrepareAsync(CheckoutPromotionRequest request) {
        List<ActSearchParam.GoodsInSearch> goodsInSearchList = carShopActivitySearcher.createSearchGoods(request.getCartList());
        ActSearchParam param = new ActSearchParam()
                .setChannel(request.getChannel())
                .setGoodsList(goodsInSearchList);
        List<ActivityTool> activityTools = carShopActivitySearcher.searchCarActivity(param);

        // slave id set
        Set<Long> slaveIdSet = new HashSet<>();
        for (ActivityTool tool : activityTools) {
            if (!Objects.equals(tool.getType(), PromotionToolType.BUY_GIFT) || !Objects.equals(tool.getBizPlatform(),
                    BizPlatformEnum.CAR_SHOP)) {
                continue;
            }
            BuyGiftPromotionConfig promotionConfig = (BuyGiftPromotionConfig) tool.getPromotionConfig();
            for (SkuGroup skuGroup : promotionConfig.getGiftGoods().getSkuGroupList()) {
                for (GiftBargainGroup gift : skuGroup.getListInfo()) {
                    slaveIdSet.add(gift.getSsuId());
                }
            }
        }

        // master id set
        //Set<Long> masterIdSet = request.getCartList().stream().map(OrderCartItem::getSsuId).collect(Collectors.toSet());

        return goodsStockServiceProxy.getGiftStockBatchAsync(goodsInSearchList, new ArrayList<>(slaveIdSet),
                this.convertRegion(request.getRegion()), CAR_SHOP_GOODS_STOCK_CHANNEL, false);
    }

    private ListenableFuture<Map<String, GiftStockRespParam>> getCarShopProductStockAsync(MultiProductGoodsActRequest request) {
        List<ActSearchParam.GoodsInSearch> resultList = new ArrayList<>();
        request.getGoodsList().forEach(good ->{
            ActSearchParam.GoodsInSearch search = new ActSearchParam.GoodsInSearch();
            search.setSsuId(good.getSsuId());
            search.setSsuType(good.getSsuType());
            resultList.add(search);
        });
        ActSearchParam param = new ActSearchParam()
                .setChannel(request.getChannel())
                .setGoodsList(resultList);
        List<ActivityTool> activityTools = carShopActivitySearcher.searchCarActivity(param);

        // slave id set
        Set<Long> slaveIdSet = new HashSet<>();
        for (ActivityTool tool : activityTools) {
            if (!Objects.equals(tool.getType(), PromotionToolType.BUY_GIFT) || !Objects.equals(tool.getBizPlatform(),
                    BizPlatformEnum.CAR_SHOP)) {
                continue;
            }
            BuyGiftPromotionConfig promotionConfig = (BuyGiftPromotionConfig) tool.getPromotionConfig();
            for (SkuGroup skuGroup : promotionConfig.getGiftGoods().getSkuGroupList()) {
                for (GiftBargainGroup gift : skuGroup.getListInfo()) {
                    slaveIdSet.add(gift.getSsuId());
                }
            }
        }

        // master id set
        //Set<Long> masterIdSet = request.getGoodsList().stream().map(MultiGoodItem::getSsuId).collect(Collectors.toSet());

        return goodsStockServiceProxy.getGiftStockBatchAsync(resultList, new ArrayList<>(slaveIdSet),
                this.convertRegion(request.getRegion()), CAR_SHOP_GOODS_STOCK_CHANNEL, true);
    }

    private Region convertRegion(com.xiaomi.nr.promotion.api.dto.model.carshop.Region region) {
        if (Objects.isNull(region)) {
            return null;
        }
        Region regionDto = new Region();
        regionDto.setProvince(region.getProvince());
        regionDto.setCity(region.getCity());
        regionDto.setArea(region.getArea());
        regionDto.setDistrict(region.getDistrict());
        return regionDto;
    }
}
