package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.entity.redis.FillGoodsGroup;
import com.xiaomi.nr.promotion.entity.redis.Goods;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/21
 */
public abstract class AbstractBuyGiftCondition extends AbstractCondition {
    
    /**
     * 活动ID
     */
    protected Long promotionId;
    
    /**
     * promotionToolType
     */
    protected PromotionToolType promotionToolType;
    /**
     * 赠品信息
     */
    protected Goods giftGoods;
    
    /**
     * 满足商品组列表(商品+条件）
     */
    protected FillGoodsGroup includeGoodsGroup;
    
    
    
    public boolean checkItemActQualify(
            CartItem item, Integer actType) {
        // 如果该item不能参加活动, 或者item本身是赠品或加价购，该item不参加活动
        if (item.getCannotJoinAct() || SourceEnum.isGiftBargain(item.getSource())) {
            return false;
        }
        // 不能参加的活动类型判断
        List<Long> excludeActTypes = item.getCannotJoinActTypes();
        if (CollectionUtils.isNotEmpty(excludeActTypes) && excludeActTypes.contains(actType.longValue())) {
            return false;
        }
        
        return true;
    }
}
