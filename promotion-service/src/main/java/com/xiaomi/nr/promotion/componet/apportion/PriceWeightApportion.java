package com.xiaomi.nr.promotion.componet.apportion;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.componet.Action;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2018/8/14
 */

@Component
@Scope(value = "prototype")
public class PriceWeightApportion extends Action {

    private long percentage;

    @Override
    public void loadConfig(AbstractPromotionConfig config) {

    }

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context)
            throws BizError {

    }
}
