package com.xiaomi.nr.promotion.domain.coupon.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/3/5
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MaintenanceDeductedInfo extends DeductedInfo {
    
    /**
     * 数量
     */
    private Integer num;
    
    /**
     * 数量
     */
    private BigDecimal count;
    
    public MaintenanceDeductedInfo(Integer index, Long goodPrice) {
        super(index, goodPrice);
    }
}
