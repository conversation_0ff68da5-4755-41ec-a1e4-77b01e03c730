package com.xiaomi.nr.promotion.domain.coupon.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/3/5
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MaintenanceDeductedInfo extends DeductedInfo {
    
    /**
     * 抵扣数量
     */
    private Integer num;
    
    /**
     * 抵扣金额
     */
    private Long deductPrice;
    
    /**
     * 单位面数的价格
     */
    private Long priceForPerPage;
    
    public MaintenanceDeductedInfo(Integer index, Long goodPrice) {
        super(index, goodPrice);
    }
}
