package com.xiaomi.nr.promotion.annotation.load;

import org.springframework.context.annotation.Conditional;

import java.lang.annotation.*;

@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Conditional(PromotionLoadCondition.class)
public @interface PromotionCondition {

    /**
     * 指定在哪些biz下生效
     * @return 业务标识数组
     */
    String[] value() default {};
}
