package com.xiaomi.nr.promotion.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.enums.SsuIdTypeEnum;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.enums.CouponServiceTypeEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.GoodsIndexNew;
import com.xiaomi.nr.promotion.model.common.Statistics;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ChildPriceInfo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 购物车项服务工具
 *
 * <AUTHOR>
 * @date 2021/3/30
 */
@Slf4j
public class CartHelper {

    /**
     * 获取购物车下标
     *
     * @param cartList   购物车列表
     * @param source     来源
     * @param sourceCode 来源Code
     * @return 下标列表
     */
    public static List<GoodsIndexNew> getCartsIndex(List<CartItem> cartList, String source, String sourceCode) {
        if (CollectionUtils.isEmpty(cartList)) {
            return Collections.emptyList();
        }
        List<GoodsIndexNew> indexNewList = Lists.newArrayList();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            if (!Objects.equals(item.getSource(), source) || !Objects.equals(item.getSourceCode(), sourceCode)) {
                continue;
            }
            indexNewList.add(new GoodsIndexNew(item.getGroupId(), item.getItemId(), idx));
        }
        return indexNewList;
    }

    /**
     * 获取下标购物车商品列表
     *
     * @param cartList  购物车商品列表
     * @param indexList 下标列表
     * @return 符合购物车列表
     */
    public static List<CartItem> getCartList(List<CartItem> cartList, List<Integer> indexList) {
        if (CollectionUtils.isEmpty(cartList) || CollectionUtils.isEmpty(indexList)) {
            return Collections.emptyList();
        }
        return indexList.stream()
                .filter(index -> index < cartList.size())
                .map(cartList::get).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取下标购物车商品列表
     *
     * @param cartList  购物车商品列表
     * @param indexList 下标列表
     * @return 符合购物车列表
     */
    public static List<CartItem> getIndexCartList(List<CartItem> cartList, List<GoodsIndex> indexList) {
        if (CollectionUtils.isEmpty(cartList) || CollectionUtils.isEmpty(indexList)) {
            return Collections.emptyList();
        }
        return indexList.stream()
                .filter(index -> index.getIndex() < cartList.size())
                .map(index -> cartList.get(index.getIndex()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取购物车总价
     *
     * @param cartList 购物车列表
     * @return 总价
     */
    public static long getTotalPrice(List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(cartList)) {
            return 0L;
        }
        return cartList.stream()
                .filter(item -> !SourceEnum.isGift(item.getSource()))
                .mapToLong(CartHelper::itemCurPrice)
                .sum();
    }


    /**
     * ItemCurPrice 计算某个item当前实际价格
     *
     * @param cartItem 购物车项
     * @return 价格（分）
     */
    public static Long itemCurPrice(OrderCartItem cartItem) {
        Long cartPrice = cartItem.getCartPrice();
        Integer count = cartItem.getCount();
        Long reduceAmount = cartItem.getReduceAmount();
        long price = cartPrice * count - reduceAmount;
        if (price < 0) {
            log.warn("the ItemCurPrice is {}, and c.CartPrice*c.Count is {}, cart {}", price, cartPrice * count, cartItem);
        }
        return price;
    }

    /**
     * 通过分摊计算商品当前价格
     *
     * @param originalPrice
     * @param reduceDetailItemList
     * @return
     */
    public static Long itemCurPrice(Long originalPrice, List<ReduceDetailItem> reduceDetailItemList) {
        if (CollectionUtils.isEmpty(reduceDetailItemList)) {
            return originalPrice;
        }
        return originalPrice - reduceDetailItemList.stream().mapToLong(ReduceDetailItem::getReduceSingle).sum();
    }

    /**
     * 通过分摊计算商品当前价格
     *
     * @param originalPrice
     * @param reduceDetailItemList
     * @return
     */
    public static Long itemCurPrice(Long originalPrice, List<ReduceDetailItem> reduceDetailItemList, PromotionToolType excludePromotionType) {
        if (CollectionUtils.isEmpty(reduceDetailItemList)) {
            return originalPrice;
        }
        return originalPrice - reduceDetailItemList.stream()
                .filter(x -> !Objects.equals(x.getPromotionType(), excludePromotionType.getTypeId()))
                .mapToLong(ReduceDetailItem::getReduceSingle).sum();
    }

    /**
     * 计算某个cartItem当前实际价格
     *
     * @param cartItem 购物车项
     * @return
     */
    public static Long cartItemCurPrice(CartItem cartItem) {
        Long cartPrice = cartItem.getCartPrice();
        Map<String, List<ReduceDetail>> reduceDetailsMap = cartItem.getReduceDetailList();
        long totalReduce = 0L;
        for (Map.Entry<String, List<ReduceDetail>> reduceDetails : reduceDetailsMap.entrySet()) {
            totalReduce += reduceDetails.getValue().stream().mapToLong(ReduceDetail::getAmount).sum();
        }
        long currentPrice = cartPrice - totalReduce;
        if (currentPrice < 0) {
            log.warn("the ItemCurPrice is {}, cartItem {}", cartItem);
        }
        return currentPrice;
    }

    /**
     * 计算某个订单item当前实际价格
     *
     * @param orderItem 订单item项
     * @return 当前实际价格
     */
    public static Long itemCurPrice2(OrderCartItem orderItem) {
        Long cartPrice = orderItem.getCartPrice();
        Map<String, List<ReduceDetail>> reduceDetailsMap = orderItem.getOrderItemReduceList();
        long totalReduce = 0L;
        for (Map.Entry<String, List<ReduceDetail>> reduceDetails : reduceDetailsMap.entrySet()) {
            totalReduce += reduceDetails.getValue().stream().mapToLong(ReduceDetail::getAmount).sum();
        }
        long currentPrice = cartPrice - totalReduce;
        if (currentPrice < 0) {
            log.warn("the ItemCurPrice is {}, orderItem {}", orderItem);
        }
        return currentPrice;
    }

    /**
     * 获取套装售卖价格
     *
     * @param cartItem 购物项
     * @return 售价（单位分）
     */
    public static long getPackageSellPrice(CartItem cartItem) {
        long totalPrice = 0;
        for (CartItemChild itemChild : cartItem.getChilds()) {
            totalPrice += itemChild.getSellPrice();
        }
        return totalPrice;
    }

    /**
     * 获取套装子商品售卖价格
     *
     * @param itemChild 子商品
     * @return 售价（单位分）
     */
    public static long getChildOriginPrice(CartItemChild itemChild) {
        return itemChild.getOriginalSellPrice() * itemChild.getCount();
    }

    /**
     * 购物车item是否可以作为参与活动的候选item，true-可以，false-不可以
     *
     * @param item        购物车项
     * @param actType     活动类型
     * @param online      是否线上
     * @param saleSources 销售来源
     * @param accessCode  活动密码
     * @return true/false
     */
    public static boolean checkItemActQualify(CartItem item, Integer actType, boolean online, List<String> saleSources,
                                              String accessCode) {
        // 如果该item不能参加活动, 或者item本身是赠品或加价购，该item不参加活动
        if (item.getCannotJoinAct() || SourceEnum.isGiftBargain(item.getSource())) {
            return false;
        }
        // 不能参加的活动类型判断
        List<Long> excludeActTypes = item.getCannotJoinActTypes();
        if (CollectionUtils.isNotEmpty(excludeActTypes) && excludeActTypes.contains(actType.longValue())) {
            return false;
        }
        // 线下门店, 直接返回
        if (!online) {
            return true;
        }

        // （融合版）线上逻辑,检查订单来源和活动密码
        if (CollectionUtils.isNotEmpty(saleSources)) {
            if (CollectionUtils.intersection(saleSources, item.getSaleSources()).isEmpty()) {
                return false;
            }
        }
        if (StringUtils.isNotEmpty(accessCode)) {
            List<String> accessCodeList = Optional.ofNullable(item.getAccessCode()).orElse(Collections.emptyList());
            return accessCodeList.stream().anyMatch(accessCode::equals);
        }
        return true;
    }

    /**
     * 购物车item是否可以作为参与活动的候选item，true-可以，false-不可以
     *
     * @param item    购物车项
     * @param actType 活动类型
     * @return true/false
     */
    public static boolean checkItemActQualifyCommon(CartItem item, Integer actType) {
        // 如果该item不能参加活动, 或者item本身是赠品或加价购，该item不参加活动
        if (item.getCannotJoinAct() || SourceEnum.isGiftBargain(item.getSource())) {
            return false;
        }
        // 不能参加的活动类型判断
        List<Long> excludeActTypes = item.getCannotJoinActTypes();
        if (CollectionUtils.isNotEmpty(excludeActTypes) && excludeActTypes.contains(actType.longValue())) {
            return false;
        }
        return true;
    }

    /**
     * 判断该item是否可以继续作为参加直降活动的候选item，可以返回true
     *
     * @param cartItem 购物车商品
     * @param orgCode  门店Code
     * @return true/false
     */
    public static boolean isOnsaleActQualifyItem(CartItem cartItem, String orgCode) {
        if (StringUtils.isNotEmpty(orgCode)) {
            return true;
        }
        return CollectionUtils.intersection(PromotionConstant.SALE_SOURCE_EXCLUDE, cartItem.getSaleSources()).isEmpty();
    }

    /**
     * 校验购物车项
     *
     * @param cartItem 购物车
     * @return true/false
     */
    public static boolean filterOk(CartItem cartItem) {
        return filterSimpleOk(cartItem) && checkReduce(cartItem);
    }

    /**
     * d
     * filterSimpleOK 简单购物车正确x
     *
     * @param cartItem 购物项
     * @return true/false
     */
    public static boolean filterSimpleOk(CartItem cartItem) {
        return filterOrderOk(cartItem) && checkPackage(cartItem);
    }

    /**
     * 检查减价
     *
     * @param cartItem 购物项
     * @return true/false
     */
    private static boolean checkReduce(CartItem cartItem) {
        Map<String, Long> reduceList = cartItem.getReduceList();
        // 没有，则返回
        if (MapUtil.isEmpty(reduceList)) {
            cartItem.setReduceAmount(0L);
            return true;
        }
        //　每一个值正常
        long reduceAmount = 0;
        Map<String, Long> delReduceList = cartItem.getReduceList();

        for (Map.Entry<String, Long> entry : reduceList.entrySet()) {
            String idKey = entry.getKey();
            Long money = entry.getValue();
            if (entry.getValue() < 0) {
                log.warn("in ReduceList, money: {}, IDKey: {}, c {}", money, idKey, cartItem);
                return false;
            }
            if (money == 0) {
                delReduceList.put(idKey, money);
                continue;
            }
            //活动ID前缀正确
            String prefix = getKeyPrefix(idKey);
            if (Strings.EMPTY.equals(prefix)) {
                log.warn("in ReduceList, the prefix of actID {} is wrong, cart {}", idKey, cartItem);
                return false;
            }
            reduceAmount += money;
        }

        //合计正确
        if (reduceAmount != cartItem.getReduceAmount()) {
            log.warn("in ReduceList, the reduceAmount is {}, c.ReduceAmount is {}, cart {}", reduceAmount, cartItem.getReduceAmount(), cartItem);
            return false;
        }
        return true;
    }

    /**
     * 校验是否是一个合法套装，校验孩子的购物车价格
     *
     * @param cartItem 购物项
     * @return true/false
     */
    private static boolean checkPackage(CartItem cartItem) {
        // 套装合法
//        if (!(StringUtils.isEmpty(cartItem.getPackageId()) && CollectionUtils.isEmpty(cartItem.getChilds()) && StringUtils.isNotEmpty(cartItem.getSku()) && cartItem.getSsuId() != null)
//                && !(StringUtils.isNotEmpty(cartItem.getPackageId()) && CollectionUtils.isNotEmpty(cartItem.getChilds()) && (StringUtils.isEmpty(cartItem.getSku()) || cartItem.getSsuId() == null ))) {
//            log.warn("the PackageID is not matched Childs or sku still in! The cart: {}", cartItem);
//            return false;
//        }

        //不是套装 返回
        if (CollectionUtils.isEmpty(cartItem.getChilds())) {
            cartItem.setChilds(null);
            return true;
        }

        // 如果是套装,校验孩子
        long packageCartPrice = 0L;
        for (CartItemChild child : cartItem.getChilds()) {
            if (child == null) {
                log.warn("the child's is nil. the cart: {}", cartItem);
                return false;
            }
            if (childDataValid(child)) {
                log.warn("the %vth child's data is wrong,  cart: {}, child:{}", cartItem, child);
                return false;
            }
            packageCartPrice += child.getCartPrice();
        }
        if (packageCartPrice != 0 && packageCartPrice != cartItem.getCartPrice()) {
            log.error("the c.CartPrice is {} ,but the sum of child cartPrice is {}! The cart: {}",
                    cartItem.getCartPrice(), packageCartPrice, cartItem);
            return false;
        }
        return true;
    }


    /**
     * 符合条件列表
     *
     * @param goodsIndices 下标
     * @return ID列表
     */
    public static List<String> getParentItemIdList(List<GoodsIndex> goodsIndices) {
        if (CollectionUtils.isEmpty(goodsIndices)) {
            return Collections.emptyList();
        }
        return goodsIndices.stream().map(GoodsIndex::getItemId)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获得参加活动的购物车项ID列表
     *
     * @param goodsIndexNewList 符合条件的商品
     * @param cartList          购物车列表
     * @return ID列表
     */
    public static List<String> getJoinedItemIdListNew(List<GoodsIndexNew> goodsIndexNewList, List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(goodsIndexNewList)) {
            return null;
        }
        List<String> itemIdList = goodsIndexNewList.stream()
                .filter(goodsIndex ->
                        cartList.stream().anyMatch(cartItem -> goodsIndex.getItemId().equals(cartItem.getItemId())))
                .map(GoodsIndexNew::getItemId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemIdList)) {
            return null;
        }
        return itemIdList;
    }

    /**
     * 获得参加活动的购物车项ID列表
     *
     * @param goodsIndices 符合条件的商品
     * @param cartList     购物车列表
     * @return ID列表
     */
    public static List<String> getJoinedItemIdList(List<GoodsIndex> goodsIndices, List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(goodsIndices)) {
            return Collections.emptyList();
        }
        return goodsIndices.stream()
                .filter(goodsIndex ->
                        cartList.stream().anyMatch(cartItem -> goodsIndex.getItemId().equals(cartItem.getItemId())))
                .map(GoodsIndex::getItemId)
                .collect(Collectors.toList());
    }

    /**
     * 获得参加活动的购物车项ID列表
     *
     * @param goodsIndices 符合条件的商品
     * @param cartList     购物车列表
     * @param activityId   活动ID
     * @return ID列表
     */
    public static List<String> getJoinedItemIdList(List<GoodsIndex> goodsIndices, List<CartItem> cartList, Long activityId) {
        if (CollectionUtils.isEmpty(goodsIndices)) {
            return Collections.emptyList();
        }
        return goodsIndices.stream()
                .filter(goodsIndex -> cartList.stream().anyMatch(cartItem -> {
                    if (!Objects.equals(goodsIndex.getItemId(), cartItem.getItemId())) {
                        return false;
                    }
                    String idKey = PromotionConstant.CARTLIST_ACTIVITY_PREFIX + activityId;
                    return cartItem.getReduceList().containsKey(idKey);
                }))
                .map(GoodsIndex::getItemId)
                .collect(Collectors.toList());
    }

    /**
     * 购物车改价
     *
     * @param cartItem  购物车商品
     * @param cartPrice 购物车商品价
     */
    public static void changePrice(CartItem cartItem, Long cartPrice) {
        cartItem.setCartPrice(cartPrice);
    }

    /**
     * 购物车改价
     *
     * @param cartItem    购物车商品
     * @param cartPrice   购物车商品价
     * @param marketPrice 活动配置的市场价
     */
    public static void changeGiftPrice(CartItem cartItem, Long cartPrice, Long marketPrice) {
        cartItem.setCartPrice(cartPrice);
        // 赠品如果没有售价，用活动配置的市场价
        if (cartItem.getStandardPrice() == 0) {
            cartItem.setStandardPrice(marketPrice);
        }
    }

    /**
     * 从购物车列表中获得对应商品
     *
     * @param cartItemList 购物车列表
     * @param indexNew     商品位置信息
     * @return 购物车商品
     */
    public static CartItem getCartItem(List<CartItem> cartItemList, GoodsIndexNew indexNew) {
        if (CollectionUtils.isEmpty(cartItemList)) {
            return null;
        }
        int llen = cartItemList.size();
        int index = Optional.ofNullable(indexNew.getIndex()).orElse(0);
        if (index >= llen || index < 0) {
            log.warn("the GoodsIndexNew is wrong! info: {}", indexNew);
            log.warn("the len of cartList is {}, but the index is {}", llen, indexNew.getIndex());
            return null;
        }
        CartItem cartItem = cartItemList.get(index);
        if (cartItem == null) {
            log.warn("the GoodsIndexNew is wrong! info: {}", indexNew);
            log.warn("the current cart that the index {} means is nil", indexNew.getIndex());
            return null;
        }
        if (!cartItem.getItemId().equals(indexNew.getItemId())) {
            log.warn("the GoodsIndexNew is wrong! info: {}", indexNew);
            log.warn("the current cart's item {} that the index %v means is not equal to {}", cartItem.getItemId(), indexNew.getItemId());
            return null;
        }
        return cartItem;
    }

    /**
     * 购物车项能否不能参加对应券
     *
     * @param item         购物车项
     * @param couponTypeId 券类型Id
     * @return true/false
     */
    public static boolean isCouponQualifyItem(CartItem item, Long couponTypeId) {
        if (item.getCannotUseCoupon()) {
            return false;
        }
        if (CollectionUtils.isEmpty(item.getCannotUseCouponTypes())) {
            return true;
        }
        if (item.getCannotUseCouponTypes().contains(couponTypeId)) {
            return false;
        }
        return true;
    }

    /**
     * 购物车项能否不能参加对应券
     *
     * @param item         购物车项
     * @param couponTypeId 券类型Id
     * @return true/false
     */
    public static boolean isCouponQualifyItem(CartItem item, Long couponTypeId, Long serviceType) {
        return isCouponQualifyItem(item, couponTypeId)
                && (CollectionUtils.isEmpty(item.getMaintenanceInfo().getCannotUseCouponServiceTypes())
                    || !item.getMaintenanceInfo().getCannotUseCouponServiceTypes().contains(serviceType));
    }

    /**
     * 获取有效累加信息：累加价格和数量
     *
     * @param cartList 购物车列表
     * @param goodsInd 有效商品下标
     * @return ValidGoods
     */
    public static ValidGoods buildValidGoods(List<CartItem> cartList, List<GoodsIndex> goodsInd) {
        long validPrice = 0L;
        long includeCount = 0L;
        int len = cartList.size();
        for (GoodsIndex index : goodsInd) {
            int idx = index.getIndex();
            if (idx >= len) {
                continue;
            }
            CartItem item = cartList.get(idx);
            if (item == null) {
                continue;
            }
            validPrice += (item.getCartPrice() * item.getCount() - item.getReduceAmount());
            includeCount += item.getCount();
        }
        ValidGoods validGoods = new ValidGoods();
        validGoods.setValidNum(includeCount);
        validGoods.setValidPrice(validPrice);
        return validGoods;
    }

    public static ValidGoods buildValidGoods2(List<CartItem> cartList, List<GoodsIndex> goodsInd) {
        long validPrice = 0L;
        long includeCount = 0L;
        int len = cartList.size();
        for (GoodsIndex index : goodsInd) {
            int idx = index.getIndex();
            if (idx >= len) {
                continue;
            }
            CartItem item = cartList.get(idx);
            if (item == null) {
                continue;
            }
            validPrice += itemCheckoutAmount(item);
            includeCount += item.getCount();
        }
        ValidGoods validGoods = new ValidGoods();
        validGoods.setValidNum(includeCount);
        validGoods.setValidPrice(validPrice);
        return validGoods;
    }

    /**
     * 获取统计信息
     *
     * @param cartItemList 购物车项
     * @return 统计信息
     * @throws BizError 异常
     */
    public static Statistics checkAndStatistics(List<CartItem> cartItemList) throws BizError {
        Statistics statistics = new Statistics();
        statistics.setActReduce(Maps.newHashMap());
        statistics.setRedPacketReduce(Maps.newHashMap());
        statistics.setPhoenixReduce(Maps.newHashMap());

        // 迭代购物车， 追加汇总数据
        cartItemList = Optional.ofNullable(cartItemList).orElse(Collections.emptyList());
        for (CartItem cartItem : cartItemList) {
            if (cartItem == null || !CartHelper.filterOk(cartItem)) {
                log.warn("the cart's data is wrong, cart:{}", cartItem);
                throw ExceptionHelper.create(GeneralCodes.InternalError, "inner.invalidCalculate");
            }
            appendStatistic(statistics, cartItem);
        }

        // 校验结果
        long totalReduce = statistics.getTotalReduce();
        long totalActReduce = statistics.getTotalActReduce();
        long totalCouponReduce = statistics.getTotalCouponReduce();
        long totalRedPacketReduce = statistics.getTotalRedPacketReduce();
        long totalPhoenixReduce = statistics.getTotalPhoenixReduce();
        if (totalReduce != (totalActReduce + totalCouponReduce + totalRedPacketReduce + totalPhoenixReduce)) {
            log.warn("The totalReduce {}, totalActReduce {}, totalCouponReduce {}, totalRedPacketReduce {} totalPhoenixReduce:{}", totalReduce,
                    totalActReduce, totalCouponReduce, totalRedPacketReduce, totalPhoenixReduce);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "inner.invalidCalculate");
        }
        return statistics;
    }

    /**
     * 向Statistics对象追加汇总数据
     *
     * @param statistics 统计新
     * @param cartItem   购物车项
     */
    private static void appendStatistic(Statistics statistics, CartItem cartItem) {
        long totalCount = statistics.getTotalCount() + cartItem.getCount();
        long totalPrice = statistics.getTotalPrice() + cartItem.getCartPrice() * cartItem.getCount();
        long totalReduce = statistics.getTotalReduce() + cartItem.getReduceAmount();
        long totalStandardPrice = statistics.getTotalStandardPrice() + cartItem.getStandardPrice();
        statistics.setTotalCount(totalCount);
        statistics.setTotalPrice(totalPrice);
        statistics.setTotalReduce(totalReduce);
        statistics.setTotalStandardPrice(totalStandardPrice);

        long totalActReduce = statistics.getTotalActReduce() + totalActReduce(cartItem);
        long totalCouponReduce = statistics.getTotalCouponReduce() + totalCouponReduce(cartItem);
        long totalRedPacketReduce = statistics.getTotalRedPacketReduce() + totalRedPackageReduce(cartItem);
        long totalPhoenixReduce = statistics.getTotalPhoenixReduce() + totalPhoenixReduce(cartItem);
        statistics.setTotalActReduce(totalActReduce);
        statistics.setTotalCouponReduce(totalCouponReduce);
        statistics.setTotalRedPacketReduce(totalRedPacketReduce);
        statistics.setTotalPhoenixReduce(totalPhoenixReduce);

        final Map<String, Long> reduceMap = cartItem.getReduceList();
        if (MapUtil.isEmpty(reduceMap)) {
            return;
        }
        if (statistics.getActReduce() == null) {
            statistics.setActReduce(new HashMap<>());
        }
        final Map<String, Long> actReduce = statistics.getActReduce();
        reduceMap.forEach((actId, money) -> actReduce.merge(actId, money, Long::sum));
    }

    /**
     * 计算购物车活动总减价
     *
     * @param cartItem 购物项
     * @return reducePrice
     */
    private static long totalActReduce(CartItem cartItem) {
        Map<String, Long> reduceMap = cartItem.getReduceList();
        if (MapUtil.isEmpty(reduceMap)) {
            return 0L;
        }

        long totalActReduce = 0L;
        for (Map.Entry<String, Long> entry : reduceMap.entrySet()) {
            String keyPrefix = CartHelper.getKeyPrefix(entry.getKey());
            if (!PromotionConstant.CARTLIST_ACTIVITY_PREFIX.equals(keyPrefix)) {
                continue;
            }
            totalActReduce += entry.getValue();
        }
        return totalActReduce;
    }

    /**
     * 统计所有优惠券减价
     *
     * @param cartItem 购物项
     * @return reducePrice
     */
    private static long totalCouponReduce(CartItem cartItem) {
        Map<String, Long> reduceMap = cartItem.getReduceList();
        if (MapUtil.isEmpty(reduceMap)) {
            return 0L;
        }

        long totalCouponReduce = 0L;
        for (Map.Entry<String, Long> entry : reduceMap.entrySet()) {
            String keyPrefix = CartHelper.getKeyPrefix(entry.getKey());
            if (!PromotionConstant.CARTLIST_COUPON_PREFIX.equals(keyPrefix)) {
                continue;
            }
            totalCouponReduce += entry.getValue();
        }
        return totalCouponReduce;
    }

    /**
     * 统计所有红包减价
     *
     * @param cartItem 购物项
     * @return reducePrice
     */
    private static long totalRedPackageReduce(CartItem cartItem) {
        Map<String, Long> reduceMap = cartItem.getReduceList();
        if (MapUtil.isEmpty(reduceMap)) {
            return 0L;
        }

        long totalRedPackageReduce = 0L;
        for (Map.Entry<String, Long> entry : reduceMap.entrySet()) {
            String keyPrefix = CartHelper.getKeyPrefix(entry.getKey());
            if (!PromotionConstant.CARTLIST_REDPACKET_PREFIX.equals(keyPrefix)) {
                continue;
            }
            totalRedPackageReduce += entry.getValue();
        }
        return totalRedPackageReduce;
    }

    /**
     * 统计所有三方优惠减价
     *
     * @param cartItem 购物项
     * @return reducePrice
     */
    private static long totalPhoenixReduce(CartItem cartItem) {
        Map<String, Long> reduceMap = cartItem.getReduceList();
        if (MapUtil.isEmpty(reduceMap)) {
            return 0L;
        }

        long totalPhoenixReduce = 0L;
        for (Map.Entry<String, Long> entry : reduceMap.entrySet()) {
            String keyPrefix = CartHelper.getKeyPrefix(entry.getKey());
            if (!PromotionConstant.CARTLIST_PHOENIX_PREFIX.equals(keyPrefix)) {
                continue;
            }
            totalPhoenixReduce += entry.getValue();
        }
        return totalPhoenixReduce;
    }


    /**
     * 校验是否是一个合法套装,但不校验孩子的购物车价格
     *
     * @return true/false
     */
    public static boolean rightInPackage(CartItem cartItem) {
        // 套装合法
        if (!(StringUtils.isEmpty(cartItem.getPackageId()) && CollectionUtils.isEmpty(cartItem.getChilds()) && StringUtils.isNotEmpty(cartItem.getSku()))
                && !(StringUtils.isNotEmpty(cartItem.getPackageId()) && CollectionUtils.isNotEmpty(cartItem.getChilds()) && StringUtils.isEmpty(cartItem.getSku()))) {
            log.warn("the PackageID is not matched Childs or sku still in! The cart: {}", cartItem);
            return false;
        }

        //不是套装 返回
        if (CollectionUtils.isEmpty(cartItem.getChilds())) {
            cartItem.setChilds(null);
            return true;
        }

        // 如果是套装,校验孩子
        for (CartItemChild child : cartItem.getChilds()) {
            if (child == null) {
                log.warn("the child's is nil. the cart: {}", cartItem);
                return false;
            }
            if (childDataValid(child)) {
                log.warn("the %vth child's data is wrong,  cart: {}, child:{}", cartItem, child);
                return false;
            }
        }
        return true;
    }

    /**
     * 对应购物项是不是套装
     *
     * @param cartItem 购物项
     * @return true/false
     */
    public static boolean isPackage(CartItem cartItem) {
        return StringUtils.isNotEmpty(cartItem.getPackageId()) || isNewPackage(cartItem);
    }

    /**
     * 对应购物项是不是新套装
     *
     * @param cartItem 购物项
     * @return true/false
     */
    public static boolean isNewPackage(CartItem cartItem) {
        return SsuIdTypeEnum.PACKAGE.code.equals(cartItem.getSsuType());
    }

    /**
     * 对应购物项是不是单品
     *
     * @param cartItem 购物项
     * @return true/false
     */
    public static boolean isSku(CartItem cartItem) {
        return !isPackage(cartItem) && StringUtils.isNotEmpty(cartItem.getSku());
    }

    /**
     * 对应购物项是不是单品
     *
     * @param cartItem 购物项
     * @return true/false
     */
    public static boolean isSsu(CartItem cartItem) {
        return !isSku(cartItem) && cartItem.getSsuId() != null && cartItem.getSsuId() != 0;
    }

    /**
     * 简单校验
     *
     * @param cartItem 购物车项
     * @return true/false
     */
    public static boolean filterOrderOk(OrderCartItem cartItem) {
        // ItemID必须有
        if (StringUtils.isEmpty(cartItem.getItemId())) {
            log.warn("the ItemID is of the cart: {} is null", cartItem);
            return false;
        }

        // 必须为单品或者套装其一
        String sku = cartItem.getSku();
        String packageId = cartItem.getPackageId();
        Long ssuId = cartItem.getSsuId();
        if (StringUtils.isEmpty(sku) && StringUtils.isEmpty(packageId) && ssuId == null) {
            log.warn("both sku and packageId is empty. cart: {}, sku：{}, packageID：{}", cartItem, sku, packageId);
            return false;
        }

        // 价格和数量合法 reduceList和package合法
        Long standardPrice = cartItem.getStandardPrice();
        Long cartPrice = cartItem.getCartPrice();
        Long reduceAmount = cartItem.getReduceAmount();
        Integer count = cartItem.getCount();
        boolean isValidPriceAmount = standardPrice >= 0 && cartPrice >= 0 && reduceAmount >= 0 && count > 0;
        if (!isValidPriceAmount) {
            log.warn("the cart: {}", cartItem);
            log.warn("the StandardPrice is {}, CartPrice is {}, ReduceAmount is {}, all should >=0", standardPrice,
                    cartPrice, reduceAmount);
            return false;
        }

        boolean isValidCurPrice = itemCurPrice(cartItem) > 0;
        if (!isValidCurPrice) {
            //log.warn("the cart: {}", cartItem);
            // log.warn("the Count is {}, should >0", count);
        }
        return true;
    }

    /**
     * 校验套装子商品
     *
     * @param child 商品
     * @return true/false
     */
    public static boolean childDataValid(CartItemChild child) {
        if (child == null) {
            return false;
        }
        Long sellPrice = child.getSellPrice();
        String sku = child.getSku();
        Integer count = child.getCount();
        if (sellPrice < 0L || child.getCount() < 1 && StringUtils.isEmpty(sku)) {
            log.warn("the child of the package is invalid! SellPrice: {} should >=0, should >=0, Count: {} should ==1, sku: {} should != empty", sellPrice, count, sku);
            return false;
        }
        return false;
    }

    /**
     * ItemCurPrice 计算某个Goods当前实际价格
     *
     * @param cartItem 购物车项
     * @return 价格（分）
     */
    public static Long goodsCurPrice(OrderCartItem cartItem) {
        return (itemCurPrice(cartItem) / cartItem.getCount()) + (itemCurPrice(cartItem) % cartItem.getCount());
    }

    /**
     * ItemCurPrice 计算某个Goods当前实际价格
     *
     * @param cartItem 购物车项
     * @return 价格（分）
     */
    public static Long goodsCurPriceByReduceItem(CartItem cartItem) {
        long reduceTotal = cartItem.getReduceItemList().stream()
                .map(reduceDetailItem -> reduceDetailItem.getReduceSingle() == null ? 0 : reduceDetailItem.getReduceSingle())
                .mapToLong(Long::longValue)
                .sum();
        return cartItem.getOriginalCartPrice() - reduceTotal;
    }

    /**
     * 计算购物车某个item当前实际结算总金额（结果 = 当前item单个商品原价 * 当前item商品数量 - 当前item总扣减金额）
     *
     * @param cartItem 购物车项
     * @return 价格（分）
     */
    public static Long itemCheckoutAmount(CartItem cartItem) {
        long reduceTotal = 0L;
        if (CollectionUtils.isNotEmpty(cartItem.getReduceItemList())) {
            reduceTotal = cartItem.getReduceItemList().stream()
                    .map(reduceDetailItem -> reduceDetailItem.getReduce() == null ? 0 : reduceDetailItem.getReduce())
                    .mapToLong(Long::longValue)
                    .sum();
        }
        long totalCartPrice = cartItem.getOriginalCartPrice() * cartItem.getCount();
        long price = totalCartPrice - reduceTotal;
        if (price < 0) {
            log.error("checkoutPrice calc error, the ItemCurPrice is {}, and totalCartPrice is {}, and reduceTotal is {}, cartItem: {}", price, totalCartPrice, reduceTotal, cartItem);
            return 0L;
        }
        return price;
    }

    /**
     * 计算购物车某个item下子品当前实际结算总金额（结果 = 当前item中的单个子品商品原价 * 当前套装item中子品数量 * 当前item商品数量 - 当前item总扣减金额）
     *
     * @param cartItem 购物车项
     * @return 价格（分）
     */
    public static Long itemChildCheckoutAmount(CartItem cartItem, CartItemChild child) {
        long reduceTotal = 0L;
        if (CollectionUtils.isNotEmpty(cartItem.getReduceItemList())) {
            reduceTotal = cartItem.getReduceItemList().stream()
                    .filter(reduceDetailItem -> reduceDetailItem.getSsuId().equals(child.getSsuId()))
                    .map(reduceDetailItem -> reduceDetailItem.getReduce() == null ? 0 : reduceDetailItem.getReduce())
                    .mapToLong(Long::longValue)
                    .sum();
        }
        long totalCartPrice = child.getOriginalSellPrice() * child.getCount() * cartItem.getCount();
        long price = totalCartPrice - reduceTotal;
        if (price < 0) {
            log.error("checkoutChildPrice calc error, the ItemCurPrice is {}, and totalCartPrice is {}, and reduceTotal is {}, cartItem: {}", price, totalCartPrice, reduceTotal, cartItem);
            return 0L;
        }
        return price;
    }

    /**
     * 计算某个Goods当前结算价格（结果仅用作结算页展示使用，不可用于计算）
     *
     * @param cartItem 购物车项
     * @return 价格（分）
     */
    public static Long itemChildSingleCurCheckoutPrice(CartItem cartItem) {
        long itemCheckoutPrice = itemCheckoutAmount(cartItem);
        //跟雪翔和唯一一起确认的：如果不能整除的，返回加1分钱，如果能整除的则返回整除金额
        if (itemCheckoutPrice % cartItem.getCount() > 0) {
            return itemCheckoutPrice / cartItem.getCount() + 1;
        }
        return itemCheckoutPrice / cartItem.getCount();
    }

    /**
     * 计算某个Goods当前结算价格（结果仅用作结算页展示使用，不可用于计算）
     *
     * @param cartItem 购物车项
     * @return 价格（分）
     */
    public static Long itemChildSingleCurCheckoutPrice(CartItem cartItem, CartItemChild child) {
        long itemCheckoutPrice = itemChildCheckoutAmount(cartItem, child);
        //跟雪翔和唯一一起确认的：如果不能整除的，返回加1分钱，如果能整除的则返回整除金额

        int count = cartItem.getCount() * child.getCount();

        if (itemCheckoutPrice % count > 0) {
            return itemCheckoutPrice / count + 1;
        }
        return itemCheckoutPrice / count;
    }

    /**
     * 获取key 前缀
     *
     * @param key IdKey
     * @return keyPrefix
     */
    public static String getKeyPrefix(String key) {
        String[] temp = StringUtils.split(key, '_');
        final int keyLen = 2;
        if (temp.length != keyLen) {
            log.error("the key is wrong !\\n key :{}", key);
            return null;
        }

        String prefix = null;
        switch (temp[0] + "_") {
            case PromotionConstant.CARTLIST_ACTIVITY_PREFIX:
                prefix = PromotionConstant.CARTLIST_ACTIVITY_PREFIX;
                break;
            case PromotionConstant.CARTLIST_COUPON_PREFIX:
                prefix = PromotionConstant.CARTLIST_COUPON_PREFIX;
                break;
            case PromotionConstant.CARTLIST_REDPACKET_PREFIX:
                prefix = PromotionConstant.CARTLIST_REDPACKET_PREFIX;
                break;
            case PromotionConstant.CARTLIST_ONSALE_PREFIX:
                prefix = PromotionConstant.CARTLIST_ONSALE_PREFIX;
                break;
            case PromotionConstant.CARTLIST_PHOENIX_PREFIX:
                prefix = PromotionConstant.CARTLIST_PHOENIX_PREFIX;
                break;
            default:
                log.error("the prefix of the key is wrong !\\n key : {}", key);
        }
        return prefix;
    }

    /**
     * 优惠明细转换
     *
     * @param reduceList       原优惠明细
     * @param reduceDetailList 优惠明细（更细粒度）
     * @throws BizError
     */
    public static void updateReduceDetailByReduceList(Map<String, Long> reduceList, Map<String, List<ReduceDetail>> reduceDetailList) throws BizError {
        for (Map.Entry<String, Long> reduceEntry : reduceList.entrySet()) {
            String key = reduceEntry.getKey();
            Long value = reduceEntry.getValue();
            String[] temp = StringUtils.split(key, '_');
            final int keyLen = 2;
            if (temp.length != keyLen) {
                log.error("the key is wrong !\\n key :{}", key);
                throw ExceptionHelper.create(GeneralCodes.InternalError, "inner.invalidCalculate");
            }
            long id = Long.valueOf(temp[1]);
            for (Map.Entry<String, List<ReduceDetail>> entry : reduceDetailList.entrySet()) {
                List<ReduceDetail> reduceDetails = entry.getValue();
                if (reduceDetails == null) {
                    reduceDetails = new ArrayList<>();
                }
                for (ReduceDetail reduceDetail : reduceDetails) {
                    if (id == reduceDetail.getId()) {
                        reduceDetail.setAmount(value);
                        break;
                    }
                }
            }
        }

    }


    /**
     * 删除赠品加价购商品
     *
     * @param cartList 购物车列表
     * @param actId    活动ID
     */
    public static void delGiftBargain(List<CartItem> cartList, Long actId, Long ssu) {
        if (CollectionUtils.isEmpty(cartList)) {
            return;
        }
        List<CartItem> removeCartList = new ArrayList<>();
        for (CartItem item : cartList) {
            if (item.getSourceCode().equals(actId.toString())
                    && SourceEnum.isGiftBargain(item.getSource())
                    && item.getSsuId().equals(ssu)) {
                log.info("act and gift bargain not match, del item from cartList. actId:{},itemId:{}", actId, item.getItemId());
                removeCartList.add(item);
            }
        }
        cartList.removeAll(removeCartList);
    }


    /**
     * 删除赠品加价购商品
     *
     * @param cartList 购物车列表
     * @param actId    活动ID
     */
    public static void delGiftBargain(List<CartItem> cartList, Long actId) {
        if (CollectionUtils.isEmpty(cartList)) {
            return;
        }
        List<CartItem> removeCartList = new ArrayList<>();
        for (CartItem item : cartList) {
            if (item.getSourceCode().equals(actId.toString()) && SourceEnum.isGiftBargain(item.getSource())) {
                log.info("act and gift bargain not match, del item from cartList. actId:{},itemId:{}", actId, item.getItemId());
                removeCartList.add(item);
            }
        }
        cartList.removeAll(removeCartList);
    }

    /**
     * 删除赠品加价购商品
     *
     * @param cartList 购物车列表
     */
    public static void delGiftBargain(List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(cartList)) {
            return;
        }
        List<CartItem> removeCartList = new ArrayList<>(cartList.size());
        for (CartItem item : cartList) {
            if (SourceEnum.isGiftBargain(item.getSource())) {
                log.info("delete gift bargain item from cartList. itemId:{}", item.getItemId());
                removeCartList.add(item);
            }
        }
        cartList.removeAll(removeCartList);
    }

    /**
     * 获取购物车sku or package (包含套装下sku)
     *
     * @param cartList 购物车列表
     * @return sku Or package List
     */
    public static List<String> getSkuPackageList(List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(cartList)) {
            return Collections.emptyList();
        }
        List<String> skuPkgList = new ArrayList<>();
        for (CartItem cartItem : cartList) {
            skuPkgList.add(getSkuPackage(cartItem));
            if (cartItem.getSsuId() != null && cartItem.getSsuId() != 0) {
                skuPkgList.add(getSkuPackageForOnsale(cartItem));
            }
            // package children
            if (CollectionUtils.isEmpty(cartItem.getChilds())) {
                continue;
            }
            List<String> childSkuList = cartItem.getChilds().stream()
                    .map(CartItemChild::getSku).collect(Collectors.toList());
            skuPkgList.addAll(childSkuList);
        }
        return skuPkgList;
    }

    /**
     * 获取sku获得packageId
     *
     * @param cartItem 购物车项
     * @return sku/packageId
     */
    public static String getSkuPackage(CartItem cartItem) {
        if (StringUtils.isNotEmpty(cartItem.getPackageId())) {
            return cartItem.getPackageId();
        }
        if (cartItem.getSsuId() != null && cartItem.getSsuId() != 0L) {
            return cartItem.getSsuId().toString();
        }
        return cartItem.getSku();
    }

    public static String getSkuPackageForOnsale(CartItem cartItem) {
        if (StringUtils.isNotEmpty(cartItem.getPackageId())) {
            return cartItem.getPackageId();
        }

        return StringUtils.isEmpty(cartItem.getSku()) ? String.valueOf(cartItem.getSsuId()) : cartItem.getSku();
    }


    /**
     * 更新套装下子商品的价格
     *
     * @param cartItem 购物车项
     */
    public static void updateChildPrice(CartItem cartItem) {
        long totalSellPrice = CartHelper.getPackageSellPrice(cartItem);
        // 套装销售价
        long packageSellPrice = cartItem.getStandardPrice();

        long actualSellPrice = 0L;
        // 孩子的originalSellPrice按sellPrice比例做分摊
        for (CartItemChild child : cartItem.getChilds()) {
            long childRealSellPrice = Math.floorDiv(packageSellPrice * child.getSellPrice(), totalSellPrice);
            child.setOriginalSellPrice(childRealSellPrice);
            actualSellPrice += childRealSellPrice;
        }

        long priceDiff = packageSellPrice - actualSellPrice;
        if (priceDiff == 0L) {
            return;
        }

        // 有分摊余额, 一分钱一分钱分摊
        long moneyDiff = 1;
        for (CartItemChild child : cartItem.getChilds()) {
            if (priceDiff <= 0) {
                return;
            }
            child.setOriginalSellPrice(child.getOriginalSellPrice() + moneyDiff);
            priceDiff -= moneyDiff;
        }
    }

    /**
     * 获取套装子商品的活动配置价格
     *
     * @param cartItem          购物车项
     * @param batchInfoListList 价格配置
     * @return map key:sku value:skuPrice
     */
    public static Map<String, Long> getChildPriceMap(CartItem cartItem, List<List<ChildPriceInfo>> batchInfoListList) {
        if (CollectionUtils.isEmpty(batchInfoListList)) {
            return Collections.emptyMap();
        }

        final List<ChildPriceInfo> batchInfoList = new ArrayList<>();
        for (List<ChildPriceInfo> skuPriceGroup : batchInfoListList) {
            batchInfoList.addAll(skuPriceGroup);
        }

        Map<String, Long> skuPriceMap = new HashMap<>();
        List<CartItemChild> children = cartItem.getChilds();
        children.stream().filter(Objects::nonNull).forEachOrdered(child -> {
            Optional<ChildPriceInfo> batchInfoOptional = batchInfoList.stream()
                    .filter(batchInfo -> child.getSku().equals(String.valueOf(batchInfo.getSku()))).findAny();
            if (!batchInfoOptional.isPresent()) {
                return;
            }
            skuPriceMap.put(child.getSku(), batchInfoOptional.get().getPrice());
        });
        return skuPriceMap;
    }


    /**
     * 计算套装内子商品的价格
     *
     * @param cartItem 购物车项
     */
    public static void updateChildSellPrice(CartItem cartItem, long price) {
        List<CartItemChild> children = cartItem.getChilds();
        if (CollectionUtils.isEmpty(children)) {
            return;
        }

        // 计算按旧逻辑
        long totalSellPrice = CartHelper.getPackageSellPrice(cartItem);


        long cartPrice = price;


        long actualCartPrice = 0L;
        // 孩子的直降价是按sellPrice比例做分摊
        for (CartItemChild child : cartItem.getChilds()) {
            long childPrice = Math.floorDiv(cartPrice * child.getSellPrice(), totalSellPrice);
            child.setSellPrice(childPrice);
            actualCartPrice += childPrice;
        }
        long priceDiff = cartPrice - actualCartPrice;
        if (priceDiff == 0L) {
            return;
        }

        // 有分摊余额, 一分钱一分钱分摊
        long moneyDiff = 1;
        for (CartItemChild child : cartItem.getChilds()) {
            if (priceDiff <= 0) {
                return;
            }
            if (child.getSellPrice() >= child.getOriginalSellPrice()) {
                continue;
            }
            child.setSellPrice(child.getSellPrice() + moneyDiff);
            priceDiff -= moneyDiff;
        }
    }

    /**
     * 计算套装内子商品的价格
     *
     * @param cartItem 购物车项
     */
    public static void updateChildCheckoutPrice(CartItem cartItem) {
        List<CartItemChild> children = cartItem.getChilds();
        if (CollectionUtils.isEmpty(children)) {
            return;
        }

        // 计算按旧逻辑
        long totalSellPrice = CartHelper.getPackageSellPrice(cartItem);
        long checkoutPrice = cartItem.getCheckoutPrice();
        long actualCartPrice = 0L;
        // 孩子的直降价是按sellPrice比例做分摊
        for (CartItemChild child : cartItem.getChilds()) {
            long childCheckoutPrice = Math.floorDiv(checkoutPrice * child.getSellPrice(), totalSellPrice);
            child.setCheckoutPrice(childCheckoutPrice);
            actualCartPrice += childCheckoutPrice;
        }
        long priceDiff = checkoutPrice - actualCartPrice;
        if (priceDiff == 0L) {
            return;
        }

        // 有分摊余额, 一分钱一分钱分摊
        long moneyDiff = 1;
        for (CartItemChild child : cartItem.getChilds()) {
            if (priceDiff <= 0) {
                return;
            }
            if (child.getCheckoutPrice() >= child.getOriginalSellPrice()) {
                continue;
            }
            child.setCheckoutPrice(child.getCheckoutPrice() + moneyDiff);
            priceDiff -= moneyDiff;
        }
    }

    /**
     * 获取所有ssu当前总价
     *
     * @param cartList 购物车列表
     * @return 总价
     */
    public static long getTotalPriceBySsu(List<CartItem> cartList) {
        if (CollectionUtils.isEmpty(cartList)) {
            return 0L;
        }
        return cartList.stream()
                .filter(item -> !SourceEnum.isGift(item.getSource()))
                .mapToLong(item -> CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList()) * item.getCount())
                .sum();
    }

    /**
     * 判断购物车项中是否有耗材券的优惠明细
     *
     * @param cartItem 购物车项
     * @return 有耗材券返回true, 否则返回false
     */
    public static boolean hasConsumableCouponReduce(CartItem cartItem) {
        if (Objects.isNull(cartItem) || CollectionUtils.isEmpty(cartItem.getReduceItemList())) {
            return false;
        }

        return cartItem.getReduceItemList().stream()
                .filter(Objects::nonNull)
                .map(ReduceDetailItem::getExtend)
                .filter(Objects::nonNull)
                .map(extend -> GsonUtil.fromJson(extend, DeductCouponExtendDto.class))
                .filter(Objects::nonNull)
                .anyMatch(extendDto -> Objects.equals(CouponServiceTypeEnum.CONSUMABLES.getCode(), extendDto.getServiceType()));
    }

    /**
     * 获取购物车项单个商品的结算价格
     *
     * @param cartItem 购物车项
     * @return 单个商品的结算价格
     */
    public static long getCheckoutPriceSingle(CartItem cartItem) {
        long checkoutPriceAmount = itemCheckoutAmount(cartItem);
        return Math.floorDiv(checkoutPriceAmount, cartItem.getCount());
    }
}
