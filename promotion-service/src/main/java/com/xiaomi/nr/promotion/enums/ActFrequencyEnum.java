package com.xiaomi.nr.promotion.enums;

/**
 * 频次限制
 *
 * <AUTHOR>
 * @date 2021/4/13
 */
public enum ActFrequencyEnum {
    /**
     * 没有限制
     */
    NONE(1),
    /**
     * 本次活动总共只能参加一次
     */
    TOTAL(2),
    /**
     * 本次活动每天可以参加一次
     */
    DAILY(3),

    /**
     * 本次活动总共只能参加X次
     */
    TOTAL_TIMES(21);

    private Integer value;

    ActFrequencyEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    /**
     * 根据活动类型值获取活动类型枚举对象
     *
     * @param value 类型值
     * @return enum枚举对象
     */
    public static ActFrequencyEnum getByValue(int value) {
        ActFrequencyEnum[] values = ActFrequencyEnum.values();
        for (ActFrequencyEnum frequencyEnum : values) {
            if (value == frequencyEnum.value) {
                return frequencyEnum;
            }
        }
        return null;
    }
}
