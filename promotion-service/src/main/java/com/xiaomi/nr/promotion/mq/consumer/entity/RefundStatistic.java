package com.xiaomi.nr.promotion.mq.consumer.entity;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class RefundStatistic {

    @SerializedName("order_id")
    private Long orderId;

    @SerializedName("item_no")
    private Integer itemNo;

    @SerializedName("item_type")
    private Integer itemType;

    @SerializedName("goods_id")
    private Long goodsId;

    @SerializedName("all_goods_count")
    private Integer allGoodsCount;

    @SerializedName("refund_goods_count")
    private Integer refundGoodsCount;

    @SerializedName("valid_goods_count")
    private Integer validGoodsCount;

    @SerializedName("goods_amount")
    private Double goodsAmount;

    @SerializedName("total_refund_amount")
    private Double totalRefundAmount;

    @SerializedName("valid_sn")
    private List<String> validSn;

    @SerializedName("refund_items")
    private List<Object> refundItems;

    @SerializedName("partial_refund_details")
    private List<Object> partialRefundDetails;
}
