package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.StorePricePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 门店价条件判断
 * <AUTHOR>
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class StorePriceCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 活动类型
     */
    private PromotionToolType promotionType;
    /**
     * 门店价信息
     */
    private Map<String, ActPriceInfo> storePriceInfoMap;
    /**
     * 销售来源
     */
    private List<String> saleSources;
    /**
     * 活动密码
     */
    private String accessCode;


    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        Long uid = request.getUserId();
        if (MapUtil.isEmpty(storePriceInfoMap)) {
            log.error("storePriceInfoMap is empty. actId:{} uid:{}", promotionId, uid);
            return false;
        }

        List<CartItem> cartList = request.getCartList();
        String uidType = request.getUidType();
        String orgCode = request.getOrgCode();
        boolean online = StringUtils.isEmpty(orgCode);

        // 查找可以参加的购物车项
        List<GoodsIndex> indexList = findFillGoodsList(cartList, online, orgCode);

        // 没有符合的商品，不满足活动
        if (CollectionUtils.isEmpty(indexList)) {
            return false;
        }

        context.setGoodIndex(indexList);
        return true;
    }

    private List<GoodsIndex> findFillGoodsList(List<CartItem> cartList, boolean online, String orgCode) {
        List<GoodsIndex> indexList = new ArrayList<>();
        // 遍历购物车，将满足的商品放入
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            int activityType = promotionType.getTypeId();
            boolean itemQualify = CartHelper.checkItemActQualify(item, activityType, online, saleSources, accessCode);
            if (!itemQualify) {
                continue;
            }
            String skuPackage = CartHelper.getSkuPackage(item);
            if (!storePriceInfoMap.containsKey(skuPackage)) {
                continue;
            }
            GoodsIndex goodsIndex = new GoodsIndex(item.getItemId(), idx);
            indexList.add(goodsIndex);
        }
        return indexList;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof StorePricePromotionConfig)) {
            log.error("config is not instanceof StorePricePromotionConfig. config:{}", config);
            return;
        }
        StorePricePromotionConfig storePriceConfig = (StorePricePromotionConfig) config;
        this.promotionId = storePriceConfig.getPromotionId();
        this.storePriceInfoMap = storePriceConfig.getStorePriceInfoMap();
        this.promotionType = storePriceConfig.getPromotionType();
        this.saleSources = storePriceConfig.getSaleSources();
        this.accessCode = storePriceConfig.getAccessCode();
    }
}
