package com.xiaomi.nr.promotion.componet.action.carsale;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.componet.action.AbstractOnsaleAction;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.OnsaleJoin;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Created by wangweiyi on 2023/9/14
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarOnSaleAction extends AbstractOnsaleAction {

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {

        List<CartItem> cartList = request.getCartList();
        if (MapUtil.isEmpty(onsaleInfoMap)) {
            log.error("onsaleInfoMap is empty. actId:{} uid:{}", promotion, request.getUserId());
            return;
        }


        // 获取此次参加活动的商品
        List<GoodsIndex> indexList = context.getGoodIndex();

        // 计算参加商品的优惠
        Map<String, OnsaleCalcInfo> joinMap = calculateOnsaleJoinMap(cartList, indexList);

        // 改价
        changeCartPrice(cartList, joinMap);


        // 生成优惠分摊
        calcReduceItem(cartList, joinMap);


        // 设置结果
        Map<String, OnsaleJoin> onsaleJoinMap = convertToOldOnaleJoinMap(joinMap);
        setResult(context, cartList, promotion, onsaleJoinMap);
    }

    /**
     * 促销价修改
     * @param cartItemList
     * @param joinMap
     */
    private void changeCartPrice(List<CartItem> cartItemList, Map<String, OnsaleCalcInfo> joinMap) {
        for (CartItem cartItem : cartItemList) {
            if (!joinMap.containsKey(cartItem.getItemId())) {
                continue;
            }
            cartItem.setCartPrice(joinMap.get(cartItem.getItemId()).getOnsalePrice());
        }
    }


    private void calcReduceItem(List<CartItem> cartItemList, Map<String, OnsaleCalcInfo> joinMap) {
        for (CartItem item : cartItemList) {

            String productId = getJoinGoods(item);
            if (!joinMap.containsKey(item.getItemId())) {
                continue;
            }
            //处理互斥
            List<ReduceDetailItem> reduceItemList = Optional.ofNullable(item.getReduceItemList()).orElse(Lists.newArrayList());

            //TODO 仅处理同类型互斥，后续处理其他互斥
            reduceItemList.removeIf(reduceItem -> Objects.equals(reduceItem.getPromotionType(), promotionType.getTypeId()));



            OnsaleCalcInfo onsaleCalcInfo = joinMap.get(item.getItemId());

            ReduceDetailItem detailItem  = initReduceDetailItem(promotionId, promotionType, item.getSsuId(),
                    onsaleCalcInfo.getBeforeOnsalePrice(), onsaleCalcInfo.getOnsalePrice(), item.getCount());
            // 预算信息填充
            ActPriceInfo onSaleInfo = onsaleInfoMap.get(productId);
            if (onSaleInfo!=null){
                detailItem.setBudgetApplyNo(onSaleInfo.getBudgetApplyNo());
                detailItem.setLineNum(onSaleInfo.getLineNum());
            }
            reduceItemList.add(detailItem);

            item.setReduceItemList(reduceItemList);
        }
    }



    private Map<String, OnsaleJoin> convertToOldOnaleJoinMap(Map<String, OnsaleCalcInfo> joinMap) {
        Map<String, OnsaleJoin> oldJoinMap = new HashMap<>();
        joinMap.forEach((itemId, onsaleCalcInfo) -> {
            String ssuId = String.valueOf(onsaleCalcInfo.getProductId());
            if (oldJoinMap.containsKey(ssuId)) {
                //累加参加个数
                OnsaleJoin onsaleJoin = oldJoinMap.get(ssuId);
                int newCount = onsaleJoin.getJoinCounts() + onsaleCalcInfo.getJoinCounts();
                onsaleJoin.setJoinCounts(newCount);
            } else {

                OnsaleJoin onSaleJoin = new OnsaleJoin();
                onSaleJoin.setJoinCounts(onsaleCalcInfo.getJoinCounts());
                onSaleJoin.setLimitRule(onsaleCalcInfo.getLimitRule());
                onSaleJoin.setSkuOrPackage(onsaleCalcInfo.getProductId());
                oldJoinMap.put(ssuId, onSaleJoin);
            }

        });
        return oldJoinMap;
    }

    /**
     * 生成参加活动商品的直降信息
     * @param cartList
     * @param indexList
     * @return key:购物车itemId
     */
    private Map<String, OnsaleCalcInfo> calculateOnsaleJoinMap(List<CartItem> cartList, List<GoodsIndex> indexList) {

        final Map<String, OnsaleCalcInfo> joinMap = new HashMap<>();
        for (CartItem item : cartList) {
            // 判断当前商品是否可以参加直降
            boolean canJoin = canJoinOnSaleAct(item, indexList);
            if (!canJoin) {
                continue;
            }
            String productId = getJoinGoods(item);
            // 获取可以参加的直降活动(onSaleInfo == nil代表没获取到)
            ActPriceInfo onSaleInfo = onsaleInfoMap.get(productId);
            if (onSaleInfo == null) {
                log.error("onSaleInfo is null. cart:{}", item);
                continue;
            }

            // 直降后的价格不能小于等于0
            Long onsalePrice = onSaleInfo.getPrice();
            if (onsalePrice < 0L) {
                log.warn("onSaleInfo lowerPrice < 0, onSaleInfo:{} cart:{} ", onSaleInfo, item);
                continue;
            }

            // 排除其他直降，计算当前价格
            Long curPrice = CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList(), promotionType);
            if (curPrice <= 0) {
                log.warn("curPrice is zero, curPrice:{}", curPrice);
                continue;
            }
            if (curPrice < onsalePrice) {
                log.warn("onSaleInfo lowerPrice >= curPrice, onSaleInfo:{} cart:{} ", onSaleInfo, item);
                continue;
            }

//
            OnsaleCalcInfo onsaleCalcInfo = new OnsaleCalcInfo();
            onsaleCalcInfo.setCartItemId(item.getItemId());
            onsaleCalcInfo.setProductId(item.getSsuId());
            onsaleCalcInfo.setBeforeOnsalePrice(curPrice);
            onsaleCalcInfo.setOnsalePrice(onsalePrice);
            onsaleCalcInfo.setJoinCounts(item.getCount());
            onsaleCalcInfo.setLimitRule(onSaleInfo.getLimitRule());
            joinMap.put(item.getItemId(), onsaleCalcInfo);

        }
        return joinMap;
    }

    @Override
    public String getJoinGoods(CartItem cartItem) {

        return String.valueOf(cartItem.getSsuId());
    }
}
