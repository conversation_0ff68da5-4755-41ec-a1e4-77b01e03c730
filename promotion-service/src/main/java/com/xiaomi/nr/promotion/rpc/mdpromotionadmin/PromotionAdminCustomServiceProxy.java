package com.xiaomi.nr.promotion.rpc.mdpromotionadmin;

import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.xiaomi.nr.md.promotion.admin.api.constant.ActivityStatusEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.ChannelEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.PromotionTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.SourceAppEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityFilterTip;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.SimpleActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.params.ActivityConfigForPromotionResponse;
import com.xiaomi.nr.md.promotion.admin.api.dto.params.ActivityListQueryRequest;
import com.xiaomi.nr.md.promotion.admin.api.dto.params.ActivityQueryRequest;
import com.xiaomi.nr.md.promotion.admin.api.dto.params.PageResponse;
import com.xiaomi.nr.md.promotion.admin.api.service.publish.PromotionAdminCustomService;
import com.xiaomi.nr.md.promotion.admin.api.util.CompressUtil;
import com.xiaomi.nr.promotion.util.DateTimeUtil;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.RpcResultValidator;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 新促销中台后台服务
 *
 * <AUTHOR>
 * @date 2023/2/9
 */
@Component
@Slf4j
public class PromotionAdminCustomServiceProxy {

    @Reference(interfaceClass = PromotionAdminCustomService.class, timeout = 5000, group = "${md.promotion.admin.dubbo.group}")
    private PromotionAdminCustomService promotionAdminCustomService;

    private static final long SEVENS_DAYS_TO_SECONDS = 8 * 24 * 3600L;

    /**
     * 获取进行中和即将开始所有活动
     *
     * @return 活动信息
     */
    public List<ActivityConfig> queryActivityList() {
        int pageNo = 1;
        int pageSize = 50;
        long timeNow = DateTimeUtil.getCurrentTimes(TimeUnit.SECONDS);
        // 预获取
        PageResponse<ActivityConfig> pageResponse = pageQueryActivityList(timeNow, pageNo, pageSize);
        if (pageResponse == null || CollectionUtils.isEmpty(pageResponse.getData())) {
            return Lists.newArrayList();
        }
        List<ActivityConfig> configList = Lists.newArrayList();
        configList.addAll(pageResponse.getData());

        // 确定数量
        long totalCount = pageResponse.getCount();
        long totalPage = totalCount / pageSize + 1;
        if (totalPage <= 1) {
            return configList;
        }
        // 开始批量拉取
        for (pageNo = pageNo + 1; pageNo <= totalPage; pageNo++) {
            pageResponse = pageQueryActivityList(timeNow, pageNo, pageSize);
            if (pageResponse == null || CollectionUtils.isEmpty(pageResponse.getData())) {
                break;
            }
            configList.addAll(pageResponse.getData());
        }
        return configList;
    }
    
    
    /**
     * 获取进行中和即将开始所有活动
     *
     * @return 活动信息
     */
    public List<ActivityConfig> queryCarShopActivityList() {
        int pageNo = 1;
        int pageSize = 100;
        long timeNow = DateTimeUtil.getCurrentTimes(TimeUnit.SECONDS);
        // 预获取
        PageResponse<ActivityConfig> pageResponse = pageQueryCarShopActivityList(timeNow, pageNo, pageSize);
        if (pageResponse == null || CollectionUtils.isEmpty(pageResponse.getData())) {
            return Lists.newArrayList();
        }
        List<ActivityConfig> configList = Lists.newArrayList();
        configList.addAll(pageResponse.getData());
        
        // 确定数量
        long totalCount = pageResponse.getCount();
        long totalPage = totalCount / pageSize + 1;
        if (totalPage <= 1) {
            return configList;
        }
        // 开始批量拉取
        for (pageNo = pageNo + 1; pageNo <= totalPage; pageNo++) {
            pageResponse = pageQueryCarShopActivityList(timeNow, pageNo, pageSize);
            if (pageResponse == null || CollectionUtils.isEmpty(pageResponse.getData())) {
                break;
            }
            configList.addAll(pageResponse.getData());
        }
        return configList;
    }

    /**
     * 获取活动页数据
     *
     * @param timeNow  当前时间
     * @param pageNo   页码
     * @param pageSize 每页数量
     * @return 响应数据
     */
    public PageResponse<ActivityConfig> pageQueryActivityList(Long timeNow, Integer pageNo, Integer pageSize) {
        ActivityListQueryRequest request = new ActivityListQueryRequest();
        request.setSourceApps(Arrays.asList(
                SourceAppEnum.CRM.code,
                SourceAppEnum.B2T_ADMIN.code));
        request.setPromotionTypes(Arrays.asList(
                PromotionTypeEnum.B2T_CHANNEL_PRICE.code,
                PromotionTypeEnum.B2T_STEP_PRICE.code,
                PromotionTypeEnum.B2T_VIP_DISCOUNT.code,
                PromotionTypeEnum.RANGE_REDUCE.code,
                PromotionTypeEnum.ONSALE.code,
                PromotionTypeEnum.EXCHANGE_SUBSIDY.code,
                PromotionTypeEnum.ORDER_REDUCE.code));
        request.setChannels(Lists.newArrayList(
                ChannelEnum.B2T_C_CUSTOMER.value,
                ChannelEnum.B2T_GOV_BIG_CUSTOMER.value,
                ChannelEnum.B2T_MIJIA_BIG_CUSTOMER.value,
                ChannelEnum.CAR_VEHICLE.value));
        request.setBeginTime(timeNow);
        request.setStatus(ActivityStatusEnum.ONLINE.code);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        Result<PageResponse<ActivityConfig>> result;
        long startTime = System.currentTimeMillis();
        try {
            result = promotionAdminCustomService.pageQueryActivityAccurately(request);
        } catch (Exception e) {
            log.error("invoke rpc orderCloseCreditRecycleNotifyMario error. request:{}, err", request, e);
            return new PageResponse<>();
        }
        // 处理响应
        log.info("invoke rpc pageQueryActivityList. request:{} result:{} ws:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), System.currentTimeMillis() - startTime);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc pageQueryActivityList fail. request:{}, code:{} message:{}", request, result.getCode(), result.getMessage());
            return new PageResponse<>();
        }
        return result.getData();
    }
    
    /**
     * 获取活动页数据
     *
     * @param timeNow  当前时间
     * @param pageNo   页码
     * @param pageSize 每页数量
     * @return 响应数据
     */
    public PageResponse<ActivityConfig> pageQueryCarShopActivityList(Long timeNow, Integer pageNo, Integer pageSize) {
        ActivityListQueryRequest request = new ActivityListQueryRequest();
        request.setSourceApps(Arrays.asList(
                SourceAppEnum.PROMOTION_ADMIN.code));
        request.setPromotionTypes(Arrays.asList(
                PromotionTypeEnum.ONSALE.code,
                PromotionTypeEnum.BUY_GIFT.code,
                PromotionTypeEnum.BARGAIN.code));
        request.setChannels(Lists.newArrayList(
                ChannelEnum.CAR_SHOP.value));
        request.setBeginTime(timeNow);
        request.setStatus(ActivityStatusEnum.ONLINE.code);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        Result<PageResponse<ActivityConfig>> result;
        long startTime = System.currentTimeMillis();
        try {
            result = promotionAdminCustomService.pageQueryActivityAccurately(request);
        } catch (Exception e) {
            log.error("invoke rpc orderCloseCreditRecycleNotifyMario error. request:{}, err", request, e);
            return new PageResponse<>();
        }
        // 处理响应
        log.info("invoke rpc pageQueryActivityList. request:{} result:{} ws:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), System.currentTimeMillis() - startTime);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc pageQueryActivityList fail. request:{}, code:{} message:{}", request, result.getCode(), result.getMessage());
            return new PageResponse<>();
        }
        return result.getData();
    }

    /**
     * 获取2024北京换新补贴政策活动
     *
     * @param timeNow  当前时间
     * @param pageNo   页码
     * @param pageSize 每页数量
     * @return 响应数据
     */
    public List<ActivityConfig> pageQuerySubsityActivityList(Long timeNow, Integer pageNo, Integer pageSize) {
        ActivityListQueryRequest request = new ActivityListQueryRequest();
        request.setSourceApps(Arrays.asList(
                SourceAppEnum.PROMOTION_ADMIN.code));
        request.setPromotionTypes(Arrays.asList(
                PromotionTypeEnum.NEW_PURCHASE_SUBSIDY.code,
                PromotionTypeEnum.UPGRADE_PURCHASE_SUBSIDY.code));
        request.setChannels(Lists.newArrayList(
                ChannelEnum.MISHOP.value,
                ChannelEnum.DIRECT.value));
        request.setBeginTime(timeNow);
        request.setStatus(ActivityStatusEnum.ONLINE.code);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        Result<PageResponse<ActivityConfig>> result;
        long startTime = System.currentTimeMillis();
        try {
            result = promotionAdminCustomService.pageQueryActivityAccurately(request);
        } catch (Exception e) {
            log.error("invoke rpc orderCloseCreditRecycleNotifyMario error. request:{}, err", request, e);
            return Lists.newArrayList();
        }
        // 处理响应
        log.info("invoke rpc pageQueryActivityList. request:{} result:{} ws:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), System.currentTimeMillis() - startTime);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc pageQueryActivityList fail. request:{}, code:{} message:{}", request, result.getCode(), result.getMessage());
            return Lists.newArrayList();
        }
        return result.getData().getData();
    }

    /**
     * 获取汽车、车商城进行中和即将开始所有活动
     *
     * @return 活动信息
     */
    public List<ActivityConfig> queryCarActivityList(List<Integer> sources, List<Integer> promotionTypes, List<Integer> channels) throws BizError {
        int pageNo = 1;
        int pageSize = 50;
        long timeNow = DateTimeUtil.getCurrentTimes(TimeUnit.SECONDS);
        // 预获取
        PageResponse<ActivityConfig> pageResponse = pageQueryCarActivityList(timeNow, pageNo, pageSize, sources, promotionTypes, channels);
        if (pageResponse == null || pageResponse.getData() == null) {
            throw ExceptionHelper.create(GeneralCodes.InternalError, "拉取数据异常");
        }
        if (CollectionUtils.isEmpty(pageResponse.getData())) {
            return Lists.newArrayList();
        }
        List<ActivityConfig> configList = Lists.newArrayList();
        configList.addAll(pageResponse.getData());

        // 确定数量
        long totalCount = pageResponse.getCount();
        long totalPage = totalCount / pageSize + 1;
        if (totalPage <= 1) {
            return configList;
        }
        // 开始批量拉取
        for (pageNo = pageNo + 1; pageNo <= totalPage; pageNo++) {
            pageResponse = pageQueryCarActivityList(timeNow, pageNo, pageSize, sources, promotionTypes, channels);
            if (pageResponse == null || pageResponse.getData() == null) {
                throw ExceptionHelper.create(GeneralCodes.InternalError, "拉取数据异常");
            }
            if (CollectionUtils.isEmpty(pageResponse.getData())) {
                break;
            }
            configList.addAll(pageResponse.getData());
        }
        return configList;
    }

    /**
     * 获取汽车、车商城进行中和即将开始所有活动
     *
     * @return 活动信息
     */
    public List<ActivityConfig> queryCarActivityListV2(List<Integer> sources, List<Integer> promotionTypes, List<Integer> channels) throws BizError {
        int pageNo = 1;
        int pageSize = 100;
        long timeNow = DateTimeUtil.getCurrentTimes(TimeUnit.SECONDS);

        List<ActivityConfig> configList = Lists.newArrayList();
        long maxCount = 0;
        boolean isContinue = true;

        do {
            // 预获取
            PageResponse<ActivityConfig> pageResponse = pageQueryCarActivityListV2(timeNow, pageNo, pageSize, sources,
                    promotionTypes, channels);

            if (pageResponse == null || pageResponse.getData() == null) {
                throw ExceptionHelper.create(GeneralCodes.InternalError, "拉取数据异常");
            }
            if (CollectionUtils.isEmpty(pageResponse.getData())) {
                isContinue = false;
            } else {
                maxCount = Math.max(maxCount, pageResponse.getCount());
                pageNo++;
                configList.addAll(pageResponse.getData());
            }

            // 防止无限循环
            if ((long) pageNo * pageSize > maxCount + pageSize * 5) {
                break;
            }

        } while (isContinue);

        return configList;
    }

    /**
     * 获取汽车、车商城活动页数据
     *
     * @param timeNow  当前时间
     * @param pageNo   页码
     * @param pageSize 每页数量
     * @return 响应数据
     */
    public PageResponse<ActivityConfig> pageQueryCarActivityList(Long timeNow, Integer pageNo, Integer pageSize, List<Integer> sources, List<Integer> promotionTypes, List<Integer> channels) throws BizError {
        ActivityListQueryRequest request = new ActivityListQueryRequest();
        request.setSourceApps(sources);
        request.setPromotionTypes(promotionTypes);
        request.setChannels(channels);
        request.setBeginTime(timeNow);
        request.setStatus(ActivityStatusEnum.ONLINE.code);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        Result<PageResponse<ActivityConfig>> result;
        long startTime = System.currentTimeMillis();
        try {
            result = promotionAdminCustomService.pageQueryActivityAccurately(request);
        } catch (Exception e) {
            log.error("invoke rpc orderCloseCreditRecycleNotifyMario error. request:{}, err", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "数据拉取异常");
        }

        if (result.getCode() != GeneralCodes.OK.getCode() || Objects.isNull(result.getData())) {
            log.error("invoke rpc pageQueryCarActivityList fail. request:{}, code:{} message:{}", request, result.getCode(), result.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "数据拉取异常");
        }

        // 处理响应
        log.info("invoke rpc pageQueryCarActivityList request:{} result:{} ws:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), System.currentTimeMillis() - startTime);
        return result.getData();
    }

    public PageResponse<ActivityConfig> pageQueryCarActivityListV2(Long timeNow, Integer pageNo, Integer pageSize, List<Integer> sources, List<Integer> promotionTypes, List<Integer> channels) throws BizError {
        ActivityListQueryRequest request = new ActivityListQueryRequest();
        request.setSort(null);
        request.setSourceApps(sources);
        request.setPromotionTypes(promotionTypes);
        request.setChannels(channels);
        request.setBeginTime(timeNow);
        request.setEndTime(timeNow + SEVENS_DAYS_TO_SECONDS);
        request.setStatus(ActivityStatusEnum.ONLINE.code);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        long startTime = System.currentTimeMillis();
        Result<byte[]> result;
        PageResponse<ActivityConfig> response;
        try {
            result = promotionAdminCustomService.pageQueryActivityAccuratelyV3(request);
            String decompress = CompressUtil.decompress(result.getData());
            response = com.xiaomi.newretail.common.tools.utils.GsonUtil.parseObj(decompress,
                    new TypeToken<PageResponse<ActivityConfig>>(){}.getType());
        } catch (Exception e) {
            log.error("invoke rpc pageQueryCarActivityListV2 error. request:{}, err", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "数据拉取异常");
        }
        // 处理响应
        log.info("invoke rpc pageQueryCarActivityListV2 request:{} result:{} ws:{}", GsonUtil.toJson(request),
                GsonUtil.toJson(response), System.currentTimeMillis() - startTime);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc pageQueryCarActivityListV2 fail. request:{}, code:{} message:{}", request, result.getCode(), result.getMessage());
            return new PageResponse<>();
        }
        return response;
    }

    public ActivityConfigForPromotionResponse queryActivityBySeqId(Long seqId, Map<Integer, ActivityFilterTip> activityFilterTips) {
        long startTime = System.currentTimeMillis();
        long now = startTime / 1000;
        ActivityListQueryRequest request = new ActivityListQueryRequest();
        request.setBeginTime(now);
        request.setEndTime(now + SEVENS_DAYS_TO_SECONDS);
        request.setSequenceId(seqId);
        request.setActivityFilterTips(activityFilterTips);

        Result<ActivityConfigForPromotionResponse> result = null;
        try {
            result = promotionAdminCustomService.queryActivityBySeqId(request);
            log.info("invoke rpc queryActivityAfterSeq. request:{} result:{} ws:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), System.currentTimeMillis() - startTime);
            RpcResultValidator.validate(result, "请求活动信息失败");
            return result.getData();
        } catch (Exception e) {
            log.error("invoke rpc queryActivityLisByActIds fail. request:{}, result:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), e);
        }
        return null;
    }

    /**
     * 获取有效的活动ids
     *
     * @param timeNow
     * @param sources
     * @param promotionTypes
     * @param channels
     * @return
     */
    public List<Long> queryEffectiveActivityIds(Long timeNow,
                                                List<Integer> sources,
                                                List<Integer> promotionTypes,
                                                List<Integer> channels) {
        ActivityQueryRequest r = new ActivityQueryRequest();
        r.setSourceApps(sources);
        r.setPromotionTypes(promotionTypes);
        r.setChannels(channels);
        long startTime = timeNow / 1000;
        r.setBeginTime(startTime);
        r.setEndTime(startTime + SEVENS_DAYS_TO_SECONDS);
        r.setStatus(ActivityStatusEnum.ONLINE.code);
        List<Long> ids = null;
        try {
            Result<List<Long>> ret = promotionAdminCustomService.queryActivityIdList(r);
            if (ret.getCode() != GeneralCodes.OK.getCode()) {
                throw new RpcException("获取活动id不成功");
            }
            // dubbo 反序列化实际为List<BigInteger>
            ids = Optional.ofNullable(ret.getData())
                    .filter(CollectionUtils::isNotEmpty)
                    .map(GsonUtil::toJson)
                    .map(json -> GsonUtil.fromListJson(json, Long.class))
                    .orElse(Collections.emptyList());
        } catch (Exception e) {
            log.info("invoke rpc queryEffectiveActivityIds. request:{}, e", r, e);
            throw e;
        }
        return ids;
    }

    /**
     * 根据ids获取活动配置
     *
     * @param ids
     * @return
     */
    public List<SimpleActivityConfig> querySimpleActivitiesByIds(Collection<Long> ids) {
        List<SimpleActivityConfig> activities = null;
        try {
            Result<List<SimpleActivityConfig>> result =
                    promotionAdminCustomService.querySimpleActivityConfigFromSlave(new ArrayList<>(ids));
            if (result.getCode() != GeneralCodes.OK.getCode()) {
                throw new RpcException("获取活动不成功");
            }
            activities = result.getData();
            log.info("invoke rpc queryEffectiveActivityIds. request:{}, response:{}", ids, GsonUtil.toJson(activities));
        } catch (Exception e) {
            log.error("invoke rpc queryActivitiesByIds. request:{}, e", ids, e);
            throw e;
        }
        return activities;
    }

    public ActivityConfigForPromotionResponse queryActivityBySeqIdV2(Long seqId, Map<Integer, ActivityFilterTip> activityFilterTips) {
        long startTime = System.currentTimeMillis();
        long now = startTime / 1000;
        ActivityListQueryRequest request = new ActivityListQueryRequest();
        request.setBeginTime(now);
        request.setEndTime(now + SEVENS_DAYS_TO_SECONDS);
        request.setSequenceId(seqId);
        request.setActivityFilterTips(activityFilterTips);

        Result<ActivityConfigForPromotionResponse> result = null;
        try {
            result = promotionAdminCustomService.queryActivityBySeqIdV2(request);
            log.info("invoke rpc queryActivityBySeqIdV2. request:{} result:{} ws:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), System.currentTimeMillis() - startTime);
            RpcResultValidator.validate(result, "请求活动信息失败");
            return result.getData();
        } catch (Exception e) {
            log.error("invoke rpc queryActivityBySeqIdV2 fail. request:{}, result:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), e);
        }
        return null;
    }
}
