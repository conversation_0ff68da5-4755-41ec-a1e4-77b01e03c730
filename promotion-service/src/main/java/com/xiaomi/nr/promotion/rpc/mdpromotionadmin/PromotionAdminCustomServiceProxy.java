package com.xiaomi.nr.promotion.rpc.mdpromotionadmin;

import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.xiaomi.nr.md.promotion.admin.api.constant.ActivityStatusEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.ChannelEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.PromotionTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.SourceAppEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityFilterTip;
import com.xiaomi.nr.md.promotion.admin.api.dto.params.ActivityConfigForPromotionResponse;
import com.xiaomi.nr.md.promotion.admin.api.dto.params.ActivityListQueryRequest;
import com.xiaomi.nr.md.promotion.admin.api.dto.params.PageResponse;
import com.xiaomi.nr.md.promotion.admin.api.service.publish.PromotionAdminCustomService;
import com.xiaomi.nr.md.promotion.admin.api.util.CompressUtil;
import com.xiaomi.nr.promotion.util.DateTimeUtil;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.RpcResultValidator;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 新促销中台后台服务
 *
 * <AUTHOR>
 * @date 2023/2/9
 */
@Component
@Slf4j
public class PromotionAdminCustomServiceProxy {

    @Reference(interfaceClass = PromotionAdminCustomService.class, timeout = 5000, group = "${md.promotion.admin.dubbo.group}")
    private PromotionAdminCustomService promotionAdminCustomService;

    private static final long SEVENS_DAYS_TO_SECONDS = 8 * 24 * 3600L;

    /**
     * 获取进行中和即将开始所有活动
     *
     * @return 活动信息
     */
    public List<ActivityConfig> queryActivityList() {
        int pageNo = 1;
        int pageSize = 50;
        long timeNow = DateTimeUtil.getCurrentTimes(TimeUnit.SECONDS);
        // 预获取
        PageResponse<ActivityConfig> pageResponse = pageQueryActivityList(timeNow, pageNo, pageSize);
        if (pageResponse == null || CollectionUtils.isEmpty(pageResponse.getData())) {
            return Collections.emptyList();
        }
        List<ActivityConfig> configList = Lists.newArrayList();
        configList.addAll(pageResponse.getData());

        // 确定数量
        long totalCount = pageResponse.getCount();
        long totalPage = totalCount / pageSize + 1;
        if (totalPage <= 1) {
            return configList;
        }
        // 开始批量拉取
        for (pageNo = pageNo + 1; pageNo <= totalPage; pageNo++) {
            pageResponse = pageQueryActivityList(timeNow, pageNo, pageSize);
            if (pageResponse == null || CollectionUtils.isEmpty(pageResponse.getData())) {
                break;
            }
            configList.addAll(pageResponse.getData());
        }
        return configList;
    }

    /**
     * 获取进行中和即将开始所有活动
     *
     * @return 活动信息
     */
    public List<ActivityConfig> queryActivityListV2() {
        int pageNo = 1;
        int pageSize = 100;
        long timeNow = DateTimeUtil.getCurrentTimes(TimeUnit.SECONDS);

        List<ActivityConfig> configList = new ArrayList<>();
        List<ActivityConfig> tmpData = null;

        do {
            PageResponse<ActivityConfig> pageResponse = pageQueryActivityListV2(timeNow, pageNo, pageSize);
            if (pageResponse != null && CollectionUtils.isNotEmpty(pageResponse.getData())) {
                tmpData = pageResponse.getData();
                configList.addAll(tmpData);
            }
            pageNo++;
        } while (tmpData != null && tmpData.size() == pageSize);

        return configList;
    }

    /**
     * 获取活动页数据
     *
     * @param timeNow  当前时间
     * @param pageNo   页码
     * @param pageSize 每页数量
     * @return 响应数据
     */
    public PageResponse<ActivityConfig> pageQueryActivityList(Long timeNow, Integer pageNo, Integer pageSize) {
        ActivityListQueryRequest request = new ActivityListQueryRequest();
        request.setSourceApps(Arrays.asList(
                SourceAppEnum.CRM.code,
                SourceAppEnum.B2T_ADMIN.code));
        request.setPromotionTypes(Arrays.asList(
                PromotionTypeEnum.B2T_CHANNEL_PRICE.code,
                PromotionTypeEnum.B2T_STEP_PRICE.code,
                PromotionTypeEnum.B2T_VIP_DISCOUNT.code));
        request.setChannels(Lists.newArrayList(
                ChannelEnum.B2T_C_CUSTOMER.value,
                ChannelEnum.B2T_GOV_BIG_CUSTOMER.value,
                ChannelEnum.B2T_MIJIA_BIG_CUSTOMER.value));
        request.setBeginTime(timeNow);
        request.setStatus(ActivityStatusEnum.ONLINE.code);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        Result<PageResponse<ActivityConfig>> result;
        long startTime = System.currentTimeMillis();
        try {
            result = promotionAdminCustomService.pageQueryActivityAccurately(request);
        } catch (Exception e) {
            log.error("invoke rpc orderCloseCreditRecycleNotifyMario error. request:{}, err", request, e);
            return new PageResponse<>();
        }
        // 处理响应
        log.info("invoke rpc pageQueryActivityList. request:{} result:{} ws:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), System.currentTimeMillis() - startTime);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc pageQueryActivityList fail. request:{}, code:{} message:{}", request, result.getCode(), result.getMessage());
            return new PageResponse<>();
        }
        return result.getData();
    }

    /**
     * 获取2024北京换新补贴政策活动
     *
     * @param timeNow  当前时间
     * @param pageNo   页码
     * @param pageSize 每页数量
     * @return 响应数据
     */
    public List<ActivityConfig> pageQuerySubsityActivityList(Long timeNow, Integer pageNo, Integer pageSize) {
        ActivityListQueryRequest request = new ActivityListQueryRequest();
        request.setSourceApps(Arrays.asList(
                SourceAppEnum.PROMOTION_ADMIN.code));
        request.setPromotionTypes(Arrays.asList(
                PromotionTypeEnum.GOVERNMENT_SUBSIDY.code));
        request.setChannels(Lists.newArrayList(
                ChannelEnum.MISHOP.value,
                ChannelEnum.DIRECT.value));
        request.setBeginTime(timeNow);
        request.setStatus(ActivityStatusEnum.ONLINE.code);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        Result<PageResponse<ActivityConfig>> result;
        long startTime = System.currentTimeMillis();
        try {
            result = promotionAdminCustomService.pageQueryActivityAccurately(request);
        } catch (Exception e) {
            log.error("invoke rpc orderCloseCreditRecycleNotifyMario error. request:{}, err", request, e);
            return Lists.newArrayList();
        }
        // 处理响应
        log.info("invoke rpc pageQueryActivityList. request:{} result:{} ws:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), System.currentTimeMillis() - startTime);
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("invoke rpc pageQueryActivityList fail. request:{}, code:{} message:{}", request, result.getCode(), result.getMessage());
            return Lists.newArrayList();
        }
        return result.getData().getData();
    }

    /**
     * 获取活动页数据
     *
     * @param timeNow  当前时间
     * @param pageNo   页码
     * @param pageSize 每页数量
     * @return 响应数据
     */
    public PageResponse<ActivityConfig> pageQueryActivityListV2(Long timeNow, Integer pageNo, Integer pageSize) {
        ActivityListQueryRequest request = new ActivityListQueryRequest();
        request.setSort(null);
        request.setSourceApps(Arrays.asList(
                SourceAppEnum.CRM.code,
                SourceAppEnum.B2T_ADMIN.code));
        request.setPromotionTypes(Arrays.asList(
                PromotionTypeEnum.B2T_CHANNEL_PRICE.code,
                PromotionTypeEnum.B2T_STEP_PRICE.code,
                PromotionTypeEnum.B2T_VIP_DISCOUNT.code));
        request.setChannels(Lists.newArrayList(
                ChannelEnum.B2T_C_CUSTOMER.value,
                ChannelEnum.B2T_GOV_BIG_CUSTOMER.value,
                ChannelEnum.B2T_MIJIA_BIG_CUSTOMER.value));
        request.setBeginTime(timeNow);
        request.setEndTime(timeNow + SEVENS_DAYS_TO_SECONDS);
        request.setStatus(ActivityStatusEnum.ONLINE.code);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        long startTime = System.currentTimeMillis();
        try {
            Result<byte[]> result = promotionAdminCustomService.pageQueryActivityAccuratelyV3(request);
            // 处理响应
            if (result.getCode() != GeneralCodes.OK.getCode()) {
                log.error("invoke rpc pageQueryActivityListV2 fail. request:{}, code:{} message:{}", request, result.getCode(), result.getMessage());
                return new PageResponse<>();
            }
            String dataJson = CompressUtil.decompress(result.getData());
            log.info("invoke rpc pageQueryActivityListV2. request:{} result:{} ws:{}", GsonUtil.toJson(request), dataJson, System.currentTimeMillis() - startTime);
            return com.xiaomi.newretail.common.tools.utils.GsonUtil.parseObj(dataJson, new TypeToken<PageResponse<ActivityConfig>>(){}.getType());
        } catch (Exception e) {
            log.error("invoke rpc pageQueryActivityListV2 error. request:{}, err", request, e);
            return new PageResponse<>();
        }
    }

    public List<ActivityConfig> querySubsityActivityListV2() {
        int pageNo = 1;
        int pageSize = 100;
        long timeNow = DateTimeUtil.getCurrentTimes(TimeUnit.SECONDS);

        List<ActivityConfig> configList = new ArrayList<>();
        List<ActivityConfig> tmpData = null;

        do {
            PageResponse<ActivityConfig> pageResponse = pageQuerySubsityActivityListV2(timeNow, pageNo, pageSize);
            if (pageResponse != null && CollectionUtils.isNotEmpty(pageResponse.getData())) {
                tmpData = pageResponse.getData();
                configList.addAll(tmpData);
            }
            pageNo++;
        } while (tmpData != null && tmpData.size() == pageSize);

        return configList;
    }

    /**
     * 获取2024北京换新补贴政策活动
     *
     * @param timeNow  当前时间
     * @param pageNo   页码
     * @param pageSize 每页数量
     * @return 响应数据
     */
    public PageResponse<ActivityConfig> pageQuerySubsityActivityListV2(Long timeNow, Integer pageNo, Integer pageSize) {
        ActivityListQueryRequest request = new ActivityListQueryRequest();
        request.setSort(null);
        request.setSourceApps(Arrays.asList(
                SourceAppEnum.PROMOTION_ADMIN.code));
        request.setPromotionTypes(Arrays.asList(
                PromotionTypeEnum.GOVERNMENT_SUBSIDY.code));
        request.setChannels(Lists.newArrayList(
                ChannelEnum.MISHOP.value,
                ChannelEnum.DIRECT.value));
        request.setBeginTime(timeNow);
        request.setStatus(ActivityStatusEnum.ONLINE.code);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        long startTime = System.currentTimeMillis();
        try {
            Result<byte[]> result = promotionAdminCustomService.pageQueryActivityAccuratelyV3(request);
            // 处理响应
            if (result.getCode() != GeneralCodes.OK.getCode()) {
                log.error("invoke rpc pageQuerySubsityActivityListV2 fail. request:{}, code:{} message:{}", request, result.getCode(), result.getMessage());
                return new PageResponse<>();
            }
            String dataJson = CompressUtil.decompress(result.getData());
            log.info("invoke rpc pageQuerySubsityActivityListV2. request:{} result:{} ws:{}", GsonUtil.toJson(request), dataJson, System.currentTimeMillis() - startTime);
            return com.xiaomi.newretail.common.tools.utils.GsonUtil.parseObj(dataJson, new TypeToken<PageResponse<ActivityConfig>>() {}.getType());
        } catch (Exception e) {
            log.error("invoke rpc pageQuerySubsityActivityListV2 error. request:{}, err", request, e);
            return new PageResponse<>();
        }
    }

    public ActivityConfigForPromotionResponse queryActivityBySeqIdV2(Long seqId, Map<Integer, ActivityFilterTip> activityFilterTips) {
        long startTime = System.currentTimeMillis();
        long now = startTime / 1000;
        ActivityListQueryRequest request = new ActivityListQueryRequest();
        request.setBeginTime(now);
        request.setEndTime(now + SEVENS_DAYS_TO_SECONDS);
        request.setSequenceId(seqId);
        request.setActivityFilterTips(activityFilterTips);

        Result<ActivityConfigForPromotionResponse> result = null;
        try {
            result = promotionAdminCustomService.queryActivityBySeqIdV2(request);
            log.info("invoke rpc queryActivityBySeqIdV2. request:{} result:{} ws:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), System.currentTimeMillis() - startTime);
            RpcResultValidator.validate(result, "请求活动信息失败");
            return result.getData();
        } catch (Exception e) {
            log.error("invoke rpc queryActivityBySeqIdV2 fail. request:{}, result:{}", GsonUtil.toJson(request), GsonUtil.toJson(result), e);
        }
        return null;
    }
}
