package com.xiaomi.nr.promotion.componet.action;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.entity.redis.GiftBargainGroup;
import com.xiaomi.nr.promotion.entity.redis.Goods;
import com.xiaomi.nr.promotion.enums.ActFrequencyEnum;
import com.xiaomi.nr.promotion.enums.BooleanV2Enum;
import com.xiaomi.nr.promotion.enums.SendTypeEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.*;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.GiftPromotionConfig;
import com.xiaomi.nr.promotion.tool.ConditionCheckTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 赠品活动: 计价和均摊动作
 * <p>
 * 1.将不满足的赠品删除
 * 2.将超出的赠品count进行调整
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class GiftAction extends AbstractAction {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 密码
     */
    private String accessCode;
    /**
     * 频次限制 1不限制 2整个活动一次 3每天一次
     */
    private ActFrequencyEnum frequency;
    /**
     * 活动总数
     */
    private long actLimitNum;
    /**
     * 活动限制. 数据值：0-不限制， 没有限制就没有对应字段
     */
    private ActNumLimitRule numLimitRule;

    @Autowired
    private ConditionCheckTool conditionCheckTool;

    /**
     * 进行赠品活动减价
     * <p>
     * - 获取商品下标， 这边商品是赠品列表
     * 1. 根据购物车赠品中找到对应组信息
     * 2. 如果没找到，说明当前赠品不符合， 标记删除
     * 3. 找到了，将购物车赠品改价
     * 4. 将对应购物车商品（赠品）放入对应有groupId的组 cartGroup
     * - 进行赠品减价
     * - 生成活动结算.
     * 1. 按groupId 进行分组， 并进行减价
     * 2. 每组数量进行控制在 <= maxCount
     * - 生成结算资源
     *
     * @param promotion 优惠工具
     * @param request   请求参数
     * @param context   请求上下文，活动组件间
     * @throws BizError 业务异常
     */
    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        QuotaLevel quotaLevel = context.getQuotaLevel();
        if (quotaLevel == null) {
            log.warn("quota level is null. uid:{} actId:{}", request.getUserId(), promotionId);
            return;
        }
        // 购物车中来源当前活动的赠品
        List<CartItem> cartList = request.getCartList();
        List<GoodsIndexNew> indexNewList = CartHelper.getCartsIndex(cartList, SourceEnum.SOURCE_GIFT.getSource(), String.valueOf(promotionId));

        //获取赠品规则， 对赠品进行分组, 过程中会. key： groupId, val: cartList
        Goods giftGoods = quotaLevel.getGiftGoods();
        Map<Long, List<CartItem>> cartGroup = doMatchRule(cartList, indexNewList, request.getOrgCode(), giftGoods);

        // 对每组赠品进行检查， 如果超出最大，需要递减到合适值. key: groupId, val: count
        Integer maxCount = context.getFillTimes();
        if (Objects.equals(SendTypeEnum.ALL.getCode(), giftGoods.getSkuGroupList().get(0).getSendType())) {
            context.setFillTimes(giftGoods.getSkuGroupList().get(0).getListInfo().size());
        }
        Map<Long, Integer> countMap = adjustGroupCount(cartGroup, context.getFillTimes());
        log.debug("gift act adjusted.actId:{} uid:{} fillTimes:{} countMap:{}", promotionId, request.getUserId(), context.getFillTimes(), countMap);

        // 删除购物车 CART_DEL_FLAG
        delInvalidCarts(cartList);

        // 设置活动计算结果
        setResult(context, cartList, promotion, indexNewList, cartGroup, countMap, giftGoods, quotaLevel, maxCount);

        // 初始化资源
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            int joinCount = countMap.values().stream().mapToInt(Integer::intValue).max().orElse(0);
            initResource(request, promotionId, joinCount, context, actLimitNum, frequency, numLimitRule);
            initGiftLimitResource(request, promotionId, context, cartGroup);
        }
    }

    /**
     * 做分组匹配：赠品可能有有多组
     *
     * @param cartList  购物车列表
     * @param indexList 商品下标信息
     * @param orgCode   机构信息
     * @return 符合分组信息
     * @throws BizError 业务异常
     */
    private Map<Long, List<CartItem>> doMatchRule(List<CartItem> cartList, List<GoodsIndexNew> indexList, String orgCode, Goods giftGoods) throws BizError {
        //按组获取符合条件的购物车条目
        Map<Long, List<CartItem>> cartGroupMap = new HashMap<>();
        for (GoodsIndexNew indexItem : indexList) {
            CartItem cartItem = CartHelper.getCartItem(cartList, indexItem);
            if (cartItem == null || !CartHelper.filterOk(cartItem)) {
                log.error("the info's cart now is invalid! gift actId: {}, indexItem: {}, cartItem: {}", promotionId, indexItem, cartItem);
                throw ExceptionHelper.create(ErrCode.ERR_CART, "购物车错误，购物车条目为空或者不合法");
            }
            doMatchActItem(indexItem, cartItem, cartGroupMap, orgCode, giftGoods);
        }
        return cartGroupMap;
    }

    private void doMatchActItem(GoodsIndexNew actItem, CartItem cartItem, Map<Long, List<CartItem>> cartGroupMap, String orgCode, Goods giftGoods) {
        // 处理不符合活动条件的购物车条目 skuGroup==nil代表不符合活动参加条件
        com.xiaomi.nr.promotion.entity.redis.SkuGroup skuGroup = getJoinGiftActGroup(cartItem, orgCode,
                giftGoods);
        if (skuGroup == null) {
            log.error("checkout cartItem is invalid and will be del! gift cartItem:{}", cartItem);
            actItem.setItemId(CART_DEL_FLAG);
            cartItem.setItemId(CART_DEL_FLAG);
            return;
        }
        // 查找同sku
        GiftBargainGroup item = skuGroup.getListInfo().stream()
                .filter(info -> Objects.equals(cartItem.getSku(), String.valueOf(info.getSku()))).findAny().orElse(null);
        boolean conditionCheck = conditionCheckTool.checkActGiftLimit(item.getGiftLimitNum(), item.getGiftBaseNum(),
                promotionId, skuGroup.getGroupId(), item.getSku());
        if (!conditionCheck) {
            log.error("checkout cartItem is limit used and will be del! gift cartItem:{}", cartItem);
            actItem.setItemId(CART_DEL_FLAG);
            cartItem.setItemId(CART_DEL_FLAG);
            return;
        }
        // 购物车改价
        GiftBargainGroup info = skuGroup.getListInfo().get(0);
        // 改赠品价格
        CartHelper.changeGiftPrice(cartItem, info.getCartPrice(), info.getMarketPrice());

        // 按活动组id分类购物车
        cartGroupMap.computeIfAbsent(skuGroup.getGroupId(), k -> new ArrayList<>());
        cartGroupMap.get(skuGroup.getGroupId()).add(cartItem);
    }

    private com.xiaomi.nr.promotion.entity.redis.SkuGroup getJoinGiftActGroup(CartItem cartItem, String orgCode, Goods giftGoods) {
        if (giftGoods == null || CollectionUtils.isEmpty(giftGoods.getSkuGroupList())) {
            log.error("the giftGoods or giftGoods.skuGroupsList is null. giftGoods:{}", giftGoods);
            return null;
        }

        // 遍历赠品组，进行查找， 查看赠品组下赠品是否有和当前sku一样的
        List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> skuGroupsList = giftGoods.getSkuGroupList();
        com.xiaomi.nr.promotion.entity.redis.SkuGroup skuGroup = skuGroupsList.stream().map(groups -> getMatchSkuGroup(groups
                , cartItem, orgCode)).filter(Objects::nonNull).findAny().orElse(null);
        if (skuGroup == null) {
            log.error("the gift cartItem item_id:{}, sku:{} and packageID:{} and groupID:{} not in gift {} rule list {}", cartItem.getItemId(), cartItem.getSku(), cartItem.getPackageId(), cartItem.getGroupId(), promotionId, skuGroupsList);
        }
        return skuGroup;
    }

    /**
     * 赠品加价购查找匹配
     *
     * @param groups   组
     * @param cartItem 购物车项
     * @param orgCode  门店Code
     * @return SkuGroup
     */
    protected SkuGroupBean getMatchSkuGroup(SkuGroupBean groups, CartItem cartItem, String orgCode) {
        List<GiftBargainBean> skuInfoList = groups.getListInfo();
        if (org.apache.dubbo.common.utils.CollectionUtils.isEmpty(skuInfoList)) {
            return null;
        }

        GiftBargainBean skuInfo = skuInfoList.stream()
                .filter(item -> this.isMatchSkuInfo(item, groups, cartItem, orgCode)).findAny().orElse(null);
        if (skuInfo == null) {
            return null;
        }
        final List<GiftBargainBean> giftGroupList = new ArrayList<>();
        giftGroupList.add(skuInfo);
        SkuGroupBean skuGroup = new SkuGroupBean();
        skuGroup.setListInfo(giftGroupList);
        skuGroup.setGroupId(groups.getGroupId());
        return skuGroup;
    }

    private boolean isMatchSkuInfo(GiftBargainBean skuInfo, SkuGroupBean groups, CartItem cartItem, String orgCode) {
        String skuPackage = String.valueOf(skuInfo.getSku());
        boolean isSku = Objects.equals(skuPackage, cartItem.getSku());
        boolean isPackage = Objects.equals(skuPackage, cartItem.getPackageId());
        // 线上
        if (StringUtils.isEmpty(orgCode)) {
            return isSku || isPackage;
        }
        // 线下
        boolean isGroup = Objects.equals(groups.getGroupId(), cartItem.getGroupId());
        return (isSku || isPackage) && isGroup;
    }

    private void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool, List<GoodsIndexNew> goodsIndNew, Map<Long, List<CartItem>> cartGroup, Map<Long, Integer> countMap, Goods giftGoods, QuotaLevel quotaLevel, Integer maxCount) throws BizError {
        // 总赠品数 和 每组之间参加活动的最大值
        int validCountAll = countMap.values().stream().mapToInt(Integer::intValue).sum();
        List<GroupCurCount> groupCurCount = buildIncrCountMap(cartGroup, countMap);

        // 生成活动拓展信息
        String activityInfo = generateExtendInfo(maxCount, validCountAll, groupCurCount, accessCode, giftGoods);

        // 符合条件主商品 和 符合条件赠品
        List<String> parentItemIds = CartHelper.getParentItemIdList(context.getGoodIndex());
        List<String> joinedItemIdList = CartHelper.getJoinedItemIdListNew(goodsIndNew, cartList);

        // 活动优惠信息
        int joinCount = countMap.values().stream().mapToInt(Integer::intValue).max().orElse(0);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        com.xiaomi.nr.promotion.entity.redis.QuotaEle curEle = quotaLevel.getIncludeGoodsGroups().get(0).getQuota();
        PolicyNewLevel policyNewLevel = promotionInfo.getPolicyNew().getPolicy().stream().filter(level -> {
                    QuotaEle ele = level.getIncludedGoodsGroup().get(0).getQuota();
                    return Objects.equals(curEle.getType(), ele.getType()) && Objects.equals(curEle.getMoney(), ele.getMoney()) && Objects.equals(curEle.getCount(), ele.getCount());
                }
        ).findAny().orElse(null);
        promotionInfo.getPolicyNew().setPolicy(Collections.singletonList(policyNewLevel));


        promotionInfo.setJoinedItemId(joinedItemIdList);
        promotionInfo.setParentItemId(parentItemIds);
        promotionInfo.setExtend(activityInfo);
        promotionInfo.setJoinCounts(joinCount);
        promotionInfo.setJoined(CollectionUtils.isNotEmpty(joinedItemIdList) ? 1 : 0);

        // 设置上下文结果
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    private String generateExtendInfo(Integer maxCount, Integer curCount, List<GroupCurCount> countList, String accessCode, Goods giftGoods) {
        if (giftGoods == null || CollectionUtils.isEmpty(giftGoods.getSkuGroupList())) {
            return null;
        }
        List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> skuGroupsList = giftGoods.getSkuGroupList();
        List<GiftBargainGroup> listInfo = skuGroupsList.get(0).getListInfo();
        if (CollectionUtils.isEmpty(listInfo)) {
            return null;
        }
        // 获取skulist和list,兼容旧逻辑
        Map<String, String> skuMap = listInfo.stream().map(GiftBargainGroup::getSku).map(String::valueOf).collect(Collectors.toMap(sku -> sku, sku -> sku, (val1, val2) -> val2, LinkedHashMap::new));
        List<String> skuList = new ArrayList<>(skuMap.keySet());
        List<SkuGroupBean> skuGroupBeanList = checkAndConvertGroup(skuGroupsList);


        // 返回活动信息,对应promotion结构里的extend字段
        PromotionExtend expandInfo = new PromotionExtend();
        expandInfo.setCartPrice(listInfo.get(0).getCartPrice());
        expandInfo.setMaxCount(maxCount);
        expandInfo.setCurCount(curCount);
        expandInfo.setGroupCurCount(countList);
        expandInfo.setList(skuMap);
        expandInfo.setSkuList(skuList);
        expandInfo.setSelectType(giftGoods.getSelectType());
        expandInfo.setIgnoreStock(giftGoods.getIgnoreStock());
        expandInfo.setRefundValue(listInfo.get(0).getRefundValue());
        expandInfo.setAccessCode(accessCode);
        expandInfo.setSkuGroupsList(skuGroupBeanList);
        expandInfo.setOnsaleExtend(new OnsaleExtendInfo());
        return GsonUtil.toJson(expandInfo);
    }

    private List<SkuGroupBean> checkAndConvertGroup(List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> skuGroupsList) {
        if (CollectionUtils.isEmpty(skuGroupsList)) {
            return Collections.emptyList();
        }
        List<SkuGroupBean> skuGroupBeanList = Lists.newArrayList();
        for (com.xiaomi.nr.promotion.entity.redis.SkuGroup skuGroup : skuGroupsList) {
            Long groupId = skuGroup.getGroupId();
            List<GiftBargainGroup> listInfo = Optional.ofNullable(skuGroup.getListInfo()).orElse(Collections.emptyList());
            List<GiftBargainBean> giftBeanList = listInfo.stream().map(item -> convert(item, groupId)).collect(Collectors.toList());
            boolean noneMatch = giftBeanList.stream().noneMatch(bean -> Objects.equals(BooleanV2Enum.YES.getValue(), bean.getActNumLimit()));
            if (noneMatch) {
                continue;
            }
            SkuGroupBean groupBean = new SkuGroupBean();
            groupBean.setGroupId(groupId);
            groupBean.setSendType(skuGroup.getSendType());
            groupBean.setListInfo(giftBeanList);
            skuGroupBeanList.add(groupBean);
        }
        return skuGroupBeanList;
    }

    private GiftBargainBean convert(GiftBargainGroup item, Long groupId) {
        Boolean numLimit = conditionCheckTool.checkActGiftLimit(item.getGiftLimitNum(), item.getGiftBaseNum(),
                promotionId, groupId, item.getSku());
        int actNumLimit = numLimit? BooleanV2Enum.YES.getValue(): BooleanV2Enum.NO.getValue();
        GiftBargainBean bean = new GiftBargainBean();
        bean.setSku(item.getSku());
        bean.setMarketPrice(item.getMarketPrice());
        bean.setCartPrice(item.getCartPrice());
        bean.setShipmentType(item.getShipmentType());
        bean.setRefundValue(item.getRefundValue());
        bean.setActNumLimit(actNumLimit);
        bean.setBaseNum(item.getGiftBaseNum());
        return bean;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof GiftPromotionConfig)) {
            return;
        }
        GiftPromotionConfig promotionConfig = (GiftPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.accessCode = promotionConfig.getAccessCode();
        this.frequency = promotionConfig.getFrequency();
        this.actLimitNum = promotionConfig.getActLimitNum();
        this.numLimitRule = promotionConfig.getNumLimitRule();
    }
}



