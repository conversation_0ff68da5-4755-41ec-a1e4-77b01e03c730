package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.rpc.phoenix.SubsidyPhoenixProxy;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 政府模式资格操作
 * @date 2024/12/21 10:53
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class GovernmentSubsidyPhoenixProvider implements ResourceProvider<GovernmentSubsidyPhoenixProvider.ResContent> {
    private ResourceObject<GovernmentSubsidyPhoenixProvider.ResContent> resourceObject;

    @Autowired
    private SubsidyPhoenixProxy subsidyPhoenixProxy;

    @Override
    public ResourceObject<GovernmentSubsidyPhoenixProvider.ResContent> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<GovernmentSubsidyPhoenixProvider.ResContent> object) {
        this.resourceObject = object;
    }

    @Override
    public void lock() throws BizError {
        log.info("lock GovernmentSubsidyPhoenix begin, resource = {}", resourceObject);

        Long orderId = resourceObject.getContent().getOrderId();
        Long mid = resourceObject.getContent().getMid();
        String cateCode = resourceObject.getContent().getCateCode();
        Integer regionId = resourceObject.getContent().getRegionId();
        String orderInfo = resourceObject.getContent().getOrderInfo();

        // 调用三方优惠，锁定资格码
        subsidyPhoenixProxy.governmentLockSubsidyQualification(mid, regionId, cateCode, orderId, orderInfo);
        log.info("lock GovernmentSubsidyPhoenix finished, resource = {}", resourceObject);
    }

    @Override
    public void consume() throws BizError {
        log.info("lock GovernmentSubsidyPhoenix begin, resource = {}", resourceObject);
        Long mid = resourceObject.getContent().getMid();
        Long orderId = resourceObject.getContent().getOrderId();
        // 调用三方优惠，核销资格码
        Integer regionId = resourceObject.getContent().getRegionId();
        subsidyPhoenixProxy.governmentWriteOffSubsidyQualification(mid, orderId);
        log.info("consume GovernmentSubsidyPhoenix resource. {}", GsonUtil.toJson(resourceObject));
    }

    @Override
    public void rollback() throws BizError {
        log.info("rollback GovernmentSubsidyPhoenix begin, resource = {}", resourceObject);
        Long userId = resourceObject.getContent().getUserId();
        Long orderId = resourceObject.getContent().getOrderId();
        // 调用三方优惠，回滚资格码
        Integer regionId = resourceObject.getContent().getRegionId();
        subsidyPhoenixProxy.governmentRollbackSubsidyQualification(userId, orderId);
        log.info("rollback GovernmentSubsidyPhoenix finished, resource = {}", resourceObject);
    }

    @Override
    public String conflictText() {
        return "处理政府模式失败";
    }

    @Data
    public static class ResContent {
        /**
         * 订单号
         */
        private Long orderId;

        /**
         * 用户账号
         */
        private Long userId;

        private Long mid;
        private String cateCode;
        private Integer regionId;
        private String orderInfo;
    }
}
