package com.xiaomi.nr.promotion.domain.coupon.impl;

import com.xiaomi.nr.promotion.domain.coupon.AbstractCouponDomain;
import com.xiaomi.nr.promotion.domain.coupon.facade.CouponFacade;
import com.xiaomi.nr.promotion.domain.coupon.facade.CouponFacadeInterFace;
import com.xiaomi.nr.promotion.domain.coupon.facade.MaintenanceRepairCouponFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 2024/2/28
 */
@Component
@Slf4j
public class MaintenanceRepairCouponDomain extends AbstractCouponDomain {
    
    @Autowired
    private MaintenanceRepairCouponFacade facade;
    
    @Override
    protected CouponFacadeInterFace getCouponFacde() {
        return facade;
    }
}
