package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.domain.coupon.service.common.CouponInfoService;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 券资源
 *
 * <AUTHOR>
 * @date 2021/6/21
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class CouponProvider implements ResourceProvider<CouponProvider.ResContent> {
    private ResourceObject<CouponProvider.ResContent> resourceObject;

    @Autowired
    @Qualifier("couponInfoServiceRemoteImpl")
    private CouponInfoService couponInfoService;


    @Override
    public ResourceObject<CouponProvider.ResContent> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<CouponProvider.ResContent> object) {
        this.resourceObject = object;
    }

    @Override
    public void lock() throws BizError {
        boolean locked = couponInfoService.lockCoupon(resourceObject);
        if (!locked) {
            log.warn("lock coupon fail. resourceObject:{}", resourceObject);
            throw ExceptionHelper.create(ErrCode.COUPON_LOCK_FAIL, "锁定券资源失败");
        }
        CouponProvider.ResContent resContent = resourceObject.getContent();

        log.info("lock coupon ok. resourceObject:{}", resourceObject);
    }

    @Override
    public void consume() throws BizError {
        boolean consume = couponInfoService.consumeCoupon(resourceObject);
        if (!consume) {
            log.warn("consume coupon fail. resourceObject:{}", resourceObject);
            throw ExceptionHelper.create(ErrCode.COUPON_CONSUME_FAIL, "提交券资源失败");
        }
        log.info("consume coupon resource. resourceObject{}", resourceObject);
    }

    @Override
    public void rollback() throws BizError {
        boolean rollback = couponInfoService.rollbackCoupon(resourceObject);
        if (!rollback) {
            log.warn("rollback coupon fail. resourceObject:{}", resourceObject);
            throw ExceptionHelper.create(ErrCode.COUPON_ROLLBACK_FAIL, "回滚券资源失败");
        }
        CouponProvider.ResContent resContent = resourceObject.getContent();

        log.info("rollback coupon ok. resourceObject:{}", resourceObject);
    }

    @Override
    public void refund(int resourceStatus) throws BizError {
        boolean res = couponInfoService.refundCoupon(resourceStatus, resourceObject);
        if (!res) {
            log.warn("refund coupon fail. resourceStatus:{}, resourceObject:{}", resourceStatus, resourceObject);
            throw ExceptionHelper.create(ErrCode.COUPON_ROLLBACK_FAIL, "退还券资源失败");
        }
        log.info("refund coupon ok. resourceStatus:{}, resourceObject:{}", resourceStatus, resourceObject);
    }

    @Override
    public String conflictText() {
        return "使用券失败";
    }

    /**
     * 与优惠券系统交互使用的ResContent，还有订单中所有的券信息
     */
    @Data
    public static class MultiResContent extends ResContent {
        /**
         * 订单中所有券信息
         */
        private List<CouponItem> couponItems;
    }

    @Data
    public static class CouponItem {
        /**
         * 用户券ID
         */

        private Long id;

        /**
         * 用户券ID
         */
        private String code;

        /**
         * 替换钱
         */
        private BigDecimal replaceMoney;

        /**
         * 邮费扣减
         */
        private BigDecimal reduceExpress;
    }

    @Data
    public static class ResContent {

        /**
         * 用户券ID
         */

        private Long id;

        /**
         * 用户券ID
         */
        private String code;

        /**
         * 替换钱
         */
        private BigDecimal replaceMoney;

        /**
         * 邮费扣减
         */
        private BigDecimal reduceExpress;

        /**
         * 线下使用 o 线下　１　线上
         */
        private Integer offline;

        /**
         * 用户ID
         */
        private Long userId;

        /**
         * ClientID
         */
        private Long clientId;


        /**
         * 门店Code
         */
        private String orgCode = "";

        /**
         * 订单已使用的优惠券
         */
        private Long usedCouponId = 0L;

        /**
         * 下单类型
         */
        private Integer submitType = 0;
        
        /**
         * 业务类型
         * @see BizPlatformEnum
         */
        private Integer bizPlatform;
        
        /**
         * vid
         */
        private String vid;
        

        /**
         * 券类型 @link{CouponCategoryEnum}
         */
        @Deprecated
        private Integer categoryType;

        /**
         * 使用时间
         */
        @Deprecated
        private Long useTime;

        /**
         * 券类型
         */
        @Deprecated
        private String couponType;
        /**
         * 新状态
         */
        @Deprecated
        private String statNew;
        /**
         * 旧状态
         */
        @Deprecated
        private String statOld;

        /**
         * 是否操作旧券
         */
        private Boolean isOptOldCoupon;

    }
}
