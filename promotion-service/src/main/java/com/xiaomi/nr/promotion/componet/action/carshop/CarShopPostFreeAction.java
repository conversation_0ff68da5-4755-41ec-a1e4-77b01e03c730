package com.xiaomi.nr.promotion.componet.action.carshop;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.componet.action.AbstractAction;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.redis.QuotaEle;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import com.xiaomi.nr.promotion.enums.PolicyQuotaTypeEnum;
import com.xiaomi.nr.promotion.enums.PostFreeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.PostFreePromotionConfig;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @author: zhangliwei6
 * @date: 2025/5/8 19:40
 * @description:
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class CarShopPostFreeAction extends AbstractAction {

    /**
     * 活动ID
     */
    private Long promotionId;

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        QuotaLevel level = context.getQuotaLevel();
        List<GoodsIndex> indexList = context.getGoodIndex();
        if (CollectionUtils.isEmpty(indexList) || level == null) {
            log.error("carShopPostFree context indexList is empty or level is null. actId:{}, uid:{} indexList:{}",
                    promotionId, request.getUserId(), indexList);
            return;
        }

        // 活动条件判断，类型只能是满件
        QuotaEle quotaEle = level.getQuotas().getFirst();
        if (quotaEle == null || PolicyQuotaTypeEnum.POLICY_QUOTA_NUM.getType() != quotaEle.getType()) {
            log.error("carShopPostFree quotaEle is empty or error. actId:{}, uid:{} quotaLevel:{}",
                    promotionId, request.getUserId(), level);
            return;
        }

        // 获取购物车中命中活动的项
        List<CartItem> cartList = request.getCartList();
        List<CartItem> joinCartList = CartHelper.getIndexCartList(cartList, indexList);

        // 组织优惠信息promotionInfo，填充到context
        setResult(context, joinCartList, promotion, quotaEle);
    }

    private void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool, QuotaEle quotaEle) throws BizError {
        List<String> itemIdList = new ArrayList<>();
        int sum = 0;
        for (CartItem cartItem: cartList) {
            if (cartItem != null) {
                // 不管选没选中，都返回活动
                itemIdList.add(cartItem.getItemId());
                // 购物车结算接口才传selected，此时选中才参与计算count。结算页和下单接口总是参与计算count
                if (! Objects.equals( FromInterfaceEnum.CHECKOUT_CART, context.getFromInterface()) || Boolean.TRUE.equals(cartItem.getSelected())) {
                    sum += cartItem.getCount();
                }
            }
        }

        // 参加包邮的活动都记录
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setParentItemId(itemIdList);
        promotionInfo.setJoinedItemId(itemIdList);
        promotionInfo.setJoined(BooleanEnum.YES.getValue());
        promotionInfo.setPostfreeNum(quotaEle.getCount());
        // 满足活动条件的postFree置为1
        promotionInfo.setPostfree(sum >= quotaEle.getCount() ? PostFreeEnum.POST_FREE.getVal() :
                PostFreeEnum.NO_POST_FREE.getVal());
        // 差N件包邮
        promotionInfo.setNeedsNum4PostFree(Math.max(0 ,quotaEle.getCount() - sum));

        // 约定返回全部活动，重复时由订单自己去重
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof PostFreePromotionConfig promotionConfig)) {
            log.error("config is not instanceof CarShopPostFreePromotionConfig. config:{}", config);
            return;
        }
        this.promotionId = promotionConfig.getPromotionId();
    }
}
