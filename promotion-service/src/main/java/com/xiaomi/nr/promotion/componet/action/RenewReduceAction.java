package com.xiaomi.nr.promotion.componet.action;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.OnsaleExtendInfo;
import com.xiaomi.nr.promotion.model.common.OnsaleJoin;
import com.xiaomi.nr.promotion.model.common.PromotionExtend;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.RenewReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.GoodsReduceInfo;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.IdKeyHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 直降活动: 计价和均摊动作
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class RenewReduceAction extends AbstractAction {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 减价信息
     */
    private Map<String, GoodsReduceInfo> renewReduceMap;
    /**
     * 分摊信息
     */
    @Autowired
    private CheckoutCartTool checkoutCartTool;

    /**
     * 执行减价
     *
     * @param activityTool 优惠工具
     * @param request      请求参数
     * @param context      请求上下文，活动组件间
     * @throws BizError 业务异常
     */
    @Override
    public void execute(ActivityTool activityTool, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (CollectionUtils.isEmpty(context.getGoodIndex())) {
            log.error("fill goodsIndex is empty. actId:{} uid:{}", promotionId, request.getUserId());
            return;
        }

        List<Integer> indexList = context.getGoodIndex().stream().map(GoodsIndex::getIndex).collect(Collectors.toList());
        List<CartItem> cartList = request.getCartList();

        // 改价
        List<CartItem> fillCartList = CartHelper.getCartList(cartList, indexList);
        String renewLevelKey = context.getRenewLevelKey();
        changePrice(fillCartList, renewReduceMap, renewLevelKey);

        // 处理响应promotionInfo
        setResult(context, activityTool, fillCartList);

        // 初始化资源
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            List<String> skuPackageList = CartHelper.getSkuPackageList(fillCartList).stream().distinct().collect(Collectors.toList());
            initRenewResourceResource(request, promotionId, skuPackageList, context, renewLevelKey);
        }
    }

    private void changePrice(List<CartItem> fillCartList, Map<String, GoodsReduceInfo> renewReduceMap, String renewLevelKey) {
        for (CartItem item : fillCartList) {
            String skuPackage = CartHelper.getSkuPackage(item);
            String reduceMapKey = IdKeyHelper.generalReduceKey(skuPackage, renewLevelKey);
            GoodsReduceInfo reduceInfo = renewReduceMap.get(reduceMapKey);
            if (reduceInfo == null) {
                continue;
            }
            doChangeCartPrice(item, reduceInfo);
        }
    }

    private void doChangeCartPrice(CartItem item, GoodsReduceInfo reduceInfo) {
        Long reduceAmount = reduceInfo.getReduceAmount();
        if (reduceAmount <= 0L) {
            log.warn("reduceAmount <= 0, actId:{} reduceInfo:{} cart:{} ", promotionId, reduceInfo, item);
            return;
        }
        long curPrice = CartHelper.goodsCurPrice(item);
        if (curPrice <= reduceAmount) {
            log.warn("curPrice less than reduceAmount, actId:{} reduceInfo:{} cart:{} ", promotionId, reduceInfo, item);
            return;
        }
        // 分摊购物车减免金额
        String idKey = getIdKey(promotionId);
        checkoutCartTool.divideCartsReduce(reduceAmount, Collections.singletonList(item), idKey, ActivityTypeEnum.RENEW_REDUCE.getValue(), promotionId);
    }

    private void setResult(LocalContext context, ActivityTool tool, List<CartItem> fillCartList) throws BizError {
        List<String> itemList = fillCartList.stream().map(CartItem::getItemId).collect(Collectors.toList());
        Integer joined = CollectionUtils.isNotEmpty(itemList) ? BooleanEnum.YES.getValue() : BooleanEnum.NO.getValue();

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setJoinedItemId(itemList);
        promotionInfo.setParentItemId(itemList);
        promotionInfo.setExtend(generateActExpandInfo(fillCartList));
        promotionInfo.setJoined(joined);

        // 设置上下文结果
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    private String generateActExpandInfo(List<CartItem> fillCartList) {
        Map<Long, List<CartItem>> cartsMap = fillCartList.stream().collect(Collectors.groupingBy(item -> Long.valueOf(CartHelper.getSkuPackage(item))));
        List<OnsaleJoin> onSaleJoinList = Lists.newArrayList();
        cartsMap.forEach((skuPackage, list) -> {
            OnsaleJoin join = new OnsaleJoin();
            join.setSkuOrPackage(skuPackage);
            join.setJoinCounts(list.size());
            onSaleJoinList.add(join);
        });

        OnsaleExtendInfo onSaleExtend = new OnsaleExtendInfo();
        onSaleExtend.setJoinExtend(onSaleJoinList);
        PromotionExtend extend = new PromotionExtend();
        extend.setOnsaleExtend(onSaleExtend);
        return GsonUtil.toJson(extend);
    }


    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof RenewReducePromotionConfig)) {
            return;
        }
        RenewReducePromotionConfig promotionConfig = (RenewReducePromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.renewReduceMap = promotionConfig.getRenewReduceInfoMap();
    }
}