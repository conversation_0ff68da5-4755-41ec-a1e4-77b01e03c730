package com.xiaomi.nr.promotion.model.promotionconfig;

import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.model.promotionconfig.common.BenefitInfo;
import lombok.ToString;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 直降信息
 *
 * <AUTHOR>
 * @date 2021/5/31
 */
@ToString
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class OnsalePromotionConfig extends MultiPromotionConfig {
    /**
     * 直降信息Map
     * key: skuPackage val:ActPriceInfo
     */
    private Map<String, ActPriceInfo> onsaleInfoMap;

    /**
     * 交易类型
     */
    private Integer tradeType;

    /**
     * 权益信息
     */
    private BenefitInfo benefitInfo;


    public Map<String, ActPriceInfo> getOnsaleInfoMap() {
        return onsaleInfoMap;
    }

    public void setOnsaleInfoMap(Map<String, ActPriceInfo> onsaleInfoMap) {
        this.onsaleInfoMap = onsaleInfoMap;
    }

    public Integer getTradeType() {
        return tradeType;
    }

    public void setTradeType(Integer tradeType) {
        this.tradeType = tradeType;
    }

    public BenefitInfo getBenefitInfo() {
        return benefitInfo;
    }

    public void setBenefitInfo(BenefitInfo benefitInfo) {
        this.benefitInfo = benefitInfo;
    }
}
