package com.xiaomi.nr.promotion.mq.producer;

import com.xiaomi.nr.promotion.mq.producer.entity.CarEquityPerformanceMessage;
import com.xiaomi.nr.promotion.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: zhangliwei6
 * @date: 2025/1/7 15:09
 * @description:
 */
@Slf4j
@Component
public class CarEquityPerformanceProducer extends BaseProducer implements Producer<List<CarEquityPerformanceMessage>> {

    @Autowired
    public CarEquityPerformanceProducer(RocketMQTemplate rocketMQTemplate) {
        super.setRocketMqTemplate(rocketMQTemplate);
    }

    @Value("${mq.topic.carEquity}")
    private String topic;

    @Value("${mq.topic.carEquity.tag}")
    private String tag;

    @Override
    public void sendMessage(List<CarEquityPerformanceMessage> payload) {
        SendResult sendResult = super.sendMessageAndRetry(topic, payload, 1);
        log.info("send result:{}", GsonUtil.toJson(sendResult));
    }

    @Override
    public void sendMessage(List<CarEquityPerformanceMessage> payload, String key) {
        SendResult sendResult = super.sendMessageAndRetry(topic, tag, payload, key, 1);
        log.info("send key:{} result:{}", key, GsonUtil.toJson(sendResult));
    }
}
