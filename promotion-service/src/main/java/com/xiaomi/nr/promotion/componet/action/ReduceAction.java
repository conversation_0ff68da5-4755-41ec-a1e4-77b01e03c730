package com.xiaomi.nr.promotion.componet.action;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.entity.redis.QuotaEle;
import com.xiaomi.nr.promotion.enums.ActFrequencyEnum;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.PolicyQuotaTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.ReducePromotionConfig;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.IdKeyHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 满减活动：计算扣减金额和分摊
 *
 * <AUTHOR>
 * @date 2021/4/9
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class ReduceAction extends AbstractAction {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 频次限制 1不限制 2整个活动一次 3每天一次
     */
    private ActFrequencyEnum frequency;
    /**
     * 活动总数
     */
    private long actLimitNum;
    /**
     * 活动限制. 数据值：0-不限制， 没有限制就没有对应字段
     */
    private ActNumLimitRule numLimitRule;

    @Autowired
    private CheckoutCartTool checkoutCartTool;

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        QuotaLevel level = context.getQuotaLevel();
        List<GoodsIndex> indexList = context.getGoodIndex();
        if (CollectionUtils.isEmpty(indexList) || level == null) {
            log.error("reduce context indexList is empty or level is null. actId:{}, uid:{} indexList:{}", promotionId, request.getUserId(), indexList);
            return;
        }
        List<CartItem> cartList = request.getCartList();
        ValidGoods validGoods = CartHelper.buildValidGoods(cartList, indexList);
        // 获取满足条件
        QuotaEle quotaEle = getSatisfiedQuota(level.getQuotas(), validGoods);
        if (quotaEle == null) {
            log.error("reduce quota is null. actId:{}, uid:{}", promotionId, request.getUserId());
            return;
        }

        // 计算总的减免的金额
        long reduceMoney = level.getReduceMoney();
        long maxReducePrice = level.getMaxReducePrice();
        long realTotalReduceMoney = calculateRealReduceMoney(cartList, validGoods, quotaEle, reduceMoney, maxReducePrice);

        // 分摊购物车减免金额
        String idKey = IdKeyHelper.getGeneralActIdKey(promotionId);
        List<Integer> cartIndexList = getCartIndexList(indexList);
        checkoutCartTool.divideCartsReduce(realTotalReduceMoney, cartIndexList, cartList, idKey, ActivityTypeEnum.REDUCE.getValue(), promotionId);

        // 组织优惠信息promotionInfo，填充到context
        setResult(context, cartList, promotion, reduceMoney, maxReducePrice);

        // 初始化资源
        if (request.getSourceApi() == SourceApi.SUBMIT) {
            initResource(request, promotionId, 1, context, actLimitNum, frequency, numLimitRule);
        }
    }

    private long calculateRealReduceMoney(List<CartItem> cartList, ValidGoods validGoods, QuotaEle quotaEle, long reduceMoney, long maxReducePrice) {
        long totalPrice = validGoods.getValidPrice();
        long totalNum = validGoods.getValidNum();
        // 计算扣减金额
        long totalReduceMoney = calculatePolicyReduceMoney(totalPrice, totalNum, quotaEle, reduceMoney, maxReducePrice);

        //减的金额不应大于购物车所有符合条件的商品当前总价
        totalReduceMoney = Math.min(totalReduceMoney, totalPrice);

        // 购物车的商品实际总价格, 减的金额需小于当前总价，消除0元订单
        long totalCartListPrice = CartHelper.getTotalPrice(cartList);
        if (totalReduceMoney == totalCartListPrice) {
            totalReduceMoney -= 1;
        }
        return totalReduceMoney;
    }

    private long calculatePolicyReduceMoney(long totalPrice, long totalNum, QuotaEle quotaEle, long reduceMoney, long maxReducePrice) {
        PolicyQuotaTypeEnum quotaType = PolicyQuotaTypeEnum.getQuotaType((quotaEle.getType()));
        if (quotaType == null) {
            return 0L;
        }
        long money = 0L;
        // 计算金额
        switch (quotaType) {
            case POLICY_QUOTA_NUM:
            case POLICY_QUOTA_MONEY:
                money = reduceMoney;
                break;
            case POLICY_QUOTA_PER_NUM:
                money = reduceMoney * (totalNum / quotaEle.getCount());
                break;
            case POLICY_QUOTA_PER_MONEY:
                money = reduceMoney * (totalPrice / quotaEle.getMoney());
                break;
            default:
                break;
        }
        // 最多可减金额
        if (maxReducePrice != 0L && maxReducePrice < money) {
            return maxReducePrice;
        }
        return money;
    }

    private void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool, long reduceMoney, long maxReducePrice) throws BizError {
        List<GoodsIndex> indexList = context.getGoodIndex();
        List<String> joinedItemIdList = CartHelper.getJoinedItemIdList(indexList, cartList, promotionId);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setJoinedItemId(joinedItemIdList);
        promotionInfo.setParentItemId(CartHelper.getParentItemIdList(indexList));
        promotionInfo.setExtend(buildActExtendInfo(reduceMoney, maxReducePrice));
        promotionInfo.setJoined(CollectionUtils.isNotEmpty(joinedItemIdList) ? 1 : 0);

        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    private String buildActExtendInfo(long reduceMoney, long maxReducePrice) {
        Map<String, Object> activityInfoMap = new HashMap<>(2);
        activityInfoMap.put("policy_money", reduceMoney);
        activityInfoMap.put("max_price", maxReducePrice);
        return GsonUtil.toJson(activityInfoMap);
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof ReducePromotionConfig)) {
            return;
        }
        ReducePromotionConfig promotionConfig = (ReducePromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.frequency = promotionConfig.getFrequency();
        this.actLimitNum = promotionConfig.getActLimitNum();
        this.numLimitRule = promotionConfig.getNumLimitRule();
    }
}
