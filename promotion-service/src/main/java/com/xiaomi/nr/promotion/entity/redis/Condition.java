package com.xiaomi.nr.promotion.entity.redis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 活动基本信息缓存实体
 *
 * <AUTHOR>
 * @date 2021/3/24
 */
@Data
public class Condition implements Serializable {
    private static final long serialVersionUID = 23719640052326375L;
    /**
     * 活动类型
     * 具体参考 {@link com.xiaomi.nr.promotion.enums.ActivityTypeEnum value}
     */
    private Integer type = 0;

    /**
     * 区域id
     */
    @SerializedName("area_id")
    private Long areaId = 0L;

    /**
     * 活动时间
     */
    @SerializedName("datetime")
    private DataTime datetime;

    /**
     * 频次限制 ：1不限制 2整个活动一次 3每天一次
     */
    private Integer frequency = 0;
    /**
     * 活动总数
     */
    @SerializedName("act_total_stock")
    private Long actLimitNum = 0L;

    /**
     * 能参与的客户端列表
     */
    private List<String> client;

    /**
     * 能参与的人群组
     */
    private CondGroup group;

    /**
     * 政策里的限件限额等信息，这里单独复制了一份
     */
    private List<QuotaEle> quota;

    /**
     * 可参与的商品
     */
    @SerializedName("goods_include")
    private List<CompareItem> goodsInclude;

    /**
     * 排除商品
     */
    @SerializedName("goods_inexclude")
    private CompareItem goodsInexclude;

    /**
     * 整单不可用商品
     */
    @SerializedName("goods_exclude")
    private CompareItem goodsExclude;

    /**
     * 套装是否允许部分商品参加活动(套装是否拆分) 1允许 2不允许
     */
    @SerializedName("check_package")
    private Integer checkPackage = 0;

    /**
     * 特价商品是否参与  1表示不参与，2表示参与
     */
    @SerializedName("check_price")
    private Integer checkPrice = 0;

    /**
     * 是否线下可用 1仅线上使用，2仅线下使用，3均可使用
     */
    private Integer offline = 0;

    /**
     * 活动密码
     */
    @SerializedName("access_code")
    private String accessCode = "";

    /**
     * 订单来源，比如 常态销售common，常态大秒bigtap
     */
    @SerializedName("sale_source")
    private List<String> saleSource;

    /**
     * 是否单品页显示 1否 2是
     */
    @SerializedName("show_in_productView")
    private Integer showInProductView = 0;

    /**
     * 是否在单品页强制推荐加价购 1是 2否
     */
    @SerializedName("is_recommend_force_add_price")
    private Integer isRecommendForceAddPrice = 0;

    /**
     * 机构范围，1-所有门店，2-所有直营店，3-所有专卖店，4-指定门店，5-指定区域   #新增
     */
    @SerializedName("org_scope")
    private Integer orgScope = 0;

    /**
     * 指定门店的orgcode或区域id   #新
     */
    @SerializedName("select_org_codes")
    private List<String> selectOrgCodes;

    /**
     * 是否可使用优惠券 0-否 1-是
     */
    @SerializedName("coupon_limit")
    private Integer couponLimit = 0;

    /**
     * 活动是否互斥 0-否 1-是 #新
     */
    @SerializedName("activity_mutex_limit")
    private Integer activityMutexLimit = 0;

    /**
     * 互斥活动优先级
     */
    @SerializedName("activity_mutex")
    private List<String> activityMutex;

    /**
     * 是否限制商品数量
     */
    @SerializedName("num_limit")
    private Integer numLimit = 0;

    /**
     * 商品数量限制规则，数据值：0-不限制， 没有限制就没有对应字段
     */
    @SerializedName("num_limit_rule")
    private ActNumLimitRule numLimitRule;

    /**
     * 是否限制指定地区可用 true：是 false：否
     */
    @SerializedName("assign_area")
    private Boolean assignArea = false;

    /**
     * 指定可用地区配置
     * 区域类型（1:省 2:市） => 区域ID列表
     */
    @SerializedName("assign_area_config")
    private Map<Integer, List<Integer>> assignAreaConfig;

    /**
     * 使用平台对应的门店 使用渠道类型(1:小米商城 2:直营店/专卖店 3:授权店 4:堡垒店) => 渠道可用门店配置 劵
     */
    @SerializedName("use_channel_store")
    private Map<Integer, UseChannelStoreItem> useChannelStore;

    /**
     * 使用渠道 劵
     */
    @SerializedName("use_channel")
    private String useChannel;

    /**
     * 活动渠道范围
     */
    private List<Integer> channel;

    /**
     * 是否F会员专属赠品，1-是，2-否
     */
    @SerializedName("is_f_member")
    private Integer isFMember;
}
