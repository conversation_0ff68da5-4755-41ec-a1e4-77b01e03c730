package com.xiaomi.nr.promotion.componet.action.carmaintenance;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.componet.action.AbstractAction;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.CarIdentityTypeEnum;
import com.xiaomi.nr.promotion.enums.CouponServiceTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MaintenanceItemFreePromotionConfig;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description 汽车维保-会员折扣活动
 * <AUTHOR>
 * @date 2025-01-03 15:05
*/
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class MaintenanceItemFreeAction extends AbstractAction {

    /**
     * 活动ID
     */
    private Long promotionId;

    /**
     * 活动类型
     */
    private PromotionToolType promotionToolType;

    /**
     * 促销金额
     */
    private Long promotionPrice;

    /**
     * 最大扣减金额
     */
    private Long maxReduceAmount;

    /**
     * 优惠车辆身份：赛道尊享会员(SVIP)，赛道版车主
     */
    private Integer carIdentityType;

    /**
     * 会员ID、购车权益ID
     */
    private String carIdentityId;

    /**
     * 活动参与次数限制
     */
    private Integer identityJoinLimitNum;

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {

        List<CartItem> cartList = request.getCartList();
        List<GoodsIndex> goodIndex = context.getGoodIndex();

        // 如果该活动是vip活动，则默认选中   如果该活动是权益活动， 则需要指定选中
        if (CollectionUtils.isNotEmpty(goodIndex) && (Objects.equals(carIdentityType, CarIdentityTypeEnum.CAR_SHOP_VIP.getCode()) || request.getActivityIds().contains(promotionId))) {

            // 算价
            calcPrice(cartList, goodIndex);

            // 初始化资源
            if (request.getSourceApi() == SourceApi.SUBMIT) {
                if (Objects.equals(carIdentityType, CarIdentityTypeEnum.CAR_SHOP_VIP.getCode())) {
                    initUidActivityCountResource(request, context, promotionId, Integer.valueOf(carIdentityId), identityJoinLimitNum);
                }
                if (Objects.equals(carIdentityType, CarIdentityTypeEnum.USAGE_EQUITY.getCode())) {
                    initVidActivityCountResource(request, context, promotionId, carIdentityType, carIdentityId, identityJoinLimitNum);
                }

            }

            // content
            context.setActivityChecked(Boolean.TRUE);
        }



        // 组织优惠信息promotionInfo，填充到context
        setResult(context, cartList, promotion);
    }

    private void calcPrice(List<CartItem> cartList, List<GoodsIndex> goodIndex) {
        List<String> itemList = goodIndex.stream().map(GoodsIndex::getItemId).collect(Collectors.toList());

        for (CartItem item : cartList) {
            if (!itemList.contains(item.getItemId())) {
                continue;
            }

            Long curPrice = CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList());
            if (curPrice <= 0L) {
                continue;
            }
            long reduceMoney = curPrice - promotionPrice;
            long realReduceMoney = Objects.equals(maxReduceAmount, 0L) ? reduceMoney : Math.min(reduceMoney, maxReduceAmount);

            // reduce detail item
            ReduceDetailItem reduceDetailItem = initReduceDetailItem(promotionId, promotionToolType, item.getSsuId(), realReduceMoney, item.getCount());
            List<ReduceDetailItem> reduceDetailItems = Optional.ofNullable(item.getReduceItemList()).orElse(new ArrayList<>());
            reduceDetailItems.add(reduceDetailItem);
            item.setReduceItemList(reduceDetailItems);

            // 和券互斥
            List<Long> serviceTypes = Optional.ofNullable(item.getMaintenanceInfo().getCannotUseCouponServiceTypes()).orElse(new ArrayList<>());
            serviceTypes.addAll(Lists.newArrayList(CouponServiceTypeEnum.BASIC_MAINTENANCE.getCode().longValue(), CouponServiceTypeEnum.PAINT_REPAIR.getCode().longValue(), CouponServiceTypeEnum.REPAIR_TAIR.getCode().longValue(),
                    CouponServiceTypeEnum.NEED_MAINTENANCE.getCode().longValue(), CouponServiceTypeEnum.CONSUMABLES.getCode().longValue()));
            item.getMaintenanceInfo().setCannotUseCouponServiceTypes(serviceTypes);
        }
    }

    private void setResult(LocalContext context, List<CartItem> cartList, ActivityTool tool) throws BizError {
        List<GoodsIndex> indexList = context.getGoodIndex();
        List<String> joinedItemIdList = CartHelper.getJoinedItemIdList(indexList, cartList, promotionId);

        // 组织promotion数据输出
        PromotionInfo promotionInfo = tool.buildCartPromotionInfo(context);
        promotionInfo.setParentItemId(CartHelper.getParentItemIdList(indexList));
        promotionInfo.setJoinedItemId(joinedItemIdList);
        promotionInfo.setExtend(Strings.EMPTY);
        promotionInfo.setJoined(CollectionUtils.isNotEmpty(joinedItemIdList) ? 1 : 0);

        boolean canSelect = false;
        if (Objects.equals(carIdentityType, CarIdentityTypeEnum.CAR_SHOP_VIP.getCode())) {
            canSelect = false;
        }
        if (Objects.equals(carIdentityType, CarIdentityTypeEnum.USAGE_EQUITY.getCode())) {
            canSelect = CollectionUtils.isNotEmpty(indexList);
        }
        promotionInfo.setCanSelect(canSelect);
        promotionInfo.setChecked(context.getActivityChecked());

        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof MaintenanceItemFreePromotionConfig)) {
            return;
        }
        MaintenanceItemFreePromotionConfig promotionConfig = (MaintenanceItemFreePromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionToolType = promotionConfig.getPromotionType();
        this.promotionPrice = promotionConfig.getPromotionPrice();
        this.maxReduceAmount = promotionConfig.getMaxReduceAmount();
        this.carIdentityType = promotionConfig.getCarIdentityType();
        this.carIdentityId = promotionConfig.getCarIdentityId();
        this.identityJoinLimitNum = promotionConfig.getIdentityJoinLimitNum();
    }
}
