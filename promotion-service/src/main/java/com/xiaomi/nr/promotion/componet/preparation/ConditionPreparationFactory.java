package com.xiaomi.nr.promotion.componet.preparation;

import com.xiaomi.nr.promotion.engine.componet.ConditionPreparation;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 预加载信息组件工厂
 *
 * <AUTHOR>
 * @date 2021/3/16
 */
@Component
public class ConditionPreparationFactory {
    @Autowired
    private ObjectFactory<OrgInfoPreparation> orgInfoPreparationObjectFactory;
    @Autowired
    private ObjectFactory<GoodsHierarchyPreparation> goodsHierarchyPreparationFactory;
    @Autowired
    private ObjectFactory<GlobalExcludePreparation> globalInExcluPreparationFactory;
    @Autowired
    private ObjectFactory<RecycleOrderPreparation> recycleOrderPreparationObjectFactory;

    public ConditionPreparation getConditionLoader(Class<? extends ConditionPreparation> clz) throws BizError {
        if (clz == OrgInfoPreparation.class) {
            return orgInfoPreparationObjectFactory.getObject();
        }
        if (clz == GoodsHierarchyPreparation.class) {
            return goodsHierarchyPreparationFactory.getObject();
        }
        if (clz == GlobalExcludePreparation.class) {
            return globalInExcluPreparationFactory.getObject();
        }
        if (clz == RecycleOrderPreparation.class) {
            return recycleOrderPreparationObjectFactory.getObject();
        }
        throw ExceptionHelper.create(GeneralCodes.NotFound, String.format("ConditionLoader %s not Found", clz.getName()));
    }
}
