package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.entity.redis.LimitRule;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsActCount;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.PartOnsalePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 指定门店降价条件判断
 *
 * <AUTHOR>
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class PartOnsaleCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 活动类型
     */
    private PromotionToolType promotionType;
    /**
     * 直降信息
     */
    private Map<String, ActPriceInfo> onsaleInfoMap;
    /**
     * 是否限制商品数量（活动维度）
     */
    private boolean numLimit;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        Long uid = request.getUserId();
        if (MapUtil.isEmpty(onsaleInfoMap)) {
            log.error("onsaleInfoMap is empty. actId:{} uid:{}", promotionId, uid);
            return false;
        }
        List<CartItem> cartList = request.getCartList();
        String orgCode = request.getOrgCode();
        // 1.查找可以参与的购物车项
        List<CartItem> fillCartList = getFillCarts(cartList);
        // 没有满足的购物车项，返回
        if (CollectionUtils.isEmpty(fillCartList)) {
            log.info("fill carts is empty. uid:{}, actId:{}, carts:{}", uid, promotionId, cartList);
            return false;
        }
        // 2.购物车按最大优惠金额（购物车价-活动价）降序排序
        sortCarts(fillCartList);
        // 购物车项参与活动的map，key:itemId value:商品参与活动信息
        Map<String, GoodsActCount> cartItemActCountMap = new HashMap<>();
        Map<String, GoodsActCount> goodsActCountMap = new HashMap<>();
        // 3.根据商品活动数量和人店维度活动数量的限购对购物车项处理，记录可以参与的购物车项以及活动的数量到context
        dealCartActJoinCount(context, fillCartList, uid, orgCode, cartItemActCountMap, goodsActCountMap);
        // 没有满足的购物车项，则返回
        if (MapUtils.isEmpty(cartItemActCountMap)) {
            log.info("cartItem join act count map is empty. uid:{}, actId:{}, carts:{}", uid, promotionId, cartList);
            return false;
        }
        context.setCartItemActCountMap(cartItemActCountMap);
        context.setGoodsActCountMap(goodsActCountMap);
        return true;
    }

    /**
     * 获取满足活动的购物车列表
     *
     * @param cartList 购物车列表
     * @return 满足的购物车列表
     */
    private List<CartItem> getFillCarts(List<CartItem> cartList) {
        List<CartItem> fillCartList = new ArrayList<>();
        for (CartItem cartItem : cartList) {
            int activityType = promotionType.getTypeId();
            boolean itemQualify = CartHelper.checkItemActQualifyCommon(cartItem, activityType);
            if (!itemQualify) {
                continue;
            }
            ActPriceInfo onsaleInfo = onsaleInfoMap.get(CartHelper.getSkuPackage(cartItem));
            // 过滤降价信息为空或降价后的价格（活动价）大于购物车价的项
            if (onsaleInfo == null || onsaleInfo.getPrice() >= cartItem.getCartPrice()) {
                continue;
            }
            fillCartList.add(cartItem);
        }
        return fillCartList;
    }

    /**
     * 根据优惠金额排序
     *
     * @param fillCartList 购物车列表
     */
    private void sortCarts(List<CartItem> fillCartList) {
        fillCartList.sort((o1, o2) -> {
            Long o1CartPrice = o1.getCartPrice();
            Long o1OnsalePrice = onsaleInfoMap.get(CartHelper.getSkuPackage(o1)).getPrice();
            Long o1ReducePrice = o1CartPrice - o1OnsalePrice;
            Long o2CartPrice = o2.getCartPrice();
            Long o2OnsalePrice = onsaleInfoMap.get(CartHelper.getSkuPackage(o2)).getPrice();
            Long o2ReducePrice = o2CartPrice - o2OnsalePrice;
            return o2ReducePrice.compareTo(o1ReducePrice);
        });
    }

    /**
     * 处理购物车项参与活动的数量
     *
     * @param context 上下文
     * @param fillCartList 购物车项
     * @param uid 用户id
     * @param orgCode 门店id
     * @param cartItemActCountMap 购物车项参与活动数量map
     * @param goodsActCountMap 商品活动数量map
     */
    private void dealCartActJoinCount(LocalContext context, List<CartItem> fillCartList, Long uid, String orgCode,
                                      Map<String, GoodsActCount> cartItemActCountMap, Map<String, GoodsActCount> goodsActCountMap) {
        // 商品剩余的数量map ， key:sku/packageId value:数量
        Map<String, Integer> goodsRemainCountMap = new HashMap<>();
        int remainActLimit;
        int remainPersonNum = context.getFillTimes();
        for (CartItem cartItem : fillCartList) {
            String skuPackage = CartHelper.getSkuPackage(cartItem);
            int remainGoodsNum = getGoodsRemainNum(goodsRemainCountMap, skuPackage, orgCode, goodsActCountMap);
            if (remainGoodsNum <= 0) {
                continue;
            }
            if (numLimit) {
                // 人店维度的限购判断
                // 剩余活动库存取剩余人店限购和商品活动库存的最小值
                remainActLimit = Math.min(remainPersonNum, remainGoodsNum);
                if (remainActLimit == 0) {
                    break;
                }
                // 处理购物车项参与活动的数量
                int joinCount = dealCountByPersonOneLimit(remainActLimit, remainGoodsNum, cartItem, goodsRemainCountMap, skuPackage, cartItemActCountMap);
                remainPersonNum -= joinCount;
            } else {
                // 不判断活动限购，只判断商品活动库存
                dealCount(remainGoodsNum, cartItem, goodsRemainCountMap, skuPackage, cartItemActCountMap);
            }
        }
    }

    /**
     * 获取商品剩余活动数量
     *
     * @param goodsRemainCountMap 商品剩余活动数量map
     * @param skuPackage sku/packageId
     * @param orgCode 门店id
     * @param goodsActCountMap 商品活动数量map
     * @return 剩余活动数量
     */
    private int getGoodsRemainNum(Map<String, Integer> goodsRemainCountMap, String skuPackage, String orgCode, Map<String, GoodsActCount> goodsActCountMap) {
        int remainGoodsNum;
        if (goodsRemainCountMap.containsKey(skuPackage)) {
            remainGoodsNum = goodsRemainCountMap.get(skuPackage);
        } else {
            ActPriceInfo onsaleInfo = onsaleInfoMap.get(skuPackage);
            LimitRule limitRule = onsaleInfo.getLimitRule();
            // 商品每个门店的活动库存
            Integer activityLimitOne = limitRule.getActivityLimitOne();
            // 已消耗的活动库存
            Integer usedNum = activityRedisDao.getActGoodsStoreLimitNum(promotionId, String.valueOf(skuPackage), orgCode);
            // 剩余活动库存
            remainGoodsNum = activityLimitOne - usedNum;
            // 记录商品剩余活动数量
            GoodsActCount goodsActCount = new GoodsActCount();
            goodsActCount.setRemainStoreOne(remainGoodsNum);
            goodsActCountMap.put(skuPackage, goodsActCount);
        }
        return remainGoodsNum;
    }

    /**
     * 根据人店限购和商品活动库存处理数量
     *
     * @param remainActLimit 剩余活动数量
     * @param remainGoodsNum 剩余商品活动数量
     * @param cartItem 购物车项
     * @param goodsRemainCountMap 商品剩余数量map
     * @param skuPackage sku/packageId
     * @param cartItemActCountMap 购物车项活动数量map
     * @return 参与活动数量
     */
    private int dealCountByPersonOneLimit(int remainActLimit, int remainGoodsNum, CartItem cartItem, Map<String, Integer> goodsRemainCountMap,String skuPackage, Map<String, GoodsActCount> cartItemActCountMap) {
        int joinCount;
        GoodsActCount goodsActCount = new GoodsActCount();
        // item的数量超过活动库存
        if (cartItem.getCount() > remainActLimit) {
            goodsActCount.setJoinActCount(remainActLimit);
            goodsRemainCountMap.put(skuPackage, 0);
            joinCount = remainActLimit;
        } else {
            goodsActCount.setJoinActCount(cartItem.getCount());
            goodsRemainCountMap.put(skuPackage, remainGoodsNum-cartItem.getCount());
            joinCount = cartItem.getCount();
        }
        cartItemActCountMap.put(cartItem.getItemId(), goodsActCount);
        return joinCount;
    }

    /**
     * 根据商品活动库存处理数量
     *
     * @param remainGoodsNum 剩余活动数量
     * @param cartItem 购物车项
     * @param goodsRemainCountMap 商品剩余数量map
     * @param skuPackage sku/packageId
     * @param cartItemActCountMap 购物车项活动数量map
     */
    private void dealCount(int remainGoodsNum, CartItem cartItem, Map<String, Integer> goodsRemainCountMap,String skuPackage, Map<String, GoodsActCount> cartItemActCountMap) {
        GoodsActCount goodsActCount = new GoodsActCount();
        // item的数量超过活动库存
        if (cartItem.getCount() > remainGoodsNum) {
            goodsActCount.setJoinActCount(remainGoodsNum);
            goodsRemainCountMap.put(skuPackage, 0);
        } else {
            goodsActCount.setJoinActCount(cartItem.getCount());
            goodsRemainCountMap.put(skuPackage, remainGoodsNum-cartItem.getCount());
        }
        cartItemActCountMap.put(cartItem.getItemId(), goodsActCount);
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof PartOnsalePromotionConfig)) {
            log.error("config is not instanceof PartOnsalePromotionConfig. config:{}", config);
            return;
        }
        PartOnsalePromotionConfig partOnsalePromotionConfig = (PartOnsalePromotionConfig) config;
        this.promotionId = partOnsalePromotionConfig.getPromotionId();
        this.onsaleInfoMap = partOnsalePromotionConfig.getOnsaleInfoMap();
        this.promotionType = partOnsalePromotionConfig.getPromotionType();
        this.numLimit = partOnsalePromotionConfig.isNumLimit();
    }
}
