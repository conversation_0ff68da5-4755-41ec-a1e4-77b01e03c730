package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.domain.activity.service.common.GlobalConfService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

/**
 * 外部资源： 全局活动排除
 *
 * <AUTHOR>
 * @date 2021/5/18
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class GlobalActExcludeExternalProvider extends ExternalDataProvider<CompareItem> {
    /**
     * 全局排除数据
     */
    private ListenableFuture<CompareItem> future;

    @Autowired
    private GlobalConfService globalConfService;

    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        future = globalConfService.getGlobalActInExclude();
    }

    @Override
    protected long getTimeoutMills() {
        return DEFAULT_TIMEOUT;
    }

    @Override
    public ListenableFuture<CompareItem> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.GLOBAL_ACT_EXCLUDE;
    }
}
