package com.xiaomi.nr.promotion.model.promotionconfig;

import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.model.promotionconfig.common.BenefitInfo;
import lombok.ToString;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 范围立减活动配置信息
 *
 * <AUTHOR>
 * @date 2023/11/26 17:54
 **/
@ToString
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarRangeReducePromotionConfig extends MultiPromotionConfig {

    /**
     * 范围立减信息
     */
    private Map<String, ActPriceInfo> rangeReduceInfoMap;

    /**
     * 整单立减金额
     */
    private Long rangeReducePrice;

    /**
     * 活动标题
     */
    private String title;


    /**
     * 活动说明
     */
    private String description;


    /**
     * 活动说明
     */
    private String descriptionTitle;

    /**
     * 范围立减规则信息
     */
    private String rangeReduceRule;

    /**
     * 交易类型
     */
    private Integer tradeType;

    /**
     * 权益信息
     */
    private BenefitInfo benefitInfo;

    public Map<String, ActPriceInfo> getRangeReduceInfoMap() {
        return rangeReduceInfoMap;
    }

    public void setRangeReduceInfoMap(Map<String, ActPriceInfo> rangeReduceInfoMap) {
        this.rangeReduceInfoMap = rangeReduceInfoMap;
    }

    public String getRangeReduceRule() {
        return rangeReduceRule;
    }

    public void setRangeReduceRule(String rangeReduceRule) {
        this.rangeReduceRule = rangeReduceRule;
    }

    public Long getRangeReducePrice() {
        return rangeReducePrice;
    }

    public void setRangeReducePrice(Long rangeReducePrice) {
        this.rangeReducePrice = rangeReducePrice;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescriptionTitle() {
        return descriptionTitle;
    }

    public void setDescriptionTitle(String descriptionTitle) {
        this.descriptionTitle = descriptionTitle;
    }

    public Integer getTradeType() {
        return tradeType;
    }

    public void setTradeType(Integer tradeType) {
        this.tradeType = tradeType;
    }

    public BenefitInfo getBenefitInfo() {
        return benefitInfo;
    }

    public void setBenefitInfo(BenefitInfo benefitInfo) {
        this.benefitInfo = benefitInfo;
    }
}
