package com.xiaomi.nr.promotion.model.promotionconfig;

import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import lombok.ToString;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 范围立减活动配置信息
 *
 * <AUTHOR>
 * @date 2023/11/26 17:54
 **/
@ToString
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarRangeReducePromotionConfig extends MultiPromotionConfig {

    /**
     * 范围立减信息
     */
    private Map<String, ActPriceInfo> rangeReduceInfoMap;

    /**
     * 范围立减规则信息
     */
    private String rangeReduceRule;

    public Map<String, ActPriceInfo> getRangeReduceInfoMap() {
        return rangeReduceInfoMap;
    }

    public void setRangeReduceInfoMap(Map<String, ActPriceInfo> rangeReduceInfoMap) {
        this.rangeReduceInfoMap = rangeReduceInfoMap;
    }

    public String getRangeReduceRule() {
        return rangeReduceRule;
    }

    public void setRangeReduceRule(String rangeReduceRule) {
        this.rangeReduceRule = rangeReduceRule;
    }
}
