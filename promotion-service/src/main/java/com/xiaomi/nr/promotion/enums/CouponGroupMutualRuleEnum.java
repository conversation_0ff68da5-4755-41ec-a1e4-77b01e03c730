package com.xiaomi.nr.promotion.enums;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 2024/3/1
 */
public enum CouponGroupMutualRuleEnum {


    /**
     * 互斥
     */
    MUTEX(1),
    /**
     * 叠加
     */
    APPEND(2);

    private final int code;

    CouponGroupMutualRuleEnum(int code) {
        this.code = code;
    }

    public static CouponGroupMutualRuleEnum getByCode(int code){
        for (CouponGroupMutualRuleEnum ruleEnum:CouponGroupMutualRuleEnum.values()){
            if (ruleEnum.code==code){
                return ruleEnum;
            }
        }
        return null;
    }

    /**
     *
     * @param code
     * @return
     */
    public static int calcMaxCountByCode(int code){
        Integer maxCount = 0;
        CouponGroupMutualRuleEnum mutualRule = CouponGroupMutualRuleEnum.getByCode(code);
        if (CouponGroupMutualRuleEnum.MUTEX.equals(mutualRule)) {
            maxCount = 1;
        } else if (CouponGroupMutualRuleEnum.APPEND.equals(mutualRule)) {
            maxCount = Integer.MAX_VALUE;
        }

        return maxCount;
    }
}
