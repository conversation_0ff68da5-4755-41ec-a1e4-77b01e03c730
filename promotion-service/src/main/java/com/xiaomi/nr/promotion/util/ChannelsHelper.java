package com.xiaomi.nr.promotion.util;


import com.xiaomi.nr.promotion.api.dto.enums.ProductDepartmentEnum;
import com.xiaomi.nr.promotion.entity.redis.Condition;

import com.xiaomi.nr.promotion.enums.ChannelEnum;
import com.xiaomi.nr.promotion.enums.OnOffLineEnum;
import com.xiaomi.nr.promotion.enums.OrgScopeEnum;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * channels Helper 渠道转换（channels，orgScope）
 */
public class ChannelsHelper {


    /**
     * 团购+销售二部+主附品强绑定标记 == 团购主附品生效
     *
     * @param channel           渠道
     * @param department        部门
     * @param bindMainAccessory 主附品强绑定标记
     * @return 团购主附品是否生效
     */
    public static boolean isBindMainAccessory(Integer channel, Integer department, Boolean bindMainAccessory) {
        if (channel == null || department == null || bindMainAccessory == null) {
            return false;
        }
        return isGroupBuyChannel(channel) && department.equals(ProductDepartmentEnum.S2.getValue()) && bindMainAccessory;
    }

    /**
     * 判断是否团购渠道
     *
     * @param channel 渠道
     * @return 是否团购渠道
     */
    public static boolean isGroupBuyChannel(int channel) {
        return com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum.B2T_C_CUSTOMER.getValue() == channel ||
                com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum.B2T_GOV_BIG_CUSTOMER.getValue() == channel ||
                com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum.B2T_MIJIA_BIG_CUSTOMER.getValue() == channel;
    }

    /**
     * channel 转换，由orgScope，channel转换为channels
     *
     * @return
     */
    public static List<Integer> convertChannels(Condition condition) {
        Integer orgScope = condition.getOrgScope();
        Integer offline = condition.getOffline();
        // promotion-admin
        if (CollectionUtils.isNotEmpty(condition.getChannel())) {
            return condition.getChannel();
        }
        if (orgScope == null) {
            return Collections.emptyList();
        }
        // other channel
        List<Integer> channels = new ArrayList<>();
        // 仅线上
        if (OnOffLineEnum.ONLINE.getValue() == offline) {
            channels.add(ChannelEnum.MI_SHOP.getId());
            return channels;
        }
        if (OnOffLineEnum.ONOFFLINE.getValue() == offline) {
            channels.add(ChannelEnum.MI_SHOP.getId());
        }
        // 渠道默认值0根据offline判断
        if (orgScope == 0) {
            if (OnOffLineEnum.OFFLINE.getValue() == offline || OnOffLineEnum.ONOFFLINE.getValue() == offline) {
                channels.add(ChannelEnum.DIRECT.getId());
                channels.add(ChannelEnum.SPECIALTY.getId());
                channels.add(ChannelEnum.AUTHORIZED.getId());
            }
        }
        if (OrgScopeEnum.ORG_ALL_STORE.getOrgScope() == orgScope) {
            channels.add(ChannelEnum.DIRECT.getId());
            channels.add(ChannelEnum.SPECIALTY.getId());
        }
        if (OrgScopeEnum.ORG_ALL_DIRECT_STORE.getOrgScope() == orgScope) {
            channels.add(ChannelEnum.DIRECT.getId());
        }
        if (OrgScopeEnum.ORG_ALL_SPECIALTY_STORE.getOrgScope() == orgScope) {
            channels.add(ChannelEnum.SPECIALTY.getId());
        }
        if (OrgScopeEnum.ORG_ALL_AUTHORIZED_STORE.getOrgScope() == orgScope) {
            channels.add(ChannelEnum.AUTHORIZED.getId());
        }
        if (OrgScopeEnum.ORG_SPECIFY_STORE.getOrgScope() == orgScope) {
            channels.add(ChannelEnum.DIRECT_ASSIGN_STORE.getId());
            channels.add(ChannelEnum.SPECIALTY_ASSIGN_STORE.getId());
        }
        if (OrgScopeEnum.ORG_SPECIFY_AREA.getOrgScope() == orgScope) {
            channels.add(ChannelEnum.DIRECT_ASSIGN_AREA.getId());
            channels.add(ChannelEnum.SPECIALTY_ASSIGN_AREA.getId());
            channels.add(ChannelEnum.AUTHORIZED_ASSIGN_AREA.getId());
        }
        if (OrgScopeEnum.ORG_AUTHORIZED_STORE.getOrgScope() == orgScope) {
            channels.add(ChannelEnum.AUTHORIZED_ASSIGN_STORE.getId());
        }
        if (OrgScopeEnum.ORG_AUTHORIZED_AREA.getOrgScope() == orgScope) {
            channels.add(ChannelEnum.AUTHORIZED_ASSIGN_AREA.getId());

        }
        return channels;
    }

}
