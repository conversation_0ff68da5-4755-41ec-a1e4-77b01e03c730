package com.xiaomi.nr.promotion.enums;

/**
 * 礼品卡类型
 *
 * <AUTHOR>
 * @date 2021/5/7
 */
public enum EcardTypeEnum {
    /**
     * TypeIDRecycle 爱回收卡类型
     */
    TYPE_ID_RECYCLE_AHS(9),

    TYPE_ID_RECYCLE_EXTEND_AHS(26),
    /**
     * micare 换新券
     */
    TYPE_ID_RECYCLE_AHS_MI_CARE(29),
    /**
     * micare 换新补贴券
     */
    TYPE_ID_RECYCLE_AHS_MI_CARE_EXTEND(32),
    /**
     * 快递补偿卡类型
     */
    TYPE_ID_EXPRESS(14),
    /**
     * 标准
     */
    TYPE_ID_RECYCLE_STAND(24),
    /**
     * 补贴
     */
    TYPE_ID_RECYCLE_EXTEND(25),
    /**
     * micare 换新券
     */
    TYPE_ID_RECYCLE_MI_CARE(30),
    /**
     * micare 换新补贴券
     */
    TYPE_ID_RECYCLE_MI_CARE_EXTEND(31),
    /**
     * 北京消费劵
     */
    TYPE_ID_BEIJING_SHOPPING_COUPON(28),
    /**
     * 北京市年货劵
     */
    TYPE_ID_BEIJING_SPRING_COUPON(40);

    private final int val;

    EcardTypeEnum(int val) {
        this.val = val;
    }

    public int getVal() {
        return val;
    }

    /**
     * 根据值获取类型
     */
    public static EcardTypeEnum get(int val) {
        EcardTypeEnum[] values = EcardTypeEnum.values();
        for (EcardTypeEnum typeEnum : values) {
            if (val == typeEnum.getVal()) {
                return typeEnum;
            }
        }
        return null;
    }
}
