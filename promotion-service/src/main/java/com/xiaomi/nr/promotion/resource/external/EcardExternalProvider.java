package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.domain.ecard.model.UserEcard;
import com.xiaomi.nr.promotion.domain.ecard.service.common.EcardInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.List;

/**
 * 礼品卡外部资源
 *
 * <AUTHOR>
 * 2021/5/7 11:59 上午
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class EcardExternalProvider extends ExternalDataProvider<List<UserEcard>> {

    @Autowired
    private EcardInfoService ecardInfoService;

    private ListenableFuture<List<UserEcard>> future;

    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    /**
     * 准备外部资源的具体逻辑
     * 此处未做参数非空校验 clientId、userId、ecardIds
     */
    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        Long userId = request.getUserId();
        Long clientId = request.getClientId();
        List<String> ecardIds = request.getEcardIds();
        if (CollectionUtils.isEmpty(ecardIds) || userId == null || clientId == null) {
            return;
        }
        // 结算 并且 && 北京消费券统一由 BeijingCouponExternalProvider
        if (request.getUseBeijingcoupon()) {
            return;
        }
        this.future = ecardInfoService.getEcardsInfoAsync(userId, ecardIds, clientId);
    }

    @Override
    protected long getTimeoutMills() {
        return DEFAULT_TIMEOUT;
    }

    @Override
    protected ListenableFuture<List<UserEcard>> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.ECARD;
    }
}
