package com.xiaomi.nr.promotion.activity.carsale;

import com.xiaomi.nr.promotion.activity.AbstractActivityTool;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailAddItem;
import com.xiaomi.nr.promotion.componet.condition.carsale.CarOrderReduceCondition;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.CarOrderReduceConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class CarOrderReduceActivity extends AbstractActivityTool implements ActivityTool {

    private Map<String, ActPriceInfo> orderReduceMap;

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof CarOrderReduceConfig)) {
            return false;
        }
        super.DSLLoad(config);
        CarOrderReduceConfig promotionConfig = (CarOrderReduceConfig) config;
        this.orderReduceMap = promotionConfig.getOrderReduceMap();
        return true;
    }

    @Override
    public ActivityDetail getActivityDetail() {
        ActivityDetail detail = super.getBasicActivityDetail();
        detail.setPriceInfoMap(orderReduceMap);
        return detail;
    }

    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) throws BizError {
        return null;
    }

    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        return null;
    }

    @Override
    public Boolean checkUsableAddAct(ReduceDetailAddItem item) {
        if (orderReduceMap == null) {
            return false;
        }
        ActPriceInfo actPriceInfo = orderReduceMap.get(item.getSsuId().toString());
        return actPriceInfo != null && actPriceInfo.getPrice() >= item.getReduce();
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.ORDER_REDUCE;
    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR;
    }

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine.condition(CarOrderReduceCondition.class);
    }
}
