package com.xiaomi.nr.promotion.domain.coupon.service.base.type;

import com.xiaomi.nr.promotion.domain.coupon.service.maintenance.MaintenanceDeductCoupon;
import com.xiaomi.nr.promotion.domain.coupon.service.mishop.DeductCoupon;
import com.xiaomi.nr.promotion.domain.coupon.service.mishop.DiscountCoupon;
import com.xiaomi.nr.promotion.domain.coupon.service.mishop.ShipmentCoupon;
import com.xiaomi.nr.promotion.domain.coupon.service.car.CarCashCoupon;
import com.xiaomi.nr.promotion.domain.coupon.service.mishop.CashCoupon;
import com.xiaomi.nr.promotion.engine.CouponTool;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 券工具工厂
 *
 * <AUTHOR>
 * @date 2022/3/3
 */
@Component
public class CouponFactory {
    @Autowired
    private ObjectFactory<CashCoupon> cashCouponObjectFactory;
    @Autowired
    private ObjectFactory<DiscountCoupon> discountCouponObjectFactory;
    @Autowired
    private ObjectFactory<DeductCoupon> deductCouponObjectFactory;
    @Autowired
    private ObjectFactory<ShipmentCoupon> expressCouponObjectFactory;

    @Autowired
    private ObjectFactory<CarCashCoupon> carCashCouponObjectFactory;
    
    @Autowired
    private ObjectFactory<MaintenanceDeductCoupon> maintenanceDeductCouponObjectFactory;

    /**
     * 根据类型获取
     *
     * @param type 类型
     * @return CouponTool
     */
    public CouponTool getByTypeAndBiz(PromotionToolType type, BizPlatformEnum bizPlatform) throws BizError {
        switch (type) {
            case COUPON_CASH:
                return getCashCoupon(bizPlatform);
            case COUPON_DISCOUNT:
                return discountCouponObjectFactory.getObject();
            case COUPON_DEDUCT:
                return getDeductCoupon(bizPlatform);
            default:
                throw ExceptionHelper.create(GeneralCodes.NotFound, String.format("coupon %s not implement", type.toString()));
        }
    }

    public CouponTool getCashCoupon(BizPlatformEnum bizPlatform) {
        if (bizPlatform == BizPlatformEnum.CAR) {
            return carCashCouponObjectFactory.getObject();
        } else {
            return cashCouponObjectFactory.getObject();
        }
    }
    
    public CouponTool getDeductCoupon(BizPlatformEnum bizPlatform) {
        if (bizPlatform == BizPlatformEnum.MAINTENANCE_REPAIR) {
            return maintenanceDeductCouponObjectFactory.getObject();
        } else {
            return deductCouponObjectFactory.getObject();
        }
    }

    public CouponTool getExpressCoupon() throws BizError {
        return expressCouponObjectFactory.getObject();
    }
}
