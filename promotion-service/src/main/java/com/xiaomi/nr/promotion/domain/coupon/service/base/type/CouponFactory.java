package com.xiaomi.nr.promotion.domain.coupon.service.base.type;

import com.xiaomi.newretail.common.tools.base.exception.BaseException;
import com.xiaomi.nr.promotion.domain.coupon.service.carshop.CarShopCashCoupon;
import com.xiaomi.nr.promotion.domain.coupon.service.carshop.CarShopDeductCoupon;
import com.xiaomi.nr.promotion.domain.coupon.service.carshop.CarShopDiscountCoupon;
import com.xiaomi.nr.promotion.domain.coupon.service.carshop.CarShopGiftCoupon;
import com.xiaomi.nr.promotion.domain.coupon.service.maintenance.MaintenanceDeductCoupon;
import com.xiaomi.nr.promotion.domain.coupon.service.mishop.DeductCoupon;
import com.xiaomi.nr.promotion.domain.coupon.service.mishop.DiscountCoupon;
import com.xiaomi.nr.promotion.domain.coupon.service.mishop.ShipmentCoupon;
import com.xiaomi.nr.promotion.domain.coupon.service.car.CarCashCoupon;
import com.xiaomi.nr.promotion.domain.coupon.service.mishop.CashCoupon;
import com.xiaomi.nr.promotion.engine.CouponTool;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 券工具工厂
 *
 * <AUTHOR>
 * @date 2022/3/3
 */
@Component
public class CouponFactory {
    @Autowired
    private ObjectFactory<CashCoupon> cashCouponObjectFactory;
    @Autowired
    private ObjectFactory<DiscountCoupon> discountCouponObjectFactory;
    @Autowired
    private ObjectFactory<CarShopCashCoupon> carShopCashCouponObjectFactory;
    @Autowired
    private ObjectFactory<CarShopDiscountCoupon> carShopDiscountCouponObjectFactory;
    @Autowired
    private ObjectFactory<CarShopDeductCoupon> carShopDeductCouponObjectFactory;

    @Autowired
    private ObjectFactory<CarShopGiftCoupon> carShopGiftCouponObjectFactory;

    @Autowired
    private ObjectFactory<DeductCoupon> deductCouponObjectFactory;
    @Autowired
    private ObjectFactory<ShipmentCoupon> expressCouponObjectFactory;

    @Autowired
    private ObjectFactory<CarCashCoupon> carCashCouponObjectFactory;
    
    @Autowired
    private ObjectFactory<MaintenanceDeductCoupon> maintenanceDeductCouponObjectFactory;

    /**
     * 根据类型获取
     *
     * @param type 类型
     * @return CouponTool
     */
    public CouponTool getByTypeAndBiz(PromotionToolType type, BizPlatformEnum bizPlatform) throws BizError {
        switch (type) {
            case COUPON_CASH:
                return getCashCoupon(bizPlatform);
            case COUPON_DISCOUNT:
                return getDiscountCoupon(bizPlatform);
            case COUPON_DEDUCT:
                return getDeductCoupon(bizPlatform);
            case COUPON_GIFT:
                return getGiftCoupon(bizPlatform);
            default:
                throw ExceptionHelper.create(GeneralCodes.NotFound, String.format("coupon %s not implement", type.toString()));
        }
    }

    public CouponTool getCashCoupon(BizPlatformEnum bizPlatform) {
        if (bizPlatform == BizPlatformEnum.CAR) {
            return carCashCouponObjectFactory.getObject();
        } else if (bizPlatform == BizPlatformEnum.CAR_SHOP) {
            return carShopCashCouponObjectFactory.getObject();
        } else {
            return cashCouponObjectFactory.getObject();
        }
    }

    public CouponTool getDiscountCoupon(BizPlatformEnum bizPlatform) {
        if (bizPlatform == BizPlatformEnum.CAR_SHOP) {
            return carShopDiscountCouponObjectFactory.getObject();
        } else {
            return discountCouponObjectFactory.getObject();
        }
    }

    public CouponTool getDeductCoupon(BizPlatformEnum bizPlatform) {
        if (bizPlatform == BizPlatformEnum.MAINTENANCE_REPAIR) {
            return maintenanceDeductCouponObjectFactory.getObject();
        } else if (bizPlatform == BizPlatformEnum.CAR_SHOP) {
            return carShopDeductCouponObjectFactory.getObject();
        } else {
            return deductCouponObjectFactory.getObject();
        }
    }

    public CouponTool getExpressCoupon() throws BizError {
        return expressCouponObjectFactory.getObject();
    }

    public CouponTool getGiftCoupon(BizPlatformEnum bizPlatform) {
        if (BizPlatformEnum.CAR_SHOP.equals(bizPlatform)) {
            return carShopGiftCouponObjectFactory.getObject();
        }
        throw new BaseException(-1, "兑换券暂不支持当前领域");
    }
}
