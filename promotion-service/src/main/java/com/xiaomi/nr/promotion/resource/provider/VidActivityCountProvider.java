package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionCarActivityCountMapper;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.CarActivityCount;
import com.xiaomi.nr.promotion.mq.producer.CarEquityPerformanceProducer;
import com.xiaomi.nr.promotion.mq.producer.entity.CarEquityPerformanceMessage;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @author: zhangliwei6
 * @date: 2025-01-06 20:31
*/
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class VidActivityCountProvider implements ResourceProvider<VidActivityCountProvider.VidJoinActNum> {

    private ResourceObject<VidActivityCountProvider.VidJoinActNum> resourceObject;

    @Autowired
    private PromotionCarActivityCountMapper carActivityCountMapper;

    @Autowired
    private CarEquityPerformanceProducer carEquityPerformanceProducer;

    @Override
    public ResourceObject<VidJoinActNum> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<VidActivityCountProvider.VidJoinActNum> object) {
        this.resourceObject = object;
    }

    @Override
    public void lock() throws BizError {
        long currentTime = System.currentTimeMillis() / 1000;
        VidActivityCountProvider.VidJoinActNum content = resourceObject.getContent();
        content.addValidOrderId(resourceObject.getOrderId());

        CarActivityCount carActivityCount = carActivityCountMapper.getByVidAndPromotionId(content.getVid(), content.getActId());
        if (carActivityCount == null) {
            carActivityCount = new CarActivityCount();
            carActivityCount.setVid(content.getVid());
            carActivityCount.setPromotionId(content.getActId());
            carActivityCount.setNum(1);
            carActivityCount.setCreateTime(currentTime);
            carActivityCount.setUpdateTime(currentTime);
            carActivityCount.setExtend(GsonUtil.toJson(content));
            carActivityCountMapper.insert(carActivityCount);
        } else {
            int num = carActivityCount.getNum();
            if (!Objects.equals(content.getVidJoinNumLimit(), 0) && num >= content.getVidJoinNumLimit()) {
                throw ExceptionHelper.create(GeneralCodes.InternalError, "该vid超过活动使用次数");
            }

            this.addOldContent(content, carActivityCount);

            Integer effect = carActivityCountMapper.updateCount(content.getVid(), content.getActId(), num + 1, num, GsonUtil.toJson(content), currentTime);
            if (!Objects.equals(effect, 1)) {
                throw ExceptionHelper.create(GeneralCodes.InternalError, "限购更新异常");
            }
        }
        log.info("lock VidActivityCountProvider resource, resourceObject:{}", GsonUtil.toJson(resourceObject));

        // 赛道权益包发车权益mq，失败打日志即可
        CarEquityPerformanceMessage carEquityPerformanceMessage = CarEquityPerformanceMessage.builder()
                // 上游定义的，暂时写死
                .sourceSvc("ACTIVITY")
                .equityKey(content.getCarIdentityId())
                .vid(content.getVid())
                .implAbilityId(String.valueOf(content.getActId()))
                .usageRecordId(String.valueOf(carActivityCount.getId()))
                .usageOrderId(resourceObject.getOrderId())
                .build();
        carEquityPerformanceProducer.sendMessage(List.of(carEquityPerformanceMessage), content.getVid());
    }

    @Override
    public void consume() throws BizError {
        log.info("consume VidActivityCountProvider resource, resourceObject:{}", GsonUtil.toJson(resourceObject));
    }

    @Override
    public void rollback() throws BizError {
        long currentTime = System.currentTimeMillis() / 1000;
        VidActivityCountProvider.VidJoinActNum content = resourceObject.getContent();
        content.addValidOrderId(resourceObject.getOrderId());

        CarActivityCount carActivityCount = carActivityCountMapper.getByVidAndPromotionId(content.getVid(), content.getActId());
        if (carActivityCount == null) {
            return;
        }
        int num = carActivityCount.getNum();
        if (num < 1) {
            return;
        }

        this.addOldContent(content, carActivityCount);

        carActivityCountMapper.updateCount(content.getVid(), content.getActId(), num - 1, num, GsonUtil.toJson(content), currentTime);
        log.info("rollback VidActivityCountProvider resource, resourceObject:{}", GsonUtil.toJson(resourceObject));

        // 发回滚消息，失败打日志即可
        CarEquityPerformanceMessage carEquityPerformanceMessage = CarEquityPerformanceMessage.builder()
                // 上游定义的，暂时写死
                .sourceSvc("ACTIVITY")
                .equityKey(content.getCarIdentityId())
                .vid(content.getVid())
                .implAbilityId(String.valueOf(content.getActId()))
                .usageRecordId(String.valueOf(carActivityCount.getId()))
                .usageOrderId(resourceObject.getOrderId())
                .deleted(true)
                .build();
        carEquityPerformanceProducer.sendMessage(List.of(carEquityPerformanceMessage), content.getVid());
    }

    @Override
    public String conflictText() {
        return "整车参与活动数据处理失败";
    }

    private void addOldContent(VidActivityCountProvider.VidJoinActNum content, CarActivityCount carActivityCount) throws BizError {
        VidActivityCountProvider.VidJoinActNum oldContent = GsonUtil.fromJson(carActivityCount.getExtend(), VidActivityCountProvider.VidJoinActNum.class);
        if (Objects.isNull(oldContent)) {
            log.warn("oldContent is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "oldContent is null");
        }

        oldContent.getValidOrderId().forEach((k,v) -> {
            content.addValidOrderId(k);
        });

        oldContent.getInValidOrderId().forEach((k,v) -> {
            content.addInValidOrderId(k);
        });
    }

    @Data
    public static class VidJoinActNum {

        private static final Integer DEFAULT_INCR_NUM = 1;

        /**
         * 用户ID
         */
        private Long uid;

        /**
         * 活动ID
         */
        private Long actId;

        /**
         * VID
         */
        private String vid;

        /**
         * 身份类型
         */
        private Integer carIdentifyType;

        /**
         * 身份id
         */
        private String carIdentityId;

        /**
         * 有效订单id
         */
        private Map<Long, Integer> validOrderId = new HashMap<>();

        /**
         * 无效订单id
         */
        private Map<Long, Integer> inValidOrderId = new HashMap<>();

        /**
         * 整车参与活动次数限制
         */
        private Integer vidJoinNumLimit;

        public void addValidOrderId(Long validOrderId) {
            Map<Long, Integer> valid = Optional.ofNullable(this.validOrderId).orElse(new HashMap<>());
            valid.put(validOrderId, DEFAULT_INCR_NUM);
            this.validOrderId = valid;
        }

        public void addInValidOrderId(Long inValidOrderId) {
            Map<Long, Integer> inValid = Optional.ofNullable(this.inValidOrderId).orElse(new HashMap<>());
            inValid.put(inValidOrderId, DEFAULT_INCR_NUM);
            this.inValidOrderId = inValid;
        }
    }
}
