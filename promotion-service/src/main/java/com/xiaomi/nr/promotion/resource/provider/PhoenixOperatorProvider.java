package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.dao.redis.StoreRedisDao;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.rpc.phoenix.ChinaUnicomHsServiceProxy;
import com.xiaomi.nr.promotion.rpc.phoenix.PhoenixOperatorServiceProxy;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 三方优惠资源-运营商入驻
 *
 * <AUTHOR>
 * @date 2022/9/21
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class PhoenixOperatorProvider implements ResourceProvider<PhoenixOperatorProvider.ResContent> {
    private ResourceObject<PhoenixOperatorProvider.ResContent> resourceObject;
    @Autowired
    private PhoenixOperatorServiceProxy phoenixOperatorServiceProxy;

    @Override
    public ResourceObject<PhoenixOperatorProvider.ResContent> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<PhoenixOperatorProvider.ResContent> object) {
        this.resourceObject = object;
    }

    @Override
    public void lock() throws BizError {
        ResContent content = resourceObject.getContent();
        phoenixOperatorServiceProxy.freezeCoupon(content.businessType, content.userId, content.orgCode,
                content.orderId, content.orderTime,content.amount, content.payBarCode, content.params);
        log.info("freeze chinaUnicom coupon ok. resourceObject:{}", resourceObject);
    }

    @Override
    public void consume() throws BizError {
    }

    @Override
    public void rollback() throws BizError {
        ResContent content = resourceObject.getContent();
        phoenixOperatorServiceProxy.unFreezeCoupon(content.businessType, content.userId, content.orgCode,
                content.orderId, content.orderTime, content.amount);
        log.info("unfreeze chinaUnicom coupon ok. resourceObject:{}", resourceObject);
    }

    @Override
    public String conflictText() {
        return null;
    }

    @Data
    public static class ResContent {
        /**
         * 业务请求类型
         */
        private Integer businessType;
        /**
         * 用户ID
         */
        private Long userId;
        /**
         * 门店Code
         */
        private String orgCode;
        /**
         * 订单id
         */
        private Long orderId;
        /**
         * 提单时间
         */
        private Long orderTime;
        /**
         * 优惠金额
         */
        private Long amount;
        /**
         * 支付条码
         */
        private String payBarCode;
        /**
         * 关联信息
         */
        private Map<String, String> params;
    }
}
