package com.xiaomi.nr.promotion.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description 汽车售后工单类型
 * <AUTHOR>
 * @date 2025-01-04 11:02
*/
@Getter
@AllArgsConstructor
public enum WorkOrderTypeEnum {

    /**
     * 到店维保
     */
    IN_STORE_MAINTENANCE(1, "到店维保"),

    /**
     * 上门维保
     */
    HOME_MAINTENANCE(2, "上门维保"),

    /**
     * 售前维修
     */
    PRE_SALE_REPAIR(3, "售前维修"),

    /**
     * 赛道整备
     */
    TRACK_PREPARATION(4, "赛道整备"),
    ;

    private final Integer val;

    private final String name;

}
