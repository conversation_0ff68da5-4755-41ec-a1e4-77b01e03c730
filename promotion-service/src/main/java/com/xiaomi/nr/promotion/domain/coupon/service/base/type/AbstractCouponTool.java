package com.xiaomi.nr.promotion.domain.coupon.service.base.type;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.api.dto.model.CouponInfo;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.constant.CouponConstant;
import com.xiaomi.nr.promotion.constant.PriceConstant;
import com.xiaomi.nr.promotion.domain.coupon.model.*;
import com.xiaomi.nr.promotion.engine.CouponTool;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.CouponQuotaTypeEnum;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import com.xiaomi.nr.promotion.enums.PostFreeEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.GlobaExcludeExternalProvider;
import com.xiaomi.nr.promotion.resource.external.GlobalCouponInExcludeExternalProvider;
import com.xiaomi.nr.promotion.resource.external.GoodsHierarchyExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.CompareHelper;
import com.xiaomi.nr.promotion.util.CouponHelper;
import com.xiaomi.nr.promotion.util.ExpressHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.NumberUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 优惠券工具
 *
 * <AUTHOR>
 * @date 2022/3/3
 */
@Slf4j
public abstract class AbstractCouponTool implements CouponTool {
    /**
     * 劵配置id
     */
    protected Long id;

    /**
     * 参与商品信息
     */
    protected CompareItem joinGoods;


    protected CheckoutCoupon checkoutCoupon;

    /**
     * 优惠券实际使用商品范围
     */
    protected List<CouponRangeGoodsDO> validCouponRangeGoodsList;

    @Autowired
    private transient CheckoutCartTool checkoutCartTool;

    @Override
    public CouponCheckoutResult checkoutCoupon(CouponCheckoutContext couponCheckoutContext) throws BizError {
        return null;
    }


    /**
     * 获取商品层级关系
     * @param providerMap 外部资源map
     * @return 商品层级关系
     * @throws BizError
     */
    protected Map<String, GoodsHierarchy> getGoodsHierarchy(Map<ResourceExtType, ExternalDataProvider<?>> providerMap) throws BizError {
        GoodsHierarchyExternalProvider provider = (GoodsHierarchyExternalProvider) providerMap.get(ResourceExtType.GOODS_HIERARCHY);
        return null == provider ? Collections.emptyMap() : provider.getData();
    }

    /**
     * 获取全局排除商品
     * @param providerMap 外部资源map
     * @return 全局排除商品
     * @throws BizError
     */
    protected CompareItem getGlobalInExclude(Map<ResourceExtType, ExternalDataProvider<?>> providerMap) throws BizError {
        GlobaExcludeExternalProvider provider = (GlobaExcludeExternalProvider) providerMap.get(ResourceExtType.GLOBAL_IN_EXCLUDE);
        return provider.getData();
    }

    /**
     * 获取全局优惠劵排除商品
     * @param providerMap 外部资源map
     * @return 全局优惠劵排除商品
     * @throws BizError
     */
    protected CompareItem getGlobalCouponInExclude(Map<ResourceExtType, ExternalDataProvider<?>> providerMap) throws BizError {
        GlobalCouponInExcludeExternalProvider provider = (GlobalCouponInExcludeExternalProvider) providerMap.get(ResourceExtType.GLOBAL_COUPON_EXCLUDE);
        return provider.getData();
    }

    /**
     * 符合列表
     *
     * @param cartList      购物车列表
     * @param context       上下文
     * @param joinGoods     参与商品
     * @param couponTypeId  劵类型id
     * @return 符合列表
     */
    protected List<GoodsIndex> matchCartList(List<CartItem> cartList, CheckoutContext context, CompareItem joinGoods,
                                             Long couponTypeId) throws BizError{
        Map<String, GoodsHierarchy> hierarchyMap = getGoodsHierarchy(context.getExternalDataMap());
        CompareItem globalInExclude = getGlobalInExclude(context.getExternalDataMap());
        CompareItem globalCouponInExclude = getGlobalCouponInExclude(context.getExternalDataMap());

        // 查找符合包含条件的购物车item列表
        List<GoodsIndex> indexList = new ArrayList<>();
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);

            if (SourceEnum.isGiftBargain(item.getSource())) {
                continue;
            }
            //处理当前item是否可参加活动
            boolean itemQualify = CartHelper.isCouponQualifyItem(item, couponTypeId);
            if (!itemQualify) {
                log.info("coupon:{} matchCartList itemQualify item:{}", getCouponId(), GsonUtil.toJson(item));
                continue;
            }
            // 做单品或套装的匹配
            boolean isMatched;
            GoodsHierarchy goodsHierarchy;
            if (StringUtils.isNotBlank(item.getSku())) {
                goodsHierarchy = hierarchyMap.get(item.getSku());
            } else if (StringUtils.isNotBlank(item.getPackageId())) {
                goodsHierarchy = hierarchyMap.get(item.getPackageId());
            } else {
                log.info("coupon:{} matchCartList goodsHierarchy item:{}", getCouponId(), GsonUtil.toJson(item));
                continue;
            }
            isMatched = doMatch(goodsHierarchy, globalInExclude, globalCouponInExclude, joinGoods);
            if (!isMatched) {
                log.info("coupon:{} matchCartList isMatched item:{}", getCouponId(), GsonUtil.toJson(item));

                continue;
            }
            indexList.add(new GoodsIndex(item.getItemId(), idx));
        }
        return indexList;
    }


    private boolean doMatch(GoodsHierarchy goodsHierarchy, CompareItem globalInExclude,
                            CompareItem globalCouponInExclude, CompareItem joinGoods) {
        if (goodsHierarchy == null) {
            return false;
        }
        //是否满足全局排除
        if (CompareHelper.isFillInclude(goodsHierarchy, globalInExclude, true)) {
            return false;
        }
        // 满足全局优惠劵排除的
        if (CompareHelper.isFillInclude(goodsHierarchy, globalCouponInExclude, true)) {
            return false;
        }
        //是否属于活动配置中的包含商品
        return CompareHelper.isFillInclude(goodsHierarchy, joinGoods, true);
    }


    protected Pair<Boolean, String> isSatisfiedQuota(ValidGoods validGoods) {
        StringBuilder reasonBuilder = new StringBuilder();
        reasonBuilder.append("订单中用券商品需满");
        CouponQuotaTypeEnum quotaType = CouponQuotaTypeEnum.valueOf(checkoutCoupon.getBottomType());
        if (quotaType == null) {
            return Pair.of(false, StringUtils.EMPTY);
        }
        boolean isSatisfied = false;
        switch (quotaType) {
            case QUOTA_MONEY:
                isSatisfied = validGoods.getValidPrice() >= checkoutCoupon.getBottomPrice();
                long lastMoney = checkoutCoupon.getBottomPrice() - validGoods.getValidPrice();
                reasonBuilder.append(String.format("%.2f元, 还差%.2f元", NumberUtil.amountConvertF2Y(checkoutCoupon.getBottomPrice().longValue()), NumberUtil.amountConvertF2Y(lastMoney)));

                break;
            case QUOTA_COUNT:
                isSatisfied = validGoods.getValidNum() >= checkoutCoupon.getBottomCount();
                long lastCount = checkoutCoupon.getBottomCount() - validGoods.getValidNum();
                reasonBuilder.append(String.format("%d件, 还差%d件", checkoutCoupon.getBottomCount(), lastCount));
                break;
            default:
                log.error("coupon policy quota type is wrong. couponTypeId:{}", id);
                break;
        }
        return Pair.of(isSatisfied, reasonBuilder.toString());
    }

    @Override
    public boolean load(CouponOwnedInfo couponOwnedInfo) throws BizError {

        return false;
    }

    public boolean load(CheckoutCoupon checkoutCoupon) throws BizError {
        this.id = checkoutCoupon.getCouponId();
        this.checkoutCoupon = checkoutCoupon;
        CompareItem validGoods = new CompareItem();
        if (checkoutCoupon.getValidSkuList() != null) {

            List<String> skuList = checkoutCoupon.getValidSkuList()
                    .stream().map(String::valueOf)
                    .collect(Collectors.toList());
            validGoods.setSku(skuList);
        }
        if (checkoutCoupon.getValidPackageList() != null) {
            List<String> packageList = checkoutCoupon.getValidPackageList()
                    .stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());

            validGoods.setPackages(packageList);
        }
        if (checkoutCoupon.getValidGoodsList() != null) {
            List<String> ssuList = checkoutCoupon.getValidGoodsList()
                    .stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList());

            validGoods.setCommodity(ssuList);
        }
        this.joinGoods = validGoods;
        return true;

    }

    /**
     * 生成展示层券信息
     *
     * @return
     */
    protected Coupon initCoupon() {
        Coupon coupon = new Coupon();
        coupon.setId(checkoutCoupon.getCouponId());
        coupon.setStat(checkoutCoupon.getStatus());
        coupon.setStartTime(checkoutCoupon.getStartTime());
        coupon.setEndTime(checkoutCoupon.getEndTime());
        coupon.setCouponTypeId(checkoutCoupon.getConfigId());


        coupon.setType(checkoutCoupon.getType());
        if (coupon.getType() == CouponTypeEnum.CASH.getType()) {
            BigDecimal promotionValueDesc = NumberUtil.amountConvertF2Y(checkoutCoupon.getPromotionValue());
            coupon.setShowTitle(String.valueOf(promotionValueDesc));
            coupon.setShowUnit("元");
        } else if (coupon.getType() == CouponTypeEnum.DISCOUNT.getType()) {
            BigDecimal promotionValueDesc = new BigDecimal(checkoutCoupon.getPromotionValue())
                    .divide(new BigDecimal(10), 2,  RoundingMode.DOWN);
            coupon.setShowTitle(promotionValueDesc.toPlainString());
            coupon.setShowUnit("折");
        } else if (coupon.getType() == CouponTypeEnum.DEDUCT.getType()){
            BigDecimal divide = new BigDecimal(checkoutCoupon.getPromotionValue())
                    .divide(new BigDecimal(100), 2, RoundingMode.DOWN);
            coupon.setShowTitle(divide.toPlainString());
        } else if (coupon.getType() == CouponTypeEnum.GIFT.getType()){
            BigDecimal divide = new BigDecimal(checkoutCoupon.getPromotionValue())
                    .divide(new BigDecimal(100), 2, RoundingMode.DOWN);
            coupon.setShowTitle(divide.toPlainString());
        }
        coupon.setIsShare(checkoutCoupon.getShare() == null? null: checkoutCoupon.getShare().longValue());
        coupon.setCouponTypeRangeDesc(checkoutCoupon.getCouponRangeDesc());
        coupon.setCouponTypeName(checkoutCoupon.getCouponName());
        coupon.setTypeCode(checkoutCoupon.getTypeCode());
        coupon.setPostFree(checkoutCoupon.getPostageFree());

        coupon.setCheckPrice(BooleanUtils.toInteger(true));


        coupon.setExInfo(new HashMap<>());
        if (checkoutCoupon.getValidCode() == 0) {
            coupon.setAllow(BooleanUtils.toInteger(true));
        } else {
            coupon.updateInvalidInfo(checkoutCoupon.getValidCode(), checkoutCoupon.getInvalidReason(), checkoutCoupon.getInvalidData());
            coupon.setAllow(BooleanUtils.toInteger(false));
        }



        coupon.setLimitUseRegion(checkoutCoupon.getLimitUseRegion());
        coupon.setTags(Optional.ofNullable(checkoutCoupon.getTags()).orElse(new ArrayList<>()));
        coupon.setValidGoodsPrice(0L);
        coupon.setCouponGroupNo(checkoutCoupon.getCouponGroupNo());
        coupon.setCouponType(checkoutCoupon.getCouponType());
        coupon.setUseChannel(checkoutCoupon.getUseChannel());
        coupon.setSendChannel(checkoutCoupon.getSendChannel());
        coupon.setUseChannelDesc(checkoutCoupon.getUseChannelDesc());
        coupon.setBottomType(checkoutCoupon.getBottomType());
        coupon.setBottomPrice(checkoutCoupon.getBottomPrice());
        coupon.setBottomCount(checkoutCoupon.getBottomCount());
        coupon.setCheckoutStage(checkoutCoupon.getCheckoutStage());
        return coupon;
    }



    /**
     * 更新分摊的共有操作
     * @param request
     * @param context
     */
    protected void updateCommonInfo(CheckoutPromotionRequest request, CheckoutContext context) {


        String couponBaseInfoList = null;
        List<Coupon> couponList;
        if (StringUtils.isEmpty(context.getCouponBaseInfoList())) {
            couponList = new ArrayList<>();
        } else {
            couponList = GsonUtil.fromListJson(context.getCouponBaseInfoList(), Coupon.class);
        }
        Coupon coupon = this.initCoupon();
        couponList.add(coupon);
        couponBaseInfoList = GsonUtil.toJson(couponList);


        context.setCouponBaseInfoList(couponBaseInfoList);



        context.setCouponName(checkoutCoupon.getCouponName());
        context.setCouponType(checkoutCoupon.getType());
    }

    protected void updateExpressInfo(CheckoutContext context, CouponCheckoutResult result) {


        long couponId = result.getCouponId();

        String idKey = CouponHelper.getCartListCouponKey(String.valueOf(couponId));

        List<String> ids = result.getValidGoods().stream().map(GoodsIndex::getItemId).collect(Collectors.toList());

        Integer postFree = checkoutCoupon.getPostageFree();
        if (postFree == PostFreeEnum.POST_FREE.getVal()) {
            //处理免邮
            Express express = ExpressHelper.buildNew(idKey, postFree, ids, PriceConstant.POST_FREE_PRICE);
            context.appendExpress(express);
        }
    }

    /**
     * 处理商品分摊
     * @param cartList
     * @param result
     */
    @Override
    public void updateItemReduce(List<CartItem> cartList, CouponCheckoutResult result) {

        long couponId = result.getCouponId();
        String idKey = CouponHelper.getCartListCouponKey(String.valueOf(couponId));
        List<Integer> indexList = result.getValidGoods().stream().map(GoodsIndex::getIndex).collect(Collectors.toList());
        //分摊金额
        checkoutCartTool.divideCartsReduce(result.getReduceAmount(), indexList, cartList, idKey, getCouponType().getType(), couponId);
    }

    @Override
    public void updateItemReduce(CouponCheckoutContext context, CouponCheckoutResult result) {
        return;
    }


    @Override
    public void doCheckout(CheckoutPromotionRequest request, CheckoutContext checkoutContext) throws BizError {

    }

    @Override
    public CouponInfo generateCouponInfo(CouponCheckoutResult result) {
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setOffline(CouponConstant.COUPON_ONLINE_DB);
        couponInfo.setCouponCode("");
        if (couponInfo.getCouponCode() != null) {

            couponInfo.setCouponCode(checkoutCoupon.getCouponCode());
        }
        couponInfo.setCouponId(checkoutCoupon.getCouponId());

        couponInfo.setReduceMoney(result.getReduceAmount());
        couponInfo.setCouponName(checkoutCoupon.getCouponName());
        couponInfo.setCouponType(checkoutCoupon.getType());
        return couponInfo;
    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.NEW_RETAIL;
    }
}
