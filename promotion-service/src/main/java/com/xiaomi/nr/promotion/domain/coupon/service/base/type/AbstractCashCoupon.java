package com.xiaomi.nr.promotion.domain.coupon.service.base.type;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.enums.CouponQuotaTypeEnum;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.common.utils.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by wangweiyi on 2023/9/21
 */
@Slf4j
public abstract class AbstractCashCoupon extends AbstractCouponTool {

    protected Set<Long> includeGoods;

    @Override
    public Long getCouponId() {
        return id;
    }

    @Override
    public CouponTypeEnum getCouponType() {
        return CouponTypeEnum.CASH;
    }

    @Override
    public Coupon generateCartCoupon() {
        return initCoupon();
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.COUPON_CASH;
    }

    public abstract Boolean checkCoupon(CheckoutPromotionRequest request, CheckoutContext context);

    @Override
    public boolean load(CheckoutCoupon checkoutCoupon) throws BizError {
        this.id = checkoutCoupon.getCouponId();
        this.checkoutCoupon = checkoutCoupon;
        if (checkoutCoupon.getValidGoodsList() != null) {
            this.includeGoods = new HashSet<>(checkoutCoupon.getValidGoodsList());
        } else {
            this.includeGoods = new HashSet<>();
        }
        return true;
    }

    @Override
    public CouponCheckoutResult checkoutCoupon(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {

        // 券可用逻辑校验
        if (!checkCoupon(request, context)) {
            CouponCheckoutResult result = new CouponCheckoutResult();
            result.setAllow(false);
            result.setCouponId(this.id);
            if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                result.setCouponCode(checkoutCoupon.getCouponCode());
            }
            result.setUnusableCode(ErrCode.ERR_COUPON_MONEY_NUM.getCode());
            result.setUnusableReason("此券不满足使用条件");
            result.setKeyDataUnusable("rdata_invalid_coupon_reason");
        }

        // 筛选满足商品列表
        List<CartItem> cartList = request.getCartList();
        List<GoodsIndex> indexList = matchCartList(cartList, context);

        if (CollectionUtils.isEmpty(indexList)) {
            CouponCheckoutResult result = new CouponCheckoutResult();
            result.setAllow(false);
            result.setCouponId(this.id);
            if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                result.setCouponCode(checkoutCoupon.getCouponCode());
            }
            result.setUnusableCode(ErrCode.ERR_COUPON_NO_VALID_GOODS.getCode());
            result.setUnusableReason("订单中不含可用券商品");
            result.setKeyDataUnusable("rdata_invalid_coupon_reason");
        }
        // 计算符合总价
        ValidGoods validGoods = buildValidGoods(cartList, indexList);
        // 判断
        CouponCheckoutResult result = new CouponCheckoutResult();
        Pair<Boolean, String> pair = isSatisfiedQuota(validGoods);
        long cartTotalPrice = getCartTotalPrice(cartList);
        // 构建结果
        // 条件满足
        if (pair.getLeft()) {
            result.setCouponId(this.id);
            if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                result.setCouponCode(checkoutCoupon.getCouponCode());
            }
            result.setAllow(true);
            result.setValidGoodsPrice(validGoods.getValidPrice());
            result.setValidGoods(indexList);
            // 计算减免金额
            Long reduceMoney = getReduce(cartTotalPrice, validGoods);


            result.setReduceAmount(reduceMoney);
            result.setCouponReduce(checkoutCoupon.getPromotionValue());
            result.setBudgetApplyNo(checkoutCoupon.getBudgetApplyNo());
            result.setLineNum(checkoutCoupon.getLineNum());
        } else {
            result.setCouponId(this.id);
            if (StringUtils.isNotBlank(checkoutCoupon.getCouponCode())) {
                result.setCouponCode(checkoutCoupon.getCouponCode());
            }
            result.setAllow(false);
            result.setUnusableReason("未满足优惠券使用条件");
            result.setUnusableCode(ErrCode.ERR_COUPON_MONEY_NUM.getCode());
            result.setKeyDataUnusable(pair.getRight());
            result.setCouponReduce(checkoutCoupon.getPromotionValue());
        }

        return result;

    }

    protected Long getReduce(long cartTotalPrice, ValidGoods validGoods) {
        CouponQuotaTypeEnum quotaType = CouponQuotaTypeEnum.valueOf(checkoutCoupon.getType());
        if (quotaType == null) {
            return 0L;
        }
        long reduceMoney = checkoutCoupon.getPromotionValue();

        reduceMoney = Math.min(reduceMoney, validGoods.getValidPrice());

        // 减的金额需小于当前总价，消除0元订单
        if (reduceMoney >= 1 && reduceMoney == validGoods.getValidPrice() && cartTotalPrice == validGoods.getValidPrice()) {
            reduceMoney -= 1L;
        }
        return reduceMoney;
    }

    protected abstract ValidGoods buildValidGoods(List<CartItem> cartList, List<GoodsIndex> goodsInd);

    protected abstract List<GoodsIndex> matchCartList(List<CartItem> cartList, CheckoutContext context) throws BizError;


    protected abstract long getCartTotalPrice(List<CartItem> cartList);

}
