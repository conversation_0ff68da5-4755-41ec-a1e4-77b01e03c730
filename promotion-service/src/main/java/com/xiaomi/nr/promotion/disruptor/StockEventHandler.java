package com.xiaomi.nr.promotion.disruptor;

import com.lmax.disruptor.EventHandler;

import java.util.function.Consumer;

/**
 * 活动库存事件处理器
 *
 * <AUTHOR>
 */
public class StockEventHandler implements EventHandler<StockEvent> {
    private Consumer<StockRecord> consumer;

    public StockEventHandler(Consumer<StockRecord> consumer) {
        this.consumer = consumer;
    }

    public void onEvent(StockEvent stockEvent, long l, boolean b) throws Exception {
        this.consumer.accept(stockEvent.getStockRecord());
    }
}
