package com.xiaomi.nr.promotion.componet.condition.carsale;

import com.google.common.collect.Lists;
import com.xiaomi.nr.md.promotion.admin.api.constant.TradeType;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.SubmitTypeEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.GoodsReduceInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 立减条件判断
 *
 * <AUTHOR>
 * @date 2022/07/11
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CarBuyReduceCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 活动类型
     */
    private PromotionToolType promotionType;
    /**
     * 下单立减信息
     * key: skuPackage val:GoodsReduceInfo
     */
    private Map<String, GoodsReduceInfo> reduceInfoMap;

    /**
     * 交易模式
     */
    private Integer tradeType;


    /**
     * - 遍历购物车,合并同一个sku或套装的item
     * - 过滤不能参加直降的. 查找是否找到对应的直降信息， 以及限购数是否符合
     * - 筛选购物车中可以参加直降的列表
     *
     * @param request 请求参数
     * @param context 活动内上下文
     * @return 是否满足
     */
    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) {
        if (MapUtil.isEmpty(reduceInfoMap)) {
            log.warn("reduceInfoMap is empty. actId:{} uid:{}", promotionId, request.getUserId());
            return false;
        }

        // check biz platform
        if (!Objects.equals(context.getBizPlatform(), BizPlatformEnum.CAR)) {
            return false;
        }

        // check submit type
        // check submit type
        if (!Objects.equals(request.getSubmitType(), SubmitTypeEnum.PRESENT.getCode()) || !Objects.equals(tradeType, TradeType.PRESENT.getCode())) {
            return false;
        }


        List<CartItem> cartList = request.getCartList();

        // 筛选满足数据
        List<GoodsIndex> indexList = goodsActMatch(cartList, context);

        // 没有符合的商品，不满足活动
        if (CollectionUtils.isEmpty(indexList)) {
            return false;
        }
        context.setGoodIndex(indexList);
        return true;
    }


    private List<GoodsIndex> goodsActMatch(List<CartItem> cartList, LocalContext context) {
        List<GoodsIndex> indexList = Lists.newArrayList();


        if (CollectionUtils.isEmpty(cartList)) {
            return indexList;
        }
        for (int idx = 0; idx < cartList.size(); idx++) {
            CartItem item = cartList.get(idx);
            String ssuId = CartHelper.getSkuPackage(item,context);
            if (StringUtils.isEmpty(ssuId)) {
                continue;
            }

            // 规则判断
            boolean itemCheck = CartHelper.checkItemActQualifyCommon(item, promotionType.getTypeId());
            if (!itemCheck) {
                continue;
            }

            // 是否匹配
            GoodsReduceInfo reduceInfo = reduceInfoMap.get(ssuId);
            if (reduceInfo == null) {
                continue;
            }

            Long currentPrice = CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList());
            if (currentPrice <= 0) {
                continue;
            }


            indexList.add(new GoodsIndex(item.getItemId(), idx));
        }
        return indexList;
    }


    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof BuyReducePromotionConfig)) {
            log.error("config is not instanceof BuyReducePromotionConfig. config:{}", config);
            return;
        }
        BuyReducePromotionConfig buyReduceConfig = (BuyReducePromotionConfig) config;
        this.promotionId = buyReduceConfig.getPromotionId();
        this.promotionType = buyReduceConfig.getPromotionType();
        this.reduceInfoMap = buyReduceConfig.getBuyReduceInfoMap();
        this.tradeType = buyReduceConfig.getTradeType();
    }
}
