package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.disruptor.ActStockActionEnum;
import com.xiaomi.nr.promotion.disruptor.ActStockManager;
import com.xiaomi.nr.promotion.disruptor.StockRecord;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 线下全部门店每天参与活动次数
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OfflineActAllStoreLimitDailyProvider implements ResourceProvider<OfflineActAllStoreLimitDailyProvider.ActAllStoreDayLimit> {
    /**
     * 线上用户参与活动记录
     */
    private ResourceObject<ActAllStoreDayLimit> resourceObject;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Autowired
    private ActStockManager actStockManager;

    @Autowired
    private NacosConfig nacosConfig;

    @Override
    public ResourceObject<ActAllStoreDayLimit> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<ActAllStoreDayLimit> object) {
        this.resourceObject = object;
    }

    /**
     * 扣减库存
     */
    @Override
    public void lock() throws BizError {
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("OfflineActAllStoreLimitDailyProvider.lock(): writeSwitch  has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存失败");
        }
        log.info("lock act all store daily limit resource. {}", resourceObject);
        activityRedisDao.incrActAllStoreDayLimitNum(resourceObject.getContent().getActId(),
                resourceObject.getContent().getDateTimeMills(),
                resourceObject.getContent().getCount(),
                resourceObject.getContent().getLimitNum());
        log.info("lock act all store daily limit resource ok. {}", resourceObject);
        actStockManager.publishEvent(() -> stockManage(ActStockActionEnum.COMMIT));
    }

    @Override
    public void consume() {
        log.info("consume act all store daily limit resource. {}", resourceObject);
    }

    @Override
    public void rollback() throws BizError {
        // 写开关关闭: 阻断缓存写、库存扣减
        if (!nacosConfig.isWriteSwitch()) {
            log.error("OfflineActAllStoreLimitDailyProvider.rollback(): writeSwitch  has been turned off. isWriteSwitch: {}", nacosConfig.isWriteSwitch());
            throw ExceptionHelper.create(GeneralCodes.InternalError, "写开关关闭, 扣减库存回滚失败");
        }
        log.info("rollback act all store daily limit resource. {}", resourceObject);
        activityRedisDao.decrActAllStoreDayLimitNum(resourceObject.getContent().getActId(),
                resourceObject.getContent().getDateTimeMills(),
                resourceObject.getContent().getCount());
        log.info("rollback act all store daily limit resource ok. {}", resourceObject);
        actStockManager.publishEvent(() -> stockManage(ActStockActionEnum.ROLLBACK));
    }

    @Override
    public String conflictText() {
        return "减限购失败";
    }

    private StockRecord stockManage(ActStockActionEnum actionEnum) {
        Long actId = resourceObject.getContent().getActId();
        Long actLimitNum = resourceObject.getContent().getLimitNum();
        Long dateMills = resourceObject.getContent().getDateTimeMills();
        Integer limitNum = activityRedisDao.getActAllStoreDayLimitNum(actId, dateMills);
        if (actLimitNum == null || actLimitNum == 0L ||
                (ActStockActionEnum.COMMIT == actionEnum && limitNum < actLimitNum)) {
            return null;
        }
        return new StockRecord().setActivityId(actId)
                .setResourceType(getResource().getResourceType().getValue())
                .setAction(actionEnum.getCode())
                .setDateTimeMills(System.currentTimeMillis());
    }

    /**
     * 活动记录
     */
    @Data
    public static class ActAllStoreDayLimit {
        /**
         * 活动ID
         */
        private Long actId;
        /**
         * 时间(毫秒）
         */
        private Long dateTimeMills;
        /**
         * 数量
         */
        private Integer count;
        /**
         * 限制
         */
        private Long limitNum;
    }
}
