package com.xiaomi.nr.promotion.activity.pool;

import com.google.common.collect.Lists;
import com.xiaomi.nr.md.promotion.admin.api.constant.SourceAppEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityFilterTip;
import com.xiaomi.nr.md.promotion.admin.api.dto.params.ActivityConfigForPromotionResponse;
import com.xiaomi.nr.promotion.activity.ActivityFactory;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.dsl.DSLGeneralPromotion;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.*;
import com.xiaomi.nr.promotion.model.promotionconfig.loader.PromotionConfigLoaderAdapter;
import com.xiaomi.nr.promotion.rpc.mdpromotionadmin.PromotionAdminCustomServiceProxy;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xiaomi.nr.promotion.constant.PromotionCacheConstant.Car.*;

/**
 * 汽车、车商城在线的所有优惠活动数据池
 *
 */
@Slf4j
@Component
@ConditionalOnExpression(value = "'${biz.area.id.list}'.contains('CAR')")
public class CarPromotionInstancePool implements InitializingBean {

    /**
     * 本地锁
     */
    private final Lock lock = new ReentrantLock();

    /**
     * 有序活动列表, 本列表有序，使用时以本列表为 唯一基线
     */
    private List<Long> sortedActivityList = new CopyOnWriteArrayList<>();

    /**
     * 当前活动列表
     * key：activityId val:tool {@link ActivityTool}
     */
    @Getter
    private Map<Long, ActivityTool> activityToolCacheMap = new ConcurrentHashMap<>();

    /**
     * sku package可以参加的活动列表
     * key：skuPackage(注：规划之后给gid用) val:act Id List
     */
    private Map<String, List<Long>> skuPackageToActivityIdsCacheMap = new ConcurrentHashMap<>();

    /**
     * 指定channel可以参加的活动列表
     * key：skuPackage(注：规划之后给gid用) val:act Id List
     */
    private Map<Integer, List<Long>> channelToActivityIdsCacheMap = new ConcurrentHashMap<>();

    /**
     * 黑名单商品活动，所有商品都能参加，在活动内判断是否命中黑名单
     */
    private volatile List<Long> goodsCommonActivityIds = new ArrayList<>();

    @Getter
    private volatile long lastCacheUpdateTime = System.currentTimeMillis();

    /**
     * 当期最新的版本id
     */
    private final AtomicLong latestSeqId = new AtomicLong(0);

    @Autowired
    private ActivityFactory activityFactory;
    @Autowired
    private PromotionConfigFactory promotionConfigFactory;
    @Autowired
    private PromotionConfigLoaderAdapter promotionConfigLoaderAdapter;
    @Autowired
    private PromotionAdminCustomServiceProxy promotionAdminCustomServiceProxy;

    /**
     * 根据ID列表获取活动列表, 正常业务勿用
     * @return 活动工具列表
     */
    public Map<Long, ActivityTool> getActivityIdListForTool(List<Long> activityIdList) {
        if (CollectionUtils.isEmpty(activityIdList)) {
            return new HashMap<>(activityToolCacheMap);
        }
        return activityIdList.stream().map(activityToolCacheMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ActivityTool::getId, Function.identity(), (x, y) -> x));
    }

    /**
     * 根据ID列表获取活动列表, 正常业务勿用
     * @return 活动工具列表
     */
    public Map<String, List<Long>> getSku2ActsMapForTool(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return new HashMap<>(skuPackageToActivityIdsCacheMap);
        }
        return skuList.stream().collect(Collectors.toMap(Function.identity(), x -> skuPackageToActivityIdsCacheMap.getOrDefault(x, Lists.newArrayList())));
    }

    /**
     * 根据ID列表获取活动列表, 正常业务勿用
     * @return 活动工具列表
     */
    public Map<Integer, List<Long>> getChannel2ActsMapForTool(List<Integer> channelList) {
        if (CollectionUtils.isEmpty(channelList)) {
            return new HashMap<>(channelToActivityIdsCacheMap);
        }
        return channelList.stream().collect(Collectors.toMap(Function.identity(), x -> channelToActivityIdsCacheMap.getOrDefault(x, Lists.newArrayList())));
    }

    /**
     * 根据ID列表获取活动列表, 正常业务勿用
     * @return 活动工具列表
     */
    public List<Long> getCommonActListForTool() {
        return new ArrayList<>(goodsCommonActivityIds);
    }

    /**
     * 根据ID列表获取活动列表, 正常业务勿用
     * @return 活动工具列表
     */
    public List<Long> getSortedActivityListForTool() {
        return new ArrayList<>(sortedActivityList);
    }

    /**
     * 根据ID列表获取活动列表
     *
     * @param actIdList 活动ID列表
     * @return 活动工具列表
     */
    public List<ActivityTool> getCurrentTools(List<Long> actIdList) {
        if (CollectionUtils.isEmpty(actIdList)) {
            return Lists.newArrayList();
        }
        // 迭代获取
        return actIdList.stream().map(activityToolCacheMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public ActivityTool getCurrentTool(Long actId) {
        return activityToolCacheMap.get(actId);
    }

    /**
     * 获取对应sku 或packageId 能参加的活动列表
     *
     * @param skuPackageList 列表
     * @return 活动ID集
     */
    public Set<Long> getCurrentActIds(List<String> skuPackageList) {
        if (CollectionUtils.isEmpty(skuPackageList)) {
            return Collections.emptySet();
        }
        // 迭代获取
        final Set<Long> actIdSet = new HashSet<>();

        skuPackageList.forEach(skuPackage -> {
            List<Long> idList = new ArrayList<>(skuPackageToActivityIdsCacheMap.getOrDefault(skuPackage, goodsCommonActivityIds));
            actIdSet.addAll(idList);
        });
        return actIdSet;
    }

    /**
     * 获取对应sku 或packageId 能参加的活动列表
     *
     * @param channels 列表
     * @return 活动ID集
     */
    public List<Long> getCurrentChannelActIds(List<Integer> channels) {
        if (CollectionUtils.isEmpty(channels)) {
            return Lists.newArrayList();
        }
        // 迭代获取
        final Set<Long> actIdSet = new HashSet<>();
        channels.forEach(channel -> {
            List<Long> idList = new ArrayList<>(channelToActivityIdsCacheMap.getOrDefault(channel, Lists.newArrayList()));
            actIdSet.addAll(idList);
        });
        return new ArrayList<>(actIdSet);
    }

    /**
     * 获取有序活动列表
     *
     * @param actIds 列表
     * @return 活动ID集
     */
    public List<Long> getSortedActIds(List<Long> actIds) {
        if (CollectionUtils.isEmpty(actIds)) {
            return Lists.newArrayList();
        }
        // 迭代获取
        List<Long> actIdList = new ArrayList<>(sortedActivityList);
        actIdList.retainAll(actIds);
        return new ArrayList<>(actIdList);
    }

    /**
     * 全量更新
     * 1. 获取活动数据 : 整车活动 、 车商城活动 、 整车售后活动
     * 2. 将活动信息转换成 promotion config
     * 3. 更新3类索引 活动索引 、 商品索引 、 渠道索引
     * 4. 构建有序活动基线， 保证活动列表有序性
     */
    public void rebuildCacheTask() throws Exception {
        try {
            long startTime = System.currentTimeMillis();
            log.info("CarPromotionInstancePool rebuildCacheTask startTime:{}", startTime);

            // 获取汽车活动数据
            List<ActivityConfig> carActivity = promotionAdminCustomServiceProxy.queryCarActivityListV2(CAR_SOURCE, CAR_PROMOTION_TYPE, CAR_CHANNEL);
            // 获取车商城活动数据
            List<ActivityConfig> carShopActivity = promotionAdminCustomServiceProxy.queryCarActivityListV2(CAR_SHOP_SOURCE, CAR_SHOP_PROMOTION_TYPE, CAR_SHOP_CHANNEL);

            List<AbstractPromotionConfig> carPromotion = convertActivityConfigList(carActivity);
            List<AbstractPromotionConfig> carShopPromotion = convertActivityConfigList(carShopActivity);
            List<AbstractPromotionConfig> promotionConfigList = Lists.newArrayList();
            promotionConfigList.addAll(carPromotion);
            promotionConfigList.addAll(carShopPromotion);

            // 更新活动Tools，返回活动Id列表
            updatePromotionTools(promotionConfigList);
            // 更新sku package 能参加的活动Id列表
            updateGidToActIds(promotionConfigList);
            // 更新channel 能参加的活动列表
            updateChannelToActIds(promotionConfigList);
            // 更新活动有序列表
            updateSortedActIds(sortPromotionConfigList(promotionConfigList));

            long endTime = System.currentTimeMillis();
            lastCacheUpdateTime = startTime;
            log.info("CarPromotionInstancePool rebuildCacheTask endTime:{} ws:{}", endTime, endTime - startTime);
        } catch (Exception e) {
            log.error("CarPromotionInstancePool rebuildCacheTask error.", e);
            throw e;
        }
    }

    /**
     * 增量更新活动
     * 1. 获取活动增量数据 : 整车活动 、 车商城活动 、 整车售后活动
     * 2. 将变更活动信息转换成 promotion config
     * 3. 增量更新3类索引 活动索引 、 商品索引 、 渠道索引
     * 4. 获取所有活动索引中的 promotion config
     * 5. 将promotion config 排序， 并更新活动基线
     */
    public void updateCacheTask(long seqId) throws Exception {
        try {
            lock.lock();

            //当前实例的版本
            long curVersion = latestSeqId.get();
            if (curVersion == 0) {
                curVersion = seqId;
            } else if (seqId < curVersion) {
                // 当前版本大于变更版本则忽略
                log.warn("current version-{} greater than seqId-{}, will be ignored", curVersion, seqId);
                return;
            }

            // car + maintenance
            Map<Integer, ActivityFilterTip> activityFilterTips = new HashMap<>();
            ActivityFilterTip car = new ActivityFilterTip();
            car.setPromotionTypes(CAR_PROMOTION_TYPE);
            car.setChannels(CAR_CHANNEL);
            activityFilterTips.put(SourceAppEnum.CAR.code, car);

            // car shop
            ActivityFilterTip carShop = new ActivityFilterTip();
            carShop.setPromotionTypes(CAR_SHOP_PROMOTION_TYPE);
            carShop.setChannels(CAR_SHOP_CHANNEL);
            activityFilterTips.put(SourceAppEnum.PROMOTION_ADMIN.code, carShop);

            ActivityConfigForPromotionResponse response = promotionAdminCustomServiceProxy.queryActivityBySeqIdV2(curVersion, activityFilterTips);
            if (response == null) {
                return;
            }
            List<AbstractPromotionConfig> updatePromotionConfigList = convertActivityConfigList(response.getActivityConfigs());
            long remoteVersion = response.getSeqId();

            // 受影响的活动id : 包括失效活动以及变更的活动
            Set<Long> effectedActIds = new HashSet<>(response.getInvalidActivityIds());
            updatePromotionConfigList.forEach(config -> {
                effectedActIds.add(config.getPromotionId());
            });

            if (remoteVersion < curVersion) {
                return;
            }
            // 更新活动Tools
            updatePromotionTools(updatePromotionConfigList, effectedActIds);
            // 更新sku package 能参加的活动Id列表
            updateGidToActIds(updatePromotionConfigList, effectedActIds);
            // 更新channel 能参加的活动列表
            updateChannelToActIds(updatePromotionConfigList, effectedActIds);
            // 获取所有活动配置
            List<AbstractPromotionConfig> promotionConfigList = new ArrayList<>();
            activityToolCacheMap.values().forEach(tool -> {
                promotionConfigList.add(tool.getPromotionConfig());
            });
            // 更新有序活动基线
            updateSortedActIds(sortPromotionConfigList(promotionConfigList));
            latestSeqId.getAndSet(remoteVersion);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 促销活动排序
     * 1. 将所有活动按照活动类型分组，根据活动类型将分组排序
     * 2. 组内进行排序，将排序优先级抽象到一个字段，由此字段控制活动的优先级，此字段由活动模型转换时自动生成
     * @param promotionConfigList
     * @return
     */
    private List<AbstractPromotionConfig> sortPromotionConfigList(List<AbstractPromotionConfig> promotionConfigList) {
        List<AbstractPromotionConfig> configList = Lists.newArrayList();
        Map<ActivityTypeEnum, List<AbstractPromotionConfig>> activityGroup = promotionConfigList.stream().collect(Collectors.groupingBy(config -> ActivityTypeEnum.getByValue(config.getPromotionType().getTypeId())));

        List<ActivityTypeEnum> sortedTypeList =
                activityGroup.keySet().stream().sorted(Comparator.comparing(ActivityTypeEnum::getCarWeight)).collect(Collectors.toList());
        for (ActivityTypeEnum anEnum : sortedTypeList) {
            List<AbstractPromotionConfig> configs = activityGroup.get(anEnum);
            if (CollectionUtils.isEmpty(configs)) {
                continue;
            }
            configs.sort(Comparator.comparing(AbstractPromotionConfig::getPriority));
            configList.addAll(configs);
        }

        return configList;
    }

    /**
     * 将活动缓存转化为活动配置列表
     *
     * @param activityInfoList 缓存活动信息
     * @return 配置信息
     */
    private List<AbstractPromotionConfig> convertActivityConfigList(List<ActivityConfig> activityInfoList) {
        if (CollectionUtils.isEmpty(activityInfoList)) {
            return Lists.newArrayList();
        }
        return activityInfoList.stream().map(this::convertItem)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 转化具体活动配置
     *
     * @param activityConfig 活动配置信息
     * @return 优惠信息
     */
    private AbstractPromotionConfig convertItem(ActivityConfig activityConfig) {
        log.info("convert activityConfig to PromotionConfig. cacheInfo:{}", activityConfig);
        if (activityConfig == null) {
            return null;
        }
        Integer promotionType = activityConfig.getPromotionType();
        PromotionToolType toolType = PromotionToolType.fromTypeId(promotionType);
        if (toolType == null) {
            log.error("get PromotionToolType failed. type:{}",promotionType);
            return null;
        }
        // 根据type 获取基本Config对象
        AbstractPromotionConfig promotionConfig;
        try {
            promotionConfig = promotionConfigFactory.getConfig(toolType);
            promotionConfigLoaderAdapter.load(promotionConfig, activityConfig);
        } catch (BizError bizError) {
            log.error("convertItem err. err", bizError);
            return null;
        }
        return promotionConfig;
    }

    /**
     * 更新活动列表 activityToolCacheMap
     *
     * @param promotionConfigList 优惠配置列表
     * @return 有效活动ID
     */
    private void updatePromotionTools(List<AbstractPromotionConfig> promotionConfigList) throws Exception {
        if (CollectionUtils.isEmpty(promotionConfigList)) {
            return;
        }
        // 加载新tools
        List<DSLGeneralPromotion> newTools = loadDSLPromotionFromConfig(promotionConfigList);
        // 更新或新增 并且记录成功的活动
        Map<Long, ActivityTool> newMap = new ConcurrentHashMap<>();
        newTools.forEach(newTool -> {
            newMap.put(newTool.getId(), newTool);
        });
        try {
            lock.lock();
            activityToolCacheMap = newMap;
        }finally {
            lock.unlock();
        }
    }

    /**
     * 更新活动列表 activityToolCacheMap
     *
     * @param promotionConfigList 优惠配置列表
     */
    private void updatePromotionTools(List<AbstractPromotionConfig> promotionConfigList, Set<Long> effectedActIds) throws Exception {
        if (CollectionUtils.isEmpty(promotionConfigList) && CollectionUtils.isEmpty(effectedActIds)) {
            return;
        }

        Map<Long, ActivityTool> activityToolMap = new HashMap<>(activityToolCacheMap);
        for (Long effectedActId : effectedActIds) {
            activityToolMap.remove(effectedActId);
        }
        // 加载新tools
        List<DSLGeneralPromotion> newTools = loadDSLPromotionFromConfig(promotionConfigList);
        // 更新或新增 并且记录成功的活动
        newTools.forEach(newTool -> {
            activityToolMap.put(newTool.getId(), newTool);
        });

        activityToolCacheMap = new ConcurrentHashMap<>(activityToolMap);
    }

    /**
     * 重构商品索引
     * 1. 重构通用可参与活动
     * 2. 重构商品索引
     *
     * @param promotionConfigList 优惠配置列表
     * @return 有效活动SkuPacakge
     */
    private void updateGidToActIds(List<AbstractPromotionConfig> promotionConfigList) {
        if (CollectionUtils.isEmpty(promotionConfigList)) {
            return;
        }

        // 共同可参与活动
        List<Long> commonIdList = new ArrayList<>();
        promotionConfigList.forEach(config -> {
            if (config instanceof MaintenanceDiscountPromotionConfig) {
                commonIdList.add(config.getPromotionId());
            }
        });
        // 生成新skuPackage 列表对应活动列表
        final Map<String, List<Long>> skuPackageActIdMap = new HashMap<>();
        promotionConfigList.forEach(config -> {
            if (!(config instanceof MultiPromotionConfig)) {
                return;
            }
            MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
            Set<String> skuPackages  = Optional.ofNullable(promotionConfig.getIncludeSkuPackages()).orElse(new HashSet<>());
            Long actId = config.getPromotionId();
            skuPackages.forEach(skuPackage -> {
                List<Long> actIdList = skuPackageActIdMap.getOrDefault(skuPackage, new ArrayList<>(commonIdList));
                actIdList.add(actId);
                skuPackageActIdMap.put(skuPackage, actIdList);
            });
        });

        // 更新或新增 并且记录成功的活动
        try{
            lock.lock();
            skuPackageToActivityIdsCacheMap = new ConcurrentHashMap<>(skuPackageActIdMap);
            goodsCommonActivityIds = new ArrayList<>(commonIdList);
        }finally {
            lock.unlock();
        }
    }

    /**
     * 增量更新商品索引
     * 1. 处理共同可用活动  ---  删除无效活动 & 添加更新活动
     * 2. 处理商品缓存  ---  删除无效活动 & 添加有效活动
     *
     * @param promotionConfigList 优惠配置列表
     * @return 有效活动SkuPacakge
     */
    private void updateGidToActIds(List<AbstractPromotionConfig> promotionConfigList, Set<Long> effectedActIds) {
        if (CollectionUtils.isEmpty(promotionConfigList) && CollectionUtils.isEmpty(effectedActIds)) {
            return;
        }

        // 处理共同可参与活动
        Set<Long> commonIdList = new HashSet<>(goodsCommonActivityIds);
        commonIdList.removeAll(effectedActIds);
        promotionConfigList.forEach(config -> {
            if (config instanceof MaintenanceDiscountPromotionConfig) {
                commonIdList.add(config.getPromotionId());
            }
        });


        // 处理活动索引
        final Map<String, List<Long>> skuPackageActIdMap = new HashMap<>(skuPackageToActivityIdsCacheMap);
        skuPackageActIdMap.forEach((sku, acts) -> {
            // 删除变更活动
            acts.removeAll(effectedActIds);
            // 添加并集活动
            commonIdList.forEach(commonId -> {
                if (!acts.contains(commonId)) {
                    acts.add(commonId);
                }
            });
        });
        promotionConfigList.forEach(config -> {
            if (!(config instanceof MultiPromotionConfig)) {
                return;
            }
            MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
            Set<String> skuPackages  = Optional.ofNullable(promotionConfig.getIncludeSkuPackages()).orElse(Collections.emptySet());
            Long actId = config.getPromotionId();
            skuPackages.forEach(skuPackage -> {
                List<Long> actIdList = skuPackageActIdMap.getOrDefault(skuPackage, new ArrayList<>(commonIdList));
                actIdList.add(actId);
                skuPackageActIdMap.put(skuPackage, actIdList);
            });
        });

        // 更新或新增 并且记录成功的活动
        try{
            lock.lock();
            skuPackageToActivityIdsCacheMap = new ConcurrentHashMap<>(skuPackageActIdMap);
            goodsCommonActivityIds = new ArrayList<>(commonIdList);
        }finally {
            lock.unlock();
        }
    }

    /**
     * 更新skuPackage对应活动列表 goodsActivityIdsMap
     *
     * @param promotionConfigList 优惠配置列表
     * @return 有效活动SkuPacakge
     */
    @Deprecated
    private void updateExcludeGidToActIds(List<AbstractPromotionConfig> promotionConfigList) {
        if (CollectionUtils.isEmpty(promotionConfigList)) {
            return;
        }
        // 共同可参与活动
        List<Long> commonIdList = new ArrayList<>();
        promotionConfigList.forEach(config -> {
            if (config instanceof B2tVipDiscountPromotionConfig) {
                commonIdList.add(config.getPromotionId());
            }
        });
        // 生成新skuPackage 列表对应活动列表
        final Map<String, List<Long>> skuPackageActIdMap = new HashMap<>();
        promotionConfigList.forEach(config -> {
            if (!(config instanceof MultiPromotionConfig)) {
                return;
            }
            MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
            Set<String> skuPackages  = Optional.ofNullable(promotionConfig.getIncludeSkuPackages()).orElse(Collections.emptySet());
            Long actId = config.getPromotionId();
            skuPackages.forEach(skuPackage -> {
                List<Long> actIdList = skuPackageActIdMap.getOrDefault(skuPackage, new ArrayList<>(commonIdList));
                actIdList.add(actId);
                skuPackageActIdMap.put(skuPackage, actIdList);
            });
        });

        // 更新或新增 并且记录成功的活动
        try{
            lock.lock();
            Map<String, List<Long>> map = new ConcurrentHashMap<>();
            skuPackageActIdMap.forEach((skuPackage, actIds) -> {
                map.put(skuPackage, actIds);
            });
            skuPackageToActivityIdsCacheMap = map;
            goodsCommonActivityIds = commonIdList;
        }finally {
            lock.unlock();
        }
        return;
    }

    /**
     * 重构渠道索引
     *
     * @param promotionConfigList
     */
    private void updateChannelToActIds(List<AbstractPromotionConfig> promotionConfigList) {
        if (CollectionUtils.isEmpty(promotionConfigList)) {
            return;
        }
        // 生成新skuPackage 列表对应活动列表
        final Map<Integer, List<Long>> channelActIdMap = new HashMap<>();
        promotionConfigList.forEach(config -> {
            if (!(config instanceof MultiPromotionConfig)) {
                return;
            }
            MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
            List<Integer> channels = Optional.ofNullable(promotionConfig.getChannels()).orElse(new ArrayList<>());
            Long actId = config.getPromotionId();
            channels.forEach(channel -> {
                List<Long> actIdSet = channelActIdMap.getOrDefault(channel, new ArrayList<>());
                actIdSet.add(actId);
                channelActIdMap.put(channel, actIdSet);
            });
        });

        // 更新或新增 并且记录成功的活动
        try {
            lock.lock();
            channelToActivityIdsCacheMap = new ConcurrentHashMap<>(channelActIdMap);
        }finally {
            lock.unlock();
        }
    }

    private void updateChannelToActIds(List<AbstractPromotionConfig> promotionConfigList, Set<Long> effectedActIds) {
        if (CollectionUtils.isEmpty(promotionConfigList) && CollectionUtils.isEmpty(effectedActIds)) {
            return;
        }
        // 生成新skuPackage 列表对应活动列表
        final Map<Integer, List<Long>> channelActIdMap = new HashMap<>(channelToActivityIdsCacheMap);
        channelActIdMap.forEach((channel, actIds) -> {
            actIds.removeAll(effectedActIds);
        });
        promotionConfigList.forEach(config -> {
            if (!(config instanceof MultiPromotionConfig)) {
                return;
            }
            MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
            List<Integer> channels = Optional.ofNullable(promotionConfig.getChannels()).orElse(Lists.newArrayList());
            Long actId = config.getPromotionId();
            channels.forEach(channel -> {
                List<Long> actIdSet = channelActIdMap.getOrDefault(channel, new ArrayList<>());
                actIdSet.add(actId);
                channelActIdMap.put(channel, actIdSet);
            });
        });

        // 更新或新增 并且记录成功的活动
        try {
            lock.lock();
            channelToActivityIdsCacheMap = new ConcurrentHashMap<>(channelActIdMap);
        }finally {
            lock.unlock();
        }
    }

    private void updateSortedActIds(List<AbstractPromotionConfig> promotionConfigList) {
        if (CollectionUtils.isEmpty(promotionConfigList)) {
            return;
        }
        // 生成新skuPackage 列表对应活动列表
        final List<Long> sortedActIds = new ArrayList<>();
        promotionConfigList.forEach(config -> {
            if (!(config instanceof MultiPromotionConfig)) {
                return;
            }
            sortedActIds.add(config.getPromotionId());
        });

        // 更新或新增 并且记录成功的活动
        try {
            lock.lock();
            sortedActivityList = new CopyOnWriteArrayList<>(sortedActIds);
        }finally {
            lock.unlock();
        }
    }

    /**
     * 从Config加载活动
     *
     * @param configList 活动配置数据
     * @return 加载成功的优惠工具
     * @throws Exception 异常
     */
    private List<DSLGeneralPromotion> loadDSLPromotionFromConfig(List<AbstractPromotionConfig> configList) throws Exception {
        final ArrayList<DSLGeneralPromotion> tools = new ArrayList<>();
        final HashSet<Long> loadSuccessIdList = new HashSet<>();


        for (AbstractPromotionConfig config : configList) {

            //根据channel初始化不同平台的活动
            Set<BizPlatformEnum> platforms = config.getChannels().stream()
                    .map(BizPlatformEnum::findByChannel)
                    .collect(Collectors.toSet());
            // todo 跨biz会存在tool覆盖问题
            for (BizPlatformEnum bizPlatform : platforms) {
                DSLGeneralPromotion tool = getDSLGeneralPromotionByType(config.getPromotionType(), bizPlatform);
                if (tool == null) {
                    continue;
                }
                final boolean loadSuccess = tool.load(config);
                if (loadSuccess) {
                    tools.add(tool);
                    loadSuccessIdList.add(config.getPromotionId());
                } else {
                    log.error("load tool fail, config:{}", config);
                }
            }
        }

        log.info("new config load ok:{}", loadSuccessIdList);
        return tools;
    }

    /**
     * 根据活动类型获取优惠工具的定义
     * 注：分期免息活动没有优惠工具定义，所以要跳过
     *
     * @param promotionType 优惠工具类型
     * @return 优惠工具
     */
    private DSLGeneralPromotion getDSLGeneralPromotionByType(PromotionToolType promotionType, BizPlatformEnum bizPlatform) {
        try {
            return activityFactory.getByTypeAndBiz(promotionType, bizPlatform);
        } catch (Exception e) {
            log.warn("getDSLGeneralPromotionByType promotionType={},do not support DSLGeneralPromotion!!!", promotionType);
            return null;
        }
    }

	@Override
	public void afterPropertiesSet() throws Exception {
		log.info("begin to rebuild activity pool on server Started.");
		rebuildCacheTask();
	}
}
