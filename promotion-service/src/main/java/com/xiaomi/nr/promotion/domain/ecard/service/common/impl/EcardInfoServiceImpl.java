package com.xiaomi.nr.promotion.domain.ecard.service.common.impl;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.model.Ecard;
import com.xiaomi.nr.promotion.api.dto.model.EcardConsumeItem;
import com.xiaomi.nr.promotion.constant.EcardConstant;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.EcardLogMapper;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.TbBeijingCouponMapper;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.TbEcardLogMapper;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.TbEcardMapper;
import com.xiaomi.nr.promotion.dao.redis.EcardRedisDao;
import com.xiaomi.nr.promotion.dao.redis.UserTagRedisDao;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.TbBeijingCoupon;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.TbEcard;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.TbEcardLog;
import com.xiaomi.nr.promotion.entity.redis.EcardTotalType;
import com.xiaomi.nr.promotion.enums.EcardLogTypeEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.domain.ecard.model.UserEcard;
import com.xiaomi.nr.promotion.resource.provider.EcardProvider;
import com.xiaomi.nr.promotion.rpc.coupon.CouponServiceProxy;
import com.xiaomi.nr.promotion.rpc.ecard.EcardServiceProxy;
import com.xiaomi.nr.promotion.domain.ecard.service.common.EcardInfoService;
import com.xiaomi.nr.promotion.util.DateTimeUtil;
import com.xiaomi.nr.promotion.util.NumberUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.util.concurrent.ListenableFuture;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 礼品卡信息服务
 *
 * <AUTHOR>
 * @date 2021/5/7 2:26 下午
 */
@Slf4j
@Service
public class EcardInfoServiceImpl implements EcardInfoService {

    @Autowired
    private TbEcardMapper tbEcardMapper;
    @Autowired
    private EcardLogMapper ecardLogMapper;
    @Autowired
    private EcardRedisDao ecardRedisDao;
    @Autowired
    private UserTagRedisDao userTagRedisDao;
    @Autowired
    private TbBeijingCouponMapper tbBeijingCouponMapper;
    @Autowired
    private TbEcardLogMapper tbEcardLogMapper;
    @Autowired
    private CouponServiceProxy couponServiceProxy;
    @Resource
    private EcardServiceProxy ecardServiceProxy;

    /**
     * 异步获取北京消费券礼品卡列表信息
     *
     * @param userId 用户ID
     * @return 礼品卡信息列表
     */
    @Async("couponAsyncTaskExecutor")
    @Override
    public ListenableFuture<List<UserEcard>> getBeijingCouponEcardsInfoAsync(Long userId) {
        long startTime = System.currentTimeMillis();
        try {
            // 获取可用的北京消费劵列表
            List<TbBeijingCoupon> beijingCouponList = getValidBeijingCouponList(userId);
            if (CollectionUtils.isEmpty(beijingCouponList)) {
                log.info("user valid beijingCouponList is empty. userId:{}", userId);
                return AsyncResult.forValue(Collections.emptyList());
            }
            // 拿对应消费券礼品卡ID列表
            List<String> cardIdList = beijingCouponList.stream()
                    .map(TbBeijingCoupon::getEcardId).map(String::valueOf)
                    .collect(Collectors.toList());

            // 取具体用户礼品卡数据
            List<UserEcard> userEcardList = getUserEcardList(userId, cardIdList);
            return AsyncResult.forValue(userEcardList);
        } catch (Exception e) {
            log.error("get beijingCoupon ecards info async error. uid:{}, ws:{}", userId, System.currentTimeMillis() - startTime, e);
            return AsyncResult.forExecutionException(e);
        }
    }

    /**
     * 异步查询礼品卡信息   待确认：缓存信息是否提前在该步骤获取
     *
     * @param userId   用户ID
     * @param ecardIds 礼品卡列表
     * @param clientId 应用ID
     * @return 礼品卡列表信息
     */
    @Async("couponAsyncTaskExecutor")
    @Override
    public ListenableFuture<List<UserEcard>> getEcardsInfoAsync(Long userId, List<String> ecardIds, Long clientId) {
        try {
            List<UserEcard> userEcardList = getUserEcardList(userId, ecardIds);
            return AsyncResult.forValue(userEcardList);
        } catch (Exception e) {
            log.error("get ecard info async error. ecardIds:{}, uid:{}, clientId:{}, err", ecardIds, userId, clientId, e);
            return AsyncResult.forExecutionException(e);
        }
    }

    /**
     * 消费礼品卡（锁定资源）
     */
    @Transactional(transactionManager = "promotionUserConfigTransactionManager", rollbackFor = {Exception.class, BizError.class})
    @Override
    public void consumeEcard(EcardProvider.EcardContent ecardContent) throws BizError {
        if (ecardContent == null || CollectionUtils.isEmpty(ecardContent.getEcardList())) {
            log.error("ecardContent is null or ecardList id ecardContent:{}", ecardContent);
            return;
        }

        // 修改礼品卡余额和日志
        List<TbEcardLog> ecardLogList = new ArrayList<>();
        for (Ecard ecard : ecardContent.getEcardList()) {
            Long ecardId = ecard.getECardId();
            Long userId = ecardContent.getUserId();
            BigDecimal balanceOld = NumberUtil.amountConvertF2Y(ecard.getBalanceOld());
            BigDecimal balanceNew = NumberUtil.amountConvertF2Y(ecard.getBalanceNew());
            // 修改礼品卡余额
            int lockedCnt = tbEcardMapper.updateEcardBalance(ecardId, userId, balanceNew, balanceOld);
            if (lockedCnt <= 0) {
                log.error("lock ecard fail. resId:{}", ecardId);
                throw ExceptionHelper.create(ErrCode.ERR_ECARD_UPDATEBALANCE, "锁定礼品卡资源失败");
            }
            // 日志
            List<TbEcardLog> logList = createEcardLogList(ecardContent, ecard, ecard.getBalanceOld());
            ecardLogList.addAll(logList);
        }
        //保存消费记录
        ecardLogMapper.insertBatch(ecardLogList);
    }

    private List<TbEcardLog> createEcardLogList(EcardProvider.EcardContent ecardContent, Ecard ecard, Long banlanceOld) {
        String desc = "[门店拆单]下单使用" + ecardContent.getClientId();
        List<EcardConsumeItem> itemList = Optional.ofNullable(ecard.getConsumeList()).orElse(new ArrayList<>());
        if (CollectionUtils.isEmpty(itemList)) {
            desc = "下单使用" + ecardContent.getClientId();
            EcardConsumeItem ecardConsumeItem = new EcardConsumeItem();
            ecardConsumeItem.setSOrderId(ecardContent.getOrderId());
            ecardConsumeItem.setMoney(-ecard.getIncome());
            itemList.add(ecardConsumeItem);
        }

        List<TbEcardLog> ecardLogList = new ArrayList<>();
        for (EcardConsumeItem ecardConsumeItem : itemList) {
            TbEcardLog ecardLog = initTbEcardLog(ecardConsumeItem, ecard, ecardContent, desc, banlanceOld);
            ecardLogList.add(ecardLog);

            banlanceOld -= ecardConsumeItem.getMoney();
        }
        return ecardLogList;
    }

    private TbEcardLog initTbEcardLog(EcardConsumeItem ecardConsumeItem, Ecard ecard, EcardProvider.EcardContent ecardContent, String desc, Long banlanceOld) {
        TbEcardLog ecardLog = new TbEcardLog();
        ecardLog.setCardId(ecard.getECardId());
        ecardLog.setUserId(ecardContent.getUserId());
        ecardLog.setOrderId(ecardConsumeItem.getSOrderId() + "");
        ecardLog.setLogType(EcardLogTypeEnum.CONSUME.getVal());

        ecardLog.setIncome(NumberUtil.amountConvertF2Y(ecardConsumeItem.getMoney()).negate());
        ecardLog.setOldBalance(NumberUtil.amountConvertF2Y(banlanceOld));
        ecardLog.setNewBalance(NumberUtil.amountConvertF2Y(banlanceOld - ecardConsumeItem.getMoney()));
        ecardLog.setOperatorId(1L);
        ecardLog.setAddTime(Instant.now().getEpochSecond());
        ecardLog.setLastUpdateTime(new Date());
        ecardLog.setDescription(desc);
        ecardLog.setOffline(ecardContent.getOffline());

        String str = ecardLog.getUserId() + ecardLog.getOrderId() + ecardLog.getCardId() + ecardLog.getAddTime() + ecardLog.getOldBalance();
        String md5Code = DigestUtils.md5DigestAsHex(str.getBytes());
        ecardLog.setHashCode(md5Code);
        return ecardLog;
    }

    /**
     * 回滚
     *
     * @param ecardContent 礼品卡资源内容
     * @throws BizError 业务异常
     */
    @Transactional(transactionManager = "promotionUserConfigTransactionManager", rollbackFor = {Exception.class, BizError.class})
    @Override
    public void rollbackEcard(EcardProvider.EcardContent ecardContent, Set<Long> orderIdSet) throws BizError {
        // 处理订单（或者是小单）对应礼品卡
        for (Long sOrderId : orderIdSet) {
            rollbackEcard(ecardContent, sOrderId);
        }

    }

    /**
     * 推mario消息
     *
     * @param userId     用户id
     * @param orderIdSet 订单列表
     */
    @Override
    public void sendMarioMsg(Long userId, Set<Long> orderIdSet) {
        // 退还礼品卡消息推Mario
        List<Long> orderIds = new ArrayList<>(orderIdSet);
        ecardServiceProxy.sendEcardMsgToMario(userId, orderIds);
    }


    /**
     * 处理对应小单礼品卡回滚
     *
     * @param ecardContent 内容
     * @param sOrderId     小单号
     * @throws BizError 业务异常
     */
    public void rollbackEcard(EcardProvider.EcardContent ecardContent, Long sOrderId) throws BizError {
        Long userId = ecardContent.getUserId();
        // 获取礼卡日志
        List<TbEcardLog> ecardLogList = ecardLogMapper.listByOrderId(userId, sOrderId);
        if (CollectionUtils.isEmpty(ecardLogList)) {
            String msg = String.format("the ecard may be refund, and the order may have no coupon, info userId %d sOrderId %d;", userId, sOrderId);
            log.error(msg);
            throw ExceptionHelper.create(ErrCode.ERR_REFUND_REPEAT, msg);
        }

        // 过滤礼品卡消费记录
        Map<Long, TbEcardLog> logMap = filterEcardLogList(ecardLogList, userId, sOrderId);
        List<String> ecardIdList = ecardLogList.stream().map(TbEcardLog::getCardId).map(String::valueOf).distinct().collect(Collectors.toList());

        // 查找对应礼品卡信息
        List<TbEcard> tbEcardList = tbEcardMapper.listEcardsByIds(userId, ecardIdList);
        if (tbEcardList.size() != ecardIdList.size()) {
            log.error("length of tbECardList is {} but should be {}, uid: {} orderId {}", tbEcardList.size(), ecardIdList.size(), userId, sOrderId);
            throw ExceptionHelper.create(ErrCode.ERR_ECARD_DB_CONTENT, "礼品卡数量不一致");
        }

        // 处理礼品卡金额
        for (TbEcard tbEcard : tbEcardList) {
            TbEcardLog ecardLog = logMap.get(tbEcard.getCardId());
            BigDecimal newBalance = tbEcard.getBalance().subtract(ecardLog.getIncome()).setScale(2, BigDecimal.ROUND_HALF_UP);
            tbEcardMapper.updateBalanceById(tbEcard.getCardId(), newBalance);
        }

        // 转化日志对象
        String desc = "关单恢复" + ecardContent.getClientId();
        List<TbEcardLog> logList = tbEcardList.stream().map(tbEcard -> initRefundLog(tbEcard, logMap.get(tbEcard.getCardId()), desc)).collect(Collectors.toList());

        //保存回滚记录
        ecardLogMapper.insertBatch(logList);
    }

    private TbEcardLog initRefundLog(TbEcard tbEcard, TbEcardLog ecardLog, String desc) {
        BigDecimal newBalance = tbEcard.getBalance().subtract(ecardLog.getIncome()).setScale(2, BigDecimal.ROUND_HALF_UP);
        Long addTime = Instant.now().getEpochSecond();
        String str = ecardLog.getUserId() + ecardLog.getOrderId() + ecardLog.getCardId() + addTime + tbEcard.getBalance();
        String md5Code = DigestUtils.md5DigestAsHex(str.getBytes());

        ecardLog.setDescription(desc);
        ecardLog.setLastUpdateTime(new Date());
        ecardLog.setAddTime(addTime);
        ecardLog.setIncome(ecardLog.getIncome().negate());
        ecardLog.setOldBalance(tbEcard.getBalance());
        ecardLog.setNewBalance(newBalance);
        ecardLog.setHashCode(md5Code);
        return ecardLog;
    }

    /**
     * 过滤礼品卡消费记录
     *
     * @param ecardLogList 礼品卡日志
     * @param userId       用户ID
     * @param sOrderId     小单号
     * @return Map, key: 礼品卡ID val: Log
     * @throws BizError 业务异常
     */
    private Map<Long, TbEcardLog> filterEcardLogList(List<TbEcardLog> ecardLogList, Long userId, Long sOrderId) throws BizError {
        BigDecimal totalIncome = new BigDecimal(0);

        // key：ecardId
        Map<Long, TbEcardLog> map = new HashMap<>();
        for (TbEcardLog tbEcardLog : ecardLogList) {
            totalIncome = totalIncome.add(tbEcardLog.getIncome());
            Long ecardId = tbEcardLog.getCardId();
            if (!map.containsKey(ecardId)) {
                map.put(tbEcardLog.getCardId(), tbEcardLog);
                continue;
            }

            // 礼品卡关单记录
            BigDecimal logIncome = tbEcardLog.getIncome().setScale(2, BigDecimal.ROUND_HALF_UP);
            // 礼卡扣减记录
            BigDecimal negateLogIncome = map.get(ecardId).getIncome().negate().setScale(2, BigDecimal.ROUND_HALF_UP);
            if (!Objects.equals(logIncome, negateLogIncome)) {
                log.error("the money of log is wrong , one is {}, other is {}, info userId {} orderId {}", logIncome, negateLogIncome, userId, sOrderId);
                throw ExceptionHelper.create(ErrCode.ERR_ECARD_DB_CONTENT, "礼品卡金额不一致");
            }
        }
        // 检查总价格
        if (totalIncome.compareTo(new BigDecimal(0)) > 0) {
            log.error("the totalIncome is {}, > 0 ,wrong, userId {} orderId {}", totalIncome, userId, sOrderId);
            throw ExceptionHelper.create(ErrCode.ERR_ECARD_DB_CONTENT, "数据库内容异常");
        }
        if (totalIncome.compareTo(new BigDecimal(0)) == 0) {
            log.error("the totalIncome is {}, > 0 ,wrong, userId {} orderId {}", totalIncome, userId, sOrderId);
            throw ExceptionHelper.create(ErrCode.ERR_ECARD_DB_CONTENT, "订单已关闭");
        }
        return map;
    }

    /**
     * 获取用户对应礼品卡信息
     *
     * @param userId   用户ID
     * @param ecardIds 礼品卡信息
     * @return 用户礼品卡信息
     * @throws BizError 业务异常
     */
    private List<UserEcard> getUserEcardList(Long userId, List<String> ecardIds) throws BizError {
        // 查询用户礼品卡信息
        List<TbEcard> list = tbEcardMapper.listEcardsByIds(userId, ecardIds);
        if (CollectionUtils.isEmpty(list)) {
            log.error("invalid ecard id:{} for uid:{}", ecardIds, userId);
            throw ExceptionHelper.create(ErrCode.ERR_INVALID_ECARD, "DB里不存在礼品卡ID:" + ecardIds);
        }
        if (list.size() != ecardIds.size()) {
            log.error("uid:{} exists invalid ecard id:{}, DB ecards len:{}", userId, ecardIds, list.size());
            throw ExceptionHelper.create(ErrCode.ERR_INVALID_ECARD, "传入了DB里不存在的礼品卡");
        }
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 取具体配置信息
        List<UserEcard> userEcardList = Lists.newArrayList();
        for (TbEcard tbEcard : list) {
            userEcardList.add(transformUserEcard(tbEcard));
        }
        return userEcardList;
    }

    /**
     * 转化用户礼品卡信息
     *
     * @param tbEcard 用户礼品卡
     * @return 用户礼品卡配置信息
     * @throws BizError 业务异常
     */
    private UserEcard transformUserEcard(TbEcard tbEcard) throws BizError {
        // 取配置信息
        EcardTotalType ecardTotalType = ecardRedisDao.getEcardInfoByTypeId(tbEcard.getTypeId());
        if (ecardTotalType == null || StringUtils.isBlank(ecardTotalType.getBasetype().getEcardType())) {
            log.warn("check: can't get ecard info for type id{} ", tbEcard.getTypeId());
            throw ExceptionHelper.create(ErrCode.ERR_ECARD_TYPE_INFO, "获取礼品卡类型信息失败");
        }
        // 组装用户和卡配置信息
        UserEcard userEcard = new UserEcard();
        userEcard.setTbEcard(tbEcard);
        userEcard.setEcardTotalType(ecardTotalType);
        return userEcard;
    }

    /**
     * 获取北京消费券对应关系
     *
     * @param userId 用户ID
     * @return 北京消费券对应关系
     */
    private List<TbBeijingCoupon> getValidBeijingCouponList(Long userId) {
        // 人群判断
        String result = userTagRedisDao.getBeijingCoupon(userId);
        if (result == null) {
            log.info("user beijingCoupon is empty. userId:{}", userId);
            return Collections.emptyList();
        }

        // 获取券列表, 指定券ID
        List<Integer> typeIdList = Arrays.asList(EcardConstant.TYPE_ID_2022BEIJING_COUPON, EcardConstant.TYPE_ID_BEIJING_COUPON);
        List<TbBeijingCoupon> couponList = tbBeijingCouponMapper.getBeijingCouponIds(userId, typeIdList);

        // 检查用户券列表
        return couponList.stream().filter(this::checkBeijingCoupon).collect(Collectors.toList());
    }

    /**
     * 检查券信息
     *
     * @param coupon 北京消费券
     * @return 是否满足
     */
    private boolean checkBeijingCoupon(TbBeijingCoupon coupon) {
        // 检查时间
        long timeNow = DateTimeUtil.getCurrentTimes(TimeUnit.SECONDS);
        if (timeNow < coupon.getCouponStartTime() || timeNow >= coupon.getCouponEndTime()) {
            log.info("check beijingcoupon: invalid time for ecard {}, uid {}", coupon.getEcardId(), coupon.getUserId());
            return false;
        }
        // 券使用次数
        int count = ecardRedisDao.getBeijingCouponUsedTimes(coupon.getUserId(), coupon.getEcardId());
        if (EcardConstant.USED_COUNT_LIMIT_BEIJING_COUPON <= count) {
            log.info("check beijingcoupon: invalid counts for ecard {}, uid {}, counts {}", coupon.getEcardId(), coupon.getUserId(), count);
            return false;
        }
        return true;
    }
}
