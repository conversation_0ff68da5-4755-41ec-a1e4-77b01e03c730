package com.xiaomi.nr.promotion.bizplatform.impl;

import com.xiaomi.hera.metrics.api.BusinessMetrics;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.PromotionType;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailItem;
import com.xiaomi.nr.promotion.bizplatform.AbstractBaseBizPlatform;
import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.domain.activity.impl.CarActivityDomain;
import com.xiaomi.nr.promotion.domain.coupon.impl.CarCouponDomain;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.OrderTypeEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.nr.promotion.model.OrderPromotionDetailModel;
import com.xiaomi.nr.promotion.model.ProductShare;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.provider.CarOrderPromotionProvider;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class CarBizPlatform extends AbstractBaseBizPlatform {

    @Autowired
    private ResourceProviderFactory resourceProviderFactory;

    @Autowired
    private NacosConfig nacosConfig;

    private BaseDomainList baseDomainList;

    //场景id
    private static final String SCENE_ID = "scene_id";
    private static final String DEFAULT = "";
    //指标id
    private static final String QUOTA_TOTAL_ID = "quota_total_id";
    private static final String QUOTA_ON_SALE_ID = "quota_on_sale_id";
    private static final String QUOTA_NO_ON_SALE_ID = "quota_no_on_sale_id";
    private static final String QUOTA_ULTRA_COUPON = "quota_ultra_coupon";
    private static final String QUOTA_NO_ULTRA_COUPON = "quota_no_ultra_coupon";
    //counter增1
    private static final double COUNTER_ADD = 1;
    private static final double GAUGE_ADD = 1;
    private List<Long> WHITE_LIST;

    @Override
    public BaseDomainList checkBaseDomainList() {
        return baseDomainList;
    }

    @Override
    public BizPlatformEnum getBiz() {
        return BizPlatformEnum.CAR;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        baseDomainList=super.instanceBaseDomainList();
        baseDomainList.addResourceList(CarActivityDomain.class)
                .addResourceList(CarCouponDomain.class);
    }

    @Override
    public void generateResponse(DomainCheckoutContext domainCheckoutContext) throws BizError {
        super.originalGenerateResponse(domainCheckoutContext);
    }

    @Override
    public void addResource(DomainCheckoutContext domainCheckoutContext) throws BizError {
        CheckoutContext context = domainCheckoutContext.getContext();
        CheckoutPromotionRequest request = domainCheckoutContext.getRequest();
        List<ResourceProvider<?>> resourceProviders = initCarOrderPromotionResource(request);
        context.getResourceHandlers().addAll(resourceProviders);
    }

    @Override
    public void postProcessing(CheckoutPromotionRequest request, DomainCheckoutContext domainCheckoutContext) {

        //开关配置
        if (!nacosConfig.isHeraMonitor()) {
            log.info("hera指标监控关闭中");
            return;
        }
        //取指标值配置
        String sceneId = nacosConfig.getHeraQuotaMap().getOrDefault(SCENE_ID, DEFAULT);
        String quotaTotalId = nacosConfig.getHeraQuotaMap().getOrDefault(QUOTA_TOTAL_ID, DEFAULT);
        String quotaOnSaleId = nacosConfig.getHeraQuotaMap().getOrDefault(QUOTA_ON_SALE_ID, DEFAULT);
        String quotaNoOnSaleId = nacosConfig.getHeraQuotaMap().getOrDefault(QUOTA_NO_ON_SALE_ID, DEFAULT);
        String quotaUltraCoupon = nacosConfig.getHeraQuotaMap().getOrDefault(QUOTA_ULTRA_COUPON, DEFAULT);
        String quotaNoUltraCoupon = nacosConfig.getHeraQuotaMap().getOrDefault(QUOTA_NO_ULTRA_COUPON, DEFAULT);
        //所有请求count+1
        BusinessMetrics.counter(sceneId, quotaTotalId, GAUGE_ADD);

        //参与直降count+1
        if (request != null && request.getCartList() != null) {
            boolean hasDirectDrop = request.getCartList().stream()
                    .filter(cartItem -> cartItem != null && cartItem.getReduceItemList() != null)
                    .flatMap(cartItem -> cartItem.getReduceItemList().stream())
                    .anyMatch(reduceDetailItem -> reduceDetailItem != null && Integer.valueOf(PromotionType.ONSALE.getValue()).equals(reduceDetailItem.getPromotionType()));
            if (hasDirectDrop) {
                BusinessMetrics.counter(sceneId, quotaOnSaleId, GAUGE_ADD);
            } else {
                log.warn("未命中直降 orderId={}", request.getOrderId());
                BusinessMetrics.counter(sceneId, quotaNoOnSaleId, GAUGE_ADD);
            }
        }

        WHITE_LIST = nacosConfig.getHeraUltraSsuList();
        //log.info("测试ultra配置白名单:{}, request:{}, context={}", GsonUtil.toJson(WHITE_LIST), GsonUtil.toJson(request), GsonUtil.toJson(domainCheckoutContext));

        //改配后为ultra
        boolean isUltra = request != null && request.getCartList() != null && request.getCartList().stream()
                .anyMatch(cartItem -> cartItem != null && WHITE_LIST.contains(cartItem.getSsuId()));
        //入参有券 即改配前为ultra有券
        boolean hasCouponBefore = request != null && request.getUsedCouponId() != null && request.getUsedCouponId() > 0;
        //最终有券 即改配后有券
        boolean hasCouponAfter = domainCheckoutContext != null && domainCheckoutContext.getContext() != null && domainCheckoutContext.getContext().getCouponList() != null && !domainCheckoutContext.getContext().getCouponList().isEmpty();
        //改配前是ultra有券，改配后是ultra无券，异常数+1
        if (hasCouponBefore && isUltra && !hasCouponAfter) {
            BusinessMetrics.counter(sceneId, quotaUltraCoupon, GAUGE_ADD);
            log.warn("改配前是ultra有券，改配后是ultra无券 orderId={}", request.getOrderId());
        }
        //改配前是ultra有券，改配后非ultra有券，异常数+1
        if (hasCouponBefore && !isUltra && hasCouponAfter) {
            BusinessMetrics.counter(sceneId, quotaNoUltraCoupon, GAUGE_ADD);
            log.warn("改配前是ultra有券，改配后非ultra有券 orderId={}", request.getOrderId());

        }
    }

    private List<ResourceProvider<?>> initCarOrderPromotionResource(CheckoutPromotionRequest request) throws BizError {
        List<ResourceProvider<?>> providerList = new ArrayList<>();
        Long orderId = request.getOrderId();
        Long userId = request.getUserId();

        // 汽车订单优惠
        CarOrderPromotionProvider.OrderPromotionDetailResource carOrderBenefitResource=new CarOrderPromotionProvider.OrderPromotionDetailResource();
        carOrderBenefitResource.setOrderId(orderId);
        carOrderBenefitResource.setOrderScene(BizPlatformEnum.CAR.getValue());
        carOrderBenefitResource.setOrderType(OrderTypeEnum.NORMAL_ORDER.getOrderType());
        carOrderBenefitResource.setUserId(userId);
        List<CarOrderPromotionProvider.OrderPromotionDetailPo> promotionDetailList=new ArrayList<>();
        carOrderBenefitResource.setPromotionDetailList(promotionDetailList);
        // 分摊明细, 以promotionId+promotionType维度聚合
        List<CartItem> cartList = request.getCartList();
        Map<String,OrderPromotionDetailModel> promotionDetailMap=new HashMap<>();
        for (CartItem cartItem:cartList){
            List<ReduceDetailItem> reduceItemList = cartItem.getReduceItemList();
            if (reduceItemList==null|| reduceItemList.isEmpty()){
                continue;
            }
            for (ReduceDetailItem reduceItem:cartItem.getReduceItemList()){
                String key=reduceItem.getPromotionId()+"-"+reduceItem.getPromotionType();
                OrderPromotionDetailModel promotionDetail=promotionDetailMap.getOrDefault(key,new OrderPromotionDetailModel());

                promotionDetail.setPromotionId(reduceItem.getPromotionId());
                promotionDetail.setPromotionType(reduceItem.getPromotionType());
                promotionDetail.setBudgetApplyNo(reduceItem.getBudgetApplyNo());
                promotionDetail.setLineNum(reduceItem.getLineNum());
                long reduce = promotionDetail.getReduce() == null ? 0 : promotionDetail.getReduce();
                if (reduceItem.getReduce()!=null){
                    promotionDetail.setReduce(reduce+reduceItem.getReduce());
                }
                // 产品维度分摊
                List<ProductShare> productShareList = promotionDetail.getProductShareList();
                if (productShareList==null){
                    productShareList=new ArrayList<>();
                }
                ProductShare productShare=new ProductShare();
                productShare.setProductId(cartItem.getSsuId());
                productShare.setCount(cartItem.getCount());
                productShare.setReduce(reduceItem.getReduce());
                productShareList.add(productShare);

                promotionDetail.setProductShareList(productShareList);
                promotionDetailMap.put(key,promotionDetail);
            }
        }
        promotionDetailMap.forEach((k,v)->{
            CarOrderPromotionProvider.OrderPromotionDetailPo po=new CarOrderPromotionProvider.OrderPromotionDetailPo();
            po.setPromotionId(v.getPromotionId());
            po.setPromotionType(v.getPromotionType());
            po.setPromotionDetail(GsonUtil.toJson(v));
            promotionDetailList.add(po);
        });
//        if (promotionDetailList.isEmpty()){
//            return providerList;
//        }
        // 空优惠也记录
        carOrderBenefitResource.setPromotionDetailList(promotionDetailList);
        ResourceObject<CarOrderPromotionProvider.OrderPromotionDetailResource> resourceObject = buildCarOrderBenefitResourceObject(orderId, userId, carOrderBenefitResource);
        CarOrderPromotionProvider benefitProvider = (CarOrderPromotionProvider) resourceProviderFactory.getProvider(ResourceType.CAR_ORDER_BENEFIT);
        benefitProvider.initResource(resourceObject);
        log.debug("car order benefit provider:{}", benefitProvider);
        providerList.add(benefitProvider);
        return providerList;
    }


    private ResourceObject<CarOrderPromotionProvider.OrderPromotionDetailResource> buildCarOrderBenefitResourceObject(Long orderId, Long userId, CarOrderPromotionProvider.OrderPromotionDetailResource orderBenefit) {
        ResourceObject<CarOrderPromotionProvider.OrderPromotionDetailResource> resourceObject = new ResourceObject<>();
        resourceObject.setContent(orderBenefit);
        resourceObject.setPid(0L);
        resourceObject.setPromotionId(orderId);
        resourceObject.setOrderId(orderId);
        resourceObject.setResourceType(ResourceType.CAR_ORDER_BENEFIT);
        resourceObject.setResourceId(String.format("%s_%s", orderId, userId));
        return resourceObject;
    }
}
