package com.xiaomi.nr.promotion.config;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by wangweiyi on 2023/5/18
 */
@Component
@Data
@NacosConfigurationProperties(dataId = NacosConfig.DATA_ID, autoRefreshed = true, ignoreNestedProperties = true, type = ConfigType.JSON)
public class NacosConfig {
    public static final String DATA_ID = "promotion_service_common";

    /**
     * 活动池灰度开关
     */
    private boolean poolGreySwitch = false;

    /**
     * 活动池灰度活动类型
     */
    private List<Integer> poolGreyActTypes = new ArrayList<>();


    /**
     * 活动池灰度活动id
     */
    private List<Long> poolGreyActIds = new ArrayList<>();

    /**
     * 库存计数类写开关（false允许读，true不允许读）
     */
    private boolean writeSwitch = true;

    /**
     * 集群切换开关（false老集群，true新集群）
     */
    private boolean clusterSwitch = false;

    /**
     * 汽车优惠券降级开关
     */
    private boolean carCouponDegrade = false;

    /**
     * 车商城优惠券降级开关
     */
    private boolean carShopCouponDegrade = false;

    /**
     * 工时去重黑名单
     */
    private List<String> vidBlackList = new ArrayList<>();

    /**
     * 一期以旧换新开关
     */
    private boolean queryOldSubsity = false;

    /**
     * 赠品库存查询使用缓存开关
     */
    private boolean giftStockCacheSwitch = false;

    /**
     * 赠品库存请求降级开关
     */
    private boolean giftStockReqFallback = false;

    /**
     * 汽车加购降级开关
     */
    private boolean cartCouponDegradeForCar = false;

    /**
     * 促销缓存diff开关
     */
    private boolean promotionCacheDiffSwitch = false;
    /**
     * 调用iauth开关
     */
    private boolean iauthSwitch = false;

    /**
     * 汽车补偿任务开关
     */
    private boolean carRemedialSwitch = false;

}