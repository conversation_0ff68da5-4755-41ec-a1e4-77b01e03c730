package com.xiaomi.nr.promotion.config;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by wangweiyi on 2023/5/18
 */
@Component
@Data
@NacosConfigurationProperties(dataId = NacosConfig.DATA_ID, autoRefreshed = true, ignoreNestedProperties = true, type = ConfigType.JSON)
public class NacosConfig {
    public static final String DATA_ID = "promotion_service_common";

    /**
     * 活动池灰度开关
     */
    private boolean poolGreySwitch = false;

    /**
     * 活动池灰度活动类型
     */
    private List<Integer> poolGreyActTypes = new ArrayList<>();


    /**
     * 活动池灰度活动id
     */
    private List<Long> poolGreyActIds = new ArrayList<>();

    /**
     * 库存计数类写开关（false允许读，true不允许读）
     */
    private boolean writeSwitch = true;

    /**
     * 集群切换开关（false老集群，true新集群）
     */
    private boolean clusterSwitch = false;

    /**
     * 汽车优惠券降级开关
     */
    private boolean carCouponDegrade = false;

    /**
     * 一期以旧换新开关
     */
    private boolean queryOldSubsity = false;

    /**
     * 活动白名单，在白名单中的活动，不进行区域校验
     */
    private List<Long> activityAreaWhiteList = new ArrayList<>();

    /**
     * 国补预发活动黑名单, 防止预发活动影响线上
     */
    private List<Long> preActivityBlackList = new ArrayList<>();
    
    /**
     * 【门店】国补活动品类编码配置化
     */
    private Map<String,String> subsidyCateCodeMap;

    /**
     * 极简国补站内领-活动ID
     */
    private List<Long> jijianCouponsSiteActIdList = new ArrayList<>();

    /**
     * 极简国补站内领-支持的省
     */
    private List<Integer> jijianCouponsSiteProvinceList = new ArrayList<>();

    /**
     * 活动配置缓存开关
     */
    private boolean actConfigCacheEnable = true;

    /**
     * 消息处理黑名单
     */
    private List<Long> miShopMessageHandleBlackList = new ArrayList<>();

    /**
     * 国补活动基数增加6000元限制:限制品类3C(B01,B02,B03)
     */
    private List<String> subsidyLimitCateCode = new ArrayList<>();

    /**
     * 国补活动基数增加6000元限制:限制价格(600000，单位:分)
     */
    private Long subsidyLimitMoney = 600000L;
}