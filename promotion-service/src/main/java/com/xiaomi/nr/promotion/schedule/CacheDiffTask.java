package com.xiaomi.nr.promotion.schedule;

import com.xiaomi.nr.md.promotion.admin.api.constant.ActivityStatusEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.SimpleActivityConfig;
import com.xiaomi.nr.promotion.activity.pool.CarPromotionInstancePool;
import com.xiaomi.nr.promotion.activity.pool.PromotionInstancePool;
import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.constant.PromotionCacheConstant;
import static com.xiaomi.nr.promotion.constant.PromotionCacheConstant.Car;
import static com.xiaomi.nr.promotion.constant.PromotionCacheConstant.Gov;
import static com.xiaomi.nr.promotion.constant.PromotionCacheConstant.MiShop;
import com.xiaomi.nr.promotion.rpc.feishu.FeiShuMessageService;
import com.xiaomi.nr.promotion.rpc.mdpromotionadmin.PromotionAdminCustomServiceProxy;
import com.xiaomi.nr.promotion.service.V3ActivityOnlineService;
import com.xiaomi.nr.promotion.util.DateTimeUtil;
import com.xiaomi.nr.promotion.util.RetryUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.SetUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/4/8 19:24
 */
@Component
@Slf4j
public class CacheDiffTask {

    @Autowired
    private PromotionAdminCustomServiceProxy promotionAdminCustomServiceProxy;

    @Autowired
    private V3ActivityOnlineService v3ActivityOnlineService;

    @Autowired(required = false)
    private PromotionInstancePool promotionInstancePool;

    @Autowired(required = false)
    private CarPromotionInstancePool carPromotionInstancePool;

    @Autowired
    private FeiShuMessageService feiShuMessageService;

    @Value("#{'${biz.area.id.list}'.split(',')}")
    private List<String> bizAreaIdList;

    @Autowired
    private NacosConfig nacosConfig;

    @Scheduled(fixedRate = 1000 * 5 * 60, initialDelay = 1000 * 5 * 60)
    public void cacheDiff() {
        if (!nacosConfig.isPromotionCacheDiffSwitch()) {
            log.info("promotion cache diff filter. switch is close");
            return;
        }
        if (CollectionUtils.isEmpty(bizAreaIdList)) {
            log.warn("promotion cache diff filter. no area id list is empty");
            return;
        }
        long start = System.currentTimeMillis();
        log.info("promotion cache diff start");
        if (bizAreaIdList.contains(PromotionCacheConstant.Area.CAR)) {
            carCacheDiff();
        }
//        if (bizAreaIdList.contains(PromotionCacheConstant.Area.MISHOP_BIZ_AREA)) {
//            miShopCacheDiff();
//        }
        log.info("promotion cache diff end, cost time:{} ms", System.currentTimeMillis() - start);
    }

    public void miShopCacheDiff() {
        Set<Long> cacheActivityIds = promotionInstancePool.getActivityToolCacheMap().keySet();
        long now = System.currentTimeMillis();
        Set<Long> effectiveActivityIds = new HashSet<>();
        effectiveActivityIds.addAll(queryMiShopActivityIds(now));
        effectiveActivityIds.addAll(querySubActivityIds(now));
        // effectiveActivityIds.addAll(queryV3ActivityIds(now));
        // 车活动对比并且发送飞书消息
        diffAndSendMessage(cacheActivityIds, effectiveActivityIds, promotionInstancePool.getLastCacheUpdateTime());
    }

    public void carCacheDiff() {
        Set<Long> cacheActivityIds = carPromotionInstancePool.getActivityToolCacheMap().keySet();
        long now = System.currentTimeMillis();
        Set<Long> effectiveActivityIds = new HashSet<>();
        effectiveActivityIds.addAll(queryCarActivityIds(now));
        effectiveActivityIds.addAll(queryCarShopActivityIds(now));
        // 车活动对比并且发送飞书消息
        diffAndSendMessage(cacheActivityIds, effectiveActivityIds, carPromotionInstancePool.getLastCacheUpdateTime());
    }


    public void diffAndSendMessage(Set<Long> cacheActivityIds,
                                   Set<Long> effectiveActivityIds,
                                   long lastCacheUpdateTime) {
        Set<Long> diffIds = SetUtils.disjunction(cacheActivityIds, effectiveActivityIds).toSet();
        List<List<Long>> partition = ListUtils.partition(new ArrayList<>(diffIds), 30);
        List<SimpleActivityConfig> activityConfigs = new ArrayList<>();
        for (List<Long> activityIds : partition) {
            activityConfigs.addAll(promotionAdminCustomServiceProxy.querySimpleActivitiesByIds(activityIds));
        }
        List<Long> moreIds = new ArrayList<>();
        List<Long> lessIds = new ArrayList<>();
        long compareTime = lastCacheUpdateTime / 1000;
        for (SimpleActivityConfig activityConfig : activityConfigs) {
            if (activityConfig.getAcStatus() == ActivityStatusEnum.ONLINE.code) {
                if (activityConfig.getBeginTime() < compareTime) {
                    if (activityConfig.getUpdateTime().getTime() < lastCacheUpdateTime) {
                        moreIds.add(activityConfig.getId());
                    }
                }
            } else if (activityConfig.getAcStatus() == ActivityStatusEnum.OFFLINE.code) {
                if (activityConfig.getEndTime() < compareTime) {
                    if (activityConfig.getUpdateTime().getTime() < lastCacheUpdateTime) {
                        lessIds.add(activityConfig.getId());
                    }
                }
            }
        }
        feiShuMessageService.sendCacheDiffMessage(moreIds, lessIds);
    }


    private List<Long> queryV3ActivityIds(Long nowTime) {

        return v3ActivityOnlineService.getActivityIds(nowTime);
    }

    private List<Long> queryCarActivityIds(Long nowTime) {
        return queryActivityIdsWithRetry(nowTime,
                Car.CAR_SOURCE,
                Car.CAR_PROMOTION_TYPE,
                Car.CAR_CHANNEL);
    }

    private List<Long> queryCarShopActivityIds(Long nowTime) {
        return queryActivityIdsWithRetry(nowTime,
                Car.CAR_SHOP_SOURCE,
                Car.CAR_SHOP_PROMOTION_TYPE,
                Car.CAR_SHOP_CHANNEL);
    }

    private List<Long> queryMiShopActivityIds(Long nowTime) {
        return queryActivityIdsWithRetry(nowTime,
                MiShop.MI_SHOP_SOURCE,
                MiShop.MI_SHOP_PROMOTION_TYPE,
                MiShop.MI_SHOP_CHANNEL);
    }

    private List<Long> querySubActivityIds(Long nowTime) {
        return queryActivityIdsWithRetry(nowTime,
                Gov.GOV_SOURCE,
                Gov.GOV_PROMOTION_TYPE,
                Gov.GOV_CHANNEL);
    }


    private List<Long> queryActivityIdsWithRetry(Long nowTime,
                                                List<Integer> sources,
                                                List<Integer> promotionTypes,
                                                List<Integer> channels) {
        List<Long> ids = null;
        try {
            ids = RetryUtil.retryableCall(
                    () -> promotionAdminCustomServiceProxy.queryEffectiveActivityIds(nowTime, sources, promotionTypes,
                            channels), 1);
        } catch (Exception e) {
            throw new RuntimeException("get activity ids error", e);
        }
        return ids;
    }

}
