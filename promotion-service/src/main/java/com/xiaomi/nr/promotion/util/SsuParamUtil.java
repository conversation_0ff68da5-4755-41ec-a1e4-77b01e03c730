package com.xiaomi.nr.promotion.util;

import com.xiaomi.nr.promotion.enums.PromotionSsuTypeEnum;

public class SsuParamUtil {

    private SsuParamUtil() {
        throw new IllegalStateException("SsuParamUtil class");
    }

    /**
     * 判断新套装
     *
     * @param ssuType ssuType
     * @return boolean
     */
    public static boolean isNewPackage(Integer ssuType) {
        return ssuType != null && ssuType.equals(PromotionSsuTypeEnum.PACKAGE.getCode());
    }


}
