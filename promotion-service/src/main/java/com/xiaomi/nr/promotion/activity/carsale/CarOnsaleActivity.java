package com.xiaomi.nr.promotion.activity.carsale;

import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.activity.AbstractOnsaleActivity;
import com.xiaomi.nr.promotion.api.dto.enums.SubmitTypeEnum;
import com.xiaomi.nr.promotion.api.dto.model.GoodsDto;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfoDTO;
import com.xiaomi.nr.promotion.api.dto.model.PromotionPriceDTO;
import com.xiaomi.nr.promotion.api.dto.model.promotionprice.OnsaleProductRuleDto;
import com.xiaomi.nr.promotion.componet.action.carsale.CarOnSaleAction;
import com.xiaomi.nr.promotion.componet.condition.carsale.CarOnsaleCondition;
import com.xiaomi.nr.promotion.constant.PromotionTextConstant;
import com.xiaomi.nr.promotion.engine.PromotionPriceNormProvider;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.OnsalePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.model.promotionconfig.common.BenefitInfo;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.xiaomi.nr.promotion.constant.ApplicationConstant.CONTEXT_PARAM_ORDER_TIME;
import static com.xiaomi.nr.promotion.constant.ApplicationConstant.CONTEXT_PARAM_SUBMIT_TYPE;

/**
 * Created by wangweiyi on 2023/9/14
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class CarOnsaleActivity extends AbstractOnsaleActivity implements PromotionPriceNormProvider {

    protected Map<String, ActPriceInfo> onsaleInfoMap;

    protected Integer tradeType;

    protected BenefitInfo benefitInfo;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine
                .condition(CarOnsaleCondition.class)
                .action(CarOnSaleAction.class);
    }

    /**
     * 检查条件是否满足
     *
     * @param contextParams 包含上下文参数的Map，其中应包含订单时间
     * @return 如果条件满足返回true，否则返回false
     */
    @Override
    public boolean checkCondition(Map<String, String> contextParams) {
        String orderTime = contextParams.get(CONTEXT_PARAM_ORDER_TIME);
        String submitType = contextParams.get(CONTEXT_PARAM_SUBMIT_TYPE);
        boolean isOrderTimeEmpty = StringUtils.isEmpty(orderTime);
        boolean isTypeMatch = true;
        if (Objects.nonNull(submitType)) {
            isTypeMatch = tradeType != null && submitType.equals(tradeType.toString());
        }
        if (SubmitTypeEnum.REPLACE.getCode() == tradeType) {
            // 改配活动，传现车不处理；传改配且下单时间合规，需要处理；传常态且下单时间合规，需要处理
            if (isOrderTimeEmpty) {
                return false;
            }
            if (String.valueOf(SubmitTypeEnum.PRESENT.getCode()).equals(submitType)) {
                return false;
            }
            Long matchTime = Long.valueOf(orderTime);
            return benefitInfo != null && benefitInfo.getStartTime() <= matchTime && benefitInfo.getEndTime() >= matchTime;
        } else if (SubmitTypeEnum.NORMAL.getCode() == tradeType) {
            // 常态活动 传现车不处理；如果传常态且下单时间为空时，需要处理；如果传改配且下单为空，需要处理
            if (String.valueOf(SubmitTypeEnum.PRESENT.getCode()).equals(submitType)) {
                return false;
            }
            return isOrderTimeEmpty;
        } else if (SubmitTypeEnum.PRESENT.getCode() == tradeType) {
            // 现车活动，必须传现车才处理
            return isTypeMatch;
        }
        return true;
    }


    @Override
    public Map<Long, PromotionPriceDTO> getGoodsPromotionPrice(List<GoodsDto> goodsList, Map<String, String> contextParams) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(goodsList)) {
            return Collections.emptyMap();
        }

        Map<Long, PromotionPriceDTO> priceDtoMap = Maps.newHashMap();
        for (GoodsDto goodsDto : goodsList) {
            ActPriceInfo priceInfo = onsaleInfoMap.get(String.valueOf(goodsDto.getSsuId()));
            if (priceInfo == null) {
                continue;
            }
            long onSalePrice = priceInfo.getPrice();
            Long finalPrice = goodsDto.getPrice();
            if (finalPrice != null && onSalePrice >= finalPrice) {
                continue;
            }
            finalPrice = onSalePrice;
            // 包装结果
            OnsaleProductRuleDto onSaleRule = new OnsaleProductRuleDto();
            onSaleRule.setPromotionPrice(onSalePrice);
            PromotionPriceDTO priceDTO = buildPromotionPrice(GsonUtil.toJson(onSaleRule), finalPrice);
            // 处理直降标签，限时直降/限时免费
            setOnSalePromotionText(priceDTO, onSalePrice);
            priceDtoMap.put(goodsDto.getSsuId(), priceDTO);
            // 改价
            goodsDto.setPrice(finalPrice);
        }
        return priceDtoMap;
    }

    private void setOnSalePromotionText(PromotionPriceDTO priceDTO, long onSalePrice) {
        if (priceDTO == null || priceDTO.getPromotionInfos() == null) {
            return;
        }
        for (PromotionInfoDTO infoDTO : priceDTO.getPromotionInfos()) {
            if (onSalePrice == 0) {
                infoDTO.setPromotionText(PromotionTextConstant.ON_SALE_FREE);
            } else {
                infoDTO.setPromotionText(PromotionTextConstant.ON_SALE_REDUCE);
            }
        }
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof OnsalePromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        OnsalePromotionConfig promotionConfig = (OnsalePromotionConfig) config;
        this.actMutexLimit = promotionConfig.isActMutexLimit();
        this.actMutexes = promotionConfig.getActMutexes();
        this.onsaleInfoMap = promotionConfig.getOnsaleInfoMap();
        this.tradeType = promotionConfig.getTradeType();
        this.benefitInfo = promotionConfig.getBenefitInfo();
        return true;
    }

    @Override
    public ActivityDetail getActivityDetail() {
        return null;
    }


    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        return null;
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.ONSALE;
    }


    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR;
    }


}
