package com.xiaomi.nr.promotion.domain.activity.impl;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.domain.activity.AbstractActivityDomain;
import com.xiaomi.nr.promotion.domain.activity.facade.MaintenanceActivityFacade;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class CarMaintenanceActivityDomain extends AbstractActivityDomain {

    @Autowired
    private MaintenanceActivityFacade facade;

    @Override
    public void checkout(DomainCheckoutContext domainCheckoutContext) throws Exception {
        List<ActivityTool> activityTools = facade.activitySearch(domainCheckoutContext);
        executeActivity(domainCheckoutContext, activityTools);
    }

    /**
     * 售后活动执行逻辑
     * @param domainCheckoutContext 促销领域上下文
     * @throws BizError 业务异常
     */
    @Override
    public void executeActivity(DomainCheckoutContext domainCheckoutContext,List<ActivityTool> allActivityTools) throws BizError {
        CheckoutPromotionRequest request = domainCheckoutContext.getRequest();
        CheckoutContext checkoutContext = domainCheckoutContext.getContext();
        String vid = request.getVid();
        long startTime = System.currentTimeMillis();

        if (CollectionUtils.isEmpty(allActivityTools)) {
            CartHelper.delGiftBargain(request.getCartList());
            log.info("actList is empty. vid:{}, orgCode:{} clientId:{}", vid, request.getOrgCode(), request.getClientId());
            return;
        }

        // 迭代处理每个活动Tool
        for (ActivityTool activityTool : allActivityTools) {
            activityTool.doCheckout(request, checkoutContext);
        }

        long endTime = System.currentTimeMillis();
        log.info("maintenance act checkout end. vid:{} endTime:{} ws:{}", vid, endTime, (endTime - startTime));
    }
}
