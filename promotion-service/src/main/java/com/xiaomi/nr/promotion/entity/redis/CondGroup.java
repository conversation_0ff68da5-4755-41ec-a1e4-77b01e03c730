package com.xiaomi.nr.promotion.entity.redis;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 能参与的用户组
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Data
public class CondGroup implements Serializable {
    private static final long serialVersionUID = -388978079283137580L;

    /**
     * 1 所有人群参与
     */
    private Integer action = 0;

    /**
     * 如果是指定人群的，这里会加上人群tag slice类型
     */
    private List<String> data;
}
