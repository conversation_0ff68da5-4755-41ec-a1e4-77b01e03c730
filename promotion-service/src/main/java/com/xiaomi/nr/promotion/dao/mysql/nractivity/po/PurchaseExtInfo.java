package com.xiaomi.nr.promotion.dao.mysql.nractivity.po;

import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import com.xiaomi.nr.promotion.api.dto.model.carshop.Region;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： Pancras
 * @date： 2024/9/19
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Data
public class PurchaseExtInfo implements Serializable {
    
    private static final long serialVersionUID = -1076135866894738231L;

    private String cateLevel;

    private ReduceInfo reduce;
    
    private List<String> scope;

    private List<Region> regionList;

    private Integer paymentAccess;

    private Integer invoiceRule;

    private Integer reportCity;

    private String tag;

    private String registrationEntity;

    private Integer subsidyMode;

    private String activityUrl;

    private String actPayTag = "";

    private Integer invoiceCompanyId = -1;

    private Integer fetchType;

    private String usageGuide;

    private String usageGuideImgUrl;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReduceInfo implements Serializable {
        
        private static final long serialVersionUID = 5043709613090928656L;
        
        private Long reduce;
        
        private Long maxReduce;
    }
}
