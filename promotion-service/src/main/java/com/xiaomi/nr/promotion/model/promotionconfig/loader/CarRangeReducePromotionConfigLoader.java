package com.xiaomi.nr.promotion.model.promotionconfig.loader;

import com.xiaomi.nr.md.promotion.admin.api.constant.ProductIdTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ProductPolicy;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.RangeReduceRule;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.specification.BrBudgetSpecification;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.CarRangeReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 范围立减配置数据加载
 *
 * <AUTHOR>
 * @date 2023/11/26 19:46
 **/
@Slf4j
@Component
public class CarRangeReducePromotionConfigLoader implements PromotionConfigLoader {

    @Override
    public boolean support(AbstractPromotionConfig promotionConfig) {
        if (promotionConfig instanceof CarRangeReducePromotionConfig) {
            return true;
        }
        return false;
    }

    /**
     * 加载范围立减活动信息
     *
     * @param promotionConfig 配置对象
     * @param activityConfig  活动缓存
     * @throws BizError
     */
    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityConfig activityConfig) throws BizError {
        if (activityConfig == null || promotionConfig == null) {
            log.error("rangeReduce activityInfo is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "activityInfo is null");
        }
        CarRangeReducePromotionConfig config = (CarRangeReducePromotionConfig) promotionConfig;

        // 范围立减商品信息、规则信息
        Map<String, ActPriceInfo> rangeReduceInfoMap = new HashMap<>();
        for (ProductPolicy productPolicy : activityConfig.getProductPolicyList()) {
            if (productPolicy.getProductIdType() != ProductIdTypeEnum.SSU.code) {
                continue;
            }
            Long productId = productPolicy.getProductId();
            ActPriceInfo actPriceInfo = new ActPriceInfo();
            actPriceInfo.setPrice(productPolicy.getPromotionPrice());
            actPriceInfo.setSsuId(productId);
            // 财务信息
            String rule = activityConfig.getRule();
            RangeReduceRule reduceRule = GsonUtil.fromJson(rule, RangeReduceRule.class);
            if (reduceRule!=null){
                BrBudgetSpecification specification = reduceRule.getBudgetSpecification();
                if (specification!=null){
                    actPriceInfo.setBudgetApplyNo(specification.getBudgetApplyNo());
                    actPriceInfo.setLineNum(specification.getLineNum());
                }
            }
            rangeReduceInfoMap.put(String.valueOf(productId), actPriceInfo);
        }
        config.setRangeReduceRule(activityConfig.getRule());
        config.setRangeReduceInfoMap(rangeReduceInfoMap);
    }

}
