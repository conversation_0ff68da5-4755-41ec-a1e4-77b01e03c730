package com.xiaomi.nr.promotion.domain.coupon.model;

import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import lombok.Data;

import java.util.List;

/**
 * Created by wangweiyi on 2022/3/15
 */
@Data
public class CouponCheckoutResult {

    /**
     * 券id
     */
    private long couponId;

    /**
     * 券id
     */
    private String couponCode;

    /**
     * 是否有效
     */
    private boolean allow;

    /**
     * 减免金额
     */
    private long reduceAmount;

    /**
     * 券面值
     */
    private Long couponReduce;

    /**
     * 可使用券的商品金额
     */
    private Long validGoodsPrice = 0L;


    /**
     * 可使用券的商品
     */
    private List<GoodsIndex> validGoods;

    /**
     * 不可使用原因code
     */
    private long unusableCode;

    /**
     * 不可使用原因
     */
    private String unusableReason;

    /**
     * 不可使用原因，对应keydata_unusable
     */
    private String keyDataUnusable;

    /**
     * 券抵扣信息
     */
    private DeductedInfo deductedInfo;

    /**
     * BR单据
     */
    private String budgetApplyNo;

    /**
     * BR行号
     */
    private Long lineNum;
    
    /**
     * 券抵扣信息 ---> 可抵扣多个商品
     */
    private List<DeductedInfo> deductedInfoList;

}
