package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 用户线上活动参与记录
 *
 * <AUTHOR>
 * @date 2021/4/20
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class OnlineActUserRecordTotalProvider implements ResourceProvider<OnlineActUserRecordTotalProvider.ActUserRecord> {
    /**
     * 线上用户参与活动记录
     */
    private ResourceObject<ActUserRecord> resourceObject;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Override
    public ResourceObject<ActUserRecord> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<ActUserRecord> object) {
        this.resourceObject = object;
    }

    /**
     * 扣减库存
     */
    @Override
    public void lock() throws BizError {
        log.info("lock act user record total resource. {}", resourceObject);
        activityRedisDao.setUserActTotalRecord(resourceObject.getContent().getActId(),
                resourceObject.getContent().getUid(), resourceObject.getOrderId(), resourceObject.getContent().getExpireTime());
        log.info("lock act user record total resource. {}", resourceObject);
    }

    @Override
    public void consume() {
        log.info("consume act user record total resource. {}", resourceObject);
    }

    @Override
    public void rollback() throws BizError {
        log.info("rollback act user record total resource. {}", resourceObject);
        activityRedisDao.delUserActTotalRecord(resourceObject.getContent().getActId(),
                resourceObject.getContent().getUid());
        log.info("rollback act user record total resource. {}", resourceObject);
    }

    @Override
    public String conflictText() {
        return "插入记录失败";
    }

    /**
     * 活动记录
     */
    @Data
    public static class ActUserRecord {
        /**
         * 用户ID
         */
        private Long uid;
        /**
         * 活动ID
         */
        private Long actId;
        /**
         * 过期时间 (秒）
         */
        private Long expireTime;
    }
}
