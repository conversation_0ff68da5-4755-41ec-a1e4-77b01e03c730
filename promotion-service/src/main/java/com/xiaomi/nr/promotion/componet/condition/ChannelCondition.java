package com.xiaomi.nr.promotion.componet.condition;

import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.entity.redis.OrgInfo;
import com.xiaomi.nr.promotion.enums.ChannelEnum;
import com.xiaomi.nr.promotion.enums.OrgTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MultiPromotionConfig;
import com.xiaomi.youpin.infra.rpc.errors.BizError;

import lombok.extern.slf4j.Slf4j;

/**
 * 渠道范围条件判断（从promoition-admin创建的活动）
 *
 * <AUTHOR>
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class ChannelCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;

    /**
     * 活动版本，0-b.d创建，1-promotion-admin创建
     */
    private Integer version;

    /**
     * 渠道范围列表
     */
    private List<Integer> channel;
    /**
     * 指定门店或区域ID
     */
    private List<String> selectOrgCodes;
    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        // b.d创建的活动不走此逻辑
        if (PromotionConstant.VERSION_BD.equals(version)) {
            return true;
        }
        if (Objects.equals(com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum.B2T_C_CUSTOMER.getValue(), request.getChannel())) {
            return true;
        }
        if (Objects.equals(com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum.B2T_GOV_BIG_CUSTOMER.getValue(), request.getChannel())) {
            return true;
        }

        if (Objects.equals(com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum.B2T_MIJIA_BIG_CUSTOMER.getValue(), request.getChannel())) {
            return true;
        }

        if (Objects.equals(com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum.CAR_VEHICLE.getValue(), request.getChannel())) {
            return true;
        }
        if (com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum.isThirdChannel(request.getChannel())) {
            return channel.contains(request.getChannel());
        }

        // 小米商城渠道校验
        if (StringUtils.isEmpty(request.getOrgCode())) {
           return channel.contains(ChannelEnum.MI_SHOP.getId());
        } else {
            // 获取门店信息
            OrgInfo orgInfo = context.getOrgInfo();
            // 门店id
            String orgCode = request.getOrgCode();
            // 门店类型
            Integer orgType = orgInfo.getOrgType();
            OrgTypeEnum orgTypeEnum = OrgTypeEnum.getOrgType(orgType);
            if (orgTypeEnum == null) {
                return false;
            }
            switch (orgTypeEnum) {
                case ORG_TYPE_DIRECT:
                    if (channel.contains(ChannelEnum.DIRECT.getId())) {
                        return true;
                    }
                    if (channel.contains(ChannelEnum.DIRECT_ASSIGN_STORE.getId())) {
                        return selectOrgCodes.contains(orgCode);
                    }
                    return false;
                case ORG_TYPE_SPECIALTY:
                    if (channel.contains(ChannelEnum.SPECIALTY.getId())) {
                        return true;
                    }
                    if (channel.contains(ChannelEnum.SPECIALTY_ASSIGN_STORE.getId())) {
                        return selectOrgCodes.contains(orgCode);
                    }
                    return false;
                case ORG_TYPE_AUTHORIZED:
                    if (channel.contains(ChannelEnum.AUTHORIZED.getId())) {
                        return true;
                    }
                    if (channel.contains(ChannelEnum.AUTHORIZED_ASSIGN_STORE.getId())) {
                        return selectOrgCodes.contains(orgCode);
                    }
                    return false;
                default:
                    log.error("channel is invalid. uid:{}, orgCode:{}, actId:{}", request.getUserId(), orgCode, promotionId);
                    return false;
            }
        }
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof MultiPromotionConfig)) {
            log.error("config is not instanceof MultiPromotionConfig. config:{}", config);
            return;
        }
        MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.version = promotionConfig.getVersion();
        this.channel = promotionConfig.getChannel();
        this.selectOrgCodes = promotionConfig.getSelectOrgCodes();
    }
}
