package com.xiaomi.nr.promotion.rpc.phoenix;

import cn.hutool.core.collection.CollUtil;
import com.google.common.base.Stopwatch;
import com.xiaomi.nr.phoenix.api.dto.request.qualifycode.TradeInQualifyDetailReq;
import com.xiaomi.nr.phoenix.api.dto.request.qualifycode.TradeInQualifyRollbackReq;
import com.xiaomi.nr.phoenix.api.dto.request.qualifycode.TradeInQualifyWriteOffReq;
import com.xiaomi.nr.phoenix.api.dto.request.subsidyqualify.QuerySubsidyQualificationForTradeReq;
import com.xiaomi.nr.phoenix.api.dto.request.subsidyqualify.SubsidyQualificationLockReq;
import com.xiaomi.nr.phoenix.api.dto.request.subsidyqualify.SubsidyQualificationRollbackReq;
import com.xiaomi.nr.phoenix.api.dto.request.subsidyqualify.SubsidyQualificationWriteOffReq;
import com.xiaomi.nr.phoenix.api.dto.request.tradeInfo.TradeInfoQueryListReq;
import com.xiaomi.nr.phoenix.api.dto.response.QualificationSubsidyResponse;
import com.xiaomi.nr.phoenix.api.dto.response.tradeInfo.TradeInfoQueryListResp;
import com.xiaomi.nr.phoenix.api.service.SubsidyQualificationDubboService;
import com.xiaomi.nr.phoenix.api.service.TradeInfoQualificationService;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 三方优惠置换补贴proxy
 *
 * <AUTHOR>
 * @date 2024/8/21 15:46
 */
@Service
@Slf4j
public class SubsidyPhoenixProxy {
    @Reference(interfaceClass = TradeInfoQualificationService.class, version = "1.0", timeout = 2000, group = "${phoenix.dubbo.group}")
    private TradeInfoQualificationService tradeInfoQualificationService;

    @Reference(interfaceClass = SubsidyQualificationDubboService.class, version = "1.0", timeout = 2000, group = "${phoenix.dubbo.group}")
    private SubsidyQualificationDubboService subsidyQualificationDubboService;

    /**
     * 根据订单ID和资格详情列表进行核销操作
     *
     * @param orderId           订单ID
     * @param qualifyDetailList 资格详情列表
     * @return 核销结果信息
     */
    public String writeOffByOrder(Long userId, String orderId, List<TradeInQualifyDetailReq> qualifyDetailList) throws BizError {
        log.info("SubsidyPhoenixProxy.writeOffByOrder begin, userId={}, orderId = {}, qualifyDetailList = {}", userId, orderId, qualifyDetailList);
        Stopwatch stopwatch = Stopwatch.createStarted();

        TradeInQualifyWriteOffReq request = new TradeInQualifyWriteOffReq();
        request.setOrderId(orderId);
        request.setMid(userId);
        request.setWriteOffList(qualifyDetailList);

        Result<String> response;
        try {
            response = tradeInfoQualificationService.writeOffByOrder(request);
        } catch (Exception e) {
            log.error("SubsidyPhoenixProxy.writeOffByOrder error. request:{}, err", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "调用三方服务批量核销资格码异常");
        }

        log.info("SubsidyPhoenixProxy.writeOffByOrder finished, request = {}, response = {}, ws = {}", request, GsonUtil.toJson(response), stopwatch.elapsed(TimeUnit.MILLISECONDS));
        if (response.getCode() != GeneralCodes.OK.getCode()) {
            log.error("SubsidyPhoenixProxy.writeOffByOrder fail, request = {}, code = {}, message = {}", request, response.getCode(), response.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, response.getMessage());
        }
        if (response.getData() == null) {
            log.error("SubsidyPhoenixProxy.writeOffByOrder data is null. request = {}, code = {}, message = {}", request, response.getCode(), response.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, response.getMessage());
        }

        return response.getData();
    }

    /**
     * 根据订单ID和资格详情列表进行回滚操作
     *
     * @param orderId           订单ID
     * @param qualifyDetailList 资格详情列表
     * @return 回滚结果信息
     */
    public String rollbackWriteOff(Long userId, String orderId, List<TradeInQualifyDetailReq> qualifyDetailList) throws BizError {
        log.info("SubsidyPhoenixProxy.rollbackWriteOff begin, orderId = {}, qualifyDetailList = {}", orderId, qualifyDetailList);
        Stopwatch stopwatch = Stopwatch.createStarted();

        TradeInQualifyRollbackReq request = new TradeInQualifyRollbackReq();
        request.setMid(userId);
        request.setOrderId(orderId);
        request.setDetailList(qualifyDetailList);

        Result<String> response;
        try {
            response = tradeInfoQualificationService.rollbackWriteOff(request);
        } catch (Exception e) {
            log.error("SubsidyPhoenixProxy.rollbackWriteOff error. request:{}, err", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "调用三方服务批量资格码回滚异常");
        }

        log.info("SubsidyPhoenixProxy.rollbackWriteOff finished, request = {}, response = {}, ws = {}", request, GsonUtil.toJson(response), stopwatch.elapsed(TimeUnit.MILLISECONDS));
        if (response.getCode() != GeneralCodes.OK.getCode()) {
            log.error("SubsidyPhoenixProxy.rollbackWriteOff fail, request = {}, code = {}, message = {}", request, response.getCode(), response.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, response.getMessage());
        }
        if (response.getData() == null) {
            log.error("SubsidyPhoenixProxy.rollbackWriteOff data is null. request = {}, code = {}, message = {}", request, response.getCode(), response.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, response.getMessage());
        }

        return response.getData();
    }

    /**
     * 根据订mid查询资格码
     *
     * @param mid mid
     * @return 资格码信息
     */
    public TradeInfoQueryListResp queryListForCoupon(Long mid) throws BizError {
        log.info("SubsidyPhoenixProxy.queryList begin, mid = {}", mid);
        Stopwatch stopwatch = Stopwatch.createStarted();

        TradeInfoQueryListReq request = new TradeInfoQueryListReq();
        request.setMid(mid);

        Result<TradeInfoQueryListResp> response;
        try {
            response = tradeInfoQualificationService.queryListForCoupon(request);
        } catch (Exception e) {
            log.error("SubsidyPhoenixProxy.queryList error. request:{}, err", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "调用三方服务查询资格码异常");
        }

        log.info("SubsidyPhoenixProxy.queryList finished, request = {}, response = {}, ws = {}", request, GsonUtil.toJson(response), stopwatch.elapsed(TimeUnit.MILLISECONDS));
        if (response.getCode() != GeneralCodes.OK.getCode()) {
            log.error("SubsidyPhoenixProxy.queryList fail, request = {}, code = {}, message = {}", request, response.getCode(), response.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, response.getMessage());
        }
        if (response.getData() == null) {
            log.error("SubsidyPhoenixProxy.queryList data is null. request = {}, code = {}, message = {}", request, response.getCode(), response.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, response.getMessage());
        }

        return response.getData();
    }

    /**
     * 根据personalInfo查询资格码
     *
     * @param qualification personalInfo
     * @return 三方优惠资格码列表
     */
    public List<QualificationSubsidyResponse> queryPersonalInfoListForQualification(QuerySubsidyQualificationForTradeReq qualification) throws BizError {
        log.info("SubsidyPhoenixProxy.queryPersonalInfoListForQualification begin, mid = {}", qualification);
        Stopwatch stopwatch = Stopwatch.createStarted();
        Result<List<QualificationSubsidyResponse>> listResult;
        try {
            listResult = subsidyQualificationDubboService.queryValidSubsidyQualification(qualification);
        } catch (Exception e) {
            log.error("SubsidyPhoenixProxy.queryPersonalInfoListForQualification error. request:{}, err", qualification, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "调用三方服务查询深圳资格码异常");
        }
        if (listResult.getCode() != GeneralCodes.OK.getCode()) {
            log.error("SubsidyPhoenixProxy.queryPersonalInfoListForQualification fail, request = {}, code = {}, message = {}", qualification, listResult.getCode(), listResult.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, listResult.getMessage());
        }
        if (CollUtil.isEmpty(listResult.getData())) {
            log.error("SubsidyPhoenixProxy.queryPersonalInfoListForQualification data is null. request = {}, code = {}, message = {}", qualification, listResult.getCode(), listResult.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, listResult.getMessage());
        }
        log.info("SubsidyPhoenixProxy.queryPersonalInfoListForQualification finished, qualification = {}, response = {}, ws = {}", qualification, listResult, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return listResult.getData();
    }

    /**
     * 根据订单ID进行锁定操作
     *
     * @param mid regionId cateCode orderId orderInfo
     */
    public void governmentLockSubsidyQualification(Long mid, Integer regionId, String cateCode, Long orderId, String orderInfo) throws BizError {
        log.info("SubsidyPhoenixProxy.governmentLockSubsidyQualification begin, mid={}, regionId = {},cateCode = {},orderId = {},orderInfo = {}", mid, regionId, cateCode, orderId, orderInfo);
        Stopwatch stopwatch = Stopwatch.createStarted();

        SubsidyQualificationLockReq request = new SubsidyQualificationLockReq();
        request.setMid(mid);
        request.setRegionId(regionId);
        request.setCateCode(cateCode);
        request.setOrderId(orderId);
        request.setOrderInfo(orderInfo);
        Result<Void> result;
        try {
            result = subsidyQualificationDubboService.lockSubsidyQualification(request);
        } catch (Exception e) {
            log.error("SubsidyPhoenixProxy.governmentLockSubsidyQualification error. request:{}, err", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError,e.getMessage());
        }
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("SubsidyPhoenixProxy.governmentLockSubsidyQualification fail, request = {}, code = {}, message = {}", request, result.getCode(), result.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, result.getMessage());
        }
        log.info("SubsidyPhoenixProxy.governmentLockSubsidyQualification finished, request = {}, response = {}, ws = {}", request, result, stopwatch.elapsed(TimeUnit.MILLISECONDS));
    }

    /**
     * 根据订单ID进行核销操作
     *
     * @param mid orderId
     */
    public void governmentWriteOffSubsidyQualification(Long mid, Long orderId) throws BizError {
        log.info("SubsidyPhoenixProxy.governmentWriteOffSubsidyQualification begin, mid={},orderId = {}", mid, orderId);
        Stopwatch stopwatch = Stopwatch.createStarted();

        SubsidyQualificationWriteOffReq request = new SubsidyQualificationWriteOffReq();
        request.setMid(mid);
        request.setOrderId(orderId);
        Result<Void> result;
        try {
            result = subsidyQualificationDubboService.writeOffSubsidyQualification(request);
        } catch (Exception e) {
            log.error("SubsidyPhoenixProxy.governmentWriteOffSubsidyQualification error. request:{}, err", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("SubsidyPhoenixProxy.governmentWriteOffSubsidyQualification fail, request = {}, code = {}, message = {}", request, result.getCode(), result.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, result.getMessage());
        }
        log.info("SubsidyPhoenixProxy.governmentWriteOffSubsidyQualification finished, request = {}, response = {}, ws = {}", request, result, stopwatch.elapsed(TimeUnit.MILLISECONDS));

    }

    /**
     * 根据订单ID进行回滚操作
     *
     * @param userId orderId
     */
    public void governmentRollbackSubsidyQualification(Long userId, Long orderId) throws BizError {
        log.info("SubsidyPhoenixProxy.governmentRollbackSubsidyQualification begin, userId={},orderId = {}", userId, orderId);
        Stopwatch stopwatch = Stopwatch.createStarted();

        SubsidyQualificationRollbackReq request = new SubsidyQualificationRollbackReq();
        request.setUserId(userId);
        request.setOrderId(orderId);
        Result<Void> result;
        try {
            result = subsidyQualificationDubboService.rollbackSubsidyQualification(request);
        } catch (Exception e) {
            log.error("SubsidyPhoenixProxy.governmentRollbackSubsidyQualification error. request:{}, err", request, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, e.getMessage());
        }
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            log.error("SubsidyPhoenixProxy.governmentRollbackSubsidyQualification fail, request = {}, code = {}, message = {}", request, result.getCode(), result.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, result.getMessage());
        }
        log.info("SubsidyPhoenixProxy.governmentRollbackSubsidyQualification finished, request = {}, response = {}, ws = {}", request, result, stopwatch.elapsed(TimeUnit.MILLISECONDS));
    }
}
