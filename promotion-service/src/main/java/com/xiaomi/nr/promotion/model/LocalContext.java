package com.xiaomi.nr.promotion.model;

import com.xiaomi.nr.phoenix.api.dto.response.tradeInfo.TradeQualificationInfo;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.domain.redpackage.model.RedPacket;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.entity.redis.OrgInfo;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.common.GoodsActCount;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.GoodsIndexExtend;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import lombok.Data;

import java.util.*;

/**
 * 区别于RequestContext，用于记录优惠相关详细信息
 *
 * <AUTHOR>
 * @date 2021/3/16
 */
@Data
final public class LocalContext {

    /**
     * 业务场景
     */
    private BizPlatformEnum bizPlatform;
    /**
     * 购物车信息
     */
    private List<CartItem> carts;


    /**
     * 外部资源
     */
    private Map<ResourceExtType, ExternalDataProvider<?>> externalDataMap = new HashMap<>();

    /**
     * 需要被使用的资源
     */
    private List<ResourceProvider<?>> handlers = new ArrayList<>();

    /**
     * 场景下品能参加的活动，SSU_ID, Join ActId sets
     */
    private Map<Long, Set<Long>> sceneActIdsMap = new HashMap<>();
    // -------------------------------------------------

    /**
     * 购物车里所有商品的层级关系信息
     */
    private Map<String, GoodsHierarchy> goodsHierarchyMap;

    /**
     * 门店信息
     */
    private OrgInfo orgInfo;

    /**
     * 全局排除商品
     */
    private CompareItem globalInExclu;

    /**
     * 全局排除活动券
     */
    private CompareItem globalActInExclu;

    /**
     * 以旧换新单档位key
     */
    private String renewLevelKey;
    // -------------------------------------------------

    /**
     * 符合条件的商品的index
     */
    private List<GoodsIndex> goodIndex = new ArrayList<>();

    /**
     * 活动结束时间
     */
    private Long actEndTime = 0L;

    /**
     * 符合活动的次数
     */
    private Integer fillTimes = 0;

    /**
     * 符合条件的阶梯
     */
    private QuotaLevel quotaLevel;

    /**
     * 符合条件的商品扩展信息
     */
    private List<GoodsIndexExtend> goodsIndexExtends;

    /**
     * 购物车参与活动数量信息 key:cartItemId
     */
    private Map<String, GoodsActCount> cartItemActCountMap = new HashMap<>();

    /**
     * 剩余人每店活动数量
     */
    private Long remainPersonStoreNum = 0L;

    /**
     * 商品活动数量信息 key:sku/packageId
     */
    private Map<String, GoodsActCount> goodsActCountMap = new HashMap<>();
    // -------------------------------------------------

    /**
     * 优惠信息
     */
    private PromotionInfo promotion;
    /**
     * 活动最后时间
     */
    private Long finTime = 0L;

    /**
     * 包邮信息
     */
    private Express express;

    // ---------------------红包相关---------------------
    /**
     * 购物车使用的红包列表
     */
    private List<RedPacket> usedRedpacketList = new ArrayList<>();
    /**
     * 购物车可用的红包列表
     */
    private List<RedPacket> fulRedpacketList = new ArrayList<>();
    /**
     * 红包可使用金额
     */
    private Long hasRedpacket = 0L;

    // ---------------------三方优惠相关---------------------

    /**
     * 购物车可用的三方优惠资格码列表 key:spuGroup value:QualificationInfo
     */
    private Map<String, TradeQualificationInfo> usedQualifyMap = new HashMap<>();

    /**
     * 下单使用的三方优惠资格码列表 key:itemId value:QualificationInfo
     */
    private Map<String, TradeQualificationInfo> cartItemQualifyMap = new HashMap<>();

    // ---------------------其他---------------------
    /**
     * 是否是价保，true-是，false-否
     */
    private Boolean isProtectPrice = false;
    /**
     * 订单号
     */
    private Long orderId = 0L;
    /**
     * 北京以旧换新加购数量导致不可用优惠错误提示
     */
    private String purchaseSubsidyInvalid = "";
    /**
     * mid所属人群
     */
    private List<String> crowdList;
    /**
     * 商品是否强绑定
     */
    private Boolean isBind = false;

    /**
     * 参加活动类型
     */
    private List<Integer> joinActivityType;

    /**
     * 增加资源
     *
     * @param resourceHandler 资源处理器
     */
    public void addProvider(ResourceProvider<?> resourceHandler) {
        this.handlers.add(resourceHandler);
    }
}
