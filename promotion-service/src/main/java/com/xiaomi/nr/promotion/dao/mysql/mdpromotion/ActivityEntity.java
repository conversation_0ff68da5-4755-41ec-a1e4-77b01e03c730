package com.xiaomi.nr.promotion.dao.mysql.mdpromotion;

import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
@Data
public class ActivityEntity {

	private Long id;

	private String name;

	private List<Integer> channels;

	private Long beginTime;

	private Long endTime;

	private Integer sourceApp;

	private String uniqId;

	private Integer acStatus;

	private Integer promotionType;

	private String rule;

	private Timestamp updateTime;

	private String creator;

	private String expand;

	private Integer tradeType;

	protected Timestamp createTime;

	protected Integer isDeleted;
}
