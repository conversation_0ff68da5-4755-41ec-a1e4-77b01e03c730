package com.xiaomi.nr.promotion.entity.redis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * 商品数量限制规则
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Data
public class ActNumLimitRule implements Serializable {
    private static final long serialVersionUID = 955862614053091604L;

    /**
     * 每单限购数
     */
    @SerializedName("order_limit")
    private Long orderLimit = 0L;

    /**
     * 每人每天限购数
     */
    @SerializedName("person_limit_day")
    private Long personLimitDay = 0L;

    /**
     * 每人限购数
     */
    @SerializedName("person_limit_activity")
    private Long personLimit = 0L;

    /**
     * 每天每个门店限量
     */
    @SerializedName("day_limit_one")
    private Long dayLimitOne = 0L;

    /**
     * 每天全部门店限量
     */
    @SerializedName("day_limit_all")
    private Long dayLimitAll = 0L;

    /**
     * 活动期间每个门店限量
     */
    @SerializedName("activity_limit_one")
    private Long activityLimitOne = 0L;

    /**
     * 活动期间全部门店限量
     */
    @SerializedName("activity_limit_all")
    private Long activityLimitAll = 0L;

    /**
     * 每人每个门店限购数
     */
    @SerializedName("person_limit_one")
    private Long personLimitOne = 0L;
}
