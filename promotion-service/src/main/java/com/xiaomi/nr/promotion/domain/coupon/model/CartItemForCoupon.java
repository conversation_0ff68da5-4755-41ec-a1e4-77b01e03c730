package com.xiaomi.nr.promotion.domain.coupon.model;

import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.enums.CouponServiceTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Optional;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： duanpengqi
 * @date： 2024/4/26
 * @description：本类继承于CartItem类，主要存放coupon domain域中的类，请勿在领域外使用
 * @modifiedBy：
 * @version: 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CartItemForCoupon extends CartItem {

    /**
     * 过coupon域之前的价格
     */
    private Long cartPriceBeforeCoupon = 0L;

    /**
     * remain workHourStandardPage
     */
    private Integer remainStandardPage;

    /**
     * remain
     */
    private Integer remainCount;

    /**
     * 使用过耗材券
     */
    private boolean isUsedConsumables = false;

    /**
     * 使用过按需维保券
     */
    private boolean isUsedNeedMaintenance = false;

    /**
     * 使用过基础保养券
     */
    private boolean isUsedBasicMaintenance = false;

    /**
     * 使用过漆面修复券
     */
    private boolean isUsedPaintRepair = false;

    /**
     * 使用过补胎券
     */
    private boolean isUsedTireRepair = false;

    /**
     * 使用玻璃无忧券
     */
    private boolean isUsedGlassRepair = false;

    public void useCouponTag(CouponServiceTypeEnum serviceTypeEnum) {
        switch (serviceTypeEnum) {
            case CONSUMABLES:
                isUsedConsumables = true;
                break;
            case NEED_MAINTENANCE:
                isUsedNeedMaintenance = true;
                break;
            case BASIC_MAINTENANCE:
                isUsedBasicMaintenance = true;
                break;
            case PAINT_REPAIR:
                isUsedPaintRepair = true;
                break;
            case REPAIR_TAIR:
                isUsedTireRepair = true;
                break;
            case GLASS_REPAIR:
                isUsedGlassRepair = true;
                break;
            default:
                break;
        }
    }
    
    public Integer getRemainStandardPage() {
        return Optional.ofNullable(this.remainStandardPage).orElse(0);
    }

    public Integer getRemainCount() {
        return Optional.ofNullable(this.remainCount).orElse(0);
    }
}
