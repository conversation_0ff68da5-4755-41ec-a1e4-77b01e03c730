package com.xiaomi.nr.promotion.util;

import com.google.common.collect.Sets;
import com.xiaomi.nr.md.promotion.admin.api.constant.ScopeTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityScope;
import com.xiaomi.nr.promotion.api.dto.enums.PromotionTag;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.UserCarPermitEnum;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyReducePromotionConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/5/12 18:11
 */
public class CarShopBuyReduceUtil {

    private CarShopBuyReduceUtil() {
    }


    /**
     * 判断是否需要车主信息
     *
     * @param activityTool
     * @return
     */
    public static boolean needCarOwnerInfo(ActivityTool activityTool) {
        if (Objects.isNull(activityTool)) {
            return false;
        }
        return needCarOwnerInfo(activityTool.getPromotionConfig());
    }

    /**
     * 判断是否需要车主信息
     *
     * @param activityConfig
     * @return
     */
    public static PromotionTag needCarOwnerInfo(ActivityConfig activityConfig) {
        if (Objects.isNull(activityConfig)) {
            return PromotionTag.ALL;
        }

        Set<String> scopes = PromotionUserTagUtil.getUserTagScopes(activityConfig);
        if (scopes.contains("ALL")) {
            return PromotionTag.ALL;
        }
        if (scopes.contains("CAR_OWNER")) {
            if (scopes.contains("LOCK_ORDER_OWNER")) {
                return PromotionTag.ALL_CAR_OWNER_REDUCE;
            }
            return PromotionTag.CAR_OWNER_REDUCE;
        }
        if (scopes.contains("LOCK_ORDER_OWNER")) {
            return PromotionTag.LOCK_ORDER_OWNER_REDUCE;
        }
        return PromotionTag.ALL;
    }

    /**
     * 判断是否需要车主信息
     *
     * @param promotionConfig
     * @return
     */
    public static boolean needCarOwnerInfo(AbstractPromotionConfig promotionConfig) {
        if (!(promotionConfig instanceof BuyReducePromotionConfig)) {
            return false;
        }
        BuyReducePromotionConfig config = (BuyReducePromotionConfig) promotionConfig;
        return PromotionTag.needCarOwnerInfo(config.getActivityCarTag());
    }

    /**
     * 判断车主身份和活动是否匹配
     *
     * @param activityTool
     * @param userCarPermit
     * @return
     */
    public static boolean scopeMatch(ActivityTool activityTool, UserCarPermitEnum userCarPermit) {
        boolean needCarOwner = needCarOwnerInfo(activityTool);
        if (!needCarOwner) {
            return true;
        }

        AbstractPromotionConfig promotionConfig = activityTool.getPromotionConfig();
        if (!(promotionConfig instanceof BuyReducePromotionConfig)) {
            return false;
        }
        BuyReducePromotionConfig config = (BuyReducePromotionConfig) promotionConfig;
        return scopeMatch(config.getActivityCarTag(), userCarPermit);
    }

    public static boolean scopeMatch(PromotionTag activityCarTag, UserCarPermitEnum userCarPermit) {
        if (activityCarTag == PromotionTag.ALL) {
            return true;
        }
        if (activityCarTag == PromotionTag.CAR_OWNER_REDUCE) {
            return userCarPermit == UserCarPermitEnum.OWNER ||
                    userCarPermit == UserCarPermitEnum.OWNER_AND_UNDELIVERED_OWNER;
        }
        if (activityCarTag == PromotionTag.LOCK_ORDER_OWNER_REDUCE) {
            return userCarPermit == UserCarPermitEnum.UNDELIVERED_OWNER ||
                    userCarPermit == UserCarPermitEnum.OWNER_AND_UNDELIVERED_OWNER;
        }
        if (activityCarTag == PromotionTag.ALL_CAR_OWNER_REDUCE) {
            return userCarPermit == UserCarPermitEnum.UNDELIVERED_OWNER ||
                    userCarPermit == UserCarPermitEnum.OWNER ||
                    userCarPermit == UserCarPermitEnum.OWNER_AND_UNDELIVERED_OWNER;
        }
        return false;
    }

}
