package com.xiaomi.nr.promotion.enums;

/**
 * <AUTHOR>
 * @date 2024/12/13
 */
import lombok.Getter;

@Getter
public enum CouponPromotionType {

    /**
     * 满减
     */
    ConditionReduce(1, "cash","现金券"),

    /**
     * 满折
     */
    ConditionDiscount(2, "discount","折扣券"),

    /**
     * 兑换券
     */
    NyuanBuy(3, "deductible","抵扣券"),

    /**
     * 立减
     */
    DirectReduce(4, "cash","现金券"),

    /**
     * 立减
     */
    Gift(5, "gift","礼品券"),

    /**
     * 米金兑换券
     */
    TypeCodeCouponCoin(11,"coin","米金兑换券"),

    /**
     * 会员券
     */
    ProMemberCoupon(12,"proMember","会员券")
    ;

    private final int code;
    private final String value;
    private final String name;

    CouponPromotionType(int code, String value, String name) {
        this.code = code;
        this.value = value;
        this.name = name;
    }

    public static CouponPromotionType getPromotionType(int code) {
        for (CouponPromotionType couponPromotionType : CouponPromotionType.values()) {
            if (couponPromotionType.code == code) {
                return couponPromotionType;
            }
        }
        return null;
    }


    public static String getValueByCode(int code) {
        for (CouponPromotionType couponPromotionType : CouponPromotionType.values()) {
            if (couponPromotionType.code == code) {
                return couponPromotionType.getValue();
            }
        }
        return null;
    }

}
