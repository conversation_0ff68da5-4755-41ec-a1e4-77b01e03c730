package com.xiaomi.nr.promotion.domain.coupon.service.base.processor;

import com.google.common.collect.ImmutableMap;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.TreeSet;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 2023/1/10
 */
@Slf4j
@Component
public class CarGoodsCouponProcessor extends GoodsCouponProcessor {


    /**
     * 券类型排序
     */
    private static final Map<Integer, Integer> TYPE_SORT_ORDER = ImmutableMap.of(
            CouponTypeEnum.CASH.getType(), 3,
            CouponTypeEnum.DEDUCT.getType(), 2,
            CouponTypeEnum.DISCOUNT.getType(), 1);


    /**
     * 初始化可用券列表，排序规则：
     * 1.ClientID匹配的在前
     * 2. 金额排序： 优惠金额高的在前
     * 3. 时间排序：结束时间小的在前
     * 4. 定金券 --->  尾款券
     * 5. 类型排序：CASH > DEDUCT > DISCOUT > 免邮
     * @param checkoutResultMap
     * @return
     */
    public TreeSet<Coupon> initSortCouponList(Map<Long, CouponCheckoutResult> checkoutResultMap) {
        TreeSet<Coupon> validCouponList = new TreeSet<>((coupon1, coupon2) -> {

            Integer checkoutStage1 = Optional.ofNullable(coupon1.getCheckoutStage()).orElse(0);
            Integer checkoutStage2 = Optional.ofNullable(coupon2.getCheckoutStage()).orElse(0);
            if (checkoutStage1 < checkoutStage2) {
                return -1;
            } else if (checkoutStage1 > checkoutStage2) {
                return 1;
            }

            //结束时间排序，时间小的在前
            long endTime1 = coupon1.getEndTime();
            long endTime2 = coupon2.getEndTime();
            if (endTime1 < endTime2) {
                return -1;
            } else if (endTime1 > endTime2) {
                return 1;
            }

            //reduce大的在前
            CouponCheckoutResult result1 = checkoutResultMap.get(coupon1.getId());
            long reduceAmount1 = result1.getReduceAmount();
            CouponCheckoutResult result2 = checkoutResultMap.get(coupon2.getId());
            long reduceAmount2 = result2.getReduceAmount();
            if (reduceAmount1 > reduceAmount2) {
                return -1;
            } else if (reduceAmount1 < reduceAmount2) {
                return 1;
            }

            //券类型排序,CASH > DEDUCT > DISCOUT
            int type1 = coupon1.getType();
            int type2 = coupon2.getType();
            if (TYPE_SORT_ORDER.getOrDefault(type1, 0) > TYPE_SORT_ORDER.getOrDefault(type2, 0)) {
                return -1;
            }
            if (TYPE_SORT_ORDER.getOrDefault(type1, 0) < TYPE_SORT_ORDER.getOrDefault(type2, 0)) {
                return 1;
            }

            //treeSet需要消除重复项
            if (coupon1.getId() < coupon2.getId()) {
                return -1;
            } else {
                return 1;
            }

        });

        return validCouponList;

    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR;
    }

}
