package com.xiaomi.nr.promotion.activity;

import com.google.common.collect.Lists;
import com.xiaomi.goods.gis.dto.stock.Region;
import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.GetProductActRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.componet.action.CouponLimitAction;
import com.xiaomi.nr.promotion.componet.action.GiftAction;
import com.xiaomi.nr.promotion.componet.condition.*;
import com.xiaomi.nr.promotion.componet.preparation.GlobalExcludePreparation;
import com.xiaomi.nr.promotion.componet.preparation.GoodsHierarchyPreparation;
import com.xiaomi.nr.promotion.componet.preparation.OrgInfoPreparation;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.GiftActGoodsProvider;
import com.xiaomi.nr.promotion.engine.ProductActGoodsProvider;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.FillGoodsGroup;
import com.xiaomi.nr.promotion.entity.redis.GiftBargainGroup;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.enums.BooleanV2Enum;
import com.xiaomi.nr.promotion.enums.PolicyQuotaTypeEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.*;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.GiftPromotionConfig;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.*;
import java.util.concurrent.TimeUnit;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 赠品活动
 *
 * <AUTHOR>
 * @date 2021/3/23
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class GiftActivity extends AbstractActivityTool implements ActivityTool, ProductActGoodsProvider, GiftActGoodsProvider {
    /**
     * 活动阶梯信息 a
     */
    private List<QuotaLevel> levelList;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine
                .conditionPreparation(GoodsHierarchyPreparation.class)
                .conditionPreparation(GlobalExcludePreparation.class)
                .conditionPreparation(OrgInfoPreparation.class)
                .condition(DailyTimeCondition.class)
                .condition(OrgCondition.class)
                .condition(ClientCondition.class)
                .condition(GiftCondition.class)
                .condition(UserGroupCondition.class)
                .condition(OnlineJoinLimitCondition.class)
                .condition(OfflineJoinLimitCondition.class)
                .condition(FrequencyCondition.class)
                .action(CouponLimitAction.class)
                .action(GiftAction.class);
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.GIFT;
    }

    /**
     * 构建购物车优惠数据
     *
     * @param context 上下文
     * @return 内容
     */
    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) {
        int mutexLimit = actMutexLimit ? BooleanEnum.YES.getValue() : BooleanEnum.NO.getValue();
        Integer frequencyVal = frequency != null ? frequency.getValue() : null;
        NumLimitRule numLimitRule = ActRespConverter.convert(this.numLimitRule);

        List<PolicyNewLevel> policy = levelList.stream()
                .map(level -> getPolicyNewItem(level.getIncludeGoodsGroups(), level.getGiftGoods().getSkuGroupList(), null, false))
                .collect(Collectors.toList());
        PolicyNew policyNew = new PolicyNew();
        policyNew.setPolicy(policy);
        policyNew.setType((long) type.getValue());

        PromotionInfo promotionInfo = buildDefaultPromotionInfo(context);
        promotionInfo.setFrequent(frequencyVal);
        promotionInfo.setTotalLimitNum(actLimitNum);
        promotionInfo.setActivityMutexLimit(mutexLimit);
        promotionInfo.setActivityMutex(actMutexes);
        promotionInfo.setNumLimitRule(numLimitRule);
        promotionInfo.setPolicyNew(policyNew);
        return promotionInfo;
    }

    /**
     * 获取政策
     *
     * @param includeGoodsGroups 主商品
     * @param stockMap           库存
     * @param checkStock         是否检查库存，true-是，false-否
     * @return 政策
     */
    private PolicyNewLevel getPolicyNewItem(List<FillGoodsGroup> includeGoodsGroups, List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> skuGroupList, Map<Long, Long> stockMap, boolean checkStock) {
        if (CollectionUtils.isEmpty(includeGoodsGroups)) {
            return null;
        }
        PolicyNewGoods newGoods = new PolicyNewGoods();
        PolicyNewRule newRule = new PolicyNewRule();
        List<PolicyNewSkuGroup> skuGroupsList = getNewSkuGroupListFromBean(stockMap, skuGroupList, checkStock);
        if (CollectionUtils.isEmpty(skuGroupsList)) {
            log.info("gift sku group list is empty. actId:{}", id);
            return null;
        }
        newGoods.setSkuGroupsList(skuGroupsList);
        newRule.setGiftGoods(newGoods);

        List<PolicyNewFillGoodsGroup> includedGoodsGroup = new ArrayList<>();
        for (FillGoodsGroup fillGoodsGroup : includeGoodsGroups) {
            PolicyNewFillGoodsGroup goodsGroup = new PolicyNewFillGoodsGroup();
            goodsGroup.setQuota(ActRespConverter.convert(fillGoodsGroup.getQuota()));
            includedGoodsGroup.add(goodsGroup);
        }
        PolicyNewLevel policyItem = new PolicyNewLevel();
        policyItem.setIncludedGoodsGroup(includedGoodsGroup);
        policyItem.setRule(newRule);
        return policyItem;
    }

    /**
     * 获取产品站信息
     *
     * @param clientId       应用ID
     * @param orgCode        门店Code
     * @param skuPackageList 活动类别
     */
    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        if (!checkProductGoodsActCondition(clientId, orgCode)) {
            log.debug("gift condition is not match. actId:{} clientId:{} orgCode:{}", id, clientId, orgCode);
            return Collections.emptyMap();
        }
        List<String> joinedSkuPackageList = Lists.newArrayList(includeSkuPackages);
        joinedSkuPackageList.retainAll(skuPackageList);
        if (CollectionUtils.isEmpty(joinedSkuPackageList)) {
            log.debug("gift joinedSkuPackageList retain empty. actId:{} clientId:{} orgCode:{}", id, clientId, orgCode);
            return Collections.emptyMap();
        }
        List<ProductActGoods> giftGoodsInfoV2s = Optional.ofNullable(levelList)
                .orElse(Collections.emptyList()).stream().map(quotaLevel -> doConvertActGoodsFromLevel(quotaLevel, Boolean.TRUE)).collect(Collectors.toList());

        ProductActInfo productActInfo = new ProductActInfo();
        productActInfo.setType(type.getValue());
        productActInfo.setId(id);
        productActInfo.setName(name);
        productActInfo.setChannels(channels);
        productActInfo.setSelectClientList(selectClientList);
        productActInfo.setSelectOrgList(selectOrgList);
        productActInfo.setUnixStartTime(getUnixStartTime());
        productActInfo.setUnixEndTime(getUnixEndTime());
        productActInfo.setGiftGoods(giftGoodsInfoV2s.get(giftGoodsInfoV2s.size() - 1));
        productActInfo.setGiftGoodsV2(Lists.reverse(giftGoodsInfoV2s));
        return joinedSkuPackageList.stream().collect(Collectors.toMap(skuPackage -> skuPackage, skuPackage -> productActInfo));
    }

    /**
     * 获取活动详情
     *
     * @return 活动详情
     */
    @Override
    public ActivityDetail getActivityDetail() {
        ActivityDetail activityDetail = getBasicActivityDetail();
        activityDetail.setDescRule(descRule);
        return activityDetail;
    }

    /**
     * 获取产品站活动优惠信息
     *
     * @param request   产品站信息
     * @param hierarchy 商品层级
     * @param isOrgTool 是否来自门店
     * @return 优惠数据
     */
    @Override
    public PromotionInfo getProductAct(GetProductActRequest request, GoodsHierarchy hierarchy, boolean isOrgTool) throws BizError {
        if (!checkProductActCondition(request, isOrgTool)) {
            log.debug("gift condition is not match. request:{}", request);
            return null;
        }

        QuotaLevel lastLevel = levelList.get(levelList.size() - 1);
        ActPromExtend extend = buildGiftPromotionExtend(lastLevel.getGiftGoods().getSkuGroupList(), lastLevel.getIncludeGoodsGroups(),
                lastLevel.getGiftGoods().getIgnoreStock());
        extend.setShowUsableGoods(true);

        PolicyNew policyNew = buildPolicyNewForGiftAct(request, isOrgTool, hierarchy);
        if (policyNew == null) {
            return null;
        }
        PromotionInfo promotionInfo = getDefaultProductAct();
        promotionInfo.setExtend(GsonUtil.toJson(extend));
        promotionInfo.setTypeInfo("满赠");
        promotionInfo.setPolicyNew(policyNew);
        promotionInfo.setPolicys(null);
        return promotionInfo;
    }

    /**
     * 构建线上产品站 PromotionExtend
     *
     * @param skuGroupList       赠品
     * @param includeGoodsGroups 主商品
     * @param ignoreStock        是否忽略库存
     * @return ActPromExtend
     */
    protected ActPromExtend buildGiftPromotionExtend(List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> skuGroupList, List<FillGoodsGroup> includeGoodsGroups, Integer ignoreStock) {
        if (CollectionUtils.isEmpty(skuGroupList) || CollectionUtils.isEmpty(includeGoodsGroups)) {
            return null;
        }
        if (includeGoodsGroups.get(0).getQuota() == null) {
            return null;
        }
        //线上只能有一个阶梯
        List<GiftBargainGroup> listInfo = skuGroupList.get(0).getListInfo();
        Map<String, String> skuMap = listInfo.stream().map(item -> String.valueOf(item.getSku()))
                .collect(Collectors.toMap(sku -> sku, sku -> sku, (val1, val2) -> val1, LinkedHashMap::new));
        List<String> skuList = listInfo.stream().map(item -> String.valueOf(item.getSku())).collect(Collectors.toList());

        // perNum
        boolean perNum = false;
        if (Objects.equals(PolicyQuotaTypeEnum.POLICY_QUOTA_PER_NUM.getType(), includeGoodsGroups.get(0).getQuota().getType())) {
            perNum = true;
        }

        //线上只有一个组,且取第一个商品的信息（线上加价购商品的价格必须相同）
        ActPromExtend extend = new ActPromExtend();
        extend.setList(skuMap);
        extend.setSkuList(skuList);
        extend.setEndTime(getUnixEndTime());
        extend.setAccessCode(accessCode);
        extend.setCartPrice(listInfo.get(0).getCartPrice());
        extend.setRefundValue(listInfo.get(0).getRefundValue());
        extend.setIgnoreStock(ignoreStock);
        extend.setPerNum(perNum);
        return extend;
    }

    protected PolicyNew buildPolicyNewForGiftAct(GetProductActRequest request, boolean isOrgTool, GoodsHierarchy hierarchy) throws BizError {
        List<PolicyNewLevel> policy = levelList.stream()
                .map(level -> {
                    Map<Long, Long> stockMap = Collections.emptyMap();
                    // 门店或者线上非忽略库存， 请求库存
                    boolean checkStock = isOrgTool || BooleanV2Enum.isNo(level.getGiftGoods().getIgnoreStock());
                    if (checkStock) {
                        Set<Long> idList = getGroupGoodsIdSetFromBean(level.getGiftGoods().getSkuGroupList());
                        Long masterId = Long.valueOf(StringUtils.isNotBlank(request.getSku()) ? hierarchy.getGoodsId() : request.getCommodityId());
                        try {
                            stockMap = getGoodsStock(masterId, idList, request, isOrgTool);
                        } catch (BizError e) {
                            throw new RuntimeException(e);
                        }
                    }
                    return getPolicyNewItem(level.getIncludeGoodsGroups(), level.getGiftGoods().getSkuGroupList(), stockMap, checkStock);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(policy)) {
            return null;
        }

        PolicyNew policyNew = new PolicyNew();
        policyNew.setPolicy(Lists.reverse(policy));
        policyNew.setType((long) type.getValue());
        return policyNew;
    }

    /**
     * gift goods
     *
     * @return ProductActGoods
     */
    @Override
    public ProductActGoods getAdditionalGoods() {
        if (CollectionUtils.isEmpty(levelList)) {
            return new ProductActGoods();
        }
        return doConvertActGoodsFromLevel(levelList.get(levelList.size() - 1), false);
    }


    /**
     * 获取赠品详情，v2
     *
     * @return List<ProductActGoods>
     */
    @Override
    public List<ProductActGoods> getAdditionalGoodsV2() {
        List<ProductActGoods> giftGoods = Optional.ofNullable(levelList).orElse(Collections.emptyList()).stream().map(quotaLevel -> doConvertActGoodsFromLevel(quotaLevel, Boolean.TRUE)).collect(Collectors.toList());
        return Lists.reverse(giftGoods);
    }

    /**
     * 根据区域id设置库存
     *
     * @param actGoodsV2s 返回值信息
     * @param areaId      区域id
     * @param cityId      城市id
     * @param provinceId  省Id
     * @param districtId  街道Id
     */
    @Override
    public void setGiftStockByArea(List<ProductActGoods> actGoodsV2s, Integer areaId, Integer cityId, Integer provinceId, Integer districtId) {
        if (CollectionUtils.isEmpty(actGoodsV2s)) {
            return;
        }
        HashSet<Long> goodsIdSet = getAreaQueryStockIds(actGoodsV2s);
        if (CollectionUtils.isEmpty(goodsIdSet)) {
            return;
        }
        Region region = new Region();
        if (districtId != null && provinceId != null && areaId!= null && cityId!= null){
            region.setDistrict(districtId);
            region.setProvince(provinceId);
            region.setArea(areaId);
            region.setCity(cityId);
        }
        Map<Long, Long> skuStockMap;
        try {
            ListenableFuture<Map<Long, Long>> goodsStockNumAsync = goodsStockServiceProxy.getGoodsStockNumAsync(new ArrayList<>(goodsIdSet), region);
            skuStockMap = goodsStockNumAsync.get(500, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("request gis stock error or timeout", e);
            return;
        }
        for (ProductActGoods actGoodsV2 : actGoodsV2s) {
            setAreaActNumLimit(skuStockMap, actGoodsV2);
        }
    }

    private HashSet<Long> getAreaQueryStockIds(List<ProductActGoods> actGoodsV2s) {
        HashSet<Long> goodsIdSet = new HashSet<>();
        for (ProductActGoods actGoodsV2 : actGoodsV2s) {
            for (SkuGroup skuGroup : actGoodsV2.getSkuGroupList()) {
                for (GoodsInfo goodsInfo : skuGroup.getGoodsInfoList()) {
                    goodsIdSet.add(goodsInfo.getGoodsId());
                }
            }
        }
        return goodsIdSet;
    }

    private void setAreaActNumLimit(Map<Long, Long> skuStockMap, ProductActGoods actGoodsV2) {
        for (SkuGroup skuGroup : actGoodsV2.getSkuGroupList()) {
            for (GoodsInfo goodsInfo : skuGroup.getGoodsInfoList()) {
                Long goodsId = goodsInfo.getGoodsId();
                Long stockNum = skuStockMap.get(goodsId);
                goodsInfo.setActNumLimit(2);
                if (stockNum != null && stockNum > 0) {
                    goodsInfo.setActNumLimit(1);
                }
            }
        }
    }

    /**
     * 加购时校验商品参加活动的有效性
     *
     * @param request 商品信息
     * @param goodsHierarchyMap 商品层级关系map
     * @return 不合法的商品列表
     * @throws BizError 业务异常
     */
    @Override
    public List<CheckGoodsItem> checkProductsAct(CheckProductsActRequest request, Map<String, GoodsHierarchy> goodsHierarchyMap) throws BizError {
        List<CheckGoodsItem> invalidGoodsList = new ArrayList<>();
        // 校验商品是否在活动配置中（多阶梯，主品取第一组，赠品打平）
        List<FillGoodsGroup> allMainGoodsList = levelList.get(0).getIncludeGoodsGroups();
        List<com.xiaomi.nr.promotion.entity.redis.SkuGroup> sendGoodsList = new ArrayList<>();
        for (QuotaLevel quotaLevel : levelList) {
            sendGoodsList.addAll(quotaLevel.getGiftGoods().getSkuGroupList());
        }
        checkIncludeGoods(request.getMainGoodsList(), allMainGoodsList, goodsHierarchyMap, request.getAttachedGoodsList(), sendGoodsList, invalidGoodsList);
        return invalidGoodsList;
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof GiftPromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        GiftPromotionConfig promotionConfig = (GiftPromotionConfig) config;
        this.levelList = promotionConfig.getLevelList();
        return true;
    }
}
