package com.xiaomi.nr.promotion.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date： 2024/5/9
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum CouponServiceTypeEnum {

    /**
     * 1: 基础保养
     */
    BASIC_MAINTENANCE(1, "基础保养", 3),

    /**
     * 2: 漆面修复
     */
    PAINT_REPAIR(2, "漆面修复", 4),
    /**
     * 3: 补胎
     */
    REPAIR_TAIR(3, "补胎", 5),

    /**
     * 4: 按需保养
     */
    NEED_MAINTENANCE(4, "按需保养", 2),

    /**
     * 耗材券
     */
    CONSUMABLES(5, "耗材券", 1),

    /**
     * 6: 玻璃维修
     */
    GLASS_REPAIR(6, "玻璃维修", 6),
    ;



    /**
     * 枚举code
     */
    private final Integer code;

    /**
     * 枚举描述
     */
    private final String desc;

    /**
     * 排序顺序
     */
    private final Integer sortNum;

    private static final Map<Integer, CouponServiceTypeEnum> ENUM_MAP = new HashMap<>();

    static {
        for (CouponServiceTypeEnum e : CouponServiceTypeEnum.values()) {
            ENUM_MAP.put(e.getCode(), e);
        }
    }

    public static CouponServiceTypeEnum valueOf(Integer code) {
        return ENUM_MAP.get(code);
    }

}
