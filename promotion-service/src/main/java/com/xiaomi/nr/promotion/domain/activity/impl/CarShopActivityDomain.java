package com.xiaomi.nr.promotion.domain.activity.impl;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.domain.activity.AbstractActivityDomain;
import com.xiaomi.nr.promotion.domain.activity.facade.CarShopActivityFacade;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024/6/19
 */
@Component
@Slf4j
public class CarShopActivityDomain extends AbstractActivityDomain {
    
    @Resource
    private CarShopActivityFacade facade;
    
    @Override
    public void checkout(DomainCheckoutContext domainCheckoutContext) throws Exception {
        List<ActivityTool> activityTools = facade.activitySearch(domainCheckoutContext);
        executeActivity(domainCheckoutContext,activityTools);
    }

    /**
     * 车商城活动执行
     * @param domainCheckoutContext 促销领域上下文
     * @throws BizError 业务异常
     */
    @Override
    public void executeActivity(DomainCheckoutContext domainCheckoutContext,List<ActivityTool> allActivityTools) throws BizError {
        CheckoutPromotionRequest request = domainCheckoutContext.getRequest();
        CheckoutContext checkoutContext = domainCheckoutContext.getContext();
        Long uid = request.getUserId();
        long startTime = System.currentTimeMillis();

        if (CollectionUtils.isEmpty(allActivityTools)) {
            CartHelper.delGiftBargain(request.getCartList());
            log.info("actList is empty. uid:{}, orgCode:{} clientId:{}", uid, request.getOrgCode(), request.getClientId());
            return;
        }

        // source和sourcecode 校验. 校验赠品、加价购 对应的活动类型. 可能会修改购物车(赠品加价购sourcecode不在可参加的活动中，删除)
        List<CartItem> cartList = request.getCartList();
        facade.checkSourceAndSourceCode(cartList, allActivityTools);

        // 再次校验 cartList 是否为空
        if (CollectionUtils.isEmpty(cartList)) {
            log.error("post checkSourceAndSourceCode, cart is empty. uid:{}", uid);
            throw ExceptionHelper.create(ErrCode.ERR_EMPTY_CART, "购物车为空");
        }

        // 迭代处理每个活动Tool
        for (ActivityTool activityTool : allActivityTools) {
            activityTool.doCheckout(request, checkoutContext);
        }

        // 同一个sku只能参加一个直降活动，取降价最多的直降活动
        this.updateItemOnSalePromotion(cartList, checkoutContext, request.getChannel());
        // 处理活动互斥
        this.processActMutex(cartList, checkoutContext);

        long endTime = System.currentTimeMillis();
        log.info("cart act checkout end. uid:{} endTime:{} ws:{}", uid, endTime, (endTime - startTime));
    }
}
