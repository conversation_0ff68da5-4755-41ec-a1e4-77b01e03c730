package com.xiaomi.nr.promotion.domain.coupon.facade;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Request;
import com.xiaomi.nr.promotion.api.dto.enums.SubmitTypeEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.domain.coupon.service.base.processor.GoodsCouponProcessor;
import com.xiaomi.nr.promotion.domain.coupon.service.car.CarCashCoupon;
import com.xiaomi.nr.promotion.domain.coupon.service.common.CouponInfoService;
import com.xiaomi.nr.promotion.engine.CouponTool;
import com.xiaomi.nr.promotion.entity.redis.OrgInfo;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.CouponGeneralType;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.external.OrgInfoExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.provider.CouponProvider;
import com.xiaomi.nr.promotion.tool.CheckoutCartTool;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by wangweiyi on 2024/2/24
 */
@Slf4j
@Component
public class CarCouponFacade extends CouponFacade {

    @Autowired
    @Qualifier("couponInfoServiceRemoteImpl")
    private CouponInfoService couponInfoService;

    @Autowired
    private GoodsCouponProcessor goodsCouponProcessor;

    @Autowired
    private ResourceProviderFactory providerFactory;

    @Autowired
    private NacosConfig nacosConfig;


    /**
     * 券下单结算逻辑，按要求结算指定优惠券
     * <p>
     * - 没有用券，直接返回。如果是结算提交则需要 返回部分信息
     * - 获取用户券列表
     * - 校验优惠券数量
     * - 按类型分组
     * - 转换为优惠工具，处理得到优惠结果
     * - 处理资源，包装响应
     *
     * @param request  业务请求
     * @param response 业务响应
     * @param context  计算上下文
     * @throws BizError 业务异常
     */
    public void checkoutForSubmit(CheckoutPromotionRequest request, CheckoutPromotionResponse response, CheckoutContext context) throws Exception {


        //限制结算业务
        if (context.getBizPlatform() != BizPlatformEnum.CAR) {
            processEmptyCoupon(request, context);
            return;
        }

        if (nacosConfig.isCarCouponDegrade()) {
            processEmptyCoupon(request, context);
            return;
        }
        if (CollectionUtils.isEmpty(request.getCouponIds()) && !CheckoutCartTool.validCouponId(request.getUsedCouponId())) {
            processEmptyCoupon(request, context);
            return;
        }

        if (CollectionUtils.isNotEmpty(request.getCouponCodes())) {
            throw ExceptionHelper.create(ErrCode.ERR_COUPON_MONEY_NUM, "无效的结算类型");
        }
        OrgInfo orgInfo = new OrgInfo();
        if (StringUtils.isNotEmpty(request.getOrgCode())) {
            OrgInfoExternalProvider externalDataProvider = (OrgInfoExternalProvider) context.getExternalDataMap().get(ResourceExtType.ORG_INFO);
            orgInfo = externalDataProvider.getFuture().get(2000, TimeUnit.MILLISECONDS);
        }

        ListenableFuture<List<CheckoutCoupon>> couponForCheckout = couponInfoService.getCouponForCheckout(request, orgInfo.getOrgType(), false);
        List<CheckoutCoupon> checkoutCouponList = couponForCheckout.get(2000, TimeUnit.MILLISECONDS);

        log.info("checkoutForSubmit checkoutCouponList:{}", GsonUtil.toJson(checkoutCouponList));

        //校验优惠券数量
        checkCouponCount(request, checkoutCouponList);
        try {

            // 取使用券类型 , 用于整车改配场景
            if (Objects.equals(request.getSubmitType(), SubmitTypeEnum.REPLACE.getCode()) && Objects.nonNull(request.getUsedCouponId())) {
                CheckoutCoupon usedCoupon = checkoutCouponList.stream().filter(coupon -> coupon.getCouponId().equals(request.getUsedCouponId())).findFirst().orElse(null);
                if (Objects.isNull(usedCoupon)) {
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "使用无效券");
                }
                context.setUsedCouponCheckoutStage(Optional.ofNullable(usedCoupon.getCheckoutStage()).orElse(0));
            }

            Map<Long, CheckoutCoupon> coupons = checkoutCouponList.stream().collect(Collectors.toMap(CheckoutCoupon::getCouponId, Function.identity()));


            // 转换为优惠工具，处理得到优惠结果
            List<CouponTool> couponTools = goodsCouponProcessor.loadCouponToolsForCheckout(checkoutCouponList, context.getBizPlatform());

            Map<Long, CouponTool> couponToolMap = couponTools.stream().collect(Collectors.toMap(CouponTool::getCouponId, Function.identity()));
//            log.info("checkoutCoupons tools:{}", GsonUtil.toJson(couponTools));

            if (CollectionUtils.isNotEmpty(request.getCouponIds())) {

                long couponId = request.getCouponIds().get(0);
                CheckoutCoupon checkoutCoupon = coupons.get(couponId);

                CouponTool selectCouponTool = couponToolMap.get(checkoutCoupon.getCouponId());
                CouponCheckoutResult result = checkoutSelectCoupon(request, context, selectCouponTool, goodsCouponProcessor, orgInfo);

                //生成context.carts
                if (request.getSourceApi() == SourceApi.SUBMIT) {
                    selectCouponTool.updateItemReduce(context.getCarts(), result);

                }
            }

            // 整车改配场景特殊处理，如果使用过券则需要退券
            if (request.getSourceApi() == SourceApi.SUBMIT && CheckoutCartTool.validCouponId(request.getUsedCouponId())) {
                CarCashCoupon usedCouponTool = (CarCashCoupon) couponToolMap.get(request.getUsedCouponId());
                if (usedCouponTool != null && usedCouponTool.canReturnUsedCoupon()) {
                    ResourceProvider<?> usedProvider = context.getResourceHandlers().stream()
                            .filter(provider -> provider.getResource().getResourceType() == ResourceType.COUPON)
                            .findAny().orElse(null);
                    if (usedProvider != null) {
                        ResourceObject<CouponProvider.ResContent> resource = (ResourceObject<CouponProvider.ResContent>) usedProvider.getResource();
                        resource.getContent().setUsedCouponId(request.getUsedCouponId());

                    } else {
                        CouponProvider returnProvider = goodsCouponProcessor.initReturnResource(request, usedCouponTool);
                        context.getResourceHandlers().add(returnProvider);
                    }
                }


            }


            if (context.getCarts() == null) {
                List<CartItem> simpleCarts = copyList(request.getCartList());
                context.setCarts(simpleCarts);
            }
        } catch (Exception e) {
            log.error("checkoutCouponsForSubmit Error. request:{} ", GsonUtil.toJson(request), e);
            if (e instanceof BizError) {
                context.setCouponBaseInfoInvalid(((BizError) e).getMsg());
            }
            if (request.getSourceApi() == SourceApi.SUBMIT) {
                log.error("coupon submit error. request:{}", request, e);
                throw e;
            }
            processEmptyCoupon(request, context);
        }

    }


    @Override
    protected void checkCouponCount(CheckoutPromotionRequest request, List<CheckoutCoupon> checkoutCouponList) throws BizError {
        if (request.getCouponIds().size() > 1) {
            throw ExceptionHelper.create(ErrCode.ERR_TOO_MANY_COUPONS, "同种类型最多只能使用一张优惠券");
        }
    }

    /**
     * 处理结算时的优惠券列表
     *
     * @param request  结算促销请求对象
     * @param response 结算促销响应对象
     * @param context  结算上下文对象
     * @throws BizError 业务异常
     */
    public void checkoutForCouponList(CheckoutPromotionV2Request request, CheckoutPromotionResponse response, CheckoutContext context) throws
            BizError {
        try {

            // 没传用户ID或按渠道结算暂时不处理券
            if (request.getUserId() == null || request.getUserId() == 0L) {
                return;
            }

            //限制结算业务
            if (context.getBizPlatform() != BizPlatformEnum.CAR) {
                return;
            }
            if (nacosConfig.isCarCouponDegrade()) {
                processEmptyCoupon(request, context);
                return;
            }


            OrgInfo orgInfo = new OrgInfo();
            if (StringUtils.isNotEmpty(request.getOrgCode())) {

                OrgInfoExternalProvider externalDataProvider = (OrgInfoExternalProvider) context.getExternalDataMap().get(ResourceExtType.ORG_INFO);
                orgInfo = externalDataProvider.getFuture().get(2000, TimeUnit.MILLISECONDS);
            }

            ListenableFuture<List<CheckoutCoupon>> couponForCheckout = couponInfoService.getCouponForCheckout(request, orgInfo.getOrgType(), request.getGetCouponList());
            List<CheckoutCoupon> checkoutCouponList = couponForCheckout.get(2000, TimeUnit.MILLISECONDS);
            log.info("checkoutForCouponList checkoutCouponList:{}", GsonUtil.toJson(checkoutCouponList));


            //先计算最优券
            List<Long> bestCouponList = getBestCoupon(request, context, checkoutCouponList);

            //计算券列表 && 分摊
            List<Coupon> finalCouponList = getCouponList(request, context, checkoutCouponList);

            //最终排序
            finalCouponList.sort(Comparator.comparingInt(o -> CouponGeneralType.valueOf(o.getCouponType()).getSortValue()));
            finalCouponList.forEach(coupon -> {
                if (bestCouponList.contains(coupon.getId())) {
                    coupon.setTags(Arrays.asList("optimal"));
                }
            });
            context.setCouponList(finalCouponList);
        } catch (Exception e) {
            log.error("CouponServiceV2 checkout error. request:{}", GsonUtil.toJson(request), e);
        }


    }

}
