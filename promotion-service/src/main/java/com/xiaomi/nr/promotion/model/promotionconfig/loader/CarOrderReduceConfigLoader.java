package com.xiaomi.nr.promotion.model.promotionconfig.loader;

import com.xiaomi.nr.md.promotion.admin.api.constant.ProductIdTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ProductPolicy;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.RangeReduceRule;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.specification.BrBudgetSpecification;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.CarOrderReduceConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


/**
 * 置换补贴
 */
@Slf4j
@Component
public class CarOrderReduceConfigLoader implements PromotionConfigLoader{
    @Override
    public boolean support(AbstractPromotionConfig promotionConfig) {
        return promotionConfig instanceof CarOrderReduceConfig;
    }

    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityConfig activityConfig) throws BizError {
        if (activityConfig == null || promotionConfig == null) {
            log.error("order reduce activityInfo is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "activityInfo is null");
        }
        CarOrderReduceConfig config = (CarOrderReduceConfig) promotionConfig;

        Map<String, ActPriceInfo> orderReduceMap = new HashMap<>();
        for (ProductPolicy productPolicy : activityConfig.getProductPolicyList()) {
            if (productPolicy.getProductIdType() != ProductIdTypeEnum.SSU.code) {
                continue;
            }
            Long productId = productPolicy.getProductId();
            ActPriceInfo actPriceInfo = new ActPriceInfo();
            actPriceInfo.setPrice(productPolicy.getPromotionPrice());
            actPriceInfo.setSsuId(productId);
            // 财务信息
            RangeReduceRule reduceRule = GsonUtil.fromJson(activityConfig.getRule(), RangeReduceRule.class);
            if (reduceRule != null) {
                BrBudgetSpecification specification = reduceRule.getBudgetSpecification();
                if (specification != null) {
                    actPriceInfo.setBudgetApplyNo(specification.getBudgetApplyNo());
                    actPriceInfo.setLineNum(specification.getLineNum());
                }
            }
            orderReduceMap.put(String.valueOf(productId), actPriceInfo);
        }
        config.setOrderReduceMap(orderReduceMap);
    }
}
