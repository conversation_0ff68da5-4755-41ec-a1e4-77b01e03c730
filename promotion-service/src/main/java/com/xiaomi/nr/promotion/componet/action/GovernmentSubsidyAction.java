package com.xiaomi.nr.promotion.componet.action;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.xiaomi.nr.phoenix.api.dto.request.qualifycode.OrderInfoReq;
import com.xiaomi.nr.phoenix.api.dto.response.tradeInfo.TradeQualificationInfo;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.domain.subsidyactivity.model.TradeInQualifyInfo;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityReportCityEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.GovernmentRule;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.GovernmentSubsidyPromotionConfig;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class GovernmentSubsidyAction extends AbstractAction {

    /**
     * 预售定金
     */
    public static final String TAILORDER = "tailorder";
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 优惠类型
     */
    private PromotionToolType promotionType;
    /**
     * 折扣打的折扣，9折就是90
     */
    private long reduceDiscount = 0;
    /**
     * 折扣最高可以减免的钱
     */
    private long maxReducePrice = 0;
    /**
     * sku-品类
     */
    private Map<String, String> goodsSpuGroupMap;
    /**
     * sku-深圳政府模式上报数据
     */
    private Map<String, GovernmentRule> governmentRuleMap;
    /**
     * 能效等级
     */
    private String cateLevel;
    /**
     * 国补模式：1-政府模式 2-银联模式 3-极简模式
     */
    private Integer subsidyMode;
    /**
     * 上报城市ID
     */
    private Integer reportCity;
    /**
     * 上报城市标签(xx以旧换新)
     */
    private String reportTag;
    /**
     * 开票规则： 1-国补金额优惠开票，2-国补金额优惠不开票
     */
    private Integer invoiceRule;
    /**
     * 开票主体：(深圳小米景明)
     */
    private Integer invoiceCompanyId;

    /**
     * @description: 分转换成元
     */
    private static String getMoneyF2Y(Long money) {
        BigDecimal bigDecimalFen = new BigDecimal(money);
        return bigDecimalFen.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString();
    }

    @Override
    public void execute(ActivityTool promotion, CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (MapUtil.isEmpty(goodsSpuGroupMap)) {
            log.warn("goodsSpuGroupMap is empty. actId:{} uid:{}", promotion, request.getUserId());
            return;
        }
        List<CartItem> cartItemList = request.getSourceApi() == SourceApi.SUBMIT ? context.getCarts() : request.getCartList();
        // 获取此次参加活动的商品
        List<GoodsIndex> indexList = context.getGoodIndex();
        Map<String, Long> map = new HashMap<>();
        CartItem cartItem = null;
        List<CartItem> cartItems = new ArrayList<>();
        Boolean isBind = context.getIsBind();
        if (!isBind) {
            //只有一品一类一件
            cartItem = cartItemList.stream().filter(item -> item.getItemId().equals(indexList.getFirst().getItemId())).toList().getFirst();
            // 算优惠价
            // 返回是否有可参与的itemId及对应优惠金额    Map<String, Long> map===>只有一个
            map = changePriceAndGetJoinedItemIdList(cartItem, request.getUserId());
            cartItems.add(cartItem);
        } else {
            //强绑定商品有两件
            List<String> dxItems = indexList.stream().map(GoodsIndex::getItemId).toList();
            cartItems = cartItemList.stream().filter(item -> dxItems.contains(item.getItemId())).toList();
            map = bindChangePriceAndGetJoinedItemIdList(cartItems);
            //获取主品cartItem
            //cartItem = cartItems.stream().filter(cart -> StrUtil.isEmpty(cart.getParentItemId())).toList().getFirst();
            //获取活动池中的cartItem
            List<CartItem> list = cartItems.stream().filter(cart -> StrUtil.isNotEmpty(goodsSpuGroupMap.get(CartHelper.getSkuPackage(cart)))).toList();
            cartItem = list.getFirst();
        }
        if (CollUtil.isEmpty(map)) {
            return;
        }
        List<String> items = new ArrayList<>(map.keySet());
        String skuPackage = CartHelper.getSkuPackage(cartItem);
        String cateCode = goodsSpuGroupMap.get(skuPackage);
        Long reduceMoney = map.get(cartItem.getItemId());
        // 如果有构建构造promotionInfo
        //优惠信息封装
        PromotionInfo promotionInfo = new PromotionInfo();
        promotionInfo.setPromotionId(promotionId.toString());
        promotionInfo.setType(String.valueOf(promotionType.getTypeId()));
        GovernmentActivityDto government = getGovernmentActivityDto(reduceMoney, cartItems.stream().map(CartItem::getItemId).toList(), cateCode, request.getOrgCode(), context.getUsedQualifyMap());
        promotionInfo.setExtend(GsonUtil.toJson(government));
        promotionInfo.setJoinedItemId(items);
        context.setPromotion(promotionInfo);
        context.setExpress(new Express());
        // 初始化资源
        if (request.getSourceApi() == SourceApi.SUBMIT && request.getUsePurchaseSubsidy()) {
            if (reportCity.equals(ActivityReportCityEnum.BJ_REPORT_CITY.getId())) {
                //构建北京三方优惠资格码条件
                List<TradeInQualifyInfo> tradeInQualifyInfoList = initLimitResource(indexList.stream().map(GoodsIndex::getItemId).toList(), cartItemList, context.getUsedQualifyMap());
                log.info("GovernmentSubsidyAction tradeInQualifyInfoList:{}", tradeInQualifyInfoList);
                initializePurchaseSubsidyLimit(request, promotionId, context, tradeInQualifyInfoList);
            } else {
                //构造 orderInfo
                OrderInfoReq req = getOrderInfo(request, cartItem, skuPackage, reduceMoney);
                log.info("GovernmentSubsidyAction orderInfo:{}", req);
                initializeGovernmentSubsidyLimit(request, promotionId, context, cateCode, reportCity, GsonUtil.toJson(req));
            }
        }
        context.setJoinActivityType(Lists.newArrayList(promotionType.getTypeId()));
    }

    private OrderInfoReq getOrderInfo(CheckoutPromotionRequest request, CartItem cartItem, String skuPackage, Long reduceMoney) {
        OrderInfoReq orderInfo = new OrderInfoReq();
        orderInfo.setGrade(cateLevel);
        orderInfo.setSales(getMoneyF2Y(cartItem.getCartPrice()));
        orderInfo.setSubsidy(getMoneyF2Y(reduceMoney));
        orderInfo.setGoodsId(skuPackage);
        orderInfo.setGoodsName(cartItem.getGoodsName());
        orderInfo.setProvince(request.getRegion().getProvince());
        orderInfo.setCity(request.getRegion().getCity());
        orderInfo.setDistrict(request.getRegion().getDistrict());
        orderInfo.setAreaId(request.getRegion().getArea());
        orderInfo.setAddress(request.getRegion().getAddress());
        if (reportCity.equals(ActivityReportCityEnum.SZ_REPORT_CITY.getId())) {
            GovernmentRule rule = governmentRuleMap.get(skuPackage);
            if (Objects.nonNull(rule)) {
                orderInfo.setBrand(rule.getBrand());
                orderInfo.setItemName(rule.getItemName());
                orderInfo.setSpecModel(rule.getSpecModel());
                orderInfo.setBarcode(rule.getBarcode());
            }
        }
        return orderInfo;
    }

    private GovernmentActivityDto getGovernmentActivityDto(Long reduceMoney, List<String> itemIds, String cateCode, String orgCode, Map<String, TradeQualificationInfo> qualificationInfoMap) {
        GovernmentActivityDto government = new GovernmentActivityDto();
        government.setReduceMoney(reduceMoney);
        // CateCode只赋值可用商品的id和品类，目前只包含一个元素，不是返回全量的goodsSpuGroupMap
        Map<String, QualifyDetailInfo> qualifyDetailInfoMap = new HashMap<>();
        QualifyDetailInfo qualifyDetailInfo = new QualifyDetailInfo();
        qualifyDetailInfo.setQualificationType(cateCode);
        if (reportCity.equals(ActivityReportCityEnum.BJ_REPORT_CITY.getId())) {
            TradeQualificationInfo tradeQualificationInfo = qualificationInfoMap.get(cateCode);
            qualifyDetailInfo.setQualificationCode(tradeQualificationInfo.getQualificationCode());
            qualifyDetailInfo.setQualificationSeq(tradeQualificationInfo.getQualificationSeq());
        }
        for (String itemId : itemIds) {
            qualifyDetailInfoMap.put(itemId, qualifyDetailInfo);
        }
        government.setQualifyDetailInfoMap(qualifyDetailInfoMap);
        government.setCateLevel(cateLevel);
        government.setSubsidyMode(subsidyMode);
        government.setReportCity(reportCity);
        government.setReportTag(reportTag);
        government.setInvoiceRule(invoiceRule);
        //门店不返回开票主体
        if (StrUtil.isEmpty(orgCode)) {
            government.setInvoiceCompanyId(invoiceCompanyId);
        }
        return government;
    }

    private Map<String, Long> changePriceAndGetJoinedItemIdList(CartItem cartItem, Long userId) {
        Map<String, Long> map = new HashMap<>();
        // 当前优惠后的价格
        long curPrice = cartItem.getCartPrice() * cartItem.getCount() - cartItem.getReduceAmount();
        long reducePrice = 0;
        if (curPrice >= 0) {
            // 定金预售处理 折扣后的价格
            if (cartItem.getSaleSource().equals(TAILORDER)) {
                //对于定金预售：基准价=定金+尾款
                reducePrice = calculateReduceMoney(curPrice + cartItem.getPrePrice() * cartItem.getCount(), reduceDiscount, maxReducePrice);
            } else {
                reducePrice = calculateReduceMoney(curPrice, reduceDiscount, maxReducePrice);
            }
            if (reducePrice == 0) {
                return map;
            }
            //优惠金额>当前金额，过滤
            if (reducePrice >= curPrice) {
                return map;
            }
        } else {
            log.warn("promotion price error. actId:{} uid:{} curPrice:{}", promotionId, userId, curPrice);
        }
        map.put(cartItem.getItemId(), reducePrice);
        return map;
    }

    private List<TradeInQualifyInfo> initLimitResource(List<String> satisfyItemIdList, List<CartItem> cartList, Map<String, TradeQualificationInfo> qualificationInfoMap) {
        List<TradeInQualifyInfo> tradeInQualifyInfoList = new ArrayList<>();
        Map<String, CartItem> cartItemMap = cartList.stream().collect(Collectors.toMap(CartItem::getItemId, p -> p, (p1, p2) -> p1));
        for (String itemId : satisfyItemIdList) {
            CartItem cartItem = cartItemMap.get(itemId);
            if (cartItem == null) {
                continue;
            }
            String skuPackage = CartHelper.getSkuPackage(cartItem);
            String spuGroup = goodsSpuGroupMap.get(skuPackage);
            if (StringUtils.isEmpty(spuGroup)) {
                continue;
            }
            TradeQualificationInfo qualificationInfo = qualificationInfoMap.get(spuGroup);
            if (qualificationInfo != null) {
                TradeInQualifyInfo tradeInQualifyInfo = new TradeInQualifyInfo();
                tradeInQualifyInfo.setSku(skuPackage);
                tradeInQualifyInfo.setItemId(itemId);
                tradeInQualifyInfo.setQualifyDetailInfo(qualificationInfo);
                tradeInQualifyInfoList.add(tradeInQualifyInfo);
            }
        }
        return tradeInQualifyInfoList;
    }

    private Map<String, Long> bindChangePriceAndGetJoinedItemIdList(List<CartItem> cartItems) {
        Map<String, Long> map = new HashMap<>();
        Map<String, List<CartItem>> cartItemMap = cartItems.stream().collect(Collectors.groupingBy(CartItem::getItemId));
        //拿子品
        List<CartItem> childItems = cartItems.stream().filter(cartItem -> StrUtil.isNotEmpty(cartItem.getParentItemId())).toList();
        CartItem childItem = childItems.getFirst();
        //通过子品拿主品
        List<CartItem> parentItems = cartItemMap.get(childItems.getFirst().getParentItemId());
        CartItem parentItem = parentItems.getFirst();
        // 当前优惠后的价格
        long parentCurPrice = parentItem.getCartPrice() * parentItem.getCount() - parentItem.getReduceAmount();
        long childCurPrice = childItem.getCartPrice() * childItem.getCount() - childItem.getReduceAmount();
        long totalCurPrice = parentCurPrice + childCurPrice;
        long totalReducePrice = 0;
        if (parentCurPrice >= 0 && childCurPrice >= 0) {
            if (parentItem.getSaleSource().equals(TAILORDER) && childItem.getSaleSource().equals(TAILORDER)) {
                totalReducePrice = calculateReduceMoney(totalCurPrice + parentItem.getPrePrice() * parentItem.getCount() + childItem.getPrePrice() * childItem.getCount(), reduceDiscount, maxReducePrice);
            } else {
                // 折扣后的价格
                totalReducePrice = calculateReduceMoney(totalCurPrice, reduceDiscount, maxReducePrice);
            }
        }
        if (totalReducePrice == 0) {
            return map;
        }
        //优惠金额>当前金额，过滤
        if (totalReducePrice >= totalCurPrice) {
            return map;
        }
        map.put(parentItem.getItemId(), totalReducePrice);
        map.put(childItem.getItemId(), totalReducePrice);
        return map;
    }

    /**
     * @param totalPrice     当前优惠价总价格
     * @param reduceDiscount 折扣率
     * @param maxReducePrice 最大优惠价格
     * @return 计算打折后和最大优惠价格取其小
     */
    private long calculateReduceMoney(long totalPrice, long reduceDiscount, long maxReducePrice) {
        //打折后的价格, 改为四舍五入方式
        long reducePrice = Math.round((double) totalPrice * reduceDiscount / 100);
        reducePrice = Math.min(reducePrice, totalPrice);
        //最高可以减免的钱
        if (maxReducePrice > 0) {
            reducePrice = Math.min(maxReducePrice, reducePrice);
        }
        return reducePrice;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof GovernmentSubsidyPromotionConfig promotionConfig)) {
            log.error("config is not instanceof GovernmentSubsidyPromotionConfig. config:{}", config);
            return;
        }
        this.promotionId = promotionConfig.getPromotionId();
        this.promotionType = promotionConfig.getPromotionType();
        this.reduceDiscount = promotionConfig.getReduceDiscount();
        this.maxReducePrice = promotionConfig.getMaxReducePrice();
        this.goodsSpuGroupMap = promotionConfig.getGoodsSpuGroupMap();
        this.cateLevel = promotionConfig.getCateLevel();
        this.subsidyMode = promotionConfig.getSubsidyMode();
        this.reportCity = promotionConfig.getReportCity();
        this.reportTag = promotionConfig.getReportTag();
        this.invoiceRule = promotionConfig.getInvoiceRule();
        this.invoiceCompanyId = promotionConfig.getInvoiceCompanyId();
        this.governmentRuleMap = promotionConfig.getGovernmentRuleMap();
    }
}
