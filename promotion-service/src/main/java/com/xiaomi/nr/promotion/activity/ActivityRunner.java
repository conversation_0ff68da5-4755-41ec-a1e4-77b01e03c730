package com.xiaomi.nr.promotion.activity;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xiaomi.nr.promotion.activity.pool.ActSearchParam;
import com.xiaomi.nr.promotion.activity.pool.ActivityPool;
import com.xiaomi.nr.promotion.activity.pool.CarActivitySearcher;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.enums.ProductDepartmentEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.CartItemChild;
import com.xiaomi.nr.promotion.api.dto.model.Express;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.flows.PromotionSceneFlowEngine;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.nr.promotion.util.ChannelsHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动运行器
 *
 * <AUTHOR>
 * @date 2021/3/22
 */
@Slf4j
@Component
public class ActivityRunner {

    /**
     * 活动tool 池
     */
    @Autowired
    private ActivityPool activityPool;

    @Autowired
    private PromotionSceneFlowEngine promotionSceneFlowEngine;

    @Autowired
    private CarActivitySearcher carActivitySearcher;

    /**
     * 结算方法
     * wiki: <a href="https://xiaomi.f.mioffice.cn/docs/dock4dyFNxdSEqwGERv8qLWHiud#">...</a>
     * <p>
     * 三部分：
     * <li>
     * 一. 前置处理
     * - 获取优惠工具列表， 根据orgCode 和 clientId
     * - sourceCode校验. 检查是否来源赠品或者加价购， 判断是否和对应活动类型一致
     * <li>
     * 二. 优惠工具列表迭代处理，包含条件准备，条件判断，行为执行
     * while(tools.hasNext()）{
     * 1. loop  PrepareCondition
     * 2. loop  Condition
     * 3. loop  Action
     * }
     * <li>
     * 三. 后置处理
     * - 一个只能参加一个直降活动
     * - 同类型活动互斥
     *
     * @param request         结算入参
     * @param response        结算结果
     * @param checkoutContext 结算过程上下文
     * @throws BizError 错误数据
     */
    public void checkout(CheckoutPromotionRequest request, CheckoutPromotionResponse response, CheckoutContext checkoutContext) throws Exception {
        Long uid = request.getUserId();
        long startTime = System.currentTimeMillis();

        //  获取可参与的促销活动工具. 注：返回工具默认已经排序. （如果没有可参加活动，记得删除赠品和加价购）
        List<ActivityTool> allActivityTools = getCheckoutPromotions(request, checkoutContext);
        if (CollectionUtils.isEmpty(allActivityTools)) {
            CartHelper.delGiftBargain(request.getCartList());
            log.info("actList is empty. uid:{}, orgCode:{} clientId:{}", uid, request.getOrgCode(), request.getClientId());
            return;
        }

        // source和sourcecode 校验. 校验赠品、加价购 对应的活动类型. 可能会修改购物车(赠品加价购sourcecode不在可参加的活动中，删除)
        List<CartItem> cartList = request.getCartList();
        checkSourceAndSourceCode(cartList, allActivityTools);

        // 再次校验 cartList 是否为空
        if (CollectionUtils.isEmpty(cartList)) {
            log.error("post checkSourceAndSourceCode, cart is empty. uid:{}", uid);
            throw ExceptionHelper.create(ErrCode.ERR_EMPTY_CART, "购物车为空");
        }

        // 迭代处理每个活动Tool
        for (ActivityTool activityTool : allActivityTools) {


            activityTool.doCheckout(request, checkoutContext);
        }

        // 同一个sku只能参加一个直降活动，取降价最多的直降活动
        updateItemOnSalePromotion(cartList, checkoutContext, request.getChannel());
        // 处理活动互斥
        processActMutex(cartList, checkoutContext);

        long endTime = System.currentTimeMillis();
        log.info("cart act checkout end. uid:{} endTime:{} ws:{}", uid, endTime, (endTime - startTime));
    }

    /**
     * 计算活动（价保）
     *
     * @param request         结算请求参数
     * @param response        结算返回值
     * @param checkoutContext 上下文
     * @throws Exception 业务异常
     */
    public void checkoutForProtectPrice(CheckoutPromotionRequest request, CheckoutPromotionResponse response, CheckoutContext checkoutContext) throws Exception {
        // 购物车中所有的sku和套装id 获取可参加活动
        List<String> skuPackageList = CartHelper.getSkuPackageList(request.getCartList());
        List<ActivityTool> activityTools = activityPool.getCurrent(null, request.getClientId(), skuPackageList);

        // 只处理直降、满减和折扣，其他活动跳过
        activityTools = activityTools.stream()
                .filter(tool -> PromotionToolType.isSupportProtectPrice(tool.getType()))
                .collect(Collectors.toList());

        // 迭代处理每个活动Tool, 处理活动
        for (ActivityTool activityTool : activityTools) {
            activityTool.doCheckout(request, checkoutContext);
        }
        // 线上不会出现同个sku对应多个直降活动
        checkoutContext.setCarts(request.getCartList());
    }

    /**
     * 购物车 source sourcecode 校验
     * 可能会修改购物车(赠品加价购sourcecode不在可参加的活动中，删除)
     *
     * @param cartList      购物车列表
     * @param activityTools 活动工具列表
     */
    public void checkSourceAndSourceCode(List<CartItem> cartList, List<ActivityTool> activityTools) throws BizError {
        if (CollectionUtils.isEmpty(cartList)) {
            return;
        }
        // 获取所有活动id字符串列表
        final Set<String> actIdSet = activityTools.stream().map(ActivityTool::getId).filter(id -> id != 0L).map(String::valueOf)
                .collect(Collectors.toSet());

        // 遍历购物车进行检查, 查找不能参加活动的购物车item
        final List<CartItem> excludedItemList = new ArrayList<>();
        for (CartItem cartItem : cartList) {
            boolean valid = checkItemSource(cartItem, actIdSet);
            if (!valid) {
                excludedItemList.add(cartItem);
            }
        }
        // 移除不能参加活动的购物车item
        cartList.removeAll(excludedItemList);
    }

    /**
     * 校验单个购物车购物车sourceCode
     *
     * @param cartItem 购物车项
     * @param actIdSet 活动Id字符串列表
     * @return true/false false的item需要从购物车中删除
     */
    private boolean checkItemSource(CartItem cartItem, Set<String> actIdSet) throws BizError {
        String source = cartItem.getSource();
        if (!SourceEnum.isGiftBargain(source)) {
            return true;
        }

        // source是赠品或者加价购活动的，如果sourcecode不符合活动列表，删除
        String sourceCode = cartItem.getSourceCode();
        if (!actIdSet.contains(sourceCode)) {
            log.info("sourcecode invalid, will delete. itemId:{} sourcecode:{}", cartItem.getItemId(), sourceCode);
            return false;
        }

        // 获取活动
        ActivityTool activityTool = activityPool.getPromotionById(Long.valueOf(sourceCode));
        if (activityTool == null) {
            log.info("get activity info failed, act is not invalid. itemId:{} sourcecode:{}", cartItem.getItemId(), sourceCode);
            return false;
        }

        int actType = activityTool.getType().getTypeId();
        // 赠品 校验逻辑，判断购物车中类型是否和活动本身类型一样
        if (SourceEnum.isGift(source) && !ActivityTypeEnum.isGiftAct(actType) && !ActivityTypeEnum.isBuyGiftAct(actType)) {
            log.warn("source is gift while act is not gift. itemId:{} actId:{}, actType:{}", cartItem.getItemId(), sourceCode, actType);
            throw ExceptionHelper.create(GeneralCodes.ParamError, String.format("赠品对应的活动%s不是赠品活动", sourceCode));
            // 加价购 校验逻辑，判断购物车中类型是否和活动本身类型一样
        } else if (SourceEnum.isBargain(source) && !ActivityTypeEnum.isBargainAct(actType)) {
            log.warn("source is bargain while act is not bargain. itemId:{} actId:{}, actType:{}", cartItem.getItemId(), sourceCode, actType);
            throw ExceptionHelper.create(GeneralCodes.ParamError, String.format("加价购对应的活动%s不是加价购活动", sourceCode));
        }
        return true;
    }

    /**
     * 同一个sku只能参加一个直降活动，取降价最多的直降活动
     *
     * @param cartList 购物车项列表
     * @param context  上下文
     */
    public void updateItemOnSalePromotion(List<CartItem> cartList, CheckoutContext context,Integer channel) {
        // 购物车获取直降条目对应的活动id key: 直降活动ID value：参加的ItemIds
        Map<String, List<String>> onsaleItemIdsMapping = Maps.newHashMap();
        for (CartItem cartItem : cartList) {
            if (!cartItem.isJoinOnsale()) {
                continue;
            }
            List<String> sourceCodeList = new ArrayList<>();
            // 主附品强绑定场景下，一个套装可能参加多个直降活动
            if (ChannelsHelper.isBindMainAccessory(channel,cartItem.getDepartment(),cartItem.getBindMainAccessory())) {
                Map<Integer, String> onSalePromotionIdMap = cartItem.getOnSalePromotionIdMap();
                List<String> promotionIdList = onSalePromotionIdMap.values().stream().distinct().collect(Collectors.toList());
                sourceCodeList.addAll(promotionIdList);
            } else {
                String sourceCode = cartItem.getSourceCode();
                sourceCodeList.add(sourceCode);
            }
            sourceCodeList = sourceCodeList.stream().distinct().collect(Collectors.toList());

            for (String sourceCode : sourceCodeList) {
                List<String> itemIds = onsaleItemIdsMapping.getOrDefault(sourceCode, Lists.newArrayList());
                itemIds.add(cartItem.getItemId());
                onsaleItemIdsMapping.put(sourceCode, itemIds);
            }

        }
        // 没有直降，不用处理
        if (MapUtils.isEmpty(onsaleItemIdsMapping)) {
            return;
        }

        List<PromotionInfo> promotionInfos = context.getPromotion();
        List<Express> express = context.getExpress();
        List<Long> finTime = context.getFinTime();

        // 过滤单个sku参加多个直降活动
        List<PromotionInfo> newPromotion = Lists.newArrayList();
        List<Express> newExpress = Lists.newArrayList();
        List<Long> newFinTime = Lists.newArrayList();
        for (int i = 0; i < promotionInfos.size(); i++) {
            PromotionInfo p = promotionInfos.get(i);
            int type = Integer.parseInt(p.getType());
            // 非直降，直接保留
            if (!Objects.equals(ActivityTypeEnum.ONSALE.getValue(), type)) {
                newPromotion.add(p);
                newExpress.add(express.get(i));
                newFinTime.add(finTime.get(i));
                continue;
            }
            List<String> itemIds = onsaleItemIdsMapping.get(p.getPromotionId());
            // 直降，则需要检查对应是否有参加了直降的品
            if (CollectionUtils.isEmpty(itemIds)) {
                continue;
            }
            p.setParentItemId(itemIds);
            p.setJoinedItemId(itemIds);
            newPromotion.add(p);
            newExpress.add(express.get(i));
            newFinTime.add(finTime.get(i));
        }
        context.setPromotion(newPromotion);
        context.setExpress(newExpress);
        context.setFinTime(newFinTime);
    }

    /**
     * 处理活动互斥
     *
     * @param cartList 购物车
     * @param context  上下文
     */
    public void processActMutex(List<CartItem> cartList, CheckoutContext context) {
        // 低优先级的活动id列表
        List<String> lowPriorityActIds = new ArrayList<>();
        List<PromotionInfo> promotionList = context.getPromotion();
        for (PromotionInfo promotion : promotionList) {
            // 活动互斥映射关系map，key为活动id，value为优先级，值越小优先级越高
            if (CollectionUtils.isEmpty(promotion.getActivityMutex())) {
                continue;
            }
            List<String> activityMutex = promotion.getActivityMutex();
            Map<String, Integer> actMutexMap = new HashMap<>();
            for (int kPriority = 0; kPriority < activityMutex.size(); kPriority++) {
                actMutexMap.put(activityMutex.get(kPriority), kPriority);
            }
            // 遍历能参与的活动(算出需要删除的已参加的活动lowPriorityActID)
            for (PromotionInfo actPromotion : promotionList) {
                Integer priority = actMutexMap.get(actPromotion.getPromotionId());
                if (priority == null) {
                    continue;
                }
                // 如果能参与的活动中有比当前活动优先级高的活动，则记录当前活动
                if (priority < actMutexMap.get(promotion.getPromotionId())) {
                    lowPriorityActIds.add(promotion.getPromotionId());
                } else if (priority > actMutexMap.get(promotion.getPromotionId())) {
                    lowPriorityActIds.add(actPromotion.getPromotionId());
                }
            }
        }
        if (CollectionUtils.isEmpty(lowPriorityActIds)) {
            return;
        }

        // 删除低优先级的活动
        List<Express> newExpress = new ArrayList<>();
        List<PromotionInfo> newPromotion = new ArrayList<>();
        List<Long> newFinTime = new ArrayList<>();
        for (int i = 0; i < promotionList.size(); i++) {
            PromotionInfo promotion = promotionList.get(i);
            // 标记是否删除， true-删除，false-不删除
            boolean delLowPriorityAct = lowPriorityActIds.stream()
                    .anyMatch(lowPriorityActId -> lowPriorityActId.equals(promotion.getPromotionId()));
            if (!delLowPriorityAct) {
                newPromotion.add(promotion);
                newExpress.add(context.getExpress().get(i));
                newFinTime.add(context.getFinTime().get(i));
            } else {
                CartHelper.delGiftBargain(cartList, Long.valueOf(promotion.getPromotionId()));
            }
        }
        context.setPromotion(newPromotion);
        context.setExpress(newExpress);
        context.setFinTime(newFinTime);
    }

    private List<ActivityTool> getCheckoutPromotions(CheckoutPromotionRequest request, CheckoutContext context) {
        // 不是渠道获取，则直接按之前获取
        if (request.getChannel() == null || request.getChannel() == 0) {
            return activityPool.getCheckoutPromotions(request);
        } else if (ChannelEnum.isThirdChannel(request.getChannel())) {
            return activityPool.getCheckoutPromotionsByChannel(request, request.getChannel());
        } else if (ChannelEnum.CAR_VEHICLE.getValue() == request.getChannel()) {
            ActSearchParam param = new ActSearchParam()
                    .setChannel(request.getChannel())
                    .setGoodsList(carActivitySearcher.createSearchGoods(request.getCartList()));
            return carActivitySearcher.searchCarActivity(param);

        }

        // 活动检索
        List<ActivityTool> toolList = Lists.newArrayList();
        for (CartItem item : request.getCartList()) {
            List<ActivityTool> activityTools = searchItemActivityTools(item, request);
            toolList.addAll(activityTools);
            // 活动ID
            List<Long> actIdList = activityTools.stream().map(ActivityTool::getId).collect(Collectors.toList());
            context.getGoodsActIdsMap().put(item.getSsuId(), Sets.newHashSet(actIdList));
        }

        // 活动去重
        List<ActivityTool> actToolList = Lists.newArrayList();
        Set<Long> actIdSet = Sets.newHashSet();
        for (ActivityTool activityTool : toolList) {
            if (!actIdSet.contains(activityTool.getId())) {
                actToolList.add(activityTool);
                actIdSet.add(activityTool.getId());
            }
        }
        // 活动排序
        actToolList.sort(Comparator.comparing(tool ->
                Optional.ofNullable(ActivityTypeEnum.getByValue(tool.getType().getTypeId()))
                        .map(ActivityTypeEnum::getWeight).orElse(0)));
        Collections.reverse(actToolList);
        return actToolList;
    }

    public List<ActivityTool> searchItemActivityTools(CartItem item, CheckoutPromotionRequest request) {
        Integer channelId = request.getChannel();
        List<String> goodsIdList = new ArrayList<>();
        goodsIdList.add(String.valueOf(item.getSsuId()));

        //兼容ssuId != sku的情况
        if (StringUtils.isNotEmpty(item.getSku())) {
            goodsIdList.add(item.getSku());
        }

        // 团购、二部、主附品强绑定，可用子品进行活动检索
        if (ChannelsHelper.isBindMainAccessory(request.getChannel(),item.getDepartment(),item.getBindMainAccessory())){
            for (CartItemChild child : item.getChilds()) {
                goodsIdList.add(child.getSku());
            }
        }

        Integer goodsType = item.getGoodsType();
        Integer department = item.getDepartment();
        // 活动检索
        List<ActivityTool> activityTools = activityPool.getCurrent(null, null, Collections.singletonList(channelId),
                goodsIdList, Collections.singletonList(item.getDepartment()));
        // 过滤场景活动
        return promotionSceneFlowEngine.filterSceneAct(activityTools, channelId, goodsType, department);
    }
}
