package com.xiaomi.nr.promotion.schedule;

import com.xiaomi.hera.trace.annotation.Trace;
import com.xiaomi.nr.promotion.activity.cache.NrPromotionConfigCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @author: zhangliwei6
 * @date: 2025/3/19 10:10
 * @description: 活动配置缓存全量刷新
 */
@Slf4j
@Component
public class ActivityConfigCacheRefreshTask {

    @Autowired
    private NrPromotionConfigCache nrPromotionConfigCache;

    /**
     * 重新构建活动缓存，延迟1分钟后每10分钟构建一次
     */
    @Trace
    @Scheduled(fixedDelay = 10000 * 60, initialDelay = 1000 * 60)
    public void rebuildActConfigCache() {
        long startTime = System.currentTimeMillis();
        try {
            nrPromotionConfigCache.rebuildCache();
            log.info("rebuildActConfigCache success cost={}", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("rebuildActConfigCache err:{}", e.getMessage());
        }
    }
}
