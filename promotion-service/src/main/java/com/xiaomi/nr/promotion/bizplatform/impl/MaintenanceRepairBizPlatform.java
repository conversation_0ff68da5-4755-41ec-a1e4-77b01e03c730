package com.xiaomi.nr.promotion.bizplatform.impl;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Response;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.bizplatform.AbstractBaseBizPlatform;
import com.xiaomi.nr.promotion.domain.coupon.impl.MaintenanceRepairCouponDomain;
import com.xiaomi.nr.promotion.domain.workhour.impl.MaintenanceRepairWorkHourDomain;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.FromInterfaceEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.DomainCheckoutContext;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MaintenanceRepairBizPlatform extends AbstractBaseBizPlatform {

    private BaseDomainList baseDomainList;

    @Override
    public BaseDomainList checkBaseDomainList() {
        return baseDomainList;
    }

    @Override
    public BizPlatformEnum getBiz() {
        return BizPlatformEnum.MAINTENANCE_REPAIR;
    }

    @Override
    public void afterPropertiesSet() {
        baseDomainList = super.instanceBaseDomainList();
        baseDomainList.addResourceList(MaintenanceRepairWorkHourDomain.class);
        baseDomainList.addResourceList(MaintenanceRepairCouponDomain.class);
    }

    @Override
    public void generateResponse(DomainCheckoutContext domainCheckoutContext) {
        FromInterfaceEnum fromInterface = domainCheckoutContext.getFromInterface();
        List<CartItem> cartList = domainCheckoutContext.getRequest().getCartList();
        CheckoutContext context = domainCheckoutContext.getContext();
        // 计算checkoutPrice
        for (CartItem cartItem : cartList) {
            if (CollectionUtils.isNotEmpty(cartItem.getReduceItemList())) {
                cartItem.setCheckoutPrice(CartHelper.goodsCurPriceByReduceItem(cartItem));
            } else {
                cartItem.setCheckoutPrice(CartHelper.goodsCurPrice(cartItem));
            }
            CartHelper.updateChildCheckoutPrice(cartItem);
        }
        if (fromInterface.equals(FromInterfaceEnum.CHECKOUT_ORDER)) {
            CheckoutPromotionResponse response = domainCheckoutContext.getResponse();
            response.setPromotions(context.getPromotion());
            response.setCartList(cartList);
        }
        if (fromInterface.equals(FromInterfaceEnum.CHECKOUT_PAGE)) {
            CheckoutPromotionV2Response responseV2 = domainCheckoutContext.getResponseV2();
            responseV2.setPromotions(context.getPromotion());
            responseV2.setCartList(cartList);
            responseV2.setCouponList(context.getCouponList());
        }
    }

    @Override
    public void addResource(DomainCheckoutContext domainCheckoutContext) throws BizError {

    }
}
