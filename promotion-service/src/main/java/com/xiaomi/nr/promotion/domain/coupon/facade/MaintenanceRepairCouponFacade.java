package com.xiaomi.nr.promotion.domain.coupon.facade;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Request;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CheckoutCoupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutContext;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponGroupInfoDO;
import com.xiaomi.nr.promotion.domain.coupon.model.RemoteCouponResponse;
import com.xiaomi.nr.promotion.domain.coupon.service.base.group.CouponGroupFactory;
import com.xiaomi.nr.promotion.domain.coupon.service.base.group.impl.MaintenanceCouponCheckoutGroup;
import com.xiaomi.nr.promotion.domain.coupon.service.common.CouponInfoService;
import com.xiaomi.nr.promotion.entity.redis.OrgInfo;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.common.BasicInfo;
import com.xiaomi.nr.promotion.resource.external.OrgInfoExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * Created by wangweiyi on 2024/3/1
 */
@Component
@Slf4j
public class MaintenanceRepairCouponFacade implements CouponFacadeInterFace {

    @Autowired
    @Qualifier("couponInfoServiceRemoteImpl")
    private CouponInfoService couponInfoService;

    @Autowired
    private CouponGroupFactory couponGroupFactory;

    /**
     * 券下单结算逻辑，按要求结算指定优惠券
     * 支持按选择的券计算分摊
     * 支持券资源核销
     *
     * @param request  业务请求
     * @param response 业务响应
     * @param context  计算上下文
     * @throws BizError 业务异常
     */
    public void checkoutForSubmit(CheckoutPromotionRequest request, CheckoutPromotionResponse response, CheckoutContext context) throws Exception {
        
        //限制结算业务
        if (context.getBizPlatform() != BizPlatformEnum.MAINTENANCE_REPAIR || Objects.isNull(request.getVid())) {
            return;
        }
        
        OrgInfo orgInfo = getOrgInfo(request, context);

        //获取券列
        //获取结算组信息
        RemoteCouponResponse remoteCouponResponse = getRemoteCouponResponse(request, orgInfo, false);

        //初始化结算组
        List<MaintenanceCouponCheckoutGroup> groupList = initCouponGroup(remoteCouponResponse);

        //初始化context
        CouponCheckoutContext couponCheckoutContext = initCouponCheckoutContext(request, context, orgInfo);

        //调用结算组完成券列表计算
        for (MaintenanceCouponCheckoutGroup maintenanceCouponCheckoutGroup : groupList) {
            //找出本组使用的券
            List<Long> selectCouponIds = maintenanceCouponCheckoutGroup.matchCouponList(request);

            //调用结算组完成指定券分摊计算
            maintenanceCouponCheckoutGroup.checkoutForSubmit(couponCheckoutContext, selectCouponIds);
        }

        //更新reduceItem



        //更新resource
        context.appendResourceHandler(couponCheckoutContext.getResourceHandlers());
    }

    private OrgInfo getOrgInfo(CheckoutPromotionRequest request, CheckoutContext context)
            throws InterruptedException, ExecutionException, TimeoutException {
        OrgInfo orgInfo = new OrgInfo();
        if (StringUtils.isNotEmpty(request.getOrgCode())) {
            OrgInfoExternalProvider externalDataProvider = (OrgInfoExternalProvider) context.getExternalDataMap().get(ResourceExtType.ORG_INFO);
            orgInfo = externalDataProvider.getFuture().get(2000, TimeUnit.MILLISECONDS);
        }
        return orgInfo;
    }

    /**
     * 券列表逻辑
     * 支持按选择的券计算分摊
     * 支持券列表展示
     * 支持自动选择最优券并使用
     *
     * @param request  业务请求
     * @param response 业务响应
     * @param context  计算上下文
     * @throws BizError 业务异常
     */
    public void checkoutForCouponList(CheckoutPromotionV2Request request, CheckoutPromotionResponse response, CheckoutContext context)
            throws Exception {
        //限制结算业务
        if (context.getBizPlatform() != BizPlatformEnum.MAINTENANCE_REPAIR || Objects.isNull(request.getVid())) {
            processEmptyCoupon(request, context);
            return;
        }

        //获取券列
        //获取结算组信息
        OrgInfo orgInfo = getOrgInfo(request, context);

        //获取券列表
        RemoteCouponResponse remoteCouponResponse = getRemoteCouponResponse(request, orgInfo, request.getGetCouponList());

        //初始化结算组
        List<MaintenanceCouponCheckoutGroup> groupList = initCouponGroup(remoteCouponResponse);

        //初始化context
        CouponCheckoutContext couponCheckoutContext = initCouponCheckoutContext(request, context, orgInfo);


        List<Coupon> couponList = new ArrayList<>();
        //TODO 最优券计算

        //调用结算组完成指定券分摊计算
        for (MaintenanceCouponCheckoutGroup maintenanceCouponCheckoutGroup : groupList) {

            //找出本组应使用的券
            List<Long> selectCouponIds = maintenanceCouponCheckoutGroup.matchCouponList(request);
            maintenanceCouponCheckoutGroup.checkoutForCouponList(couponCheckoutContext, selectCouponIds);

            couponList.addAll(maintenanceCouponCheckoutGroup.getValidCouponList());
            couponList.addAll(maintenanceCouponCheckoutGroup.getInvalidCouponList());

        }
        //合并券列表
        //外部排序逻辑

        context.setCouponList(couponList);
    }

    private RemoteCouponResponse getRemoteCouponResponse(CheckoutPromotionRequest request, OrgInfo orgInfo, boolean getCouponList)
            throws InterruptedException, ExecutionException, TimeoutException {
        ListenableFuture<RemoteCouponResponse> couponForCheckout = couponInfoService.getCouponAndGroupForCheckout(request, orgInfo.getOrgType(), getCouponList);

        return couponForCheckout.get(2000, TimeUnit.MILLISECONDS);
    }

    private CouponCheckoutContext initCouponCheckoutContext(CheckoutPromotionRequest request, CheckoutContext context, OrgInfo orgInfo) {
        CouponCheckoutContext couponCheckoutContext = new CouponCheckoutContext();
        couponCheckoutContext.setContext(context);
        couponCheckoutContext.setRequest(request);
        couponCheckoutContext.setOrgInfo(orgInfo);
        couponCheckoutContext.setResourceHandlers(new ArrayList<>());
        couponCheckoutContext.setCartItemList(copyList(request.getCartList()));
        return couponCheckoutContext;
    }

    protected List<CartItem> copyList(List<CartItem> cartList) {
        List<CartItem> simCarts = new ArrayList<>();
        for (CartItem item : cartList) {
            CartItem newItem = new CartItem();
            BeanUtils.copyProperties(item, newItem);
            simCarts.add(newItem);
        }
        return simCarts;
    }



    private List<MaintenanceCouponCheckoutGroup> initCouponGroup(RemoteCouponResponse remoteCouponResponse) {
        Map<String, List<CheckoutCoupon>> groupByNo = remoteCouponResponse.getCheckoutCouponList().stream().collect(Collectors.groupingBy(CheckoutCoupon::getCouponGroupNo));

        //初始化结算组
        List<MaintenanceCouponCheckoutGroup> groupList = new ArrayList<>();
        for (Map.Entry<String, List<CheckoutCoupon>> groupCoupon : groupByNo.entrySet()) {
            CouponGroupInfoDO couponGroupInfo = remoteCouponResponse.getCouponGroupInfoMap().get(groupCoupon.getKey());
            if (Objects.nonNull(couponGroupInfo)) {
                MaintenanceCouponCheckoutGroup couponGroup = couponGroupFactory.createCouponGroup(groupCoupon.getValue(), couponGroupInfo);
                groupList.add(couponGroup);
            }
        }
        return groupList;
    }

    protected void processEmptyCoupon(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        doProcessEmptyCoupon(request.getCartList(), request.getUserId(), request.getOrderId(), request.getClientId(), context);
    }

    private void doProcessEmptyCoupon(List<CartItem> cartItemList, Long userId, Long orderId, Long clientId, CheckoutContext context) throws BizError {
        try {
            CartHelper.checkAndStatistics(cartItemList);
        } catch (BizError biz) {
            log.error("param.CartList.CheckAndStatistics err:{}. uid:{}", biz.getMsg(), userId);
            throw ExceptionHelper.create(ErrCode.ERR_CART_CHECK, "校验统计信息错误");
        }
        BasicInfo baseInfoPtr = new BasicInfo(userId, orderId, clientId);
        List<CartItem> simpleCarts = copyList(cartItemList);

        context.setCarts(simpleCarts);
        context.setBaseInfo(baseInfoPtr);
    }


}




