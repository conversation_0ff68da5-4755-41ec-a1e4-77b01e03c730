package com.xiaomi.nr.promotion.domain.coupon.facade;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Request;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.domain.coupon.model.*;
import com.xiaomi.nr.promotion.domain.coupon.service.base.group.CouponGroupFactory;
import com.xiaomi.nr.promotion.domain.coupon.service.base.group.impl.MaintenanceCouponCheckoutGroup;
import com.xiaomi.nr.promotion.domain.coupon.service.common.CouponInfoService;
import com.xiaomi.nr.promotion.entity.redis.OrgInfo;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.common.BasicInfo;
import com.xiaomi.nr.promotion.resource.external.OrgInfoExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * Created by wangweiyi on 2024/3/1
 */
@Component
@Slf4j
public class MaintenanceRepairCouponFacade implements CouponFacadeInterFace {

    @Autowired
    @Qualifier("couponInfoServiceRemoteImpl")
    private CouponInfoService couponInfoService;

    @Autowired
    private CouponGroupFactory couponGroupFactory;

    /**
     * 券下单结算逻辑，按要求结算指定优惠券
     * 支持按选择的券计算分摊
     * 支持券资源核销
     *
     * @param request  业务请求
     * @param response 业务响应
     * @param context  计算上下文
     * @throws BizError 业务异常
     */
    public void checkoutForSubmit(CheckoutPromotionRequest request, CheckoutPromotionResponse response, CheckoutContext context) throws Exception {
        
        //限制结算业务
        if (context.getBizPlatform() != BizPlatformEnum.MAINTENANCE_REPAIR || Objects.isNull(request.getVid())) {
            return;
        }
        
        OrgInfo orgInfo = getOrgInfo(request, context);

        //获取券列
        //获取结算组信息
        RemoteCouponResponse remoteCouponResponse = getRemoteCouponResponse(request, orgInfo, false);

        //初始化context
        CouponCheckoutContext couponCheckoutContext = initCouponCheckoutContext(request, context, orgInfo);

        //初始化结算组
        List<MaintenanceCouponCheckoutGroup> groupList = initCouponGroup(remoteCouponResponse,request.getCouponIds());

        //调用结算组完成券列表计算
        for (MaintenanceCouponCheckoutGroup maintenanceCouponCheckoutGroup : groupList) {
            List<Long> selectCouponIds = maintenanceCouponCheckoutGroup.preSelectCoupon(couponCheckoutContext);
            maintenanceCouponCheckoutGroup.checkoutForSelectCoupons(couponCheckoutContext, selectCouponIds);
        }
        // 回写结果到reduceItemList
        for (int index=0;index<request.getCartList().size();index++){
            CartItem domainCartItem = couponCheckoutContext.getCartItemList().get(index);
            CartItem cartItem = request.getCartList().get(index);
            cartItem.setReduceItemList(domainCartItem.getReduceItemList());
        }
        // 更新resource
        context.appendResourceHandler(couponCheckoutContext.getResourceHandlers());
    }

    private OrgInfo getOrgInfo(CheckoutPromotionRequest request, CheckoutContext context)
            throws InterruptedException, ExecutionException, TimeoutException {
        OrgInfo orgInfo = new OrgInfo();
        if (StringUtils.isNotEmpty(request.getOrgCode())) {
            OrgInfoExternalProvider externalDataProvider = (OrgInfoExternalProvider) context.getExternalDataMap().get(ResourceExtType.ORG_INFO);
            orgInfo = externalDataProvider.getFuture().get(2000, TimeUnit.MILLISECONDS);
        }
        return orgInfo;
    }

    /**
     * 券列表逻辑
     * 支持按选择的券计算分摊
     * 支持券列表展示
     * 支持自动选择最优券并使用
     *
     * @param request  业务请求
     * @param response 业务响应
     * @param context  计算上下文
     * @throws BizError 业务异常
     */
    public void checkoutForCouponList(CheckoutPromotionV2Request request, CheckoutPromotionResponse response, CheckoutContext context)
            throws Exception {
        //限制结算业务
        if (context.getBizPlatform() != BizPlatformEnum.MAINTENANCE_REPAIR || Objects.isNull(request.getVid())) {
            processEmptyCoupon(request, context);
            return;
        }

        //获取券列
        //获取结算组信息
        OrgInfo orgInfo = getOrgInfo(request, context);

        //获取券列表
        RemoteCouponResponse remoteCouponResponse = getRemoteCouponResponse(request, orgInfo, request.getGetCouponList());

        //初始化context
        CouponCheckoutContext couponCheckoutContext = initCouponCheckoutContext(request, context, orgInfo);

        // 按照已选择券 和 未选择券分为两批优惠券组
        // 初始化已选择券结算组
        List<MaintenanceCouponCheckoutGroup> selectedCouponGroupList = initCouponGroup(remoteCouponResponse,request.getCouponIds());
        // 初始化未选择券结算组
        List<Long> notSelectedCouponIdList = remoteCouponResponse.getCheckoutCouponList().stream().map(CheckoutCoupon::getCouponId).filter(couponId -> !request.getCouponIds().contains(couponId)).toList();
        List<MaintenanceCouponCheckoutGroup> notSelectedCouponGroupList = initCouponGroup(remoteCouponResponse,notSelectedCouponIdList);

        List<Coupon> couponList = new ArrayList<>();
        try {

            // 优先计算已选择券结算组：算价和选择券列表
            for (MaintenanceCouponCheckoutGroup maintenanceCouponCheckoutGroup : selectedCouponGroupList) {
                List<Long> selectCouponIds = maintenanceCouponCheckoutGroup.preSelectCoupon(couponCheckoutContext);
                //调用结算组完成指定券分摊计算
                maintenanceCouponCheckoutGroup.checkoutForSelectCoupons(couponCheckoutContext, selectCouponIds);

                //更新券列表，一定是可用券
                couponList.addAll(couponCheckoutContext.getSelectedCouponList());
            }
            // 最后统计未选择券结算组：可用券and不可用券列表
            for (MaintenanceCouponCheckoutGroup maintenanceCouponCheckoutGroup : notSelectedCouponGroupList) {
                CouponListCheckoutResult couponListCheckoutResult = maintenanceCouponCheckoutGroup.doCheckout(maintenanceCouponCheckoutGroup.getCouponToolMap(), couponCheckoutContext);
                //更新券列表
                couponList.addAll(couponListCheckoutResult.getValidCouponList());
                couponList.addAll(couponListCheckoutResult.getInvalidCouponList());
            }
            // 回写结果到reduceItemList
            for (int index=0;index<request.getCartList().size();index++){
                CartItem domainCartItem = couponCheckoutContext.getCartItemList().get(index);
                CartItem cartItem = request.getCartList().get(index);
                cartItem.setReduceItemList(domainCartItem.getReduceItemList());
            }
        } catch (BizError bizError) {
            log.error("checkoutForCouponList bizError" , bizError);

            String couponNames = extractSelectCouponName(request.getCouponIds(), remoteCouponResponse);
            if (bizError.getCode() == ErrCode.PROMOTION_GROUP_COUPON_MUTEX.getCode()) {
                throw ExceptionHelper.create(ErrCode.PROMOTION_GROUP_COUPON_MUTEX, couponNames + "有核销规则限制，不可同时选中，请酌情选中其一以保证最佳优惠。");
            }
        } catch (Exception e) {
            log.error("checkoutForCouponList", e);
        }
        //合并券列表后排序
        context.setCouponList(couponList);
    }

    private String extractSelectCouponName(List<Long> selectCouponIds, RemoteCouponResponse remoteCouponResponse) {
        if (CollectionUtils.isEmpty(selectCouponIds) || CollectionUtils.isEmpty(remoteCouponResponse.getCheckoutCouponList())) {
            return StringUtils.EMPTY;
        }
        StringBuilder builder = new StringBuilder();
        for (CheckoutCoupon checkoutCoupon : remoteCouponResponse.getCheckoutCouponList()) {
            if (selectCouponIds.contains(checkoutCoupon.getCouponId())) {
                builder.append("优惠券：");
                builder.append(checkoutCoupon.getCouponName());
                builder.append("、");
            }
        }
        return builder.toString();
    }

    private RemoteCouponResponse getRemoteCouponResponse(CheckoutPromotionRequest request, OrgInfo orgInfo, boolean getCouponList)
            throws InterruptedException, ExecutionException, TimeoutException, BizError {
        ListenableFuture<RemoteCouponResponse> couponForCheckout = couponInfoService.getCouponAndGroupForCheckout(request, orgInfo.getOrgType(), getCouponList);
        RemoteCouponResponse remoteCouponResponse = couponForCheckout.get(2000, TimeUnit.MILLISECONDS);
        if (request.getCouponIds() != null && !request.getCouponIds().isEmpty()){
            List<Long> couponIds = request.getCouponIds();
            if (remoteCouponResponse == null){
                log.error("getRemoteCouponResponse error! remoteCouponResponse is null");
                throw ExceptionHelper.create(ErrCode.ERR_COUPON_INVALID, "无效优惠券");
            } else {
                List<CheckoutCoupon> checkoutCouponList = remoteCouponResponse.getCheckoutCouponList();
                if (checkoutCouponList == null){
                    log.error("getRemoteCouponResponse error! checkoutCouponList is null");
                    throw ExceptionHelper.create(ErrCode.ERR_COUPON_INVALID, "无效优惠券");
                }
                List<Long> remoteCouponIdList = checkoutCouponList.stream().map(CheckoutCoupon::getCouponId).toList();
                if (!new HashSet<>(remoteCouponIdList).containsAll(couponIds)){
                    log.error("getRemoteCouponResponse error! remoteCouponIdList not containsAll couponIds:{}, remoteCouponIdList:{}", couponIds, remoteCouponIdList);
                    throw ExceptionHelper.create(ErrCode.ERR_COUPON_INVALID, "无效优惠券");
                }
            }
        }
        return remoteCouponResponse;
    }

    private CouponCheckoutContext initCouponCheckoutContext(CheckoutPromotionRequest request, CheckoutContext context, OrgInfo orgInfo) {
        CouponCheckoutContext couponCheckoutContext = new CouponCheckoutContext();
        couponCheckoutContext.setOrgInfo(orgInfo);
        couponCheckoutContext.setResourceHandlers(new ArrayList<>());
        couponCheckoutContext.setCartItemList(copyList(request.getCartList()));
        couponCheckoutContext.setSelectCouponIds(request.getCouponIds());
        couponCheckoutContext.setRequest(request);
        return couponCheckoutContext;
    }

    protected List<CartItem> copyList(List<CartItem> cartList) {
        List<CartItem> simCarts = new ArrayList<>();
        for (CartItem item : cartList) {
            CartItemForCoupon newItem = new CartItemForCoupon();
            BeanUtils.copyProperties(item, newItem);
            newItem.setRemainStandardPage(item.getMaintenanceInfo().getWorkHourStandardPage());
            newItem.setRemainCount(item.getCount());
            newItem.setCartPriceBeforeCoupon(CartHelper.itemCurPrice(item.getOriginalCartPrice(), item.getReduceItemList()));
            simCarts.add(newItem);
        }
        return simCarts;
    }


    private List<MaintenanceCouponCheckoutGroup> initCouponGroup(RemoteCouponResponse remoteCouponResponse,List<Long> selectedCouponIdList) {
        Map<String, List<CheckoutCoupon>> groupByNo = remoteCouponResponse.getCheckoutCouponList().stream().collect(Collectors.groupingBy(CheckoutCoupon::getCouponGroupNo));

        //初始化结算组
        List<MaintenanceCouponCheckoutGroup> groupList = new ArrayList<>();
        for (Map.Entry<String, List<CheckoutCoupon>> groupCoupon : groupByNo.entrySet()) {
            CouponGroupInfoDO couponGroupInfo = remoteCouponResponse.getCouponGroupInfoMap().get(groupCoupon.getKey());
            if (Objects.nonNull(couponGroupInfo)) {
                List<CheckoutCoupon> filterCheckoutCouponList = groupCoupon.getValue().stream().filter(f->selectedCouponIdList.contains(f.getCouponId())).toList();
                if (filterCheckoutCouponList.isEmpty()){
                    continue;
                }
                MaintenanceCouponCheckoutGroup couponGroup = couponGroupFactory.createCouponGroup(filterCheckoutCouponList, couponGroupInfo);
                groupList.add(couponGroup);
            }
        }
        // 结算组排序：耗材券>按需保养>基础保养>漆面修复>上门补胎
        groupList.sort(Comparator.comparingInt(MaintenanceCouponCheckoutGroup::getSortNum));
        return groupList;
    }

    protected void processEmptyCoupon(CheckoutPromotionRequest request, CheckoutContext context) throws BizError {
        doProcessEmptyCoupon(request.getCartList(), request.getUserId(), request.getOrderId(), request.getClientId(), context);
    }

    private void doProcessEmptyCoupon(List<CartItem> cartItemList, Long userId, Long orderId, Long clientId, CheckoutContext context) throws BizError {
        try {
            CartHelper.checkAndStatistics(cartItemList);
        } catch (BizError biz) {
            log.error("param.CartList.CheckAndStatistics err:{}. uid:{}", biz.getMsg(), userId);
            throw ExceptionHelper.create(ErrCode.ERR_CART_CHECK, "校验统计信息错误");
        }
        BasicInfo baseInfoPtr = new BasicInfo(userId, orderId, clientId);
        List<CartItem> simpleCarts = copyList(cartItemList);

        context.setCarts(simpleCarts);
        context.setBaseInfo(baseInfoPtr);
    }


}




