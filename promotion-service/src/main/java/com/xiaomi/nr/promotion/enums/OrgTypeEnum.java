package com.xiaomi.nr.promotion.enums;

/**
 * 机构类型
 *
 * <AUTHOR>
 * @date 2021/4/1
 */
public enum OrgTypeEnum {
    /**
     * 直营店
     */
    ORG_TYPE_DIRECT(1),
    /**
     * 专卖店
     */
    ORG_TYPE_SPECIALTY(2),
    /**
     * 授权店
     */
    ORG_TYPE_AUTHORIZED(3);

    private final int orgType;

    OrgTypeEnum(int orgType) {
        this.orgType = orgType;
    }

    public int getOrgType() {
        return orgType;
    }

    public static OrgTypeEnum getOrgType(int orgType) {
        OrgTypeEnum[] values = OrgTypeEnum.values();
        for (OrgTypeEnum orgTypeEnum : values) {
            if (orgType == orgTypeEnum.orgType) {
                return orgTypeEnum;
            }
        }
        return null;
    }

    /**
     * 是否是直营店
     *
     * @param orgType 门店类型
     * @return true/false
     */
    public static boolean isDirect(int orgType) {
        return ORG_TYPE_DIRECT.getOrgType() == orgType;
    }

    /**
     * 是否是专卖店
     *
     * @param orgType 门店类型
     * @return true/false
     */
    public static boolean isSpecialty(int orgType) {
        return ORG_TYPE_SPECIALTY.getOrgType() == orgType;
    }

    /**
     * 是否是授权店
     *
     * @param orgType 门店类型
     * @return true/false
     */
    public static boolean isAuthorized(int orgType) {
        return ORG_TYPE_AUTHORIZED.getOrgType() == orgType;
    }
}
