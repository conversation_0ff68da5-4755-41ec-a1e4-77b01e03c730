package com.xiaomi.nr.promotion.componet.condition;

import com.google.gson.reflect.TypeToken;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.OrderBenefitMapper;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.OrderBenefit;
import com.xiaomi.nr.promotion.enums.ActFrequencyEnum;
import com.xiaomi.nr.promotion.error.ErrCode;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MultiPromotionConfig;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.MapUtil;
import com.xiaomi.nr.promotion.util.StringUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class FrequencyForProtectPriceCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 频次限制 1不限制 2整个活动一次 3每天一次
     */
    private ActFrequencyEnum frequency;

    @Autowired
    private OrderBenefitMapper orderBenefitMapper;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        // 线下门店不校验频次
        if (StringUtils.isNotEmpty(request.getOrgCode())) {
            return true;
        }
        // 价保
        if (!context.getIsProtectPrice()) {
            return true;
        }

        Long uid = request.getUserId();
        if (frequency == null) {
            log.error("condition is not satisfied. condition frequency is illegal. actId:{} uid:{}", promotionId, uid);
            return false;
        }
        // 没有频次限制
        if (frequency == ActFrequencyEnum.NONE) {
            return true;
        }
        // 有频次限制，用户又没有登录，认为不能参加活动
        if (uid == null || uid == 0L) {
            return false;
        }
        // 获取订单中用户参加的活动
        OrderBenefit orderBenefit = orderBenefitMapper.getOrderBenefitByUidAndOrderId(uid, context.getOrderId());
        if (orderBenefit == null) {
            log.error("order benefit is not exist. userId:{} orderId:{}", uid, context.getOrderId());
            throw ExceptionHelper.create(ErrCode.ERR_ORDER_NOT_EXIST, "订单不存在");
        }
        boolean isCheckUerJoin = true;
        // 价保活动次数校验
        if (StringUtils.isNotEmpty(orderBenefit.getJoinedActs())) {
            Map<String, PromotionInfo> joinActsMap = GsonUtil.fromMapJson(orderBenefit.getJoinedActs(), new TypeToken<HashMap<String, PromotionInfo>>(){
            }.getType());
            if (MapUtil.isNotEmpty(joinActsMap) && joinActsMap.containsKey(promotionId)) {
                isCheckUerJoin = false;
            }
        }
        if (isCheckUerJoin) {
            boolean existRecord;
            long now = Instant.now().toEpochMilli();
            if (frequency == ActFrequencyEnum.DAILY) {
                existRecord = activityRedisDao.isExistUserActDailyRecord(promotionId, uid, now);
            } else {
                existRecord = activityRedisDao.isExistUserActTotalRecord(promotionId, uid);
            }
            // 用户参与已达到次数限制
            if (existRecord) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof MultiPromotionConfig)) {
            log.error("config is not instanceof MultiPromotionConfig. config:{}", config);
            return;
        }
        MultiPromotionConfig promotionConfig = (MultiPromotionConfig) config;
        this.promotionId = promotionConfig.getPromotionId();
        this.frequency = promotionConfig.getFrequency();
    }
}
