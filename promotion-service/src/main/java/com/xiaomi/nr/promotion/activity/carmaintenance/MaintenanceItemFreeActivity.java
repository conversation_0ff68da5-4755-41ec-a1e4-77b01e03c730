package com.xiaomi.nr.promotion.activity.carmaintenance;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.activity.AbstractActivityTool;
import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.GetProductActRequest;
import com.xiaomi.nr.promotion.api.dto.model.CheckGoodsItem;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.componet.action.OnsaleAction;
import com.xiaomi.nr.promotion.componet.action.carmaintenance.MaintenanceItemFreeAction;
import com.xiaomi.nr.promotion.componet.condition.FrequencyCondition;
import com.xiaomi.nr.promotion.componet.condition.OnlineJoinLimitCondition;
import com.xiaomi.nr.promotion.componet.condition.UserGroupCondition;
import com.xiaomi.nr.promotion.componet.condition.carmaintenance.MaintenanceItemFreeCondition;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.CarIdentityTypeEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.MaintenanceItemFreePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.ReducePromotionConfig;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 满减活动工具
 *
 * <AUTHOR>
 * @date 2021/4/9
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class MaintenanceItemFreeActivity extends AbstractActivityTool implements ActivityTool {

    /**
     * 活动说明
     */
    private String description;

    /**
     * 有效商品
     */
    private List<Long> validGoods;

    @Override
    protected DSLStream getDSLDefinition() {
        return DSLEngine
                .condition(MaintenanceItemFreeCondition.class)
                .action(MaintenanceItemFreeAction.class);
    }

    @Override
    public PromotionToolType getType() {
        return PromotionToolType.MAINTENANCE_ITEM_FREE;
    }

    /**
     * 构建购物车优惠数据
     *
     * @param context 上下文
     * @return 内容
     */
    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) {
        Integer frequencyVal = frequency != null ? frequency.getValue() : null;

        PromotionInfo promotionInfo = buildDefaultPromotionInfo(context);
        promotionInfo.setFrequent(frequencyVal);
        promotionInfo.setValidGoodsList(validGoods);
        promotionInfo.setDescRule(Lists.newArrayList(description));
        return promotionInfo;
    }

    /**
     * 获取产品站信息
     *
     * @param clientId       应用ID
     * @param orgCode        门店Code
     * @param skuPackageList 活动类别
     */
    @Override
    public Map<String, ProductActInfo> getProductGoodsAct(Long clientId, String orgCode, List<String> skuPackageList) throws BizError {
        return null;
    }

    /**
     * 获取产品站活动优惠信息
     *
     * @param request   产品站信息
     * @param hierarchy 商品层级关系
     * @param isOrgTool 是否只检查门店
     * @return 优惠数据
     */
    @Override
    public PromotionInfo getProductAct(GetProductActRequest request, GoodsHierarchy hierarchy, boolean isOrgTool) throws BizError {
        return null;
    }



    @Override
    public List<CheckGoodsItem> checkProductsAct(CheckProductsActRequest request, Map<String, GoodsHierarchy> goodsHierarchyMap) throws BizError {
        return null;
    }


    @Override
    public ActivityDetail getActivityDetail() {
        return null;
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        if (!(config instanceof MaintenanceItemFreePromotionConfig)) {
            return false;
        }
        super.DSLLoad(config);
        MaintenanceItemFreePromotionConfig promotionConfig = (MaintenanceItemFreePromotionConfig) config;
        this.description = promotionConfig.getDescription();
        this.validGoods = promotionConfig.getJoinGoods().getSsuId();
        return true;
    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.MAINTENANCE_REPAIR;
    }

}
