package com.xiaomi.nr.promotion.resource.external;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.domain.activity.service.common.GoodsService;
import com.xiaomi.nr.promotion.util.CartHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.List;
import java.util.Map;

/**
 * 商品层级关系信息
 *
 * <AUTHOR>
 * @date 2021/4/28
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class GoodsHierarchyExternalProvider extends ExternalDataProvider<Map<String, GoodsHierarchy>> {
    /**
     * 商品信息数据
     */
    private ListenableFuture<Map<String, GoodsHierarchy>> future;

    @Autowired
    private GoodsService goodsService;

    @Override
    protected boolean switchOn() {
        return Boolean.TRUE;
    }

    @Override
    protected void doPrepare(CheckoutPromotionRequest request) {
        List<String> skuPkgList = CartHelper.getSkuPackageList(request.getCartList());
        future = goodsService.getHierarchyMapAsync(skuPkgList);
    }

    @Override
    protected long getTimeoutMills() {
        return DEFAULT_TIMEOUT;
    }

    @Override
    public ListenableFuture<Map<String, GoodsHierarchy>> getFuture() {
        return future;
    }

    @Override
    public ResourceExtType getResourceExtType() {
        return ResourceExtType.GOODS_HIERARCHY;
    }
}
