package com.xiaomi.nr.promotion.model.common;

import cn.hutool.core.util.NumberUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 符合条件累加器
 *
 * <AUTHOR>
 * @date 2021/4/23
 */
@Data
public class ValidGoods {
    /**
     * 符合条件的总金额
     */
    private Long validPrice = 0L;
    /**
     * 符合条件的总件数
     */
    private Long validNum = 0L;
    
    /**
     * 维保抵扣券用
     * 漆面修复券为工时的面数
     */
    private BigDecimal validCount = BigDecimal.ZERO;
    /**
     * 是否线上
     */
    @Deprecated
    private Integer isOnline = 0;
}
