package com.xiaomi.nr.promotion.model.common;

import cn.hutool.core.util.NumberUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 符合条件累加器
 *
 * <AUTHOR>
 * @date 2021/4/23
 */
@Data
public class ValidGoods {
    /**
     * 符合条件的总金额
     */
    private Long validPrice = 0L;
    /**
     * 符合条件的总件数
     */
    private Long validNum = 0L;
    
    /**
     * 维保抵扣券用  ---  改为使用validNum
     * 漆面修复券为工时的面数：实际面数和逻辑面数之间为10倍的关系
     * promotion计算时使用的时逻辑面数
     * for example：一个漆面的实际面数为1.5，传过来的逻辑面数为15，进行计算
     */
    @Deprecated
    private Integer validCount = 0;
    /**
     * 是否线上
     */
    @Deprecated
    private Integer isOnline = 0;
}
