package com.xiaomi.nr.promotion.domain.activity.service.common.impl;

import com.xiaomi.micar.club.api.model.member.MemberInfo;
import com.xiaomi.micar.club.api.resp.member.MemberInfoResp;
import com.xiaomi.nr.promotion.domain.activity.service.common.VipMemberService;
import com.xiaomi.nr.promotion.rpc.club.VipClubProxyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;

@Slf4j
@Service
public class VipMemberServiceImpl implements VipMemberService {

    @Autowired
    private VipClubProxyService vipClubProxyService;

    @Override
    public ListenableFuture<MemberInfo> getUserMemberInfoAsync(Long userId, String vid) {
        try {
            MemberInfoResp memberInfo = vipClubProxyService.getUserMemberInfo(userId, vid);
            return AsyncResult.forValue(memberInfo);
        } catch (Exception e) {
            log.error("getUserMemberInfoAsync get by code async error userId:{}", userId, e);
            return AsyncResult.forExecutionException(e);
        }
    }

    @Override
    public ListenableFuture<MemberInfo> getVidMemberInfoAsync(String vid) {
        try {
            MemberInfo memberInfo = vipClubProxyService.getVidMemberInfo(vid);
            return AsyncResult.forValue(memberInfo);
        } catch (Exception e) {
            log.error("getVidMemberInfoAsync get by code async error vid:{}", vid, e);
            return AsyncResult.forExecutionException(e);
        }
    }
}
