package com.xiaomi.nr.promotion.resource.provider;

import com.xiaomi.nr.promotion.api.dto.model.Ecard;
import com.xiaomi.nr.promotion.api.dto.model.EcardConsumeItem;
import com.xiaomi.nr.promotion.resource.ResourceProvider;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.domain.ecard.service.common.EcardInfoService;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 礼品卡资源
 *
 * <AUTHOR>
 * 2021/5/11 5:44 下午
 */
@Slf4j
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class EcardProvider implements ResourceProvider<EcardProvider.EcardContent> {

    private ResourceObject<EcardContent> resourceObject;

    @Autowired
    private EcardInfoService ecardInfoService;

    @Override
    public ResourceObject<EcardContent> getResource() {
        return resourceObject;
    }

    @Override
    public void initResource(ResourceObject<EcardContent> object) {
        this.resourceObject = object;
    }

    @Override
    public void lock() throws BizError {
        EcardContent res = resourceObject.getContent();
        ecardInfoService.consumeEcard(res);
        log.info("lock ecard od. orderId:{} userId:{}", res.getOrderId(), res.getUserId());
    }

    @Override
    public void consume() {
        log.info("consume ecard resource. {}", resourceObject);
    }

    /**
     * 查询消费日志 & 校验 按userId orderId 查询（orderId 疑问）
     * 逐条恢复礼品卡金额
     * <p>
     * 日志状态修改 & 记录回滚日志
     *
     * @throws BizError 业务异常
     */
    @Override
    public void rollback() throws BizError {
        EcardContent res = resourceObject.getContent();
        Set<Long> orderIdSet = getOrderIds(res);
        ecardInfoService.rollbackEcard(res, orderIdSet);
        ecardInfoService.sendMarioMsg(res.getUserId(), orderIdSet);

        log.info("rollback ecard ok. orderId:{} userId:{}", res.getOrderId(), res.getUserId());
    }

    @Override
    public String conflictText() {
        return "使用券失败";
    }

    @Data
    public static class EcardContent {
        /**
         * 用户ID
         */
        private Long userId;
        /**
         * 订单ID
         */
        private Long orderId;
        /**
         * 应用id
         */
        private Long clientId;
        /**
         * 礼品卡
         */
        List<Ecard> ecardList;
        /**
         * 0 线下使用
         */
        private Integer offline;
    }

    private Set<Long> getOrderIds(EcardContent ecardContent) {
        Set<Long> sOrderIdSet = new HashSet<>();
        List<Ecard> ecardList = ecardContent.getEcardList();
        if (CollectionUtils.isEmpty(ecardList)) {
            log.warn("ecard content ecardList is empty. content:{}", GsonUtil.toJson(ecardContent));
            return sOrderIdSet;
        }

        // 获取所有小单号（注：加orderId 是当不拆单时候就会用orderId)
        ecardList.forEach(ecard -> {
            List<EcardConsumeItem> consumeItemList = ecard.getConsumeList();
            if (CollectionUtils.isEmpty(consumeItemList)) {
                return;
            }
            consumeItemList.stream().filter(Objects::nonNull).forEach(consumeItem -> sOrderIdSet.add(consumeItem.getSOrderId()));
        });

        // 避免小单关单情况
        if (CollectionUtils.isEmpty(sOrderIdSet)) {
            sOrderIdSet.add(ecardContent.getOrderId());
        }
        return sOrderIdSet;
    }
}
