package com.xiaomi.nr.promotion.activity.pool;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * Created by wa<PERSON><PERSON><PERSON> on 2023/8/9
 * 搜索的条件
 */
@Data
@Accessors(chain = true)
public class ActSearchParam {

    private Integer channel;

    private Integer clientId;

    private String orgCode;

    private Integer department;

    private List<GoodsInSearch> goodsList;


    @Data
    public static class GoodsInSearch {

        private String sku;

        private Long ssuId;

        private String pkgId;

        private Integer ssuType;
    }


}
