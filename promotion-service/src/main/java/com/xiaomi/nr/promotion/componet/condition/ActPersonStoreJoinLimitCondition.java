package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.entity.redis.ActNumLimitRule;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.PartOnsalePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.CartHelper;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 活动每人每店限购条件判断
 *
 * <AUTHOR>
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class ActPersonStoreJoinLimitCondition extends Condition {
    /**
     * 活动ID
     */
    private Long promotionId;
    /**
     * 活动类型
     */
    private PromotionToolType promotionType;
    /**
     * 直降信息
     */
    private Map<String, ActPriceInfo> onsaleInfoMap;
    /**
     * 是否限制商品数量（活动维度）
     */
    private boolean numLimit;
    /**
     * 活动限制规则
     */
    private ActNumLimitRule numLimitRule;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        Long uid = request.getUserId();
        // 不限购不校验
        if (!numLimit) {
            return true;
        }
        List<CartItem> cartList = request.getCartList();
        // 购物车中商品符合的数量
        int fillGoodsCount = getFillGoodsCount(cartList);
        // 购物车中没有符合的商品数量，则返回
        if (fillGoodsCount == 0) {
            return false;
        }
        // 满足的数量
        long fillTimes = fillGoodsCount;
        // 校验每人每店参与次数, 用户id（mid、手机号）的次数
        if (numLimitRule.getPersonLimitOne() != 0L) {
            Long remain = getPersonLimitRemain(request, numLimitRule.getPersonLimitOne());
            if (remain < 1L) {
                log.info("condition is not satisfied. act person limit one remain count <1. actId:{},uid:{},personLimitOne:{},remain:{}", promotionId, uid, numLimitRule.getPersonLimitOne(), remain);
                return false;
            }
            context.setRemainPersonStoreNum(remain);
            fillTimes = Math.min(fillGoodsCount, remain);
        }
        context.setFillTimes((int)fillTimes);
        return true;
    }

    /**
     * 获取购物车中满足的商品数量
     *
     * @param cartList 购物车列表
     * @return 数量
     */
    private int getFillGoodsCount(List<CartItem> cartList) {
        int fillGoodsCount = 0;
        for (CartItem cartItem : cartList) {
            int activityType = promotionType.getTypeId();
            boolean itemQualify = CartHelper.checkItemActQualifyCommon(cartItem, activityType);
            if (!itemQualify) {
                continue;
            }
            // sku/packageId
            String skuPackageId = CartHelper.getSkuPackage(cartItem);
            if (onsaleInfoMap.containsKey(skuPackageId)) {
                fillGoodsCount += cartItem.getCount();
            }
        }
        return fillGoodsCount;
    }

    /**
     * 获取人店剩余活动数量
     *
     * @param request 结算请求参数
     * @param limit 人店活动限制数
     * @return 剩余活动数量
     */
    private Long getPersonLimitRemain(CheckoutPromotionRequest request, Long limit) {
        Integer numJoin;
        if (StringUtils.isNotBlank(request.getUidType())) {
            numJoin = activityRedisDao.getActUtypePersonStoreLimitNum(promotionId, request.getUserId(), request.getOrgCode());
        } else {
            numJoin = activityRedisDao.getActPersonStoreLimitNum(promotionId, request.getUserId(), request.getOrgCode());
        }
        return limit - numJoin;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof PartOnsalePromotionConfig)) {
            log.error("config is not instanceof PartOnsalePromotionConfig. config:{}", config);
            return;
        }
        PartOnsalePromotionConfig partOnsalePromotionConfig = (PartOnsalePromotionConfig) config;
        this.promotionId = partOnsalePromotionConfig.getPromotionId();
        this.onsaleInfoMap = partOnsalePromotionConfig.getOnsaleInfoMap();
        this.numLimitRule = partOnsalePromotionConfig.getNumLimitRule();
        this.numLimit = partOnsalePromotionConfig.isNumLimit();
        this.promotionType = partOnsalePromotionConfig.getPromotionType();
    }
}
