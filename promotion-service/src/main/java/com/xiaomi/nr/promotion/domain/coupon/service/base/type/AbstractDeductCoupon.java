package com.xiaomi.nr.promotion.domain.coupon.service.base.type;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.Coupon;
import com.xiaomi.nr.promotion.domain.coupon.model.CouponCheckoutResult;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.ValidGoods;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import com.xiaomi.nr.promotion.domain.coupon.service.base.condition.CouponCondition;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/3/2
 */
@Slf4j
public abstract class AbstractDeductCoupon extends AbstractCouponTool {

    /**
     * 抵扣规则列表
     */
    protected List<CouponCondition> conditionList = Lists.newArrayList();
    
    @Override
    public Long getCouponId() {
        return id;
    }
    
    @Override
    public CouponTypeEnum getCouponType() {
        return CouponTypeEnum.DEDUCT;
    }
    
    @Override
    public CouponCheckoutResult checkoutCoupon(CheckoutPromotionRequest request, CheckoutContext context)
            throws BizError {
        return null;
    }
    
    @Override
    public void updateCartsReduce(CheckoutPromotionRequest request, CheckoutContext context,
            CouponCheckoutResult result) {

    }

    @Override
    public Coupon generateCartCoupon() {
        return null;
    }
    
    @Override
    public PromotionToolType getType() {
        return PromotionToolType.COUPON_DEDUCT;
    }

    protected Pair<Boolean, String> isSatisfiedByCouponRule(Map<Long, ValidGoods> validGoodsMap) {
        for (CouponCondition condition : conditionList) {
            boolean satisfied = condition.isSatisfied(validGoodsMap, this.validCouponRangeGoodsList);
            if (!satisfied) {
                return Pair.of(satisfied, "订单中用券商品不满足券范围");
            }
        }
        return Pair.of(true, "");
    }
}
