package com.xiaomi.nr.promotion.activity.pool;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.SourceEnum;
import com.xiaomi.nr.promotion.util.CartHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/6/19
 */
@Component
@Slf4j
public class CarShopActivitySearcher extends AbstractActivitySearcher {

    @Autowired(required = false)
    private CarPromotionInstancePool carPromotionInstancePool;

    /**
     * 根据活动搜索参数进行索引搜索，返回符合条件的活动ID列表
     *
     * @param actSearchParam 活动搜索参数，包含商品列表和渠道信息
     * @return 符合条件的活动ID列表，如果商品列表为空则返回空列表
     */
    @Override
    public List<Long> doSearchIndex(ActSearchParam actSearchParam) {
        List<ActSearchParam.GoodsInSearch> goodsList = actSearchParam.getGoodsList();
        if (CollectionUtils.isEmpty(goodsList)) {
            return Collections.emptyList();
        }
        List<String> goodsIdList = goodsList.stream()
                .map(goods -> String.valueOf(goods.getSsuId()))
                .collect(Collectors.toList());

        // 获取skuPackage可参加活动
        Set<Long> actIdInGoods = carPromotionInstancePool.getCurrentActIds(goodsIdList);


        List<Long> actIdInChannel = carPromotionInstancePool.getCurrentChannelActIds(Lists.newArrayList(actSearchParam.getChannel()));

        // 做交集
        actIdInGoods.retainAll(actIdInChannel);

        // 获取有序活动列表
        List<Long> sortedActIds = carPromotionInstancePool.getSortedActIds(new ArrayList<>(actIdInGoods));
        return new ArrayList<>(sortedActIds);
    }

    @Override
    public List<ActivityTool> doSearchAct(List<Long> actIds) {
        return carPromotionInstancePool.getCurrentTools(actIds);
    }

    @Override
    public ActivityTool doSearchAct(Long actId) {
        return carPromotionInstancePool.getCurrentTool(actId);
    }

    @Override
    public List<ActivityTool> doFilter(ActSearchParam actSearchParam, List<ActivityTool> activityTools) {
        return activityTools.stream()
                .filter(tool -> checkTime(tool, Boolean.FALSE))
                .filter(activityTool -> activityTool.getBizPlatform() == getBizPlatform())
                .collect(Collectors.toList());
    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.CAR_SHOP;
    }

    /**
     * 根据购物车列表创建搜索商品列表
     *
     * @param cartList 购物车列表
     * @return 搜索商品列表
     */
    public List<ActSearchParam.GoodsInSearch> createSearchGoods(List<CartItem> cartList) {
        List<ActSearchParam.GoodsInSearch> resultList = new ArrayList<>();

        for (CartItem item : cartList) {
            if (SourceEnum.isGiftBargain(item.getSource())) {
                continue;
            }
            ActSearchParam.GoodsInSearch goodsInSearch = new ActSearchParam.GoodsInSearch();
            goodsInSearch.setSsuId(item.getSsuId());
            goodsInSearch.setSsuType(item.getSsuType());
            resultList.add(goodsInSearch);
        }
        return resultList;
    }
}
