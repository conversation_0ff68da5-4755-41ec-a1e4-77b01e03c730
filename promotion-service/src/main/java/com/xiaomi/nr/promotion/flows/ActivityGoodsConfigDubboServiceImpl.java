package com.xiaomi.nr.promotion.flows;


import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.phoenix.api.dto.request.subsidyqualify.CateCodeRegionData;
import com.xiaomi.nr.phoenix.api.dto.request.subsidyqualify.QuerySubsidyQualificationForTradeReq;
import com.xiaomi.nr.phoenix.api.dto.request.subsidyqualify.SubsidyQualificationReq;
import com.xiaomi.nr.phoenix.api.dto.request.subsidyqualify.SubsidyQualificationRequest;
import com.xiaomi.nr.phoenix.api.dto.response.QualificationSubsidyResponse;
import com.xiaomi.nr.phoenix.api.dto.response.guangdongSubsudy.QuerySubsidyQualificationStatusResp;
import com.xiaomi.nr.phoenix.api.dto.response.qualifyCode.QualificationPaymentModeExtendInfoResp;
import com.xiaomi.nr.phoenix.api.enums.SubsidyPaymentModeEnum;
import com.xiaomi.nr.phoenix.api.service.SubsidyQualificationDubboService;
import com.xiaomi.nr.promotion.activity.pool.InstallmentActivityPool;
import com.xiaomi.nr.promotion.activity.pool.InstallmentGiftGoodsPool;
import com.xiaomi.nr.promotion.activity.pool.PurchaseActivityPool;
import com.xiaomi.nr.promotion.annotation.log.Log;
import com.xiaomi.nr.promotion.api.dto.GetGoodsSubsidyActRequest;
import com.xiaomi.nr.promotion.api.dto.GetGoodsSubsidyActResponse;
import com.xiaomi.nr.promotion.api.dto.GetGovernmentSubsidyRequest;
import com.xiaomi.nr.promotion.api.dto.GetQualificationSubsidyExtInfoResp;
import com.xiaomi.nr.promotion.api.dto.GetValidInstallmentRequest;
import com.xiaomi.nr.promotion.api.dto.GetValidInstallmentResponse;
import com.xiaomi.nr.promotion.api.dto.GovernmentSubsidyActResponse;
import com.xiaomi.nr.promotion.api.dto.InstallmentConditionCheckRequest;
import com.xiaomi.nr.promotion.api.dto.InstallmentConditionCheckResponse;
import com.xiaomi.nr.promotion.api.dto.QueryProductLayerSubsidyQualificationReq;
import com.xiaomi.nr.promotion.api.dto.QueryProductLayerSubsidyQualificationResp;
import com.xiaomi.nr.promotion.api.dto.enums.AliPayGovEnergyLevel;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.enums.FetchTypeEnum;
import com.xiaomi.nr.promotion.api.dto.enums.InstallmentTypeEnum;
import com.xiaomi.nr.promotion.api.dto.enums.PaymentAccessEnum;
import com.xiaomi.nr.promotion.api.dto.enums.SkuCanJoinEnum;
import com.xiaomi.nr.promotion.api.dto.enums.SubsidyActivityCodeEnum;
import com.xiaomi.nr.promotion.api.dto.enums.SubsidyModeEnum;
import com.xiaomi.nr.promotion.api.dto.model.GovActInfo;
import com.xiaomi.nr.promotion.api.dto.model.InstallmentDetail;
import com.xiaomi.nr.promotion.api.dto.model.InstallmentGoodItem;
import com.xiaomi.nr.promotion.api.dto.model.PromotionIndexDto;
import com.xiaomi.nr.promotion.api.dto.model.PurchaseActivityInfo;
import com.xiaomi.nr.promotion.api.dto.model.QualificationInfo;
import com.xiaomi.nr.promotion.api.dto.model.SubsidyActivityContext;
import com.xiaomi.nr.promotion.api.dto.model.carshop.Region;
import com.xiaomi.nr.promotion.api.service.ActivityGoodsConfigDubboService;
import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.dao.mysql.mdpromotion.TInstallmentGiftGoodsPo;
import com.xiaomi.nr.promotion.domain.activity.service.common.ProductActivityService;
import com.xiaomi.nr.promotion.rpc.phoenix.SubsidyPhoenixProxy;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.util.RegionUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.annotation.Service;
import org.apache.logging.log4j.util.Strings;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service(timeout = 1000, group = "${dubbo.group}", version = "1.0", retries = 0)
@ApiModule(value = "活动商品配置服务", apiInterface = ActivityGoodsConfigDubboService.class)
public class ActivityGoodsConfigDubboServiceImpl implements ActivityGoodsConfigDubboService {

    @Resource
    private InstallmentGiftGoodsPool installmentGiftGoodsPool;

    @Resource
    private PurchaseActivityPool purchaseActivityPool;

    @Resource
    private InstallmentActivityPool installmentActivityPool;

    @Resource
    private SubsidyPhoenixProxy subsidyPhoenixProxy;

    @Reference(interfaceClass = SubsidyQualificationDubboService.class, version = "1.0", timeout = 2000, group = "${phoenix.dubbo.group}")
    private SubsidyQualificationDubboService subsidyQualificationDubboService;

    @Resource
    private ProductActivityService productActivityService;

    private static final List<Integer> OFFLINE_CHANNEL = Lists.newArrayList(
            // 直营店
            ChannelEnum.DIRECT.getValue(),
            // 专卖店
            ChannelEnum.SPECIALTY.getValue(),
            // 授权店
            ChannelEnum.AUTHORIZED.getValue()
    );

    /**
     * 门店的渠道列表
     */
    private final static List<Integer> INSTALLMENT_STORE_CHANNELS = Lists.newArrayList(
            ChannelEnum.DIRECT.getValue(),
            ChannelEnum.SPECIALTY.getValue(),
            ChannelEnum.AUTHORIZED.getValue()
    );


    @Resource
    private NacosConfig nacosConfig;

    /**
     * 分期免息和买赠二选一 生效商品判断
     *
     * @param productIdList 商品id列表
     * @return 其中处于生效的商品id
     */

    @Override
    @ApiDoc("分期免息和买赠二选一生效商品判断")
    public Result<List<Long>> installmentBuyGiftValidProduct(List<Long> productIdList) {

        try {
            if (CollectionUtils.isEmpty(productIdList)) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "请求参数错误，商品id不能为空");
            }
            if (productIdList.size() > 100) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "请求参数错误，商品id列表长度不能超过100");
            }
            List<Long> validProductList = new ArrayList<>();
            long currentTimestamp = System.currentTimeMillis() / 1000;
            Map<Long, TInstallmentGiftGoodsPo> tInstallmentGiftGoodsPoMap = installmentGiftGoodsPool.getCurrentValidProductMapCache();
            productIdList.forEach(e -> {
                TInstallmentGiftGoodsPo po = tInstallmentGiftGoodsPoMap.get(e);
                if (null != po && po.getBeginTime() < currentTimestamp && po.getEndTime() > currentTimestamp) {
                    validProductList.add(po.getProductId());
                }
            });
            return Result.success(validProductList);
        } catch (Exception e) {
            log.error("ActivityGoodsConfigDubboService installmentBuyGiftValidProduct error. request:{}", GsonUtil.toJson(productIdList), e);
            return Result.fromException(e);
        }
    }

    @Override
    @ApiDoc("国补以旧换新查询接口")
    @Log(name = "ActivityGoodsConfigDubboService#getGoodsSubsidyAct")
    public Result<GetGoodsSubsidyActResponse> getGoodsSubsidyAct(GetGoodsSubsidyActRequest request) {
        log.info("ActivityGoodsConfigDubboService.getGoodsSubsidyAct begin, request = {}", GsonUtil.toJson(request));
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 1、默认渠道为直营店
            request.setChannel(Optional.ofNullable(request.getChannel()).orElse(ChannelEnum.DIRECT.getValue()));

            // 2、入参校验
            getGoodsSubsidyActParamCheck(request);

            GetGoodsSubsidyActResponse response = new GetGoodsSubsidyActResponse();
            SubsidyActivityContext context = new SubsidyActivityContext();

            // 3、活动过滤
            filterActivityIds(request, context);

            response.setSkuCanJoin(context.getSkuCanJoin());
            if (!Objects.equals(context.getInvalidCode(), SubsidyActivityCodeEnum.VALID.getCode())) {
                response.setInvalidCode(context.getInvalidCode());
                return Result.success(response);
            }
            // 从活动检索流程开始进行渠道分发
            if (ChannelEnum.MISHOP.getValue() == request.getChannel()) {
                // 商城国补场景，可能查出多个活动 需要和用户资格进行匹配
                findValidActivityForMishop(request, context);
            } else {
                // 原有逻辑 线下场景使用
                findValidActivity(request, context);
            }


            if (!Objects.equals(context.getInvalidCode(), SubsidyActivityCodeEnum.VALID.getCode())) {
                response.setInvalidCode(context.getInvalidCode());
                // 不可用的活动配置的标签
                response.setActivityInvalidTag(context.getActivityInvalidTag());
                return Result.success(response);
            }

            response.setInvalidCode(SubsidyActivityCodeEnum.VALID.getCode());
            response.setActivityInfo(copyResponse(context.getActivityInfo(), request));
            response.setQualificationId(context.getQualificationId());

            log.info("ActivityGoodsConfigDubboService.getGoodsSubsidyAct finished, request = {}, response = {}, ws = {}ms",
                    GsonUtil.toJson(request), GsonUtil.toJson(response), stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return Result.success(response);
        } catch (Exception e) {
            log.error("ActivityGoodsConfigDubboService.getGoodsSubsidyAct is error, request is {}", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 查找有效的国补活动信息
     *
     * @param request 获取商品补贴活动的请求对象
     * @param context 上下文
     */
    private void findValidActivity(GetGoodsSubsidyActRequest request, SubsidyActivityContext context) {
        PurchaseActivityInfo activityInfo = null;
        // 是否有收货地址和入参收货地址为相同省的活动
        boolean hasSameProvince = false;
        // 是否有sku全匹配的活动
        boolean SkuMatch = false;
        // 不可用的活动配置的标签
        String tag = null;

        for (Long id : context.getActivityIds()) {
            PurchaseActivityInfo info = purchaseActivityPool.getActivityInfo(id);
            // 活动为空
            if (Objects.isNull(info)) {
                continue;
            }

            tag = info.getTag();

            if (!info.getSkuList().containsAll(request.getSkuList())) {
                // 活动商品不匹配
                continue;
            } else {
                SkuMatch = true;
            }

            // 判断是否有收货地址和入参收货地址为相同省的活动
            if (CollectionUtils.isNotEmpty(info.getActivityRegion()) && request.getRegion() != null && !hasSameProvince) {
                Set<Integer> provinces = info.getActivityRegion().stream().map(Region::getProvince).collect(Collectors.toSet());
                if (provinces.contains(request.getRegion().getProvince())) {
                    hasSameProvince = true;
                }
            }

            // 检查活动区域
            if (!checkRegion(request, info)) {
                continue;
            }

            // 活动时间校验
            if (!checkTime(info.getStartTime(), info.getEndTime())) {
                continue;
            }

            // 唯一活动选定
            if (Objects.isNull(activityInfo)) {
                activityInfo = copyResponse(info);
            }
        }

        // 无可用活动
        if (Objects.isNull(activityInfo)) {
            if (!SkuMatch) {
                // 不存在sku全匹配的活动，商品不匹配
                context.setInvalidCode(SubsidyActivityCodeEnum.GOODS_UNUSABLE.getCode());
            } else {
                // 该收货地址没有可参加的国补活动
                if (hasSameProvince) {
                    // 存在收货地址为当前省的活动，当前城市不可用
                    context.setInvalidCode(SubsidyActivityCodeEnum.CITY_UNUSABLE.getCode());
                } else {
                    // 不存在收货地址为当前省的活动，当前省不可用
                    context.setInvalidCode(SubsidyActivityCodeEnum.PROVINCE_UNUSABLE.getCode());
                }
            }

            context.setActivityInvalidTag(tag);
            return;
        }

        if (!checkTime(activityInfo.getStartTime(), activityInfo.getEndTime())) {
            // 活动时间校验
            context.setInvalidCode(SubsidyActivityCodeEnum.ACT_EXPIRE.getCode());
            context.setActivityInvalidTag(activityInfo.getTag());
            return;
        }

        context.setActivityInfo(activityInfo);
    }

    /**
     * 灰度开关控制，是否是极简-站内领券的模式
     *
     * @param activityInfo
     * @return
     */
    private boolean isJijianCouponSiteActivity(PurchaseActivityInfo activityInfo) {
        if (Objects.isNull(activityInfo) || Objects.isNull(activityInfo.getSubsidyMode()) || Objects.isNull(activityInfo.getFetchType())) {
            return false;
        }

        // 国补模式为极简模式且领取方式为站内领券
        return (SubsidyModeEnum.JIJIAN_MODE.getValue() == activityInfo.getSubsidyMode())
                && (FetchTypeEnum.IN_STATION_FETCH.getValue() == activityInfo.getFetchType());
    }

    /**
     * 查找有效的国补活动信息 这里为了兼容发全国的逻辑可能有多个活动匹配
     *
     * @param request 获取商品补贴活动的请求对象
     * @param context 上下文
     */
    private void findValidActivityForMishop(GetGoodsSubsidyActRequest request, SubsidyActivityContext context) throws BizError {
        PurchaseActivityInfo activityInfo = null;

        List<PurchaseActivityInfo> activityInfoList = new ArrayList<>();
        for (Long id : context.getActivityIds()) {
            PurchaseActivityInfo info = purchaseActivityPool.getActivityInfo(id);
            if (null == info) {
                continue;
            }

            // 商品sku判断
            if (!new HashSet<>(info.getSkuList()).containsAll(request.getSkuList())) {
                continue;
            }

            // 检查活动区域
            if (!checkRegion(request, info)) {
                continue;
            }

            // 活动时间校验
            if (!checkTime(info.getStartTime(), info.getEndTime())) {
                continue;
            }

            activityInfoList.add(info);
        }
        if (CollectionUtils.isEmpty(activityInfoList)) {
            log.info("sku:{} 未命中活动", request.getSkuList().get(0));
            context.setInvalidCode(SubsidyActivityCodeEnum.NO_ACTIVITY_MATCH.getCode());
            return;
        }
        // 根据personalInfo获取全量资格列表
        QuerySubsidyQualificationForTradeReq qualification = QuerySubsidyQualificationForTradeReq.builder().build();
        qualification.setPersonalInfo(request.getPersonalInfo());
        List<QualificationSubsidyResponse> qualificationSubsidyList = subsidyPhoenixProxy.queryPersonalInfoListForQualification(qualification);

        for (PurchaseActivityInfo info : activityInfoList) {
            if (MapUtils.isEmpty(info.getGroupMap())) {
                log.info("活动数据有误,groupMap为空:{}", GsonUtil.toJson(info));
                throw ExceptionHelper.create(GeneralCodes.InternalError, "groupMap为空");
            }
            String cateCode = info.getGroupMap().get(request.getSkuList().getFirst());
            if (StringUtils.isBlank(cateCode)) {
                log.info("活动数据有误,cateCode为空:{}", GsonUtil.toJson(info));
                continue;
            }
            Optional<QualificationSubsidyResponse> matchfirst = qualificationSubsidyList.stream()
                    .filter(qualificationSubsidy -> info.getReportCity().equals(qualificationSubsidy.getRegionId())
                            && cateCode.equals(qualificationSubsidy.getCateCode())
                            && qualificationSubsidy.getHasQualification()).findFirst();
            if (!matchfirst.isPresent()) {
                continue;
            }
            if (info.getSubsidyMode() != SubsidyModeEnum.JIJIAN_MODE.getValue()) {
                // 有资格码，命中银商模式
                activityInfo = info;
                log.info("三方优惠返回有国补资格，有广东模式活动，命中广东模式，活动id：{}", activityInfo.getActId());
                break;
            }
            if (isJijianCouponSiteActivity(info)) {
                QualificationSubsidyResponse qualificationSubsidyResponse = matchfirst.get();
                activityInfo = info;
                activityInfo.setSubsidyMode(SubsidyModeEnum.JIJIAN_MODE.getValue());
                // 支付渠道
                activityInfo.setPaymentMode(qualificationSubsidyResponse.getPaymentMode());
                // 支付扩展信息
                GetQualificationSubsidyExtInfoResp qualificationPaymentModeExtendInfoResp
                        = buildPaymentModeExtendInfoRespByExtInfo(request.getSkuList().get(0),qualificationSubsidyResponse,info);
                if (Objects.nonNull(qualificationPaymentModeExtendInfoResp)){
                    activityInfo.setExtInfo(GsonUtil.toJson(qualificationPaymentModeExtendInfoResp));
                }
                context.setQualificationId(qualificationSubsidyResponse.getQualificationId());
                log.info("三方优惠返回有国补资格，有极简站内领券模式活动，活动id：{}", activityInfo.getActId());
            }
        }
        // 银商模式、极简站内模式，活动都为空。兜底判断是否存在极简普通模式的活动
        if (activityInfo == null) {
            for (PurchaseActivityInfo info : activityInfoList) {
                if (isJijianCouponSiteActivity(info)) {
                    // 是站内领的极简，直接过
                    continue;
                }
                // 没有资格码，有极简活动->命中极简活动
                if (info.getSubsidyMode() == SubsidyModeEnum.JIJIAN_MODE.getValue()) {
                    activityInfo = info;
                    log.info("三方优惠返回没有国补资格，有极简模式活动，命中极简模式，活动id：{}", activityInfo.getActId());
                    break;
                }
            }
        }
        if (activityInfo == null) {
            context.setInvalidCode(SubsidyActivityCodeEnum.QUALIFY_ACTIVITY_NULL.getCode());
            return;
        }

        context.setActivityInfo(activityInfo);
    }

    /**
     * 三方优惠扩展信息，映射到支付宝国补扩展信息
     *
     * @param qualificationSubsidyResponse 资格信息
     * @return
     */
    private GetQualificationSubsidyExtInfoResp buildPaymentModeExtendInfoRespByExtInfo(Long sku, QualificationSubsidyResponse qualificationSubsidyResponse, PurchaseActivityInfo info) {
        String extInfo = qualificationSubsidyResponse.getExtInfo();
        String paymentMode = qualificationSubsidyResponse.getPaymentMode();
        if (!SubsidyPaymentModeEnum.ALIPAY.getPaymentMode().equals(paymentMode)) {
            return null;
        }
        if (StringUtils.isBlank(extInfo)) {
            log.error("ActivityGoodsConfigDubboServiceImpl.buildPaymentModeExtendInfoRespByExtInfo param error, extInfo:{}", extInfo);
            return null;
        }
        try {
            QualificationPaymentModeExtendInfoResp paymentModeExtendInfoResp = GsonUtil.fromJson(extInfo, QualificationPaymentModeExtendInfoResp.class);
            QualificationPaymentModeExtendInfoResp.AliPayExtendInfoResp aliPayExtInfo = paymentModeExtendInfoResp.getAliPayExtInfo();
            if (aliPayExtInfo == null) {
                log.error("ActivityGoodsConfigDubboServiceImpl.buildPaymentModeExtendInfoRespByExtInfo param error, extInfo:{}", extInfo);
                return null;
            }
            GetQualificationSubsidyExtInfoResp.QualificationAliPayExtendInfoResp qualificationAliPayExtendInfoResp = new GetQualificationSubsidyExtInfoResp.QualificationAliPayExtendInfoResp();
            qualificationAliPayExtendInfoResp.setPolicyDiscountScene(aliPayExtInfo.getPolicyDiscountScene());
            qualificationAliPayExtendInfoResp.setQualificationTemplate(aliPayExtInfo.getQualificationTemplate());
            qualificationAliPayExtendInfoResp.setQualificationCateId(aliPayExtInfo.getQualificationCateId());
            qualificationAliPayExtendInfoResp.setQualificationVoucherId(aliPayExtInfo.getQualificationVoucherId());
            Map<Long, String> skuCateLevel = info.getSkuCateLevel();
            if (MapUtils.isNotEmpty(skuCateLevel)) {
                qualificationAliPayExtendInfoResp.setEnergyLevel(AliPayGovEnergyLevel.valueOfXmShopEnergyLevel(skuCateLevel.get(sku)).getAliPayGovEnergyLevel());
            }
            return new GetQualificationSubsidyExtInfoResp(qualificationAliPayExtendInfoResp);
        } catch (Exception e) {
            log.error("ActivityGoodsConfigDubboServiceImpl.buildPaymentModeExtendInfoRespByExtInfo param exception, extInfo:{}, e:", extInfo, e);
        }
        return null;
    }

    private Integer getQualificationRegionIdFromPhoenix(String personnalInfo, String cateCode) throws BizError {
        // 得到品类，同一商品每个活动的品类都是一样的，取第一个即可
        Result<QualificationSubsidyResponse> response = null;
        SubsidyQualificationRequest request = new SubsidyQualificationRequest(personnalInfo, cateCode);
        try {
            response = subsidyQualificationDubboService.querySubsidyQualificationForPromotion(request);
        } catch (Exception e) {
            log.error("querySubsidyQualificationForPromotion error. personalInfo:{},cateCode:{}, err", personnalInfo, cateCode, e);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "调用phoenix服务查询用户资格信息异常");
        }
        log.info("querySubsidyQualificationForPromotion finished, personalInfo:{},cateCode:{}, response = {}",
                personnalInfo, cateCode, GsonUtil.toJson(response));
        // 结果判断
        if (response.getCode() != GeneralCodes.OK.getCode()) {
            log.error("querySubsidyQualificationForPromotion fail, personalInfo:{},cateCode:{}, code = {}, message = {}",
                    personnalInfo, cateCode, response.getCode(), response.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, response.getMessage());
        }
        if (response.getData() == null) {
            log.error("querySubsidyQualificationForPromotion data is null. personalInfo:{},cateCode:{}, code = {}, message = {}",
                    personnalInfo, cateCode, response.getCode(), response.getMessage());
            throw ExceptionHelper.create(GeneralCodes.InternalError, response.getMessage());
        }
        // 拿到资格适用城市
        if (Boolean.FALSE.equals(response.getData().getHasQualification())) {
            return null;
        } else {
            return response.getData().getRegionId();
        }
    }

    /**
     * 过滤可参加的国补活动ID列表
     *
     * @param request 包含商品SKU列表、渠道和组织代码的请求对象
     * @param context 上下文
     * @return 可参加的国补活动ID列表，如果没有可参加的活动则返回空列表
     */
    private void filterActivityIds(GetGoodsSubsidyActRequest request, SubsidyActivityContext context) {
        // 3、活动过滤
        // 3.1 检查商品是否有可参加的国补活动
        List<Long> activityIds = purchaseActivityPool.getActivityIdBySku(request.getSkuList());

        log.info("filterActivityIds 商品可参加国补活动ids = {}", activityIds);

        if (CollectionUtils.isEmpty(activityIds)) {
            // 商品没有可参加的国补活动
            context.setInvalidCode(SubsidyActivityCodeEnum.GOODS_UNUSABLE.getCode());
            return;
        }


        // 3.2 检查渠道是否有可参加的国补活动
        List<Long> activityIdByChannel = purchaseActivityPool.getActivityIdByChannel(request.getChannel());
        log.info("filterActivityIds 渠道可参加国补活动ids = {}", activityIdByChannel);

        if (CollectionUtils.isEmpty(activityIdByChannel)) {
            // 渠道没有可参加的国补活动
            context.setInvalidCode(SubsidyActivityCodeEnum.ORG_UNUSABLE.getCode());
            return;
        }

        // 获取商品、渠道可参加活动 交集
        activityIds = (List<Long>) CollectionUtils.intersection(activityIds, activityIdByChannel);
        log.info("filterActivityIds 商品&渠道可参加活动交集ids = {}", activityIds);

        if (CollectionUtils.isEmpty(activityIds)) {
            // 渠道没有可参加的国补活动
            context.setInvalidCode(SubsidyActivityCodeEnum.ORG_UNUSABLE.getCode());
            return;
        }

        // 3.3 检查门店是否可参加的国补活动
        if (OFFLINE_CHANNEL.contains(request.getChannel())) {
            List<Long> activityIdByOrgCode = purchaseActivityPool.getActivityIdByOrgCode(request.getOrgCode());
            log.info("filterActivityIds 门店可参加活动ids = {}", activityIdByOrgCode);

            if (CollectionUtils.isEmpty(activityIdByOrgCode)) {
                // 门店没有可参加的国补活动
                context.setInvalidCode(SubsidyActivityCodeEnum.ORG_UNUSABLE.getCode());
                return;
            }

            // 获取商品、渠道、门店可参加活动交集
            activityIds = (List<Long>) CollectionUtils.intersection(activityIds, activityIdByOrgCode);
            log.info("filterActivityIds 商品&渠道&门店可参加活动交集ids = {}", activityIds);

            if (CollectionUtils.isEmpty(activityIds)) {
                // 门店没有可参加的国补活动
                context.setInvalidCode(SubsidyActivityCodeEnum.ORG_UNUSABLE.getCode());
                return;
            }

        }

        // sku有可参加非小U活动
        int skuCanJoin = SkuCanJoinEnum.NO_XU_ACTIVITY.getCode();
        for (Long activityId : activityIds) {
            PurchaseActivityInfo activityInfo = purchaseActivityPool.getActivityInfo(activityId);
            if (activityInfo.getPaymentAccess() != null
                    && Objects.equals(activityInfo.getPaymentAccess(), PaymentAccessEnum.XUZF.getCode())) {
                // 有可参加非小U活动
                skuCanJoin = SkuCanJoinEnum.HAVE_XU_ACTIVITY.getCode();
                break;
            }
        }
        // sku是否有可参加的国补活动
        context.setSkuCanJoin(skuCanJoin);

        context.setActivityIds(activityIds);
    }


    /**
     * 检查获取商品补贴活动的请求参数
     *
     * @param request 获取商品补贴活动的请求参数对象
     * @throws BizError 如果请求参数不符合要求，抛出业务错误异常
     */
    private static void getGoodsSubsidyActParamCheck(GetGoodsSubsidyActRequest request) throws BizError {
        // 商品不能为空
        if (CollectionUtils.isEmpty(request.getSkuList())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请求参数错误，商品id不能为空");
        }

        // 小米商城渠道，只支持一个商品
        if (Objects.equals(request.getChannel(), ChannelEnum.MISHOP.getValue()) && request.getSkuList().size() > 1) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "小米商城渠道，只支持一个商品");
        }

        // 直营、专卖店、授权店渠道门店不能为空
        if (OFFLINE_CHANNEL.contains(request.getChannel()) && StringUtils.isEmpty(request.getOrgCode())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请求参数错误，门店不能为空");
        }

        // 小米商城渠道，region不能为空
        if (Objects.equals(request.getChannel(), ChannelEnum.MISHOP.getValue()) && Objects.isNull(request.getRegion())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "小米商城渠道，收货地址不能为空");
        }
    }

    @Override
    @ApiDoc("湖北以旧换新查询接口")
    @Log(name = "ActivityGoodsConfigDubboService#getGoodsSubsidyActList")
    public Result<GetGoodsSubsidyActResponse> getGoodsSubsidyActList(GetGoodsSubsidyActRequest request) {
        log.info("ActivityGoodsConfigDubboService.getGoodsSubsidyActList begin, request = {}", GsonUtil.toJson(request));
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 1、默认渠道为直营店
            request.setChannel(Optional.ofNullable(request.getChannel()).orElse(ChannelEnum.DIRECT.getValue()));

            // 2、入参校验
            getGoodsSubsidyActListParamCheck(request);

            GetGoodsSubsidyActResponse response = new GetGoodsSubsidyActResponse();
            SubsidyActivityContext context = new SubsidyActivityContext();

            // 3、活动过滤
            filterActivityIds(request, context);
            if (!Objects.equals(context.getInvalidCode(), SubsidyActivityCodeEnum.VALID.getCode())) {
                response.setPromotionIndexMap(context.getPromotionIndexMap());
                response.setPromotionInfoMap(context.getPromotionInfoMap());
                return Result.success(response);
            }

            // 4、活动检索
            findValidActivityList(request, context);

            response.setPromotionIndexMap(context.getPromotionIndexMap());
            response.setPromotionInfoMap(context.getPromotionInfoMap());

            log.info("ActivityGoodsConfigDubboService.getGoodsSubsidyActList finished, request = {}, response = {}. ws = {}ms",
                    GsonUtil.toJson(request), GsonUtil.toJson(response), stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return Result.success(response);
        } catch (Exception e) {
            log.error("ActivityGoodsConfigDubboService.getGoodsSubsidyActList is error, request is {}", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    private void findValidActivityList(GetGoodsSubsidyActRequest request, SubsidyActivityContext context) {
        Map<Long, List<PromotionIndexDto>> skuMap = context.getPromotionIndexMap();
        Map<Long, PurchaseActivityInfo> actMap = context.getPromotionInfoMap();

        for (Long id : context.getActivityIds()) {
            PurchaseActivityInfo info = purchaseActivityPool.getActivityInfo(id);
            // 活动为空
            if (Objects.isNull(info)) {
                continue;
            }

            if (!checkTime(info.getStartTime(), info.getEndTime())) {
                // 活动时间校验
                continue;
            }

            // build sku map
            List<Long> skus = (List<Long>) CollectionUtils.intersection(request.getSkuList(), info.getSkuList());
            for (Long sku : skus) {
                List<PromotionIndexDto> actList = skuMap.getOrDefault(sku, new ArrayList<>());
                actList.add(new PromotionIndexDto(info.getActId()));
                skuMap.put(sku, actList);
            }

            // build act map
            PurchaseActivityInfo activityInfo = actMap.get(id);
            if (Objects.isNull(activityInfo)) {
                actMap.put(id, copyResponse(info));
            }
        }

        // sku map 和 act map 为空
        if (CollectionUtils.isEmpty(skuMap.keySet()) || CollectionUtils.isEmpty(actMap.keySet())) {
            context.setPromotionIndexMap(new HashMap<>());
            context.setPromotionInfoMap(new HashMap<>());
        }
    }

    /**
     * 检查获取商品补贴活动列表的请求参数
     *
     * @param request 包含请求参数的对象
     * @throws BizError 如果商品ID列表为空或门店代码为空时抛出业务错误
     */
    private static void getGoodsSubsidyActListParamCheck(GetGoodsSubsidyActRequest request) throws BizError {
        // 商品不能为空
        if (CollectionUtils.isEmpty(request.getSkuList())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请求参数错误，商品id不能为空");
        }

        // 门店不能为空
        if (StringUtils.isEmpty(request.getOrgCode())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请求参数错误，门店不能为空");
        }
    }

    /**
     * 根据条件获取有效的分期活动信息
     *
     * @param request 参数
     * @return Result
     */
    @Override
    @ApiDoc("根据条件获取有效的分期活动信息")
    @Log(name = "ActivityGoodsConfigDubboService#getValidInstallment")
    public Result<GetValidInstallmentResponse> getValidInstallment(GetValidInstallmentRequest request) {
        try {
            // 参数校验
            getInstallmentActParamCheck(request);

            GetValidInstallmentResponse response = new GetValidInstallmentResponse();

            // 如果传了门店，则判断门店是否可参与活动
            List<Long> allStoreActId = new ArrayList<>();
            if (Strings.isNotEmpty(request.getOrgCode())) {
                List<Long> actIdList = installmentActivityPool.getOrgCanJoinActId(request.getOrgCode());
                if (CollectionUtils.isEmpty(actIdList)) {
                    response.setInstallmentList(Collections.emptyList());
                    log.info("getValidInstallment, getOrgCanJoinActId is empty, request is {}", GsonUtil.toJson(request));
                    return Result.success(response);
                }
                allStoreActId.addAll(actIdList);
            }

            // 获取商品可参与活动
            List<Long> allGoodsActId = new ArrayList<>();
            for (InstallmentGoodItem item : request.getGoodsList()) {
                List<Long> actIdList = installmentActivityPool.getGoodsCanJoinActId(item.getId());
                if (CollectionUtils.isEmpty(actIdList)) {
                    log.info("getValidInstallment, getGoodsCanJoinActId is empty, goods id is {}", item.getId());
                    continue;
                }
                allGoodsActId.addAll(actIdList);
            }

            // 去重
            allGoodsActId = allGoodsActId.stream().distinct().collect(Collectors.toList());

            List<InstallmentDetail> actListResponse = new ArrayList<>();
            for (Long actId : allGoodsActId) {
                InstallmentDetail info = installmentActivityPool.getActInfo(actId);
                if (Objects.isNull(info)) {
                    log.warn("getValidInstallment, getActInfo is empty, actId：{}", actId);
                    continue;
                }

                // 分期类型校验
                if (!checkedInstallmentActType(info.getInstallmentType(), request.getInstallmentType())) {
                    continue;
                }

                // 时间校验
                if (!checkedInstallmentActTime(info.getStartTime(), info.getEndTime(), request.getStartTime(), request.getEndTime())) {
                    continue;
                }

                // 门店可参与活动的校验
                if (!checkedInstallmentOrgCanJoinAct(actId, request.getOrgCode(), allStoreActId)) {
                    continue;
                }

                // 渠道校验，只当没有传门店ID的时候才需要校验
                if (!checkedInstallmentActChannel(info.getChannels(), request.getChannelId(), request.getOrgCode())) {
                    continue;
                }

                // 金额范围校验
                if (!checkedInstallmentActAmountScope(info.getMinAmount(), info.getMaxAmount(), request.getIsCheckAmountScope(), request.getAmount())) {
                    continue;
                }

                // 匹配商品
                ImmutablePair<Boolean, List<InstallmentGoodItem>> matchGoodsRes = matchInstallmentActGoods(info.getGoodsList(), request.getIsPerfectMatchGoods(), request.getGoodsList());
                if (!matchGoodsRes.getLeft()) {
                    continue;
                }

                List<InstallmentGoodItem> matchGoods = matchGoodsRes.getRight();
                InstallmentDetail resInfo = copyActInfo(info, matchGoods);
                actListResponse.add(resInfo);
            }

            response.setInstallmentList(actListResponse);
            return Result.success(response);
        } catch (Exception e) {
            log.error("ActivityGoodsConfigDubboService.getValidInstallment is error, request is {}", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        }
    }

    /**
     * copy活动信息
     *
     * @param info       源活动信息
     * @param matchGoods 匹配到的商品
     * @return 新活动信息
     */
    private InstallmentDetail copyActInfo(InstallmentDetail info, List<InstallmentGoodItem> matchGoods) {
        InstallmentDetail resInfo = new InstallmentDetail();
        resInfo.setId(info.getId());
        resInfo.setInstallmentType(info.getInstallmentType());
        resInfo.setStatus(info.getStatus());
        resInfo.setName(info.getName());
        resInfo.setMinAmount(info.getMinAmount());
        resInfo.setMaxAmount(info.getMaxAmount());
        resInfo.setInstallmentTimes(info.getInstallmentTimes());
        resInfo.setChannels(info.getChannels());
        resInfo.setInterestPayer(info.getInterestPayer());
        resInfo.setStartTime(info.getStartTime());
        resInfo.setEndTime(info.getEndTime());
        resInfo.setGoodsList(matchGoods);
        return resInfo;
    }

    /**
     * 根据条件校验是否能参与有效分期活动
     *
     * @param request 参数
     * @return Result
     */
    @Override
    @ApiDoc("根据条件校验是否能参与有效分期活动")
    @Log(name = "ActivityGoodsConfigDubboService#installmentConditionCheck")
    public Result<InstallmentConditionCheckResponse> installmentConditionCheck(InstallmentConditionCheckRequest request) {
        try {
            // 参数校验
            checkInstallmentActParamVerify(request);

            InstallmentConditionCheckResponse response = new InstallmentConditionCheckResponse();
            response.setResult(false);
            response.setInstallmentList(Collections.emptyList());

            // 如果传了门店，则判断门店是否可参与活动
            List<Long> allStoreActId = new ArrayList<>();
            if (Strings.isNotEmpty(request.getOrgCode())) {
                List<Long> actIdList = installmentActivityPool.getOrgCanJoinActId(request.getOrgCode());
                if (CollectionUtils.isEmpty(actIdList)) {
                    response.setReason("门店未查到可参与的分期活动");
                    return Result.success(response);
                }
                allStoreActId.addAll(actIdList);
            }

            // 获取商品可参与活动
            List<Long> allGoodsActId = new ArrayList<>();
            if (Objects.nonNull(request.getActId())) {
                allGoodsActId.add(request.getActId());
            } else {
                for (InstallmentGoodItem item : request.getGoodsList()) {
                    List<Long> actIdList = installmentActivityPool.getGoodsCanJoinActId(item.getId());
                    if (CollectionUtils.isEmpty(actIdList)) {
                        continue;
                    }
                    allGoodsActId.addAll(actIdList);
                }

                // 商品没有任何可参与的活动
                if (CollectionUtils.isEmpty(allGoodsActId)) {
                    response.setReason("商品未查到可参与的分期活动");
                    return Result.success(response);
                }
            }

            // 去重
            allGoodsActId = allGoodsActId.stream().distinct().collect(Collectors.toList());

            // 是否需要金额范围校验（门店渠道需要校验）
            boolean isCheckAmountScope = StringUtils.isNotEmpty(request.getOrgCode());

            // 兜底文案
            String reason = "条件不满足分期活动要求";
            List<InstallmentDetail> actListResponse = new ArrayList<>();
            for (Long actId : allGoodsActId) {
                InstallmentDetail info = installmentActivityPool.getActInfo(actId);
                if (Objects.isNull(info)) {
                    reason = "未查到分期信息";
                    continue;
                }

                // 分期类型校验
                if (!checkedInstallmentActType(info.getInstallmentType(), request.getInstallmentType())) {
                    reason = "分期类型不匹配";
                    continue;
                }

                // 门店可参与活动的校验
                if (!checkedInstallmentOrgCanJoinAct(actId, request.getOrgCode(), allStoreActId)) {
                    reason = "该门店不可参与此分期活动";
                    continue;
                }

                // 渠道校验，只当没有传门店ID的时候才需要校验
                if (!checkedInstallmentActChannel(info.getChannels(), request.getChannelId(), request.getOrgCode())) {
                    reason = "该渠道不可参与此分期活动";
                    continue;
                }

                // 金额范围校验（门店需要校验）
                if (!checkedInstallmentActAmountScope(info.getMinAmount(), info.getMaxAmount(), isCheckAmountScope, request.getAmount())) {
                    reason = "金额不满足此分期活动配置";
                    continue;
                }

                // 分期数校验
                if (!checkedInstallmentActInstallmentTimes(info.getInstallmentTimes(), request.getInstallmentTime())) {
                    reason = "分期数不匹配";
                    continue;
                }

                // 匹配商品
                ImmutablePair<Boolean, List<InstallmentGoodItem>> matchGoodsRes = matchInstallmentActGoods(info.getGoodsList(), request.getIsPerfectMatchGoods(), request.getGoodsList());
                if (!matchGoodsRes.getLeft()) {
                    reason = "商品不可参与此分期活动";
                    continue;
                }

                List<InstallmentGoodItem> matchGoods = matchGoodsRes.getRight();
                InstallmentDetail resInfo = copyActInfo(info, matchGoods);
                actListResponse.add(resInfo);
            }

            if (CollectionUtils.isEmpty(actListResponse)) {
                // 校验指定活动的情况
                if (Objects.nonNull(request.getActId())) {
                    response.setReason(reason);
                } else {
                    response.setReason("根据条件未查到可参与的分期活动");
                }
            } else {
                response.setResult(true);
                response.setInstallmentList(actListResponse);
            }
            return Result.success(response);
        } catch (Exception e) {
            log.error("ActivityGoodsConfigDubboService.getValidInstallment is error, request is {}", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        }
    }

    /**
     * 查询产品站弹层补贴资格
     *
     * @param req 查询产品站弹层补贴资格的请求参数
     * @return 查询产品站弹层补贴资格的响应结果
     */
    @Override
    @ApiDoc("查询产品站弹层补贴资格")
    @Log(name = "ActivityGoodsConfigDubboService#queryProductLayerSubsidyQualification")
    public Result<QueryProductLayerSubsidyQualificationResp> queryProductLayerSubsidyQualification(QueryProductLayerSubsidyQualificationReq req) {
        log.info("ActivityGoodsConfigDubboService.queryProductLayerSubsidyQualification begin, req = {}", GsonUtil.toJson(req));
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            // 1、参数校验
            queryProductLayerSubsidyQualificationParamCheck(req);
            QueryProductLayerSubsidyQualificationResp resp = new QueryProductLayerSubsidyQualificationResp();

            // 2、根据商品id+region+channel查询可参加的国补活动
            GetGovernmentSubsidyRequest getGovernmentSubsidyReq = new GetGovernmentSubsidyRequest();
            getGovernmentSubsidyReq.setId(req.getId());
            getGovernmentSubsidyReq.setChannel(req.getChannel());
            getGovernmentSubsidyReq.setRegion(req.getRegion());
            GovernmentSubsidyActResponse governmentSubsidyActResp = productActivityService.queryGovernmentSubsidyAct(getGovernmentSubsidyReq);

            log.info("ActivityGoodsConfigDubboService.queryProductLayerSubsidyQualification 根据商品id+region+channel查询可参加的国补活动, getGovernmentSubsidyReq = {}, governmentSubsidyActResp = {}",
                    GsonUtil.toJson(getGovernmentSubsidyReq), GsonUtil.toJson(governmentSubsidyActResp));

            resp.setHasGovAct(governmentSubsidyActResp.getHitGovAct());
            // 命中国补活动
            if (Boolean.TRUE.equals(governmentSubsidyActResp.getHitGovAct())) {
                List<GovActInfo> allActInfos = governmentSubsidyActResp.getAllActInfos();
                if (CollectionUtils.isEmpty(allActInfos)) {
                    log.error("ActivityGoodsConfigDubboService.queryProductLayerSubsidyQualification queryGovernmentSubsidyAct error, governmentSubsidyActResp = {}", GsonUtil.toJson(resp));
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "根据商品sku、渠道、收获地址，查询可参与国补活动及国补信息异常！");
                }

                // 按照品类编码聚合，并对 value 中的对象去重
                Map<String, List<CateCodeRegionData>> cateCodeRegionMap = allActInfos.stream()
                                .collect(Collectors.groupingBy(
                                        GovActInfo::getCateCode,
                                        Collectors.mapping(actInfo -> {
                                            CateCodeRegionData cateCodeRegionData = new CateCodeRegionData();
                                            cateCodeRegionData.setRegionId(actInfo.getReportCity());
                                            cateCodeRegionData.setFetchType(actInfo.getFetchType());
                                            cateCodeRegionData.setCateCode(actInfo.getCateCode());
                                            cateCodeRegionData.setNationalAvailable(isNationalAvailable(actInfo));
                                            return cateCodeRegionData;
                                        }, Collectors.collectingAndThen(
                                                Collectors.toCollection(LinkedHashSet::new), // 使用LinkedHashSet保持顺序
                                                ArrayList::new
                                        ))
                                ));

                // 4、调用三方优惠，查询产品站用户资格
                QuerySubsidyQualificationStatusResp querySubsidyQualificationStatusResp = subsidyPhoenixProxy.queryProductLayerSubsidyQualificationForPromotion(req.getMid(), cateCodeRegionMap);

                QualificationInfo qualificationInfo = new QualificationInfo();
                qualificationInfo.setRegionId(querySubsidyQualificationStatusResp.getRegionId());
                qualificationInfo.setCateCode(querySubsidyQualificationStatusResp.getCateCode());
                qualificationInfo.setCateName(querySubsidyQualificationStatusResp.getCateName());
                qualificationInfo.setImgUrl(querySubsidyQualificationStatusResp.getImgUrl());
                qualificationInfo.setActivityCategory(querySubsidyQualificationStatusResp.getActivityCategory());
                qualificationInfo.setStatusCode(Integer.valueOf(querySubsidyQualificationStatusResp.getStatusCode()));
                qualificationInfo.setStatusDesc(querySubsidyQualificationStatusResp.getStatusDesc());
                qualificationInfo.setPaymentMode(querySubsidyQualificationStatusResp.getPaymentMode());

                // 5、根据regionId + cateCode + fetchType 获取命中的国补活动，默认取第一个
                Map<String, GovActInfo> actInfoMap = allActInfos.stream()
                        .collect(Collectors.toMap(
                                actInfo -> actInfo.getReportCity() + "_" + actInfo.getCateCode() + "_" + actInfo.getFetchType(),
                                Function.identity(),
                                (existing, replacement) -> existing
                        ));
                GovActInfo actInfo = actInfoMap.get(querySubsidyQualificationStatusResp.getRegionId() + "_" + querySubsidyQualificationStatusResp.getCateCode() + "_" + querySubsidyQualificationStatusResp.getFetchType());
                if (actInfo == null) {
                    log.error("ActivityGoodsConfigDubboService.queryProductLayerSubsidyQualification 不存在该活动！");
                    throw ExceptionHelper.create(GeneralCodes.InternalError, "活动信息异常");
                }

                resp.setGovActInfo(actInfo);
                resp.setQualificationInfo(qualificationInfo);

            }

            log.info("ActivityGoodsConfigDubboService.queryProductLayerSubsidyQualification finished, req = {}, resp = {}, ws = {}ms",
                    GsonUtil.toJson(req), GsonUtil.toJson(resp), stopwatch.elapsed(TimeUnit.MILLISECONDS));

            return Result.success(resp);
        } catch (Exception e) {
            log.error("ActivityGoodsConfigDubboService.queryProductLayerSubsidyQualification is error, req is {}", GsonUtil.toJson(req), e);
            return Result.fromException(e);
        } finally {
            stopwatch.stop();
        }
    }

    /**
     * 对所有活动信息进行排序
     *
     * @param allActInfos 活动信息列表
     */
    public void sortAllActInfos(List<GovActInfo> allActInfos) {
        allActInfos.sort((info1, info2) -> {
            // 首先判断收货地址是否全国可用
            boolean isInfo1NationalAvailable = isNationalAvailable(info1);
            boolean isInfo2NationalAvailable = isNationalAvailable(info2);

            if (isInfo1NationalAvailable != isInfo2NationalAvailable) {
                return isInfo1NationalAvailable ? -1 : 1;
            }

            // 无需领 > 站内领 > 站外领
            return compareFetchType(info1, info2);
        });
    }

    private int compareDiscount(GovActInfo info1, GovActInfo info2) {
        Long discount1 = info1.getReduceDiscount();
        Long discount2 = info2.getReduceDiscount();

        if (discount1 == null && discount2 == null) {
            return 0;
        } else if (discount1 == null) {
            return 1; // info2 在前
        } else if (discount2 == null) {
            return -1; // info1 在前
        } else {
            return Long.compare(discount2, discount1);
        }
    }

    /**
     * 检查国补活动是否全国可用
     *
     * @param info GovActInfo对象，包含活动信息
     * @return 如果活动区域白名单不为空且包含给定的活动ID，则返回true；否则返回false
     */
    private boolean isNationalAvailable(GovActInfo info) {
        return CollectionUtils.isNotEmpty(nacosConfig.getActivityAreaWhiteList()) && nacosConfig.getActivityAreaWhiteList().contains(info.getId());
    }

    /**
     * 比较两个活动国补模式，按照政府模式 > 银商模式 > 极简模式 排序
     *
     * @param info1 第一个GovActInfo对象
     * @param info2 第二个GovActInfo对象
     * @return 比较结果，正数表示info1的补贴模式排序在info2之前，负数表示info1的补贴模式排序在info2之后，零表示两者相等
     */
    private int compareSubsidyMode(GovActInfo info1, GovActInfo info2) {
        Integer mode1 = info1.getSubsidyMode();
        Integer mode2 = info2.getSubsidyMode();

        if (mode1 == null && mode2 == null) {
            return 0;
        } else if (mode1 == null) {
            return 1; // info2 在前
        } else if (mode2 == null) {
            return -1; // info1 在前
        } else {
            return Integer.compare(SubsidyModeEnum.getSortByValue(mode1), SubsidyModeEnum.getSortByValue(mode2));
        }
    }

    /**
     * 领券方式排序：无需领 > 站内领 > 站外领
     *
     * @param info1 第一个GovActInfo对象
     * @param info2 第二个GovActInfo对象
     * @return 无需领 > 站内领 > 站外领
     */
    private int compareFetchType(GovActInfo info1, GovActInfo info2) {
        return Integer.compare(FetchTypeEnum.getReorderPriorityByValue(info1.getFetchType()), FetchTypeEnum.getReorderPriorityByValue(info2.getFetchType()));
    }

    /**
     * 检查查询产品层补贴资格的参数
     *
     * @param req 查询产品层补贴资格的请求对象
     * @throws BizError 如果参数不符合要求，则抛出业务错误
     */
    private void queryProductLayerSubsidyQualificationParamCheck(QueryProductLayerSubsidyQualificationReq req) throws BizError {
        if (req == null) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "入参不能为空");
        }

        // 检查mid是否为空
        if (req.getMid() == null) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "mid不能为空");
        }
        // 检查商品id是否为空
        if (req.getId() == null) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "商品id不能为空");
        }
        // 检查收货地址是否为空
        Region region = req.getRegion();
        if (region == null || region.getCity() == null || region.getProvince() == null || region.getDistrict() == null || region.getArea() == null) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "收货地址不能为空");
        }
        // 检查渠道是否为空且是否为小米商城
        if (req.getChannel() == null || !Objects.equals(req.getChannel(), ChannelEnum.MISHOP.getValue())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "渠道不能为空，且必须为小米商城");
        }
    }

    /**
     * 匹配可参与活动的商品
     *
     * @param actGoods       活动配置的商品
     * @param isPerfectMatch 是否完全匹配商品（true:传入商品必须为配置商品的了集，false:只要有一个在活动配置商品列表中即可）
     * @param reqGoods       请求商品
     * @return bool
     */
    private ImmutablePair<Boolean, List<InstallmentGoodItem>> matchInstallmentActGoods(List<InstallmentGoodItem> actGoods, boolean isPerfectMatch, List<InstallmentGoodItem> reqGoods) {
        // map转换
        Map<String, InstallmentGoodItem> actGoodsMap = convertInstallmentGoodsMap(actGoods);
        Map<String, InstallmentGoodItem> reqGoodsMap = convertInstallmentGoodsMap(reqGoods);

        // 取交集
        List<String> actGoodsKeys = MapUtils.isNotEmpty(actGoodsMap) ? new ArrayList<>(actGoodsMap.keySet().stream().toList()) : new ArrayList<>();
        List<String> reqGoodsKeys = MapUtils.isNotEmpty(reqGoodsMap) ? reqGoodsMap.keySet().stream().toList() : new ArrayList<>();
        actGoodsKeys.retainAll(reqGoodsKeys);

        // 完全匹配商品
        if (isPerfectMatch) {
            if (actGoodsKeys.size() == reqGoodsKeys.size()) {
                return ImmutablePair.of(true, actGoods);
            }
            List<InstallmentGoodItem> matchGoods = actGoodsMap.entrySet().stream()
                    .filter(e -> actGoodsKeys.contains(e.getKey()))
                    .map(Map.Entry::getValue)
                    .collect(Collectors.toList());
            return ImmutablePair.of(false, matchGoods);
        }

        // 任何一个商品匹配即可
        if (CollectionUtils.isEmpty(actGoodsKeys)) {
            return ImmutablePair.of(false, Collections.emptyList());
        }
        List<InstallmentGoodItem> matchGoods = actGoodsMap.entrySet().stream()
                .filter(entry -> actGoodsKeys.contains(entry.getKey()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
        return ImmutablePair.of(true, matchGoods);
    }

    /**
     * 分期活动的商品转换
     *
     * @param goods 商品
     * @return Map
     */
    private Map<String, InstallmentGoodItem> convertInstallmentGoodsMap(List<InstallmentGoodItem> goods) {
        return goods.stream().collect(Collectors.toMap(item -> item.getId() + "_" + item.getType(), item -> item, (x, y) -> x));
    }

    /**
     * 分期活动的门店可参与活动校验（不传门店则不校验）
     *
     * @param actId            活动ID
     * @param reqOrgCode       请求门店ID
     * @param reqOrgCanJoinAct 请求门店可参与的活动ID
     * @return bool
     */
    private boolean checkedInstallmentOrgCanJoinAct(long actId, String reqOrgCode, List<Long> reqOrgCanJoinAct) {
        if (StringUtils.isEmpty(reqOrgCode)) {
            return true;
        }
        return CollectionUtils.isNotEmpty(reqOrgCanJoinAct) && reqOrgCanJoinAct.contains(actId);
    }

    /**
     * 分期类型校验（不传活动类型则不校验）
     *
     * @param actType    活动类型
     * @param reqActType 请求活动类型
     * @return bool
     */
    private boolean checkedInstallmentActType(Integer actType, Integer reqActType) {
        if (Objects.isNull(reqActType)) {
            return true;
        }
        return actType.equals(reqActType);
    }

    /**
     * 分期活动的分期数校验
     *
     * @param actInstallmentTimes 活动分期列表
     * @param reqInstallmentTime  请求分期数
     * @return bool
     */
    private boolean checkedInstallmentActInstallmentTimes(List<Integer> actInstallmentTimes, Integer reqInstallmentTime) {
        return Objects.nonNull(reqInstallmentTime) && actInstallmentTimes.contains(reqInstallmentTime);
    }

    /**
     * 分期活动的渠道校验（传门店则不校验渠道）
     *
     * @param actChannels  活动渠道ID列表
     * @param reqChannelId 渠道ID
     * @param reqOrgCode   门店ID
     * @return bool
     */
    private boolean checkedInstallmentActChannel(List<Integer> actChannels, Integer reqChannelId, String reqOrgCode) {
        if (Strings.isNotEmpty(reqOrgCode)) {
            return true;
        }
        return actChannels.contains(reqChannelId);
    }

    /**
     * 分期活动的金额范围校验（根据参数决定是否需要校验）
     *
     * @param actMinAmount       金额范围的起始金额
     * @param actMaxAmount       金额范围的结束金额
     * @param isCheckAmountScope 是否校验金额
     * @param amount             金额（单位：分）
     * @return bool
     */
    private boolean checkedInstallmentActAmountScope(Long actMinAmount, Long actMaxAmount, boolean isCheckAmountScope, Long amount) {
        if (!isCheckAmountScope) {
            return true;
        }
        return actMinAmount <= amount && amount <= actMaxAmount;
    }

    /**
     * 分期活动的时间校验（不传时间则用当前时间校验）
     *
     * @param actStartTime 活动开始时间
     * @param actEndTime   活动结束时间
     * @param reqStartTime 请求开始时间
     * @param reqEndTime   请求结束时间
     * @return bool
     */
    private boolean checkedInstallmentActTime(long actStartTime, long actEndTime, Long reqStartTime, Long reqEndTime) {
        long now = System.currentTimeMillis() / 1000;

        // 进行中
        if (Objects.isNull(reqStartTime) || Objects.isNull(reqEndTime)) {
            return actStartTime <= now && now <= actEndTime;
        }

        // 进行中 + 未开始的
        return !(reqEndTime <= actStartTime || reqStartTime >= reqEndTime);
    }

    /**
     * 校验分期活动列表的参数判断
     *
     * @param request 请求参数
     */
    private void checkInstallmentActParamVerify(InstallmentConditionCheckRequest request) throws BizError {
        if (Objects.isNull(request.getActId()) && Objects.isNull(request.getInstallmentType())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请求参数错误，分期类型不能为空");
        }
        if (Objects.isNull(request.getChannelId()) && StringUtils.isEmpty(request.getOrgCode())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请求参数错误，渠道和门店不能同时为空");
        }
        if (INSTALLMENT_STORE_CHANNELS.contains(request.getChannelId()) && StringUtils.isEmpty(request.getOrgCode())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请求参数错误，门店不能为空");
        }
        if (CollectionUtils.isEmpty(request.getGoodsList())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请求参数错误，商品不能为空");
        }
        if (Objects.isNull(request.getIsPerfectMatchGoods())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请求参数错误，是否完全匹配商品不能为空");
        }
        if (Objects.nonNull(request.getInstallmentType()) &&
                request.getInstallmentType().equals(InstallmentTypeEnum.JD_BT.getValue()) &&
                Objects.isNull(request.getAmount())
        ) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请求参数错误，金额不能为空");
        }
        if (Objects.isNull(request.getInstallmentTime())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "请求参数错误，分期数不能为空");
        }
    }

    /**
     * 获取分期活动列表的参数校验
     *
     * @param request 请求参数
     */
    private void getInstallmentActParamCheck(GetValidInstallmentRequest request) throws BizError {
        if (Objects.isNull(request.getChannelId()) && StringUtils.isEmpty(request.getOrgCode())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "校验请求参数错误，渠道和门店不能同时为空");
        }
        if (INSTALLMENT_STORE_CHANNELS.contains(request.getChannelId()) && StringUtils.isEmpty(request.getOrgCode())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "校验请求参数错误，门店不能为空");
        }
        if (CollectionUtils.isEmpty(request.getGoodsList())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "校验请求参数错误，商品不能为空");
        }
        if (Objects.isNull(request.getIsPerfectMatchGoods())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "校验请求参数错误，是否完全匹配商品不能为空");
        }
        if (Objects.isNull(request.getIsCheckAmountScope())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "校验请求参数错误，是否校验金额范围不能为空");
        }
        if (request.getIsCheckAmountScope() && Objects.isNull(request.getAmount())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "校验请求参数错误，金额不能为空");
        }
    }

    private boolean checkTime(Long startTime, Long endTime) {
        long now = System.currentTimeMillis() / 1000;
        // 进行中
        return now >= startTime && now <= endTime;
    }

    private boolean checkRegion(GetGoodsSubsidyActRequest request, PurchaseActivityInfo info) {
        // nacos 白名单 不检查区域（全国可用）
        if (CollectionUtils.isNotEmpty(nacosConfig.getActivityAreaWhiteList()) && nacosConfig.getActivityAreaWhiteList().contains(info.getActId())) {
            return true;
        }

        // 如果没有区域则不检查，直营店、专卖店、授权店现场购
        if (OFFLINE_CHANNEL.contains(request.getChannel()) && Objects.isNull(request.getRegion())) {
            return true;
        }

        // region匹配
        if (RegionUtil.isAnyMatch(request.getRegion(), info.getActivityRegion())) {
            return true;
        }

        return false;
    }

    private PurchaseActivityInfo copyResponse(PurchaseActivityInfo activityInfo) {
        return copyResponse(activityInfo, null);
    }

    private PurchaseActivityInfo copyResponse(PurchaseActivityInfo activityInfo, GetGoodsSubsidyActRequest request) {
        PurchaseActivityInfo info = new PurchaseActivityInfo();
        info.setActId(activityInfo.getActId());
        info.setActName(activityInfo.getActName());
        info.setActType(activityInfo.getActType());
        info.setStartTime(activityInfo.getStartTime());
        info.setEndTime(activityInfo.getEndTime());
        info.setMaxReduce(activityInfo.getMaxReduce());
        info.setReduceDiscount(activityInfo.getReduceDiscount());
        info.setPaymentAccess(activityInfo.getPaymentAccess());
        info.setInvoiceRule(activityInfo.getInvoiceRule());
        info.setReportCity(activityInfo.getReportCity());
        info.setTag(activityInfo.getTag());
        info.setCateLevel(activityInfo.getCateLevel());
        info.setActivityRegion(Optional.ofNullable(activityInfo.getActivityRegion()).orElse(Lists.newArrayList()));
        info.setSkuList(Optional.ofNullable(activityInfo.getSkuList()).orElse(Lists.newArrayList()));
        info.setGroupMap(Optional.ofNullable(activityInfo.getGroupMap()).orElse(Maps.newHashMap()));
        info.setActivityUrl(activityInfo.getActivityUrl());
        info.setSubsidyMode(activityInfo.getSubsidyMode());
        info.setActPayTag(activityInfo.getActPayTag());
        info.setSkuCateLevel(activityInfo.getSkuCateLevel());
        info.setInvoiceCompanyId(activityInfo.getInvoiceCompanyId());
        info.setCheckSnMap(activityInfo.getCheckSnMap());
        info.setFetchType(activityInfo.getFetchType());
        info.setUsageGuide(activityInfo.getUsageGuide());
        info.setUsageGuideImgUrl(activityInfo.getUsageGuideImgUrl());
        info.setExtInfo(activityInfo.getExtInfo());
        info.setPaymentMode(activityInfo.getPaymentMode());
        if (Objects.nonNull(request)) {
            Long sku = request.getSkuList().get(0);
            if (MapUtils.isNotEmpty(activityInfo.getGroupMap())) {
                String groupCode = activityInfo.getGroupMap().get(sku);
                info.setCateCode(groupCode);
            }
            if (Objects.nonNull(activityInfo.getSubsidyMode())) {
                if (SubsidyModeEnum.GUANGDONG_MODE.getValue() == activityInfo.getSubsidyMode()) {
                    String copyCategoryName = Optional.ofNullable(activityInfo.getSkuCategoryName()).map(map -> map.get(sku)).orElse(StringUtils.EMPTY);
                    String copySku69Code = Optional.ofNullable(activityInfo.getSku69Code()).map(map -> map.get(sku)).orElse(StringUtils.EMPTY);
                    String brand = Optional.ofNullable(activityInfo.getBrandMap()).map(map -> map.get(sku)).orElse(StringUtils.EMPTY);
                    String itemName = Optional.ofNullable(activityInfo.getItemNameMap()).map(map -> map.get(sku)).orElse(StringUtils.EMPTY);
                    String specModel = Optional.ofNullable(activityInfo.getSpecModelMap()).map(map -> map.get(sku)).orElse(StringUtils.EMPTY);
                    Map<Long, String> resultGroupMap = new HashMap<>();
                    resultGroupMap.put(sku, activityInfo.getGroupMap().get(sku));
                    Map<Long, String> skuCateLevel = new HashMap<>();
                    skuCateLevel.put(sku, activityInfo.getSkuCateLevel().get(sku));
                    info.setSkuCategoryName(Map.of(sku, copyCategoryName));
                    info.setSku69Code(Map.of(sku, copySku69Code));
                    info.setBrand(brand);
                    info.setItemName(itemName);
                    info.setSpecModel(specModel);
                    info.setSkuList(List.of(sku));
                    info.setGroupMap(resultGroupMap);
                    info.setSkuCateLevel(skuCateLevel);

                }
                if (SubsidyModeEnum.JIJIAN_MODE.getValue() == activityInfo.getSubsidyMode()) {
                    // 处理极简模式字段
                    String copySkuDiscountCode = Optional.ofNullable(activityInfo.getSkuDiscountCode()).map(map -> map.get(sku)).orElse(StringUtils.EMPTY);
                    String copyCategoryName = Optional.ofNullable(activityInfo.getSkuCategoryName()).map(map -> map.get(sku)).orElse(StringUtils.EMPTY);
                    String copySku69Code = Optional.ofNullable(activityInfo.getSku69Code()).map(map -> map.get(sku)).orElse(StringUtils.EMPTY);
                    String copyUnionPayActivityId = Optional.ofNullable(activityInfo.getSkuUnionPayActivityId()).map(map -> map.get(sku)).orElse(StringUtils.EMPTY);
                    String copyCateCode = Optional.ofNullable(activityInfo.getGroupMap()).map(map -> map.get(sku)).orElse(null);
                    String brand = Optional.ofNullable(activityInfo.getBrandMap()).map(map -> map.get(sku)).orElse(StringUtils.EMPTY);
                    String itemName = Optional.ofNullable(activityInfo.getItemNameMap()).map(map -> map.get(sku)).orElse(StringUtils.EMPTY);
                    String specModel = Optional.ofNullable(activityInfo.getSpecModelMap()).map(map -> map.get(sku)).orElse(StringUtils.EMPTY);

                    // 只返回入参 SKU 信息
                    info.setSkuDiscountCode(Map.of(sku, copySkuDiscountCode));
                    info.setSkuCategoryName(Map.of(sku, copyCategoryName));
                    info.setSku69Code(Map.of(sku, copySku69Code));
                    info.setSkuUnionPayActivityId(Map.of(sku, copyUnionPayActivityId));
                    if (Objects.nonNull(copyCateCode)) {
                        info.setCateCode(copyCateCode);
                    }
                    info.setBrand(brand);
                    info.setItemName(itemName);
                    info.setSpecModel(specModel);
                }
            }
            if (ChannelEnum.MISHOP.getValue() != request.getChannel()) {
                Map<Long, Boolean> checkSnMap = new HashMap<>();
                checkSnMap.put(sku, Optional.ofNullable(activityInfo.getCheckSnMap()).map(map -> map.get(sku)).orElse(null));
                info.setCheckSnMap(checkSnMap);
            }
        }

        return info;
    }
}
