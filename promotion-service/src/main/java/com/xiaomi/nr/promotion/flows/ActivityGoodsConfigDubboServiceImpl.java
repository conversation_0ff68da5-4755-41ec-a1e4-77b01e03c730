package com.xiaomi.nr.promotion.flows;


import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import com.xiaomi.nr.promotion.activity.pool.FinancialSubsidyPool;
import com.xiaomi.nr.promotion.activity.pool.InstallmentGiftGoodsPool;
import com.xiaomi.nr.promotion.annotation.log.Log;
import com.xiaomi.nr.promotion.api.dto.GetValidSubsidyRequest;
import com.xiaomi.nr.promotion.api.dto.GetValidSubsidyResponse;
import com.xiaomi.nr.promotion.api.dto.enums.InterestBearerEnum;
import com.xiaomi.nr.promotion.api.dto.enums.UserScopeEnum;
import com.xiaomi.nr.promotion.api.dto.model.ActivitySubsidyInfo;
import com.xiaomi.nr.promotion.api.dto.model.SubsidyDTO;
import com.xiaomi.nr.promotion.api.service.ActivityGoodsConfigDubboService;
import com.xiaomi.nr.promotion.dao.mysql.mdpromotion.TInstallmentGiftGoodsPo;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service(timeout = 1000, group = "${dubbo.group}", version = "1.0", retries = 0)
@ApiModule(value = "活动商品配置服务", apiInterface = ActivityGoodsConfigDubboService.class)
public class ActivityGoodsConfigDubboServiceImpl implements ActivityGoodsConfigDubboService {

    @Autowired
    private InstallmentGiftGoodsPool installmentGiftGoodsPool;

    @Resource
    private FinancialSubsidyPool financialSubsidyPool;

    /**
     * 分期免息和买赠二选一 生效商品判断
     *
     * @param productIdList 商品id列表
     * @return 其中处于生效的商品id
     */
    
    @Override
    @ApiDoc("分期免息和买赠二选一生效商品判断")
    public Result<List<Long>> installmentBuyGiftValidProduct(List<Long> productIdList) {

        try {
            if (CollectionUtils.isEmpty(productIdList)) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "请求参数错误，商品id不能为空");
            }
            if (productIdList.size() > 100) {
                throw ExceptionHelper.create(GeneralCodes.ParamError, "请求参数错误，商品id列表长度不能超过100");
            }
            List<Long> validProductList = new ArrayList<>();
            long currentTimestamp = System.currentTimeMillis() / 1000;
            Map<Long, TInstallmentGiftGoodsPo> tInstallmentGiftGoodsPoMap = installmentGiftGoodsPool.getCurrentValidProductMapCache();
            productIdList.forEach(e -> {
                TInstallmentGiftGoodsPo po = tInstallmentGiftGoodsPoMap.get(e);
                if (null != po && po.getBeginTime() < currentTimestamp && po.getEndTime() > currentTimestamp) {
                    validProductList.add(po.getProductId());
                }
            });
            return Result.success(validProductList);
        } catch (Exception e) {
            log.error("ActivityGoodsConfigDubboService installmentBuyGiftValidProduct error. request:{}", GsonUtil.toJson(productIdList), e);
            return Result.fromException(e);
        }
    }

    @Override
    @ApiDoc("根据条件获取有效的金融贴息活动信息")
    @Log(name = "ActivityGoodsConfigDubboService#getValidFinancialSubsidy")
    public Result<GetValidSubsidyResponse> getValidFinancialSubsidy(GetValidSubsidyRequest request) {
        log.info("ActivityGoodsConfigDubboService.getValidFinancialSubsidy.request is {}", GsonUtil.toJson(request));

        try {
            // 参数校验
            getSubsidyActParamCheck(request);

            // 对可能为空的用户标签做兼容，只查all类型的活动
            if (CollectionUtils.isEmpty(request.getUserScope())) {
                request.setUserScope(Collections.singletonList(UserScopeEnum.ALL.getKey()));
            }

            GetValidSubsidyResponse response = new GetValidSubsidyResponse();

            List<Long> ssuCanJoinActId = financialSubsidyPool.getSsuCanJoinActId(request.getSsuId());
            log.info("ActivityGoodsConfigDubboService.getValidFinancialSubsidy.ssuCanJoinActId is {}", GsonUtil.toJson(ssuCanJoinActId));
            if(CollectionUtils.isEmpty(ssuCanJoinActId)){
                return Result.success(response);
            }
            long nowTime = System.currentTimeMillis() / 1000;
            List<ActivitySubsidyInfo> activityList = ssuCanJoinActId.stream()
                    .map(e -> financialSubsidyPool.getActDTOByActId(e))
                    //过滤异常情况，空指针降级（主次缓存不同步，缓存刷新）
                    .filter(Objects::nonNull)
                    .filter(e -> nowTime >= e.getStartTime() && nowTime < e.getEndTime())
                    //用户范围过滤
                    .filter(e -> request.getUserScope().contains(e.getUserScope()) || StringUtils.equals(e.getUserScope(), UserScopeEnum.ALL.getKey()))
                    //渠道过滤
                    .filter(e -> e.getChannels().contains(request.getChannel()))
                    //活动类型过滤
                    .filter(e -> e.getTypeId().intValue() == request.getActivityType().intValue())
                    //订单大定时间过滤
                    .filter(e -> request.getOrderTime() >= e.getOrderStartTime() && request.getOrderTime() <= e.getOrderEndTime())
                    .map(this::convertDTO2Response)
                    .collect(Collectors.toList());
            response.setActivityList(activityList);
            log.info("ActivityGoodsConfigDubboService.getValidFinancialSubsidy.activityList is {}", GsonUtil.toJson(activityList));
            return Result.success(response);
        } catch (Exception e) {
            log.error("ActivityGoodsConfigDubboService.getValidFinancialSubsidy is error, request is {}", GsonUtil.toJson(request), e);
            return Result.fromException(e);
        }
    }

    private ActivitySubsidyInfo convertDTO2Response(SubsidyDTO subsidyDTO){
        log.info("ActivityGoodsConfigDubboService.subsidyDTO is {}", GsonUtil.toJson(subsidyDTO));
        ActivitySubsidyInfo activitySubsidyInfo = new ActivitySubsidyInfo();
        activitySubsidyInfo.setActivityId(subsidyDTO.getId());
        activitySubsidyInfo.setTypeId(subsidyDTO.getTypeId());
        activitySubsidyInfo.setName(subsidyDTO.getName());
        activitySubsidyInfo.setStartTime(subsidyDTO.getStartTime());
        activitySubsidyInfo.setEndTime(subsidyDTO.getEndTime());
        activitySubsidyInfo.setChannel(subsidyDTO.getChannels());
        activitySubsidyInfo.setUserScope(subsidyDTO.getUserScope());
        activitySubsidyInfo.setInterestBearer(InterestBearerEnum.findKeyByValue(subsidyDTO.getInterestBearer()));
        activitySubsidyInfo.setBudgetApplyNo(subsidyDTO.getBudgetApplyNo());
        activitySubsidyInfo.setLineNum(subsidyDTO.getLineNum());
        log.info("ActivityGoodsConfigDubboService.convertDTO2Response is {}", GsonUtil.toJson(activitySubsidyInfo));
        return activitySubsidyInfo;
    }

    /**
     * 获取分期活动列表的参数校验
     *
     * @param request 请求参数
     */
    private void getSubsidyActParamCheck(GetValidSubsidyRequest request) throws BizError {

        if (Objects.isNull(request.getChannel())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "校验请求参数错误，活动渠道不能为空");
        }
        if (Objects.isNull(request.getActivityType())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "校验请求参数错误，活动类型不能为空");
        }
        if (Objects.isNull(request.getOrderTime())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "校验请求参数错误，大定订单时间不能为空");
        }
        if (Objects.isNull(request.getSsuId())) {
            throw ExceptionHelper.create(GeneralCodes.ParamError, "校验请求参数错误，ssuId不能为空");
        }

    }
}
