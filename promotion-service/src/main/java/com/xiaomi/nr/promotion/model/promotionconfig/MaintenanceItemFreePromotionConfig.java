package com.xiaomi.nr.promotion.model.promotionconfig;

import lombok.ToString;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 售后维保 --- 工项免费活动
 */
@ToString
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class MaintenanceItemFreePromotionConfig extends MultiPromotionConfig {

    /**
     * 促销金额
     */
    private Long promotionPrice;

    /**
     * 最大扣减金额
     */
    private Long maxReduceAmount;

    /**
     * 优惠车辆身份：赛道尊享会员(SVIP)，赛道版车主
     */
    private Integer carIdentityType;

    /**
     * 会员ID、购车权益ID
     */
    private String carIdentityId;

    /**
     * 活动说明
     */
    private String description;

    /**
     * 支持工单类型
     */
    private List<Integer> workOrderType;

    /**
     * 用户参与次数限制
     */
    private Integer identityJoinLimitNum;

    public Long getPromotionPrice() {
        return promotionPrice;
    }

    public void setPromotionPrice(Long promotionPrice) {
        this.promotionPrice = promotionPrice;
    }

    public Long getMaxReduceAmount() {
        return maxReduceAmount;
    }

    public void setMaxReduceAmount(Long maxReduceAmount) {
        this.maxReduceAmount = maxReduceAmount;
    }

    public Integer getCarIdentityType() {
        return carIdentityType;
    }

    public void setCarIdentityType(Integer carIdentityType) {
        this.carIdentityType = carIdentityType;
    }

    public String getCarIdentityId() {
        return carIdentityId;
    }

    public void setCarIdentityId(String carIdentityId) {
        this.carIdentityId = carIdentityId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<Integer> getWorkOrderType() {
        return workOrderType;
    }

    public void setWorkOrderType(List<Integer> workOrderType) {
        this.workOrderType = workOrderType;
    }

    public Integer getIdentityJoinLimitNum() {
        return identityJoinLimitNum;
    }

    public void setIdentityJoinLimitNum(Integer identityJoinLimitNum) {
        this.identityJoinLimitNum = identityJoinLimitNum;
    }
}
