package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.CrowdEnum;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.api.dto.model.CrowdInfo;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyGiftPromotionConfig;
import com.xiaomi.nr.promotion.resource.external.CrowdPortraitExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.resource.external.UserPropertyExternalProvider;
import com.xiaomi.nr.promotion.resource.model.UserPropertyResult;
import com.xiaomi.nr.promotion.rpc.crowdportrait.model.RuleResponse;
import com.xiaomi.nr.promotion.rpc.crowdportrait.model.RuleResult;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;


/**
 * 人群condition
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CrowdCondition extends Condition {

    /**
     * 人群列表，枚举见@CrowdEnum
     */
    private List<String> crowdList;

    /**
     * 人群信息
     */
    private Map<String, CrowdInfo> crowdMap;

    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (crowdList.contains(CrowdEnum.ALL.getCode())) {
            context.setCrowdList(Collections.singletonList(CrowdEnum.ALL.getCode()));
            return true;
        }

        if (crowdList.contains(CrowdEnum.STUDENT.getCode()) || crowdList.contains(CrowdEnum.F_MEMBER.getCode()) || crowdList.contains(CrowdEnum.MARKET_PLATFORM.getCode())) {
            UserPropertyExternalProvider userProviderProvider = (UserPropertyExternalProvider) context.getExternalDataMap().get(ResourceExtType.USER_PROPERTY_RESULT);

            try {
                UserPropertyResult userPropertyResult = userProviderProvider.getData();

                // 判断学生是否匹配
                if (crowdList.contains(CrowdEnum.STUDENT.getCode()) && userPropertyResult.isStudent()) {
                    context.setCrowdList(Collections.singletonList(CrowdEnum.STUDENT.getCode()));
                    return true;
                }

                // 判断F会员是否匹配
                if (crowdList.contains(CrowdEnum.F_MEMBER.getCode()) && userPropertyResult.isFMember()) {
                    context.setCrowdList(Collections.singletonList(CrowdEnum.F_MEMBER.getCode()));
                    return true;
                }

                // 判断海葵人群是否匹配
                if (crowdList.contains(CrowdEnum.MARKET_PLATFORM.getCode())) {
                    CrowdInfo crowdInfo = crowdMap.getOrDefault(CrowdEnum.MARKET_PLATFORM.getCode(), null);
                    if (crowdInfo != null && userPropertyResult.getHitHaikuiIds().contains(crowdInfo.getCrowdId())) {
                        context.setCrowdList(Collections.singletonList(CrowdEnum.MARKET_PLATFORM.getCode()));
                        return true;
                    }
                }

            } catch (Exception e){
                // 接口问题-》》降级，不过此买赠活动
                log.warn("CrowdCondition UserPropertyExternalProvider downgrade. userId:{}, e:", request.getUserId(), e);
                return false;
            }
        }

        // 判断人群画像是否匹配
        if (crowdList.contains(CrowdEnum.CROWD_PHOTO.getCode())) {
            CrowdPortraitExternalProvider crowdPortraitExternalProvider = (CrowdPortraitExternalProvider) context.getExternalDataMap().get(ResourceExtType.CROWD_PORTRAIT);

            try {
                RuleResponse ruleResponse = crowdPortraitExternalProvider.getData();

                Map<String, RuleResult> result = ruleResponse.getResult();

                CrowdInfo crowdInfo = crowdMap.getOrDefault(CrowdEnum.CROWD_PHOTO.getCode(), null);
                if (crowdInfo != null) {
                    RuleResult ruleResult = result.get("up_" + crowdInfo.getCrowdId());
                    if (ruleResult != null && ruleResult.isRuleMatch()) {
                        context.setCrowdList(Collections.singletonList(CrowdEnum.CROWD_PHOTO.getCode()));
                        return true;
                    }
                }

            } catch (Exception e) {
                // 接口问题-》》降级，不过此买赠活动
                log.warn("CrowdCondition CrowdPortraitExternalProvider downgrade. userId:{}, e:", request.getUserId(), e);
                return false;
            }
        }

        return false;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof BuyGiftPromotionConfig)) {
            log.error("config is not instanceof BuyGiftPromotionConfig. config:{}", config);
            return;
        }
        BuyGiftPromotionConfig promotionConfig = (BuyGiftPromotionConfig) config;
        crowdList = promotionConfig.getCrowdList();
        crowdMap = promotionConfig.getCrwodMap();
    }


}
