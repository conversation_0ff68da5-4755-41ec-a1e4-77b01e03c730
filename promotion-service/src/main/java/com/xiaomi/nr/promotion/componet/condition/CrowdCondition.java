package com.xiaomi.nr.promotion.componet.condition;

import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.CrowdEnum;
import com.xiaomi.nr.promotion.engine.componet.Condition;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyGiftPromotionConfig;
import com.xiaomi.nr.promotion.resource.external.ProMemberExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.resource.external.UserPropertyExternalProvider;
import com.xiaomi.nr.promotion.resource.model.UserPropertyResult;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;


/**
 * 人群condition
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component
public class CrowdCondition extends Condition {

    /**
     * 人群列表，枚举见@CrowdEnum
     */
    private List<String> crowdList;


    @Override
    public boolean isSatisfied(CheckoutPromotionRequest request, LocalContext context) throws BizError {
        if (crowdList.contains(CrowdEnum.ALL.getCode())) {
            context.setCrowdList(Collections.singletonList(CrowdEnum.ALL.getCode()));
            return true;
        }
        // 判断学生是否匹配
        if (crowdList.contains(CrowdEnum.STUDENT.getCode())) {
            UserPropertyExternalProvider userProviderProvider = (UserPropertyExternalProvider) context.getExternalDataMap().get(ResourceExtType.USER_PROPERTY_RESULT);
            // 接口问题-》》降级，不过此买赠活动
            try {
                UserPropertyResult userPropertyResult = userProviderProvider.getData();
                if (userPropertyResult.isStudent()){
                    context.setCrowdList(Collections.singletonList(CrowdEnum.STUDENT.getCode()));
                    return true;
                }else {
                    return false;
                }
            }catch (Exception e){
                log.warn("CrowdCondition UserPropertyExternalProvider downgrade. userId:{}", request.getUserId());
                return false;
            }
        }

        // 判断F会员是否匹配
        if (crowdList.contains(CrowdEnum.F_MEMBER.getCode())) {
            ProMemberExternalProvider proMemberExternalProvider = (ProMemberExternalProvider) context.getExternalDataMap().get(ResourceExtType.PRO_MEMBER_RESULT);
            // 接口问题-》》降级，不过此买赠活动
            try {
                Boolean isFMember = proMemberExternalProvider.getData();
                if (isFMember){
                    context.setCrowdList(Collections.singletonList(CrowdEnum.F_MEMBER.getCode()));
                    return true;
                }else {
                    return false;
                }
            }catch (Exception e){
                log.warn("CrowdCondition ProMemberExternalProvider downgrade. userId:{}", request.getUserId());
                return false;
            }
        }

        return true;
    }

    @Override
    public void loadConfig(AbstractPromotionConfig config) {
        if (!(config instanceof BuyGiftPromotionConfig)) {
            log.error("config is not instanceof BuyGiftPromotionConfig. config:{}", config);
            return;
        }
        BuyGiftPromotionConfig promotionConfig = (BuyGiftPromotionConfig) config;
        crowdList = promotionConfig.getCrowdList();
    }


}
