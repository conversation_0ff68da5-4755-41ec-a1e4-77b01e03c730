package com.xiaomi.nr.promotion.activity;

import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.CheckProductsActResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.GetProductActRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.constant.PromotionConstant;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.CheckoutContext;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.apache.dubbo.common.utils.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.xiaomi.nr.promotion.enums.ActivityTypeEnum.CONTRACT_PHONE;

/**
 * <AUTHOR>
 */
public class ContractPhoneActivity implements ActivityTool {
    @Override
    public void doCheckout(CheckoutPromotionRequest request, CheckoutContext checkoutContext) throws BizError {
        List<CartItem> cartList = request.getCartList();
        if (CollectionUtils.isEmpty(cartList)) {
            return;
        }
        // 处理购物车中的合约机优惠
        List<String> joinedItemIdList = new ArrayList<>();
        for (CartItem cartItem : cartList) {
            if (CollectionUtils.isEmpty(cartItem.getPreferentialInfos())) {
                continue;
            }
            List<PreferentialInfo> preferentialInfos = cartItem.getPreferentialInfos();
            for (PreferentialInfo preferentialInfo : preferentialInfos) {
                if (preferentialInfo.getTypeCode().equals(PromotionConstant.CONTRACTPHONE)) {
                    long maxContractAmount = preferentialInfo.getMaxAmount();
                    long cartPrice = cartItem.getCartPrice();
                    // 合约机实际优惠金额
                    long contractReduce = cartPrice-maxContractAmount>0?maxContractAmount:cartPrice;
                    long newCartPrice = cartPrice - contractReduce;
                    cartItem.setCartPrice(newCartPrice);
                    preferentialInfo.setAmount(contractReduce);
                    joinedItemIdList.add(cartItem.getItemId());
                    break;
                }
            }
        }
        // 组织promotion数据输出
        List<PromotionInfo> promotionInfos = checkoutContext.getPromotion();
        PromotionInfo promotionInfo = new PromotionInfo();
        promotionInfo.setType(String.valueOf(CONTRACT_PHONE.getValue()));
        promotionInfo.setTypeInfo(CONTRACT_PHONE.getName());
        promotionInfo.setJoinedItemId(joinedItemIdList);
        promotionInfos.add(promotionInfo);
        // 邮费
        List<Express> expressList = checkoutContext.getExpress();
        expressList.add(new Express());
        // 活动结束时间
        List<Long> finTimeList = checkoutContext.getFinTime();
        finTimeList.add(0L);
    }

    @Override
    public long getId() {
        return 0;
    }

    @Override
    public PromotionToolType getType() {
        return null;
    }

    @Override
    public long getUnixStartTime() {
        return 0;
    }

    @Override
    public long getUnixEndTime() {
        return 0;
    }

    @Override
    public boolean load(AbstractPromotionConfig config) throws BizError {
        return false;
    }

    @Override
    public ActivityDetail getActivityDetail() {
        return null;
    }

    @Override
    public PromotionInfo getProductAct(GetProductActRequest request, GoodsHierarchy hierarchy, boolean isOrgTool) throws BizError {
        return null;
    }

    @Override
    public PromotionInfo buildCartPromotionInfo(LocalContext context) throws BizError {
        return null;
    }

    @Override
    public List<CheckGoodsItem> checkProductsAct(CheckProductsActRequest request, Map<String, GoodsHierarchy> goodsHierarchyMap) throws BizError {
        return null;
    }

    @Override
    public AbstractPromotionConfig getPromotionConfig() {
        return null;
    }

    @Override
    public BizPlatformEnum getBizPlatform() {
        return BizPlatformEnum.NEW_RETAIL;
    }
}
