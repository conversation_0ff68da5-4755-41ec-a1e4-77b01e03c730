package com.xiaomi.nr.promotion.model.promotionconfig.loader;

import com.google.common.collect.Lists;
import com.xiaomi.nr.md.promotion.admin.api.constant.MultiUnionRuleTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.ProductIdTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.ProductWhileBlackEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ProductPolicy;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.CarShopPostFreeRule;
import com.xiaomi.nr.promotion.entity.redis.*;
import com.xiaomi.nr.promotion.enums.PolicyQuotaTypeEnum;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.PostFreePromotionConfig;
import com.xiaomi.nr.promotion.tool.PromotionDescRuleTool;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 包邮配置数据加载
 *
 * <AUTHOR>
 * @date 2021/6/3
 */
@Slf4j
@Component
public class PostFreePromotionConfigLoader implements PromotionConfigLoader {

    @Autowired
    private PromotionDescRuleTool promotionDescRuleTool;

    @Override
    public boolean support(AbstractPromotionConfig promotionConfig) {
        return promotionConfig instanceof PostFreePromotionConfig;
    }

    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityInfo activityInfo) throws BizError {
        if (activityInfo == null || promotionConfig == null) {
            log.error("[PostFreePromotionConfigLoader] activityInfo is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "activityInfo is null");
        }
        TypeBase typeBase = activityInfo.getBasetype();
        Condition condition = activityInfo.getCondition();
        Policy policy = activityInfo.getPolicy();
        if (typeBase == null || condition == null || policy == null) {
            log.error("[PostFreePromotionConfigLoader] baseType or policy or policy is null. actInfo:{}", activityInfo);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "baseType or policy or policy is null");
        }
        Long id = typeBase.getId();
        PostFreePromotionConfig postFreeConfig = (PostFreePromotionConfig) promotionConfig;
        List<CompareItem> goodsIncludes = condition.getGoodsInclude();
        if (CollectionUtils.isEmpty(goodsIncludes)) {
            log.error("condition postFree goodsInclude is empty. actId:{} condition:{} ", id, condition);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "condition goodsInclude is empty");
        }
        CompareItem compareItem = goodsIncludes.get(0);
        if (compareItem == null) {
            log.error("condition postFree goodsInclude goods is empty. actId:{} compareItem:{} ", id, compareItem);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "condition goodsInclude goods is empty");
        }

        Set<String> skuPackageSet = new HashSet<>();
        List<String> skuList = Optional.ofNullable(compareItem.getSku()).orElse(Collections.emptyList());
        List<String> packageList = Optional.ofNullable(compareItem.getPackages()).orElse(Collections.emptyList());
        skuPackageSet.addAll(skuList);
        skuPackageSet.addAll(packageList);

        postFreeConfig.setIncludeGoods(compareItem.getGoods());
        postFreeConfig.setJoinGoods(compareItem);
        postFreeConfig.setIncludeSkuPackages(skuPackageSet);

        if (CollectionUtils.isEmpty(policy.getPolicies()) || policy.getPolicies().get(0) == null) {
            log.error("policy postFree is invalid. actId:{} policy:{} ", id, policy);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "policy is invalid");
        }
        List<QuotaLevel> levelList = new ArrayList<>();
        for (PolicyLevel policyLevel : policy.getPolicies()) {
            List<QuotaEle> quotaEleList = policyLevel.getQuota();
            if (CollectionUtils.isEmpty(quotaEleList)) {
                log.error("policy postFree quota empty. actId:{} policy:{} ", id, policy);
                throw ExceptionHelper.create(GeneralCodes.InternalError, "policy quota empty");
            }
            QuotaLevel level = new QuotaLevel();
            level.setQuotas(policyLevel.getQuota());
            level.setPostFree(policyLevel.getRule().getPostFree());
            levelList.add(level);
        }
        postFreeConfig.setLevelList(levelList);
        // 活动规则
        String descRuleIndex = promotionDescRuleTool.generatePostFreeDescRuleIndex(levelList);
        List<String> descRule = promotionDescRuleTool.generatePostFreeDescRule(levelList);
        postFreeConfig.setDescRuleIndex(descRuleIndex);
        postFreeConfig.setDescRule(descRule);
    }

    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityConfig activityConfig) throws BizError {
        if (activityConfig == null || promotionConfig == null) {
            log.warn("[CarShopPostFreePromotionConfig] activityConfig is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "activityConfig is null");
        }
        PostFreePromotionConfig carShopPostFreePromotionConfig = (PostFreePromotionConfig) promotionConfig;
        Long actId = activityConfig.getId();

        // 白名单商品
        List<ProductPolicy> productPolicyList = Optional.ofNullable(activityConfig.getProductPolicyList()).orElse(new ArrayList<>());
        List<Long> ssuIdList = productPolicyList.stream()
                .filter(productPolicy -> {
                    CarShopPostFreeRule.ProductRule productRule = GsonUtil.fromJson(productPolicy.getRule(), CarShopPostFreeRule.ProductRule.class);
                    return productRule != null && productRule.getLimitType() != null && ProductWhileBlackEnum.WHITE.value == productRule.getLimitType();
                })
                .filter(productPolicy -> productPolicy.getProductId() != null && productPolicy.getProductId() > 0L)
                .filter(productPolicy -> productPolicy.getProductIdType() != null && ProductIdTypeEnum.SSU.code == productPolicy.getProductIdType())
                .map(ProductPolicy::getProductId)
                .toList();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(ssuIdList)) {
            log.warn("CarShopPostFreePromotionConfig goods is empty. actId:{}", actId);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "CarShopPostFreePromotionConfig goods is empty");
        }
        CompareItem compareItem = new CompareItem();
        compareItem.setSsuId(ssuIdList);
        carShopPostFreePromotionConfig.setJoinGoods(compareItem);

        // 次数限制
        CarShopPostFreeRule rule = GsonUtil.fromJson(activityConfig.getRule(), CarShopPostFreeRule.class);
        if (rule == null || rule.getRuleType() != MultiUnionRuleTypeEnum.OVER_COUNT_FREESHIPPING.code || rule.getSpecification() == null
                || rule.getSpecification().getThreshold() == null || rule.getSpecification().getThreshold() < 0L) {
            log.warn("CarShopPostFreePromotionConfig rule is invalid. actId:{} rule:{} ", actId, rule);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "policy is invalid");
        }
        QuotaLevel level = new QuotaLevel();
        carShopPostFreePromotionConfig.setLevelList(Lists.newArrayList(level));

        QuotaEle quotaEle = new QuotaEle();
        level.setQuotas(Lists.newArrayList(quotaEle));
        quotaEle.setType(PolicyQuotaTypeEnum.POLICY_QUOTA_NUM.getType());
        quotaEle.setCount(rule.getSpecification().getThreshold().intValue());

        // 规则文案
        carShopPostFreePromotionConfig.setDescRuleIndex(promotionDescRuleTool.generatePostFreeDescRuleIndex(carShopPostFreePromotionConfig.getLevelList()));
    }
}
