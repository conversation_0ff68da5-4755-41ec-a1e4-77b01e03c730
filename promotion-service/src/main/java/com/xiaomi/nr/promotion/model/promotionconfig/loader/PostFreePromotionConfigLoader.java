package com.xiaomi.nr.promotion.model.promotionconfig.loader;

import com.xiaomi.nr.promotion.entity.redis.*;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.promotionconfig.AbstractPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.PostFreePromotionConfig;
import com.xiaomi.nr.promotion.tool.PromotionDescRuleTool;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import com.xiaomi.youpin.infra.rpc.errors.ExceptionHelper;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 包邮配置数据加载
 *
 * <AUTHOR>
 * @date 2021/6/3
 */
@Slf4j
@Component
public class PostFreePromotionConfigLoader implements PromotionConfigLoader {

    @Autowired
    private PromotionDescRuleTool promotionDescRuleTool;

    @Override
    public boolean support(AbstractPromotionConfig promotionConfig) {
        if (promotionConfig instanceof PostFreePromotionConfig) {
            return true;
        }
        return false;
    }

    @Override
    public void load(AbstractPromotionConfig promotionConfig, ActivityInfo activityInfo) throws BizError {
        if (activityInfo == null || promotionConfig == null) {
            log.error("[PostFreePromotionConfigLoader] activityInfo is null");
            throw ExceptionHelper.create(GeneralCodes.InternalError, "activityInfo is null");
        }
        TypeBase typeBase = activityInfo.getBasetype();
        Condition condition = activityInfo.getCondition();
        Policy policy = activityInfo.getPolicy();
        if (typeBase == null || condition == null || policy == null) {
            log.error("[PostFreePromotionConfigLoader] baseType or policy or policy is null. actInfo:{}", activityInfo);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "baseType or policy or policy is null");
        }
        Long id = typeBase.getId();
        PostFreePromotionConfig postFreeConfig = (PostFreePromotionConfig) promotionConfig;
        List<CompareItem> goodsIncludes = condition.getGoodsInclude();
        if (CollectionUtils.isEmpty(goodsIncludes)) {
            log.error("condition postFree goodsInclude is empty. actId:{} condition:{} ", id, condition);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "condition goodsInclude is empty");
        }
        CompareItem compareItem = goodsIncludes.get(0);
        if (compareItem == null) {
            log.error("condition postFree goodsInclude goods is empty. actId:{} compareItem:{} ", id, compareItem);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "condition goodsInclude goods is empty");
        }

        Set<String> skuPackageSet = new HashSet<>();
        List<String> skuList = Optional.ofNullable(compareItem.getSku()).orElse(Collections.emptyList());
        List<String> packageList = Optional.ofNullable(compareItem.getPackages()).orElse(Collections.emptyList());
        skuPackageSet.addAll(skuList);
        skuPackageSet.addAll(packageList);

        postFreeConfig.setIncludeGoods(compareItem.getGoods());
        postFreeConfig.setJoinGoods(compareItem);
        postFreeConfig.setIncludeSkuPackages(skuPackageSet);

        if (CollectionUtils.isEmpty(policy.getPolicies()) || policy.getPolicies().get(0) == null) {
            log.error("policy postFree is invalid. actId:{} policy:{} ", id, policy);
            throw ExceptionHelper.create(GeneralCodes.InternalError, "policy is invalid");
        }
        List<QuotaLevel> levelList = new ArrayList<>();
        for (PolicyLevel policyLevel : policy.getPolicies()) {
            List<QuotaEle> quotaEleList = policyLevel.getQuota();
            if (CollectionUtils.isEmpty(quotaEleList)) {
                log.error("policy postFree quota empty. actId:{} policy:{} ", id, policy);
                throw ExceptionHelper.create(GeneralCodes.InternalError, "policy quota empty");
            }
            QuotaLevel level = new QuotaLevel();
            level.setQuotas(policyLevel.getQuota());
            level.setPostFree(policyLevel.getRule().getPostFree());
            levelList.add(level);
        }
        postFreeConfig.setLevelList(levelList);
        // 活动规则
        String descRuleIndex = promotionDescRuleTool.generatePostFreeDescRuleIndex(levelList);
        List<String> descRule = promotionDescRuleTool.generatePostFreeDescRule(levelList);
        postFreeConfig.setDescRuleIndex(descRuleIndex);
        postFreeConfig.setDescRule(descRule);
    }
}
