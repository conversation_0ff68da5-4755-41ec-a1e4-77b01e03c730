package com.xiaomi.nr.promotion.activity.pool;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.api.dto.enums.SubsidyModeEnum;
import com.xiaomi.nr.promotion.api.dto.model.PurchaseActivityInfo;
import com.xiaomi.nr.promotion.config.NacosConfig;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.NrActGoodsMapper;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.NrActivityMapper;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.NrActGoodsPo;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.NrActivityPo;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.PurchaseExtInfo;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.PurchaseGoodRule;
import com.xiaomi.nr.promotion.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;
import java.util.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： Pancras
 * @date： 2024/9/18
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Slf4j
@Component
public class PurchaseActivityPool {

    // 湖北以旧换新类型、极简模式、深圳模式
    public static final Integer type = 15;

    /**
     * 本地锁
     */
    private final Lock lock = new ReentrantLock();

    /**
     * 商品map缓存数据
     * key:sku, value:List<actId>
     */
    private Map<Long, List<Long>> ACT_ID_LIST_CACHE = new ConcurrentHashMap<>();

    /**
     * 活动map缓存数据
     * key:actId, value:PurchaseActivityInfo
     */
    private Map<Long, PurchaseActivityInfo> ACT_INFO_LIST_CACHE = new ConcurrentHashMap<>();

    /**
     * 门店可参加活动map缓存数据
     * key:orgCode, value:List<actId>
     */
    private Map<String, List<Long>> ORG_CODE_ACT_LIST_CACHE = new ConcurrentHashMap<>();

    /**
     * 渠道可参加活动map缓存数据
     * key:channel, value:List<actId>
     */
    private Map<Integer, List<Long>> CHANNEL_ACT_LIST_CACHE = new ConcurrentHashMap<>();

    @Autowired
    private NrActivityMapper nrActivityMapper;

    @Autowired
    private NrActGoodsMapper nrActGoodsMapper;

    @Autowired
    private NacosConfig nacosConfig;

    /**
     * 刷新购买缓存
     * <p>
     * 该方法从数据库中查询活动和商品信息，并将其组织成不同的映射关系，最后更新到缓存中。
     * <p>
     * 主要步骤包括：
     * 1. 初始化各种映射关系的缓存。
     * 2. 查询活动列表，如果为空则直接更新缓存并返回。
     * 3. 查询商品列表，如果为空则直接返回。
     * 4. 将活动和商品信息组织成映射关系。
     * 5. 处理每个活动的信息，构建活动信息对象，并更新各种映射关系。
     * 6. 最后更新缓存。
     */
    public void refreshPurchaseCache() {
        Map<Long, List<Long>> skuMap = new ConcurrentHashMap<>();
        Map<Long, PurchaseActivityInfo> actMap = new ConcurrentHashMap<>();
        Map<String, List<Long>> orgCodeMap = new ConcurrentHashMap<>();
        Map<Integer, List<Long>> channelMap = new ConcurrentHashMap<>();

        // 活动列表
        long nowTime = System.currentTimeMillis() / 1000;
        List<NrActivityPo> nrActivityPos = nrActivityMapper.queryListByType(type, nowTime);

        if (CollectionUtils.isEmpty(nrActivityPos)) {
            log.warn("hubei purchase activity is empty");

            // 更新缓存
            try {
                lock.lock();
                ACT_ID_LIST_CACHE = skuMap;
                ACT_INFO_LIST_CACHE = actMap;
                ORG_CODE_ACT_LIST_CACHE = orgCodeMap;
                CHANNEL_ACT_LIST_CACHE = channelMap;
            } finally {
                lock.unlock();
            }

            // exit
            return;
        }
        List<Long> actIds = nrActivityPos.stream().map(NrActivityPo::getId).collect(Collectors.toList());

        // 商品列表
        List<NrActGoodsPo> nrActGoodsPos = nrActGoodsMapper.queryListByType(actIds);
        if (CollectionUtils.isEmpty(nrActivityPos)) {
            log.warn("hubei purchase goods is empty");
            return;
        }

        // 活动map
        Map<Long, NrActivityPo> activityPoMap = nrActivityPos.stream()
                .collect(Collectors.toMap(NrActivityPo::getId, Function.identity(), (x, y) -> x));

        // 商品map
        Map<Long, List<NrActGoodsPo>> actGoodsPoMap = nrActGoodsPos.stream()
                .collect(Collectors.groupingBy(NrActGoodsPo::getActivityId));

        // 处理活动信息
        for (Long actId : actIds) {
            // 活动信息
            NrActivityPo actPo = activityPoMap.get(actId);
            if (Objects.isNull(actPo)) {
                log.warn("skip act, act is {}", GsonUtil.toJson(actId));
                continue;
            }

            // 商品信息
            List<NrActGoodsPo> goodsPos = actGoodsPoMap.get(actId);
            if (CollectionUtils.isEmpty(goodsPos)) {
                log.warn("skip act, goods is empty, act is {}", GsonUtil.toJson(actId));
                continue;
            }

            // 构建活动信息
            PurchaseActivityInfo activityInfo = buildPurchaseActivityInfo(actPo, goodsPos);

            // 商品map
            buildSkuMap(activityInfo, skuMap);

            // 活动map
            buildActMap(activityInfo, actMap);

            // 门店活动map
            buildOrgMap(activityInfo, orgCodeMap);

            // 渠道活动map
            buildChannelMap(activityInfo, channelMap);
        }

        // 更新缓存
        try {
            lock.lock();
            ACT_ID_LIST_CACHE = skuMap;
            ACT_INFO_LIST_CACHE = actMap;
            ORG_CODE_ACT_LIST_CACHE = orgCodeMap;
            CHANNEL_ACT_LIST_CACHE = channelMap;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 构建渠道映射表
     *
     * @param activityInfo 购买活动信息
     * @param channelMap   渠道映射表，键为渠道ID，值为活动ID列表
     */
    private void buildChannelMap(PurchaseActivityInfo activityInfo, Map<Integer, List<Long>> channelMap) {
        if (Objects.isNull(activityInfo)) {
            return;
        }

        for (Integer channel : activityInfo.getChannel()) {
            List<Long> actIds = channelMap.getOrDefault(channel, Lists.newArrayList());
            actIds.add(activityInfo.getActId());
            channelMap.put(channel, actIds);
        }
    }

    private void buildOrgMap(PurchaseActivityInfo activityInfo, Map<String, List<Long>> orgCodeMap) {
        if (Objects.isNull(activityInfo) || CollectionUtils.isEmpty(activityInfo.getOrgList())) {
            return;
        }

        for (String orgCode : activityInfo.getOrgList()) {
            List<Long> actIds = orgCodeMap.getOrDefault(orgCode, Lists.newArrayList());
            actIds.add(activityInfo.getActId());
            orgCodeMap.put(orgCode, actIds);
        }
    }

    /**
     * 将PurchaseActivityInfo对象添加到指定的Map中
     *
     * @param activityInfo 购买活动信息对象
     * @param actMap       存储购买活动信息的Map，键为活动ID，值为PurchaseActivityInfo对象
     */
    private void buildActMap(PurchaseActivityInfo activityInfo, Map<Long, PurchaseActivityInfo> actMap) {
        if (Objects.isNull(activityInfo)) {
            return;
        }

        actMap.put(activityInfo.getActId(), activityInfo);
    }

    /**
     * 构建SKU映射表
     *
     * @param activityInfo 购买活动信息，包含SKU列表和活动ID
     * @param skuMap       存储SKU与活动ID列表的映射表
     */
    private void buildSkuMap(PurchaseActivityInfo activityInfo, Map<Long, List<Long>> skuMap) {
        if (CollectionUtils.isEmpty(activityInfo.getSkuList())) {
            return;
        }

        for (Long sku : activityInfo.getSkuList()) {
            List<Long> actList = skuMap.getOrDefault(sku, new ArrayList<>());
            actList.add(activityInfo.getActId());
            skuMap.put(sku, actList);
        }
    }

    /**
     * 构建购买活动信息
     *
     * @param actPo    活动信息对象
     * @param goodsPos 活动商品列表
     * @return 构建的购买活动信息对象
     */
    private PurchaseActivityInfo buildPurchaseActivityInfo(NrActivityPo actPo, List<NrActGoodsPo> goodsPos) {
        PurchaseActivityInfo activityInfo = new PurchaseActivityInfo();
        activityInfo.setActId(actPo.getId());
        activityInfo.setActName(actPo.getName());
        activityInfo.setChannel(Arrays.stream(StringUtils.split(actPo.getChannel(), ","))
                .map(Integer::valueOf)
                .collect(Collectors.toList()));
        activityInfo.setStartTime(actPo.getStartTime());
        activityInfo.setEndTime(actPo.getEndTime());
        activityInfo.setActType(type);

        // 将 JSON 字符串转换为原始的 extInfo 对象

        byte[] bytes = actPo.getExtInfo().getBytes(Charset.forName("ISO-8859-1"));
        String newStr = new String(bytes, Charset.forName("UTF-8"));
        PurchaseExtInfo extInfo = GsonUtil.fromJson(newStr, PurchaseExtInfo.class);

        if (extInfo != null && StringUtils.isNotBlank(extInfo.getTag())) {
            activityInfo.setTag(extInfo.getTag());
        }

        if (extInfo != null && extInfo.getReduce() != null) {
            activityInfo.setReduceDiscount(extInfo.getReduce().getReduce());
            activityInfo.setMaxReduce(extInfo.getReduce().getMaxReduce());
            activityInfo.setSubsidyMode(extInfo.getSubsidyMode());
            activityInfo.setActivityUrl(extInfo.getActivityUrl());
            activityInfo.setActPayTag(extInfo.getActPayTag());
            activityInfo.setInvoiceCompanyId(extInfo.getInvoiceCompanyId());
        }

        activityInfo.setSkuList(goodsPos.stream().map(NrActGoodsPo::getProductId).collect(Collectors.toList()));
        Map<Integer, String> keyConvertedMap = new HashMap<>(16);
        if (MapUtil.isNotEmpty(nacosConfig.getSubsidyCateCodeMap())) {
            Map<String, String> subsidyCateCodeMap = nacosConfig.getSubsidyCateCodeMap();
            keyConvertedMap = subsidyCateCodeMap.entrySet().stream()
                    .collect(Collectors.toMap(entry -> Integer.valueOf(entry.getKey()), Map.Entry::getValue));
        }
        Map<Integer, String> finalCateCodeMap = keyConvertedMap;
        activityInfo.setGroupMap(goodsPos.stream().collect(Collectors.toMap(NrActGoodsPo::getProductId, po -> finalCateCodeMap.getOrDefault(po.getGroup(), ""))));

        activityInfo.setOrgList(extInfo.getScope());
        if (StringUtils.isNotBlank(extInfo.getCateLevel())) {
            activityInfo.setCateLevel(extInfo.getCateLevel());
        }

        activityInfo.setActivityRegion(extInfo.getRegionList());

        if (extInfo.getPaymentAccess() != null) {
            activityInfo.setPaymentAccess(extInfo.getPaymentAccess());
        }

        if (extInfo.getInvoiceRule() != null) {
            activityInfo.setInvoiceRule(extInfo.getInvoiceRule());
        }

        if (extInfo.getReportCity() != null) {
            activityInfo.setReportCity(extInfo.getReportCity());
        }

        if (StringUtils.isNotBlank(extInfo.getTag())) {
            activityInfo.setTag(extInfo.getTag());
        }

        Map<Long, String> skuCateLevel = Maps.newHashMap();
        Map<Long, String> skuDiscountCode = Maps.newHashMap();
        Map<Long, String> skuCategoryName = Maps.newHashMap();
        Map<Long, String> sku69Code = Maps.newHashMap();
        Map<Long, String> groupMap = Maps.newHashMap();
        Map<Long, String> unionPayActivityIdMap = Maps.newHashMap();
        Map<Long, String> brandMap = Maps.newHashMap();
        Map<Long, String> itemNameMap = Maps.newHashMap();
        Map<Long, String> specModelMap = Maps.newHashMap();
        for (NrActGoodsPo goodsPo : goodsPos) {
            PurchaseGoodRule rule = GsonUtil.fromJson(goodsPo.getRule(), PurchaseGoodRule.class);
            if (Objects.isNull(rule)){
                continue;
            }
            if (StringUtils.isNotBlank(rule.getCateLevel())) {
                skuCateLevel.put(goodsPo.getProductId(), rule.getCateLevel());
            }
            if (Objects.nonNull(activityInfo.getSubsidyMode())) {
                if (SubsidyModeEnum.GUANGDONG_MODE.getValue() == activityInfo.getSubsidyMode()) {
                    if (StringUtils.isNotBlank(rule.getCategoryName())) {
                        skuCategoryName.put(goodsPo.getProductId(), rule.getCategoryName());
                    }
                    if (StringUtils.isNotBlank(rule.getCustomCateCode())) {
                        groupMap.put(goodsPo.getProductId(), rule.getCustomCateCode());
                    }
                    if (StringUtils.isNotBlank(rule.getCode69())) {
                        sku69Code.put(goodsPo.getProductId(), rule.getCode69());
                    }
                    if (StringUtils.isNotBlank(rule.getBrand())) {
                        brandMap.put(goodsPo.getProductId(), rule.getBrand());
                    }
                    if (StringUtils.isNotBlank(rule.getItemName())) {
                        itemNameMap.put(goodsPo.getProductId(), rule.getItemName());
                    }
                    if (StringUtils.isNotBlank(rule.getSpecModel())) {
                        specModelMap.put(goodsPo.getProductId(), rule.getSpecModel());
                    }
                }
                if (SubsidyModeEnum.JIJIAN_MODE.getValue() == activityInfo.getSubsidyMode()) {
                    // 极简模式下的特殊字段
                    if (StringUtils.isNotBlank(rule.getDiscountCode())) {
                        skuDiscountCode.put(goodsPo.getProductId(), rule.getDiscountCode());
                    }
                    if (StringUtils.isNotBlank(rule.getCategoryName())) {
                        skuCategoryName.put(goodsPo.getProductId(), rule.getCategoryName());
                    }
                    if (StringUtils.isNotBlank(rule.getCode69())) {
                        sku69Code.put(goodsPo.getProductId(), rule.getCode69());
                    }
                    if (StringUtils.isNotBlank(rule.getCustomCateCode())) {
                        groupMap.put(goodsPo.getProductId(), rule.getCustomCateCode());
                    }
                    if (StringUtils.isNotBlank(rule.getUnionPayActivityId())) {
                        unionPayActivityIdMap.put(goodsPo.getProductId(), rule.getUnionPayActivityId());
                    }
                }
            }
        }
        activityInfo.setSkuCateLevel(skuCateLevel);
        if (Objects.nonNull(activityInfo.getSubsidyMode())) {
            if (SubsidyModeEnum.GUANGDONG_MODE.getValue() == activityInfo.getSubsidyMode()) {
                activityInfo.setSkuCategoryName(skuCategoryName);
                activityInfo.setGroupMap(groupMap);
                activityInfo.setSku69Code(sku69Code);
                activityInfo.setBrandMap(brandMap);
                activityInfo.setItemNameMap(itemNameMap);
                activityInfo.setSpecModelMap(specModelMap);
            }
            if (SubsidyModeEnum.JIJIAN_MODE.getValue() == activityInfo.getSubsidyMode()) {
                activityInfo.setSkuDiscountCode(skuDiscountCode);
                activityInfo.setSkuCategoryName(skuCategoryName);
                activityInfo.setSku69Code(sku69Code);
                activityInfo.setGroupMap(groupMap);
                activityInfo.setSkuUnionPayActivityId(unionPayActivityIdMap);
            }
        }
        if (StringUtils.isNotBlank(extInfo.getRegistrationEntity())) {
            activityInfo.setRegistrationEntity(extInfo.getRegistrationEntity());
        }

        return activityInfo;
    }

    /**
     * 检查给定的组织代码对应的活动列表是否不为空
     *
     * @param orgCode 组织代码
     * @return 如果活动列表不为空则返回true，否则返回false
     */
    public Boolean OrgCanJoin(String orgCode) {
        return CollectionUtils.isNotEmpty(ORG_CODE_ACT_LIST_CACHE.get(orgCode));
    }

    /**
     * 根据SKU获取活动ID列表
     *
     * @param sku SKU编号
     * @return 活动ID列表
     */
    public List<Long> getActivityId(Long sku) {
        return ACT_ID_LIST_CACHE.get(sku);
    }

    /**
     * 根据活动ID获取活动信息
     *
     * @param id 活动ID
     * @return 对应的活动信息
     */
    public PurchaseActivityInfo getActivityInfo(Long id) {
        return ACT_INFO_LIST_CACHE.get(id);
    }

    public List<Long> getActivityIdByChannel(Integer channel) {
        return CHANNEL_ACT_LIST_CACHE.get(channel);
    }

    public List<Long> getActivityIdByOrgCode(String orgCode) {
        return ORG_CODE_ACT_LIST_CACHE.get(orgCode);
    }

    /**
     * 根据SKU列表获取活动ID列表
     *
     * @param skuList SKU列表
     * @return 与所有SKU相关的活动ID列表
     */
    public List<Long> getActivityIdBySku(List<Long> skuList) {
        Set<Long> activityIds = new HashSet<>();
        for (Long sku : skuList) {
            List<Long> ids = ACT_ID_LIST_CACHE.getOrDefault(sku, Lists.newArrayList());
            activityIds.addAll(ids);
        }

        return new ArrayList<>(activityIds);
    }


    /**
     * 检查SKU列表中的所有SKU是否都存在于缓存中
     *
     * @param skuList SKU的ID列表
     * @return 如果所有SKU都存在于缓存中则返回true，否则返回false
     */
    public Boolean skuCanJoin(List<Long> skuList) {
        return skuList.stream().allMatch(ACT_ID_LIST_CACHE::containsKey);
    }

    public Boolean channelCanJoin(Integer channel) {
        return CollectionUtils.isNotEmpty(CHANNEL_ACT_LIST_CACHE.get(channel));
    }
}
