<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionUserActivityCountMapper">
    <select id="getByUserIdAndPromotionIds" resultType="com.xiaomi.nr.promotion.entity.mysql.promotionuser.UserActivityCount">
        select *
        from promotion_user_activity_count
        where user_id = #{mid} and promotion_id in
        <foreach collection="promotionIds" item="pId" index="index" open="(" separator="," close=")">
            #{pId}
        </foreach>
        order by id desc limit 1
    </select>
</mapper>