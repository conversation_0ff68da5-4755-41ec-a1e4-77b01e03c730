<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaomi.nr.promotion.dao.mysql.promotionuser.RedpacketMapper">
    <update id="batchUpdateRPBalanceToZero">
        update tb_rp_redpacket
        set balance = 0, update_time = #{nowTime}
        where user_id = #{userId} and
        <foreach collection="rpBalanceList" item="rpBalance" index="index" open="(" separator="OR" close=")">
            (redpacket_id = #{rpBalance.redpacketId} and balance = #{rpBalance.oldBalance})
        </foreach>
    </update>
    <insert id="batchCreateRedpacketLog">
        insert into tb_rp_log
        (redpacket_id, user_id, order_id, add_time, income, old_balance, new_balance, op_type)
        values
        <foreach collection="rpLogList" item="rpLog" separator=",">
            (#{rpLog.redpacketId}, #{rpLog.userId}, #{rpLog.orderId}, #{rpLog.addTime}, #{rpLog.income}, #{rpLog.oldBalance}, #{rpLog.newBalance}, #{rpLog.opType})
        </foreach>
    </insert>
    <select id="findRedpacketsForUpdate" resultType="com.xiaomi.nr.promotion.domain.redpackage.model.RedPacket">
        select user_id, amount , balance , start_time ,end_time , redpacket_id, type_id
        from tb_rp_redpacket  WHERE  user_id = #{userId} AND redpacket_id  in
        <foreach collection="rpIds" item="rpId" index="index" open="(" separator="," close=")">
            #{rpId}
        </foreach>
        for update
    </select>
    <update id="loadUserBatchUpdateRPBalanceToZero">
        update tb_rp_redpacket
        set update_time = #{nowTime}
        where user_id = #{userId} and
        <foreach collection="rpBalanceList" item="rpBalance" index="index" open="(" separator="OR" close=")">
            (redpacket_id = #{rpBalance.redpacketId} and balance = #{rpBalance.oldBalance})
        </foreach>
    </update>
    <select id="getRedPacketListByIds" resultType="com.xiaomi.nr.promotion.domain.redpackage.model.RedPacket">
        select user_id, amount , balance , start_time ,end_time , redpacket_id, type_id
        from tb_rp_redpacket  WHERE  user_id = #{userId} AND redpacket_id  in
        <foreach collection="redPacketIds" item="rpId" index="index" open="(" separator="," close=")">
            #{rpId}
        </foreach>
    </select>
</mapper>