<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaomi.nr.promotion.dao.mysql.mdpromotion.ActivityMapper">
    <select id="queryByIds" resultType="com.xiaomi.nr.promotion.dao.mysql.mdpromotion.ActivityEntity">
        select id, name, promotion_type, rule
        from t_prom_activity
        where id in
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
        and ac_status = 1
        and is_deleted = 0
    </select>
</mapper>