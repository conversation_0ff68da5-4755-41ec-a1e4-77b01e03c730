<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>promotion</artifactId>
        <groupId>com.xiaomi.nr</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>promotion-service</artifactId>

    <properties>
    	<maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <artifactId>promotion-common</artifactId>
            <groupId>com.xiaomi.nr</groupId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>promotion-api</artifactId>
        </dependency>

        <!--   Dubbo  RPC   -->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>

        <!--    Thrift RPC   -->
        <dependency>
            <groupId>org.mi</groupId>
            <artifactId>thrift</artifactId>
        </dependency>

        <!-- 数据库相关 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>

        <!--   Redis     -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.lettuce</groupId>
                    <artifactId>lettuce-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--framework-->
        <dependency>
            <groupId>com.xiaomi.nr-promotion</groupId>
            <artifactId>framework</artifactId>
            <version>0.0.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--mq-rocketmq-->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.2.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot</artifactId>
            <version>2.2.2</version>
            <scope>compile</scope>
        </dependency>

        <!--    公共依赖库       -->
        <dependency>
            <groupId>javax.el</groupId>
            <artifactId>javax.el-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>aop-utils</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.xiaomi</groupId>
                    <artifactId>passportsdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>

        <!--   Http Client     -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <!--   人群服务     -->
        <dependency>
            <artifactId>retail-data-user-tag-service-rpc-api</artifactId>
            <groupId>com.xiaomi.retail</groupId>
        </dependency>

        <!--   gis 服务     -->
        <dependency>
            <artifactId>gis-api</artifactId>
            <groupId>com.xiaomi.goods</groupId>
        </dependency>

        <!--   order 服务     -->
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>order-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.xiaomi.nr.order</groupId>
                    <artifactId>common-interceptor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- coupon服务 -->
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>dubbo-docs-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo</artifactId>
                    <groupId>org.apache.dubbo</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>coupon-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.xiaomi.nr</groupId>
                    <artifactId>xiaomi-dubbo-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- miapi -->
        <dependency>
            <groupId>com.xiaomi.mone</groupId>
            <artifactId>dubbo-docs-core</artifactId>
        </dependency>


        <!--  三方优惠服务   -->
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>phoenix-api</artifactId>
        </dependency>
        <!-- 换新中台 -->
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>recycle-api</artifactId>
        </dependency>
        <!--   mit     -->
        <dependency>
            <groupId>com.xiaomi.mit</groupId>
            <artifactId>mit-starter</artifactId>
        </dependency>
        <!-- 新促销后台 -->
        <dependency>
            <groupId>com.xiaomi.nr.md.promotion.admin</groupId>
            <artifactId>md-promotion-admin-api</artifactId>
        </dependency>
        <!--  ecard服务   -->
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>ecard-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>xiaomi-dubbo-validator</artifactId>
                    <groupId>com.xiaomi.nr</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--  aries服务   -->
        <dependency>
            <groupId>com.xiaomi.youpin</groupId>
            <artifactId>aries-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>xiaomi-dubbo-validator</artifactId>
                    <groupId>com.xiaomi.nr</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--  维保策略系统   -->
        <dependency>
            <groupId>com.xiaomi.nr</groupId>
            <artifactId>mro-policy-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>xiaomi-dubbo-validator</artifactId>
                    <groupId>com.xiaomi.nr</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.micar</groupId>
            <artifactId>club-api</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
            <version>2.1.4.1-mone-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.mi.car.iccc</groupId>
            <artifactId>iccc-user-permit-common</artifactId>
        </dependency>

        <!--  iauth校验   -->
        <dependency>
            <groupId>com.xiaomi</groupId>
            <artifactId>xiaomi-iauth-java-sdk</artifactId>
        </dependency>
    </dependencies>
</project>