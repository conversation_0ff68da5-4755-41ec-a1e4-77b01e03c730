#server
app.name=promotion
biz.area.id.list=CAR
server.type=online
server.port=8080
server.debug=true
server.connection-timeout=1000
dubbo.group=car_online_noreq
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.registry.address=nacos://nacos.systech.b2c.srv:80
nacos.config.addrs=nacos.systech.b2c.srv:80
youpin.log.group=online
log.path=/home/<USER>/log

# redis config for redis_misc
redis.config.promotion.host=cache01.b2c.srv
redis.config.promotion.port=5100
redis.config.promotion.password=cn_mishop_karos_misc_87FRR9kYbOYuN
redis.config.promotion.timeout=5000
redis.config.promotion.database=0
redis.config.promotion.jedis-pool.max-idle=50
redis.config.promotion.jedis-pool.min-idle=20
redis.config.promotion.jedis-pool.max-total=50
redis.config.promotion.jedis-pool.test-while-idle=true
redis.config.promotion.jedis-pool.time-between-eviction-runs-millis=30000
redis.config.promotion.jedis-pool.min-evictable-idle-time-millis=150000
redis.config.promotion.jedis-pool.num-tests-per-eviction-run=-1
redis.config.promotion.jedis-pool.max-wait-millis=3000
#redis.config.promotion.lettuce-pool.max-idle=200
#redis.config.promotion.lettuce.pool.min-idle=0
#redis.config.promotion.lettuce-pool.max-total=200
#redis.config.promotion.lettuce-pool.timeout=5000
#redis.config.promotion.lettuce-pool.shutdown-timeOut=3000
#redis.config.promotion.lettuce.pool.max-wait-millis=3000


redis.config.promotionfinal.host=ares.b2c.cache.b2c.srv
redis.config.promotionfinal.port=5100
redis.config.promotionfinal.password=GDAreeKqBwsnkTJoGKLWu8CyxosoNs6TxKzUZvdIoaUuB4LK04i4HN4mK0nNW8Z2AQcYEkUtR/NdO0o0vlh/w6dBhZj7ARgQql2ruY/GS1aVoWjdMsdcdxgUfMdN6YVTux3JEEhs+gU0I4maWRoA
redis.config.promotionfinal.password@kc-sid=mi_newretail.g
redis.config.promotionfinal.timeout=5000
redis.config.promotionfinal.database=0
redis.config.promotionfinal.jedis-pool.max-idle=50
redis.config.promotionfinal.jedis-pool.min-idle=20
redis.config.promotionfinal.jedis-pool.max-total=50
redis.config.promotionfinal.jedis-pool.test-while-idle=true
redis.config.promotionfinal.jedis-pool.time-between-eviction-runs-millis=30000
redis.config.promotionfinal.jedis-pool.min-evictable-idle-time-millis=150000
redis.config.promotionfinal.jedis-pool.num-tests-per-eviction-run=-1
redis.config.promotionfinal.jedis-pool.max-wait-millis=3000


# redis config for act_cache
redis.config.actcache.host=cache01.b2c.srv
redis.config.actcache.port=5100
redis.config.actcache.password=cn_mishop_karos_misc_87FRR9kYbOYuN
redis.config.actcache.timeout=5000
redis.config.actcache.database=0
redis.config.actcache.jedis-pool.max-idle=50
redis.config.actcache.jedis-pool.min-idle=20
redis.config.actcache.jedis-pool.max-total=50
redis.config.actcache.jedis-pool.test-while-idle=true
redis.config.actcache.jedis-pool.time-between-eviction-runs-millis=30000
redis.config.actcache.jedis-pool.min-evictable-idle-time-millis=150000
redis.config.actcache.jedis-pool.num-tests-per-eviction-run=-1
redis.config.actcache.jedis-pool.max-wait-millis=3000
#redis.config.actcache.lettuce-pool.max-idle=8
#redis.config.actcache.lettuce.pool.min-idle=1
#redis.config.actcache.lettuce-pool.max-total=200
#redis.config.actcache.lettuce-pool.timeout=3000
#redis.config.actcache.lettuce-pool.shutdown-timeOut=3000
#redis.config.actcache.lettuce.pool.max-wait-millis=3000

# redis config for redis_act
redis.config.act.host=cache01.b2c.srv
redis.config.act.port=5100
redis.config.act.password=cn_mishop_redis_common_storage_STLSts0KcEh8CDAj
redis.config.act.timeout=5000
redis.config.act.database=0
redis.config.act.jedis-pool.max-idle=50
redis.config.act.jedis-pool.min-idle=20
redis.config.act.jedis-pool.max-total=50
redis.config.act.jedis-pool.test-while-idle=true
redis.config.act.jedis-pool.time-between-eviction-runs-millis=30000
redis.config.act.jedis-pool.min-evictable-idle-time-millis=150000
redis.config.act.jedis-pool.num-tests-per-eviction-run=-1
redis.config.act.jedis-pool.max-wait-millis=3000
#redis.config.act.lettuce-pool.max-idle=200
#redis.config.act.lettuce.pool.min-idle=0
#redis.config.act.lettuce-pool.max-total=200
#redis.config.act.lettuce-pool.timeout=5000
#redis.config.act.lettuce-pool.shutdown-timeOut=3000
#redis.config.act.lettuce.pool.max-wait-millis=3000

# redis config for coupon
redis.config.coupon.host=cache01.b2c.srv
redis.config.coupon.port=5100
redis.config.coupon.password=cn_mishop_xiaomi_pulse_Fpi1yxEjfeLh
redis.config.coupon.timeout=5000
redis.config.coupon.database=0
redis.config.coupon.jedis-pool.max-idle=50
redis.config.coupon.jedis-pool.min-idle=20
redis.config.coupon.jedis-pool.max-total=50
redis.config.coupon.jedis-pool.test-while-idle=true
redis.config.coupon.jedis-pool.time-between-eviction-runs-millis=30000
redis.config.coupon.jedis-pool.min-evictable-idle-time-millis=150000
redis.config.coupon.jedis-pool.num-tests-per-eviction-run=-1
redis.config.coupon.jedis-pool.max-wait-millis=3000
#redis.config.coupon.lettuce-pool.max-idle=200
#redis.config.coupon.lettuce.pool.min-idle=0
#redis.config.coupon.lettuce-pool.max-total=200
#redis.config.coupon.lettuce-pool.timeout=5000
#redis.config.coupon.lettuce-pool.shutdown-timeOut=3000
#redis.config.coupon.lettuce.pool.max-wait-millis=3000

#redis config for redpacket
redis.config.redpacket.host=cache01.b2c.srv
redis.config.redpacket.port=5100
redis.config.redpacket.password=cn_mishop_xiaomi_pulse_Fpi1yxEjfeLh
redis.config.redpacket.timeout=5000
redis.config.redpacket.database=0
redis.config.redpacket.jedis-pool.max-idle=50
redis.config.redpacket.jedis-pool.min-idle=20
redis.config.redpacket.jedis-pool.max-total=50
redis.config.redpacket.jedis-pool.test-while-idle=true
redis.config.redpacket.jedis-pool.time-between-eviction-runs-millis=30000
redis.config.redpacket.jedis-pool.min-evictable-idle-time-millis=150000
redis.config.redpacket.jedis-pool.num-tests-per-eviction-run=-1
redis.config.redpacket.jedis-pool.max-wait-millis=3000
#redis.config.redpacket.lettuce-pool.max-idle=200
#redis.config.redpacket.lettuce.pool.min-idle=0
#redis.config.redpacket.lettuce-pool.max-total=200
#redis.config.redpacket.lettuce-pool.timeout=5000
#redis.config.redpacket.lettuce-pool.shutdown-timeOut=3000
#redis.config.redpacket.lettuce.pool.max-wait-millis=3000

# mysql promotion config
spring.promotionconfig-datasource.name=xm_pulse
spring.promotionconfig-datasource.username=misho_pulse_wn
spring.promotionconfig-datasource.url=***********************************************************************************
spring.promotionconfig-datasource.password=
spring.promotionconfig-datasource.connectionProperties=password=GDAJiFSgZ662dvsbbhPgpyWJ1XYae1aB/yil+LIjcp6Oy8ss95KrxTh3vd3wP4TvUh8YEv//AkuzbkEekSGlvzziOKOfARgQ45b/qz60SqmJ6GUCUxFkVBgUiXKIE+1ZlHvv+fLMZKs6x5eAFjwA;keycenter-sid=mi_newretail.g
spring.promotionconfig-datasource.sql-script-encoding=UTF-8
spring.promotionconfig-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.promotionconfig-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.promotionconfig-datasource.initial-size=5
spring.promotionconfig-datasource.max-active=300
spring.promotionconfig-datasource.min-idle=3
spring.promotionconfig-datasource.max-wait=500
spring.promotionconfig-datasource.remove-abandoned=true
spring.promotionconfig-datasource.remove-abandoned-timeout=180
spring.promotionconfig-datasource.time-between-eviction-runs-millis=60000
spring.promotionconfig-datasource.min-evictable-idle-time-millis=300000
spring.promotionconfig-datasource.validation-query=SELECT 1 FROM DUAL
spring.promotionconfig-datasource.test-while-idle=true
spring.promotionconfig-datasource.test-on-borrow=false
spring.promotionconfig-datasource.test-on-return=false
spring.promotionconfig-datasource.pool-prepared-statements=true
spring.promotionconfig-datasource.max-pool-prepared-statement-per-connection-size=50
spring.promotionconfig-datasource.filters=stat,wall,log4j,com.xiaomi.nr.infra.aop.keycenter.KeycenterPasswordFilter

# mysql nractivity config
spring.nractivityconfig-datasource.name=nr_activity_admin
spring.nractivityconfig-datasource.username=nr_activity_admin_wn
spring.nractivityconfig-datasource.url=*********************************************************************************************************************************************************
spring.nractivityconfig-datasource.password=
spring.nractivityconfig-datasource.connectionProperties=password=GDCXA3hiTgZuU9e5T15sS/sbmWewBSScdFfWt3zLZmori8x2NIvuVyeaqGG7vwIYM9EYEg5hXcC47UtSh/z789yak8RiARgQkinQrC5yTcezpOjQ+7kYChgUBLARffIvWPjl+vNcX+AYq0R/IdIA;keycenter-sid=mi_newretail_risk.g
spring.nractivityconfig-datasource.sql-script-encoding=UTF-8
spring.nractivityconfig-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.nractivityconfig-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.nractivityconfig-datasource.initial-size=5
spring.nractivityconfig-datasource.max-active=10
spring.nractivityconfig-datasource.min-idle=3
spring.nractivityconfig-datasource.max-wait=600000
spring.nractivityconfig-datasource.remove-abandoned=true
spring.nractivityconfig-datasource.remove-abandoned-timeout=180
spring.nractivityconfig-datasource.time-between-eviction-runs-millis=600000
spring.nractivityconfig-datasource.min-evictable-idle-time-millis=300000
spring.nractivityconfig-datasource.validation-query=SELECT 1 FROM DUAL
spring.nractivityconfig-datasource.test-while-idle=true
spring.nractivityconfig-datasource.test-on-borrow=false
spring.nractivityconfig-datasource.test-on-return=false
spring.nractivityconfig-datasource.pool-prepared-statements=true
spring.nractivityconfig-datasource.max-pool-prepared-statement-per-connection-size=50
spring.nractivityconfig-datasource.filters=stat,wall,log4j,com.xiaomi.nr.infra.aop.keycenter.KeycenterPasswordFilter

# mysql promotion user config
spring.promotionuserconfig-datasource.name=xm_pulse
spring.promotionuserconfig-datasource.username=misho_pulse_wn
spring.promotionuserconfig-datasource.url=*******************************************************************************************
spring.promotionuserconfig-datasource.password=
spring.promotionuserconfig-datasource.connectionProperties=password=GDCdv2Vyj6Lw53sSi/AwJDysexNhv/BA6qlkhsPcRLsI7zG+k24qw3ecbrPg1RHm5zAYEu79r191ZUzBs4XNyjTUKazBARgQULCf4vGKSp+/iUzO2CwiDhgUPfrm4T6xI+Q0kE3IeJaeZet3kSIA;keycenter-sid=mi_newretail.g
spring.promotionuserconfig-datasource.sql-script-encoding=UTF-8
spring.promotionuserconfig-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.promotionuserconfig-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.promotionuserconfig-datasource.initial-size=5
spring.promotionuserconfig-datasource.max-active=300
spring.promotionuserconfig-datasource.min-idle=3
spring.promotionuserconfig-datasource.max-wait=500
spring.promotionuserconfig-datasource.remove-abandoned=true
spring.promotionuserconfig-datasource.remove-abandoned-timeout=180
spring.promotionuserconfig-datasource.time-between-eviction-runs-millis=60000
spring.promotionuserconfig-datasource.min-evictable-idle-time-millis=300000
spring.promotionuserconfig-datasource.validation-query=SELECT 1 FROM DUAL
spring.promotionuserconfig-datasource.test-while-idle=true
spring.promotionuserconfig-datasource.test-on-borrow=false
spring.promotionuserconfig-datasource.test-on-return=false
spring.promotionuserconfig-datasource.pool-prepared-statements=true
spring.promotionuserconfig-datasource.max-pool-prepared-statement-per-connection-size=50
spring.promotionuserconfig-datasource.filters=stat,wall,log4j,com.xiaomi.nr.infra.aop.keycenter.KeycenterPasswordFilter

# mysql fcode config
spring.fcodeconfig-datasource.name=xm_shop_fcode
spring.fcodeconfig-datasource.username=misho_fc_wn
spring.fcodeconfig-datasource.url=*************************************************************************************
spring.fcodeconfig-datasource.password=
spring.fcodeconfig-datasource.connectionProperties=password=GDCUjvXkFsAiQz3gh3gtW3ucg1E9r0h5/lKkP8mPMp2H+/8yhfgn6m4uUe6SwOQr4q4YEoLsFvkXakLrjM30bfIH4RqrARgQN4+JFB7GTcqMhYUxmqBt6hgUeXGvH3l1iv0ZUacxc6T+Vt6WB+wA;keycenter-sid=mi_newretail.g
spring.fcodeconfig-datasource.sql-script-encoding=UTF-8
spring.fcodeconfig-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.fcodeconfig-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.fcodeconfig-datasource.initial-size=5
spring.fcodeconfig-datasource.max-active=100
spring.fcodeconfig-datasource.min-idle=3
spring.fcodeconfig-datasource.max-wait=600000
spring.fcodeconfig-datasource.remove-abandoned=true
spring.fcodeconfig-datasource.remove-abandoned-timeout=180
spring.fcodeconfig-datasource.time-between-eviction-runs-millis=60000
spring.fcodeconfig-datasource.min-evictable-idle-time-millis=300000
spring.fcodeconfig-datasource.validation-query=SELECT 1 FROM DUAL
spring.fcodeconfig-datasource.test-while-idle=true
spring.fcodeconfig-datasource.test-on-borrow=false
spring.fcodeconfig-datasource.test-on-return=false
spring.fcodeconfig-datasource.pool-prepared-statements=true
spring.fcodeconfig-datasource.max-pool-prepared-statement-per-connection-size=50
spring.fcodeconfig-datasource.filters=stat,wall,log4j,com.xiaomi.nr.infra.aop.keycenter.KeycenterPasswordFilter


# md_promotion
spring.mdpromotion-datasource.name=md_promotion_wn
spring.mdpromotion-datasource.username=md_promotion_wn
spring.mdpromotion-datasource.url=*********************************************************************************************
spring.mdpromotion-datasource.password=
spring.mdpromotion-datasource.connectionProperties=password=GDC2W7bpOt6rhbZV+2PTc8DaJeseEehoIVLqGZpB5MoDdppUbAJQmDb2jgUH8+fO9LQYEj+6G0oTmkVVvB4Zefz4jA44ARgQr4PzYoEeRFSYVrPnKn+Z2BgUaQc9fp+M9E9JOoQEm5iwRwwP27YA;keycenter-sid=mi_newretail.g
spring.mdpromotion-datasource.sql-script-encoding=UTF-8
spring.mdpromotion-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.mdpromotion-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.mdpromotion-datasource.initial-size=5
spring.mdpromotion-datasource.max-active=10
spring.mdpromotion-datasource.min-idle=3
spring.mdpromotion-datasource.max-wait=600000
spring.mdpromotion-datasource.remove-abandoned=true
spring.mdpromotion-datasource.remove-abandoned-timeout=180
spring.mdpromotion-datasource.time-between-eviction-runs-millis=600000
spring.mdpromotion-datasource.min-evictable-idle-time-millis=300000
spring.mdpromotion-datasource.validation-query=SELECT 1 FROM DUAL
spring.mdpromotion-datasource.test-while-idle=true
spring.mdpromotion-datasource.test-on-borrow=false
spring.mdpromotion-datasource.test-on-return=false
spring.mdpromotion-datasource.pool-prepared-statements=true
spring.mdpromotion-datasource.max-pool-prepared-statement-per-connection-size=50
spring.mdpromotion-datasource.filters=stat,wall,com.xiaomi.nr.infra.aop.keycenter.KeycenterPasswordFilter

configHost=http://soa01.etcd.b2c.srv:4001
appGroup=mishop
serviceName=promotion

# 人群
user.dubbo.group=c3
userTagToken=GDAFICPVH9AqRQWGMVDyJDuUuCc4ctBr9XrOpVYLpYeUQlgpCFRWdlEj9w0rIcjawUEYEsIH09k3skNNnIgXsGdYS/9+ARgQIINFR2HeTWCnKHrjhiksExgUqbSZ8vCB1mtgIN/SBwm27Ze18QsA
userTagToken@kc-sid=mi_newretail.g

logging.level=debug

# 订单服务
order.dubbo.group=online
vipmember.dubbo.group=online
# gis
gis.dubbo.version=0.1
gis.dubbo.group=car_online
# phoenix
phoenix.dubbo.group=online

#卡劵服务
coupon.dubbo.version=1.0
coupon.dubbo.group=car_online
coupon.appId=XM2108
coupon.secret=GDDvUlbtaq552DkswITKGcR+IvBgTSJgDJqJHb3WjD6YZ1vy1cQtIlGAfqed9NodiEoYEqyRWx6bSko4usZBPjSpxRhEARgQzTj6rCYkSHGL2wBL9+FXSBgUQePtTU2hnRrmVOdH4RchCT5n1bkA
coupon.secret@kc-sid=mi_newretail.g

# [consumer_coupon]消费券配置 [consumer_coupon] 消费券可用区域，空代表没有，券配置ID:区域用逗号分隔(P_代表省份ID，C_代表城市ID)
coupon.region.consumer=26371:C_36

# rocketMq
rocketmq.name-server=cnbj1-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.producer.group=promotion-producer-prod
rocketmq.producer.access-key=AKT7P4VWPIPO4PGMPI
rocketmq.producer.secret-key=GDDLPIt7D9m1zqKeVbccQvP011UgBxi81PGJr52qlXRqKxCnksuNWxlP9+5YQXngD4sYEqvNaoAQ0UnAudy5MuFXPOo5ARgQpOQzwjk4TFOi+xAcd53sGhgU+xy9TyBie//wwMgbO3jqTfx3D14A
rocketmq.producer.secret-key@kc-sid=mi_newretail.g
mq.topic.actstock=nr_promotion_act_stock
rocketmq.carActivity.name-server=shopapi-cnbj1-rocketmq.namesrv.api.xiaomi.net:9876
mq.topic.carActivity=nr_car_activity_use_message
mq.topic.carActivity.tag=vipMember
mq.topic.carEquity=car_equity_performance
mq.topic.carEquity.tag=ACTIVITY

# order
rocketmq.access-key=AKT7P4VWPIPO4PGMPI
rocketmq.secret-key=GDDLPIt7D9m1zqKeVbccQvP011UgBxi81PGJr52qlXRqKxCnksuNWxlP9+5YQXngD4sYEqvNaoAQ0UnAudy5MuFXPOo5ARgQpOQzwjk4TFOi+xAcd53sGhgU+xy9TyBie//wwMgbO3jqTfx3D14A
rocketmq.secret-key@kc-sid=mi_newretail.g
rocketmq.order.name-server=shopapi-cnbj1-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.topic.order=nr_message_bus
rocketmq.topic.order.consumerGroup=nr_message_bus_consumer_promotion_pro
rocketmq.topic.order.enabled=false
rocketmq.topic.order.offline=nr_message_bus_offline
rocketmq.topic.order.offline.consumerGroup=nr_message_bus_offline_consumer_promotion_pro
rocketmq.topic.order.offline.enabled=false
rocketmq.topic.order.carLife=nr_message_bus_car_life
rocketmq.topic.order.carLife.consumerGroup=nr_message_bus_car_life_consumer_promotion_pro
rocketmq.topic.order.carLife.enabled=true
rocketmq.topic.order.car=nr_message_bus_car
rocketmq.topic.order.car.consumerGroup=nr_message_bus_car_consumer_promotion_pro
rocketmq.topic.order.car.enabled=true
rocketmq.topic.order.afterSale=nr_message_bus_car_aftersale
rocketmq.topic.order.afterSale.consumerGroup=nr_message_bus_aftersale_consumer_promotion_pro
rocketmq.topic.order.afterSale.enabled=true
rocketmq.topic.promotion.notify.broadcast=CI105884_MD_Promotion_Notify_BroadCast
rocketmq.consumer.group.promotion.notify.broadcast=CI105884_MD_Promotion_Notify_BroadCast_Consumer_Group

# recycle
recycle.dubbo.group=online

#mdpromotionadmin
md.promotion.admin.dubbo.group=car_online

#礼品卡服务
ecard.dubbo.version=1.0
ecard.dubbo.group=online

mro.policy.dubbo.group=car_online


#sentinel
sentinel.dashboard.host=http://sentinel.be.mi.com

#aries
aries.dubbo.version=1.0
aries.dubbo.group=car_online

#user.permit
user.permit.url=https://inner-partner.iccc.xiaomiev.com
