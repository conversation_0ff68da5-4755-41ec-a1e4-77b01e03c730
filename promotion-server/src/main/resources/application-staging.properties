#server
app.name=promotion
biz.area.id.list=CAR
server.type=staging
server.port=8080
server.debug=true
server.connection-timeout=1000
dubbo.group=staging
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.registry.address=nacos://mione-staging-nacos.api.xiaomi.net:80
nacos.config.addrs=mione-staging-nacos.api.xiaomi.net:80
youpin.log.group=staging
log.path=/home/<USER>/log


# redis config
# redis config for redis_misc
redis.config.promotion.host=wcc.cache01.test.b2c.srv
redis.config.promotion.port=22122
redis.config.promotion.password=shopapi_misc
redis.config.promotion.timeout=5000
redis.config.promotion.database=0
redis.config.promotion.jedis-pool.max-idle=50
redis.config.promotion.jedis-pool.min-idle=20
redis.config.promotion.jedis-pool.max-total=50
redis.config.promotion.jedis-pool.test-while-idle=true
redis.config.promotion.jedis-pool.time-between-eviction-runs-millis=30000
redis.config.promotion.jedis-pool.min-evictable-idle-time-millis=150000
redis.config.promotion.jedis-pool.num-tests-per-eviction-run=-1
redis.config.promotion.jedis-pool.max-wait-millis=3000
#redis.config.promotion.lettuce-pool.max-idle=8
#redis.config.promotion.lettuce.pool.min-idle=1
#redis.config.promotion.lettuce-pool.max-total=200
#redis.config.promotion.lettuce-pool.timeout=3000
#redis.config.promotion.lettuce-pool.shutdown-timeOut=3000
#redis.config.promotion.lettuce.pool.max-wait-millis=3000


redis.config.promotionfinal.host=wcc.cache01.test.b2c.srv
redis.config.promotionfinal.port=22122
redis.config.promotionfinal.password=shopapi_misc
redis.config.promotionfinal.timeout=5000
redis.config.promotionfinal.database=0
redis.config.promotionfinal.jedis-pool.max-idle=50
redis.config.promotionfinal.jedis-pool.min-idle=20
redis.config.promotionfinal.jedis-pool.max-total=50
redis.config.promotionfinal.jedis-pool.test-while-idle=true
redis.config.promotionfinal.jedis-pool.time-between-eviction-runs-millis=30000
redis.config.promotionfinal.jedis-pool.min-evictable-idle-time-millis=150000
redis.config.promotionfinal.jedis-pool.num-tests-per-eviction-run=-1
redis.config.promotionfinal.jedis-pool.max-wait-millis=3000



# redis config for act_cache
redis.config.actcache.host=wcc.cache01.test.b2c.srv
redis.config.actcache.port=22122
redis.config.actcache.password=shopapi_misc
redis.config.actcache.timeout=5000
redis.config.actcache.database=0
redis.config.actcache.jedis-pool.max-idle=50
redis.config.actcache.jedis-pool.min-idle=20
redis.config.actcache.jedis-pool.max-total=50
redis.config.actcache.jedis-pool.test-while-idle=true
redis.config.actcache.jedis-pool.time-between-eviction-runs-millis=30000
redis.config.actcache.jedis-pool.min-evictable-idle-time-millis=150000
redis.config.actcache.jedis-pool.num-tests-per-eviction-run=-1
redis.config.actcache.jedis-pool.max-wait-millis=3000
#redis.config.actcache.lettuce-pool.max-idle=8
#redis.config.actcache.lettuce.pool.min-idle=1
#redis.config.actcache.lettuce-pool.max-total=200
#redis.config.actcache.lettuce-pool.timeout=3000
#redis.config.actcache.lettuce-pool.shutdown-timeOut=3000
#redis.config.actcache.lettuce.pool.max-wait-millis=3000

# redis config for redis_act
redis.config.act.host=wcc.cache01.test.b2c.srv
redis.config.act.port=22122
redis.config.act.password=cn_mishop_pulse_redis_common_storage_DPBRaZryAOwr
redis.config.act.timeout=5000
redis.config.act.database=0
redis.config.act.jedis-pool.max-idle=50
redis.config.act.jedis-pool.min-idle=20
redis.config.act.jedis-pool.max-total=50
redis.config.act.jedis-pool.test-while-idle=true
redis.config.act.jedis-pool.time-between-eviction-runs-millis=30000
redis.config.act.jedis-pool.min-evictable-idle-time-millis=150000
redis.config.act.jedis-pool.num-tests-per-eviction-run=-1
redis.config.act.jedis-pool.max-wait-millis=3000
#redis.config.act.lettuce-pool.max-idle=8
#redis.config.act.lettuce.pool.min-idle=1
#redis.config.act.lettuce-pool.max-total=200
#redis.config.act.lettuce-pool.timeout=3000
#redis.config.act.lettuce-pool.shutdown-timeOut=3000
#redis.config.act.lettuce.pool.max-wait-millis=3000

# redis config for coupon
redis.config.coupon.host=wcc.cache01.test.b2c.srv
redis.config.coupon.port=22122
redis.config.coupon.password=cn_mishop_pulse_redis_isLm7NcoEPdwjra9
redis.config.coupon.timeout=5000
redis.config.coupon.database=0
redis.config.coupon.jedis-pool.max-idle=50
redis.config.coupon.jedis-pool.min-idle=20
redis.config.coupon.jedis-pool.max-total=50
redis.config.coupon.jedis-pool.test-while-idle=true
redis.config.coupon.jedis-pool.time-between-eviction-runs-millis=30000
redis.config.coupon.jedis-pool.min-evictable-idle-time-millis=150000
redis.config.coupon.jedis-pool.num-tests-per-eviction-run=-1
redis.config.coupon.jedis-pool.max-wait-millis=3000
#redis.config.coupon.lettuce-pool.max-idle=8
#redis.config.coupon.lettuce.pool.min-idle=1
#redis.config.coupon.lettuce-pool.max-total=200
#redis.config.coupon.lettuce-pool.timeout=3000
#redis.config.coupon.lettuce-pool.shutdown-timeOut=3000
#redis.config.coupon.lettuce.pool.max-wait-millis=3000

#redis config for redpacket
redis.config.redpacket.host=wcc.cache01.test.b2c.srv
redis.config.redpacket.port=22122
redis.config.redpacket.password=cn_mishop_pulse_redis_isLm7NcoEPdwjra9
redis.config.redpacket.timeout=5000
redis.config.redpacket.database=0
redis.config.redpacket.jedis-pool.max-idle=50
redis.config.redpacket.jedis-pool.min-idle=20
redis.config.redpacket.jedis-pool.max-total=50
redis.config.redpacket.jedis-pool.test-while-idle=true
redis.config.redpacket.jedis-pool.time-between-eviction-runs-millis=30000
redis.config.redpacket.jedis-pool.min-evictable-idle-time-millis=150000
redis.config.redpacket.jedis-pool.num-tests-per-eviction-run=-1
redis.config.redpacket.jedis-pool.max-wait-millis=3000
#redis.config.redpacket.lettuce-pool.max-idle=8
#redis.config.redpacket.lettuce.pool.min-idle=1
#redis.config.redpacket.lettuce-pool.max-total=200
#redis.config.redpacket.lettuce-pool.timeout=3000
#redis.config.redpacket.lettuce-pool.shutdown-timeOut=3000
#redis.config.redpacket.lettuce.pool.max-wait-millis=3000

# mysql promotion config
spring.promotionconfig-datasource.name=xm_pulse_natl
spring.promotionconfig-datasource.username=pulse_natl_w
spring.promotionconfig-datasource.url=*********************************************************************************************
spring.promotionconfig-datasource.password=NsYIltp7GI1kK_YLrBGhkuAOpT
spring.promotionconfig-datasource.connectionProperties=
spring.promotionconfig-datasource.sql-script-encoding=UTF-8
spring.promotionconfig-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.promotionconfig-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.promotionconfig-datasource.initial-size=5
spring.promotionconfig-datasource.max-active=10
spring.promotionconfig-datasource.min-idle=3
spring.promotionconfig-datasource.max-wait=600000
spring.promotionconfig-datasource.remove-abandoned=true
spring.promotionconfig-datasource.remove-abandoned-timeout=180
spring.promotionconfig-datasource.time-between-eviction-runs-millis=600000
spring.promotionconfig-datasource.min-evictable-idle-time-millis=300000
spring.promotionconfig-datasource.validation-query=SELECT 1 FROM DUAL
spring.promotionconfig-datasource.test-while-idle=true
spring.promotionconfig-datasource.test-on-borrow=false
spring.promotionconfig-datasource.test-on-return=false
spring.promotionconfig-datasource.pool-prepared-statements=true
spring.promotionconfig-datasource.max-pool-prepared-statement-per-connection-size=50
spring.promotionconfig-datasource.filters=stat,wall

# mysql promotion user config
spring.promotionuserconfig-datasource.name=pulse_user
spring.promotionuserconfig-datasource.username=pulse_user
spring.promotionuserconfig-datasource.url=****************************************************************************************
spring.promotionuserconfig-datasource.password=ifvSZGHPXMtBwi31_0jpSSt6gPX4dI
spring.promotionuserconfig-datasource.connectionProperties=
spring.promotionuserconfig-datasource.sql-script-encoding=UTF-8
spring.promotionuserconfig-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.promotionuserconfig-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.promotionuserconfig-datasource.initial-size=5
spring.promotionuserconfig-datasource.max-active=10
spring.promotionuserconfig-datasource.min-idle=3
spring.promotionuserconfig-datasource.max-wait=600000
spring.promotionuserconfig-datasource.remove-abandoned=true
spring.promotionuserconfig-datasource.remove-abandoned-timeout=180
spring.promotionuserconfig-datasource.time-between-eviction-runs-millis=600000
spring.promotionuserconfig-datasource.min-evictable-idle-time-millis=300000
spring.promotionuserconfig-datasource.validation-query=SELECT 1 FROM DUAL
spring.promotionuserconfig-datasource.test-while-idle=true
spring.promotionuserconfig-datasource.test-on-borrow=false
spring.promotionuserconfig-datasource.test-on-return=false
spring.promotionuserconfig-datasource.pool-prepared-statements=true
spring.promotionuserconfig-datasource.max-pool-prepared-statement-per-connection-size=50
spring.promotionuserconfig-datasource.filters=stat,wall

# mysql fcode config
spring.fcodeconfig-datasource.name=fcode_user_w
spring.fcodeconfig-datasource.username=fcode_user_w
spring.fcodeconfig-datasource.url=*******************************************************************************************
spring.fcodeconfig-datasource.password=5d8f5uiCrOY_S0SFPCt9c8
spring.fcodeconfig-datasource.connectionProperties=
spring.fcodeconfig-datasource.sql-script-encoding=UTF-8
spring.fcodeconfig-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.fcodeconfig-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.fcodeconfig-datasource.initial-size=5
spring.fcodeconfig-datasource.max-active=10
spring.fcodeconfig-datasource.min-idle=3
spring.fcodeconfig-datasource.max-wait=600000
spring.fcodeconfig-datasource.remove-abandoned=true
spring.fcodeconfig-datasource.remove-abandoned-timeout=180
spring.fcodeconfig-datasource.time-between-eviction-runs-millis=600000
spring.fcodeconfig-datasource.min-evictable-idle-time-millis=300000
spring.fcodeconfig-datasource.validation-query=SELECT 1 FROM DUAL
spring.fcodeconfig-datasource.test-while-idle=true
spring.fcodeconfig-datasource.test-on-borrow=false
spring.fcodeconfig-datasource.test-on-return=false
spring.fcodeconfig-datasource.pool-prepared-statements=true
spring.fcodeconfig-datasource.max-pool-prepared-statement-per-connection-size=50
spring.fcodeconfig-datasource.filters=stat,wall

# md_promotion
spring.mdpromotion-datasource.name=md_promotion_wn
spring.mdpromotion-datasource.username=md_promotion_wn
spring.mdpromotion-datasource.url=******************************************************************************************
spring.mdpromotion-datasource.password=GDDVMbuqAzDNWvNYidWl/RyK/auEAzbgpZYyDVEVq7MIDNmYgoN5LwRaANiUlilKN4kYEhb9EEIf2UuUvDVUouIa9Y3A/xgQnBVGfs6PS+e0QdvMFlYEtBgUg5/oPG1YGoLF0fGQD+FF7bZhPdQA
spring.mdpromotion-datasource.password@kc-sid=mi_newretail_risk.g
spring.mdpromotion-datasource.sql-script-encoding=UTF-8
spring.mdpromotion-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.mdpromotion-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.mdpromotion-datasource.initial-size=5
spring.mdpromotion-datasource.max-active=10
spring.mdpromotion-datasource.min-idle=3
spring.mdpromotion-datasource.max-wait=600000
spring.mdpromotion-datasource.remove-abandoned=true
spring.mdpromotion-datasource.remove-abandoned-timeout=180
spring.mdpromotion-datasource.time-between-eviction-runs-millis=600000
spring.mdpromotion-datasource.min-evictable-idle-time-millis=300000
spring.mdpromotion-datasource.validation-query=SELECT 1 FROM DUAL
spring.mdpromotion-datasource.test-while-idle=true
spring.mdpromotion-datasource.test-on-borrow=false
spring.mdpromotion-datasource.test-on-return=false
spring.mdpromotion-datasource.pool-prepared-statements=true
spring.mdpromotion-datasource.max-pool-prepared-statement-per-connection-size=50
spring.mdpromotion-datasource.filters=stat,wall


# mysql nractivity config
spring.nractivityconfig-datasource.name=xm_pulse_natl
spring.nractivityconfig-datasource.username=pulse_natl_w
spring.nractivityconfig-datasource.url=*********************************************************************************************
spring.nractivityconfig-datasource.password=NsYIltp7GI1kK_YLrBGhkuAOpT
spring.nractivityconfig-datasource.connectionProperties=
spring.nractivityconfig-datasource.sql-script-encoding=UTF-8
spring.nractivityconfig-datasource.driver-class-name=com.mysql.jdbc.Driver
spring.nractivityconfig-datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.nractivityconfig-datasource.initial-size=5
spring.nractivityconfig-datasource.max-active=10
spring.nractivityconfig-datasource.min-idle=3
spring.nractivityconfig-datasource.max-wait=600000
spring.nractivityconfig-datasource.remove-abandoned=true
spring.nractivityconfig-datasource.remove-abandoned-timeout=180
spring.nractivityconfig-datasource.time-between-eviction-runs-millis=600000
spring.nractivityconfig-datasource.min-evictable-idle-time-millis=300000
spring.nractivityconfig-datasource.validation-query=SELECT 1 FROM DUAL
spring.nractivityconfig-datasource.test-while-idle=true
spring.nractivityconfig-datasource.test-on-borrow=false
spring.nractivityconfig-datasource.test-on-return=false
spring.nractivityconfig-datasource.pool-prepared-statements=true
spring.nractivityconfig-datasource.max-pool-prepared-statement-per-connection-size=50
spring.nractivityconfig-datasource.filters=stat,wall

configHost=http://etcd.test.mi.com
appGroup=mishop
serviceName=promotion

# 人群
user.dubbo.group=staging
userTagToken=GDD/YqtNlG43iEf2WIP9pL68Mn1mUkgpnK3WE8J6wUu2IV+YBCiBjFULtvBwNAHV3WIYEkWMCHyi1kJjgXyRzh5FTpMy/xgQdjScM4+PS6OnV6OxY+r90hgUHGbyWHEQdvlYscapBPJkqMa67pEA
userTagToken@kc-sid=mi_newretail_risk.g

logging.level.com=debug

# 订单服务
order.dubbo.group=staging
vipmember.dubbo.group=staging
# gis
gis.dubbo.version=0.1
gis.dubbo.group=staging
# phoenix
phoenix.dubbo.group=staging

#卡劵服务
coupon.dubbo.version=1.0
coupon.dubbo.group=staging
coupon.appId=XM2108
coupon.secret=GDCt1z6jB1cVLMV90meSA3ClEDw8tCdtDkA06pgbGNcUtrgz5ca/nxxnWdCihtx6rJ8YEvwJPF1VE0tjruDGgaM8wU95/xgQ58MDDkNVQ7SBHciB/JmSshgUrR4IIlbnTMHoKn2TVkXe7on3BNYA
coupon.secret@kc-sid=mi_newretail_risk.g

# [consumer_coupon]消费券配置 [consumer_coupon] 消费券可用区域，空代表没有，券配置ID:区域用逗号分隔(P_代表省份ID，C_代表城市ID)
coupon.region.consumer=11958:C_36;11995:C_36

# rocketMq
rocketmq.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.producer.group=promotion-producer
rocketmq.producer.access-key=AKT7P4VWPIPO4PGMPI
rocketmq.producer.secret-key=GDAksE/+DtdWXpESyiiDsaNsdNdEE4yMVCkOcN0YmxoPbs2QIxzoiRsDuIWZGylqQ+oYEq015z57nUhulmxH8FmZ52lO/xgQVR8oSWX2SwK2UIE4bXkHKxgU40VhM1HrGlR7Z5WMonIo4JHrNjQA
rocketmq.producer.secret-key@kc-sid=mi_newretail_risk.g
mq.topic.actstock=nr_promotion_act_stock
rocketmq.carActivity.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
mq.topic.carActivity=nr_car_activity_use_message
mq.topic.carActivity.tag=vipMember
mq.topic.carEquity=car_equity_performance
mq.topic.carEquity.tag=ACTIVITY

# order
rocketmq.access-key=AKT7P4VWPIPO4PGMPI
rocketmq.secret-key=GDAksE/+DtdWXpESyiiDsaNsdNdEE4yMVCkOcN0YmxoPbs2QIxzoiRsDuIWZGylqQ+oYEq015z57nUhulmxH8FmZ52lO/xgQVR8oSWX2SwK2UIE4bXkHKxgU40VhM1HrGlR7Z5WMonIo4JHrNjQA
rocketmq.secret-key@kc-sid=mi_newretail_risk.g
rocketmq.order.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
rocketmq.topic.order=nr_message_bus
rocketmq.topic.order.consumerGroup=nr_message_bus_consumer_promotion_test
rocketmq.topic.order.enabled=true
rocketmq.topic.order.offline=nr_message_bus_offline
rocketmq.topic.order.offline.consumerGroup=nr_message_bus_offline_consumer_promotion_test
rocketmq.topic.order.offline.enabled=true
rocketmq.topic.order.carLife=nr_message_bus_car_life
rocketmq.topic.order.carLife.consumerGroup=nr_message_bus_car_life_consumer_promotion_test
rocketmq.topic.order.carLife.enabled=true
rocketmq.topic.order.car=nr_message_bus_car
rocketmq.topic.order.car.consumerGroup=nr_message_bus_car_consumer_promotion_test
rocketmq.topic.order.car.enabled=true
rocketmq.topic.order.afterSale=nr_message_bus_car_aftersale
rocketmq.topic.order.afterSale.consumerGroup=nr_message_bus_aftersale_consumer_promotion_test
rocketmq.topic.order.afterSale.enabled=true
rocketmq.topic.promotion.notify.broadcast=CI105884_MD_Promotion_Notify_BroadCast
rocketmq.consumer.group.promotion.notify.broadcast=CI105884_MD_Promotion_Notify_BroadCast_Consumer_Group

# recycle
recycle.dubbo.group=staging


#mdpromotionadmin
md.promotion.admin.dubbo.group=staging

#礼品卡服务
ecard.dubbo.version=1.0
ecard.dubbo.group=staging

mro.policy.dubbo.group=staging


#sentinel
sentinel.dashboard.host=http://sentinel.test.be.mi.com

#aries
aries.dubbo.version=1.0
aries.dubbo.group=staging

#user.permit
user.permit.url=https://inner-partner.staging.iccc.xiaomiev.com

