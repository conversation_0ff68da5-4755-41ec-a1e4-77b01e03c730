package com.xiaomi.nr.promotion.config.datasource;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： Pancras
 * @date： 2024/9/19
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Configuration
@ConditionalOnExpression(value = "'${biz.area.id.list}'.contains('MISHOP') || '${biz.area.id.list}'.contains('CAR')")
@MapperScan(basePackages = {"com.xiaomi.nr.promotion.dao.mysql.nractivity"}, sqlSessionFactoryRef = "nrActivityConfigSqlSessionFactory")
public class NrActivityDataSourceConfiguration {
    
    @Value("${spring.nractivityconfig-datasource.url:#{null}}")
    private String dataSourceUrl;
    
    @Bean(name = "nrActivityConfigDatasource")
    @ConfigurationProperties("spring.nractivityconfig-datasource")
    public DataSource mysqlDataSource() {
        return DruidDataSourceBuilder.create().build();
    }
    
    @Bean(name = "nrActivityConfigSqlSessionFactory")
    public SqlSessionFactory mysqlSqlSessionFactory(@Qualifier("nrActivityConfigDatasource") DataSource dataSource)
            throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/nractivity/*.xml"));
        sessionFactory.getObject().getConfiguration().setMapUnderscoreToCamelCase(true);
        return sessionFactory.getObject();
    }
    
    @Bean(name = "nrActivityConfigTransactionManager")
    public PlatformTransactionManager prodTransactionManager(@Qualifier("nrActivityConfigDatasource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
