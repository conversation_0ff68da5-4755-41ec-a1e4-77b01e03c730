package com.xiaomi.nr.promotion.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 线程池次配置
 *
 * <AUTHOR>
 * @date 2021/4/28
 */
@Slf4j
@Configuration
public class ThreadPoolConfiguration {
    /**
     * promotion信息线程池
     *
     * @return async executor
     */
    @Bean("promotionAsyncTaskExecutor")
    public Executor promotionTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(50);
        executor.setMaxPoolSize(120);
        executor.setKeepAliveSeconds(30);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("promotionAsyncTaskExecutor-");
        executor.setTaskDecorator(runnable -> runnable);
        executor.setRejectedExecutionHandler((r, executor1) ->
                log.error("promotionAsyncTaskExecutor reject thread. active thread:{}, queue size:{}", executor1.getActiveCount(),
                        executor1.getQueue().size()));
        return executor;
    }

    /**
     * 请求商品信息线程池
     *
     * @return async executor
     */
    @Bean("goodsAsyncTaskExecutor")
    public Executor goodsTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(30);
        executor.setMaxPoolSize(120);
        executor.setKeepAliveSeconds(30);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("goodsAsyncTaskExecutor-");
        executor.setTaskDecorator(runnable -> runnable);
        executor.setRejectedExecutionHandler((r, executor1) ->
                log.error("goodsAsyncTaskExecutor reject thread. active thread:{}, queue size:{}", executor1.getActiveCount(),
                        executor1.getQueue().size()));
        return executor;
    }

    /**
     * 请求门店信息线程池
     *
     * @return async executor
     */
    @Bean("storeAsyncTaskExecutor")
    public Executor storeTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(30);
        executor.setMaxPoolSize(120);
        executor.setKeepAliveSeconds(30);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("storeAsyncTaskExecutor-");
        executor.setTaskDecorator(runnable -> runnable);
        executor.setRejectedExecutionHandler((r, executor1) ->
                log.error("storeAsyncTaskExecutor reject thread. active thread:{}, queue size:{}", executor1.getActiveCount(),
                        executor1.getQueue().size()));
        return executor;
    }

    /**
     * 请求优惠信息线程池
     *
     * @return async executor
     */
    @Bean("couponAsyncTaskExecutor")
    public Executor couponTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(120);
        executor.setKeepAliveSeconds(30);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("couponAsyncTaskExecutor-");
        executor.setTaskDecorator(runnable -> runnable);
        executor.setRejectedExecutionHandler((r, executor1) ->
                log.error("couponAsyncTaskExecutor reject thread. active thread:{}, queue size:{}", executor1.getActiveCount(),
                        executor1.getQueue().size()));
        return executor;
    }

    /**
     * 请求conf信息线程池
     *
     * @return async executor
     */
    @Bean("confAsyncTaskExecutor")
    public Executor confTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(120);
        executor.setKeepAliveSeconds(30);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("confAsyncTaskExecutor-");
        executor.setTaskDecorator(runnable -> runnable);
        executor.setRejectedExecutionHandler((r, executor1) ->
                log.error("confAsyncTaskExecutor reject thread. active thread:{}, queue size:{}", executor1.getActiveCount(),
                        executor1.getQueue().size()));
        return executor;
    }

    @Bean("workHourTaskExecutor")
    public Executor workHourTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(120);
        executor.setKeepAliveSeconds(30);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("workHourTaskExecutor-");
        executor.setTaskDecorator(runnable -> runnable);
        executor.setRejectedExecutionHandler((r, executor1) ->
                log.error("workHourTaskExecutor reject thread. active thread:{}, queue size:{}", executor1.getActiveCount(),
                        executor1.getQueue().size()));
        return executor;
    }

    /**
     * 请求Gis赠品库存信息线程池
     *
     * @return async executor
     */
    @Bean("gisStockAsyncTaskExecutor")
    public Executor gisStockTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(30);
        executor.setMaxPoolSize(120);
        executor.setKeepAliveSeconds(30);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("gisStockAsyncTaskExecutor-");
        executor.setTaskDecorator(runnable -> runnable);
        executor.setRejectedExecutionHandler((r, executor1) ->
                log.error("gisStockAsyncTaskExecutor reject thread. active thread:{}, queue size:{}", executor1.getActiveCount(),
                        executor1.getQueue().size()));
        return executor;
    }

    /**
     * 请求Phoenix信息线程池
     *
     * @return async executor
     */
    @Bean("phoenixTaskExecutor")
    public Executor phoenixTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(30);
        executor.setKeepAliveSeconds(30);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("phoenixTaskExecutor-");
        executor.setTaskDecorator(runnable -> runnable);
        executor.setRejectedExecutionHandler((r, executor1) ->
                log.error("phoenixTaskExecutor reject thread. active thread:{}, queue size:{}", executor1.getActiveCount(),
                        executor1.getQueue().size()));
        return executor;
    }

    /**
     * 通用任务线程
     *
     * @return async executor
     */
    @Bean("commonAsyncTaskExecutor")
    public Executor commonAsyncTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(30);
        executor.setKeepAliveSeconds(30);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("commonAsyncTaskExecutor-");
        executor.setTaskDecorator(runnable -> runnable);
        executor.setRejectedExecutionHandler((r, executor1) ->
                log.error("commonAsyncTaskExecutor reject thread. active thread:{}, queue size:{}", executor1.getActiveCount(),
                        executor1.getQueue().size()));
        return executor;
    }

    /**
     * 通用任务线程
     *
     * @return async executor
     */
    @Bean("checkoutAsyncTaskExecutor")
    public ThreadPoolTaskExecutor checkoutTaskExecutor() {

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(100);
        executor.setMaxPoolSize(200);
        executor.setKeepAliveSeconds(30);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("checkoutAsyncTaskExecutor-");
        executor.setTaskDecorator(runnable -> runnable);
        executor.setRejectedExecutionHandler((r, executor1) ->
                log.error("checkoutAsyncTaskExecutor reject thread. active thread:{}, queue size:{}", executor1.getActiveCount(),
                        executor1.getQueue().size()));
        return executor;
    }

    /**
     * 用户标签线程池
     *
     * @return async executor
     */
    @Bean("userPropertyAsyncTaskExecutor")
    public Executor userPropertyTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(30);
        executor.setMaxPoolSize(120);
        executor.setKeepAliveSeconds(30);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("userPropertyAsyncTaskExecutor-");
        executor.setTaskDecorator(runnable -> runnable);
        executor.setRejectedExecutionHandler((r, executor1) ->
                log.error("userPropertyAsyncTaskExecutor reject thread. active thread:{}, queue size:{}", executor1.getActiveCount(),
                        executor1.getQueue().size()));
        return executor;
    }

    /**
     * F会员线程池
     *
     * @return async executor
     */
    @Bean("proMemberAsyncTaskExecutor")
    public Executor proMemberAsyncTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(30);
        executor.setMaxPoolSize(120);
        executor.setKeepAliveSeconds(30);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("proMemberAsyncTaskExecutor-");
        executor.setTaskDecorator(runnable -> runnable);
        executor.setRejectedExecutionHandler((r, executor1) ->
                log.error("proMemberAsyncTaskExecutor reject thread. active thread:{}, queue size:{}", executor1.getActiveCount(),
                        executor1.getQueue().size()));
        return executor;
    }
}
