package com.xiaomi.nr.promotion.config;

import com.xiaomi.iauth.java.sdk.constants.IAuthConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/15 18:45
 */
@Component
public class IAuthConfig implements ApplicationListener<ContextRefreshedEvent> {

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        // todo 可采用常量的方式
        String envName = event.getApplicationContext().getEnvironment().getProperty("spring.profiles.active");
        String env = null;
        if (StringUtils.equals("dev", envName)) {
            env = "dev";
        } else if (StringUtils.equals("staging", envName)) {
            env = "staging";
        } else {
            env = "prod";
        }
        String configPath = "iauth/iauth-" + env + ".properties";
        System.setProperty(IAuthConstants.CONFIG_FILE_SYTSTEM_PROPERTY, configPath);
    }
}
