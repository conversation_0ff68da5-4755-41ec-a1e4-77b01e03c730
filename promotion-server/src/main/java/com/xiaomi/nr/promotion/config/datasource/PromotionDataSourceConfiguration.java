package com.xiaomi.nr.promotion.config.datasource;

import javax.sql.DataSource;

import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;

import java.util.Properties;

/**
 * 数据源配置类
 *
 * <AUTHOR>
 * @date 2021/3/5
 */
@Configuration
@MapperScan(basePackages = {"com.xiaomi.nr.promotion.dao.mysql.promotion"}, sqlSessionFactoryRef = "promotionConfigSqlSessionFactory")
public class PromotionDataSourceConfiguration {

    @Value("${spring.promotionconfig-datasource.url:#{null}}")
    private String dataSourceUrl;

    @Bean(name = "promotionConfigDatasource")
    @ConfigurationProperties("spring.promotionconfig-datasource")
    public DataSource mysqlDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "promotionConfigSqlSessionFactory")
    public SqlSessionFactory mysqlSqlSessionFactory(@Qualifier("promotionConfigDatasource") DataSource dataSource)
            throws Exception {
        final SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/promotion/*.xml"));
        sessionFactory.getObject().getConfiguration().setMapUnderscoreToCamelCase(true);
        return sessionFactory.getObject();
    }

    @Bean(name = "promotionConfigTransactionManager")
    public PlatformTransactionManager prodTransactionManager(@Qualifier("promotionConfigDatasource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
