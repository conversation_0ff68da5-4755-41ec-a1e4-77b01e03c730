package com.xiaomi.nr.promotion.bootstrap;

import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.xiaomi.mone.dubbo.docs.EnableDubboApiDocs;

/**
 * <AUTHOR>
 */
@EnableAutoConfiguration
@ComponentScan(basePackages = {"com.xiaomi.nr.promotion", "com.xiaomi.youpin"})
@DubboComponentScan(basePackages = "com.xiaomi.nr.promotion")
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
@EnableTransactionManagement
@EnableAsync
@EnableDubboApiDocs
public class PromotionBootstrap {
    private static final Logger logger = LoggerFactory.getLogger(PromotionBootstrap.class);

    public static void main(String... args) {
        try {
            SpringApplication.run(PromotionBootstrap.class, args);
            logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>PromotionBootstrap Started!<<<<<<<<<<<<<<<<<<<<<<<<<<<");
        } catch (Throwable throwable) {
            logger.error(throwable.getMessage(), throwable);
            System.exit(-1);
        }
    }
}