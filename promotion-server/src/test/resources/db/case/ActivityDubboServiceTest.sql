
INSERT INTO `v3_activity_online` (
    `id`,
    `name`,
    `type_id`,
    `stat`,
    `priority`,
    `group_name`,
    `start_date`,
    `end_date`,
    `start_time`,
    `end_time`,
    `is_daily`,
    `limited`,
    `check_package`,
    `offline`,
    `goods_include`,
    `goods_inexclude`,
    `goods_exclude`,
    `users`,
    `client`,
    `quota`,
    `policy`,
    `cond`,
    `admin_id`,
    `add_time`,
    `add_user`,
    `modify_time`,
    `modify_id`,
    `modify_user`,
    `is_copy`,
    `from_id`,
    `approve_id`,
    `approve_time`,
    `area_id`,
    `subchannels`,
    `ext_prop`,
    `select_orgIds`,
    `online_status`,
    `workflow`,
    `sale_source`,
    `bpm_id`,
    `channel`,
    `version`,
    `org_scope`,
    `policy_new`,
    `ext`,
    `activity_scene`,
    `sequence_id`
) VALUES (
    '9999999', -- id （唯一标识符）
    '测试活动-洗衣机促销', -- name （活动名称）
    '20', -- type_id （活动类型 ID）
    'approved', -- stat （活动状态：已批准）
    '1', -- priority （优先级）
    '', -- group_name （分组名称，可为空）
    '2025-04-01 00:00', -- start_date （活动开始日期）
    '2099-12-31 00:00', -- end_date （活动结束日期）
    '00:00:00', -- start_time （活动开始时间，默认值）
    '23:59:59', -- end_time （活动结束时间，默认值）
    '2', -- is_daily （是否每日活动）
    '1', -- limited （是否限制）
    '2', -- check_package （检查包）
    '1', -- offline （是否离线）
    '', -- goods_include （包含商品，可为空）
    '', -- goods_inexclude （排除商品，可为空）
    '', -- goods_exclude （排除商品，可为空）
    '{"data":["*"],"action":1}', -- users （用户组 JSON 数据）
    '["180100031013","180100031016"]', -- client （客户端 JSON 数据）
    '', -- quota （配额，可为空）
    '', -- policy （策略，可为空）
    '', -- cond （条件，可为空）
    '0', -- admin_id （管理员 ID）
    '1744164549', -- add_time （添加时间，时间戳格式）
    'testuser', -- add_user （添加人）
    '1744164549', -- modify_time （修改时间，时间戳格式）
    '0', -- modify_id （修改人 ID）
    'testuser', -- modify_user （修改人）
    '0', -- is_copy （是否复制）
    '0', -- from_id （来源 ID）
    NULL, -- approve_id （审批 ID，允许 NULL）
    0, -- approve_time （审批时间，默认值 0）
    0, -- area_id （区域 ID，默认值 0）
    '', -- subchannels （子渠道，可为空）
    '{"sale_source":"","act_total_stock":"","channel":"","access_code":"","frequency":"1","check_package":"2","client":"[\"180100031013\",\"180100031016\"]","user_group":{"data":["*"],"action":1},"show_in_productView":2,"is_recommend_force_add_price":2}', -- ext_prop （扩展属性 JSON 数据）
    '', -- select_orgIds （选择组织 ID，可为空）
    '1', -- online_status （在线状态）
    '', -- workflow （工作流，可为空）
    '{"desc_is_show_add_on_item":"0","desc_short_name":"","desc_title":"","desc_url":""}', -- sale_source （销售来源 JSON 数据）
    '', -- bpm_id （BPM ID，可为空）
    '1', -- channel （渠道）
    '1', -- version （版本号）
    '0', -- org_scope （组织范围）
    '[{"id":1230802798,"level":"package","lower_price":"4499.00","lower_price_groups":{"0":"1499.41","1":"2999.59"},"lower_price_list":[[{"commodity_id":1230801781,"lower_price":"1499.42"}],[{"commodity_id":1230802793,"lower_price":"2999.58"}]],"online_status":1}]', -- policy_new （新策略 JSON 数据）
    '{"ext_file":[{"name":"测试文件.xlsx","url":"https://example.com/test_file.xlsx"}]}', -- ext （扩展文件 JSON 数据）
    '', -- activity_scene （活动场景，可为空）
    '560000000000999999' -- sequence_id （序列 ID）
);
