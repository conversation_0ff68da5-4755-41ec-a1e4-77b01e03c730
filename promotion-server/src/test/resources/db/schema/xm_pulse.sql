CREATE TABLE `promotion_resource_detail` (
  `id` bigint(21) NOT NULL DEFAULT '0' COMMENT '自增ID',
  `resource_id` varchar(100) NOT NULL DEFAULT '0' COMMENT '资源ID',
  `resource_type` int(8) NOT NULL DEFAULT '0' COMMENT '资源类型',
  `order_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '订单号',
  `pid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'pid',
  `promotion_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '优惠工具id',
  `status` int(8) NOT NULL DEFAULT '0' COMMENT '状态',
  `content` mediumtext NOT NULL COMMENT '详情',
  `create_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  `unique_key` varchar(128) DEFAULT NULL COMMENT '资源唯一字段',
  `return_status` int(8) NOT NULL DEFAULT '0' COMMENT '资源返还状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_unique_key` (`unique_key`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `promotion_resource_operation_log` (
  `id` bigint(21) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单ID',
  `transaction_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '事务ID',
  `resource_context` text NOT NULL COMMENT '资源域变量',
  `operation_source` int(8) NOT NULL DEFAULT '0' COMMENT '操作来源',
  `operation_type` int(8) NOT NULL DEFAULT '0' COMMENT '操作类型',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `uid` bigint(21) NOT NULL DEFAULT '0' COMMENT '用户id',
  PRIMARY KEY (`id`),
  KEY `idx_order_id1` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `promotion_resource_status` (
  `order_id` bigint(20) unsigned NOT NULL DEFAULT '0',
  `status` int(8) NOT NULL DEFAULT '0',
  `trade_from` int(4) DEFAULT '1' COMMENT '来源 1：商城 2：门店',
  `create_time` bigint(20) unsigned NOT NULL DEFAULT '0',
  `update_time` bigint(20) unsigned NOT NULL DEFAULT '0',
  `uid` bigint(21) NOT NULL DEFAULT '0' COMMENT '用户id',
  PRIMARY KEY (`order_id`),
  KEY `idx_status_create_time` (`status`,`create_time`),
  KEY `idx_uid` (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `subsidy_qualification` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `mid` bigint(20) NOT NULL COMMENT '用户mid',
  `encrypted_cert_name` varchar(255) NOT NULL COMMENT '加密的用户名称',
  `encrypted_cert_no` varchar(255) NOT NULL COMMENT '加密的实名身份证号',
  `encrypted_mobile_phone` varchar(255) NOT NULL COMMENT '加密后的手机号',
  `cert_no_idx` varchar(255) NOT NULL COMMENT '身份证号md5,便于检索',
  `idcard_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '实名认证方式 1.身份证',
  `cate_code` varchar(50) NOT NULL COMMENT '品类编码',
  `qualify_status` tinyint(4) NOT NULL COMMENT '资格状态,0已解绑,1已绑定,2已核销',
  `report_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '上报状态,0未上报,1上报成功,2上报失败',
  `report_info` text COMMENT '上报信息json',
  `active_unbind_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '主动解绑状态,0无需解绑,1待解绑,2解绑成功,3-解绑失败',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `region_id` int(10) NOT NULL DEFAULT '1' COMMENT '资格适用地区1广东珠海 3广东深圳',
  `qualify_source` int(10) NOT NULL DEFAULT '0' COMMENT '资格来源',
  `third_identity_idx` varchar(255) DEFAULT NULL COMMENT '三方平台身份索引',
  `extend_info` text COMMENT '扩展信息json',
  `qualify_version` int(10) NOT NULL DEFAULT '1' COMMENT '资格版本',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_cert_cat_region_version` (`cert_no_idx`,`cate_code`,`region_id`,`qualify_version`),
  KEY `idx_mid` (`mid`),
  KEY `idx_cert_no_idx` (`cert_no_idx`),
  KEY `idx_active_unbind_status` (`active_unbind_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='以旧换新资格表';

CREATE TABLE `subsidy_qualification_change_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `mid` bigint(20) NOT NULL COMMENT '用户mid',
  `qualification_id` bigint(20) NOT NULL COMMENT '资格ID',
  `operation_type` tinyint(4) NOT NULL COMMENT '操作类型：1-领资格 2-解绑资格 3-发券（发起支付） 4、支付成功 5、取消订单 6、退款',
  `operation_info` text COMMENT '操作记录',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_qualification_id` (`qualification_id`),
  KEY `idx_mid1` (`mid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='以旧换新资格码变更日志表';

CREATE TABLE `subsidy_qualification_coupon` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `qualification_id` bigint(20) NOT NULL COMMENT '资格ID',
  `cate_level` varchar(255) NOT NULL COMMENT '品类能效编码',
  `coupon_order_no` varchar(255) NOT NULL COMMENT '券联盟订单号',
  `payment_channel` varchar(255) NOT NULL COMMENT '支付渠道 WECHAT/UNIONPAY',
  `payment_info` text COMMENT '支付回调信息',
  `coupon_status` tinyint(4) NOT NULL COMMENT '券状态,0未使用,1已使用,2已锁定3,已作废',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0：未删除 1：删除',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_coupon_order_no_payment_channel` (`coupon_order_no`,`payment_channel`),
  KEY `idx_order_id2` (`order_id`),
  KEY `idx_qualification_id_is_deleted` (`qualification_id`,`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='以旧换新优惠券表';

CREATE TABLE `subsidy_qualification_order_extend` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `qualify_id` bigint(20) NOT NULL COMMENT '资格ID',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单ID',
  `extend_info` text COMMENT '扩展信息json',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_qualify_id` (`qualify_id`),
  UNIQUE KEY `idx_order_id3` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资格订单扩展表';

CREATE TABLE `tb_beijing_coupon` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `name` varchar(64) NOT NULL DEFAULT '' COMMENT '姓名',
  `tel` varchar(512) NOT NULL DEFAULT '' COMMENT '手机号',
  `id_card_token` varchar(255) NOT NULL DEFAULT '' COMMENT '身份证号hash值',
  `id_card_mask` varchar(255) NOT NULL DEFAULT '' COMMENT '身份证加掩码处理',
  `id_card_encrypt` varchar(255) NOT NULL DEFAULT '' COMMENT '身份证号加密',
  `ecard_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'ecard编号',
  `batch` int(11) NOT NULL DEFAULT '0' COMMENT '批次',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '领取时间',
  `coupon_start_time` int(11) NOT NULL DEFAULT '0' COMMENT '券使用开始时间',
  `coupon_end_time` int(11) NOT NULL DEFAULT '0' COMMENT '券使用结束时间',
  `push_oc` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否推送oc',
  `join_info` varchar(255) NOT NULL DEFAULT '' COMMENT '用户id_身份证md5_批次唯一索引',
  `bank_account_encrypt` varchar(255) NOT NULL DEFAULT '' COMMENT '用户银行卡号',
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '券面额(元)',
  `use_threshold` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '使用门槛(元)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_join_info` (`join_info`),
  KEY `index_user_id` (`user_id`),
  KEY `index_id_card_token` (`id_card_token`),
  KEY `index_batch` (`batch`),
  KEY `index_add_time` (`add_time`),
  KEY `index_ecard_id` (`ecard_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='小米北京消费券领取记录';

CREATE TABLE `tb_car_coupon` (
  `id` bigint(20) NOT NULL DEFAULT '0' COMMENT '优惠券编号',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户编号',
  `vid` varchar(20) NOT NULL DEFAULT '' COMMENT '汽车对应vid',
  `type_id` int(11) NOT NULL DEFAULT '0' COMMENT '优惠券类型编号',
  `activity_id` varchar(32) NOT NULL DEFAULT '0' COMMENT '活动编号',
  `start_time` varchar(20) NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` varchar(20) NOT NULL DEFAULT '0' COMMENT '过期时间',
  `days` int(11) NOT NULL DEFAULT '0' COMMENT '有效天数',
  `stat` varchar(10) NOT NULL DEFAULT 'unused' COMMENT '优惠券状态',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单编号',
  `use_time` int(11) NOT NULL DEFAULT '0' COMMENT '使用时间',
  `expire_time` int(11) NOT NULL DEFAULT '0' COMMENT '过期时间',
  `is_pass` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可用',
  `admin_id` int(11) NOT NULL DEFAULT '0' COMMENT '发放部门id',
  `admin_name` varchar(64) NOT NULL DEFAULT '' COMMENT '发放部门名称',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `send_type` varchar(10) NOT NULL DEFAULT 'marketing' COMMENT '优惠券发送类型, reissue(pulse用户中心补发优惠券),marketing(活动发券),external(外部调用pulse接口发券),orderEngine(追单发送的优惠券)',
  `from_order_id` varchar(64) NOT NULL DEFAULT '0' COMMENT '发券的订单编号',
  `replace_money` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '实际抵用金额',
  `invalid_time` int(11) NOT NULL DEFAULT '0' COMMENT '作废时间',
  `last_update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `offline` tinyint(1) NOT NULL DEFAULT '0' COMMENT '线下使用',
  `reduce_express` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '减免邮费',
  `parent_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '券父id',
  `send_channel` varchar(32) NOT NULL DEFAULT 'other' COMMENT '投放渠道',
  `request_id` varchar(64) NOT NULL DEFAULT '' COMMENT '请求ID',
  `extend_info` text COMMENT '扩展JSON信息',
  `biz_platform` tinyint(1) NOT NULL DEFAULT '0' COMMENT '所属业务 0 零售业务 3 整车销售 4 汽车售后',
  `times_limit` tinyint(1) NOT NULL DEFAULT '1' COMMENT '次数限制 1 限制 2 不限制',
  `used_times` int(10) NOT NULL DEFAULT '0' COMMENT '已用次数',
  `service_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '服务场景',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_idem_id` (`vid`,`send_channel`,`request_id`,`activity_id`),
  KEY `ix_add_time` (`add_time`),
  KEY `ix_vid` (`vid`),
  KEY `idx_type_id_stat` (`type_id`,`stat`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='汽车专用优惠券';

CREATE TABLE `tb_car_coupon_log` (
  `id` bigint(20) unsigned NOT NULL COMMENT '日志id',
  `coupon_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '汽车券id',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `order_id` varchar(64) NOT NULL DEFAULT '0' COMMENT '订单号',
  `vid` varchar(50) NOT NULL DEFAULT '' COMMENT '车辆vid',
  `log_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '日志类型 1:核销 2:退还',
  `log_desc` varchar(256) NOT NULL DEFAULT '' COMMENT '日志描述',
  `change_times` int(10) NOT NULL DEFAULT '0' COMMENT '使用次数变化 使用- 退还+',
  `biz_platform` tinyint(1) NOT NULL DEFAULT '0' COMMENT '所属业务 0:零售业务 3:整车销售 4:汽车售后',
  `add_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`),
  KEY `idx_vid` (`vid`),
  KEY `idx_coupon_id_type` (`coupon_id`,`log_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券核销流水';

CREATE TABLE `tb_car_coupon_opt` (
  `id` bigint(20) unsigned NOT NULL COMMENT 'id',
  `vid` varchar(20) NOT NULL DEFAULT '' COMMENT '汽车对应vid',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户编号',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单编号',
  `opt_type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '状态 1 锁定 2 回滚 3 核销',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_order_id_opt_type` (`vid`,`order_id`,`opt_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='汽车优惠券下单操作表';

CREATE TABLE `tb_codecoupon` (
  `id` bigint(20) NOT NULL DEFAULT '0' COMMENT '优惠券编号',
  `coupon_code` varchar(255) NOT NULL DEFAULT '' COMMENT '加密code',
  `coupon_index` varchar(32) NOT NULL DEFAULT '' COMMENT '索引',
  `type_id` int(11) NOT NULL DEFAULT '0' COMMENT '优惠券类型编号',
  `batch_id` varchar(32) NOT NULL DEFAULT '0' COMMENT '活动编号',
  `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '过期时间',
  `stat` enum('used','unused','expired','locked','invalid','cancel') NOT NULL DEFAULT 'unused' COMMENT '优惠券状态',
  `multiplex` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否可重复使用',
  `total_count` int(11) NOT NULL DEFAULT '0' COMMENT '初始总次数',
  `total_money` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '初始总金额',
  `replace_money` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '实际抵用金额',
  `reduce_express` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '实际减免邮费',
  `residue_count` int(11) NOT NULL DEFAULT '0' COMMENT '剩余次数    ',
  `residue_money` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '剩余金额',
  `offline` tinyint(1) NOT NULL DEFAULT '0' COMMENT '线下使用',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单编号',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户编号',
  `use_time` int(11) NOT NULL DEFAULT '0' COMMENT '使用时间',
  `expire_time` int(11) NOT NULL DEFAULT '0' COMMENT '过期时间',
  `admin_id` int(11) NOT NULL DEFAULT '0',
  `admin_desc` varchar(64) NOT NULL DEFAULT '',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `send_type` varchar(30) NOT NULL DEFAULT 'marketing' COMMENT '发送类型, reissue(pulse用户中心补发优惠券),marketing(活动发券),external(外部调用pulse接口发券),orderEngine(追单发送的优惠券),xiguaMarket(西瓜商超发券)',
  `from_order_id` varchar(64) NOT NULL DEFAULT '0' COMMENT '发券的订单编号',
  `invalid_time` int(11) NOT NULL DEFAULT '0' COMMENT '作废时间',
  `last_update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `use_mode` tinyint(1) NOT NULL DEFAULT '1' COMMENT '使用方式 1:明码 2:兑换',
  `coupon_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '兑换的优惠券ID',
  `org_code` varchar(30) NOT NULL DEFAULT '' COMMENT '兑换或使用的门店ID',
  `sync_status` int(11) NOT NULL DEFAULT '0' COMMENT '同步状态 0:未同步，1:已同步',
  PRIMARY KEY (`id`),
  KEY `ix_add_index` (`coupon_index`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_t_s` (`type_id`,`stat`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='有码优惠券';

CREATE TABLE `tb_codecoupon_log` (
  `coupon_index` varchar(32) NOT NULL DEFAULT '' COMMENT '索引',
  `log_type` int(11) NOT NULL DEFAULT '0' COMMENT '日志类型',
  `replace_money` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '实际抵用金额',
  `reduce_express` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '实际减免邮费',
  `old_residue_count` int(11) NOT NULL DEFAULT '0' COMMENT '老剩余次数',
  `old_residue_money` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '老剩余金额',
  `new_residue_count` int(11) NOT NULL DEFAULT '0' COMMENT '新剩余次数    ',
  `new_residue_money` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '新剩余金额',
  `offline` tinyint(1) NOT NULL DEFAULT '0' COMMENT '线下使用',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单编号',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户编号',
  `use_desc` varchar(64) NOT NULL DEFAULT '' COMMENT '使用描述',
  `admin_id` int(11) NOT NULL DEFAULT '0',
  `admin_desc` varchar(64) NOT NULL DEFAULT '',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`coupon_index`,`log_type`,`add_time`),
  KEY `ix_add_index1` (`coupon_index`),
  KEY `idx_user_id1` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='有码优惠券log';

CREATE TABLE `tb_codecoupon_receive_record` (
  `coupon_code` varchar(255) NOT NULL COMMENT '加密code',
  `coupon_index` varchar(32) NOT NULL DEFAULT '' COMMENT '索引',
  `type_id` int(11) NOT NULL DEFAULT '0' COMMENT '优惠券类型编号',
  `mobile_no` varchar(255) NOT NULL DEFAULT '' COMMENT '用户加密手机号',
  `external_id` varchar(255) NOT NULL DEFAULT '' COMMENT '外部唯一标识',
  `external_user_id` varchar(255) NOT NULL DEFAULT '' COMMENT '外部用户id',
  `assign_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '外投方式 1 小米投放 2 西瓜投放',
  `assign_time` int(11) NOT NULL DEFAULT '0' COMMENT '券领取时间',
  `assign_market` varchar(32) NOT NULL DEFAULT '' COMMENT '领取品牌',
  PRIMARY KEY (`coupon_code`),
  KEY `ix_coupon_index` (`coupon_index`),
  KEY `ix_external_id` (`external_id`),
  KEY `idx_t_c` (`type_id`,`coupon_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='优惠码领取记录表';

CREATE TABLE `tb_coupon` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '优惠券编号',
  `user_id` bigint(20) unsigned DEFAULT NULL COMMENT '用户编号',
  `type_id` int(11) DEFAULT '0' COMMENT '优惠券类型编号',
  `activity_id` varchar(32) DEFAULT '0' COMMENT '活动编号',
  `start_time` varchar(20) DEFAULT '0' COMMENT '开始时间',
  `end_time` varchar(20) DEFAULT '0' COMMENT '过期时间',
  `days` int(11) DEFAULT '0' COMMENT '有效天数',
  `stat` enum('used','unused','expired','locked','invalid','cancel','presented','received') DEFAULT 'unused' COMMENT '优惠券状态',
  `order_id` bigint(20) DEFAULT '0' COMMENT '订单编号',
  `use_time` int(11) DEFAULT '0' COMMENT '使用时间',
  `expire_time` int(11) DEFAULT '0' COMMENT '过期时间',
  `is_pass` tinyint(1) DEFAULT '1' COMMENT '是否可用',
  `admin_id` int(11) DEFAULT '0',
  `admin_name` varchar(64) DEFAULT NULL,
  `add_time` int(11) DEFAULT '0' COMMENT '添加时间',
  `send_type` enum('reissue','marketing','orderEngine','external') NOT NULL DEFAULT 'marketing' COMMENT '优惠券发送类型, reissue(pulse用户中心补发优惠券),marketing(活动发券),external(外部调用pulse接口发券),orderEngine(追单发送的优惠券)',
  `from_order_id` varchar(64) DEFAULT '0' COMMENT '发券的订单编号',
  `replace_money` decimal(16,2) DEFAULT '0.00' COMMENT '实际抵用金额',
  `invalid_time` int(11) DEFAULT NULL COMMENT '作废时间',
  `last_update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `offline` tinyint(1) NOT NULL DEFAULT '0' COMMENT '线下使用',
  `reduce_express` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '减免邮费',
  `parent_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '券父id',
  `send_channel` varchar(32) NOT NULL DEFAULT 'other' COMMENT '投放渠道',
  `request_id` varchar(64) DEFAULT NULL COMMENT '请求ID',
  `extend_info` text NOT NULL COMMENT '扩展信息',
  `biz_platform` tinyint(1) NOT NULL DEFAULT '0' COMMENT '所属业务 0 零售业务 3 整车销售 4 汽车售后',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_idem_id1` (`user_id`,`send_channel`,`request_id`,`activity_id`),
  KEY `ix_add_time1` (`add_time`),
  KEY `idx_t_s1` (`type_id`,`stat`),
  KEY `idx_s_e` (`stat`,`end_time`),
  KEY `idx_user_stat_end_time` (`user_id`,`stat`,`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='优惠券';

CREATE TABLE `tb_coupon_gift` (
  `coupon_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '赠送的券ID',
  `new_coupon_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '新领取的券id',
  `token` varchar(64) NOT NULL DEFAULT '' COMMENT '唯一授权码',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '赠送人id',
  `recipient_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '领取人',
  `giving_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '赠送时间',
  `recipient_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '领取时间',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态，1，赠送，2，领取',
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除，1，删除，0，正常',
  PRIMARY KEY (`coupon_id`),
  UNIQUE KEY `tocken` (`token`),
  KEY `giving_uid` (`user_id`),
  KEY `recipient_uid` (`recipient_uid`),
  KEY `giving_time` (`giving_time`),
  KEY `recipient_time` (`recipient_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='优惠券赠送记录表';

CREATE TABLE `tb_coupon_log` (
  `coupon_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '优惠券ID',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户编号',
  `type` varchar(16) DEFAULT '' COMMENT '日志类型',
  `old_stat` varchar(16) NOT NULL DEFAULT '' COMMENT '优惠券原始状态',
  `new_stat` varchar(16) NOT NULL DEFAULT '' COMMENT '优惠券最新状态',
  `admin_id` int(11) DEFAULT '0',
  `admin_name` varchar(64) DEFAULT NULL,
  `coupon_desc` varchar(256) DEFAULT NULL COMMENT '描述',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `offline` tinyint(1) NOT NULL DEFAULT '0' COMMENT '线下使用',
  PRIMARY KEY (`coupon_id`,`user_id`,`add_time`),
  KEY `ix_add_time2` (`add_time`),
  KEY `ix_user_id` (`user_id`),
  KEY `ix_type` (`type`),
  KEY `coupon_id` (`coupon_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='优惠券日志';

CREATE TABLE `tb_coupon_opt` (
  `id` bigint(20) unsigned NOT NULL COMMENT 'id',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户编号',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单编号',
  `opt_type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '状态 1 锁定 2 回滚 3 核销',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_order_id_opt_type1` (`user_id`,`order_id`,`opt_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券下单操作表';

CREATE TABLE `tb_ecard` (
  `card_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'ecard编号',
  `password` varchar(255) NOT NULL DEFAULT '' COMMENT 'ecard密码',
  `password_idx` varchar(32) NOT NULL DEFAULT '' COMMENT 'ecard密码索引',
  `sn` varchar(32) NOT NULL DEFAULT '' COMMENT 'sn',
  `user_id` bigint(20) unsigned DEFAULT '0' COMMENT '用户ID',
  `type_id` int(11) DEFAULT '0' COMMENT 'ecard类型ID',
  `sku` varchar(16) NOT NULL DEFAULT '' COMMENT 'e卡原始SKU',
  `money` decimal(10,2) DEFAULT '0.00' COMMENT '面值',
  `balance` decimal(10,2) DEFAULT '0.00' COMMENT '余额',
  `sale_price` decimal(10,2) DEFAULT '0.00' COMMENT '售出价',
  `mission_id` varchar(32) DEFAULT '0' COMMENT '生成任务ID',
  `area_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '区域id',
  `start_time` varchar(20) DEFAULT '0' COMMENT '有效期开始时间',
  `end_time` varchar(20) DEFAULT '0' COMMENT '有效期结束时间',
  `stat` tinyint(1) DEFAULT '0' COMMENT '0预开卡，1开卡，2开卡作废，3激活，4激活作废，5绑定，6绑定作废',
  `is_locked` tinyint(1) DEFAULT '0' COMMENT '0未锁定，1锁定',
  `is_virtual` tinyint(1) DEFAULT '0' COMMENT '1虚拟卡,0实物卡',
  `from_user_id` bigint(20) unsigned DEFAULT '0' COMMENT '购买的用户ID',
  `from_order_id` varchar(64) DEFAULT '0' COMMENT '购买的订单号',
  `send_type` enum('reissue','marketing','external') NOT NULL DEFAULT 'marketing' COMMENT '生成类型, reissue(后台生成),marketing(任务生成),external(接口生成)',
  `add_time` int(11) DEFAULT '0' COMMENT '添加时间',
  `invalid_time` int(11) DEFAULT '0' COMMENT '作废时间',
  `active_time` int(11) DEFAULT '0' COMMENT '激活时间',
  `bind_time` int(11) DEFAULT '0' COMMENT '绑定时间',
  `delay_times` tinyint(1) DEFAULT '0' COMMENT '延期次数',
  `last_update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `is_casual` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否是临时卡',
  PRIMARY KEY (`card_id`),
  KEY `ix_add_time3` (`add_time`),
  KEY `ix_user_id1` (`user_id`),
  KEY `ix_stat` (`stat`),
  KEY `idx_type_id` (`type_id`),
  KEY `idx_money` (`money`),
  KEY `idx_balance` (`balance`),
  KEY `ix_from_order_id` (`from_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='ecard列表';

CREATE TABLE `tb_ecard_log` (
  `card_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'ecard编号',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `order_id` varchar(64) NOT NULL DEFAULT '0' COMMENT '订单号',
  `refund_no` bigint(20) NOT NULL DEFAULT '0' COMMENT '退款单ID',
  `log_type` tinyint(1) DEFAULT '0' COMMENT '日志类型：-1系统异常日志，0系统正常日志，1消费（含退款）',
  `income` decimal(10,2) DEFAULT '0.00' COMMENT '金额变化',
  `old_balance` decimal(10,2) DEFAULT '0.00' COMMENT '老余额',
  `new_balance` decimal(10,2) DEFAULT '0.00' COMMENT '新余额',
  `operator_id` bigint(20) unsigned DEFAULT '0' COMMENT '操作人ID，系统操作为0',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `description` varchar(255) DEFAULT '' COMMENT '描述',
  `last_update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `hash_code` varchar(32) NOT NULL DEFAULT '',
  `offline` tinyint(1) NOT NULL DEFAULT '0' COMMENT '线下使用',
  PRIMARY KEY (`card_id`,`add_time`,`hash_code`),
  KEY `ix_add_time4` (`add_time`),
  KEY `ix_user_id2` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='ecard日志列表';

CREATE TABLE `tb_equipment_fix` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '小米账号',
  `device_id` varchar(64) NOT NULL DEFAULT '' COMMENT '设备唯一表示符号',
  `sku_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'sku_id',
  `device_type` varchar(20) NOT NULL DEFAULT '' COMMENT '类型',
  `ts` int(20) unsigned NOT NULL DEFAULT '0' COMMENT '事件时间戳',
  `msg_ts` int(20) unsigned NOT NULL DEFAULT '0' COMMENT '消息接受时间，写入talos时间',
  `add_time` int(20) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `msg_status` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '消息状态：0-未处理，1-已处理',
  `try_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '尝试次数',
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_skuid` (`sku_id`,`msg_status`,`add_time`),
  KEY `idx_device` (`device_id`,`device_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='硬件设备数据修复';

CREATE TABLE `tb_growth_credit` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'MID',
  `score` int(10) NOT NULL DEFAULT '0' COMMENT '信用分',
  `reason` varchar(255) NOT NULL DEFAULT '' COMMENT '原因',
  `punish_type_id` int(11) unsigned NOT NULL COMMENT '处罚类型id',
  `add_time` int(10) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `add_user` varchar(32) NOT NULL COMMENT '添加人',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_user` varchar(32) NOT NULL COMMENT '更新人',
  `status` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '状态 1可用 0不可用',
  PRIMARY KEY (`id`),
  KEY `idx_user_id2` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会员成长值-信誉值';

CREATE TABLE `tb_growth_no_user` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '小米账号',
  `add_time` int(10) NOT NULL COMMENT '添加时间',
  `status` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '状态 1可用 0不可用',
  PRIMARY KEY (`id`),
  KEY `idx_user_id3` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='没成长值用户';

CREATE TABLE `tb_growth_no_user_v2` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '小米账号',
  `add_time` int(10) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `status` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '状态 1可用 0不可用',
  PRIMARY KEY (`id`),
  KEY `idx_user_id4` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='没成长值用户v2';

CREATE TABLE `tb_growth_punish_type` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `name` varchar(255) NOT NULL COMMENT '类型名称',
  `score` int(11) unsigned NOT NULL COMMENT '处罚百分比数值',
  `status` enum('Y','N') NOT NULL DEFAULT 'N' COMMENT '上线状态,Y:上线，N:下线',
  `create_time` datetime NOT NULL COMMENT '添加时间',
  `create_user` varchar(32) NOT NULL COMMENT '添加人',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_user` varchar(32) NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规处罚类型数据表';

CREATE TABLE `tb_growth_score` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `month` int(11) NOT NULL DEFAULT '0' COMMENT '月',
  `initial_gval` int(11) NOT NULL DEFAULT '0' COMMENT '声明周期成长值，基础分',
  `shop_gval` int(11) NOT NULL DEFAULT '0' COMMENT '交易成长值，购物分',
  `active_gval` int(11) NOT NULL DEFAULT '0' COMMENT '活跃成长值，活跃分',
  `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '行记录时间',
  `rep_gval` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_idx_user_id_month` (`user_id`,`month`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='成长值表';

CREATE TABLE `tb_growth_score_v2` (
  `id` bigint(10) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'MID',
  `base` int(11) NOT NULL DEFAULT '0' COMMENT '基础',
  `shop` int(11) NOT NULL DEFAULT '0' COMMENT '购物',
  `smart` int(11) NOT NULL DEFAULT '0' COMMENT '智能硬件',
  `active` int(11) NOT NULL DEFAULT '0' COMMENT '活动',
  `reward` int(11) NOT NULL DEFAULT '0' COMMENT '奖励',
  `interaction` int(11) NOT NULL DEFAULT '0' COMMENT '奖励',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `month` int(11) NOT NULL DEFAULT '0' COMMENT '月',
  PRIMARY KEY (`id`),
  KEY `idx_uid_month` (`user_id`,`month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tb_old_intransit_points` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '小米账号',
  `points` varchar(16) NOT NULL DEFAULT '' COMMENT '在途积分带小数',
  `score` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '在途积分',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '接口添加时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '行记录添加时间',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '原始订单id',
  `shop_price` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '原始shop_price',
  `signed_time` int(11) NOT NULL DEFAULT '0' COMMENT '原始妥投时间',
  `is_update` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否升级，0-否，1-是',
  `goods_id` bigint(20) NOT NULL DEFAULT '0',
  `extend` varchar(4196) NOT NULL DEFAULT '' COMMENT '在途接口冗余信息',
  PRIMARY KEY (`id`),
  KEY `idx_user_id5` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户在途积分-旧';

CREATE TABLE `tb_old_usable_points` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '小米账号',
  `points` varchar(16) NOT NULL DEFAULT '' COMMENT '可用积分带小数',
  `score` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '可用积分',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '行记录添加时间',
  `is_update` tinyint(4) NOT NULL DEFAULT '0' COMMENT '旧积分是否已升级,1升级',
  PRIMARY KEY (`id`),
  KEY `idx_user_id6` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户可用积分-旧';

CREATE TABLE `tb_order_activity` (
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单编号',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户编号',
  `activityDiscountMoney` decimal(10,2) DEFAULT '0.00' COMMENT '活动优惠总金额',
  `orderMoney` decimal(10,2) DEFAULT '0.00' COMMENT '订单结算总金额',
  `productMoney` decimal(10,2) DEFAULT '0.00' COMMENT '商品总金额',
  `items` longtext NOT NULL COMMENT '订单项目列表',
  `coupons` text COMMENT '赠券信息json',
  `joinedActs` text COMMENT '已经参加的活动',
  `expressCost` decimal(10,2) DEFAULT '0.00' COMMENT '邮费',
  `discountMin` decimal(10,2) DEFAULT '0.00' COMMENT '标准免邮金额',
  `vcodeReplaceMoney` decimal(10,2) DEFAULT '0.00' COMMENT '券码抵扣金额',
  `moneyCouponReplaceMoney` decimal(10,2) DEFAULT '0.00' COMMENT '现金券抵扣金额',
  `discountCouponReplaceMoney` decimal(10,2) DEFAULT '0.00' COMMENT '折扣券抵扣金额',
  `reduction` text COMMENT '减免',
  `moneyCouponIds` varchar(512) DEFAULT NULL COMMENT '现金券编号,号分割',
  `vcode` varchar(64) DEFAULT NULL COMMENT '推广券码',
  `discountCouponId` varchar(64) DEFAULT NULL COMMENT '打折券',
  `presentCouponId` varchar(64) DEFAULT NULL COMMENT '礼品券ID',
  `appId` bigint(20) DEFAULT NULL COMMENT '订单来源应用ID',
  `level` int(11) DEFAULT NULL COMMENT '级别',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `actDetail` mediumtext COMMENT '购物车参加活动的明细',
  `pushStatus` tinyint(1) NOT NULL DEFAULT '0' COMMENT '推送财务基础数据状态，0默认，1推送成功，2推送失败，3无效订单',
  `expressReplaceDetail` text NOT NULL COMMENT '减免邮费明细',
  `syncStatus` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT 'OC系统推送状态 0-未推送 1-已推送',
  PRIMARY KEY (`order_id`,`user_id`,`add_time`),
  UNIQUE KEY `uniq_order_user` (`user_id`,`order_id`),
  KEY `ix_add_time5` (`add_time`),
  KEY `ix_order_id` (`order_id`),
  KEY `ix_user_id3` (`user_id`),
  KEY `ix_pushStatus` (`pushStatus`),
  KEY `ix_syncStatus` (`syncStatus`),
  KEY `idx_p_s_a` (`pushStatus`,`syncStatus`,`add_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='促销订单记录';

CREATE TABLE `tb_order_benefit` (
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单编号',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户编号',
  `all_reduce_money` decimal(10,2) DEFAULT '0.00' COMMENT '优惠总金额',
  `order_money` decimal(10,2) DEFAULT '0.00' COMMENT '订单结算总金额',
  `cart_money` decimal(10,2) DEFAULT '0.00' COMMENT '购物车商品总金额（加入购物车时各金额总和）',
  `product_money` decimal(10,2) DEFAULT '0.00' COMMENT '原商品总金额',
  `items` longtext NOT NULL COMMENT '订单项目列表',
  `used_coupons` text COMMENT '已经使用的优惠券',
  `joined_acts` text COMMENT '已经参加的活动',
  `coupon_reduce_money` decimal(10,2) DEFAULT '0.00' COMMENT '无码券抵扣商品金额',
  `code_coupon_reduce_money` decimal(10,2) DEFAULT '0.00' COMMENT '有码券抵扣商品金额',
  `coupon_express_reduce_money` decimal(10,2) DEFAULT '0.00' COMMENT '优惠券邮费减免',
  `coupon_other_reduce_money` decimal(10,2) DEFAULT '0.00' COMMENT '优惠券其他减免',
  `coupon_ids` varchar(512) DEFAULT NULL COMMENT '无码券编号,号分割',
  `code_coupon_ids` varchar(64) DEFAULT NULL COMMENT '有码券code',
  `app_id` bigint(20) DEFAULT NULL COMMENT '订单来源应用ID',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `act_detail` mediumtext COMMENT '购物车参加活动的明细',
  `push_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '推送财务基础数据状态，0默认，1推送成功，2推送失败，3无效订单',
  `lock_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '优惠资格锁定状态，1锁定（默认），0已经释放',
  `used_redpacket` text COMMENT '已经使用的红包',
  `redpacket_reduce_money` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '红包减免',
  `org_code` varchar(100) NOT NULL DEFAULT '' COMMENT '机构id，线下门店使用',
  `uid_type` varchar(100) NOT NULL DEFAULT '' COMMENT '用户id类型,mobile-手机号',
  PRIMARY KEY (`order_id`,`user_id`),
  KEY `id_add_time` (`add_time`),
  KEY `id_order_id` (`order_id`),
  KEY `id_user_id` (`user_id`),
  KEY `id_push_status` (`push_status`),
  KEY `idx_lock_push_status` (`lock_status`,`push_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单优惠记录';

CREATE TABLE `tb_order_promotion` (
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单编号',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户编号',
  `promotion_detail` longtext NOT NULL COMMENT '订单优惠明细json',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`order_id`,`user_id`),
  KEY `id_add_time1` (`add_time`),
  KEY `id_user_id1` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单优惠明细记录';

CREATE TABLE `tb_order_promotion_detail` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) NOT NULL DEFAULT '-1' COMMENT '订单编号',
  `order_type` int(10) NOT NULL DEFAULT '-1' COMMENT '订单类型：1：正向交易优惠 2：追加优惠 @OrderTypeEnum',
  `order_scene` int(10) NOT NULL DEFAULT '-1' COMMENT '订单场景：0:3C 1:团购 2:融合三方 3:汽车 4:维保售后 5:车商城 @BizPlatformEnum',
  `promotion_id` bigint(20) NOT NULL DEFAULT '-1' COMMENT '促销ID',
  `promotion_type` int(10) NOT NULL DEFAULT '-1' COMMENT '促销类型 @PromotionType',
  `promotion_detail` text COMMENT '订单优惠明细json @OrderPromotionDetailModel',
  `user_id` bigint(20) NOT NULL DEFAULT '-1' COMMENT '用户编号',
  `add_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_orderId_orderType` (`order_id`,`order_type`),
  KEY `idx_userId` (`user_id`),
  KEY `idx_orderScene` (`order_scene`),
  KEY `idx_addTime` (`add_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单优惠分摊明细表';

CREATE TABLE `tb_order_promotion_detail_status` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `order_id` bigint(20) NOT NULL DEFAULT '-1' COMMENT '订单编号',
  `order_type` int(10) NOT NULL DEFAULT '-1' COMMENT '订单类型：1：正向交易优惠 2：追加优惠 @OrderTypeEnum',
  `order_scene` int(10) NOT NULL DEFAULT '-1' COMMENT '订单场景：0:3C 1:团购 2:融合三方 3:汽车 4:维保售后 5:车商城 @BizPlatformEnum',
  `promotion_id` bigint(20) NOT NULL DEFAULT '-1' COMMENT '促销ID',
  `promotion_type` int(10) NOT NULL DEFAULT '-1' COMMENT '促销类型 @PromotionType',
  `promotion_detail` text COMMENT '订单优惠明细json @OrderPromotionDetailModel',
  `user_id` bigint(20) NOT NULL DEFAULT '-1' COMMENT '用户编号',
  `status` int(10) NOT NULL DEFAULT '-1' COMMENT '状态：0:锁定 1：提交 2：回滚 3:驳回',
  `add_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_orderId_orderType1` (`order_id`,`order_type`),
  KEY `idx_addTime1` (`add_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单优惠分摊明细状态表';

CREATE TABLE `tb_phoenix_discount` (
  `serial_no` bigint(20) NOT NULL DEFAULT '0' COMMENT '流水号',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '小米ID',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '小米订单号(SA)',
  `op_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '操作类型 1下单冻结 2支付前解冻 3支付后核销 4支付后退款',
  `amount` bigint(20) NOT NULL DEFAULT '0' COMMENT '操作金额(分)',
  `channel_id` tinyint(1) NOT NULL DEFAULT '0' COMMENT '渠道ID 1联通华盛',
  `org_code` varchar(20) NOT NULL DEFAULT '' COMMENT '门店编码',
  `order_time` int(11) NOT NULL DEFAULT '0' COMMENT '小米订单生成时间(SA)',
  `refund_no` bigint(20) NOT NULL DEFAULT '0' COMMENT '小米退款单号(OD)',
  `third_refund_no` varchar(32) NOT NULL DEFAULT '' COMMENT '第三方退款单号',
  `txid` varchar(64) NOT NULL DEFAULT '' COMMENT '联通返回的交易txid',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '描述',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`serial_no`),
  KEY `idx_acoo` (`add_time`,`channel_id`,`org_code`,`op_type`),
  KEY `idx_txid` (`txid`),
  KEY `idx_order_id4` (`order_id`),
  KEY `idx_user_id7` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='三方优惠流水表';

CREATE TABLE `tb_phoenix_package` (
  `id` bigint(20) NOT NULL DEFAULT '0' COMMENT '主键编号',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '小米ID',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '小米订单号(SA)',
  `business_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '业务类型 1:联通 2:电信 3:移动',
  `package_code` varchar(30) NOT NULL DEFAULT '' COMMENT '套餐编码',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态 1:冻结 2:核销',
  `org_code` varchar(20) NOT NULL DEFAULT '' COMMENT '门店编码',
  `order_time` int(11) NOT NULL DEFAULT '0' COMMENT '小米订单生成时间(SA)',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `ext_info` varchar(100) NOT NULL DEFAULT '' COMMENT '扩展信息',
  `valid_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1:有效 2:失效',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_idem_order_id` (`order_id`),
  KEY `idx_code_valid_status` (`package_code`,`valid_status`),
  KEY `idx_user_order_status_id` (`user_id`,`order_id`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='三方优惠套餐记录表';

CREATE TABLE `tb_points_detail` (
  `detail_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '唯一明细ID',
  `log_id` varchar(64) NOT NULL DEFAULT '' COMMENT '唯一log ID',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '小米账号',
  `points_operate` smallint(8) unsigned NOT NULL DEFAULT '0' COMMENT '积分操作 1增加 2扣减',
  `rights_type` varchar(30) NOT NULL DEFAULT '' COMMENT '权益类型 coupon:兑换优惠券,redpacket:兑换红包,product:兑换商品,recharge:充值金,code:明码 sign_in:签到,point_compete_treasure:积分夺宝,luck_draw:抽奖 transfer:旧积分迁移 equipment:设备',
  `rights_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '权益id，比如红包类型id、优惠券类型id、货品或套装id',
  `rights_name` varchar(255) NOT NULL DEFAULT '' COMMENT '权益名称',
  `item_no` smallint(5) NOT NULL DEFAULT '0' COMMENT '原始订单的item_no',
  `item_index` smallint(5) NOT NULL DEFAULT '0' COMMENT 'item_index',
  `points_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '积分数量',
  `end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '失效时间',
  `client_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'client_id',
  `channel_id` varchar(64) NOT NULL DEFAULT '' COMMENT '渠道id',
  `add_time` int(20) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `update_time` int(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `points_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '积分状态 1可用 2在途 3退款 4删除 5可领取',
  `is_old` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否迁移 0否 1是',
  `rights_detail` mediumtext NOT NULL COMMENT '详情',
  `virtual_code` varchar(255) NOT NULL DEFAULT '' COMMENT '虚拟号',
  `idempotent_param` varchar(64) NOT NULL DEFAULT '' COMMENT '幂等参数',
  `start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '生效时间',
  `shop_price` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '实付金额',
  `signed_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '妥投时间',
  `origin_order_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '原始订单号',
  `order_id` varchar(20) NOT NULL DEFAULT '' COMMENT '订单号',
  `origin_points_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '原始积分数量',
  `sent_ratio` decimal(15,2) NOT NULL DEFAULT '1.00' COMMENT '积分发放比例',
  `cart_price` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '购物车价格',
  `remain_amount` int(11) NOT NULL DEFAULT '-1' COMMENT '关联扣减后剩余米金数',
  `can_use_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '到账时间 毫秒',
  PRIMARY KEY (`detail_id`),
  KEY `idx_uppp` (`user_id`,`points_operate`,`rights_type`,`points_status`),
  KEY `idx_add_time` (`add_time`),
  KEY `idx_request_id` (`log_id`),
  KEY `idx_update_time_status` (`update_time`,`points_status`),
  KEY `idx_prors` (`points_operate`,`rights_type`,`origin_order_id`,`rights_id`,`shop_price`),
  KEY `idx_orderid` (`order_id`),
  KEY `idx_origin_oid` (`origin_order_id`),
  KEY `idx_idem` (`idempotent_param`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_can_use_time` (`can_use_time`),
  KEY `idx_spr` (`signed_time`,`points_status`,`rights_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户积分明细';

CREATE TABLE `tb_points_detail_old` (
  `detail_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '唯一明细ID',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '小米账号',
  `rights_name` varchar(255) NOT NULL DEFAULT '' COMMENT '权益名称',
  `points_count` int(11) NOT NULL DEFAULT '0' COMMENT '积分数量',
  `add_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '记录添加时间',
  PRIMARY KEY (`detail_id`),
  KEY `idx_user1` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户积分明细-旧';

CREATE TABLE `tb_points_equipment_detail` (
  `id` bigint(20) unsigned NOT NULL COMMENT '设备明细ID',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '小米账号',
  `sku_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'sku_id',
  `product_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'product_id',
  `class_id` int(10) unsigned NOT NULL DEFAULT '0',
  `points_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '积分状态 1待解锁 2已解锁',
  `device_id` varchar(128) NOT NULL DEFAULT '' COMMENT '设备唯一表示符号',
  `device_type` varchar(20) NOT NULL DEFAULT '' COMMENT '类型',
  `extend` mediumtext NOT NULL COMMENT '扩展信息',
  `points_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '积分数量',
  `add_time` int(20) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `update_time` int(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_up` (`user_id`),
  KEY `idx_u_status` (`user_id`,`points_status`,`update_time`),
  KEY `idx_device1` (`device_id`,`device_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='硬件设备积分明细';

CREATE TABLE `tb_points_log` (
  `log_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '唯一日志ID',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '小米账号',
  `client_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'client_id',
  `request_id` varchar(64) NOT NULL DEFAULT '' COMMENT '唯一请求ID',
  `data` text NOT NULL COMMENT '详细json数据',
  `add_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_user_id8` (`user_id`),
  KEY `idx_request_id1` (`request_id`),
  KEY `idx_add_time1` (`add_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='积分日志';

CREATE TABLE `tb_points_order_idempotent` (
  `idem_key` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '幂等key(订单号或发货单号)',
  `idem_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '幂等类型 1支付 2妥投 3退款',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '小米账号',
  `add_time` int(20) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`idem_key`,`idem_type`),
  KEY `idx_uid1` (`user_id`),
  KEY `idx_add_time2` (`add_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='积分处理订单幂等表';

CREATE TABLE `tb_points_user` (
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '小米账号',
  `total_points` int(11) NOT NULL DEFAULT '0' COMMENT '总积分',
  `expire_points` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '过期积分',
  `intransit_points` int(11) NOT NULL DEFAULT '0' COMMENT '在途积分',
  `used_points` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '已用积分',
  `usable_points` int(11) NOT NULL DEFAULT '0' COMMENT '可用积分',
  `expire_time` int(11) NOT NULL DEFAULT '0' COMMENT '过期执行时间',
  `add_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `user_status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '用户状态',
  `is_update` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否升级，0-否，1-是',
  `equipment_points` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '设备积分',
  PRIMARY KEY (`user_id`),
  KEY `idx_add_time3` (`add_time`),
  KEY `idx_update_time` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='积分用户信息';

CREATE TABLE `tb_rp_log` (
  `redpacket_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '红包id',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '使用的用户id',
  `order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '订单id',
  `income` int(11) NOT NULL DEFAULT '0' COMMENT '红包使用金额，单位分，负数为扣，正数为还',
  `op_type` tinyint(3) NOT NULL DEFAULT '0' COMMENT '1,消费;2,关单;3,退款',
  `old_balance` int(11) NOT NULL DEFAULT '0' COMMENT '原来余额',
  `new_balance` int(11) NOT NULL DEFAULT '0' COMMENT '最新余额',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`redpacket_id`,`add_time`,`op_type`),
  KEY `user_id` (`user_id`),
  KEY `order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='红包消费日志表';

CREATE TABLE `tb_rp_redpacket` (
  `redpacket_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '红包id',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
  `type_id` int(11) NOT NULL DEFAULT '0' COMMENT '红包类型id',
  `mission_id` int(11) NOT NULL DEFAULT '0' COMMENT '红包任务id',
  `uniq_id` varchar(64) NOT NULL DEFAULT '' COMMENT '幂等id,唯一,类似订单号',
  `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '红包生效时间',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '红包的过期时间，时间戳',
  `amount` int(11) NOT NULL DEFAULT '0' COMMENT '红包面额，单位分',
  `balance` int(11) NOT NULL DEFAULT '0' COMMENT '红包余额，单位分',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '红包更新时间',
  PRIMARY KEY (`redpacket_id`),
  UNIQUE KEY `mission_id` (`mission_id`,`uniq_id`),
  KEY `user_id1` (`user_id`),
  KEY `type_id` (`type_id`),
  KEY `start_time` (`start_time`),
  KEY `end_time` (`end_time`),
  KEY `amount` (`amount`),
  KEY `balance` (`balance`),
  KEY `add_time` (`add_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户红包列表';

CREATE TABLE `tb_user_points_consume_detail` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `user_id` bigint(20) NOT NULL COMMENT '小米账号',
  `exchange_detail_id` bigint(20) unsigned NOT NULL COMMENT '兑换记录，tb_points_detail对应detail_id',
  `consume_detail_id` bigint(20) NOT NULL COMMENT 'tb_user_usable_points_detail，对应detail_id',
  `consume_points` int(10) NOT NULL COMMENT '消耗米金',
  `verify_channel` int(10) NOT NULL COMMENT '对账渠道',
  `add_time` int(10) NOT NULL COMMENT '兑换时间',
  PRIMARY KEY (`id`),
  KEY `idx_add_time4` (`add_time`),
  KEY `idx_uid_edid` (`user_id`,`exchange_detail_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户消耗米金明细';

CREATE TABLE `tb_user_usable_points_detail` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `detail_id` bigint(20) unsigned NOT NULL COMMENT 'detial表id',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '小米账号',
  `total_points` int(10) NOT NULL COMMENT '总米金',
  `verify_channel` int(10) NOT NULL DEFAULT '1' COMMENT '对账渠道',
  `balance` int(10) NOT NULL COMMENT '剩余米金',
  `add_time` int(10) NOT NULL COMMENT '到账时间',
  `update_time` int(10) NOT NULL COMMENT '更新时间',
  `expire_time` int(10) NOT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_uid_did_vc` (`user_id`,`detail_id`,`verify_channel`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户可用米金明细';

CREATE TABLE `tbl_benefit_voucher` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `unique_id` varchar(100) NOT NULL DEFAULT '0' COMMENT '发卡批次id',
  `source_app` varchar(20) NOT NULL DEFAULT '0' COMMENT '业务类型',
  `third_part_channel` int(10) NOT NULL DEFAULT '0' COMMENT '发卡渠道',
  `status` int(2) NOT NULL DEFAULT '0' COMMENT '发卡状态, 0:已创建 1:失败 2:发送成功 ',
  `fail_count` int(10) DEFAULT '0' COMMENT '发卡失败次数',
  `voucher_send_time` int(11) NOT NULL DEFAULT '0' COMMENT '发卡成功时间',
  `send_fail_reason` varchar(1000) DEFAULT '' COMMENT '发卡失败原因',
  `created_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `created_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '修改人',
  `updated_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_id_source` (`unique_id`,`source_app`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='权益礼品卡发放信息主表';

CREATE TABLE `tbl_benefit_voucher_detail` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `unique_id` varchar(100) NOT NULL DEFAULT '0' COMMENT '发卡批次id',
  `source_app` varchar(20) NOT NULL DEFAULT '0' COMMENT '业务来源',
  `bill_id` varchar(100) NOT NULL DEFAULT '0' COMMENT '单据id',
  `buyer_tel_idx` char(16) NOT NULL DEFAULT '' COMMENT '发卡人手机号密文索引',
  `buyer_tel_c` varchar(256) NOT NULL DEFAULT '' COMMENT '发卡人手机号',
  `receiver_tel_idx` char(16) NOT NULL DEFAULT '' COMMENT '收卡人手机号密文索引',
  `receiver_tel_c` varchar(256) NOT NULL DEFAULT '' COMMENT '接受人手机号',
  `mid` bigint(20) NOT NULL DEFAULT '0' COMMENT '收卡人mid',
  `voucher_id` varchar(100) DEFAULT '' COMMENT '卡券id',
  `voucher_type` int(10) NOT NULL DEFAULT '0' COMMENT '卡类型',
  `voucher_amount` bigint(20) NOT NULL DEFAULT '0' COMMENT '卡面额',
  `voucher_remain_amount` bigint(20) NOT NULL DEFAULT '0' COMMENT '卡余额',
  `effect_period_start` bigint(20) NOT NULL DEFAULT '0' COMMENT '卡有效期起',
  `effect_period_end` bigint(20) NOT NULL DEFAULT '0' COMMENT '卡有效期止',
  `effect_flag` int(2) NOT NULL DEFAULT '0' COMMENT '卡有效标志 0:无效 1:有效 ',
  `reason` varchar(200) NOT NULL DEFAULT '0' COMMENT '卡作废理由',
  `created_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `created_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '修改人',
  `updated_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_id_source1` (`unique_id`,`source_app`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='权益礼品卡发放明细表';

CREATE TABLE `trade_in_qualification` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `mid` bigint(20) NOT NULL COMMENT '用户mid',
  `qualify_user` varchar(255) NOT NULL COMMENT '加密存储的用户身份认证',
  `qualify_seq` varchar(255) NOT NULL COMMENT '资格码序列号',
  `qualify_type` varchar(20) NOT NULL COMMENT '品类编码',
  `write_off_status` tinyint(4) NOT NULL COMMENT '核销状态（0:未核销 1已核销）',
  `bind_status` tinyint(4) NOT NULL COMMENT '绑定状态(0:未绑定 1已绑定)',
  `order_id` bigint(20) NOT NULL COMMENT '订单号',
  `sku` int(11) NOT NULL COMMENT 'SKU',
  `encrypted_cert_name` varchar(255) NOT NULL COMMENT '加密的用户名称',
  `encrypted_cert_no` varchar(255) NOT NULL COMMENT '加密的实名认证号',
  `encrypted_mobile_phone` varchar(255) NOT NULL COMMENT '加密后的手机号',
  `idcard_type` tinyint(4) NOT NULL COMMENT '实名认证方式 1.身份证',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `phone_idx` varchar(255) NOT NULL COMMENT '手机号md5，便于检索',
  `cert_no_idx` varchar(255) NOT NULL COMMENT '身份证号md5,便于检索',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_qualify_seq` (`qualify_seq`),
  UNIQUE KEY `unique_mid_qualify_type` (`mid`,`qualify_type`),
  KEY `idx_order_id5` (`order_id`),
  KEY `idx_mid2` (`mid`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='以旧换新资格码表';

CREATE TABLE `trade_in_qualification_change_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `qualify_id` bigint(20) NOT NULL COMMENT '资格码id',
  `qualify_seq` varchar(255) NOT NULL COMMENT '资格码序列号',
  `action_type` varchar(255) NOT NULL COMMENT '变更动作',
  `content` text COMMENT '变更前的数据快照信息',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `mid` bigint(20) NOT NULL COMMENT '用户mid',
  PRIMARY KEY (`id`),
  KEY `idx_qualify_seq` (`qualify_seq`),
  KEY `idx_mid3` (`mid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='以旧换新资格码变更日志表';

CREATE TABLE `trade_in_qualify` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `mid` bigint(20) unsigned NOT NULL COMMENT '用户mid',
  `qualify_user` varchar(255) NOT NULL COMMENT '加密存储的用户身份认证',
  `qualify_seq` varchar(255) NOT NULL COMMENT '资格码序列号',
  `qualify_type` varchar(20) NOT NULL COMMENT '品类编码',
  `write_off_status` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '核销状态（0:未核销 1已核销）',
  `bind_status` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '绑定状态(0:未绑定 1已绑定)',
  `order_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '订单号',
  `sku` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'SKU',
  `encrypted_cert_name` varchar(255) NOT NULL COMMENT '加密的用户名称',
  `encrypted_cert_no` varchar(255) NOT NULL COMMENT '加密的实名认证号',
  `encrypted_mobile_phone` varchar(255) NOT NULL COMMENT '加密后的手机号',
  `idcard_type` tinyint(4) unsigned NOT NULL DEFAULT '1' COMMENT '实名认证方式 1.身份证',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `phone_idx` varchar(255) NOT NULL COMMENT '手机号md5，便于检索',
  `cert_no_idx` varchar(255) NOT NULL COMMENT '身份证号md5,便于检索',
  `act_id` int(11) NOT NULL DEFAULT '0' COMMENT '活动id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_qualify_seq` (`qualify_seq`),
  UNIQUE KEY `idx_unique_mid_qualify_type_act_id` (`mid`,`qualify_type`,`act_id`),
  KEY `idx_order_id6` (`order_id`),
  KEY `idx_mid4` (`mid`),
  KEY `idx_created_time1` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='以旧换新资格码表';

CREATE TABLE `trade_in_qualify_change_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `qualify_id` bigint(20) unsigned NOT NULL COMMENT '资格码id',
  `qualify_seq` varchar(255) NOT NULL COMMENT '资格码序列号',
  `action_type` varchar(255) NOT NULL COMMENT '变更动作',
  `content` text COMMENT '变更前的数据快照信息',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `mid` bigint(20) unsigned NOT NULL COMMENT '用户mid',
  PRIMARY KEY (`id`),
  KEY `idx_qualify_seq1` (`qualify_seq`),
  KEY `idx_mid5` (`mid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='以旧换新资格码变更日志表';

CREATE TABLE `trade_in_qualify_change_log_for_partner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `qualify_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '资格码id',
  `qualify_sequence` varchar(255) NOT NULL DEFAULT '' COMMENT '首信资格码序列号',
  `qualify_type` varchar(20) NOT NULL DEFAULT '' COMMENT '品类编码',
  `cert_no_idx` varchar(255) NOT NULL DEFAULT '' COMMENT '身份证号md5,便于检索',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `operation_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '操作类型',
  `operation_information` varchar(255) NOT NULL DEFAULT '' COMMENT '操作详情',
  PRIMARY KEY (`id`),
  KEY `idx_cert_no_idx_qualify_type` (`cert_no_idx`,`qualify_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='以旧换新资格码日志表(给合作商使用)';

CREATE TABLE `trade_in_qualify_for_partner` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `qualify_sequence` varchar(255) NOT NULL DEFAULT '' COMMENT '首信资格码序列号',
  `qualify_type` varchar(20) NOT NULL DEFAULT '' COMMENT '品类编码',
  `bind_status` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '绑定状态(1已绑定 3已解绑)',
  `encrypted_cert_no` varchar(255) NOT NULL DEFAULT '' COMMENT '加密的实名认证号',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `cert_no_idx` varchar(255) NOT NULL DEFAULT '' COMMENT '身份证号md5,便于检索',
  `unique_id` varchar(255) NOT NULL DEFAULT '' COMMENT '幂等id',
  `act_id` int(11) NOT NULL DEFAULT '0' COMMENT '活动id',
  PRIMARY KEY (`id`),
  KEY `idx_cert_no_idx_qualify_type1` (`cert_no_idx`,`qualify_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='以旧换新资格码表(给合作商使用)';

CREATE TABLE `promotion_user_activity_count` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户id',
  `promotion_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '优惠工具id',
  `num` int(10) NOT NULL DEFAULT '0' COMMENT '活动参与次数',
  `extend` varchar(1024) NOT NULL DEFAULT '' COMMENT '扩展字段',
  `create_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id_promotion_id` (`user_id`,`promotion_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户参与活动记录表';

CREATE TABLE `promotion_car_activity_count` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `vid` varchar(255) NOT NULL DEFAULT '' COMMENT 'vid',
  `promotion_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '优惠工具id',
  `num` int(10) NOT NULL DEFAULT '0' COMMENT '活动参与次数',
  `extend` varchar(1024) NOT NULL DEFAULT '' COMMENT '扩展字段',
  `create_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_vid_promotion_id` (`vid`,`promotion_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='整车参与活动记录表';

CREATE TABLE `v3_activity_online` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(256) DEFAULT NULL COMMENT '活动名称',
  `type_id` int(11) NOT NULL DEFAULT '0' COMMENT '活动类型ID',
  `stat` enum('add','approving','approved','reject','cancel') DEFAULT 'add' COMMENT '活动状态(已保存,审批中,审批通过,审批未通过,终止)',
  `priority` int(11) NOT NULL DEFAULT '1' COMMENT '优先级,数字越大,优先级越高,默认值为1',
  `group_name` varchar(256) DEFAULT NULL COMMENT '活动组名',
  `start_date` varchar(16) NOT NULL DEFAULT '' COMMENT '开始日期',
  `end_date` varchar(16) NOT NULL DEFAULT '' COMMENT '结束日期',
  `start_time` varchar(8) NOT NULL DEFAULT '' COMMENT '开始时间',
  `end_time` varchar(8) NOT NULL DEFAULT '' COMMENT '过期时间',
  `is_daily` varchar(20) NOT NULL DEFAULT 'no' COMMENT '是否每天进行, yes-每天指定时间段生效 no-全程时间段内有效',
  `limited` varchar(20) DEFAULT 'limitless' COMMENT '参加活动限制，limitless-不限制 onece-整个活动一次 everyday-每天一次',
  `check_package` varchar(20) DEFAULT 'no' COMMENT '是否拆分套装 no 不拆分，yes 拆分',
  `offline` varchar(20) NOT NULL DEFAULT 'online' COMMENT '线上，线下限制 online 仅线上使用 offline 仅线下使用 both 线上线下均可使用',
  `goods_include` mediumtext NOT NULL COMMENT '包含商品',
  `goods_inexclude` mediumtext NOT NULL COMMENT '包含排除商品',
  `goods_exclude` mediumtext NOT NULL COMMENT '整单排除商品',
  `users` text COMMENT '用户资格json,为空表示所有用户',
  `client` text NOT NULL COMMENT '参与活动的应用ID',
  `quota` varchar(2048) DEFAULT '' COMMENT '第一优先级使用规则 json字符串',
  `policy` text COMMENT '使用规则json字符串',
  `cond` mediumtext NOT NULL COMMENT '条件',
  `admin_id` int(11) DEFAULT '0' COMMENT '管理员编号',
  `add_time` int(11) DEFAULT '0' COMMENT '添加时间',
  `add_user` varchar(50) NOT NULL DEFAULT '' COMMENT '添加人',
  `modify_time` int(11) DEFAULT '0' COMMENT '修改时间',
  `modify_id` int(11) DEFAULT NULL COMMENT '修改人ID',
  `modify_user` varchar(50) NOT NULL DEFAULT '' COMMENT '修改人',
  `is_copy` tinyint(1) DEFAULT '0' COMMENT '是否是复制生成的活动',
  `from_id` bigint(20) DEFAULT NULL,
  `approve_id` int(11) DEFAULT NULL COMMENT '审核人ID',
  `approve_time` int(11) DEFAULT NULL COMMENT '审核时间',
  `area_id` int(8) unsigned NOT NULL DEFAULT '0' COMMENT '区域',
  `subchannels` varchar(1024) NOT NULL DEFAULT '' COMMENT '流量渠道',
  `ext_prop` text NOT NULL COMMENT '附加属性',
  `select_orgIds` mediumtext COMMENT '机构',
  `online_status` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '上/下线状态(0:下线;1:上线)',
  `workflow` text COMMENT '审批流',
  `sale_source` varchar(255) NOT NULL DEFAULT '' COMMENT '订单来源',
  `bpm_id` varchar(255) NOT NULL DEFAULT '' COMMENT 'bpm系统审核id',
  `channel` varchar(50) NOT NULL DEFAULT '' COMMENT '渠道(1.小米商城 2.直营店 3.专卖店),多个由逗号分隔',
  `version` tinyint(2) NOT NULL DEFAULT '0' COMMENT '活动版本',
  `org_scope` varchar(256) NOT NULL DEFAULT '' COMMENT '机构范围，1-所有门店，2-所有直营店，3-所有专卖店，4-指定门店，5-指定区域',
  `policy_new` mediumtext,
  `ext` text COMMENT '扩展字段',
  `activity_scene` varchar(32) NOT NULL DEFAULT '' COMMENT '活动场景',
  `sequence_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '序列号',
  PRIMARY KEY (`id`),
  KEY `v3_activity_online_ix_add_time` (`add_time`),
  KEY `v3_activity_online_ix_area_id` (`area_id`),
  KEY `v3_activity_online_idx_end_date` (`end_date`)
) ENGINE=InnoDB AUTO_INCREMENT=3142217 DEFAULT CHARSET=utf8 COMMENT='线上活动类型'





