CREATE TABLE `t_url` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `appid` varchar(128) NOT NULL DEFAULT '',
  `url` varchar(1024) NOT NULL DEFAULT '',
  `sign` char(32) NOT NULL DEFAULT '',
  `domain` varchar(128) NOT NULL DEFAULT '',
  `code` char(10) NOT NULL DEFAULT '',
  `add_time` bigint(20) NOT NULL,
  `status` tinyint(2) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_sign` (`sign`),
  KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `xm_admin_log` (
  `admin_log_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `owner_id` bigint(20) NOT NULL,
  `user_name` char(20) NOT NULL,
  `action` char(32) NOT NULL,
  `action_id` char(32) NOT NULL,
  `action_desc` varchar(255) DEFAULT NULL,
  `notes` text,
  `user_ip` char(15) NOT NULL,
  `add_time` int(11) NOT NULL,
  PRIMARY KEY (`admin_log_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

CREATE TABLE `xm_giftcard_log` (
  `id` bigint(16) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `card_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '礼物卡ID',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户编号',
  `action` varchar(64) NOT NULL DEFAULT '' COMMENT '日志类型',
  `old_stat` varchar(32) NOT NULL DEFAULT '' COMMENT '原始状态',
  `new_stat` varchar(32) NOT NULL DEFAULT '' COMMENT '最新状态',
  `admin_id` int(11) NOT NULL DEFAULT '0' COMMENT '管理id',
  `admin_name` varchar(64) NOT NULL DEFAULT '' COMMENT '管理名',
  `action_desc` varchar(256) NOT NULL DEFAULT '' COMMENT '描述',
  `add_time` int(11) NOT NULL DEFAULT '0' COMMENT '添加时间',
  `offline` tinyint(1) NOT NULL DEFAULT '0' COMMENT '线下使用',
  PRIMARY KEY (`id`),
  KEY `ix_add_time` (`add_time`),
  KEY `ix_user_id` (`user_id`),
  KEY `ix_action` (`action`,`add_time`),
  KEY `ix_card_id` (`card_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COMMENT='礼物卡日志';

CREATE TABLE `xm_queue_vcode` (
  `id` bigint(16) NOT NULL AUTO_INCREMENT,
  `encrypt_code` varchar(60) DEFAULT NULL COMMENT '加密的券码',
  `status` varchar(10) DEFAULT NULL COMMENT 'F码状态',
  `level` tinyint(3) NOT NULL COMMENT 'F码级别',
  `memo` text COMMENT 'F码类型描述',
  `order_id` bigint(20) NOT NULL COMMENT '验证码所使用的订单ID',
  `dateline` int(11) NOT NULL COMMENT '更改时间',
  `type` int(11) NOT NULL,
  `batch_id` int(10) NOT NULL COMMENT '批次号',
  `used_count` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'usedCount',
  `start_time` int(10) NOT NULL COMMENT '开始时间',
  `end_time` int(10) NOT NULL COMMENT '失效时间',
  `add_time` int(10) NOT NULL COMMENT '生产时间',
  `giftcard_status` varchar(10) NOT NULL DEFAULT 'unbind' COMMENT '礼品卡激活状态',
  `bind_order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '礼品卡配货订单号',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'F码绑定米id',
  `department_id` int(11) NOT NULL DEFAULT '0' COMMENT '部门id',
  `cost_code` varchar(16) NOT NULL DEFAULT '' COMMENT '成本中心编码',
  `delivery_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '预计推单时间',
  `delivery_text` varchar(128) NOT NULL DEFAULT '' COMMENT '预计发货描述',
  PRIMARY KEY (`id`),
  UNIQUE KEY `encrypt_code_UNIQUE` (`encrypt_code`),
  KEY `order_id` (`order_id`),
  KEY `ix_status` (`status`),
  KEY `ix_encrypt_code` (`encrypt_code`),
  KEY `ix_d_l` (`dateline`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `level` (`level`),
  KEY `bind_order_id` (`bind_order_id`),
  KEY `idx_add_time` (`add_time`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

CREATE TABLE `xm_queue_vcode_batch` (
  `batch_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `apply_userid` int(10) unsigned NOT NULL,
  `examine_userid` int(10) NOT NULL COMMENT '审批人',
  `apply_time` int(10) NOT NULL COMMENT '申请时间',
  `examine_time` int(10) NOT NULL COMMENT '审核时间',
  `num` int(10) NOT NULL COMMENT '申请数量',
  `batch_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '审批状态（0：待审核，1：审核通过，2：审核不通过，3：已生产）',
  `apply_memo` text NOT NULL COMMENT '审批理由',
  `batch_channel` tinyint(1) NOT NULL COMMENT '不同的分发方式',
  `batch_memo` text NOT NULL COMMENT '批次信息',
  `start_time` int(10) NOT NULL COMMENT '开始时间',
  `end_time` int(10) NOT NULL COMMENT '过期时间',
  `type` int(10) NOT NULL COMMENT '优惠券/F码类型',
  `apply_user_name` varchar(50) NOT NULL COMMENT '申请人姓名',
  `apply_user_mail` varchar(50) NOT NULL COMMENT '申请人邮箱',
  `apply_user_mobile` varchar(50) NOT NULL COMMENT '申请人手机号',
  `examine_user_name` varchar(50) NOT NULL COMMENT '审批人',
  `batch_time` int(10) NOT NULL COMMENT '批次添加时间',
  `download_url` varchar(255) NOT NULL COMMENT '下载地址',
  `extract_password` varchar(30) NOT NULL COMMENT '解压密码',
  `count_used` int(10) NOT NULL DEFAULT '0' COMMENT '已支付数',
  `count_lock` int(10) NOT NULL DEFAULT '0' COMMENT '下单未支付数',
  `count_updatetime` int(10) NOT NULL DEFAULT '0' COMMENT '使用数最后更新时间',
  `department_id` int(11) NOT NULL DEFAULT '0' COMMENT '部门id',
  `cost_code` varchar(16) NOT NULL DEFAULT '' COMMENT '成本中心编码',
  `delivery_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '预计推单时间',
  `delivery_text` varchar(128) NOT NULL DEFAULT '' COMMENT '预计发货描述',
  PRIMARY KEY (`batch_id`),
  KEY `idx_batch_status` (`batch_status`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COMMENT='F码批次表';

CREATE TABLE `xm_queue_vcode_history` (
  `id` bigint(16) NOT NULL,
  `encrypt_code` varchar(60) DEFAULT NULL COMMENT '加密的券码',
  `status` varchar(10) DEFAULT NULL COMMENT 'F码状态',
  `level` tinyint(3) NOT NULL COMMENT 'F码级别',
  `memo` text COMMENT 'F码类型描述',
  `order_id` bigint(20) NOT NULL COMMENT '验证码所使用的订单ID',
  `dateline` int(11) NOT NULL COMMENT '更改时间',
  `type` int(11) NOT NULL,
  `batch_id` int(10) NOT NULL COMMENT '批次号',
  `used_count` tinyint(4) NOT NULL DEFAULT '0' COMMENT '一个F码使用次数',
  `start_time` int(10) NOT NULL COMMENT '开始时间',
  `end_time` int(10) NOT NULL COMMENT '失效时间',
  `add_time` int(10) NOT NULL COMMENT '生产时间',
  `giftcard_status` varchar(10) NOT NULL DEFAULT 'unbind' COMMENT '礼品卡激活状态',
  `bind_order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '礼品卡配货订单号',
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'F码绑定米id',
  `department_id` int(11) NOT NULL DEFAULT '0' COMMENT '部门id',
  `cost_code` varchar(16) NOT NULL DEFAULT '' COMMENT '成本中心编码',
  `delivery_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '预计推单时间',
  `delivery_text` varchar(128) NOT NULL DEFAULT '' COMMENT '预计发货描述',
  PRIMARY KEY (`id`),
  UNIQUE KEY `encrypt_code_UNIQUE1` (`encrypt_code`),
  KEY `order_id1` (`order_id`),
  KEY `ix_status1` (`status`),
  KEY `ix_encrypt_code1` (`encrypt_code`),
  KEY `ix_d_l1` (`dateline`),
  KEY `idx_batch_id1` (`batch_id`),
  KEY `level1` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='F码冷表';

CREATE TABLE `xm_queue_vcode_type` (
  `tid` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(50) NOT NULL COMMENT '邀请码名称',
  `description` text NOT NULL COMMENT '描述',
  `targetGoods` text NOT NULL COMMENT '针对商品，格式csv',
  `slash` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '削减的的金额',
  `cond` int(11) DEFAULT '0' COMMENT '优惠券使用条件',
  `discount_type` mediumint(8) NOT NULL COMMENT '不同的打折类型',
  `discount_rule` text NOT NULL COMMENT '不同的打折规则',
  `excludeGoods` text NOT NULL COMMENT '排除活动商品的字段',
  `excludeCates` text NOT NULL COMMENT '排除商品分类',
  `area_id` int(10) NOT NULL DEFAULT '2' COMMENT 'F码区域id',
  `is_fcode` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为F码（1：是，0：否）',
  `shipment_expense_avoid` tinyint(1) NOT NULL COMMENT '1ä¸ºå…é™¤è¿è´¹ 0ä¸ºä¸å…é™¤è¿è´¹',
  `vcode_pre` varchar(4) NOT NULL COMMENT 'F码前缀',
  `interface_apply` enum('0','1','2') NOT NULL DEFAULT '2' COMMENT '0设置为命令行生成，1设置为接口申请,2设置为均可',
  `sailout_limit` enum('0','1') NOT NULL DEFAULT '0' COMMENT '1表示缺货无法购买，0表示缺货可以购买',
  `is_giftcard` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否是礼品卡',
  `giftcard_goodsId` int(11) NOT NULL DEFAULT '0' COMMENT '礼品卡自身商品编号',
  PRIMARY KEY (`tid`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COMMENT='优惠券类型';

--CREATE ALIAS xm_encrypt for 'com.xiaomi.nr.promotion.v2.h2function.MockXmEncryptFunction.xm_encrypt';

--DELIMITER $$
--CREATE FUNCTION xm_encrypt (a varchar(60)) RETURNS varchar(60) BEGIN RETURN a; END; $$
--DELIMITER ;