CREATE TABLE `v3_activity_online` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(256) DEFAULT NULL COMMENT '活动名称',
  `type_id` int(11) NOT NULL DEFAULT '0' COMMENT '活动类型ID',
  `stat` enum('add','approving','approved','reject','cancel') DEFAULT 'add' COMMENT '活动状态(已保存,审批中,审批通过,审批未通过,终止)',
  `priority` int(11) NOT NULL DEFAULT '1' COMMENT '优先级,数字越大,优先级越高,默认值为1',
  `group_name` varchar(256) DEFAULT NULL COMMENT '活动组名',
  `start_date` varchar(20) NOT NULL DEFAULT '' COMMENT '开始日期',
  `end_date` varchar(20) NOT NULL DEFAULT '' COMMENT '结束日期',
  `start_time` varchar(11) NOT NULL DEFAULT '' COMMENT '开始时间',
  `end_time` varchar(11) NOT NULL DEFAULT '' COMMENT '过期时间',
  `is_daily` varchar(20) NOT NULL DEFAULT 'no' COMMENT '是否每天进行, yes-每天指定时间段生效 no-全程时间段内有效',
  `limited` varchar(20) DEFAULT 'limitless' COMMENT '参加活动限制，limitless-不限制 onece-整个活动一次 everyday-每天一次',
  `check_package` varchar(20) DEFAULT 'no' COMMENT '是否拆分套装 no 不拆分，yes 拆分',
  `offline` varchar(20) NOT NULL DEFAULT 'online' COMMENT '线上，线下限制 online 仅线上使用 offline 仅线下使用 both 线上线下均可使用',
  `goods_include` mediumtext COMMENT '包含商品',
  `goods_inexclude` mediumtext COMMENT '包含排除商品',
  `goods_exclude` mediumtext COMMENT '整单排除商品',
  `users` text COMMENT '用户资格json,为空表示所有用户',
  `client` text NOT NULL COMMENT '参与活动的应用ID',
  `quota` varchar(2048) DEFAULT '' COMMENT '第一优先级使用规则 json字符串',
  `policy` text COMMENT '使用规则json字符串',
  `cond` mediumtext COMMENT '条件',
  `admin_id` int(11) DEFAULT '0' COMMENT '管理员编号',
  `add_time` int(11) DEFAULT '0' COMMENT '添加时间',
  `add_user` varchar(50) NOT NULL DEFAULT '' COMMENT '添加人',
  `modify_time` int(11) DEFAULT '0' COMMENT '修改时间',
  `modify_id` int(11) DEFAULT NULL COMMENT '修改人ID',
  `modify_user` varchar(50) NOT NULL DEFAULT '' COMMENT '修改人',
  `is_copy` tinyint(1) DEFAULT '0' COMMENT '是否是复制生成的活动',
  `from_id` bigint(20) DEFAULT NULL COMMENT '幂等参数',
  `approve_id` int(11) DEFAULT NULL COMMENT '审核人ID',
  `approve_time` int(11) DEFAULT NULL COMMENT '审核时间',
  `area_id` int(8) unsigned NOT NULL DEFAULT '0' COMMENT '区域',
  `subchannels` varchar(1024) NOT NULL DEFAULT '' COMMENT '流量渠道',
  `ext_prop` varchar(1024) NOT NULL DEFAULT '' COMMENT '附加属性',
  `org_scope` varchar(256) NOT NULL DEFAULT '0' COMMENT '机构范围，1-所有门店，2-所有直营店，3-所有专卖店，4-指定门店，5-指定区域',
  `select_orgIds` mediumtext COMMENT '机构',
  `online_status` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '上/下线状态(0:下线;1:上线)',
  `workflow` text COMMENT '审批流',
  `sale_source` varchar(255) NOT NULL DEFAULT '' COMMENT '订单来源',
  `bpm_id` varchar(255) NOT NULL DEFAULT '' COMMENT 'bpm系统审核id',
  `channel` varchar(50) NOT NULL DEFAULT '' COMMENT '渠道(1.小米商城 2.直营店 3.专卖店),多个由逗号分隔',
  `version` tinyint(2) NOT NULL DEFAULT '0' COMMENT '活动版本',
  `policy_new` mediumtext NOT NULL COMMENT '使用规则json字符串',
  `ext` text COMMENT '扩展字段',
  `activity_scene` varchar(32) NOT NULL DEFAULT '' COMMENT '活动场景',
  `sequence_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '序列号',
  PRIMARY KEY (`id`),
  KEY `ix_add_time` (`add_time`),
  KEY `ix_area_id` (`area_id`),
  KEY `idx_end_date` (`end_date`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COMMENT='v3活动详情';

CREATE TABLE `nr_activity` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(256) NOT NULL DEFAULT '' COMMENT '活动名称',
  `type_id` int(10) NOT NULL DEFAULT '0' COMMENT '活动类型ID',
  `status` tinyint(1) NOT NULL DEFAULT '2' COMMENT '状态,0：下线 ，1:上线',
  `start_time` int(11) NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(11) NOT NULL DEFAULT '0' COMMENT '过期时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(50) NOT NULL DEFAULT '' COMMENT '添加人',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `modify_user` varchar(50) NOT NULL DEFAULT '' COMMENT '修改人',
  `channel` varchar(50) NOT NULL DEFAULT '' COMMENT '渠道,多个由逗号分隔',
  `ext_info` text NOT NULL COMMENT '扩展配置信息',
  PRIMARY KEY (`id`),
  KEY `idx_st` (`start_time`),
  KEY `idx_et` (`end_time`),
  KEY `idx_type` (`type_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='活动表';

CREATE TABLE `nr_act_scope` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `activity_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '活动配置id',
  `scope_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '范围类型 1-门店',
  `relation` tinyint(1) NOT NULL DEFAULT '1' COMMENT '关系 1-与 2-或 3-非',
  `scope_value` mediumtext NOT NULL COMMENT '范围内容，多个用逗号分割',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识（默认0）0-未删除 1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_ac_id` (`activity_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='活动范围表';

CREATE TABLE `nr_act_goods` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `activity_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '活动id',
  `product_id_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '商品id类别，sku/pid/cid',
  `product_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '商品id',
  `gid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'gid',
  `pid` bigint(20) NOT NULL DEFAULT '0' COMMENT 'pid',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '上下线状态：1：上线, 2:下线',
  `type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '主从品：1：主品，2：从品',
  `group` tinyint(4) NOT NULL DEFAULT '0' COMMENT '赠品分组',
  `rule` varchar(300) NOT NULL DEFAULT '' COMMENT '活动规则，json',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_gid` (`gid`),
  KEY `idx_pid` (`pid`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_activity_id` (`activity_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='预售商品表';
