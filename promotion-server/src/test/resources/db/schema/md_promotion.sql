CREATE TABLE `t_installment_gift_goods` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `product_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '商品PID',
  `product_id_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '商品Id类型 1:sku 3:套装',
  `begin_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '结束时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人',
  `updater` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_be_t` (`begin_time`,`end_time`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COMMENT='分期免息活动赠品活动二选一商品表';

CREATE TABLE `t_prom_activity` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '活动名称',
  `begin_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '活动开始时间',
  `end_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '活动结束时间',
  `source_app` tinyint(4) NOT NULL DEFAULT '0' COMMENT '活动来源',
  `uniq_id` char(50) NOT NULL DEFAULT '' COMMENT '业务去重id',
  `area_id` varchar(100) NOT NULL DEFAULT '' COMMENT '地区id',
  `channels` varchar(100) NOT NULL DEFAULT '' COMMENT '渠道，多个以,分割',
  `ac_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '活动状态，1-已上线，0-已下线',
  `promotion_type` int(10) NOT NULL DEFAULT '0' COMMENT '促销类别',
  `rule` varchar(1000) NOT NULL DEFAULT '' COMMENT '促销规则，json',
  `expand` varchar(1000) NOT NULL DEFAULT '' COMMENT '扩展信息，如附件，活动场景等',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` char(50) NOT NULL DEFAULT '' COMMENT '创建人',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删除，1-已删除',
  `trade_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '交易类型 (0.常态 1.改配)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uniq_id` (`uniq_id`),
  KEY `idx_be_t1` (`begin_time`,`end_time`),
  KEY `idx_update_time` (`update_time`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='活动配置表';

CREATE TABLE `t_prom_activity_alter_event` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `activity_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '活动配置id',
  `operation_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '活动操作类型,1-创建，2-更新，3-上线，4-下线',
  `activity_info` text COMMENT '活动配置变更信息，创建和更新时存在',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `operator` varchar(50) NOT NULL DEFAULT '' COMMENT '操作人',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_ac_id` (`activity_id`),
  KEY `idx_ct` (`create_time`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COMMENT='活动变更事件表';

CREATE TABLE `t_prom_activity_scope` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `activity_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '活动配置id',
  `scope_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '范围类型，默认1，1-门店，2-人群，3-黑名单商品,4-区域，5-订单来源',
  `relation` tinyint(1) NOT NULL DEFAULT '1' COMMENT '关系，默认1，1-与，2-或，3-非',
  `scope_value` mediumtext NOT NULL COMMENT '范围内容，多个用逗号分割',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_ac_id1` (`activity_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='活动范围表';

CREATE TABLE `t_prom_crm_price_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `crm_price_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'crm数据id',
  `activity_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '关联的促销活动id',
  `price_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '价格类型',
  `info` text NOT NULL COMMENT 'crm原始数据，json',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_crm_price_id` (`crm_price_id`,`price_type`) ,
  KEY `idx_activity_id` (`activity_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='crm价格信息表';

CREATE TABLE `t_prom_policy_act` (
  `id` bigint(21) NOT NULL AUTO_INCREMENT COMMENT '主键ID，消息体ID',
  `policy_unit_id` bigint(21) NOT NULL DEFAULT '0' COMMENT '政策单元ID',
  `policy_batch_id` bigint(21) NOT NULL DEFAULT '0' COMMENT '政策批次ID',
  `mapping_act_id` bigint(21) NOT NULL DEFAULT '0' COMMENT '对应的活动id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_policy_unit_id` (`policy_unit_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='政策活动表';

CREATE TABLE `t_prom_policy_batch` (
  `id` bigint(21) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID，消息体ID',
  `policy_id` bigint(21) NOT NULL DEFAULT '0' COMMENT '政策ID',
  `version` bigint(20) NOT NULL DEFAULT '0' COMMENT '版本',
  `policy_name` varchar(64) NOT NULL DEFAULT '' COMMENT '政策名称',
  `spu_id` varchar(30) NOT NULL DEFAULT '' COMMENT 'spu商品id',
  `channels` varchar(50) NOT NULL DEFAULT '' COMMENT '渠道ID',
  `spu_name` varchar(200) NOT NULL DEFAULT '' COMMENT 'spu商品名称',
  `begin_time` bigint(13) NOT NULL DEFAULT '0' COMMENT '开始时间(秒）',
  `end_time` bigint(13) NOT NULL DEFAULT '0' COMMENT '结束时间(秒）',
  `status` int(8) NOT NULL DEFAULT '0' COMMENT '政策状态',
  `publish_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '下发时间',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(48) NOT NULL DEFAULT '0' COMMENT '操作人',
  `update_user` varchar(48) NOT NULL DEFAULT '0' COMMENT '修改用户名',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_idx_policy_spu_version` (`policy_id`,`spu_id`,`version`),
  KEY `idx_policy_version` (`policy_id`,`version`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_begin_end_time` (`begin_time`,`end_time`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='政策spu表';

CREATE TABLE `t_prom_policy_log` (
  `id` bigint(21) NOT NULL AUTO_INCREMENT COMMENT '主键ID，消息体ID',
  `policy_id` bigint(21) NOT NULL DEFAULT '0' COMMENT '政策ID',
  `version` bigint(20) NOT NULL DEFAULT '0' COMMENT '版本',
  `spu_id` varchar(30) NOT NULL DEFAULT '' COMMENT 'spu商品id',
  `policy_batch_id` bigint(21) NOT NULL DEFAULT '0' COMMENT '促销政策ID',
  `opt_type` int(4) NOT NULL DEFAULT '0' COMMENT '操作内容 0下发1确认',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user` varchar(48) NOT NULL DEFAULT '0' COMMENT '政策中心操作人',
  PRIMARY KEY (`id`),
  KEY `idx_policy_spu` (`policy_id`,`spu_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='政策日志表';

CREATE TABLE `t_prom_policy_unit` (
  `id` bigint(21) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID，消息体ID',
  `policy_batch_id` bigint(21) NOT NULL DEFAULT '0' COMMENT '促销批次ID',
  `channels` varchar(50) NOT NULL DEFAULT '' COMMENT '渠道ID列表1: 商城 2；直营 3:专卖 4: 授权',
  `skuList` varchar(10240) NOT NULL DEFAULT '' COMMENT 'sku列表',
  `policy_content` mediumtext COMMENT '政策详情',
  `begin_time` bigint(13) NOT NULL DEFAULT '0' COMMENT '开始时间(秒）',
  `end_time` bigint(13) NOT NULL DEFAULT '0' COMMENT '结束时间(秒）',
  `status` int(8) NOT NULL DEFAULT '0' COMMENT '政策状态',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_begin_end_time1` (`begin_time`,`end_time`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='政策单元表';

CREATE TABLE `t_prom_product_policy` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `activity_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '活动配置id',
  `product_id_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '商品类别，sku/pid/cid',
  `product_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '商品id',
  `ssu_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0-单品ssu, 1-套装ssu，2-主附品ssu',
  `product_level` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1-主品，2-从品，3-券',
  `product_group` tinyint(4) NOT NULL DEFAULT '0' COMMENT '分组',
  `promotion_price` bigint(10) NOT NULL DEFAULT '0' COMMENT '活动后价格',
  `stock` int(11) NOT NULL DEFAULT '0' COMMENT '活动商品库存,0-不限制',
  `user_limit_num` int(11) NOT NULL DEFAULT '0' COMMENT '用户限购数量,0-不限制',
  `rule` varchar(300) NOT NULL DEFAULT '' COMMENT '促销规则，json',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `validity` tinyint(1) NOT NULL DEFAULT '1' COMMENT '有效性，1-有效，0-无效',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识，0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_ac_id` (`activity_id`,`product_id_type`,`product_id`,`product_level`,`product_group`) ,
  KEY `idx_product_id1` (`product_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='商品策略表';
