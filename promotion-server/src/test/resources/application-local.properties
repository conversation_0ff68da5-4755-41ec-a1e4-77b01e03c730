##server
#app.name=promotion
#server.type=dev
#server.port=8080
#server.debug=true
#server.connection-timeout=1000
#dubbo.group=dev-temp
#dubbo.protocol.id=dubbo
#dubbo.protocol.name=dubbo
#dubbo.protocol.port=-1
#dubbo.registry.address=nacos://tj1-b2c-systech-mione-test03.kscn:80
#nacos.config.addrs=tj1-b2c-systech-mione-test03.kscn:80
#youpin.log.group=dev
#log.path=/tmp
#talos.topic=youpin_common_test
#talos.sendpoint=http://staging-cnbj2-talos.api.xiaomi.net
#talos.access.key=
#talos.access.secret=
#app.nacos=*************:80
#
## redis config
## redis config for redis_misc
#redis.config.promotion.host=wcc.cache01.test.b2c.srv
#redis.config.promotion.port=22122
#redis.config.promotion.password=shopapi_misc
#redis.config.promotion.timeout=3000
#redis.config.promotion.database=0
#redis.config.promotion.lettuce-pool.max-idle=8
#redis.config.promotion.lettuce.pool.min-idle=1
#redis.config.promotion.lettuce-pool.max-total=200
#redis.config.promotion.lettuce-pool.timeout=3000
#redis.config.promotion.lettuce-pool.shutdown-timeOut=3000
#redis.config.promotion.lettuce.pool.max-wait-millis=3000
#
## redis config for redis_act
#redis.config.act.host=wcc.cache01.test.b2c.srv
#redis.config.act.port=22122
#redis.config.act.password=cn_mishop_pulse_redis_common_storage_DPBRaZryAOwr
#redis.config.act.timeout=3000
#redis.config.act.database=0
#redis.config.act.lettuce-pool.max-idle=8
#redis.config.act.lettuce.pool.min-idle=1
#redis.config.act.lettuce-pool.max-total=200
#redis.config.act.lettuce-pool.timeout=3000
#redis.config.act.lettuce-pool.shutdown-timeOut=3000
#redis.config.act.lettuce.pool.max-wait-millis=3000
#
## redis config for coupon
#redis.config.coupon.host=wcc.cache01.test.b2c.srv
#redis.config.coupon.port=22122
#redis.config.coupon.password=cn_mishop_pulse_redis_isLm7NcoEPdwjra9
#redis.config.coupon.timeout=3000
#redis.config.coupon.database=0
#redis.config.coupon.lettuce-pool.max-idle=8
#redis.config.coupon.lettuce.pool.min-idle=1
#redis.config.coupon.lettuce-pool.max-total=200
#redis.config.coupon.lettuce-pool.timeout=3000
#redis.config.coupon.lettuce-pool.shutdown-timeOut=3000
#redis.config.coupon.lettuce.pool.max-wait-millis=3000
#
##redis config for redpacket
#redis.config.redpacket.host=wcc.cache01.test.b2c.srv
#redis.config.redpacket.port=22122
#redis.config.redpacket.password=cn_mishop_pulse_redis_isLm7NcoEPdwjra9
#redis.config.redpacket.timeout=3000
#redis.config.redpacket.database=0
#redis.config.redpacket.lettuce-pool.max-idle=8
#redis.config.redpacket.lettuce.pool.min-idle=1
#redis.config.redpacket.lettuce-pool.max-total=200
#redis.config.redpacket.lettuce-pool.timeout=3000
#redis.config.redpacket.lettuce-pool.shutdown-timeOut=3000
#redis.config.redpacket.lettuce.pool.max-wait-millis=3000
#
## mysql promotion config
#spring.promotionconfig-datasource.name=xm_pulse_natl
#spring.promotionconfig-datasource.username=pulse_natl_w
#spring.promotionconfig-datasource.url=*********************************************************************************************
#spring.promotionconfig-datasource.password=NsYIltp7GI1kK_YLrBGhkuAOpT
#spring.promotionconfig-datasource.connectionProperties=
#spring.promotionconfig-datasource.sql-script-encoding=UTF-8
#spring.promotionconfig-datasource.driver-class-name=com.mysql.jdbc.Driver
#spring.promotionconfig-datasource.type=com.alibaba.druid.pool.DruidDataSource
#spring.promotionconfig-datasource.initial-size=5
#spring.promotionconfig-datasource.max-active=10
#spring.promotionconfig-datasource.min-idle=3
#spring.promotionconfig-datasource.max-wait=600000
#spring.promotionconfig-datasource.remove-abandoned=true
#spring.promotionconfig-datasource.remove-abandoned-timeout=180
#spring.promotionconfig-datasource.time-between-eviction-runs-millis=600000
#spring.promotionconfig-datasource.min-evictable-idle-time-millis=300000
#spring.promotionconfig-datasource.validation-query=SELECT 1 FROM DUAL
#spring.promotionconfig-datasource.test-while-idle=true
#spring.promotionconfig-datasource.test-on-borrow=false
#spring.promotionconfig-datasource.test-on-return=false
#spring.promotionconfig-datasource.pool-prepared-statements=true
#spring.promotionconfig-datasource.max-pool-prepared-statement-per-connection-size=50
#spring.promotionconfig-datasource.filters=stat,wall
#
## mysql promotion user config
#spring.promotionuserconfig-datasource.name=pulse_user
#spring.promotionuserconfig-datasource.username=pulse_user
#spring.promotionuserconfig-datasource.url=jdbc:mysql://*************:4318/xm_pulse?characterEncoding=utf8&useSSL=true
#spring.promotionuserconfig-datasource.password=ifvSZGHPXMtBwi31_0jpSSt6gPX4dI
#spring.promotionuserconfig-datasource.connectionProperties=
#spring.promotionuserconfig-datasource.sql-script-encoding=UTF-8
#spring.promotionuserconfig-datasource.driver-class-name=com.mysql.jdbc.Driver
#spring.promotionuserconfig-datasource.type=com.alibaba.druid.pool.DruidDataSource
#spring.promotionuserconfig-datasource.initial-size=5
#spring.promotionuserconfig-datasource.max-active=10
#spring.promotionuserconfig-datasource.min-idle=3
#spring.promotionuserconfig-datasource.max-wait=600000
#spring.promotionuserconfig-datasource.remove-abandoned=true
#spring.promotionuserconfig-datasource.remove-abandoned-timeout=180
#spring.promotionuserconfig-datasource.time-between-eviction-runs-millis=600000
#spring.promotionuserconfig-datasource.min-evictable-idle-time-millis=300000
#spring.promotionuserconfig-datasource.validation-query=SELECT 1 FROM DUAL
#spring.promotionuserconfig-datasource.test-while-idle=true
#spring.promotionuserconfig-datasource.test-on-borrow=false
#spring.promotionuserconfig-datasource.test-on-return=false
#spring.promotionuserconfig-datasource.pool-prepared-statements=true
#spring.promotionuserconfig-datasource.max-pool-prepared-statement-per-connection-size=50
#spring.promotionuserconfig-datasource.filters=stat,wall
#
## mysql fcode config
#spring.fcodeconfig-datasource.name=fcode_user_w
#spring.fcodeconfig-datasource.username=fcode_user_w
#spring.fcodeconfig-datasource.url=*******************************************************************************************
#spring.fcodeconfig-datasource.password=5d8f5uiCrOY_S0SFPCt9c8
#spring.fcodeconfig-datasource.connectionProperties=
#spring.fcodeconfig-datasource.sql-script-encoding=UTF-8
#spring.fcodeconfig-datasource.driver-class-name=com.mysql.jdbc.Driver
#spring.fcodeconfig-datasource.type=com.alibaba.druid.pool.DruidDataSource
#spring.fcodeconfig-datasource.initial-size=5
#spring.fcodeconfig-datasource.max-active=10
#spring.fcodeconfig-datasource.min-idle=3
#spring.fcodeconfig-datasource.max-wait=600000
#spring.fcodeconfig-datasource.remove-abandoned=true
#spring.fcodeconfig-datasource.remove-abandoned-timeout=180
#spring.fcodeconfig-datasource.time-between-eviction-runs-millis=600000
#spring.fcodeconfig-datasource.min-evictable-idle-time-millis=300000
#spring.fcodeconfig-datasource.validation-query=SELECT 1 FROM DUAL
#spring.fcodeconfig-datasource.test-while-idle=true
#spring.fcodeconfig-datasource.test-on-borrow=false
#spring.fcodeconfig-datasource.test-on-return=false
#spring.fcodeconfig-datasource.pool-prepared-statements=true
#spring.fcodeconfig-datasource.max-pool-prepared-statement-per-connection-size=50
#spring.fcodeconfig-datasource.filters=stat,wall
#
#configHost=http://etcd.test.mi.com
#appGroup=mishop
#serviceName=promotion
#
## phoenix
#phoenix.dubbo.group=staging
#
#logging.level.com=debug
#
## rocketMq
#rocketmq.name-server=staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
#rocketmq.producer.group=promotion-producer
#rocketmq.producer.access-key=AKT7P4VWPIPO4PGMPI
#mq.topic.actstock=nr_promotion_act_stock
#mq.topic.carActivity=nr_car_activity_use_message
#mq.topic.carActivity.tag=vipMember
#mq.topic.carEquity=car_equity_performance
#mq.topic.carEquity.tag=ACTIVITY
#
#
##sentinel
#sentinel.dashboard.host=http://sentinel.test.be.mi.com
#
#
