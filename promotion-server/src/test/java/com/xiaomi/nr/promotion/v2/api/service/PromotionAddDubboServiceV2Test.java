package com.xiaomi.nr.promotion.v2.api.service;

import com.xiaomi.mit.unittest.db.SetupDB;
import com.xiaomi.nr.md.promotion.admin.api.constant.PromotionTypeEnum;
import com.xiaomi.nr.promotion.activity.pool.CarPromotionInstancePool;
import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailAddItem;
import com.xiaomi.nr.promotion.api.service.PromotionAddDubboService;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.OrderPromotionDetailMapper;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.OrderPromotionDetailStatusMapper;
import com.xiaomi.nr.promotion.flows.facade.PromotionAddDubboServiceFacade;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@SetupDB(value = "db/case/PromotionAddDubboServiceTest.sql", dataSourceName = "promotionUserConfigDatasource")
public class PromotionAddDubboServiceV2Test extends BaseTestV2 {

    @Autowired
    private PromotionAddDubboService promotionAddDubboService;
    @Autowired
    private PromotionAddDubboServiceFacade promotionAddDubboServiceFacade;
    @Autowired
    private OrderPromotionDetailMapper orderPromotionDetailMapper;
    @Autowired
    private OrderPromotionDetailStatusMapper orderPromotionDetailStatusMapper;
    @Autowired
    private CarPromotionInstancePool promotionInstancePool;
    @Autowired
    private CarPromotionInstancePool carPromotionInstancePool;

    /**
     * 改配场景追加优惠全量取消接口
     */
    @Test
    public void test_cancelPromotion_success(){

        CancelPromotionAddRequest request = new CancelPromotionAddRequest();
        request.setOrderId(1L);
        Result<CancelPromotionAddResponse> result = promotionAddDubboService.cancelPromotion(request);
        Assertions.assertTrue(result.getData().getResult());
    }


    @Test
    public void test_rollbackPromotion_fail(){

        Result<RollbackPromotionAddResponse> result = promotionAddDubboService.rollbackPromotion(null);
        Assertions.assertEquals("订单ID非法", result.getMessage());

        Result<CancelPromotionAddResponse> result9 = promotionAddDubboService.cancelPromotion(null);
        Assertions.assertEquals("订单ID非法", result9.getMessage());

        RollbackPromotionAddRequest request = new RollbackPromotionAddRequest();
        request.setOrderId(1L);
        List<ReduceDetailAddItem> promotionInfoList = new ArrayList<>();
        request.setPromotionInfoList(promotionInfoList);
        Result<RollbackPromotionAddResponse> result3 = promotionAddDubboService.rollbackPromotion(request);
        Assertions.assertEquals("优惠信息为空", result3.getMessage());

        request.setPromotionInfoList(promotionInfoList);
        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setPromotionId(0L);
        item.setPromotionType(PromotionTypeEnum.EXCHANGE_SUBSIDY.code);
        item.setReduce(1200L);
        item.setSsuId(600008166L);
        promotionInfoList.add(item);

        Result<RollbackPromotionAddResponse> result4 = promotionAddDubboService.rollbackPromotion(request);
        Assertions.assertEquals("promotionId非法", result4.getMessage());

        item.setPromotionId(1L);
        item.setPromotionType(0);
        Result<RollbackPromotionAddResponse> result5 = promotionAddDubboService.rollbackPromotion(request);
        Assertions.assertEquals("promotionType非法", result5.getMessage());
    }

    /**
     * 追加优惠回滚接口
     * 整车-置换补贴优惠回滚
     */
    @Test
    public void test_rollbackPromotion_29_success(){

        RollbackPromotionAddRequest request = new RollbackPromotionAddRequest();
        request.setOrderId(5248461002042553L);
        request.setExamineReject(true);
        List<ReduceDetailAddItem> promotionInfoList = new ArrayList<>();
        request.setPromotionInfoList(promotionInfoList);
        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setPromotionId(21542480L);
        item.setPromotionType(PromotionTypeEnum.EXCHANGE_SUBSIDY.code);
        item.setReduce(1200L);
        item.setSsuId(600008166L);
        promotionInfoList.add(item);
        //逆向调用促销驳回
        Result<RollbackPromotionAddResponse> result = promotionAddDubboService.rollbackPromotion(request);
        Assertions.assertTrue(result.getData().getResult());

        request.setOrderId(5248461002042553L);
        request.setExamineReject(false);
        item.setPromotionId(21542480L);
        item.setPromotionType(PromotionTypeEnum.EXCHANGE_SUBSIDY.code);
        item.setReduce(1200L);
        item.setSsuId(600008166L);
        promotionInfoList.add(item);
        //交易调用促销回滚
        Result<RollbackPromotionAddResponse> result2 = promotionAddDubboService.rollbackPromotion(request);
        Assertions.assertTrue(result2.getData().getResult());
    }

    /**
     * 追加优惠回滚接口
     * 整车-订单立减优惠回滚
     */
    @Test
    public void test_rollbackPromotion_31_success(){

        RollbackPromotionAddRequest request = new RollbackPromotionAddRequest();
        request.setOrderId(5256051001042441L);
        request.setExamineReject(true);
        List<ReduceDetailAddItem> promotionInfoList = new ArrayList<>();
        request.setPromotionInfoList(promotionInfoList);
        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setPromotionId(21542480L);
        item.setPromotionType(PromotionTypeEnum.ORDER_REDUCE.code);
        item.setReduce(100L);
        item.setSsuId(600008166L);
        promotionInfoList.add(item);
        //逆向调用促销驳回
        Result<RollbackPromotionAddResponse> result = promotionAddDubboService.rollbackPromotion(request);
        Assertions.assertTrue(result.getData().getResult());

        request.setOrderId(5255991001267577L);
        request.setExamineReject(false);
        item.setPromotionId(21542480L);
        item.setPromotionType(PromotionTypeEnum.ORDER_REDUCE.code);
        item.setReduce(100L);
        item.setSsuId(600008166L);
        //交易调用促销回滚
        Result<RollbackPromotionAddResponse> result2 = promotionAddDubboService.rollbackPromotion(request);
        Assertions.assertTrue(result2.getData().getResult());
    }


    @Test
    public void test_submitPromotion_fail(){

        Result<SubmitPromotionAddResponse> result = promotionAddDubboService.submitPromotion(null);
        Assertions.assertEquals("订单ID非法", result.getMessage());

        SubmitPromotionAddRequest request = new SubmitPromotionAddRequest();
        request.setOrderId(1L);
        List<ReduceDetailAddItem> promotionInfoList = new ArrayList<>();
        request.setPromotionInfoList(promotionInfoList);
        Result<SubmitPromotionAddResponse> result3 = promotionAddDubboService.submitPromotion(request);
        Assertions.assertEquals("优惠信息为空", result3.getMessage());

        request.setPromotionInfoList(promotionInfoList);
        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setPromotionId(0L);
        item.setPromotionType(PromotionTypeEnum.EXCHANGE_SUBSIDY.code);
        item.setReduce(1200L);
        item.setSsuId(600008166L);
        promotionInfoList.add(item);

        Result<SubmitPromotionAddResponse> result4 = promotionAddDubboService.submitPromotion(request);
        Assertions.assertEquals("promotionId非法", result4.getMessage());

        item.setPromotionId(1L);
        item.setPromotionType(0);
        Result<SubmitPromotionAddResponse> result5 = promotionAddDubboService.submitPromotion(request);
        Assertions.assertEquals("promotionType非法", result5.getMessage());
    }

    /**
     * 追加优惠提交接口
     * 整车-订单立减优惠提交
     */
    @Test
    public void test_submitPromotion_31_success(){

        SubmitPromotionAddRequest request = new SubmitPromotionAddRequest();
        request.setOrderId(5255491002047027L);
        List<ReduceDetailAddItem> promotionInfoList = new ArrayList<>();
        request.setPromotionInfoList(promotionInfoList);
        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setPromotionId(21538627L);
        item.setPromotionType(PromotionTypeEnum.ORDER_REDUCE.code);
        item.setReduce(100L);
        item.setSsuId(600016359L);
        promotionInfoList.add(item);
        //存在锁定快照，直接更新入库
        Result<SubmitPromotionAddResponse> result = promotionAddDubboService.submitPromotion(request);
        Assertions.assertEquals(1,result.getData().getReduceItemList().size());


        request.setOrderId(5255531002031651L);
        request.setPromotionInfoList(promotionInfoList);
        item.setPromotionId(21536256L);
        item.setPromotionType(PromotionTypeEnum.ORDER_REDUCE.code);
        item.setReduce(100L);
        item.setSsuId(600016359L);
        //回滚快照更新入库
        Result<SubmitPromotionAddResponse> result2 = promotionAddDubboService.submitPromotion(request);
        Assertions.assertEquals(1,result2.getData().getReduceItemList().size());
    }

    /**
     * 追加优惠提交接口
     * 整车-置换补贴优惠提交
     *
     */
    @Test
    public void test_submitPromotion_29_success(){

        SubmitPromotionAddRequest request = new SubmitPromotionAddRequest();
        request.setOrderId(5255831002227941L);
        List<ReduceDetailAddItem> promotionInfoList = new ArrayList<>();
        request.setPromotionInfoList(promotionInfoList);
        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setPromotionId(21542480L);
        item.setPromotionType(PromotionTypeEnum.EXCHANGE_SUBSIDY.code);
        item.setReduce(1200L);
        item.setSsuId(600008166L);
        promotionInfoList.add(item);

        //存在锁定快照，直接更新入库
        Result<SubmitPromotionAddResponse> result = promotionAddDubboService.submitPromotion(request);
        Assertions.assertEquals(1,result.getData().getReduceItemList().size());


        Result<SubmitPromotionAddResponse> result2 = promotionAddDubboService.submitPromotion(request);
        Assertions.assertEquals("已经存在提交的活动，promotionId:21542480",result2.getMessage());

        //回滚快照更新入库
        request.setOrderId(5255921001908206L);
        item.setPromotionId(21542480L);
        item.setReduce(100L);
        Result<SubmitPromotionAddResponse> result3 = promotionAddDubboService.submitPromotion(request);
        Assertions.assertEquals(1,result3.getData().getReduceItemList().size());
    }

    /**
     * 追加优惠下单接口
     * 整车-下单立减
     */
    @Test
    public void test_checkoutPromotion_31_success() {

        carPromotionInstancePool.rebuildCacheTask();

        CheckoutPromotionAddRequest request = new CheckoutPromotionAddRequest();
        request.setOrderId(10000001L);
        request.setUserId(600003441L);
        request.setOrderPrice(99999L);
        List<ReduceDetailAddItem> promotionInfoList = new ArrayList<>();
        request.setPromotionInfoList(promotionInfoList);
        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setPromotionId(21507614L);
        item.setPromotionType(PromotionTypeEnum.BUY_REDUCE.code);
        item.setReduce(100L);
        item.setSsuId(600003441L);
        promotionInfoList.add(item);
        Result<CheckoutPromotionAddResponse> res = promotionAddDubboService.checkoutPromotion(request);
        Assertions.assertTrue(res.getData().getResult());
    }

    /**
     * 追加优惠下单接口
     * 整车-置换补贴
     */
    @Test
    public void test_checkoutPromotion_29_success() {

        carPromotionInstancePool.rebuildCacheTask();

        CheckoutPromotionAddRequest request = new CheckoutPromotionAddRequest();
        request.setOrderId(5255211000087598L);
        request.setUserId(3150448052L);
        request.setOrderPrice(99999L);
        List<ReduceDetailAddItem> promotionInfoList = new ArrayList<>();
        request.setPromotionInfoList(promotionInfoList);
        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setPromotionId(21534572L);
        item.setPromotionType(PromotionTypeEnum.EXCHANGE_SUBSIDY.code);
        item.setReduce(1000L);
        item.setSsuId(600003438L);
        promotionInfoList.add(item);
        Result<CheckoutPromotionAddResponse> res = promotionAddDubboService.checkoutPromotion(request);
        Assertions.assertTrue(res.getData().getResult());
    }

    @Test
    public void test_checkoutPromotion_fail(){

        Result<CheckoutPromotionAddResponse> result = promotionAddDubboService.checkoutPromotion(null);
        Assertions.assertEquals("订单ID非法", result.getMessage());

        CheckoutPromotionAddRequest request = new CheckoutPromotionAddRequest();
        request.setOrderId(1L);
        Result<CheckoutPromotionAddResponse> result2 = promotionAddDubboService.checkoutPromotion(request);
        Assertions.assertEquals("userId为空", result2.getMessage());

        request.setUserId(1L);
        List<ReduceDetailAddItem> promotionInfoList = new ArrayList<>();
        request.setPromotionInfoList(promotionInfoList);
        Result<CheckoutPromotionAddResponse> result3 = promotionAddDubboService.checkoutPromotion(request);
        Assertions.assertEquals("优惠信息为空", result3.getMessage());

        request.setPromotionInfoList(promotionInfoList);
        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setPromotionId(0L);
        item.setPromotionType(PromotionTypeEnum.EXCHANGE_SUBSIDY.code);
        item.setReduce(1200L);
        item.setSsuId(600008166L);
        promotionInfoList.add(item);

        Result<CheckoutPromotionAddResponse> result4 = promotionAddDubboService.checkoutPromotion(request);
        Assertions.assertEquals("promotionId非法", result4.getMessage());

        item.setPromotionId(1L);
        item.setPromotionType(0);
        Result<CheckoutPromotionAddResponse> result5 = promotionAddDubboService.checkoutPromotion(request);
        Assertions.assertEquals("promotionType非法", result5.getMessage());
    }

    @Test
    public void test_queryCarPromotionBudgetInfo_fail() {
        Result<PromotionBudgetResponse> result = promotionAddDubboService.queryCarPromotionBudgetInfo(null);
        Assertions.assertEquals("订单ID非法", result.getMessage());
    }

    /**
     * 查询直降优惠信息
     */
    @Test
    public void test_queryCarPromotionBudgetInfo_20_success() {
        PromotionBudgetRequest request = new PromotionBudgetRequest();
        request.setOrderId(5255921003972559L);
        Result<PromotionBudgetResponse> result = promotionAddDubboService.queryCarPromotionBudgetInfo(request);
        Assertions.assertEquals("BR202403200002", result.getData().getPromotionInfoList().getFirst().getBudgetApplyNo());
    }

    /**
     * 查询选装基金优惠信息
     */
    @Test
    public void test_queryCarPromotionBudgetInfo_28_success() {
        PromotionBudgetRequest request = new PromotionBudgetRequest();
        request.setOrderId(5255551000055437L);
        Result<PromotionBudgetResponse> result = promotionAddDubboService.queryCarPromotionBudgetInfo(request);
        Assertions.assertEquals("BR202403200004", result.getData().getPromotionInfoList().getFirst().getBudgetApplyNo());
    }

    /**
     * 查询订单立减优惠信息
     */
    @Test
    public void test_queryCarPromotionBudgetInfo_31_success() {
        PromotionBudgetRequest request = new PromotionBudgetRequest();
        request.setOrderId(5255111003084961L);
        Result<PromotionBudgetResponse> result = promotionAddDubboService.queryCarPromotionBudgetInfo(request);
        Assertions.assertEquals("BR202407290001", result.getData().getPromotionInfoList().getFirst().getBudgetApplyNo());
    }

    /**
     * 查询置换补贴优惠信息
     */
    @Test
    public void test_queryCarPromotionBudgetInfo_29_success() {
        PromotionBudgetRequest request = new PromotionBudgetRequest();
        request.setOrderId(5255921001908206L);
        Result<PromotionBudgetResponse> result = promotionAddDubboService.queryCarPromotionBudgetInfo(request);
        Assertions.assertEquals("BR202403200004", result.getData().getPromotionInfoList().getFirst().getBudgetApplyNo());
    }

}