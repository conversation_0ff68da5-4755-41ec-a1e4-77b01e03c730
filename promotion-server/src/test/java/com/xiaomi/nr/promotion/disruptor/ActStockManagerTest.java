package com.xiaomi.nr.promotion.disruptor;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022/7/13
 */
public class ActStockManagerTest extends BaseTest {

    @Autowired
    private ActStockManager actStockManager;

    @Test
    public void testPublishEvent() throws InterruptedException {
        actStockManager.publishEvent(() -> new  StockRecord().setOrgCode("MI0101")
                .setActivityId(111L)
                .setResourceType(ResourceType.OFFLINE_ACT_STORE_LIMIT.getValue())
                .setAction("commit")
                .setDateTimeMills(System.currentTimeMillis()));
        Thread.sleep(5000000L);
    }
}