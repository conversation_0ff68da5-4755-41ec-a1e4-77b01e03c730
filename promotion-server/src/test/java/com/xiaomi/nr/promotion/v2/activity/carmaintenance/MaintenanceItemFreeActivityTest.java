package com.xiaomi.nr.promotion.v2.activity.carmaintenance;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.activity.carmaintenance.MaintenanceItemFreeActivity;
import com.xiaomi.nr.promotion.api.dto.CheckProductsActRequest;
import com.xiaomi.nr.promotion.api.dto.model.CheckGoodsItem;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.MaintenanceItemFreePromotionConfig;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

@Slf4j
public class MaintenanceItemFreeActivityTest extends BaseTestV2 {

    @InjectMocks
    private MaintenanceItemFreeActivity activity;

    @Mock
    private DSLEngine dslEngine;

    @Mock
    private DSLStream dslStream;

    private MaintenanceItemFreePromotionConfig config;
    private LocalContext context;

    @BeforeEach
    public void setUp() {
        // 初始化配置
        config = new MaintenanceItemFreePromotionConfig();
        config.setDescription("测试工项免费活动");
        config.setPromotionId(1L);
        config.setCarIdentityType(1); // 假设1代表某种车辆身份类型
        config.setCarIdentityId("test_identity_id");
        config.setIdentityJoinLimitNum(3);
        config.setPromotionPrice(0L); // 免费活动，价格为0
        config.setMaxReduceAmount(1000L); // 最大扣减金额
        config.setWorkOrderType(Lists.newArrayList(1, 2)); // 支持的工单类型

        // 初始化上下文
        context = new LocalContext();
        context.setBizPlatform(BizPlatformEnum.MAINTENANCE_REPAIR);
    }

    @Test
    public void test_getType() {
        // 执行测试
        PromotionToolType type = activity.getType();

        // 验证结果
        assertEquals(PromotionToolType.MAINTENANCE_ITEM_FREE, type);
    }

    @Test
    public void test_getBizPlatform() {
        // 执行测试
        BizPlatformEnum platform = activity.getBizPlatform();

        // 验证结果
        assertEquals(BizPlatformEnum.MAINTENANCE_REPAIR, platform);
    }

    @Test
    public void test_getProductGoodsAct() throws BizError {
        // 准备测试数据
        Long clientId = 123L;
        String orgCode = "TEST_ORG";
        List<String> skuPackageList = Lists.newArrayList("SKU1", "SKU2");

        // 执行测试
        Map<String, ProductActInfo> result = activity.getProductGoodsAct(clientId, orgCode, skuPackageList);

        // 验证结果
        assertNull(result);
    }


    @Test
    public void test_checkProductsAct() throws BizError {
        // 准备测试数据
        CheckProductsActRequest request = new CheckProductsActRequest();
        Map<String, GoodsHierarchy> goodsHierarchyMap = null;

        // 执行测试
        List<CheckGoodsItem> result = activity.checkProductsAct(request, goodsHierarchyMap);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void test_getActivityDetail() {
        // 执行测试
        ActivityDetail result = activity.getActivityDetail();

        // 验证结果
        assertNull(result);
    }
}