package com.xiaomi.nr.promotion.activity.pool;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/12 14:28
 */
public class CarPromotionInstancePoolTest extends BaseTest {

    @Autowired
    private CarPromotionInstancePool carPromotionInstancePool;


    @Test
    public void testGetCurrentTools() {
        List<ActivityTool> toolList = carPromotionInstancePool.getCurrentTools(Arrays.asList(21551087L));
        Assert.assertNotNull(toolList);
        Assert.assertEquals(1, toolList.size());
    }
}
