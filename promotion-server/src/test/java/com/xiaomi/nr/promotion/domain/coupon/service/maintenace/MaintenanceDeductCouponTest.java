package com.xiaomi.nr.promotion.domain.coupon.service.maintenace;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.api.dto.enums.CouponType;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.MaintenanceInfo;
import com.xiaomi.nr.promotion.domain.coupon.model.*;
import com.xiaomi.nr.promotion.domain.coupon.service.base.type.CouponFactory;
import com.xiaomi.nr.promotion.engine.CouponTool;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.BizSubTypeEnum;
import com.xiaomi.nr.promotion.enums.CouponServiceTypeEnum;
import com.xiaomi.nr.promotion.enums.CouponTypeEnum;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import groovy.util.logging.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： pancras
 * @date： 2024/5/13
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Slf4j
public class MaintenanceDeductCouponTest extends BaseTest {

    @Autowired
    private CouponFactory couponFactory;

    private CheckoutCoupon initBasicMaintenanceMockCoupon() {
        CheckoutCoupon coupon = new CheckoutCoupon();
        coupon.setCouponId(123L);
        coupon.setCouponName("test");
        coupon.setConfigId(111L);
        coupon.setStatus("unused");
        coupon.setStartTime(1715582511L);
        coupon.setEndTime(1716187306L);
        coupon.setCouponType(CouponType.DEDUCT.getType());
        coupon.setPromotionType(PromotionToolType.COUPON_DEDUCT.getTypeId());
        coupon.setType(CouponTypeEnum.DEDUCT.getType());
        coupon.setDeductRule(1);
        coupon.setServiceType(CouponServiceTypeEnum.BASIC_MAINTENANCE.getCode());
        coupon.setValidGoodsList(Lists.newArrayList(1L, 2L));
        HashMap<Long, CouponRangeGoodsDO> goodsDOMap = new HashMap<>();

        MaintenanceCouponRangeGoodsDO range1 = new MaintenanceCouponRangeGoodsDO();
        range1.setSsuId(1L);
        range1.setCount(1);
        range1.setBizSubTypeEnum(BizSubTypeEnum.CAR_PARTS);
        goodsDOMap.put(range1.getSsuId(), range1);

        MaintenanceCouponRangeGoodsDO range2 = new MaintenanceCouponRangeGoodsDO();
        range2.setSsuId(2L);
        range2.setCount(1);
        range2.setBizSubTypeEnum(BizSubTypeEnum.CAR_WORK_HOUR);
        goodsDOMap.put(range2.getSsuId(), range2);

        coupon.setCouponRangeGoodsList(goodsDOMap);
        return coupon;
    }

    private List<CartItem> initMockCartItemList() {
        List<CartItem> cartItemList = new ArrayList<>();
        CartItem item1 = new CartItemForCoupon();
        item1.setItemId("test_basic");
        item1.setSku("1");
        item1.setSsuId(1L);
        item1.setCount(1);
        item1.setStandardPrice(1000L);
        item1.setCartPrice(1000L);
        item1.setCannotUseCoupon(Boolean.FALSE);
        item1.setMarketPrice(1000L);
        item1.setOriginalCartPrice(1000L);
        item1.setSource("");
        item1.setBizSubType(BizSubTypeEnum.CAR_PARTS.getCode());

        MaintenanceInfo info1 = new MaintenanceInfo();
        info1.setPayType(1);

        item1.setMaintenanceInfo(info1);
        cartItemList.add(item1);

        CartItem item2 = new CartItemForCoupon();
        item2.setItemId("test_basic");
        item2.setSku("2");
        item2.setSsuId(2L);
        item2.setCount(1);
        item2.setStandardPrice(1000L);
        item2.setCartPrice(1000L);
        item2.setCannotUseCoupon(Boolean.FALSE);
        item2.setMarketPrice(1000L);
        item2.setOriginalCartPrice(1000L);
        item2.setSource("");
        item2.setBizSubType(BizSubTypeEnum.CAR_WORK_HOUR.getCode());

        MaintenanceInfo info2 = new MaintenanceInfo();
        info2.setPayType(1);
        item2.setMaintenanceInfo(info2);
        cartItemList.add(item2);


        return cartItemList;
    }

    @Test
    public void testBasicMaintenance() throws BizError {
        CouponTool couponTool = couponFactory.getByTypeAndBiz(PromotionToolType.COUPON_DEDUCT, BizPlatformEnum.MAINTENANCE_REPAIR);
        CheckoutCoupon coupon = initBasicMaintenanceMockCoupon();
        couponTool.load(coupon);

        List<CartItem> cartItems = initMockCartItemList();
        CouponCheckoutContext context = new CouponCheckoutContext();
        context.setCartItemList(cartItems);

        couponTool.checkoutCoupon(context);


    }

    private CheckoutCoupon initPaintRepairMockCoupon() {
        CheckoutCoupon coupon = new CheckoutCoupon();
        coupon.setCouponId(123L);
        coupon.setCouponName("test");
        coupon.setConfigId(111L);
        coupon.setStatus("unused");
        coupon.setStartTime(1715582511L);
        coupon.setEndTime(1716187306L);
        coupon.setCouponType(CouponType.DEDUCT.getType());
        coupon.setPromotionType(PromotionToolType.COUPON_DEDUCT.getTypeId());
        coupon.setType(CouponTypeEnum.DEDUCT.getType());
        coupon.setDeductRule(2);
        coupon.setServiceType(CouponServiceTypeEnum.PAINT_REPAIR.getCode());
        coupon.setValidGoodsList(Lists.newArrayList(1L, 2L));
        coupon.setWorkHourStandardPage(10);
        HashMap<Long, CouponRangeGoodsDO> goodsDOMap = new HashMap<>();

        MaintenanceCouponRangeGoodsDO range1 = new MaintenanceCouponRangeGoodsDO();
        range1.setSsuId(1L);
        range1.setCount(1);
        range1.setBizSubTypeEnum(BizSubTypeEnum.CAR_PARTS);
        goodsDOMap.put(range1.getSsuId(), range1);

        MaintenanceCouponRangeGoodsDO range2 = new MaintenanceCouponRangeGoodsDO();
        range2.setSsuId(2L);
        range2.setCount(1);
        range2.setBizSubTypeEnum(BizSubTypeEnum.CAR_WORK_HOUR);
        goodsDOMap.put(range2.getSsuId(), range2);

        coupon.setCouponRangeGoodsList(goodsDOMap);
        return coupon;
    }

    private List<CartItem> initPaintRepairMockCartItemList() {
        List<CartItem> cartItemList = new ArrayList<>();
        CartItemForCoupon item1 = new CartItemForCoupon();
        item1.setItemId("test_basic");
        item1.setSku("1");
        item1.setSsuId(1L);
        item1.setCount(1);
        item1.setStandardPrice(1000L);
        item1.setCartPrice(1000L);
        item1.setCannotUseCoupon(Boolean.FALSE);
        item1.setMarketPrice(1000L);
        item1.setOriginalCartPrice(1000L);
        item1.setSource("");
        item1.setBizSubType(BizSubTypeEnum.CAR_PARTS.getCode());
        item1.setCartPriceBeforeCoupon(1000L);
        item1.setRemainStandardPage(3);

        MaintenanceInfo info1 = new MaintenanceInfo();
        info1.setPayType(1);
        info1.setWorkHourStandardPage(3);

        item1.setMaintenanceInfo(info1);
        cartItemList.add(item1);

        CartItemForCoupon item2 = new CartItemForCoupon();
        item2.setItemId("test_basic");
        item2.setSku("2");
        item2.setSsuId(2L);
        item2.setCount(1);
        item2.setStandardPrice(1000L);
        item2.setCartPrice(1000L);
        item2.setCannotUseCoupon(Boolean.FALSE);
        item2.setMarketPrice(1000L);
        item2.setOriginalCartPrice(1000L);
        item2.setSource("");
        item2.setBizSubType(BizSubTypeEnum.CAR_WORK_HOUR.getCode());
        item2.setCartPriceBeforeCoupon(1000L);
        item2.setRemainStandardPage(12);

        MaintenanceInfo info2 = new MaintenanceInfo();
        info2.setPayType(1);
        info2.setWorkHourStandardPage(12);
        info2.setCanAdjustPrice(true);
        item2.setMaintenanceInfo(info2);
        cartItemList.add(item2);


        return cartItemList;
    }

    @Test
    public void testPaintRepair() throws BizError {
        CouponTool couponTool = couponFactory.getByTypeAndBiz(PromotionToolType.COUPON_DEDUCT, BizPlatformEnum.MAINTENANCE_REPAIR);
        CheckoutCoupon coupon = initPaintRepairMockCoupon();
        couponTool.load(coupon);

        List<CartItem> cartItems = initPaintRepairMockCartItemList();
        CouponCheckoutContext context = new CouponCheckoutContext();
        context.setCartItemList(cartItems);

        couponTool.checkoutCoupon(context);
    }

    private CheckoutCoupon initRepairTairMockCoupon() {
        CheckoutCoupon coupon = new CheckoutCoupon();
        coupon.setCouponId(123L);
        coupon.setCouponName("test");
        coupon.setConfigId(111L);
        coupon.setStatus("unused");
        coupon.setStartTime(1715582511L);
        coupon.setEndTime(1716187306L);
        coupon.setCouponType(CouponType.DEDUCT.getType());
        coupon.setPromotionType(PromotionToolType.COUPON_DEDUCT.getTypeId());
        coupon.setType(CouponTypeEnum.DEDUCT.getType());
        coupon.setDeductRule(2);
        coupon.setServiceType(CouponServiceTypeEnum.REPAIR_TAIR.getCode());
        coupon.setValidGoodsList(Lists.newArrayList(1L, 2L));
        coupon.setWorkHourStandardPage(10);
        HashMap<Long, CouponRangeGoodsDO> goodsDOMap = new HashMap<>();

        MaintenanceCouponRangeGoodsDO range1 = new MaintenanceCouponRangeGoodsDO();
        range1.setSsuId(1L);
        range1.setCount(1);
        range1.setBizSubTypeEnum(BizSubTypeEnum.CAR_PARTS);
        goodsDOMap.put(range1.getSsuId(), range1);

        MaintenanceCouponRangeGoodsDO range2 = new MaintenanceCouponRangeGoodsDO();
        range2.setSsuId(2L);
        range2.setCount(1);
        range2.setBizSubTypeEnum(BizSubTypeEnum.CAR_WORK_HOUR);
        goodsDOMap.put(range2.getSsuId(), range2);

        coupon.setCouponRangeGoodsList(goodsDOMap);
        return coupon;
    }

    private List<CartItem> initRepairTairMockCartItemList() {
        List<CartItem> cartItemList = new ArrayList<>();
        CartItemForCoupon item1 = new CartItemForCoupon();
        item1.setItemId("test_basic");
        item1.setSku("1");
        item1.setSsuId(1L);
        item1.setCount(5);
        item1.setStandardPrice(1000L);
        item1.setCartPrice(1000L);
        item1.setCannotUseCoupon(Boolean.FALSE);
        item1.setMarketPrice(1000L);
        item1.setOriginalCartPrice(1000L);
        item1.setSource("");
        item1.setBizSubType(BizSubTypeEnum.CAR_PARTS.getCode());
        item1.setCartPriceBeforeCoupon(1000L);

        MaintenanceInfo info1 = new MaintenanceInfo();
        info1.setPayType(1);

        item1.setMaintenanceInfo(info1);
        cartItemList.add(item1);

        CartItemForCoupon item2 = new CartItemForCoupon();
        item2.setItemId("test_basic");
        item2.setSku("2");
        item2.setSsuId(2L);
        item2.setCount(2);
        item2.setStandardPrice(1000L);
        item2.setCartPrice(1000L);
        item2.setCannotUseCoupon(Boolean.FALSE);
        item2.setMarketPrice(1000L);
        item2.setOriginalCartPrice(1000L);
        item2.setSource("");
        item2.setBizSubType(BizSubTypeEnum.CAR_WORK_HOUR.getCode());
        item2.setCartPriceBeforeCoupon(1000L);

        MaintenanceInfo info2 = new MaintenanceInfo();
        info2.setPayType(1);
        item2.setMaintenanceInfo(info2);
        cartItemList.add(item2);


        return cartItemList;
    }

    @Test
    public void testRepairTair() throws BizError {
        CouponTool couponTool = couponFactory.getByTypeAndBiz(PromotionToolType.COUPON_DEDUCT, BizPlatformEnum.MAINTENANCE_REPAIR);
        CheckoutCoupon coupon = initRepairTairMockCoupon();
        couponTool.load(coupon);

        List<CartItem> cartItems = initRepairTairMockCartItemList();
        CouponCheckoutContext context = new CouponCheckoutContext();
        context.setCartItemList(cartItems);

        couponTool.checkoutCoupon(context);
    }
}
