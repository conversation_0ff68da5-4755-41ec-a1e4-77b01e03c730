package com.xiaomi.nr.promotion.v2.activity.carshop;

import com.xiaomi.nr.promotion.activity.carshop.CarShopVipDiscountActivity;
import com.xiaomi.nr.promotion.api.dto.MultiProductGoodsActRequest;
import com.xiaomi.nr.promotion.api.dto.model.MultiGoodItem;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfoDTO;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.ProductDetailContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.promotionconfig.CarShopVipDiscountPromotionConfig;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class CarShopVipDiscountActivityTest extends BaseTestV2 {

    @InjectMocks
    private CarShopVipDiscountActivity activity;

    private CarShopVipDiscountPromotionConfig config;
    private QuotaLevel quotaLevel;
    private HashSet<Long> joinGoods;

    @BeforeEach
    public void setUp() {
        // 初始化配置
        config = new CarShopVipDiscountPromotionConfig();
        config.setPromotionId(1L);
        config.setName("测试车商城会员折扣活动");
        config.setVipLevel(1);
        config.setUserJoinNumLimit(3);

        // 初始化折扣阶梯
        quotaLevel = new QuotaLevel();
        quotaLevel.setReduceDiscount(90L); // 90%折扣
        quotaLevel.setMaxReducePrice(1000L); // 最高减1000
        config.setLevelList(Collections.singletonList(quotaLevel));

        // 初始化参与商品
        joinGoods = new HashSet<>();
        joinGoods.add(1L);
        joinGoods.add(2L);

        ReflectionTestUtils.setField(activity, "unixStartTime", System.currentTimeMillis() / 1000);
        ReflectionTestUtils.setField(activity, "unixEndTime", System.currentTimeMillis() / 1000 + 1000 * 3600 * 24 * 7);
        ReflectionTestUtils.setField(activity, "type", ActivityTypeEnum.CAR_SHOP_VIP);
        ReflectionTestUtils.setField(activity, "identityLimit", true);
        ReflectionTestUtils.setField(activity, "vipLevel", 1);
        ReflectionTestUtils.setField(activity, "joinGoods", joinGoods);
        ReflectionTestUtils.setField(activity, "quotaLevel", quotaLevel);
    }

    @Test
    public void test_getType() {
        // 执行测试
        PromotionToolType type = activity.getType();

        // 验证结果
        assertEquals(PromotionToolType.CAR_SHOP_VIP, type);
    }

    @Test
    public void test_getBizPlatform() {
        // 执行测试
        BizPlatformEnum platform = activity.getBizPlatform();

        // 验证结果
        assertEquals(BizPlatformEnum.CAR_SHOP, platform);
    }

    @Test
    public void test_getActivityDetail() {
        // 执行测试
        ActivityDetail detail = activity.getActivityDetail();

        // 验证结果
        assertNull(detail);
    }

    @Test
    public void test_getMultiProductAct() throws BizError {
        // 准备测试数据
        MultiGoodItem goodItem = new MultiGoodItem();
        goodItem.setSsuId(1L);
        goodItem.setMarketPrice(2000L);
        MultiProductGoodsActRequest request = new MultiProductGoodsActRequest();
        request.setVipLevel(1);
        ProductDetailContext context = new ProductDetailContext();

        // 执行测试
        PromotionInfoDTO result = activity.getMultiProductAct(goodItem, request, context);

        // 验证结果
        assertNotNull(result);
        assertEquals(1800L, result.getPromotionPrice()); // 2000 * 0.9 = 1800
    }

    @Test
    public void test_getProductGoodsAct() throws BizError {
        // 准备测试数据
        Long clientId = 123L;
        String orgCode = "TEST_ORG";
        List<String> skuPackageList = Collections.singletonList("SKU001");

        // 执行测试
        Map<String, ProductActInfo> result = activity.getProductGoodsAct(clientId, orgCode, skuPackageList);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
