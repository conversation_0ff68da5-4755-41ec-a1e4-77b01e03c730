package com.xiaomi.nr.promotion.v2.componet.action.carmaintenance;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.MaintenanceInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.componet.action.carmaintenance.MaintenanceDiscountAction;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.entity.redis.QuotaEle;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.promotionconfig.MaintenanceDiscountPromotionConfig;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.provider.UserActivityCountProvider;
import com.xiaomi.nr.promotion.tool.CheckoutCartToolV2;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@Slf4j
//@ExtendWith(MockitoExtension.class)
public class MaintenanceDiscountActionTest extends BaseTestV2 {

    @InjectMocks
    private MaintenanceDiscountAction action;

    @Mock
    private CheckoutCartToolV2 checkoutCartToolV2;

    @Mock
    private ResourceProviderFactory resourceProviderFactory;

    @Mock
    private ActivityTool activityTool;

    private CheckoutPromotionRequest request;
    private LocalContext context;
    private MaintenanceDiscountPromotionConfig config;

    @BeforeEach
    public void setUp() {
        request = new CheckoutPromotionRequest();
        request.setUserId(123L);
        request.setSourceApi(SourceApi.SUBMIT);
        
        context = new LocalContext();
        
        config = new MaintenanceDiscountPromotionConfig();
        config.setPromotionId(1L);
        config.setCarIdentityId(1);
        config.setInvalidGoods(Lists.newArrayList(1L, 2L));
        config.setUserJoinNumLimit(3);
        
        List<QuotaLevel> levelList = new ArrayList<>();
        QuotaLevel level = new QuotaLevel();
        level.setReduceDiscount(90L); // 90%折扣
        level.setMaxReducePrice(1000L); // 最高减1000
        List<QuotaEle> quotas = new ArrayList<>();
        QuotaEle quota = new QuotaEle();
        quota.setType(1);
        quota.setCount(1);
        quotas.add(quota);
        level.setQuotas(quotas);
        levelList.add(level);
        config.setLevelList(levelList);
    }

    @Test
    public void test_execute_success() throws BizError {
        // 准备测试数据
        List<GoodsIndex> goodsIndexList = Lists.newArrayList(new GoodsIndex("1", 0));
        context.setGoodIndex(goodsIndexList);
        
        List<CartItem> cartItems = new ArrayList<>();
        CartItem item = new CartItem();
        item.setItemId("1");
        item.setSsuId(3L);
        item.setOriginalCartPrice(1000L);
        item.setCount(2);
        MaintenanceInfo maintenanceInfo = new MaintenanceInfo();
        maintenanceInfo.setPayType(1);
        item.setMaintenanceInfo(maintenanceInfo);
        cartItems.add(item);
        
        request.setCartList(cartItems);
        request.setActivityIds(Lists.newArrayList(1L));
        
        // Mock方法
        UserActivityCountProvider userActivityCountProvider = mock(UserActivityCountProvider.class);
        when(resourceProviderFactory.getProvider(ResourceType.USER_JOIN_ACT_NUM)).thenReturn(userActivityCountProvider);

        PromotionInfo promotionInfo = new PromotionInfo();
        when(activityTool.buildCartPromotionInfo(any())).thenReturn(promotionInfo);

        // 执行测试
        action.loadConfig(config);
        action.execute(activityTool, request, context);
        
        // 验证结果
        verify(checkoutCartToolV2, times(1)).divideCartsReduce(anyLong(), anyList(), anyList(), anyInt(), anyLong());
    }

    @Test
    public void test_execute_fail() throws BizError {
        // 场景1: 空的商品列表
        context.setGoodIndex(new ArrayList<>());
        request.setCartList(new ArrayList<>());
        request.setActivityIds(Lists.newArrayList(1L));

        UserActivityCountProvider userActivityCountProvider = mock(UserActivityCountProvider.class);
        when(resourceProviderFactory.getProvider(ResourceType.USER_JOIN_ACT_NUM)).thenReturn(userActivityCountProvider);

        PromotionInfo promotionInfo = new PromotionInfo();
        when(activityTool.buildCartPromotionInfo(any())).thenReturn(promotionInfo);

        action.loadConfig(config);
        action.execute(activityTool, request, context);
        
        // 场景2: 商品不满足条件
        List<GoodsIndex> goodsIndexList = Lists.newArrayList(new GoodsIndex("1", 0));
        context.setGoodIndex(goodsIndexList);
        
        List<CartItem> cartItems = new ArrayList<>();
        CartItem item = new CartItem();
        item.setItemId("1");
        item.setSsuId(1L); // 在黑名单中的商品
        item.setOriginalCartPrice(50L); // 不满足最低金额要求
        item.setCount(1);
        MaintenanceInfo maintenanceInfo = new MaintenanceInfo();
        maintenanceInfo.setPayType(2);
        item.setMaintenanceInfo(maintenanceInfo);
        cartItems.add(item);
        
        request.setCartList(cartItems);
        action.execute(activityTool, request, context);
        
        // 场景3: 活动ID不匹配
        request.setActivityIds(Lists.newArrayList(2L));
        action.execute(activityTool, request, context);
    }

    @Test
    public void test_loadConfig_success() {
        action.loadConfig(config);
    }

    @Test
    public void test_loadConfig_fail() {
        // 场景1: 配置为null
        action.loadConfig(null);
        Assertions.assertNull(ReflectionTestUtils.getField(action, "promotionId"));
    }
}
