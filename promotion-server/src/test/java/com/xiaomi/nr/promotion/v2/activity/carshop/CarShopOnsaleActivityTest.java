package com.xiaomi.nr.promotion.v2.activity.carshop;

import com.xiaomi.nr.promotion.activity.carshop.CarShopOnsaleActivity;
import com.xiaomi.nr.promotion.api.dto.model.GoodsDto;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionPriceDTO;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.OnsalePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class CarShopOnsaleActivityTest extends BaseTestV2 {

    @InjectMocks
    private CarShopOnsaleActivity activity;

    private OnsalePromotionConfig config;
    private Map<String, ActPriceInfo> onsaleInfoMap;

    @BeforeEach
    public void setUp() {
        // 初始化配置
        config = new OnsalePromotionConfig();
        config.setPromotionId(1L);
        config.setName("测试车商城直降活动");

        // 初始化直降信息
        onsaleInfoMap = new HashMap<>();
        ActPriceInfo priceInfo = new ActPriceInfo();
        priceInfo.setPrice(1000L);
        onsaleInfoMap.put("1", priceInfo);
        config.setOnsaleInfoMap(onsaleInfoMap);

        ReflectionTestUtils.setField(activity, "onsaleInfoMap", onsaleInfoMap);
        ReflectionTestUtils.setField(activity, "unixStartTime", System.currentTimeMillis() / 1000);
        ReflectionTestUtils.setField(activity, "unixEndTime", System.currentTimeMillis() / 1000 + 1000 * 3600 * 24 * 7);
        ReflectionTestUtils.setField(activity, "type", ActivityTypeEnum.ONSALE);
    }

    @Test
    public void test_getType() {
        // 执行测试
        PromotionToolType type = activity.getType();

        // 验证结果
        assertEquals(PromotionToolType.ONSALE, type);
    }

    @Test
    public void test_getBizPlatform() {
        // 执行测试
        BizPlatformEnum platform = activity.getBizPlatform();

        // 验证结果
        assertEquals(BizPlatformEnum.CAR_SHOP, platform);
    }

    @Test
    public void test_getActivityDetail() {
        // 执行测试
        ActivityDetail detail = activity.getActivityDetail();

        // 验证结果
        assertNull(detail);
    }


    @Test
    public void test_getGoodsPromotionPrice() {
        // 准备测试数据
        List<GoodsDto> goodsList = Collections.singletonList(new GoodsDto());
        goodsList.get(0).setSsuId(1L);
        goodsList.get(0).setPrice(2000L);
        Map<String, String> contextParams = Collections.emptyMap();

        // 执行测试
        Map<Long, PromotionPriceDTO> result = activity.getGoodsPromotionPrice(goodsList, contextParams);

        // 验证结果
        assertNotNull(result);
        assertEquals(1000, result.get(1L).getPrice());
    }

    @Test
    public void test_getProductGoodsAct() throws BizError {
        // 准备测试数据
        Long clientId = 123L;
        String orgCode = "TEST_ORG";
        List<String> skuPackageList = Collections.singletonList("SKU001");

        // 执行测试
        Map<String, ProductActInfo> result = activity.getProductGoodsAct(clientId, orgCode, skuPackageList);

        // 验证结果
        assertNull(result);
    }

}
