package com.xiaomi.nr.promotion.api.service;

import com.google.common.collect.Lists;
import com.xiaomi.nr.md.promotion.admin.api.constant.PromotionTypeEnum;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.model.CheckGoodsItem;
import com.xiaomi.nr.promotion.api.dto.model.MultiGoodItem;
import com.xiaomi.nr.promotion.api.dto.model.ProductActGoodsItem;
import com.xiaomi.nr.promotion.api.dto.model.carshop.Region;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 活动服务接口测试
 *
 * <AUTHOR>
 * @date 2021/6/15
 */
public class ActivityDubboServiceTest extends BaseTest {

    @Resource
    private ActivityDubboService activityDubboService;

    @Test
    public void getMultiProductGoodsAct(){
        MultiProductGoodsActRequest request=new MultiProductGoodsActRequest();
        request.setChannel(ChannelEnum.CAR_VEHICLE.getValue());
        List<MultiGoodItem> goodsList=new ArrayList<>();
        MultiGoodItem item1=new MultiGoodItem();
        item1.setSsuId(600003439L);
        MultiGoodItem item2=new MultiGoodItem();
        item2.setSsuId(600013274L);
//        item2.setMarketPrice(1000000L);
        goodsList.add(item1);
        goodsList.add(item2);
        request.setGoodsList(goodsList);
        request.setPromotionTypeList(List.of(PromotionTypeEnum.RANGE_REDUCE.code));
//        Region region = new Region();
//        region.setProvince(0);
//        region.setCity(0);
//        region.setArea(0);
//        region.setDistrict(0);
//        request.setRegion(region);
//        request.setVipLevel(2);
        Result<MultiProductGoodsActResponse> result = activityDubboService.getMultiProductGoodsAct(request);
        System.out.println();
    }

    @Test
    public void testGetProductGoodsAct() {
        GetProductGoodsActRequest request = new GetProductGoodsActRequest();
//        request.setActivityType(8);
        request.setOrgCode("MI0101");
        request.setSkuList(Arrays.asList("22540"));
        request.setQueryPurchaseSubsidy(true);
        Result<GetProductGoodsActResponse> result = activityDubboService.getProductGoodsAct(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(0, result.getCode());
    }

    @Test
    public void testGetPreProductGoodsAct() {
        GetPreProductGoodsActRequest request = new GetPreProductGoodsActRequest();
//        request.setActivityType(8);
        request.setClientId(180100031052L);
        request.setSkuPackageList(Arrays.asList("1204900001","15590", "19838", "9831", "2047"));

        Result<GetPreProductGoodsActResponse> result = activityDubboService.getPreProductGoodsAct(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(0, result.getCode());
    }

    @Test
    public void testGetGoodsActPrice() {
        GetGoodsActPriceRequest request = new GetGoodsActPriceRequest();
        request.setSkuPackageList(Arrays.asList("19320","15590", "19509", "9831", "2047"));

        Result<GetGoodsActPriceResponse> result = activityDubboService.getGoodsActPrice(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(0, result.getCode());
    }

    @Test
    public void testGetProductActPrice() {
        GetProductActPriceRequest request = new GetProductActPriceRequest();
        request.setOrgCode("JM11514");
        request.setSkuPackageList(Arrays.asList("19320"));

        Result<GetProductActPriceResponse> result = activityDubboService.getProductActPrice(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(0, result.getCode());
    }

    @Test
    public void testGetProductActPriceDetail() {
        GetProductActPriceDetailRequest request = new GetProductActPriceDetailRequest();
        request.setSkuPackageList(Arrays.asList("22748","17389"));

        Result<GetProductActPriceDetailResponse> result = activityDubboService.getProductActPriceDetail(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(0, result.getCode());
    }

    /**
     * 线上
     */
    @Test
    public void testGetProductAct() {
        GetProductActRequest request = new GetProductActRequest();
        request.setClientId(180100031052L);
        request.setUserId(0L);
        request.setSku("12530");
        request.setUserIsFriend(1);
        request.setSaleSource("common");
        request.setPrice(10000L);
//        request.setOrgCode("MI0101");
        request.setProvinceId(0);
        request.setCityId(0);
        request.setDistrictId(0);
        request.setAreaId(0);

        Result<GetProductActResponse> result = activityDubboService.getProductAct(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(0, result.getCode());
    }

    /**
     * 线上+门店
     */
    @Test
    public void testGetProductAct_Store() {
        GetProductActRequest request = new GetProductActRequest();
        request.setClientId(180100031052L);
        request.setUserId(0L);
        request.setSku("15653");
        request.setSaleSource("common");
        request.setPrice(399900L);
        request.setOrgCode("MI0101");
        request.setProvinceId(2);
        request.setCityId(36);
        request.setDistrictId(384);
        request.setAreaId(384010);
        request.setShipmentType(0);
        Result<GetProductActResponse> result = activityDubboService.getProductAct(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(0, result.getCode());
    }

    @Test
    public void test() {
        GetActivityDetailRequest request = new GetActivityDetailRequest();
        request.setActivityId(9989927L);

        Result<GetActivityDetailResponse> response = activityDubboService.getActivityDetail(request);
        System.out.println(GsonUtil.toJson(response));
        Assert.assertNotNull(response);
    }

    @Test
    public void testBatchGetActivityDetail() {
        BatchGetActivityDetailRequest request = new BatchGetActivityDetailRequest();
        request.setOrgCode("MI0101");
        request.setActivityIds(Arrays.asList(21330952L, 21330262L));

        Result<BatchGetActivityDetailResponse> response = activityDubboService.batchGetActivityDetail(request);
        System.out.println(GsonUtil.toJson(response));
        Assert.assertNotNull(response);
    }

    @Test
    public void testGetActivityAreaDetail() {
        GetActivityAreaDetailRequest request = new GetActivityAreaDetailRequest();
        request.setActivityId(9989927L);
        request.setProvinceId(2);
        request.setCityId(36);
        request.setDistrictId(384);
        request.setAreaId(384010);

        Result<GetActivityAreaDetailResponse> response = activityDubboService.getActivityAreaDetail(request);
        System.out.println(GsonUtil.toJson(response));
        Assert.assertNotNull(response);
    }


    @Test
    public void testGetStoreActPrice() {
        GetStoreActPriceRequest request = new GetStoreActPriceRequest();
        request.setOrgCode("MI0101");
        request.setSkuPackageList(Arrays.asList("19320"));

        Result<GetStoreActPriceResponse> result = activityDubboService.getStoreActPrice(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(0, result.getCode());
    }

    @Test
    public void testGetStoreGoodsActPrice() {
        GetStoreGoodsActPriceRequest request = new GetStoreGoodsActPriceRequest();
        request.setOrgCode("MI0101");

        Result<GetStoreGoodsActPriceResponse> result = activityDubboService.getStoreGoodsActPrice(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(0, result.getCode());
    }

    @Test
    public void testCheckProductsAct() {
        CheckProductsActRequest request = new CheckProductsActRequest();
        request.setOrgCode("MI0101");
        CheckGoodsItem checkGoodsItem = new CheckGoodsItem();
        checkGoodsItem.setSku(20855L);
        request.setMainGoodsList(Arrays.asList(checkGoodsItem));
        CheckGoodsItem checkGoodsItem2 = new CheckGoodsItem();
        checkGoodsItem2.setSku(16173L);
        checkGoodsItem2.setGroupId(1L);
        request.setAttachedGoodsList(Arrays.asList(checkGoodsItem2));
        request.setPromotionId(9989203L);
        Result<CheckProductsActResponse> result = activityDubboService.checkProductsAct(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(0, result.getCode());
    }

    /**
     * 产品站可参与活动V2（线上+门店）
     */
    @Test
    public void testGetProductActV2_Store() {
        ProductActGoodsItem g1 = new ProductActGoodsItem();
        g1.setId(15653L);
        g1.setLevel("sku");
        g1.setSalePrice(399900L);
        g1.setSaleMode("common");
        g1.setBusinessType(1);
        g1.setVirtual(false);

        ProductActGoodsItem g2 = new ProductActGoodsItem();
        g2.setId(1586L);
        g2.setLevel("sku");
        g2.setSalePrice(399900L);
        g2.setSaleMode("common");
        g2.setBusinessType(1);
        g2.setVirtual(false);

        ProductActGoodsItem g3 = new ProductActGoodsItem();
        g3.setId(19331L);
        g3.setLevel("sku");
        g3.setSalePrice(399900L);
        g3.setSaleMode("common");
        g3.setBusinessType(1);
        g3.setVirtual(false);

        ProductActGoodsItem g4 = new ProductActGoodsItem();
        g4.setId(16909L);
        g4.setLevel("sku");
        g4.setSalePrice(399900L);
        g4.setSaleMode("common");
        g4.setBusinessType(1);
        g4.setVirtual(false);


        List<ProductActGoodsItem> goodsList = new ArrayList<>();
        goodsList.add(g1);
        goodsList.add(g2);
        goodsList.add(g3);
        goodsList.add(g4);

        GetProductActV2Request request = new GetProductActV2Request();
        request.setClientId(180100031052L);
        request.setUserId(0L);
        request.setSaleSource("common");
        request.setOrgCode("MI0101");
        request.setProvinceId(2);
        request.setCityId(36);
        request.setDistrictId(384);
        request.setAreaId(384010);
        request.setShipmentType(0);
        request.setGoodsList(goodsList);

        Result<GetProductActV2Response> result = activityDubboService.getProductActV2(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(0, result.getCode());
    }

    /**
     * 产品站可参与活动V2（线上+门店）
     */
    @Test
    public void testGetProductActV2_Store2() {
        ProductActGoodsItem g1 = new ProductActGoodsItem();
        g1.setId(15981L);
        g1.setLevel("sku");
        g1.setSalePrice(399900L);
        g1.setSaleMode("standard");
        g1.setBusinessType(1);
        g1.setVirtual(false);



        List<ProductActGoodsItem> goodsList = new ArrayList<>();
        goodsList.add(g1);

        GetProductActV2Request request = new GetProductActV2Request();
        request.setClientId(180100031055L);
        request.setUserId(3150391509L);
        request.setSaleSource("common");
        request.setOrgCode("MI0101");
        request.setProvinceId(2);
        request.setCityId(36);
        request.setDistrictId(384);
        request.setAreaId(384010);
        request.setShipmentType(0);
        request.setUserIsFriend(true);
        request.setGoodsList(goodsList);

        Result<GetProductActV2Response> result = activityDubboService.getProductActV2(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(0, result.getCode());
    }
    
    /**
     * 产品站可参与活动V2（线上+门店）加价购测试
     */
    @Test
    public void testGetProductActV2_Bargain() {
        ProductActGoodsItem g1 = new ProductActGoodsItem();
        g1.setId(9278L);
        g1.setLevel("sku");
        g1.setSalePrice(289900L);
        g1.setSaleMode("standard");
        g1.setBusinessType(1);
        g1.setVirtual(false);
        
        
        
        List<ProductActGoodsItem> goodsList = new ArrayList<>();
        goodsList.add(g1);
        
        GetProductActV2Request request = new GetProductActV2Request();
        request.setClientId(180100031055L);
        request.setUserId(3150391509L);
        request.setSaleSource("common");
        request.setOrgCode("MI0101");
        request.setProvinceId(2);
        request.setCityId(36);
        request.setDistrictId(384);
        request.setAreaId(384010);
        request.setShipmentType(0);
        request.setUserIsFriend(true);
        request.setGoodsList(goodsList);
        
        Result<GetProductActV2Response> result = activityDubboService.getProductActV2(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(0, result.getCode());
    }

    @Test
    public void testGetProductActV2CrowdBuyGift() {
        ProductActGoodsItem g1 = new ProductActGoodsItem();
        g1.setId(11991L);
        g1.setLevel("sku");
        g1.setSalePrice(289900L);
        g1.setSaleMode("standard");
        g1.setBusinessType(1);
        g1.setVirtual(false);

        ProductActGoodsItem g2 = new ProductActGoodsItem();
        g2.setId(11999L);
        g2.setLevel("sku");
        g2.setSalePrice(289900L);
        g2.setSaleMode("standard");
        g2.setBusinessType(1);
        g2.setVirtual(false);

        ProductActGoodsItem g3 = new ProductActGoodsItem();
        g3.setId(12530L);
        g3.setLevel("sku");
        g3.setSalePrice(289900L);
        g3.setSaleMode("standard");
        g3.setBusinessType(1);
        g3.setVirtual(false);

        List<ProductActGoodsItem> goodsList = new ArrayList<>();
        goodsList.add(g1);
        goodsList.add(g2);
        goodsList.add(g3);

        GetProductActV2Request request = new GetProductActV2Request();
        request.setClientId(180100031055L);
        request.setUserId(3150437322L);
        request.setSaleSource("common");
        request.setOrgCode("MI0101");
        request.setProvinceId(2);
        request.setCityId(36);
        request.setDistrictId(384);
        request.setAreaId(384010);
        request.setShipmentType(0);
        request.setGoodsList(goodsList);

        Result<GetProductActV2Response> result = activityDubboService.getProductActV2(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(0, result.getCode());
    }

    @Test
    public void test_getProductActJoinInfo() {
        ProductActJoinInfoRequest req = new ProductActJoinInfoRequest();
        req.setVid("LKBQ1KFYHKW3CCCU2");
        req.setMid(3150426120L);
        req.setPromotionIds(Lists.newArrayList(21532208L, 21533918L, 21533916L));
        Result<ProductActJoinInfoResponse> result = activityDubboService.getProductActJoinInfo(req);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(0, result.getCode());
    }
}