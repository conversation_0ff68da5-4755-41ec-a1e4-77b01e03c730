package com.xiaomi.nr.promotion.dao.redis;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.util.GsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 商品信息redis 测试
 *
 * <AUTHOR>
 * @date 2021/8/2
 */
public class GoodsRedisDaoTest extends BaseTest {
    @Autowired
    private GoodsRedisDao goodsRedisDao;

    @Test
    public void testGetHierarchyBySku() {
        GoodsHierarchy hierarchy = goodsRedisDao.getHierarchyBySku("4403");
        System.out.println(GsonUtil.toJson(hierarchy));
    }

    @Test
    public void testGetHierarchyByCommodityId() {
        GoodsHierarchy hierarchy = goodsRedisDao.getHierarchyBySku("1204900006");
        System.out.println(GsonUtil.toJson(hierarchy));
    }
}