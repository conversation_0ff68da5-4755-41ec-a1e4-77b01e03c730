package com.xiaomi.nr.promotion.api.service;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.api.dto.GetGoodsSubsidyActRequest;
import com.xiaomi.nr.promotion.api.dto.GetGoodsSubsidyActResponse;
import com.xiaomi.nr.promotion.api.dto.QueryProductLayerSubsidyQualificationReq;
import com.xiaomi.nr.promotion.api.dto.QueryProductLayerSubsidyQualificationResp;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.model.carshop.Region;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： Pancras
 * @date： 2024/9/19
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Slf4j
public class ActivityGoodsConfigDubboServiceTest extends BaseTest {
    
    @Autowired
    private ActivityGoodsConfigDubboService activityGoodsConfigDubboService;

    @Test
    public void testGetGoodsSubsidyAct_CQ() {
        GetGoodsSubsidyActRequest request = new GetGoodsSubsidyActRequest();
        Region region = new Region();
        region.setProvince(23);
        region.setCity(293);
        region.setDistrict(821469);
        region.setArea(821469002);

        request.setRegion(region);
        request.setChannel(ChannelEnum.MISHOP.getValue());
        request.setSkuList(Lists.newArrayList(20466L));
        request.setPersonalInfo("GLAB/ZcYqFH9a7ozbvyH7MdAMp81cwsDgFxygH5rtOksjHaMYFE4BdZ/q+6QLUf94W8mEBaVUP5HMhrXpsu11bQFIUS1EG36X+NTp0DSQDyaq0ORTspEsBVzHw0Q0m12cZh4D8PKgDlEsdCnwvA0b1IWV5TZz2iYVl5/OYBI9ejg8F/N3eMsBjYjBR8mCwFdgQJH9r3rOb3C3ayJCEl/HD/Z/+G52s3PG9tEkhF/OQ8j4jwYEtW8iyiBq08LqjOj6w97zw0R/xgQwyrxs1Q7tDduMVJUYnEBVxgUXF+NUWekHWo4/nxE7ElfQeElD8ITAQA=");

        Result<GetGoodsSubsidyActResponse> goodsSubsidyAct = activityGoodsConfigDubboService.getGoodsSubsidyAct(
                request);

        log.info("testGetGoodsSubsidyAct res = {}", GsonUtil.toJson(goodsSubsidyAct));
    }

    @Test
    public void testGetGoodsSubsidyAct() {
        GetGoodsSubsidyActRequest request = new GetGoodsSubsidyActRequest();
        Region region = new Region();
        region.setProvince(25);
        region.setCity(293);
        region.setDistrict(821469);
        region.setArea(821469002);

        request.setRegion(region);
        request.setChannel(ChannelEnum.MISHOP.getValue());
        request.setSkuList(Lists.newArrayList(22019L));
//        request.setOrgCode("MI0101");
        // request.setOrgCode("MI0101");
        request.setPersonalInfo("GLABIXUw7VRqUebSZ0UKLFfDu6mhESmYW0ccO7xppceXi4KVMOkswh+y9/x5mNaLlR3asAZ4b0gCCCUlVv5RTHJRhDlwXeANNBlXIX9lTk9VsXy/Iv48Srl/WzcDOW8j4rXu8SNAj3J509BL3yxfAuAqcnOiurDKZXuPWSoRmPa4Yz693tkPO80QlvlJoD8+Yxq+EicLtnYb2bsuh0IVnnfZnfEn0cUlBcWuFWbOlQLoAXEYEg33yD3t8E/BryJPvbeEPXL+/xgQZn06u4UL8N3oNorgk0345xgUyFj3vIQmHvqej4EhF+EFuMWWiaQTAQA=");

        Result<GetGoodsSubsidyActResponse> goodsSubsidyAct = activityGoodsConfigDubboService.getGoodsSubsidyAct(
                request);

        log.info("testGetGoodsSubsidyAct res = {}", GsonUtil.toJson(goodsSubsidyAct));
    }

    @Test
    public void jijianGetGoodsSubsidyAct() {
        GetGoodsSubsidyActRequest request = new GetGoodsSubsidyActRequest();
        Region region = new Region();
        region.setProvince(13);
        region.setCity(102);
        request.setRegion(region);
        request.setChannel(ChannelEnum.MISHOP.getValue());
        request.setSkuList(Lists.newArrayList(31164L));
        // request.setOrgCode("MI0101");
        // request.setOrgCode("MI0101");

        Result<GetGoodsSubsidyActResponse> goodsSubsidyAct = activityGoodsConfigDubboService.getGoodsSubsidyAct(
                request);

        log.info("testGetGoodsSubsidyAct res = {}", GsonUtil.toJson(goodsSubsidyAct));
    }

    @Test
    public void jijianCouponSiteGetGoodsSubsidyAct() {
        GetGoodsSubsidyActRequest request = new GetGoodsSubsidyActRequest();
        Region region = new Region();
        region.setProvince(11);
        request.setRegion(region);
        request.setChannel(ChannelEnum.MISHOP.getValue());
        request.setSkuList(Lists.newArrayList(31164L));
        request.setPersonalInfo("GLABVZT8YM0N6Q7zQd2c5UrbJKOeX6luM0Nsuh2m4XcqhJD1sej4Q6EHWpq9AdgTsAmDjrUP6H0+Ehwj6BEOI6CmMCOkN3YV+xPGPe7mpWz50U81u0b/2CQ1pd+3FEFnefSDOxBxlp20xtP4fLgM7nL2tkKqE19KSU9sObQCobvFFbt7kmcDPYNVBvR2daJAWcSFYPF5HOGIDN8lvOmUTZZGNXkcuKACXJMDGWkjuiHoKfEYEopqtRuSMkzbuRloyCyQ4fvI/xgQm44Scg/OWISlIAJX64SYSRgU9LShmwJa+e9NCvWoa6Y/rLEED0sTAQA=");
        Result<GetGoodsSubsidyActResponse> goodsSubsidyAct = activityGoodsConfigDubboService.getGoodsSubsidyAct(
                request);

        log.info("testGetGoodsSubsidyAct res = {}", GsonUtil.toJson(goodsSubsidyAct));
    }

    @Test
    public void jijianCouponSiteAndCommomGetGoodsSubsidyAct() {
        GetGoodsSubsidyActRequest request = new GetGoodsSubsidyActRequest();
        Region region = new Region();
        region.setProvince(11);
        request.setRegion(region);
        request.setChannel(ChannelEnum.MISHOP.getValue());
        request.setSkuList(Lists.newArrayList(31163L));
        request.setPersonalInfo("GLABVZT8YM0N6Q7zQd2c5UrbJKOeX6luM0Nsuh2m4XcqhJD1sej4Q6EHWpq9AdgTsAmDjrUP6H0+Ehwj6BEOI6CmMCOkN3YV+xPGPe7mpWz50U81u0b/2CQ1pd+3FEFnefSDOxBxlp20xtP4fLgM7nL2tkKqE19KSU9sObQCobvFFbt7kmcDPYNVBvR2daJAWcSFYPF5HOGIDN8lvOmUTZZGNXkcuKACXJMDGWkjuiHoKfEYEopqtRuSMkzbuRloyCyQ4fvI/xgQm44Scg/OWISlIAJX64SYSRgU9LShmwJa+e9NCvWoa6Y/rLEED0sTAQA=");
        Result<GetGoodsSubsidyActResponse> goodsSubsidyAct = activityGoodsConfigDubboService.getGoodsSubsidyAct(
                request);

        log.info("testGetGoodsSubsidyAct res = {}", GsonUtil.toJson(goodsSubsidyAct));
    }
    
    @Test
    public void testGetGoodsSubsidyActList() {
        GetGoodsSubsidyActRequest request = new GetGoodsSubsidyActRequest();
        request.setOrgCode("SQ678");
        request.setSkuList(Lists.newArrayList(15264L, 22401L,22403L, 14613L));
        request.setChannel(2);

        Result<GetGoodsSubsidyActResponse> goodsSubsidyAct = activityGoodsConfigDubboService.getGoodsSubsidyActList(
                request);

        log.info("testGetGoodsSubsidyActList res = {}", GsonUtil.toJson(goodsSubsidyAct));
    }

    @Test
    public void testQueryProductLayerSubsidyQualification() {
        QueryProductLayerSubsidyQualificationReq req = new QueryProductLayerSubsidyQualificationReq();
        req.setChannel(ChannelEnum.MISHOP.getValue());
        req.setId(18632L);
        req.setMid(3150433252L);

        Region region = new Region();
        region.setProvince(25);
        region.setCity(0);
        region.setArea(0);
        region.setDistrict(0);
        req.setRegion(region);

        Result<QueryProductLayerSubsidyQualificationResp> resp = activityGoodsConfigDubboService.queryProductLayerSubsidyQualification(req);

        log.info("testQueryProductLayerSubsidyQualification resp = {}", GsonUtil.toJson(resp));
    }
}
