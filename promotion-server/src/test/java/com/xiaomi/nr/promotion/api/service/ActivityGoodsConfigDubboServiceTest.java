package com.xiaomi.nr.promotion.api.service;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.api.dto.GetGoodsSubsidyActRequest;
import com.xiaomi.nr.promotion.api.dto.GetGoodsSubsidyActResponse;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.model.carshop.Region;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： Pancras
 * @date： 2024/9/19
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
@Slf4j
public class ActivityGoodsConfigDubboServiceTest extends BaseTest {
    
    @Autowired
    private ActivityGoodsConfigDubboService activityGoodsConfigDubboService;
    
    @Test
    public void testGetGoodsSubsidyAct() {
        GetGoodsSubsidyActRequest request = new GetGoodsSubsidyActRequest();
        Region region = new Region();
        region.setProvince(20);
//        region.setCity(102);
         request.setRegion(region);
         request.setChannel(ChannelEnum.MISHOP.getValue());
        request.setSkuList(Lists.newArrayList(19927L));
//        request.setOrgCode("MI0101");
        // request.setOrgCode("MI0101");
        request.setPersonalInfo("GLABBjOxtZ52JYfioIKV+lbrBdJwLm4hMMbJN7i0pdt163O26vVGXbXUIf8ozT4HfJ1ngnXP5qp7Soe0cAgq5tq8aQtn4yvq4/XKatymrLoOBs1JiEIP8kGifd4tgDBy7P1n5zLuSFcOQ4RYc+Nhf9lXk+8rmSuLpcyk9G8I43aV+npEQI030/wJqqJi2HNhvncWeb0tTw4CLLQDrl+ODVjAwGjCBkmDr4SzVaaMTY46oSYYEh6hg4rBg0EJvBvlgzZKIm6k/xgQq8OsqymgbCcgOUCy3QH1oxgUnDsMXDU4z2fl9KD1aiRQQ/6Fq3ATAQA\\u003d");
        
        Result<GetGoodsSubsidyActResponse> goodsSubsidyAct = activityGoodsConfigDubboService.getGoodsSubsidyAct(
                request);
        
        log.info("testGetGoodsSubsidyAct res = {}", GsonUtil.toJson(goodsSubsidyAct));
    }

    @Test
    public void jijianGetGoodsSubsidyAct() {
        GetGoodsSubsidyActRequest request = new GetGoodsSubsidyActRequest();
        Region region = new Region();
        region.setProvince(13);
        region.setCity(102);
        request.setRegion(region);
        request.setChannel(ChannelEnum.MISHOP.getValue());
        request.setSkuList(Lists.newArrayList(31164L));
        // request.setOrgCode("MI0101");
        // request.setOrgCode("MI0101");

        Result<GetGoodsSubsidyActResponse> goodsSubsidyAct = activityGoodsConfigDubboService.getGoodsSubsidyAct(
                request);

        log.info("testGetGoodsSubsidyAct res = {}", GsonUtil.toJson(goodsSubsidyAct));
    }
    
    @Test
    public void testGetGoodsSubsidyActList() {
        GetGoodsSubsidyActRequest request = new GetGoodsSubsidyActRequest();
        request.setOrgCode("SQ678");
        request.setSkuList(Lists.newArrayList(15264L, 22401L,22403L, 14613L));
        request.setChannel(2);

        Result<GetGoodsSubsidyActResponse> goodsSubsidyAct = activityGoodsConfigDubboService.getGoodsSubsidyActList(
                request);

        log.info("testGetGoodsSubsidyActList res = {}", GsonUtil.toJson(goodsSubsidyAct));
    }
}
