package com.xiaomi.nr.promotion.dao.mysql.promotionuser;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.PromotionResourceOperationLog;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 资源操作日志
 *
 * <AUTHOR>
 * @date 2021/5/6
 */
public class PromotionResourceOperationLogMapperTest extends BaseTest {

    @Autowired
    private PromotionResourceOperationLogMapper promotionResourceOperationLogMapper;

    @Test
    public void testInsertOperationLog() {
        PromotionResourceOperationLog log = new PromotionResourceOperationLog();
        log.setOrderId(187L);
        log.setTransactionId(1L);
        log.setUid(199L);
        log.setResourceContext("{}");
        log.setOperationSource(1);
        log.setOperationType(1);
        int count = promotionResourceOperationLogMapper.insertOperationLog(log);
        Assert.assertEquals(1, count);
    }
}