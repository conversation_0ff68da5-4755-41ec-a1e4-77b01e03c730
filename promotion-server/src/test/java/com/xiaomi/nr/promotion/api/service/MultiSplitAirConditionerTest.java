package com.xiaomi.nr.promotion.api.service;

import cn.hutool.json.JSONUtil;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class MultiSplitAirConditionerTest extends BaseTest {

    @Autowired
    public PromotionDubboService promotionDubboService;

    @Autowired
    private ActivityDubboService activityDubboService;

    /**
     * 测试普通套装和新套装区别
     */
    @Test
    public void testCheckoutPromotionOnSale() {
        String s = "{\n" +
                "    \"bargainSize\": 0,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                40,\n" +
                "                41\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"packageId\": \"600013315\",\n" +
//                "            \"sku\": \"600013315\",\n" +
                "            \"ssuId\": \"600013315\",\n" +
                "            \"ssuType\": 1,\n" +
                "            \"count\": 1,\n" +
                "            \"cartPrice\": 20000,\n" +
                "            \"marketPrice\": 20000,\n" +
                "            \"standardPrice\": 20000,\n" +
                "            \"childs\": [\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"groupId\": 0,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 10000,\n" +
                "                    \"sku\": \"1\",\n" +
                "                    \"ssuId\": \"1\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 2,\n" +
                "                    \"groupId\": 1,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 5000,\n" +
                "                    \"sku\": \"2\",\n" +
                "                    \"ssuId\": \"2\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"displayType\": 1,\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"1224500216_1_buy\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"prePrice\": 0,\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 1733195315\n" +
                "        },\n" +
                "     {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                40,\n" +
                "                41\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": false,\n" +

                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"packageId\": \"1222200207\",\n" +
                "            \"cartPrice\": 469901,\n" +
                "            \"marketPrice\": 469901,\n" +
                "            \"standardPrice\": 469901,\n" +
                "            \"childs\": [\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"groupId\": 0,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 234500,\n" +
                "                    \"sku\": \"60387\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"groupId\": 1,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 234500,\n" +
                "                    \"sku\": \"60387\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"groupId\": 1,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 901,\n" +
                "                    \"sku\": \"60357\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 1,\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"1222200207_1_buy\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"prePrice\": 0,\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"\",\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 1733195315\n" +
                "        }" +
                "    ],\n" +
                "    \"cityId\": 325,\n" +
                "    \"clientId\": 180100041089,\n" +
                "    \"ecardConsumeDetail\": {},\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"\",\n" +
                "    \"idCard\": \"\",\n" +
                "    \"noSaveDbSubmit\": true,\n" +
                "    \"orderId\": 0,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"submitType\": 0,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"usePoint\": false,\n" +
                "    \"usePurchaseSubsidy\": false,\n" +
                "    \"useRedPacket\": true,\n" +
                "    \"userId\": 2343066611\n" +
                "}";
        CheckoutPromotionRequest request = GsonUtil.fromJson(s, CheckoutPromotionRequest.class);
        request.setOrderId(System.currentTimeMillis());
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        Assert.assertEquals(result.getCode(), 0);
    }

    /**
     * 新套装买赠测试
     */
    @Test
    public void testCheckoutPromotionBuyGift() {
        String s = "{\n" +
                "    \"bargainSize\": 10,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"cartPrice\": 29900,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 1,\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"2230001447_0_buy\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 29900,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"prePrice\": 0,\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"17389\",\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"standardPrice\": 29900,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 1733817144\n" +
                "        },\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                40,\n" +
                "                41\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"sku\": \"\",\n" +
                "            \"ssuId\": \"600013315\",\n" +
                "            \"ssuType\": 1,\n" +
                "            \"count\": 1,\n" +
                "            \"cartPrice\": 20000,\n" +
                "            \"marketPrice\": 20000,\n" +
                "            \"standardPrice\": 20000,\n" +
                "            \"childs\": [\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"groupId\": 0,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 10000,\n" +
                "                    \"sku\": \"1\",\n" +
                "                    \"ssuId\": \"1\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 2,\n" +
                "                    \"groupId\": 1,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 5000,\n" +
                "                    \"sku\": \"2\",\n" +
                "                    \"ssuId\": \"2\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"displayType\": 1,\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"1224500216_1_buy\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"prePrice\": 0,\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 1733195315\n" +
                "        }\n" +
                "    ],\n" +
                "    \"channel\": 1,\n" +
                "    \"clientId\": 180100031055,\n" +
                "    \"getCouponList\": true,\n" +
                "    \"globalBusinessPartner\": \"mishop\",\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"showType\": 1,\n" +
                "    \"submitType\": 0,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useDefaultCoupon\": true,\n" +
                "    \"userId\": 2203422514\n" +
                "}";
        CartPromotionRequest cartRequest = GsonUtil.fromJson(s, CartPromotionRequest.class);
        Result<CartPromotionResponse> cartResult = promotionDubboService.cartCheckoutPromotion(cartRequest);
        System.out.println();

        String checkoutStr = "{\n" +
                "    \"bargainSize\": 0,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                40,\n" +
                "                41\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"sku\": \"\",\n" +
                "            \"ssuId\": \"600013315\",\n" +
                "            \"ssuType\": 1,\n" +
                "            \"count\": 1,\n" +
                "            \"cartPrice\": 20000,\n" +
                "            \"marketPrice\": 20000,\n" +
                "            \"standardPrice\": 20000,\n" +
                "            \"childs\": [\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"groupId\": 0,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 10000,\n" +
                "                    \"sku\": \"1\",\n" +
                "                    \"ssuId\": \"1\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 2,\n" +
                "                    \"groupId\": 1,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 5000,\n" +
                "                    \"sku\": \"2\",\n" +
                "                    \"ssuId\": \"2\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"displayType\": 1,\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"1224500216_1_buy\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"prePrice\": 0,\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 1733195315\n" +
                "        },\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": true,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                40,\n" +
                "                41\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": true,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"cartPrice\": 6900,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 1,\n" +
                "            \"groupId\": 1,\n" +
                "            \"itemId\": \"8888888_0_gift_3126025_1_2221501229\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 6900,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"prePrice\": 0,\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"26671\",\n" +
                "            \"source\": \"gift\",\n" +
                "            \"sourceCode\": \"21527872\",\n" +
                "            \"standardPrice\": 6900,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 0\n" +
                "        }\n" +
                "    ],\n" +
                "    \"cityId\": 236,\n" +
                "    \"clientId\": 180100031055,\n" +
                "    \"ecardConsumeDetail\": {},\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"mishop\",\n" +
                "    \"idCard\": \"\",\n" +
                "    \"noSaveDbSubmit\": false,\n" +
                "    \"orderId\": ****************,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"submitType\": 0,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"usePoint\": false,\n" +
                "    \"usePurchaseSubsidy\": false,\n" +
                "    \"useRedPacket\": false,\n" +
                "    \"userId\": 2203422514\n" +
                "}";

        CheckoutPromotionRequest request = GsonUtil.fromJson(checkoutStr, CheckoutPromotionRequest.class);
        request.setOrderId(System.currentTimeMillis());
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        Assert.assertEquals(result.getCode(), 0);
    }

    /**
     * 测试加价购
     */
    @Test
    public void testCheckoutPromotionBargain() {

        String checkoutStr = "{\n" +
                "    \"bargainSize\": 0,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                40,\n" +
                "                41\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"sku\": \"\",\n" +
                "            \"ssuId\": \"600013312\",\n" +
                "            \"ssuType\": 1,\n" +
                "            \"count\": 1,\n" +
                "            \"cartPrice\": 20000,\n" +
                "            \"marketPrice\": 20000,\n" +
                "            \"standardPrice\": 20000,\n" +
                "            \"childs\": [\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"groupId\": 0,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 10000,\n" +
                "                    \"sku\": \"1\",\n" +
                "                    \"ssuId\": \"1\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 2,\n" +
                "                    \"groupId\": 1,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 5000,\n" +
                "                    \"sku\": \"2\",\n" +
                "                    \"ssuId\": \"2\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"displayType\": 1,\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"1224500216_1_buy\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"prePrice\": 0,\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 1733195315\n" +
                "        },\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": true,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                40,\n" +
                "                41\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": true,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"cartPrice\": 64900,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 1,\n" +
                "            \"groupId\": 1,\n" +
                "            \"itemId\": \"8888888_0_bargain_3124728_2230001180\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 64900,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"prePrice\": 0,\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"11999\",\n" +
                "            \"source\": \"bargain\",\n" +
                "            \"sourceCode\": \"21527990\",\n" +
                "            \"standardPrice\": 64900,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 1733831023\n" +
                "        }\n" +
                "    ],\n" +
                "    \"cityId\": 236,\n" +
                "    \"clientId\": 180100031055,\n" +
                "    \"ecardConsumeDetail\": {},\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"mishop\",\n" +
                "    \"idCard\": \"\",\n" +
                "    \"noSaveDbSubmit\": true,\n" +
                "    \"orderId\": 0,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"submitType\": 0,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"usePoint\": false,\n" +
                "    \"usePurchaseSubsidy\": false,\n" +
                "    \"useRedPacket\": true,\n" +
                "    \"userId\": 2203422514\n" +
                "}";

        CheckoutPromotionRequest request = GsonUtil.fromJson(checkoutStr, CheckoutPromotionRequest.class);
        request.setOrderId(System.currentTimeMillis());
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        Assert.assertEquals(result.getCode(), 0);
    }

    /**
     * 测试产品站接口
     */
    @Test
    public void testGetProductActV2() {
        String s = "{\n" +
                "    \"areaId\": 1449004,\n" +
                "    \"cityId\": 147,\n" +
                "    \"clientId\": 180100031052,\n" +
                "    \"districtId\": 1449,\n" +
                "    \"goodsList\": [\n" +
                "        {\n" +
                "            \"businessType\": 1,\n" +
                "            \"id\": 600013315,\n" +
                "            \"ssuType\": 1,\n" +
                "            \"level\": \"package\",\n" +
                "            \"saleMode\": \"standard\",\n" +
                "            \"salePrice\": 11549900,\n" +
                "            \"virtual\": false\n" +
                "        }\n" +
                "    ],\n" +
                "    \"provinceId\": 13,\n" +
                "    \"saleSource\": \"other\",\n" +
                "    \"shipmentType\": 0,\n" +
                "    \"userId\": 2484441641\n" +
                "}";
        GetProductActV2Request request = GsonUtil.fromJson(s, GetProductActV2Request.class);
        Result<GetProductActV2Response> productActV2 = activityDubboService.getProductActV2(request);
        System.out.println();
    }

    /**
     * 新套装测试满赠
     */
    @Test
    public void testCheckoutPromotionGift() {
        String checkoutStr = "{\n" +
                "    \"bargainSize\": 0,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                40,\n" +
                "                41\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"sku\": \"\",\n" +
                "            \"ssuId\": \"600013338\",\n" +
                "            \"ssuType\": 1,\n" +
                "            \"count\": 1,\n" +
                "            \"cartPrice\": 177400,\n" +
                "            \"marketPrice\": 177400,\n" +
                "            \"standardPrice\": 177400,\n" +
                "            \"childs\": [\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"groupId\": 0,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 169900,\n" +
                "                    \"sku\": \"1\",\n" +
                "                    \"ssuId\": \"1\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"groupId\": 1,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 7500,\n" +
                "                    \"sku\": \"2\",\n" +
                "                    \"ssuId\": \"2\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"displayType\": 1,\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"1224500216_1_buy\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"prePrice\": 0,\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 1733195315\n" +
                "        },\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": true,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                40,\n" +
                "                41\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": true,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"cartPrice\": 1900,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 1,\n" +
                "            \"groupId\": 1,\n" +
                "            \"itemId\": \"8888888_0_gift_3124728_2230001180\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 1900,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"prePrice\": 0,\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"1807\",\n" +
                "            \"source\": \"gift\",\n" +
                "            \"sourceCode\": \"21531343\",\n" +
                "            \"standardPrice\": 1900,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 1733831023\n" +
                "        }\n" +
                "    ],\n" +
                "    \"cityId\": 236,\n" +
                "    \"clientId\": 180100031055,\n" +
                "    \"ecardConsumeDetail\": {},\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"mishop\",\n" +
                "    \"idCard\": \"\",\n" +
                "    \"noSaveDbSubmit\": true,\n" +
                "    \"orderId\": 0,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"submitType\": 0,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"usePoint\": false,\n" +
                "    \"usePurchaseSubsidy\": false,\n" +
                "    \"useRedPacket\": true,\n" +
                "    \"userId\": 2203422514\n" +
                "}";
        CheckoutPromotionRequest request = GsonUtil.fromJson(checkoutStr, CheckoutPromotionRequest.class);
        request.setOrderId(System.currentTimeMillis());
        System.out.println(GsonUtil.toJson(request));
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(result.getCode(), 0);
    }


    /**
     * 多联机下单立减
     */
    @Test
    public void testCheckoutPromotion_BuyReduce() {
        String checkoutStr = "{\"sourceApi\":2,\"cartList\":[{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[{\"sku\":\"1\",\"ssuId\":1,\"sellPrice\":5990,\"count\":1,\"unitId\":\"\",\"onSaleBookingPrice\":0,\"lowerPrice\":0,\"originalSellPrice\":0,\"cartPrice\":0,\"checkoutPrice\":0,\"onsaleReduce\":0,\"storepriceReduce\":0,\"groupId\":0},{\"sku\":\"2\",\"ssuId\":2,\"sellPrice\":1290,\"count\":1,\"unitId\":\"\",\"onSaleBookingPrice\":0,\"lowerPrice\":0,\"originalSellPrice\":0,\"cartPrice\":0,\"checkoutPrice\":0,\"onsaleReduce\":0,\"storepriceReduce\":0,\"groupId\":1}],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"1224500216_1_buy\",\"sku\":\"\",\"packageId\":\"\",\"ssuId\":600013363,\"ssuType\":1,\"count\":1,\"standardPrice\":7280,\"cartPrice\":7280,\"prePrice\":0,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":1733195315,\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":7280,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[40,41],\"cannotUseCouponTypes\":[],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"cannotUsePoint\":false,\"canAdjustPrice\":false}],\"clientId\":180100031055,\"userId\":2203422514,\"orderId\":1735904591171,\"bargainSize\":0,\"noSaveDbSubmit\":true,\"ecardConsumeDetail\":{},\"useRedPacket\":true,\"calculateRedpacket\":false,\"useBeijingcoupon\":false,\"orgCode\":\"\",\"uidType\":\"\",\"cityId\":236,\"shoppingMode\":0,\"shipmentId\":2,\"shipmentExpense\":0,\"globalBusinessPartner\":\"mishop\",\"fromPriceProtect\":false,\"usePoint\":false,\"pointReduceAmount\":0,\"submitType\":0,\"idCard\":\"\",\"usePurchaseSubsidy\":false}";
        CheckoutPromotionRequest request = GsonUtil.fromJson(checkoutStr, CheckoutPromotionRequest.class);
        request.setOrderId(System.currentTimeMillis());
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(result.getCode(), 0);
    }

    /**
     * 多联机-券使用
     */
    @Test
    public void testCheckoutPromotion_Coupon() {
        String checkoutStr = "{\"sourceApi\":2,\"cartList\":[{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[{\"sku\":\"1\",\"ssuId\":1,\"sellPrice\":199900,\"count\":1,\"unitId\":\"\",\"onSaleBookingPrice\":0,\"lowerPrice\":0,\"originalSellPrice\":0,\"cartPrice\":0,\"checkoutPrice\":0,\"onsaleReduce\":0,\"storepriceReduce\":0,\"groupId\":0},{\"sku\":\"2\",\"ssuId\":2,\"sellPrice\":99900,\"count\":1,\"unitId\":\"\",\"onSaleBookingPrice\":0,\"lowerPrice\":0,\"originalSellPrice\":0,\"cartPrice\":0,\"checkoutPrice\":0,\"onsaleReduce\":0,\"storepriceReduce\":0,\"groupId\":1},{\"sku\":\"3\",\"ssuId\":3,\"sellPrice\":12000,\"count\":1,\"unitId\":\"\",\"onSaleBookingPrice\":0,\"lowerPrice\":0,\"originalSellPrice\":0,\"cartPrice\":0,\"checkoutPrice\":0,\"onsaleReduce\":0,\"storepriceReduce\":0,\"groupId\":2},{\"sku\":\"4\",\"ssuId\":4,\"sellPrice\":99900,\"count\":1,\"unitId\":\"\",\"onSaleBookingPrice\":0,\"lowerPrice\":0,\"originalSellPrice\":0,\"cartPrice\":0,\"checkoutPrice\":0,\"onsaleReduce\":0,\"storepriceReduce\":0,\"groupId\":3},{\"sku\":\"5\",\"ssuId\":5,\"sellPrice\":10000,\"count\":1,\"unitId\":\"\",\"onSaleBookingPrice\":0,\"lowerPrice\":0,\"originalSellPrice\":0,\"cartPrice\":0,\"checkoutPrice\":0,\"onsaleReduce\":0,\"storepriceReduce\":0,\"groupId\":4}],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600013434_1_buy\",\"sku\":\"\",\"packageId\":\"\",\"ssuId\":600013434,\"ssuType\":1,\"count\":1,\"standardPrice\":421700,\"cartPrice\":421700,\"prePrice\":0,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":1733195315,\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":421700,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[40,41],\"cannotUseCouponTypes\":[],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"cannotUsePoint\":false,\"canAdjustPrice\":false}],\"clientId\":180100031055,\"userId\":1348091010,\"orderId\":1735904591171,\"bargainSize\":0,\"noSaveDbSubmit\":true,\"ecardConsumeDetail\":{},\"useRedPacket\":true,\"calculateRedpacket\":false,\"useBeijingcoupon\":false,\"orgCode\":\"\",\"uidType\":\"\",\"cityId\":236,\"shoppingMode\":0,\"shipmentId\":2,\"shipmentExpense\":0,\"globalBusinessPartner\":\"mishop\",\"fromPriceProtect\":false,\"usePoint\":false,\"pointReduceAmount\":0,\"submitType\":0,\"idCard\":\"\",\"usePurchaseSubsidy\":false,\"couponId\":[1011029835,1011087738],\"couponCodes\":[]}";
        CheckoutPromotionRequest request = GsonUtil.fromJson(checkoutStr, CheckoutPromotionRequest.class);
        request.setOrderId(System.currentTimeMillis());
        System.out.println(GsonUtil.toJson(request));
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(result.getCode(), 0);
    }

    @Test
    public void testCheckoutPromotion_ManZeng() {
        // 满赠
        String s = "{\"cartList\":[{\"itemId\":\"600016365_1_buy\",\"sku\":\"\",\"packageId\":\"600016365\",\"count\":1,\"standardPrice\":160000,\"cartPrice\":160000,\"prePrice\":0,\"reduceAmount\":0,\"reduceList\":null,\"childs\":[{\"sku\":\"64519\",\"ssuId\":2230000783,\"sellPrice\":80000,\"count\":2,\"onSaleBookingPrice\":0,\"unitId\":\"\",\"lowerPrice\":0,\"originalSellPrice\":0,\"groupId\":0}],\"source\":\"\",\"sourceCode\":\"\",\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":1736423235,\"displayType\":1,\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"accessCode\":[],\"marketPrice\":160000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":null,\"cannotUseCouponTypes\":null,\"saleSource\":\"common\",\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"preferentialInfos\":null,\"checkoutPrice\":0,\"subItemList\":null,\"saleSources\":[],\"reduceShareMap\":null,\"goodsName\":\"空调多联机-小1匹\",\"ssuId\":600016365,\"ssuType\":1}],\"clientId\":180100031016,\"userId\":3150445185,\"couponIds\":null,\"channel\":1,\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"mishop\",\"getCouponList\":true}";
       // 买赠
        s = "{\"cartList\":[{\"itemId\":\"600013453_1_buy\",\"sku\":\"\",\"packageId\":\"600013453\",\"count\":1,\"standardPrice\":1150000,\"cartPrice\":1150000,\"prePrice\":0,\"reduceAmount\":0,\"reduceList\":null,\"childs\":[{\"sku\":\"64531\",\"ssuId\":2230000779,\"sellPrice\":100000,\"count\":1,\"onSaleBookingPrice\":0,\"unitId\":\"\",\"lowerPrice\":0,\"originalSellPrice\":0,\"groupId\":0},{\"sku\":\"64520\",\"ssuId\":2230000810,\"sellPrice\":80000,\"count\":1,\"onSaleBookingPrice\":0,\"unitId\":\"\",\"lowerPrice\":0,\"originalSellPrice\":0,\"groupId\":1},{\"sku\":\"64519\",\"ssuId\":2230000783,\"sellPrice\":80000,\"count\":1,\"onSaleBookingPrice\":0,\"unitId\":\"\",\"lowerPrice\":0,\"originalSellPrice\":0,\"groupId\":2},{\"sku\":\"66224\",\"ssuId\":2230000813,\"sellPrice\":690000,\"count\":1,\"onSaleBookingPrice\":0,\"unitId\":\"\",\"lowerPrice\":0,\"originalSellPrice\":0,\"groupId\":3},{\"sku\":\"64521\",\"ssuId\":2230000785,\"sellPrice\":100000,\"count\":2,\"onSaleBookingPrice\":0,\"unitId\":\"\",\"lowerPrice\":0,\"originalSellPrice\":0,\"groupId\":4}],\"source\":\"\",\"sourceCode\":\"\",\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":1736427459,\"displayType\":1,\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"accessCode\":[],\"marketPrice\":1150000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":null,\"cannotUseCouponTypes\":null,\"saleSource\":\"common\",\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"preferentialInfos\":null,\"checkoutPrice\":0,\"subItemList\":null,\"saleSources\":[],\"reduceShareMap\":null,\"goodsName\":\"大6匹 一拖五（3匹+1.5匹*2+1匹+小1匹=1.21）\",\"ssuId\":600013453,\"ssuType\":1}],\"clientId\":180100031016,\"userId\":3150445185,\"couponIds\":null,\"channel\":1,\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"mishop\",\"getCouponList\":true}";
        CartPromotionRequest cartRequest = GsonUtil.fromJson(s, CartPromotionRequest.class);
        System.out.println(GsonUtil.toJson(cartRequest));
        Result<CartPromotionResponse> cartResult = promotionDubboService.cartCheckoutPromotion(cartRequest);
        System.out.println(GsonUtil.toJson(cartResult));

    }

    @Test
    public void testCheckoutPromotion_SZ_MiShop() {

        String s="{\n" +
                "    \"bargainSize\": 0,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                40,\n" +
                "                41\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"ssuId\": \"12468\",\n" +
                "            \"ssuType\": 1,\n" +
                "            \"count\": 1,\n" +
                "            \"cartPrice\": 20000,\n" +
                "            \"marketPrice\": 20000,\n" +
                "            \"standardPrice\": 20000,\n" +
                "            \"childs\": [\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"groupId\": 0,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 10000,\n" +
                "                    \"sku\": \"1\",\n" +
                "                    \"ssuId\": \"1\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 2,\n" +
                "                    \"groupId\": 1,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 5000,\n" +
                "                    \"sku\": \"2\",\n" +
                "                    \"ssuId\": \"2\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"displayType\": 1,\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"1224500216_1_buy\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {\n" +
                "\n" +
                "            },\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {\n" +
                "\n" +
                "            },\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"prePrice\": 0,\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {\n" +
                "\n" +
                "            },\n" +
                "            \"reduceItemList\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 1733195315\n" +
                "        }\n" +
                "    ],\n" +
                "    \"cityId\": 325,\n" +
                "    \"clientId\": 180100041089,\n" +
                "    \"ecardConsumeDetail\": {\n" +
                "\n" +
                "    },\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"\",\n" +
                "    \"idCard\": \"\",\n" +
                "    \"noSaveDbSubmit\": true,\n" +
                "    \"orderId\": 0,\n" +
                "    \"channel\": 1,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"submitType\": 0,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"usePoint\": false,\n" +
                "    \"usePurchaseSubsidy\": false,\n" +
                "    \"useRedPacket\": true,\n" +
                "    \"userId\": 3150433252,\n" +
                "    \"personalInfo\": \"GKABTTRIz41prkvcNgAuHpIGOsnxDrSoGyEmNWws10H9VBEOQ4SJSruqpIzJYGcVwIjDWao/76D91Zi71E7fdHyD80hgU4fI8IAYw53BKPmuWkmmhZd1CKXpzfPr6ghypJB1j8rFQv2Xp1HVFDeiwN8GmLFCbcqVlsUmt8evg8KZ1G8hz5R2xP5KdQJwcuwstwj2Wxv0MUA+XFWgKc+XssKR0hgSnTvKppzeTbusF7pN4eEJwvn/GBDBC8L+ayVkAmvUmKlWME0tGBSjEwrHVJT7yhooZcp2BYu7ywntSRMBAA==\",\n" +
                "    \"region\": {\n" +
                "        \"province\": 20,\n" +
                "        \"city\": 0,\n" +
                "        \"district\": 0,\n" +
                "        \"area\": 0\n" +
                "    }\n" +
                "}";
        // Lock
        CheckoutPromotionRequest request= GsonUtil.fromJson(s,CheckoutPromotionRequest.class);
        System.out.println("param:"+ JSONUtil.parseObj(request));
        request.setOrderId(System.currentTimeMillis());
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println("result:"+JSONUtil.parseObj(result));
    }

    @Test
    public void testCheckoutPromotion_BJ_MiShop() {

        String s="{\n" +
                "    \"bargainSize\": 0,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                40,\n" +
                "                41\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"ssuId\": \"11855\",\n" +
                "            \"ssuType\": 1,\n" +
                "            \"count\": 1,\n" +
                "            \"cartPrice\": 20000,\n" +
                "            \"marketPrice\": 20000,\n" +
                "            \"standardPrice\": 20000,\n" +
                "            \"childs\": [\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"groupId\": 0,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 10000,\n" +
                "                    \"sku\": \"1\",\n" +
                "                    \"ssuId\": \"1\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 2,\n" +
                "                    \"groupId\": 1,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 5000,\n" +
                "                    \"sku\": \"2\",\n" +
                "                    \"ssuId\": \"2\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"displayType\": 1,\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"1224500216_1_buy\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {\n" +
                "\n" +
                "            },\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {\n" +
                "\n" +
                "            },\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"prePrice\": 0,\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {\n" +
                "\n" +
                "            },\n" +
                "            \"reduceItemList\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 1733195315\n" +
                "        }\n" +
                "    ],\n" +
                "    \"cityId\": 325,\n" +
                "    \"clientId\": 180100041089,\n" +
                "    \"ecardConsumeDetail\": {\n" +
                "\n" +
                "    },\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"\",\n" +
                "    \"idCard\": \"\",\n" +
                "    \"noSaveDbSubmit\": true,\n" +
                "    \"orderId\": 0,\n" +
                "    \"channel\": 1,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"submitType\": 0,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"usePoint\": false,\n" +
                "    \"usePurchaseSubsidy\": false,\n" +
                "    \"useRedPacket\": true,\n" +
                "    \"userId\": 3150426595,\n" +
                "    \"personalInfo\": \"GKABTTRIz41prkvcNgAuHpIGOsnxDrSoGyEmNWws10H9VBEOQ4SJSruqpIzJYGcVwIjDWao/76D91Zi71E7fdHyD80hgU4fI8IAYw53BKPmuWkmmhZd1CKXpzfPr6ghypJB1j8rFQv2Xp1HVFDeiwN8GmLFCbcqVlsUmt8evg8KZ1G8hz5R2xP5KdQJwcuwstwj2Wxv0MUA+XFWgKc+XssKR0hgSnTvKppzeTbusF7pN4eEJwvn/GBDBC8L+ayVkAmvUmKlWME0tGBSjEwrHVJT7yhooZcp2BYu7ywntSRMBAA==\",\n" +
                "    \"region\": {\n" +
                "        \"province\": 2,\n" +
                "        \"city\": 0,\n" +
                "        \"district\": 0,\n" +
                "        \"area\": 0\n" +
                "    }\n" +
                "}";
        // Lock
        CheckoutPromotionRequest request= GsonUtil.fromJson(s,CheckoutPromotionRequest.class);
        System.out.println("param:"+ JSONUtil.parseObj(request));
        request.setOrderId(System.currentTimeMillis());
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println("result:"+JSONUtil.parseObj(result));
    }

    @Test
    public void testCheckoutPromotion_BJ_MiMd() {

        String s="{\n" +
                "    \"bargainSize\": 0,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                40,\n" +
                "                41\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"ssuId\": \"11855\",\n" +
                "            \"ssuType\": 1,\n" +
                "            \"count\": 1,\n" +
                "            \"cartPrice\": 20000,\n" +
                "            \"marketPrice\": 20000,\n" +
                "            \"standardPrice\": 20000,\n" +
                "            \"childs\": [\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"groupId\": 0,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 10000,\n" +
                "                    \"sku\": \"1\",\n" +
                "                    \"ssuId\": \"1\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 2,\n" +
                "                    \"groupId\": 1,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 5000,\n" +
                "                    \"sku\": \"2\",\n" +
                "                    \"ssuId\": \"2\",\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"displayType\": 1,\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"1224500216_1_buy\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {\n" +
                "\n" +
                "            },\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {\n" +
                "\n" +
                "            },\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"prePrice\": 0,\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {\n" +
                "\n" +
                "            },\n" +
                "            \"reduceItemList\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 1733195315\n" +
                "        }\n" +
                "    ],\n" +
                "    \"cityId\": 325,\n" +
                "    \"clientId\": 180100041089,\n" +
                "    \"ecardConsumeDetail\": {\n" +
                "\n" +
                "    },\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"\",\n" +
                "    \"idCard\": \"\",\n" +
                "    \"noSaveDbSubmit\": true,\n" +
                "    \"orderId\": 0,\n" +
                "    \"channel\": 2,\n" +
                "    \"orgCode\": \"MI0101\",\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"submitType\": 0,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"usePoint\": false,\n" +
                "    \"usePurchaseSubsidy\": false,\n" +
                "    \"useRedPacket\": true,\n" +
                "    \"userId\": 3150426595,\n" +
                "    \"personalInfo\": \"GKABTTRIz41prkvcNgAuHpIGOsnxDrSoGyEmNWws10H9VBEOQ4SJSruqpIzJYGcVwIjDWao/76D91Zi71E7fdHyD80hgU4fI8IAYw53BKPmuWkmmhZd1CKXpzfPr6ghypJB1j8rFQv2Xp1HVFDeiwN8GmLFCbcqVlsUmt8evg8KZ1G8hz5R2xP5KdQJwcuwstwj2Wxv0MUA+XFWgKc+XssKR0hgSnTvKppzeTbusF7pN4eEJwvn/GBDBC8L+ayVkAmvUmKlWME0tGBSjEwrHVJT7yhooZcp2BYu7ywntSRMBAA==\",\n" +
                "    \"region\": {\n" +
                "        \"province\": 0,\n" +
                "        \"city\": 0,\n" +
                "        \"district\": 0,\n" +
                "        \"area\": 0\n" +
                "    }\n" +
                "}";
        // Lock
        CheckoutPromotionRequest request= GsonUtil.fromJson(s,CheckoutPromotionRequest.class);
        System.out.println("param:"+ JSONUtil.parseObj(request));
        request.setOrderId(System.currentTimeMillis());
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println("result:"+JSONUtil.parseObj(result));
    }


}
