package com.xiaomi.nr.promotion.mq.producer;

import com.xiaomi.nr.promotion.bootstrap.PromotionBootstrap;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.mq.producer.entity.CarActivityUseMessage;
import com.xiaomi.nr.promotion.mq.producer.entity.CarEquityPerformanceMessage;
import com.xiaomi.nr.promotion.mq.producer.entity.Header;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.UUID;

/**
 * @author: zhangliwei6
 * @date: 2025/1/13 18:01
 * @description:
 */
@ActiveProfiles("staging")
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = {PromotionBootstrap.class, CarActivityUseProducerTest.class})
public class CarActivityUseProducerTest {

    @Autowired
    private CarActivityUseProducer carActivityUseProducer;

    @Autowired
    private CarEquityPerformanceProducer carEquityPerformanceProducer;

    @Test
    public void test_sendCarActivityMessage() {
        try {
            CarActivityUseMessage carActivityUseMessage = CarActivityUseMessage.builder()
                    .header(Header.builder().version("1.0").profile("staging").build())
                    .body(CarActivityUseMessage.Body.builder()
                            .unikey(UUID.randomUUID().toString().replace("-", ""))
                            .promotionId(21931517L)
                            .userId(1L)
                            .bizPlatform(BizPlatformEnum.CAR_SHOP.getValue())
                            .orderId(1L)
                            .joinTimes(1)
                            .changeTimeMillis(System.currentTimeMillis() / 1000)
                            .build())
                    .build();
            carActivityUseProducer.sendMessage(carActivityUseMessage);
        } catch (Exception e) {
            Assert.fail(e.getMessage());
            throw new RuntimeException(e);
        }
    }

    @Test
    public void test_sendCarEquityMessage() {
        // 赛道权益包发车权益mq，失败打日志即可
        CarEquityPerformanceMessage carEquityPerformanceMessage = CarEquityPerformanceMessage.builder()
                // 上游定义的，暂时写死
                .sourceSvc("ACTIVITY")
                .equityKey("1")
                .vid("1")
                .implAbilityId("1")
                .usageRecordId("1")
                .usageOrderId(1L)
                .build();
        carEquityPerformanceProducer.sendMessage(List.of(carEquityPerformanceMessage));
    }

    @Test
    public void test_sendCarEquityMessageWithKey() {
        // 赛道权益包发车权益mq，失败打日志即可
        CarEquityPerformanceMessage carEquityPerformanceMessage = CarEquityPerformanceMessage.builder()
                // 上游定义的，暂时写死
                .sourceSvc("ACTIVITY")
                .equityKey("1")
                .vid("1")
                .implAbilityId("1")
                .usageRecordId("1")
                .usageOrderId(1L)
                .build();
        carEquityPerformanceProducer.sendMessage(List.of(carEquityPerformanceMessage), "1");
    }
}
