package com.xiaomi.nr.promotion.tool;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.util.GsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * 结算购物车工具
 *
 * <AUTHOR>
 * @date 2021/8/6
 */
public class CheckoutCartToolTest extends BaseTest {

    @Autowired
    private CheckoutCartTool checkoutCartTool;

    @Test
    public void testDivideCartsReduce() {
        String idKey = "testKey";
        List<CartItem> cartItemList = getCartList();
        cartItemList.addAll(getCartList());
        //checkoutCartTool.divideCartsReduce(1000L, Arrays.asList(0, 1, 2, 3), cartItemList,  idKey);
        System.out.println(GsonUtil.toJson(cartItemList));
    }

    private List<CartItem> getCartList() {
        List<CartItem> cartItemList = new ArrayList<>();
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2191200015_0_buy");
        cartItem1.setSku("22029");
        cartItem1.setPackageId("");
        cartItem1.setCount(2);
        cartItem1.setStandardPrice(199900L);
        cartItem1.setCartPrice(199900L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1623037292L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(Arrays.asList("doorstep_access_2017"));
        cartItem1.setMarketPrice(199900L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("2202300005_0_insurance_@2191200015_0_buy");
        cartItem2.setSku("29660");
        cartItem2.setPackageId("");
        cartItem2.setCount(2);
        cartItem2.setStandardPrice(6900L);
        cartItem2.setCartPrice(6900L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setReduceList(new HashMap<>());
        cartItem2.setChilds(new ArrayList<>());
        cartItem2.setSource("insurance");
        cartItem2.setSourceCode("");
        cartItem2.setProperties("");
        cartItem2.setParentItemId("");
        cartItem2.setUpdateTime(1623037292L);
        cartItem2.setDisplayType(1);
        cartItem2.setCannotJoinAct(false);
        cartItem2.setCannotUseCoupon(false);
        cartItem2.setAccessCode(Arrays.asList("doorstep_access_2017"));
        cartItem2.setMarketPrice(7000L);
        cartItem2.setCannotUseEcard(false);
        cartItem2.setMaxUseEcardAmount(0L);
        cartItem2.setCannotJoinActTypes(new ArrayList<>());
        cartItem2.setCannotUseCouponTypes(new ArrayList<>());
        cartItem2.setSaleSource("insurance");
        cartItem2.setCannotUseRedPacket(false);
        cartItem2.setGroupId(0L);
        cartItem2.setOnSaleBookingPrice(0L);
        cartItem2.setJoinOnsale(false);
        cartItemList.add(cartItem2);
        return cartItemList;
    }
}