package com.xiaomi.nr.promotion.api.service;

import com.xiaomi.micar.club.api.model.member.MemberInfo;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Request;
import com.xiaomi.nr.promotion.domain.activity.service.common.VipMemberService;
import com.xiaomi.nr.promotion.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.concurrent.ListenableFuture;

/**
 * <AUTHOR>
 * @date 2025/1/6
 */
@Slf4j
public class VipDiscountCheckoutTest extends BaseTest {


    @Autowired
    public PromotionDubboService promotionDubboService;

    @Autowired
    private VipMemberService vipMemberService;

    @Test
    public void testCheckoutPromotionV2() {

        CheckoutPromotionV2Request req = GsonUtil.fromJson("{\n" +
                "    \"getCouponList\": true,\n" +
                "    \"showType\": 1,\n" +
                "    \"useDefaultCoupon\": true,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"reduceList\": {},\n" +
                "            \"reduceShareMap\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"childs\": [],\n" +
                "            \"bindMainAccessory\": false,\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"itemId\": \"2134700008\",\n" +
                "            \"sku\": \"30289\",\n" +
                "            \"packageId\": \"\",\n" +
                "            \"ssuId\": 2134700008,\n" +
                "            \"count\": 1,\n" +
                "            \"standardPrice\": 9900,\n" +
                "            \"cartPrice\": 9900,\n" +
                "            \"prePrice\": 0,\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"properties\": \"\",\n" +
                "            \"displayType\": 1,\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"accessCode\": [],\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"marketPrice\": 9900,\n" +
                "            \"cannotUseEcard\": true,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                1,\n" +
                "                2,\n" +
                "                3,\n" +
                "                4,\n" +
                "                8,\n" +
                "                5,\n" +
                "                9,\n" +
                "                27,\n" +
                "                7,\n" +
                "                20,\n" +
                "                10,\n" +
                "                23,\n" +
                "                26,\n" +
                "                22,\n" +
                "                25,\n" +
                "                28,\n" +
                "                70,\n" +
                "                71,\n" +
                "                72,\n" +
                "                2001,\n" +
                "                500,\n" +
                "                501,\n" +
                "                502,\n" +
                "                503,\n" +
                "                504,\n" +
                "                505,\n" +
                "                2002,\n" +
                "                29\n" +
                "            ],\n" +
                "            \"saleSource\": \"exchange\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"groupId\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"unitId\": \"\",\n" +
                "            \"goodsType\": 2,\n" +
                "            \"department\": 0,\n" +
                "            \"cannotUsePoint\": true,\n" +
                "            \"canAdjustPrice\": false\n" +
                "        }\n" +
                "    ],\n" +
                "    \"channel\": 13,\n" +
                "    \"userId\": 3150401733,\n" +
                "    \"couponIds\": [],\n" +
                "    \"couponCodes\": [],\n" +
                "    \"orderId\": 0,\n" +
                "    \"bargainSize\": 10,\n" +
                "    \"noSaveDbSubmit\": false,\n" +
                "    \"useRedPacket\": false,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"uidType\": \"\",\n" +
                "    \"cityId\": 0,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"globalBusinessPartner\": \"car\",\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"userIsFriend\": 0,\n" +
                "    \"usePoint\": false,\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"submitType\": 0,\n" +
                "    \"usePurchaseSubsidy\": false,\n" +
                "    \"region\": {\n" +
                "        \"province\": 2,\n" +
                "        \"city\": 36,\n" +
                "        \"district\": 384,\n" +
                "        \"area\": 384010\n" +
                "    }\n" +
                "}", CheckoutPromotionV2Request.class);

        promotionDubboService.checkoutPromotionV2(req);



    }

    @Test
    public void testCheckV2() {
        String json = "{\"getCouponList\":true,\"showType\":1,\"useDefaultCoupon\":false,\"cartList\":[{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600016462-1-1\",\"sku\":\"600016462\",\"packageId\":\"\",\"ssuId\":600016462,\"bizSubType\":13,\"count\":1,\"standardPrice\":110,\"cartPrice\":110,\"prePrice\":0,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":110,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false,\"canAdjustPrice\":false,\"maintenanceInfo\":{\"payType\":1,\"unitPrice\":100,\"workHour\":1.1,\"canAdjustPrice\":false,\"cannotUseCouponServiceTypes\":[]}},{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600001043-1-1_600016462-1-1\",\"sku\":\"600001043\",\"packageId\":\"\",\"ssuId\":600001043,\"bizSubType\":14,\"count\":1,\"standardPrice\":11500,\"cartPrice\":11500,\"prePrice\":0,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600016462-1-1\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":11500,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false,\"canAdjustPrice\":false,\"maintenanceInfo\":{\"payType\":1,\"canAdjustPrice\":false,\"cannotUseCouponServiceTypes\":[]}},{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600001042-1-1_600016462-1-1\",\"sku\":\"600001042\",\"packageId\":\"\",\"ssuId\":600001042,\"bizSubType\":14,\"count\":2,\"standardPrice\":11400,\"cartPrice\":11400,\"prePrice\":0,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600016462-1-1\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":11400,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false,\"canAdjustPrice\":false,\"maintenanceInfo\":{\"payType\":1,\"canAdjustPrice\":false,\"cannotUseCouponServiceTypes\":[]}}],\"channel\":12,\"userId\":79628248,\"couponIds\":[],\"activityIds\":[],\"couponCodes\":[],\"ecardIds\":[],\"orderId\":0,\"bargainSize\":10,\"noSaveDbSubmit\":false,\"useRedPacket\":false,\"calculateRedpacket\":true,\"useBeijingcoupon\":false,\"orgCode\":\"\",\"uidType\":\"\",\"cityId\":0,\"shoppingMode\":0,\"shipmentId\":2,\"shipmentExpense\":0,\"globalBusinessPartner\":\"car\",\"fromPriceProtect\":false,\"userIsFriend\":0,\"usePoint\":false,\"pointReduceAmount\":0,\"submitType\":0,\"vid\":\"1735890318928Tcgx\",\"usePurchaseSubsidy\":false,\"isExchange\":false,\"workOrderType\":4,\"isFirstCarOwner\":true,\"equityKeys\":[\"testEquity2\"]}";

        CheckoutPromotionV2Request request = GsonUtil.fromJson(json, CheckoutPromotionV2Request.class);
        promotionDubboService.checkoutPromotionV2(request);

    }

    @Test
    public void testVip() {

        ListenableFuture<MemberInfo> res = vipMemberService.getUserMemberInfoAsync(3150407921L, null);

    }

}
