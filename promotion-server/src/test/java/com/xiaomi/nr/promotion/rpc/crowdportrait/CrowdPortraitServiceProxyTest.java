package com.xiaomi.nr.promotion.rpc.crowdportrait;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.rpc.crowdportrait.model.RuleResponse;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @date 2025/4/30 09:52
 */
@Slf4j
public class CrowdPortraitServiceProxyTest extends BaseTest {
    @Autowired
    private CrowdPortraitServiceProxy crowdPortraitServiceProxy;

    @Test
    public void testBatchRuleQuery() throws BizError, ExecutionException, InterruptedException, TimeoutException {
        ListenableFuture<RuleResponse> future = crowdPortraitServiceProxy.batchRuleQuery("mid", "3150445185", Lists.newArrayList("793791"));

        RuleResponse resp = future.get(1000, TimeUnit.MILLISECONDS);

        log.info("testBatchRuleQuery resp = {}", GsonUtil.toJson(resp));
    }
}
