package com.xiaomi.nr.promotion.v2.dao.mysql;

import com.google.common.collect.Lists;
import com.xiaomi.mit.unittest.db.SetupDB;
import com.xiaomi.mit.unittest.field.autoconfig.SetupBean;
import com.xiaomi.nr.promotion.dao.mysql.mdpromotion.ActivityEntity;
import com.xiaomi.nr.promotion.dao.mysql.mdpromotion.ActivityMapper;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.NrActivityMapper;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.NrActivityPo;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.EcardLogMapper;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.TbEcardLog;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @author: zhangliwei6
 * @date: 2025/2/25 17:23
 * @description:
 */
@Slf4j
@SetupDB(value = "db/firstTest/nr_activity.sql", dataSourceName = "promotionConfigDatasource")
public class MysqlTest extends BaseTestV2 {

    private NrActivityPo activityPo;

    @Autowired
    private NrActivityMapper nrActivityMapper;

    @Autowired
    private EcardLogMapper ecardLogMapper;

    @Autowired
    private ActivityMapper activityMapper;

    @Test
    @SetupBean("field/firstTest/activityPo.json")
    public void test_xmPulseNatl_setupBean() {
        log.info(GsonUtil.toJson(activityPo));
        Assertions.assertEquals("fn1", activityPo.getName());
        List<NrActivityPo> activityPoList = nrActivityMapper.queryListByType(8, 1L);
        Assertions.assertEquals(3, activityPoList.size());
    }

    @Test
    @SetupDB(value = "db/firstTest/ecard_log.sql", dataSourceName = "promotionUserConfigDatasource")
    public void test_xmPulse() {
        List<TbEcardLog> ecardLogList = ecardLogMapper.listByOrderId(0L, 0L);
        Assertions.assertEquals(1, ecardLogList.size());
    }

    @Test
    public void test_mdPromotion() {
        List<ActivityEntity> activityEntityList = activityMapper.queryByIds(Lists.newArrayList(100L));
        Assertions.assertEquals(1, activityEntityList.size());
    }
}
