package com.xiaomi.nr.promotion.schedule;

import com.xiaomi.nr.promotion.BaseTest;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2025/4/9 11:32
 */
public class CacheDiffTaskTest extends BaseTest {

    @Autowired
    private CacheDiffTask cacheDiffTask;

    @Test
    public void testDiffAndSendMessage(){
        long startTime = System.currentTimeMillis() + 2000 * 5 * 60;
        cacheDiffTask.diffAndSendMessage(Collections.singleton(21513682L), Collections.emptySet(), startTime);
    }

    @Test
    public void testCacheDiffTask(){
        cacheDiffTask.carCacheDiff();
    }
}
