package com.xiaomi.nr.promotion.activity.pool;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * 缓存池测试
 *
 * <AUTHOR>
 * @date 2021/5/27
 */
public class PromotionInstancePoolTest extends BaseTest {

    @Autowired
    private PromotionInstancePool promotionInstancePool;

    @Test
    public void testGetCurrentTools() {
        List<ActivityTool> toolList = promotionInstancePool.getCurrentTools(Arrays.asList(2588L, 2589L));
        Assert.assertNotNull(toolList);
        Assert.assertEquals(1, toolList.size());
    }

    @Test
    public void testGetCurrentActIds() {
        Set<Long> actIdSet = promotionInstancePool.getCurrentActIds(Arrays.asList("2088", "1652"));
        Assert.assertNotNull(actIdSet);
        Assert.assertNotEquals(0, actIdSet.size());
    }

    @Test
    public void testRebuildCacheTask() throws Exception {
        promotionInstancePool.rebuildCacheTask();
    }
}