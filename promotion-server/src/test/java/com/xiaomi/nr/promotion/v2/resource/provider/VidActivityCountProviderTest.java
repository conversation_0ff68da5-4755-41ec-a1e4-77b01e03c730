package com.xiaomi.nr.promotion.v2.resource.provider;

import com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionCarActivityCountMapper;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.CarActivityCount;
import com.xiaomi.nr.promotion.mq.producer.CarEquityPerformanceProducer;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.provider.VidActivityCountProvider;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

//@ExtendWith(MockitoExtension.class)
public class VidActivityCountProviderTest extends BaseTestV2 {

    @InjectMocks
    private VidActivityCountProvider provider;

    @Mock
    private PromotionCarActivityCountMapper carActivityCountMapper;

    @Mock
    private CarEquityPerformanceProducer carEquityPerformanceProducer;

    private ResourceObject<VidActivityCountProvider.VidJoinActNum> resourceObject;
    private VidActivityCountProvider.VidJoinActNum vidJoinActNum;

    @BeforeEach
    void setUp() {
        vidJoinActNum = new VidActivityCountProvider.VidJoinActNum();
        vidJoinActNum.setVid("test_vid");
        vidJoinActNum.setActId(100L);
        vidJoinActNum.setVidJoinNumLimit(5);

        resourceObject = new ResourceObject<>();
        resourceObject.setContent(vidJoinActNum);
        provider.initResource(resourceObject);
    }

    @Test
    void test_lock_success() throws BizError {
        // Given
        when(carActivityCountMapper.getByVidAndPromotionId(anyString(), anyLong())).thenReturn(null);

        // When
        provider.lock();

        // Then
        verify(carActivityCountMapper).insert(any(CarActivityCount.class));
        verify(carEquityPerformanceProducer).sendMessage(anyList(), anyString());
    }

    @Test
    void test_lock_fail_vidJoinNumOverLimit() {
        // Given
        CarActivityCount carActivityCount = new CarActivityCount();
        carActivityCount.setNum(5);
        when(carActivityCountMapper.getByVidAndPromotionId(anyString(), anyLong())).thenReturn(carActivityCount);

        // When & Then
        BizError exception = assertThrows(BizError.class, () -> provider.lock());
        assertEquals("该vid超过活动使用次数", exception.getMessage());
    }

    @Test
    void test_consume_success() throws BizError {
        // When
        provider.consume();

        // Then
        // No exception should be thrown
    }

    @Test
    void test_rollback_success() throws BizError {
        // Given
        CarActivityCount carActivityCount = new CarActivityCount();
        carActivityCount.setNum(2);
        carActivityCount.setExtend(GsonUtil.toJson(vidJoinActNum));
        when(carActivityCountMapper.getByVidAndPromotionId(anyString(), anyLong())).thenReturn(carActivityCount);

        // When
        provider.rollback();

        // Then
        verify(carActivityCountMapper).updateCount(anyString(), anyLong(), eq(1), eq(2), anyString(), anyLong());
        verify(carEquityPerformanceProducer).sendMessage(anyList(), anyString());
    }

    @Test
    void test_rollback_fail_numLessThanOne() throws BizError {
        // Given
        CarActivityCount carActivityCount = new CarActivityCount();
        carActivityCount.setNum(0);
        when(carActivityCountMapper.getByVidAndPromotionId(anyString(), anyLong())).thenReturn(carActivityCount);

        // When
        provider.rollback();

        // Then
        verify(carActivityCountMapper, never()).updateCount(anyString(), anyLong(), anyInt(), anyInt(), anyString(), anyLong());
        verify(carEquityPerformanceProducer, never()).sendMessage(anyList(), anyString());
    }
} 