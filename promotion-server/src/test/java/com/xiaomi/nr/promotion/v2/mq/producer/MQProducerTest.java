package com.xiaomi.nr.promotion.v2.mq.producer;

import com.xiaomi.nr.promotion.v2.BaseTestV2;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: zhangliwei6
 * @date: 2025/2/25 17:24
 * @description:
 */
@Slf4j
public class MQProducerTest extends BaseTestV2 {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Test
    public void test_send() {
        SendResult sendResult = rocketMQTemplate.syncSend("test_topic", "1");
    }
}
