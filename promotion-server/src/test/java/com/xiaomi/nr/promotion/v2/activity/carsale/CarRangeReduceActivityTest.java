package com.xiaomi.nr.promotion.v2.activity.carsale;

import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.activity.carsale.CarRangeReduceActivity;
import com.xiaomi.nr.promotion.api.dto.MultiProductGoodsActRequest;
import com.xiaomi.nr.promotion.api.dto.model.MultiGoodItem;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfoDTO;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.ProductDetailContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.CarRangeReducePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.model.promotionconfig.common.BenefitInfo;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ConfigMetaInfo;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class CarRangeReduceActivityTest extends BaseTestV2 {

    @InjectMocks
    private CarRangeReduceActivity activity;

    @Mock
    private DSLEngine dslEngine;

    @Mock
    private DSLStream dslStream;

    private CarRangeReducePromotionConfig config;
    private Map<String, ActPriceInfo> rangeReduceInfoMap;
    private BenefitInfo benefitInfo;

    @BeforeEach
    public void setUp() {
        // 初始化配置
        config = new CarRangeReducePromotionConfig();
        config.setPromotionId(1L);
        config.setName("测试汽车范围立减活动");
        config.setTradeType(1);
        config.setRangeReducePrice(5000L);
        config.setTitle("测试标题");
        config.setDescription("测试描述");
        config.setDescriptionTitle("测试描述标题");
        config.setRangeReduceRule("{\"reducePrice\":5000}");
        
        // 初始化立减信息
        rangeReduceInfoMap = Maps.newHashMap();
        ActPriceInfo actPriceInfo1 = new ActPriceInfo();
        actPriceInfo1.setPrice(5000L);
        actPriceInfo1.setSsuId(1001L);
        rangeReduceInfoMap.put("1001", actPriceInfo1);
        
        ActPriceInfo actPriceInfo2 = new ActPriceInfo();
        actPriceInfo2.setPrice(3000L);
        actPriceInfo2.setSsuId(1002L);
        rangeReduceInfoMap.put("1002", actPriceInfo2);
        
        config.setRangeReduceInfoMap(rangeReduceInfoMap);
        
        // 初始化权益信息
        benefitInfo = new BenefitInfo();
        benefitInfo.setStartTime(1000L);
        benefitInfo.setEndTime(2000L);
        config.setBenefitInfo(benefitInfo);

        ReflectionTestUtils.setField(activity, "unixStartTime", System.currentTimeMillis() / 1000);
        ReflectionTestUtils.setField(activity, "unixEndTime", System.currentTimeMillis() / 1000 + 1000*3600*24*7);
        ReflectionTestUtils.setField(activity, "type", ActivityTypeEnum.RANGE_REDUCE);
        ReflectionTestUtils.setField(activity, "metaInfo", new ConfigMetaInfo());
    }


    @Test
    public void test_getType() {
        // 执行测试
        PromotionToolType type = activity.getType();

        // 验证结果
        assertEquals(PromotionToolType.RANGE_REDUCE, type);
    }

    @Test
    public void test_getBizPlatform() {
        // 执行测试
        BizPlatformEnum platform = activity.getBizPlatform();

        // 验证结果
        assertEquals(BizPlatformEnum.CAR, platform);
    }

    @Test
    public void test_getActivityDetail() {
        // 执行测试
        ActivityDetail detail = activity.getActivityDetail();

        // 验证结果
        assertNull(detail);
    }

    @Test
    public void test_buildCartPromotionInfo() throws BizError {
        // 准备测试数据
        LocalContext context = new LocalContext();
        ReflectionTestUtils.setField(activity, "actMutexLimit", true);

        // 执行测试
        PromotionInfo result = activity.buildCartPromotionInfo(context);

        // 验证结果
        assertNotNull(result);
        assertEquals(BooleanEnum.YES.getValue(), result.getActivityMutexLimit());
    }

    @Test
    public void test_getProductGoodsAct() throws BizError {
        // 准备测试数据
        Long clientId = 123L;
        String orgCode = "TEST_ORG";
        List<String> skuPackageList = List.of("SKU1", "SKU2");

        // 执行测试
        Map<String, ProductActInfo> result = activity.getProductGoodsAct(clientId, orgCode, skuPackageList);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void test_getMultiProductAct() throws BizError {
        // 准备测试数据
        MultiGoodItem goodItem = new MultiGoodItem();
        MultiProductGoodsActRequest request = new MultiProductGoodsActRequest();
        ProductDetailContext context = new ProductDetailContext();
        ReflectionTestUtils.setField(activity, "promotionConfig", config);

        // 执行测试
        PromotionInfoDTO result = activity.getMultiProductAct(goodItem, request, context);

        // 验证结果
        assertNotNull(result);
        assertEquals(config.getRangeReduceRule(), result.getRule());
    }
}
