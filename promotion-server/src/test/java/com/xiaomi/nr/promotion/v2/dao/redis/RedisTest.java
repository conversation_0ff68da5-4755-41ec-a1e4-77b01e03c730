package com.xiaomi.nr.promotion.v2.dao.redis;

import com.xiaomi.mit.unittest.field.autoconfig.SetupBean;
import com.xiaomi.mit.unittest.redis.SetupRedis;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.NrActivityPo;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.ValueOperations;

/**
 * @author: zhangliwei6
 * @date: 2025/2/25 17:23
 * @description:
 */
@Slf4j
@SetupRedis(value = "redis/firstTest/testString.json", templateName = "stringActCacheRedisTemplate")
public class RedisTest extends BaseTestV2 {

    private NrActivityPo activityPo;

    @Autowired
    private RedisTemplate stringActCacheRedisTemplate;

    @Test
    @SetupBean("field/firstTest/activityPo.json")
    public void test_string() {
        ValueOperations valueOperations = stringActCacheRedisTemplate.opsForValue();
        Assertions.assertEquals(GsonUtil.toJson(activityPo), valueOperations.get("test:string1"));
        Assertions.assertEquals("hello", valueOperations.get("test:string2"));
    }

    @Test
    @SetupRedis(value = "redis/firstTest/testSet.json", templateName = "stringActCacheRedisTemplate")
    public void test_set() {
        SetOperations setOperations = stringActCacheRedisTemplate.opsForSet();
        Assertions.assertTrue(setOperations.isMember("test:set", "1"));
        Assertions.assertTrue(setOperations.isMember("test:set", "2"));
    }

    @Test
    @SetupRedis(value = "redis/firstTest/testHash.json", templateName = "stringActCacheRedisTemplate")
    public void test_hash() {
        String value = (String) stringActCacheRedisTemplate.opsForHash().get("test:hash", "version");
        Assertions.assertEquals("1", value);
    }
}
