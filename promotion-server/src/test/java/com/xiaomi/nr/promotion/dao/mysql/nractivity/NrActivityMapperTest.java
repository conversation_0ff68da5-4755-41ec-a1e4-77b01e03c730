package com.xiaomi.nr.promotion.dao.mysql.nractivity;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.NrActivityPo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： Pancras
 * @date： 2024/9/19
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
public class NrActivityMapperTest extends BaseTest {
    
    @Autowired
    private NrActivityMapper nrActivityMapper;
    
    @Test
    public void test() {
        List<NrActivityPo> nrActivityPos = nrActivityMapper.queryListByType(15, System.currentTimeMillis() / 1000);
        System.out.println(nrActivityPos);
    }

    @Test
    public void testGetAllInvalidInstallmentAct() {
        List<NrActivityPo> nrActivityPos = nrActivityMapper.queryValidListByType(15, System.currentTimeMillis() / 1000);
        System.out.println(nrActivityPos);
    }
    
}
