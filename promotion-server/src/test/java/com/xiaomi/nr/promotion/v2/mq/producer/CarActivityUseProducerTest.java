package com.xiaomi.nr.promotion.v2.mq.producer;

import com.xiaomi.nr.promotion.mq.producer.CarActivityUseProducer;
import com.xiaomi.nr.promotion.mq.producer.entity.CarActivityUseMessage;
import com.xiaomi.nr.promotion.mq.producer.entity.Header;
import com.xiaomi.nr.promotion.mq.producer.template.CarActivityUseRocketMQTemplate;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import org.apache.rocketmq.client.producer.SendResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @author: zhangliwei6
 * @date: 2025/3/3 15:39
 * @description: CarActivityUseProducer测试类
 */
//@ExtendWith(MockitoExtension.class)
public class CarActivityUseProducerTest extends BaseTestV2 {

    @Mock
    private CarActivityUseRocketMQTemplate carActivityUseRocketMQTemplate;

    @InjectMocks
    private CarActivityUseProducer carActivityUseProducer;

    private final String topic = "test_topic";
    private final String tag = "test_tag";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(carActivityUseProducer, "topic", topic);
        ReflectionTestUtils.setField(carActivityUseProducer, "tag", tag);
        carActivityUseProducer.setRocketMqTemplate(carActivityUseRocketMQTemplate);
    }

    @Test
    void test_sendMessage_success() {
        // Given
        CarActivityUseMessage message = CarActivityUseMessage.builder()
                .header(Header.builder()
                        .version("1.0")
                        .profile("test")
                        .build())
                .body(CarActivityUseMessage.Body.builder()
                        .unikey("test-key")
                        .promotionId(1L)
                        .userId(1L)
                        .bizPlatform(3)
                        .orderId(1L)
                        .joinTimes(1)
                        .useTime(System.currentTimeMillis())
                        .desc("test")
                        .changeTimeMillis(System.currentTimeMillis())
                        .orderRefund(false)
                        .build())
                .build();

        SendResult mockResult = new SendResult();
        when(carActivityUseRocketMQTemplate.syncSend(anyString(), any(Object.class))).thenReturn(mockResult);

        // When
        carActivityUseProducer.sendMessage(message);

        // Then
        verify(carActivityUseRocketMQTemplate).syncSend(eq(topic + ":" + tag), any(Object.class));
    }

    @Test
    void test_sendMessage_retryOnFailure() {
        // Given
        CarActivityUseMessage message = CarActivityUseMessage.builder()
                .header(Header.builder()
                        .version("1.0")
                        .profile("test")
                        .build())
                .body(CarActivityUseMessage.Body.builder()
                        .unikey("test-key")
                        .promotionId(1L)
                        .userId(1L)
                        .bizPlatform(3)
                        .orderId(1L)
                        .joinTimes(1)
                        .useTime(System.currentTimeMillis())
                        .desc("test")
                        .changeTimeMillis(System.currentTimeMillis())
                        .orderRefund(false)
                        .build())
                .build();

        // First call throws exception, second call succeeds
        SendResult mockResult = new SendResult();
        when(carActivityUseRocketMQTemplate.syncSend(anyString(), any(Object.class)))
                .thenThrow(new RuntimeException("Send failed"))
                .thenReturn(mockResult);

        // When
        carActivityUseProducer.sendMessage(message);

        // Then
        verify(carActivityUseRocketMQTemplate, times(2)).syncSend(eq(topic + ":" + tag), any(Object.class));
    }
}
