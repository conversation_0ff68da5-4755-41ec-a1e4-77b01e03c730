package com.xiaomi.nr.promotion.service;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.domain.ecard.service.common.EcardInfoService;
import com.xiaomi.nr.promotion.domain.ecard.model.UserEcard;
import com.xiaomi.nr.promotion.util.GsonUtil;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * 2021/5/26 9:53 上午
 */
public class EcardInfoServiceTest extends BaseTest {
    @Autowired
    EcardInfoService ecardInfoService;

    @Test
    public void testGetEcardsInfo() {
        Long uid = 3150041197L;
        List<String> cardIds = Arrays.asList("20000520928686", "20030138228655");
        Long clientId = 180100041103L;
        ListenableFuture<List<UserEcard>> future = ecardInfoService.getEcardsInfoAsync(uid, cardIds, clientId);
        try {
            List<UserEcard> list = future.get(1, TimeUnit.MINUTES);
            Assert.assertTrue(future.isDone());
            Assert.assertNotNull(list);
            System.out.println("jjjjjjj:" + list);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testGet() throws ExecutionException, InterruptedException, TimeoutException {
        Long uid = 3150063523L;
        ListenableFuture<List<UserEcard>> future = ecardInfoService.getBeijingCouponEcardsInfoAsync(uid);
        List<UserEcard> list = future.get(1, TimeUnit.MINUTES);
        Assert.assertTrue(future.isDone());
        Assert.assertNotNull(list);
        System.out.println(GsonUtil.toJson(list));
    }
}
