package com.xiaomi.nr.promotion.v2.componet.action.carmaintenance;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.MaintenanceInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.componet.action.carmaintenance.MaintenanceItemFreeAction;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.CarIdentityTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.model.promotionconfig.MaintenanceItemFreePromotionConfig;
import com.xiaomi.nr.promotion.resource.ResourceProviderFactory;
import com.xiaomi.nr.promotion.resource.model.ResourceType;
import com.xiaomi.nr.promotion.resource.provider.UserActivityCountProvider;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

//@ExtendWith(MockitoExtension.class)
public class MaintenanceItemFreeActionTest extends BaseTestV2 {

    @InjectMocks
    private MaintenanceItemFreeAction action;

    @Mock
    private ActivityTool activityTool;

    @Mock
    private ResourceProviderFactory resourceProviderFactory;

    private MaintenanceItemFreePromotionConfig config;
    private CheckoutPromotionRequest request;
    private LocalContext context;

    @BeforeEach
    public void setUp() {
        // 初始化请求对象
        request = new CheckoutPromotionRequest();
        request.setUserId(123L);
        request.setSourceApi(SourceApi.SUBMIT);
        request.setChannel(BizPlatformEnum.MAINTENANCE_REPAIR.getValue());
        request.setActivityIds(Lists.newArrayList(1L));
        
        // 初始化购物车项
        List<CartItem> cartItems = new ArrayList<>();
        CartItem cartItem = new CartItem();
        cartItem.setSsuId(123L);
        cartItem.setCount(1);
        cartItem.setOriginalCartPrice(1000L);
        cartItem.setItemId("item123");
        
        // 设置维保信息
        MaintenanceInfo maintenanceInfo = new MaintenanceInfo();
        maintenanceInfo.setPayType(1);
        cartItem.setMaintenanceInfo(maintenanceInfo);
        
        cartItems.add(cartItem);
        request.setCartList(cartItems);

        // 初始化上下文
        context = new LocalContext();
        context.setBizPlatform(BizPlatformEnum.MAINTENANCE_REPAIR);
        context.setCarts(cartItems);
        
        // 设置商品索引
        List<GoodsIndex> goodsIndexList = new ArrayList<>();
        GoodsIndex goodsIndex = new GoodsIndex("item123", 0);
        goodsIndexList.add(goodsIndex);
        context.setGoodIndex(goodsIndexList);

        // Mock配置
        config = new MaintenanceItemFreePromotionConfig();
        config.setPromotionId(1L);
        config.setPromotionType(PromotionToolType.MAINTENANCE_ITEM_FREE);
        config.setPromotionPrice(0L);
        config.setMaxReduceAmount(1000L);
        config.setCarIdentityType(CarIdentityTypeEnum.CAR_SHOP_VIP.getCode());
        config.setCarIdentityId("1");
        config.setIdentityJoinLimitNum(3);
        config.setWorkOrderType(Lists.newArrayList(1));
    }

    @Test
    public void test_execute_success() throws BizError {
        // Mock方法
        UserActivityCountProvider userActivityCountProvider = mock(UserActivityCountProvider.class);
        when(resourceProviderFactory.getProvider(ResourceType.USER_JOIN_ACT_NUM)).thenReturn(userActivityCountProvider);
//        VidActivityCountProvider vidActivityCountProvider = mock(VidActivityCountProvider.class);
//        when(resourceProviderFactory.getProvider(ResourceType.VID_JOIN_ACT_LIMIT)).thenReturn(vidActivityCountProvider);

        PromotionInfo promotionInfo = new PromotionInfo();
        when(activityTool.buildCartPromotionInfo(any())).thenReturn(promotionInfo);

        // 执行测试
        action.loadConfig(config);
        action.execute(activityTool, request, context);
        
        // 验证结果
        verify(activityTool, times(1)).buildCartPromotionInfo(any());
    }

    @Test
    public void test_execute_fail() throws BizError {
        // 场景1: 空的商品列表
        context.setGoodIndex(new ArrayList<>());
        request.setCartList(new ArrayList<>());

        // Mock方法
        UserActivityCountProvider userActivityCountProvider = mock(UserActivityCountProvider.class);
        when(resourceProviderFactory.getProvider(ResourceType.USER_JOIN_ACT_NUM)).thenReturn(userActivityCountProvider);
//        VidActivityCountProvider vidActivityCountProvider = mock(VidActivityCountProvider.class);
//        when(resourceProviderFactory.getProvider(ResourceType.VID_JOIN_ACT_LIMIT)).thenReturn(vidActivityCountProvider);

        PromotionInfo promotionInfo = new PromotionInfo();
        when(activityTool.buildCartPromotionInfo(any())).thenReturn(promotionInfo);

        action.loadConfig(config);
        action.execute(activityTool, request, context);
        
        // 场景2: 商品不满足条件
        List<GoodsIndex> goodsIndexList = Lists.newArrayList(new GoodsIndex("1", 0));
        context.setGoodIndex(goodsIndexList);
        
        List<CartItem> cartItems = new ArrayList<>();
        CartItem item = new CartItem();
        item.setItemId("1");
        item.setSsuId(1L);
        item.setOriginalCartPrice(0L); // 无效价格
        item.setCount(1);
        MaintenanceInfo maintenanceInfo = new MaintenanceInfo();
        maintenanceInfo.setPayType(2);
        item.setMaintenanceInfo(maintenanceInfo);
        cartItems.add(item);
        
        request.setCartList(cartItems);
        action.execute(activityTool, request, context);
        
        // 场景3: 活动ID不匹配
        request.setActivityIds(Lists.newArrayList(2L));
        action.execute(activityTool, request, context);
    }

    @Test
    public void test_loadConfig_success() {
        action.loadConfig(config);
    }

    @Test
    public void test_loadConfig_fail() {
        // 场景1: 配置为null
        action.loadConfig(null);
        Assertions.assertNull(ReflectionTestUtils.getField(action, "promotionId"));
    }
}
