package com.xiaomi.nr.promotion;

import com.xiaomi.mit.unittest.configuration.UTAutoConfiguration;
import com.xiaomi.nr.promotion.bootstrap.PromotionBootstrap;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 测试类基类
 *
 * <AUTHOR>
 * @date 2021/4/26
 */
@ActiveProfiles("ut")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {UTAutoConfiguration.class, PromotionBootstrap.class})
public abstract class BaseTest {

}
