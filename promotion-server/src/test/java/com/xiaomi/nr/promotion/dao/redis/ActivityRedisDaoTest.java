package com.xiaomi.nr.promotion.dao.redis;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.entity.redis.ActivityInfo;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * 活动缓存测试
 *
 * <AUTHOR>
 * @date 2021/5/18
 */
public class ActivityRedisDaoTest extends BaseTest {
    @Autowired
    private ActivityRedisDao activityRedisDao;

    @Test
    public void testMultiGetActivityById() {
        List<ActivityInfo> activityInfoList = activityRedisDao.multiGetActivityByIdList(Lists.newArrayList(13793L));
        System.out.println(activityInfoList);
        Assert.assertNotNull(activityInfoList);
        Assert.assertEquals(13793L, activityInfoList.get(0).getBasetype().getId().longValue());
    }

    @Test
    public void testListActivityIdByOrgCode() {
        List<Long> idList = activityRedisDao.listActivityIdByOrgCode("JM11458");
        System.out.println(idList);
        Assert.assertNotNull(idList);
    }

    @Test
    public void testListActivityIdByClientId() {
        List<Long> idList = activityRedisDao.listActivityIdByClientId(180100041075L);
        System.out.println(idList);
        Assert.assertNotNull(idList);
    }

    @Test
    public void testGetActUtypePersonDayLimitNum() {
        Integer count = activityRedisDao.getActUtypePersonDayLimitNum(13855L, "18813104649", 3150075477L, Instant.now().toEpochMilli());
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(0, count.intValue());
    }

    @Test
    public void testGetActUtypePersonLimitNum() {
        Integer count = activityRedisDao.getActUtypePersonLimitNum(13855L, "18813104649", 3150075477L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(0, count.intValue());
    }

    @Test
    public void testGetActPersonDayLimitNum() {
        Integer count = activityRedisDao.getActPersonDayLimitNum(13855L, 3150205283L, 3150075477L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(0, count.intValue());
    }

    @Test
    public void testGetActPersonLimitNum() {
        Integer count = activityRedisDao.getActPersonLimitNum(13855L, 3150205283L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(0, count.intValue());
    }

    @Test
    public void testGetActAllStoreDayLimitNum() {
        Integer count = activityRedisDao.getActAllStoreDayLimitNum(13855L, Instant.now().toEpochMilli());
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(0, count.intValue());
    }

    @Test
    public void testGetActAllStoreLimitNum() {
        Integer count = activityRedisDao.getActAllStoreLimitNum(13855L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(2, count.intValue());
    }

    @Test
    public void testGetActStoreDayLimitNum() {
        Integer count = activityRedisDao.getActStoreDayLimitNum(13855L, "MI0101", Instant.now().toEpochMilli());
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(0, count.intValue());
    }

    @Test
    public void testGetOnsaleUserLimitNum() {
        Integer count = activityRedisDao.getOnsaleUserLimitNum(13663L, 3150044249L, "1204900008");
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(1, count.intValue());
    }

    @Test
    public void testGetOnsaleMobileUserLimitNum() {
        Integer count = activityRedisDao.getOnsaleMobileUserLimitNum(13856L, 15255672345L, "15589");
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(0, count.intValue());
    }

    @Test
    public void testGetOnsaleStoreDayLimitNum() {
        Integer count = activityRedisDao.getOnsaleStoreDayLimitNum(13856L, "MI0101", Instant.now().toEpochMilli(), "15589");
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(0, count.intValue());
    }

    @Test
    public void testGetOnsaleAllStoreDayLimitNum() {
        Integer count = activityRedisDao.getOnsaleAllStoreDayLimitNum(13856L, 15756782345L, "15592");
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(0, count.intValue());
    }

    @Test
    public void testGetOnsaleStoreLimitNum() {
        Integer count = activityRedisDao.getOnsaleStoreLimitNum(13848L, "JM11202", "15592");
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(1, count.intValue());
    }

    @Test
    public void testGetOnsaleAllStoreLimitNum() {
        Integer count = activityRedisDao.getOnsaleAllStoreLimitNum(13848L, "15592");
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(10, count.intValue());
    }

    @Test
    public void testGetActBuyGiftLimitNum() {
        Integer count = activityRedisDao.getActBuyGiftLimitNum(13848L, "15592", 1L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(0, count.intValue());
    }

    @Test
    public void testGetActStoreLimitNum() {
        Integer count = activityRedisDao.getActStoreLimitNum(13839L, "MI0101");
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testGetActStatusLimitNum() {
        Integer count = activityRedisDao.getActStatusLimitNum(13839L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testIsExistUserActDailyRecord() {
        Boolean exist = activityRedisDao.isExistUserActDailyRecord(990398L, 3150070208L, Instant.now().toEpochMilli());
        System.out.println(exist);
        Assert.assertNotNull(exist);
        Assert.assertTrue(exist);
    }

    @Test
    public void testIsExistUserActTotalRecord() {
        Boolean exist = activityRedisDao.isExistUserActTotalRecord(13839L, 15255672345L);
        System.out.println(exist);
        Assert.assertNotNull(exist);
        Assert.assertFalse(exist);
    }

    @Test
    public void testIncrActStatusLimitNum() {
        Long num = activityRedisDao.incrActStatusLimitNum(13839L, 1L, 2, 10L);
        System.out.println(num);
        Assert.assertNotNull(num);
        Assert.assertNotEquals(0, num.longValue());
    }

    @Test
    public void testDecrActStatusLimitNum() {
        Long num = activityRedisDao.decrActStatusLimitNum(13839L, 1L, 1);
        System.out.println(num);
        Assert.assertNotNull(num);
        Assert.assertNotEquals(0, num.longValue());
    }

    @Test
    public void testSetUserActDailyRecord() throws BizError {
        activityRedisDao.setUserActDailyRecord(13839L, 15255672345L, Instant.now().toEpochMilli(),
                187L, Instant.now().toEpochMilli() + 1000000L);
    }

    @Test
    public void testDelUserActDailyRecord() {
        activityRedisDao.delUserActDailyRecord(13839L, 15255672345L, Instant.now().toEpochMilli());
    }

    @Test
    public void testSetUserActTotalRecord() throws BizError {
        activityRedisDao.setUserActTotalRecord(13839L, 15255672345L,
                187L, Instant.now().toEpochMilli() + 1000000L);
    }

    @Test
    public void testDelUserActTotalRecord() {
        activityRedisDao.delUserActTotalRecord(13839L, 15255672345L);
    }

    @Test
    public void testIncrActPersonDayLimitNum() {
        Long count = activityRedisDao.incrActUtypePersonDayLimitNum(13855L, "18813104649", 3150075477L,
                Instant.now().toEpochMilli(), 10, 10L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testDecrActPersonDayLimitNum() {
        Long count = activityRedisDao.decrActUtypePersonDayLimitNum(13855L, "18813104649", 3150075477L,
                Instant.now().toEpochMilli(), 10);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testIncrActPersonLimitNum() {
        Long count = activityRedisDao.incrActPersonLimitNum(13855L, 3150205283L, 10, 10L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testDecrActPersonLimitNum() {
        Long count = activityRedisDao.decrActPersonLimitNum(13855L, 3150205283L, 10);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testIncrActUtypePersonDayLimitNum() {
        Long count = activityRedisDao.incrActUtypePersonDayLimitNum(13855L, "18813104649", 3150075477L, Instant.now().toEpochMilli(), 10, 10L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testDecrActUtypePersonDayLimitNum() {
        Long count = activityRedisDao.decrActUtypePersonDayLimitNum(13855L, "18813104649", 3150075477L, Instant.now().toEpochMilli(), 10);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testIncrActUtypePersonLimitNum() {
        Long count = activityRedisDao.incrActUtypePersonLimitNum(13855L, "18813104649", 3150075477L, 10, 10L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testDecrActUtypePersonLimitNum() {
        Long count = activityRedisDao.decrActUtypePersonLimitNum(13855L, "18813104649", 3150075477L, 10);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testIncrActAllStoreDayLimitNum() {
        Long count = activityRedisDao.incrActAllStoreDayLimitNum(13855L, Instant.now().toEpochMilli(), 10, 10L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testDecrActAllStoreDayLimitNum() {
        Long count = activityRedisDao.decrActAllStoreDayLimitNum(13855L, Instant.now().toEpochMilli(), 10);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testIncrActAllStoreLimitNum() {
        Long count = activityRedisDao.incrActAllStoreLimitNum(13855L, 10, 10L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testDecrActAllStoreLimitNum() {
        Long count = activityRedisDao.decrActAllStoreLimitNum(13855L, 10);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testIncrActStoreDayLimitNum() {
        Long count = activityRedisDao.incrActStoreDayLimitNum(13855L, "MI0101", Instant.now().toEpochMilli(), 10, 10L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testDecrActStoreDayLimitNum() {
        Long count = activityRedisDao.decrActStoreDayLimitNum(13855L, "MI0101", Instant.now().toEpochMilli(), 10);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testIncrActStoreLimitNum() {
        Long count = activityRedisDao.incrActStoreLimitNum(13855L, "MI0101", 10, 10L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testDecrActStoreLimitNum() {
        Long count = activityRedisDao.decrActStoreLimitNum(13855L, "MI0101", 10);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testIncrOnsaleUserLimitNum() {
        Long count = activityRedisDao.incrOnsaleUserLimitNum(25709L, 1275041031L, "9833", 2, 0L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testDecrOnsaleUserLimitNum() {
        Long count = activityRedisDao.decrOnsaleUserLimitNum(13663L, 3150044249L, "1204900008", 10);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testIncrOnsaleMobileUserLimitNum() {
        Long count = activityRedisDao.incrOnsaleMobileUserLimitNum(13856L, 15255672345L, "15589", 10, 10L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testDecrOnsaleMobileUserLimitNum() {
        Long count = activityRedisDao.decrOnsaleMobileUserLimitNum(13856L, 15255672345L, "15589", 10);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testIncrOnsaleStoreDayLimitNum() {
        Long count = activityRedisDao.incrOnsaleStoreDayLimitNum(13856L, "MI0101", Instant.now().toEpochMilli(), "15589", 10, 10L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testDecrOnsaleStoreDayLimitNum() {
        Long count = activityRedisDao.decrOnsaleStoreDayLimitNum(13856L, "MI0101", Instant.now().toEpochMilli(), "15589", 10);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testIncrOnsaleAllStoreDayLimitNum() {
        Long count = activityRedisDao.incrOnsaleAllStoreDayLimitNum(13856L, Instant.now().toEpochMilli(), "15589", 10, 0L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testDecrOnsaleAllStoreDayLimitNum() {
        Long count = activityRedisDao.decrOnsaleAllStoreDayLimitNum(13856L, Instant.now().toEpochMilli(), "15589", 10);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testIncrOnsaleStoreLimitNum() {
        Long count = activityRedisDao.incrOnsaleStoreLimitNum(990235L, "MI0101", "15589", 10, 0L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testDecrOnsaleStoreLimitNum() {
        Long count = activityRedisDao.decrOnsaleStoreLimitNum(13856L, "MI0101", "15589", 10);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testIncrOnsaleAllStoreLimitNum() {
        Long count = activityRedisDao.incrOnsaleAllStoreLimitNum(13856L, "15589", 10, 10L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testDecrOnsaleAllStoreLimitNum() {
        Long count = activityRedisDao.decrOnsaleAllStoreLimitNum(13856L, "15589", 10);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertNotEquals(0, count.intValue());
    }

    @Test
    public void testGetActBargainLimitNum() {
        Integer count = activityRedisDao.getActBargainLimitNum(13848L, "15592", 1L);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(0, count.intValue());
    }

    @Test
    public void testIncrActBargainLimitNum() {
        Long count = activityRedisDao.incrActBargainLimitNum(13848L, "15592", 1L, 1);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(0, count.intValue());
    }

    @Test
    public void testDecrActBargainLimitNum() {
        Long count = activityRedisDao.decrActBargainLimitNum(13848L, "15592", 1L, 1);
        System.out.println(count);
        Assert.assertNotNull(count);
        Assert.assertEquals(0, count.intValue());
    }

    @Test
    public void testBatch() throws BizError {

        List<ActUserLimitPo> actStockPoList = new ArrayList<>();
        ActUserLimitPo actStockPo1 = new ActUserLimitPo();
        actStockPo1.setLockKey("key1");
        actStockPo1.setLimit(1);
        actStockPo1.setTarget(1);
        actStockPoList.add(actStockPo1);

        ActUserLimitPo actStockPo2 = new ActUserLimitPo();
        actStockPo2.setLockKey("key2");
        actStockPo2.setLimit(2);
        actStockPo2.setTarget(1);
        actStockPoList.add(actStockPo2);

        activityRedisDao.batchConsumeSubsidyStockV2(actStockPoList);


    }
}