package com.xiaomi.nr.promotion.rpc.order;

import com.xiaomi.nr.order.api.dto.request.order.OrderDto;
import com.xiaomi.nr.order.common.enums.trade.TradeFromEnum;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/5
 */
public class OrderQueryServiceProxyTest extends BaseTest {

    @Autowired
    private OrderQueryServiceProxy orderQueryServiceProxy;

    @Test
    public void testGetOrderList() throws BizError {
        List<OrderDto> orderDtoList = orderQueryServiceProxy.getOrderList(1235091008971113L, 3150002895L, TradeFromEnum.STORE);
        System.out.println(GsonUtil.toJson(orderDtoList));
    }
}