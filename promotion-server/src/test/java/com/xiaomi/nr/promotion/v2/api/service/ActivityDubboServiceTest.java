package com.xiaomi.nr.promotion.v2.api.service;

import com.google.common.collect.Lists;
import com.xiaomi.nr.md.promotion.admin.api.constant.PromotionTypeEnum;
import com.xiaomi.nr.promotion.activity.pool.CarPromotionInstancePool;
import com.xiaomi.nr.promotion.activity.pool.PromotionInstancePool;
import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.api.dto.model.carshop.Region;
import com.xiaomi.nr.promotion.api.service.ActivityDubboService;
import com.xiaomi.nr.promotion.dao.mysql.promotion.V3ActivityOnlineMapper;
import com.xiaomi.nr.promotion.dao.redis.GoodsRedisDao;
import com.xiaomi.nr.promotion.domain.activity.service.common.ProductActivityService;
import com.xiaomi.nr.promotion.entity.redis.GoodsHierarchy;
import com.xiaomi.nr.promotion.util.RedisClusterAutoSwitchHelper;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ValueOperations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;

@Slf4j
public class ActivityDubboServiceTest extends BaseTestV2 {

    @Autowired
    private ActivityDubboService activityDubboService;
    @Autowired
    private ProductActivityService productActivityService;
    @Autowired
    private GoodsRedisDao goodsRedisDao;
    @Autowired
    private RedisClusterAutoSwitchHelper switchHelper;
    @Autowired
    private PromotionInstancePool promotionInstancePool;
    @Autowired
    private V3ActivityOnlineMapper v3ActivityOnlineMapper;
    @Autowired
    private CarPromotionInstancePool carPromotionInstancePool;

    @BeforeEach
    public void before(){

        ValueOperations<String, String> operations = switchHelper.getOperations();
        operations.set("pulse_activitycache_clientid_180100031024", "21535555,21539828,21545226,21546197");
    }


    /**
     * 产品站批量获取可参加进行中的活动
     * 整车销售-置换补贴
     */
    @Test
    public void test_getmultiproductgoodsact_carVehicle_29_success(){

        carPromotionInstancePool.rebuildCacheTask();

        MultiProductGoodsActRequest request = new MultiProductGoodsActRequest();
        request.setChannel(ChannelEnum.CAR_VEHICLE.getValue());
        List<MultiGoodItem> goodsList = new ArrayList<>();
        MultiGoodItem item1 = new MultiGoodItem();
        item1.setSsuId(600000658L);
        MultiGoodItem item2 = new MultiGoodItem();
        item2.setSsuId(600000672L);
        MultiGoodItem item3 = new MultiGoodItem();
        item3.setSsuId(600002250L);
        MultiGoodItem item4 = new MultiGoodItem();
        item4.setSsuId(600002274L);
        MultiGoodItem item5 = new MultiGoodItem();
        item5.setSsuId(600002292L);
        MultiGoodItem item6 = new MultiGoodItem();
        item6.setSsuId(600003289L);
        goodsList.add(item2);
        goodsList.add(item1);
        goodsList.add(item3);
        goodsList.add(item4);
        goodsList.add(item5);
        goodsList.add(item6);
        request.setGoodsList(goodsList);
        request.setPromotionTypeList(Lists.newArrayList(PromotionTypeEnum.EXCHANGE_SUBSIDY.code));
        Result<MultiProductGoodsActResponse> result = activityDubboService.getMultiProductGoodsAct(request);
        Assertions.assertTrue(result.getData().getPromotionIndexMap().size() == 6 && result.getData().getPromotionInfoMap().size() == 1 && result.getData().getPromotionPriceDTOMap().isEmpty());
    }


    /**
     * 产品站批量获取可参加进行中的活动
     * 整车销售-选装基金
     */
    @Test
    public void test_getmultiproductgoodsact_carVehicle_28_success(){

        carPromotionInstancePool.rebuildCacheTask();

        MultiProductGoodsActRequest request = new MultiProductGoodsActRequest();
        request.setChannel(ChannelEnum.CAR_VEHICLE.getValue());
        List<MultiGoodItem> goodsList = new ArrayList<>();
        MultiGoodItem item1 = new MultiGoodItem();
        item1.setSsuId(600002303L);
        MultiGoodItem item2 = new MultiGoodItem();
        item2.setSsuId(600002305L);
        goodsList.add(item2);
        goodsList.add(item1);
        request.setGoodsList(goodsList);
        request.setPromotionTypeList(Lists.newArrayList(PromotionTypeEnum.RANGE_REDUCE.code));
        Result<MultiProductGoodsActResponse> result = activityDubboService.getMultiProductGoodsAct(request);
        Assertions.assertTrue(result.getData().getPromotionIndexMap().size() == 2 && result.getData().getPromotionInfoMap().size() == 2 && result.getData().getPromotionPriceDTOMap().isEmpty());
    }

    /**
     * 产品站批量获取可参加进行中的活动
     * 车商城销售-买赠
     */
    @Test
    public void test_getmultiproductgoodsact_carShop_21_success(){

        carPromotionInstancePool.rebuildCacheTask();

        MultiProductGoodsActRequest request = new MultiProductGoodsActRequest();
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        List<MultiGoodItem> goodsList = new ArrayList<>();
        MultiGoodItem item1 = new MultiGoodItem();
        item1.setSsuId(2221000456L);
        item1.setMarketPrice(1000000L);
        goodsList.add(item1);
        request.setGoodsList(goodsList);
        request.setPromotionTypeList(Lists.newArrayList(PromotionTypeEnum.BUY_GIFT.code));
        request.setVipLevel(100000);
        Result<MultiProductGoodsActResponse> result = activityDubboService.getMultiProductGoodsAct(request);
        Assertions.assertEquals(1, result.getData().getPromotionIndexMap().size());
    }

    /**
     * 产品站批量获取可参加进行中的活动
     * 车商城销售-会员折扣
     */
    @Test
    public void test_getMultiProductGoodsAct_carShop_55_success(){

        carPromotionInstancePool.rebuildCacheTask();

        MultiProductGoodsActRequest request = new MultiProductGoodsActRequest();
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        List<MultiGoodItem> goodsList = new ArrayList<>();
        MultiGoodItem item2 = new MultiGoodItem();
        item2.setSsuId(2145000007L);
        item2.setMarketPrice(1000000L);
        goodsList.add(item2);
        request.setGoodsList(goodsList);
        request.setPromotionTypeList(Lists.newArrayList(PromotionTypeEnum.CAR_SHOP_VIP.code));
        Region region = new Region();
        region.setProvince(0);
        region.setCity(0);
        region.setArea(0);
        region.setDistrict(0);
        request.setRegion(region);
        request.setVipLevel(100000);
        Result<MultiProductGoodsActResponse> result = activityDubboService.getMultiProductGoodsAct(request);
        Assertions.assertTrue(result.getData().getPromotionIndexMap().size() == 1 && result.getData().getPromotionInfoMap().size() == 1 && result.getData().getPromotionPriceDTOMap().size() == 1);
    }


    @Test
    public void test_getActivitysByGoods2_success(){
        promotionInstancePool.rebuildCacheTask();

        GetActivitysByGoodsRequest request = new GetActivitysByGoodsRequest();
        request.setSkuPackageList(List.of("21535555", "21539828", "21545226", "21546197"));
        request.setActivityType(70);
        request.setChannel(13);
        request.setClientId(180100031024L);
        Result<GetActivitysByGoodsResponse> result = activityDubboService.getActivitysByGoods(request);
        Assertions.assertEquals(4, result.getData().getActivitys().size());
    }


    @Test
    public void test_getActivitysByGoods_success() {
        GetActivitysByGoodsRequest request = new GetActivitysByGoodsRequest();
        request.setSkuPackageList(List.of("20433","20466","400000106"));
        request.setActivityType(70);
        request.setChannel(1);
        Result<GetActivitysByGoodsResponse> result = activityDubboService.getActivitysByGoods(request);
        Assertions.assertEquals(3, result.getData().getActivitys().size());
    }


    @Test
    public void test_getProductActPriceV2_success() {

        GetProductActPriceV2Request request = new GetProductActPriceV2Request();
        GoodsDto goodsDto = new GoodsDto();
        goodsDto.setId(20433L);
        GoodsDto goodsDto2 = new GoodsDto();
        goodsDto2.setId(20466L);
        GoodsDto goodsDto3 = new GoodsDto();
        goodsDto3.setId(400000106L);
        request.setSkuPackageList(List.of(goodsDto, goodsDto2, goodsDto3));
        request.setChannel(1);
        Result<GetProductActPriceV2Response> result = activityDubboService.getProductActPriceV2(request);
        Assertions.assertEquals(0, result.getCode());
    }


    @Test
    public void test_getProductActJoinInfo_success() {
        ProductActJoinInfoRequest req = new ProductActJoinInfoRequest();
        req.setVid("LKBQ1KFYHKW3CCCU2");
        req.setMid(3150426120L);
        req.setPromotionIds(Lists.newArrayList(21532208L, 21533918L, 21533916L));
        Result<ProductActJoinInfoResponse> result = activityDubboService.getProductActJoinInfo(req);
        Assertions.assertEquals(3, result.getData().getPromotionJoinInfoMap().size());
    }

    @Test
    public void test_checkProductsAct_success() {
        CheckProductsActRequest request = new CheckProductsActRequest();
        request.setOrgCode("MI0601");
        CheckGoodsItem checkGoodsItem = new CheckGoodsItem();
        checkGoodsItem.setSku(20855L);
        request.setMainGoodsList(Arrays.asList(checkGoodsItem));
        CheckGoodsItem checkGoodsItem2 = new CheckGoodsItem();
        checkGoodsItem2.setSku(16173L);
        checkGoodsItem2.setGroupId(1L);
        request.setAttachedGoodsList(Arrays.asList(checkGoodsItem2));
        request.setPromotionId(9989203L);
        Result<CheckProductsActResponse> result = activityDubboService.checkProductsAct(request);
        Assertions.assertEquals(400030111, result.getCode());
    }

    @Test
    public void test_getStoreGoodsActPrice_success() {
        GetStoreGoodsActPriceRequest request = new GetStoreGoodsActPriceRequest();
        request.setOrgCode("MI0101");

        Result<GetStoreGoodsActPriceResponse> result = activityDubboService.getStoreGoodsActPrice(request);
        Assertions.assertEquals(0, result.getCode());
    }

    @Test
    public void test_getStoreActPrice_success() {
        GetStoreActPriceRequest request = new GetStoreActPriceRequest();
        request.setOrgCode("MI0101");
        request.setSkuPackageList(Arrays.asList("19320"));

        Result<GetStoreActPriceResponse> result = activityDubboService.getStoreActPrice(request);
        Assert.assertEquals(0, result.getCode());
    }

    @Test
    public void test_getActivityAreaDetail_success() {
        GetActivityAreaDetailRequest request = new GetActivityAreaDetailRequest();
        request.setActivityId(9989927L);
        request.setProvinceId(2);
        request.setCityId(36);
        request.setDistrictId(384);
        request.setAreaId(384010);

        Result<GetActivityAreaDetailResponse> response = activityDubboService.getActivityAreaDetail(request);
        Assert.assertNotNull(response);
    }

    @Test
    public void test_batchGetActivityDetail_success() {
        BatchGetActivityDetailRequest request = new BatchGetActivityDetailRequest();
        request.setOrgCode("MI0101");
        request.setActivityIds(Arrays.asList(21330952L, 21330262L));

        Result<BatchGetActivityDetailResponse> response = activityDubboService.batchGetActivityDetail(request);
        Assert.assertNotNull(response);
    }

    @Test
    public void test_getActivityDetail_success() {
        GetActivityDetailRequest request = new GetActivityDetailRequest();
        request.setActivityId(9989927L);

        Result<GetActivityDetailResponse> response = activityDubboService.getActivityDetail(request);
        Assert.assertNotNull(response);
    }
    @Test
    public void test_getProductActV2_Store() {
        ProductActGoodsItem g1 = new ProductActGoodsItem();
        g1.setId(15653L);
        g1.setLevel("sku");
        g1.setSalePrice(399900L);
        g1.setSaleMode("common");
        g1.setBusinessType(1);
        g1.setVirtual(false);

        ProductActGoodsItem g2 = new ProductActGoodsItem();
        g2.setId(1586L);
        g2.setLevel("sku");
        g2.setSalePrice(399900L);
        g2.setSaleMode("common");
        g2.setBusinessType(1);
        g2.setVirtual(false);

        ProductActGoodsItem g3 = new ProductActGoodsItem();
        g3.setId(19331L);
        g3.setLevel("sku");
        g3.setSalePrice(399900L);
        g3.setSaleMode("common");
        g3.setBusinessType(1);
        g3.setVirtual(false);

        ProductActGoodsItem g4 = new ProductActGoodsItem();
        g4.setId(16909L);
        g4.setLevel("sku");
        g4.setSalePrice(399900L);
        g4.setSaleMode("common");
        g4.setBusinessType(1);
        g4.setVirtual(false);


        List<ProductActGoodsItem> goodsList = new ArrayList<>();
        goodsList.add(g1);
        goodsList.add(g2);
        goodsList.add(g3);
        goodsList.add(g4);

        GetProductActV2Request request = new GetProductActV2Request();
        request.setClientId(180100031052L);
        request.setUserId(0L);
        request.setSaleSource("common");
        request.setOrgCode("MI0101");
        request.setProvinceId(2);
        request.setCityId(36);
        request.setDistrictId(384);
        request.setAreaId(384010);
        request.setShipmentType(0);
        request.setGoodsList(goodsList);

        Result<GetProductActV2Response> result = activityDubboService.getProductActV2(request);
        Assert.assertEquals(0, result.getCode());
    }

    @Test
    public void test_getProductAct_success() {

        GoodsRedisDao mockGoodsRedisDao = PowerMockito.mock(GoodsRedisDao.class);
        GoodsHierarchy goodsHierarchy = new GoodsHierarchy();
        Mockito.when(mockGoodsRedisDao.getHierarchyBySku(anyString())).thenReturn(goodsHierarchy);

        Whitebox.setInternalState(productActivityService, "goodsRedisDao", mockGoodsRedisDao);

        GetProductActRequest request = new GetProductActRequest();
        request.setClientId(180100031052L);
        request.setUserId(0L);
        request.setSku("12530");
        request.setUserIsFriend(1);
        request.setSaleSource("common");
        request.setPrice(10000L);
//        request.setOrgCode("MI0101");
        request.setProvinceId(0);
        request.setCityId(0);
        request.setDistrictId(0);
        request.setAreaId(0);

        Result<GetProductActResponse> result = activityDubboService.getProductAct(request);
        Assert.assertNotNull(result.getData().getPrice());
    }
    @Test
    public void test_getProductActPriceDetail_success() {
        GetProductActPriceDetailRequest request = new GetProductActPriceDetailRequest();
        request.setSkuPackageList(Arrays.asList("22748","17389"));

        Result<GetProductActPriceDetailResponse> result = activityDubboService.getProductActPriceDetail(request);
        Assert.assertEquals(0, result.getCode());
    }

    @Test
    public void test_getGoodsActPrice_success() {
        GetGoodsActPriceRequest request = new GetGoodsActPriceRequest();
        request.setSkuPackageList(Arrays.asList("19320","15590", "19509", "9831", "2047"));

        Result<GetGoodsActPriceResponse> result = activityDubboService.getGoodsActPrice(request);
        Assert.assertEquals(0, result.getCode());
    }

    @Test
    public void test_getProductActPrice_success() {
        GetProductActPriceRequest request = new GetProductActPriceRequest();
        request.setOrgCode("JM11514");
        request.setSkuPackageList(Arrays.asList("19320"));

        Result<GetProductActPriceResponse> result = activityDubboService.getProductActPrice(request);
        Assert.assertEquals(0, result.getCode());
    }

    @Test
    public void test_getPreProductGoodsAct_success() {
        GetPreProductGoodsActRequest request = new GetPreProductGoodsActRequest();
//        request.setActivityType(8);
        request.setClientId(180100031052L);
        request.setSkuPackageList(Arrays.asList("21391839","21391836","21372892"));

        Result<GetPreProductGoodsActResponse> result = activityDubboService.getPreProductGoodsAct(request);
        Assert.assertEquals(3, result.getData().getSkuPackageActMap().size());
    }


    @Test
    public void test_integration_getProductGoodsActV2() {
        // 创建请求对象
        GetProductGoodsActRequestV2 request = new GetProductGoodsActRequestV2();
        request.setSkuPackageList(Arrays.asList("12345", "67890"));
        request.setChannel(List.of(31));

        // 调用被测试的方法
        Result<GetProductGoodsActResponse> result = activityDubboService.getProductGoodsActV2(request);

        // 断言结果
        Assertions.assertEquals(0, result.getCode()); // 假设成功时返回码为0
        Assertions.assertNotNull(result.getData()); // 确保返回数据不为空
        Assertions.assertFalse(result.getData().getSkuActMap().isEmpty()); // 确保返回的skuActMap不为空

        // 进一步验证返回数据的属性
        GetProductGoodsActResponse response = result.getData();
        Assertions.assertNotNull(response.getSkuActMap().get("12345")); // 确保特定sku的活动信息存在
        Assertions.assertNotNull(response.getSkuActMap().get("67890")); // 确保特定sku的活动信息存在
    }



}
