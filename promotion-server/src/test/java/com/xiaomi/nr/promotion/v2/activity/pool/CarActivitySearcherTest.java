package com.xiaomi.nr.promotion.v2.activity.pool;

import com.xiaomi.nr.promotion.activity.pool.ActSearchParam;
import com.xiaomi.nr.promotion.activity.pool.CarActivitySearcher;
import com.xiaomi.nr.promotion.activity.pool.CarPromotionInstancePool;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@Slf4j
public class CarActivitySearcherTest extends BaseTestV2 {

    @InjectMocks
    private CarActivitySearcher carActivitySearcher;

    @Mock
    private CarPromotionInstancePool carPromotionInstancePool;

    private ActSearchParam actSearchParam;
    private List<CartItem> cartItems;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 初始化搜索参数
        actSearchParam = new ActSearchParam();
        actSearchParam.setChannel(1);
        
        // 初始化购物车商品
        cartItems = new ArrayList<>();
        CartItem cartItem = new CartItem();
        cartItem.setSsuId(123L);
        cartItems.add(cartItem);
    }

    @Test
    public void test_doSearchIndex_success() {
        // 准备测试数据
        List<ActSearchParam.GoodsInSearch> goodsList = carActivitySearcher.createSearchGoods(cartItems);
        actSearchParam.setGoodsList(goodsList);
        
        // Mock方法
        Set<Long> actIdInGoods = Collections.singleton(1L);
        List<Long> actIdInChannel = Collections.singletonList(1L);
        when(carPromotionInstancePool.getCurrentActIds(anyList())).thenReturn(actIdInGoods);
        when(carPromotionInstancePool.getCurrentChannelActIds(anyList())).thenReturn(actIdInChannel);
        
        // 执行测试
        List<Long> result = carActivitySearcher.doSearchIndex(actSearchParam);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0));
    }

    @Test
    public void test_doSearchIndex_empty_goods() {
        // 准备测试数据
        actSearchParam.setGoodsList(Collections.emptyList());
        
        // 执行测试
        List<Long> result = carActivitySearcher.doSearchIndex(actSearchParam);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void test_doSearchAct_success() {
        // 准备测试数据
        List<Long> actIds = Arrays.asList(1L, 2L);
        List<ActivityTool> expectedTools = Arrays.asList(mock(ActivityTool.class), mock(ActivityTool.class));
        
        // Mock方法
        when(carPromotionInstancePool.getCurrentTools(actIds)).thenReturn(expectedTools);
        
        // 执行测试
        List<ActivityTool> result = carActivitySearcher.doSearchAct(actIds);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(expectedTools.size(), result.size());
    }

    @Test
    public void test_doSearchAct_empty_list() {
        // 准备测试数据
        List<Long> actIds = Collections.emptyList();
        
        // 执行测试
        List<ActivityTool> result = carActivitySearcher.doSearchAct(actIds);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void test_doFilter_success() {
        // 准备测试数据
        List<ActivityTool> activityTools = new ArrayList<>();
        ActivityTool validTool = mock(ActivityTool.class);
        when(validTool.getBizPlatform()).thenReturn(BizPlatformEnum.CAR);
        when(validTool.getUnixStartTime()).thenReturn(1000L);
        when(validTool.getUnixEndTime()).thenReturn(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()) + 9999L);
        activityTools.add(validTool);
        
        // 执行测试
        List<ActivityTool> result = carActivitySearcher.doFilter(actSearchParam, activityTools);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
    }

    @Test
    public void test_doFilter_invalid_platform() {
        // 准备测试数据
        List<ActivityTool> activityTools = new ArrayList<>();
        ActivityTool invalidTool = mock(ActivityTool.class);
        when(invalidTool.getBizPlatform()).thenReturn(BizPlatformEnum.CAR_SHOP);
        activityTools.add(invalidTool);
        
        // 执行测试
        List<ActivityTool> result = carActivitySearcher.doFilter(actSearchParam, activityTools);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void test_getBizPlatform() {
        // 执行测试
        BizPlatformEnum result = carActivitySearcher.getBizPlatform();
        
        // 验证结果
        assertEquals(BizPlatformEnum.CAR, result);
    }

    @Test
    public void test_createSearchGoods_success() {
        // 执行测试
        List<ActSearchParam.GoodsInSearch> result = carActivitySearcher.createSearchGoods(cartItems);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(123L, result.get(0).getSsuId());
    }

    @Test
    public void test_createSearchGoods_empty_list() {
        // 准备测试数据
        List<CartItem> emptyCartItems = Collections.emptyList();
        
        // 执行测试
        List<ActSearchParam.GoodsInSearch> result = carActivitySearcher.createSearchGoods(emptyCartItems);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
