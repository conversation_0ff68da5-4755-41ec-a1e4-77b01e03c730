package com.xiaomi.nr.promotion.dao.mysql.promotionuser;

import cn.hutool.core.date.DateUtil;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.CarActivityCount;
import com.xiaomi.nr.promotion.enums.CarIdentityTypeEnum;
import com.xiaomi.nr.promotion.resource.provider.VidActivityCountProvider;
import com.xiaomi.nr.promotion.util.GsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

public class PromotionCarActivityCountMapperTest extends BaseTest {

    @Autowired
    private PromotionCarActivityCountMapper promotionCarActivityCountMapper;

    @Test
    public void testInsert() {
        CarActivityCount count = new CarActivityCount();
        count.setVid("MOCKTEST1234556");
        count.setPromotionId(21531917L);
        count.setNum(10);
        count.setCreateTime(DateUtil.currentSeconds());
        count.setUpdateTime(DateUtil.currentSeconds());

        VidActivityCountProvider.VidJoinActNum extend = new VidActivityCountProvider.VidJoinActNum();
        extend.setCarIdentifyType(CarIdentityTypeEnum.USAGE_EQUITY.getCode());
        extend.setCarIdentityId("testEqu");
        extend.setVid("MOCKTEST1234556");
        extend.setActId(21531917L);
        extend.setVidJoinNumLimit(100);

        Map<Long, Integer> validMap = new HashMap<Long, Integer>();
        validMap.put(1111L, 1);
        validMap.put(2222L, 2);
        validMap.put(3333L, 2);
        validMap.put(4444L, 2);
        extend.setValidOrderId(validMap);


        Map<Long, Integer> invalidMap = new HashMap<Long, Integer>();
        invalidMap.put(2222L, 2);
        invalidMap.put(3333L, 1);
        invalidMap.put(4444L, 2);
        extend.setInValidOrderId(invalidMap);

        count.setExtend(GsonUtil.toJson(extend));

        promotionCarActivityCountMapper.insert(count);
    }


}
