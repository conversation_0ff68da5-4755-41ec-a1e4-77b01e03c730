package com.xiaomi.nr.promotion.v2.activity.carshop;

import com.xiaomi.goods.gis.dto.stock.GiftStockRespParam;
import com.xiaomi.micar.club.api.model.member.MemberInfo;
import com.xiaomi.nr.md.promotion.admin.api.constant.PromotionTypeEnum;
import com.xiaomi.nr.promotion.activity.carshop.CarShopBuyGiftActivity;
import com.xiaomi.nr.promotion.api.dto.MultiProductGoodsActRequest;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.api.dto.model.carshop.Region;
import com.xiaomi.nr.promotion.constant.PromotionTextConstant;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.entity.redis.QuotaEle;
import com.xiaomi.nr.promotion.entity.redis.SkuGroup;
import com.xiaomi.nr.promotion.entity.redis.*;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.BooleanEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.ProductDetailContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.BuyGiftPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ConfigMetaInfo;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.GoodsStockExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@Slf4j
public class CarShopBuyGiftActivityTest extends BaseTestV2 {

    @InjectMocks
    private CarShopBuyGiftActivity activity;
    @Mock
    private ActivityRedisDao activityRedisDao;

    private BuyGiftPromotionConfig config;
    private Goods giftGoods;
    private FillGoodsGroup includeGoodsGroup;

    @BeforeEach
    public void setUp() {
        // 初始化配置
        config = new BuyGiftPromotionConfig();
        config.setPromotionId(1L);
        config.setName("测试车商城买赠活动");
        
        // 初始化赠品信息
        giftGoods = new Goods();
        giftGoods.setSkuList(Collections.singletonList("SKU001"));
        giftGoods.setSelectType("1");
        giftGoods.setIgnoreStock(0);
        giftGoods.setRefundValue(0L);
        giftGoods.setForceParent(0);
        giftGoods.setSkuList(List.of("1111"));


        SkuGroup skuGroup = new SkuGroup();
        skuGroup.setGroupId(11L);
        skuGroup.setSendType("");
        skuGroup.setDescGroupRule("");
        GiftBargainGroup giftBargainGroup = new GiftBargainGroup();
        giftBargainGroup.setSku(111L);
        giftBargainGroup.setMarketPrice(100L);
        giftBargainGroup.setCartPrice(100L);
        giftBargainGroup.setShipmentType(1);
        giftBargainGroup.setRefundValue(10L);
        giftBargainGroup.setDescRule("sss");
        giftBargainGroup.setRefundValue(10L);
        giftBargainGroup.setGiftLimitNum(10L);
        giftBargainGroup.setGiftBaseNum(1L);
        skuGroup.setListInfo(List.of(giftBargainGroup));
        giftGoods.setSkuGroupList(List.of(skuGroup));
        config.setGiftGoods(giftGoods);
        
        // 初始化包含商品组
        includeGoodsGroup = new FillGoodsGroup();
        CompareItem compareItem = new CompareItem();
        compareItem.setSsuId(List.of(1L,2L));
        includeGoodsGroup.setJoinGoods(compareItem);
        includeGoodsGroup.setQuota(new QuotaEle());
        config.setIncludeGoodsGroup(includeGoodsGroup);

        ReflectionTestUtils.setField(activity, "unixStartTime", System.currentTimeMillis() / 1000);
        ReflectionTestUtils.setField(activity, "unixEndTime", System.currentTimeMillis() / 1000 + 1000*3600*24*7);
        ReflectionTestUtils.setField(activity, "type", ActivityTypeEnum.BUY_GIFT);
        ReflectionTestUtils.setField(activity, "giftGoods", giftGoods);
        ReflectionTestUtils.setField(activity, "promotionConfig", config);
        ConfigMetaInfo configMetaInfo = new ConfigMetaInfo();
        configMetaInfo.setName("name");
        configMetaInfo.setDesc("desc");
        configMetaInfo.setTypeCode("1");
        configMetaInfo.setIsOnlyGoods(1);
        ReflectionTestUtils.setField(activity, "metaInfo", configMetaInfo);
        ReflectionTestUtils.setField(activity, "includeGoodsGroups", List.of(includeGoodsGroup));
    }

    @Test
    public void test_getType() {
        // 执行测试
        PromotionToolType type = activity.getType();

        // 验证结果
        assertEquals(PromotionToolType.BUY_GIFT, type);
    }

    @Test
    public void test_getBizPlatform() {
        // 执行测试
        BizPlatformEnum platform = activity.getBizPlatform();

        // 验证结果
        assertEquals(BizPlatformEnum.CAR_SHOP, platform);
    }

    @Test
    public void test_getActivityDetail() {
        // 执行测试
        ActivityDetail detail = activity.getActivityDetail();

        // 验证结果
        assertNull(detail);
    }


    @Test
    public void test_buildCartPromotionInfo() throws BizError {
        // 准备测试数据
        LocalContext context = new LocalContext();
        // 准备测试数据
        CartItem cartItem = new CartItem();
        cartItem.setSsuId(1L);
        cartItem.setItemId("1");
        cartItem.setOriginalCartPrice(100L);
        MaintenanceInfo maintenanceInfo = new MaintenanceInfo();
        maintenanceInfo.setPayType(1);
        cartItem.setMaintenanceInfo(maintenanceInfo);
        context.setCarts(List.of(cartItem));
        Map<String, GiftStockRespParam> stockMap = Collections.singletonMap("1001_2001", new GiftStockRespParam());
        context.getExternalDataMap().put(ResourceExtType.GIFT_GOODS_STOCK, new GoodsStockExternalProvider() {
            @Override
            public Map<String, GiftStockRespParam> getData() {
                return stockMap;
            }
        });

        when(activityRedisDao.getActBuyGiftLimitNum(anyLong(), anyString(), anyLong())).thenReturn(1);

        // 执行测试
        PromotionInfo result = activity.buildCartPromotionInfo(context);

        // 验证结果
        assertNotNull(result);
        assertEquals(BooleanEnum.NO.getValue(), result.getActivityMutexLimit());
    }

    @Test
    public void test_getProductGoodsAct() throws BizError {
        // 准备测试数据
        Long clientId = 123L;
        String orgCode = "TEST_ORG";
        List<String> skuPackageList = List.of("SKU1", "SKU2");

        // 执行测试
        Map<String, ProductActInfo> result = activity.getProductGoodsAct(clientId, orgCode, skuPackageList);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void test_getMultiProductAct() throws BizError {
        // 准备测试数据
        MultiGoodItem goodItem = new MultiGoodItem();
        MultiProductGoodsActRequest request = new MultiProductGoodsActRequest();
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        List<MultiGoodItem> goodsList=new ArrayList<>();
        MultiGoodItem item2=new MultiGoodItem();
        item2.setSsuId(2230000792L);
        item2.setMarketPrice(1000000L);
        goodsList.add(item2);
        request.setGoodsList(goodsList);
        request.setPromotionTypeList(List.of(PromotionTypeEnum.BUY_GIFT.code,PromotionTypeEnum.CAR_SHOP_VIP.code));
        Region region = new Region();
        region.setProvince(0);
        region.setCity(0);
        region.setArea(0);
        region.setDistrict(0);
        request.setRegion(region);
        request.setVipLevel(2);
        ProductDetailContext context = new ProductDetailContext();

        // Mock VIP会员信息
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setLevel(1);
        Map<ResourceExtType, ExternalDataProvider<?>> externalDataMap = new HashMap<>();
        GoodsStockExternalProvider goodsStockExternalProvider = mock(GoodsStockExternalProvider.class);


        Map<String, GiftStockRespParam> giftStockRespParamMap = new HashMap<>();
        GiftStockRespParam giftStockRespParam = new GiftStockRespParam();
        giftStockRespParam.setStockNum(1L);
        giftStockRespParam.setMasterId(2L);
        giftStockRespParamMap.put("", giftStockRespParam);

        when(goodsStockExternalProvider.getData()).thenReturn(giftStockRespParamMap);
        externalDataMap.put(ResourceExtType.GIFT_GOODS_STOCK, goodsStockExternalProvider);
        context.setExternalDataMap(externalDataMap);

        context.setExternalDataMap(externalDataMap);
        ReflectionTestUtils.setField(activity, "promotionConfig", config);

        // 执行测试
        PromotionInfoDTO result = activity.getMultiProductAct(goodItem, request, context);

        // 验证结果
        assertNotNull(result);
        assertEquals(PromotionTextConstant.CAR_SHOP_BUY_GIFT, result.getPromotionText());
    }
}
