package com.xiaomi.nr.promotion.v2.api.service;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.ManualQueryResourceResponse;
import com.xiaomi.nr.promotion.api.dto.ManualResourceRequest;
import com.xiaomi.nr.promotion.api.service.ManualOperationDubboService;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;

@Slf4j
public class ManualOperationDubboServiceTest extends BaseTestV2 {

    @Autowired
    private ManualOperationDubboService manualOperationDubboService;

    @Test
    public void test_manualRefundResource_success() throws BizError {
        ManualResourceRequest request = new ManualResourceRequest();
        request.setIdCard("111111111111111111");
        request.setSpuGroup(Arrays.asList(1, 2, 3));
        Result<String> result = manualOperationDubboService.manualRefundResource(request);
        Assertions.assertEquals(0, result.getCode());
        Assertions.assertNotNull(result.getData());
    }

    @Test
    public void test_manualRefundResource_emptySpuGroup() throws BizError {
        ManualResourceRequest request = new ManualResourceRequest();
        request.setIdCard("111111111111111111");
        request.setSpuGroup(Arrays.asList());
        Result<String> result = manualOperationDubboService.manualRefundResource(request);
        Assertions.assertEquals(0, result.getCode());
        Assertions.assertEquals("empty spuGroup", result.getData());
    }

    @Test
    public void test_manualRefundResource_largeSpuGroup() throws BizError {
        ManualResourceRequest request = new ManualResourceRequest();
        request.setIdCard("111111111111111111");
        request.setSpuGroup(Arrays.asList(1, 2, 3, 4));
        Result<String> result = manualOperationDubboService.manualRefundResource(request);
        Assertions.assertEquals(0, result.getCode());
        Assertions.assertEquals("spuGroup size over 3", result.getData());
    }

    @Test
    public void test_manualQueryResource_success() {
        ManualResourceRequest request = new ManualResourceRequest();
        request.setIdCard("111111111111111111");
        request.setSpuGroup(Arrays.asList(1, 2, 3));
        Result<ManualQueryResourceResponse> result = manualOperationDubboService.manualQueryResource(request);
        Assertions.assertEquals(0, result.getCode());
        Assertions.assertNotNull(result.getData());
    }

    @Test
    public void test_manualQueryResource_nullIdCard() {
        ManualResourceRequest request = new ManualResourceRequest();
        request.setIdCard(null);
        request.setSpuGroup(Arrays.asList(1, 2, 3));
        Result<ManualQueryResourceResponse> result = manualOperationDubboService.manualQueryResource(request);
        Assertions.assertEquals(0, result.getCode());
        Assertions.assertNotNull(result.getData());
    }
} 