package com.xiaomi.nr.promotion.v2.componet.condition.carmaintenance;

import com.xiaomi.micar.club.api.model.member.MemberInfo;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.MaintenanceInfo;
import com.xiaomi.nr.promotion.componet.condition.carmaintenance.MaintenanceItemFreeCondition;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionUserActivityCountMapper;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.UserActivityCount;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.enums.CarIdentityTypeEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.MaintenanceItemFreePromotionConfig;
import com.xiaomi.nr.promotion.resource.external.CarVidVipExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

//@ExtendWith(MockitoExtension.class)
public class MaintenanceItemFreeConditionTest extends BaseTestV2 {

    @InjectMocks
    private MaintenanceItemFreeCondition condition;

    @Mock
    private PromotionUserActivityCountMapper userActivityCountMapper;

    @Mock
    private CarVidVipExternalProvider carVidVipExternalProvider;

    private CheckoutPromotionRequest request;
    private LocalContext context;
    private MaintenanceItemFreePromotionConfig config;

    @BeforeEach
    public void setUp() {
        // 初始化request
        request = new CheckoutPromotionRequest();
        request.setVid("test_vid");
        request.setWorkOrderType(1);
        request.setUserId(123L);
        request.setEquityKeys(Arrays.asList("equity_key"));

        CartItem cartItem = new CartItem();
        cartItem.setSsuId(1L);
        cartItem.setItemId("item1");
        cartItem.setOriginalCartPrice(100L);
        MaintenanceInfo maintenanceInfo = new MaintenanceInfo();
        maintenanceInfo.setPayType(1);
        cartItem.setMaintenanceInfo(maintenanceInfo);
        request.setCartList(Arrays.asList(cartItem));

        // 初始化context
        context = new LocalContext();
        context.setBizPlatform(BizPlatformEnum.MAINTENANCE_REPAIR);
        Map<ResourceExtType, ExternalDataProvider<?>> externalDataMap = new HashMap<>();
        externalDataMap.put(ResourceExtType.VID_ULTRA_VIP_MEMBER, carVidVipExternalProvider);
        context.setExternalDataMap(externalDataMap);

        // 初始化config
        config = new MaintenanceItemFreePromotionConfig();
        config.setPromotionId(1L);
        config.setPromotionType(PromotionToolType.MAINTENANCE_ITEM_FREE);
        CompareItem joinGoods = new CompareItem();
        joinGoods.setSsuId(Arrays.asList(1L));
        config.setJoinGoods(joinGoods);
        config.setCarIdentityType(CarIdentityTypeEnum.CAR_SHOP_VIP.getCode());
        config.setCarIdentityId("123");
        config.setIdentityJoinLimitNum(5);
        config.setWorkOrderType(Arrays.asList(1));
    }

    @Test
    public void test_isSatisfied_success() throws BizError {
        // mock VIP会员信息
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setVid("test_vid");
        memberInfo.setLevel(Integer.valueOf(config.getCarIdentityId()));
        memberInfo.setMid("123");
        when(carVidVipExternalProvider.getData()).thenReturn(memberInfo);

        // mock 用户参与次数
        UserActivityCount userActivityCount = new UserActivityCount();
        userActivityCount.setNum(1);
        when(userActivityCountMapper.getByUserIdAndPromotionId(123L, 1L)).thenReturn(userActivityCount);

        // 加载配置
        condition.loadConfig(config);

        // 验证
        assertTrue(condition.isSatisfied(request, context));
    }

    @Test
    public void test_isSatisfied_fail() throws BizError {
        condition.loadConfig(config);

        // 场景1: 业务平台不匹配
        context.setBizPlatform(BizPlatformEnum.NEW_RETAIL);
        assertFalse(condition.isSatisfied(request, context));
        context.setBizPlatform(BizPlatformEnum.MAINTENANCE_REPAIR);

        // 场景2: VID为空
        CheckoutPromotionRequest emptyVidRequest = new CheckoutPromotionRequest();
        assertFalse(condition.isSatisfied(emptyVidRequest, context));
        emptyVidRequest.setVid("test_vid");

        // 场景3: 工单类型不匹配
        request.setWorkOrderType(2);
        assertFalse(condition.isSatisfied(request, context));
        request.setWorkOrderType(1);

        // 场景4: VIP身份不匹配
        MemberInfo wrongMemberInfo = new MemberInfo();
        wrongMemberInfo.setVid("test_vid");
        wrongMemberInfo.setLevel(999);
        when(carVidVipExternalProvider.getData()).thenReturn(wrongMemberInfo);
        assertFalse(condition.isSatisfied(request, context));

        // 场景5: 用户参与次数超限
        MemberInfo validMemberInfo = new MemberInfo();
        validMemberInfo.setVid("test_vid");
        validMemberInfo.setLevel(Integer.valueOf(config.getCarIdentityId()));
        validMemberInfo.setMid("123");
        when(carVidVipExternalProvider.getData()).thenReturn(validMemberInfo);

        UserActivityCount maxUserActivityCount = new UserActivityCount();
        maxUserActivityCount.setNum(5);
        when(userActivityCountMapper.getByUserIdAndPromotionId(123L, 1L)).thenReturn(maxUserActivityCount);
        assertFalse(condition.isSatisfied(request, context));

        // 场景6: 商品不满足条件
        CartItem invalidCartItem = new CartItem();
        invalidCartItem.setSsuId(999L);
        request.setCartList(Arrays.asList(invalidCartItem));
        assertFalse(condition.isSatisfied(request, context));
    }

    @Test
    public void test_loadConfig_fail() {
        condition.loadConfig(null);
        Assertions.assertNull(ReflectionTestUtils.getField(condition, "promotionId"));
    }
}
