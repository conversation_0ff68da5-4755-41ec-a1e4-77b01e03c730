package com.xiaomi.nr.promotion.api.service;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.api.dto.CartPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CartPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Request;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Response;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/12/16
 */
@Slf4j
public class GiftExchangeServiceTest extends BaseTest {


    @Resource
    private PromotionDubboService promotionDubboService;

    @Test
    public void testCheckoutPromotionV2() {

        CheckoutPromotionV2Request req = GsonUtil.fromJson("{\n" +
                "    \"getCouponList\": true,\n" +
                "    \"showType\": 1,\n" +
                "    \"useDefaultCoupon\": true,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"reduceList\": {},\n" +
                "            \"reduceShareMap\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"childs\": [],\n" +
                "            \"bindMainAccessory\": false,\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"itemId\": \"2221000021_gift_2221000288_21528741_1\",\n" +
                "            \"sku\": \"8742\",\n" +
                "            \"packageId\": \"\",\n" +
                "            \"ssuId\": 2221000288,\n" +
                "            \"count\": 1,\n" +
                "            \"standardPrice\": 12000,\n" +
                "            \"cartPrice\": 12000,\n" +
                "            \"prePrice\": 0,\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"source\": \"gift\",\n" +
                "            \"sourceCode\": \"21528741\",\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"properties\": \"\",\n" +
                "            \"parentItemId\": \"2221000021\",\n" +
                "            \"displayType\": 1,\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"accessCode\": [],\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"marketPrice\": 12000,\n" +
                "            \"cannotUseEcard\": true,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                1,\n" +
                "                2,\n" +
                "                3,\n" +
                "                4,\n" +
                "                8,\n" +
                "                5,\n" +
                "                9,\n" +
                "                27,\n" +
                "                7,\n" +
                "                20,\n" +
                "                10,\n" +
                "                23,\n" +
                "                26,\n" +
                "                22,\n" +
                "                25,\n" +
                "                28,\n" +
                "                70,\n" +
                "                71,\n" +
                "                72,\n" +
                "                2001,\n" +
                "                500,\n" +
                "                501,\n" +
                "                502,\n" +
                "                503,\n" +
                "                504,\n" +
                "                505,\n" +
                "                2002,\n" +
                "                29\n" +
                "            ],\n" +
                "            \"saleSource\": \"gift\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"groupId\": 1,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"unitId\": \"\",\n" +
                "            \"goodsType\": 2,\n" +
                "            \"department\": 0,\n" +
                "            \"cannotUsePoint\": true,\n" +
                "            \"canAdjustPrice\": false\n" +
                "        },\n" +
                "        {\n" +
                "            \"reduceList\": {},\n" +
                "            \"reduceShareMap\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"childs\": [],\n" +
                "            \"bindMainAccessory\": false,\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"itemId\": \"2221000021\",\n" +
                "            \"sku\": \"6958\",\n" +
                "            \"packageId\": \"\",\n" +
                "            \"ssuId\": 2221000021,\n" +
                "            \"count\": 1,\n" +
                "            \"standardPrice\": 9900,\n" +
                "            \"cartPrice\": 9900,\n" +
                "            \"prePrice\": 0,\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"properties\": \"\",\n" +
                "            \"displayType\": 1,\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"accessCode\": [],\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"marketPrice\": 9900,\n" +
                "            \"cannotUseEcard\": true,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                1,\n" +
                "                2,\n" +
                "                3,\n" +
                "                4,\n" +
                "                8,\n" +
                "                5,\n" +
                "                9,\n" +
                "                27,\n" +
                "                7,\n" +
                "                20,\n" +
                "                10,\n" +
                "                23,\n" +
                "                26,\n" +
                "                22,\n" +
                "                25,\n" +
                "                28,\n" +
                "                70,\n" +
                "                71,\n" +
                "                72,\n" +
                "                2001,\n" +
                "                500,\n" +
                "                501,\n" +
                "                502,\n" +
                "                503,\n" +
                "                504,\n" +
                "                505,\n" +
                "                2002,\n" +
                "                29\n" +
                "            ],\n" +
                "            \"saleSource\": \"exchange\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"groupId\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"unitId\": \"\",\n" +
                "            \"goodsType\": 2,\n" +
                "            \"department\": 0,\n" +
                "            \"cannotUsePoint\": true,\n" +
                "            \"canAdjustPrice\": false\n" +
                "        }\n" +
                "    ],\n" +
                "    \"channel\": 13,\n" +
                "    \"userId\": 3150401733,\n" +
                "    \"couponIds\": [],\n" +
                "    \"couponCodes\": [],\n" +
                "    \"orderId\": 0,\n" +
                "    \"bargainSize\": 10,\n" +
                "    \"noSaveDbSubmit\": false,\n" +
                "    \"useRedPacket\": false,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"uidType\": \"\",\n" +
                "    \"cityId\": 0,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"globalBusinessPartner\": \"car\",\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"userIsFriend\": 0,\n" +
                "    \"usePoint\": false,\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"submitType\": 0,\n" +
                "    \"usePurchaseSubsidy\": false,\n" +
                "    \"region\": {\n" +
                "        \"province\": 2,\n" +
                "        \"city\": 36,\n" +
                "        \"district\": 384,\n" +
                "        \"area\": 384010\n" +
                "    }\n" +
                "}", CheckoutPromotionV2Request.class);

        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(req);
        log.info("result:{}", GsonUtil.toJson(result));

    }

    @Test
    public void testCartCheckoutPromotion() {

        CartPromotionRequest req = GsonUtil.fromJson("{\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"reduceList\": {},\n" +
                "            \"reduceShareMap\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"childs\": [],\n" +
                "            \"bindMainAccessory\": false,\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"itemId\": \"2221000021\",\n" +
                "            \"sku\": \"6958\",\n" +
                "            \"packageId\": \"\",\n" +
                "            \"ssuId\": 2221000021,\n" +
                "            \"count\": 1,\n" +
                "            \"standardPrice\": 9900,\n" +
                "            \"cartPrice\": 9900,\n" +
                "            \"prePrice\": 0,\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"properties\": \"\",\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"displayType\": 1,\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"accessCode\": [],\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"marketPrice\": 9900,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                1,\n" +
                "                2,\n" +
                "                3,\n" +
                "                4,\n" +
                "                8,\n" +
                "                5,\n" +
                "                9,\n" +
                "                27,\n" +
                "                7,\n" +
                "                20,\n" +
                "                10,\n" +
                "                23,\n" +
                "                26,\n" +
                "                22,\n" +
                "                25,\n" +
                "                28,\n" +
                "                70,\n" +
                "                71,\n" +
                "                72,\n" +
                "                2001,\n" +
                "                500,\n" +
                "                501,\n" +
                "                502,\n" +
                "                503,\n" +
                "                504,\n" +
                "                505,\n" +
                "                2002,\n" +
                "                29\n" +
                "            ],\n" +
                "            \"saleSource\": \"exchange\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"groupId\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"unitId\": \"\",\n" +
                "            \"goodsType\": 2,\n" +
                "            \"department\": 0,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"selected\": true\n" +
                "        }\n" +
                "    ],\n" +
                "    \"channel\": 13,\n" +
                "    \"userId\": 3150401733,\n" +
                "    \"couponIds\": [],\n" +
                "    \"bargainSize\": 10,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"uidType\": \"\",\n" +
                "    \"globalBusinessPartner\": \"car\",\n" +
                "    \"getCouponList\": true,\n" +
                "    \"showType\": 1,\n" +
                "    \"useDefaultCoupon\": false,\n" +
                "    \"submitType\": 0\n" +
                "}", CartPromotionRequest.class);

        Result<CartPromotionResponse> result = promotionDubboService.cartCheckoutPromotion(req);
        log.info("result:{}", GsonUtil.toJson(result));
    }

}
