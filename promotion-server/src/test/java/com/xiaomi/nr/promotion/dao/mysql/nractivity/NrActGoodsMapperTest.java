package com.xiaomi.nr.promotion.dao.mysql.nractivity;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.dao.mysql.nractivity.po.NrActGoodsPo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @author： Pancras
 * @date： 2024/9/19
 * @description：
 * @modifiedBy：
 * @version: 1.0
 */
public class NrActGoodsMapperTest extends BaseTest {
    
    @Autowired
    private NrActGoodsMapper nrActGoodsMapper;
    
    @Test
    public void test() {
        
        List<NrActGoodsPo> nrActGoodsPos = nrActGoodsMapper.queryListByType(Lists.newArrayList(742L));
        System.out.println(nrActGoodsPos);
        
    }
}
