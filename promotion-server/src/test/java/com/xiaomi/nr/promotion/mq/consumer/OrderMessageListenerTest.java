package com.xiaomi.nr.promotion.mq.consumer;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.rpc.oc.OcSearchServiceProxy;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.net.URI;

/**
 * 消息
 *
 * <AUTHOR>
 * @date 2023/1/9
 */
public class OrderMessageListenerTest extends BaseTest {
    @Autowired
    private OrderMessageListener orderMessageListener;

    @Autowired
    private OrderCarLifeMessageListener orderCarLifeMessageListener;
    @Resource
    private OcSearchServiceProxy ocSearchServiceProxy;

    @Test
    public void testOnMessage() {
        orderMessageListener.onMessage("{\"sa_order_id\":\"SA5230109582130259\",\"od_order_id\":\"3235091009016259\",\"buyer_id\":3150058766,\"event\":\"OrderRefund\",\"hash\":\"09c4e846134eafcc2aba4868126d7fca\",\"index\":\"3235091009016259\",\"timestamp\":1673256247,\"body\":\"{\\\"order_data\\\":{\\\"org_id\\\":\\\"MI0101\\\",\\\"create_time\\\":1673256239,\\\"p_order_id\\\":\\\"5235091008746859\\\",\\\"org_order_id\\\":\\\"5235091008746859\\\",\\\"top_order_id\\\":\\\"5230109582130259\\\",\\\"order_status\\\":17,\\\"goods_amount\\\":999,\\\"order_type\\\":2,\\\"sales_type\\\":0,\\\"order_flow\\\":13,\\\"order_from\\\":67,\\\"order_app\\\":21,\\\"pay_id\\\":137,\\\"channel\\\":\\\"1044.1000.0.0\\\",\\\"update_time\\\":1673256217,\\\"refund_time\\\":0,\\\"shipment_id\\\":138,\\\"shipment_expense\\\":0,\\\"p_waybill_id\\\":\\\"\\\",\\\"partner_code\\\":\\\"\\\",\\\"order_tags\\\":null},\\\"item_detail\\\":[{\\\"item_no\\\":1,\\\"goods_id\\\":16147,\\\"goods_name\\\":\\\"\\\",\\\"goods_count\\\":1,\\\"shop_price\\\":999,\\\"cart_price\\\":999,\\\"origin_price\\\":999,\\\"insurance_sku\\\":\\\"\\\",\\\"batch_goods_id\\\":0,\\\"mode\\\":0}]}\",\"topic_name\":\"\"}");
    }

    @Test
    public void testOnCarLifeMessage() {
        orderCarLifeMessageListener.onMessage("{\"sa_order_id\":\"\",\"od_order_id\":\"100008001\",\"buyer_id\":2270998577,\"event\":\"OrderRefund\",\"hash\":\"09c4e846134eafcc2aba4868126d7fca\",\"index\":\"100007001\",\"timestamp\":1673256247,\"body\":\"{\\\"order_data\\\":{\\\"org_id\\\":\\\"MI0101\\\",\\\"create_time\\\":1673256239,\\\"p_order_id\\\":\\\"100008\\\",\\\"org_order_id\\\":\\\"100008\\\",\\\"top_order_id\\\":\\\"5230109582130259\\\",\\\"order_status\\\":17,\\\"goods_amount\\\":999,\\\"order_type\\\":2,\\\"sales_type\\\":0,\\\"order_flow\\\":13,\\\"order_from\\\":67,\\\"order_app\\\":21,\\\"pay_id\\\":137,\\\"channel\\\":\\\"1044.1000.0.0\\\",\\\"update_time\\\":1673256217,\\\"refund_time\\\":0,\\\"integral_amount\\\":\\\"18.90\\\",\\\"shipment_id\\\":138,\\\"shipment_expense\\\":0,\\\"p_waybill_id\\\":\\\"\\\",\\\"partner_code\\\":\\\"\\\",\\\"order_tags\\\":null},\\\"item_detail\\\":[{\\\"item_no\\\":1,\\\"goods_id\\\":16147,\\\"goods_name\\\":\\\"\\\",\\\"goods_count\\\":1,\\\"shop_price\\\":999,\\\"cart_price\\\":999,\\\"origin_price\\\":999,\\\"insurance_sku\\\":\\\"\\\",\\\"batch_goods_id\\\":0,\\\"mode\\\":0}]}\",\"topic_name\":\"\"}");

    }


    @Test
    public void testIsAllRefund() throws Exception {
        Long validOrderId = 5206191000002019L;
        String url = "http://search.oc.test.mi.com/refund/statistics";
        URI url2 = new URI(url);

        System.out.println(url2.getHost());

        // Test with a valid order ID
        Boolean result = ocSearchServiceProxy.isAllRefund(validOrderId);
        Assert.assertTrue(result);


    }
}
