package com.xiaomi.nr.promotion.mq.consumer;

import com.xiaomi.nr.md.promotion.admin.api.constant.ChannelEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.PromotionTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.SourceAppEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.rpc.mdpromotionadmin.PromotionAdminCustomServiceProxy;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 * 消息
 *
 * <AUTHOR>
 * @date 2023/1/9
 */
public class OrderMessageListenerTest extends BaseTest {
    @Autowired
    private OrderMessageListener orderMessageListener;

    @Autowired
    private OrderCarLifeMessageListener orderCarLifeMessageListener;
    @Autowired
    private CarPromotionChangeListener carPromotionChangeListener;
    @Autowired
    private PromotionAdminCustomServiceProxy promotionAdminCustomServiceProxy;

    @Test
    public void testOnMessage() {
        orderMessageListener.onMessage("{\"sa_order_id\":\"SA5230109582130259\",\"od_order_id\":\"3235091009016259\",\"buyer_id\":3150058766,\"event\":\"OrderRefund\",\"hash\":\"09c4e846134eafcc2aba4868126d7fca\",\"index\":\"3235091009016259\",\"timestamp\":1673256247,\"body\":\"{\\\"order_data\\\":{\\\"org_id\\\":\\\"MI0101\\\",\\\"create_time\\\":1673256239,\\\"p_order_id\\\":\\\"5235091008746859\\\",\\\"org_order_id\\\":\\\"5235091008746859\\\",\\\"top_order_id\\\":\\\"5230109582130259\\\",\\\"order_status\\\":17,\\\"goods_amount\\\":999,\\\"order_type\\\":2,\\\"sales_type\\\":0,\\\"order_flow\\\":13,\\\"order_from\\\":67,\\\"order_app\\\":21,\\\"pay_id\\\":137,\\\"channel\\\":\\\"1044.1000.0.0\\\",\\\"update_time\\\":1673256217,\\\"refund_time\\\":0,\\\"shipment_id\\\":138,\\\"shipment_expense\\\":0,\\\"p_waybill_id\\\":\\\"\\\",\\\"partner_code\\\":\\\"\\\",\\\"order_tags\\\":null},\\\"item_detail\\\":[{\\\"item_no\\\":1,\\\"goods_id\\\":16147,\\\"goods_name\\\":\\\"\\\",\\\"goods_count\\\":1,\\\"shop_price\\\":999,\\\"cart_price\\\":999,\\\"origin_price\\\":999,\\\"insurance_sku\\\":\\\"\\\",\\\"batch_goods_id\\\":0,\\\"mode\\\":0}]}\",\"topic_name\":\"\"}");
    }

    @Test
    public void testOnCarLifeMessage() {
        //orderCarLifeMessageListener.onMessage("{\"sa_order_id\":\"SA100007\",\"od_order_id\":\"100007\",\"buyer_id\":3150058766,\"event\":\"OrderClosed\",\"hash\":\"09c4e846134eafcc2aba4868126d7fca\",\"index\":\"100007\",\"timestamp\":1673256247,\"body\":\"{\\\"order_data\\\":{\\\"org_id\\\":\\\"MI0101\\\",\\\"create_time\\\":1673256239,\\\"p_order_id\\\":\\\"5235091008746859\\\",\\\"org_order_id\\\":\\\"100007\\\",\\\"top_order_id\\\":\\\"5230109582130259\\\",\\\"order_status\\\":17,\\\"goods_amount\\\":999,\\\"order_type\\\":2,\\\"sales_type\\\":0,\\\"order_flow\\\":13,\\\"order_from\\\":67,\\\"order_app\\\":21,\\\"pay_id\\\":137,\\\"channel\\\":\\\"1044.1000.0.0\\\",\\\"update_time\\\":1673256217,\\\"refund_time\\\":0,\\\"shipment_id\\\":138,\\\"shipment_expense\\\":0,\\\"p_waybill_id\\\":\\\"\\\",\\\"partner_code\\\":\\\"\\\",\\\"order_tags\\\":null},\\\"item_detail\\\":[{\\\"item_no\\\":1,\\\"goods_id\\\":16147,\\\"goods_name\\\":\\\"\\\",\\\"goods_count\\\":1,\\\"shop_price\\\":999,\\\"cart_price\\\":999,\\\"origin_price\\\":999,\\\"insurance_sku\\\":\\\"\\\",\\\"batch_goods_id\\\":0,\\\"mode\\\":0}]}\",\"topic_name\":\"\"}");
        orderCarLifeMessageListener.onMessage("{\"sa_order_id\":\"\",\"od_order_id\":\"100008001\",\"buyer_id\":2270998577,\"event\":\"OrderRefund\",\"hash\":\"09c4e846134eafcc2aba4868126d7fca\",\"index\":\"100007001\",\"timestamp\":1673256247,\"body\":\"{\\\"order_data\\\":{\\\"org_id\\\":\\\"MI0101\\\",\\\"create_time\\\":1673256239,\\\"p_order_id\\\":\\\"100008\\\",\\\"org_order_id\\\":\\\"100008\\\",\\\"top_order_id\\\":\\\"5230109582130259\\\",\\\"order_status\\\":17,\\\"goods_amount\\\":999,\\\"order_type\\\":2,\\\"sales_type\\\":0,\\\"order_flow\\\":13,\\\"order_from\\\":67,\\\"order_app\\\":21,\\\"pay_id\\\":137,\\\"channel\\\":\\\"1044.1000.0.0\\\",\\\"update_time\\\":1673256217,\\\"refund_time\\\":0,\\\"integral_amount\\\":\\\"18.90\\\",\\\"shipment_id\\\":138,\\\"shipment_expense\\\":0,\\\"p_waybill_id\\\":\\\"\\\",\\\"partner_code\\\":\\\"\\\",\\\"order_tags\\\":null},\\\"item_detail\\\":[{\\\"item_no\\\":1,\\\"goods_id\\\":16147,\\\"goods_name\\\":\\\"\\\",\\\"goods_count\\\":1,\\\"shop_price\\\":999,\\\"cart_price\\\":999,\\\"origin_price\\\":999,\\\"insurance_sku\\\":\\\"\\\",\\\"batch_goods_id\\\":0,\\\"mode\\\":0}]}\",\"topic_name\":\"\"}");

    }


    @Test
    public void testCarPromotionChangeMessage() {

        String str = "{\n" +
                "  \"header\": {\n" +
                "    \"profile\": \"prod\",\n" +
                "    \"timestamp\": 1620000000000\n" +
                "  },\n" +
                "  \"body\": {\n" +
                "    \"activityId\": 123456789,\n" +
                "    \"name\": \"Summer Promotion\",\n" +
                "    \"beginTime\": 1620000000000,\n" +
                "    \"endTime\": 1622505600000,\n" +
                "    \"promotionType\": 5,\n" +
                "    \"channels\": [1, 2, 3],\n" +
                "    \"status\": 0,\n" +
                "    \"operationType\": 1,\n" +
                "    \"sequenceId\": 987654321,\n" +
                "    \"operationDetail\": [\"code1\", \"code2\"]\n" +
                "  }\n" +
                "}\n";

        carPromotionChangeListener.onMessage(str);
    }

    /**
     * 汽车：来源、活动类型、渠道
     * 整车售后活动：来源、活动类型、渠道
     */
    private final static List<Integer> CAR_SOURCE = Arrays.asList(SourceAppEnum.CAR.code);

    private final static List<Integer> CAR_PROMOTION_TYPE = Arrays.asList(
            PromotionTypeEnum.RANGE_REDUCE.code,
            PromotionTypeEnum.ONSALE.code,
            PromotionTypeEnum.EXCHANGE_SUBSIDY.code,
            PromotionTypeEnum.ORDER_REDUCE.code,
            PromotionTypeEnum.MAINTENANCE_REPAIR_DISCOUNT.code,
            PromotionTypeEnum.MAINTENANCE_ITEM_FREE.code);

    private final static List<Integer> CAR_CHANNEL = Arrays.asList(ChannelEnum.CAR_VEHICLE.value, ChannelEnum.CAR_MAINTENANCE_REPAIR.value);

    /**
     * 车商城：来源、活动类型、渠道
     */
    private final static List<Integer> CAR_SHOP_SOURCE = Arrays.asList(SourceAppEnum.PROMOTION_ADMIN.code, SourceAppEnum.CAR_SHOP.code);

    private final static List<Integer> CAR_SHOP_PROMOTION_TYPE = Arrays.asList(
            PromotionTypeEnum.ONSALE.code,
            PromotionTypeEnum.BUY_GIFT.code,
            PromotionTypeEnum.CAR_SHOP_VIP.code);

    private final static List<Integer> CAR_SHOP_CHANNEL = Arrays.asList(ChannelEnum.CAR_SHOP.value);

    @Test
    public void testQueryCarActivityListV2() throws BizError {
        List<ActivityConfig> activityConfigs = promotionAdminCustomServiceProxy.queryCarActivityListV2(CAR_SOURCE, CAR_PROMOTION_TYPE, CAR_CHANNEL);
        List<ActivityConfig> activityConfigs1 = promotionAdminCustomServiceProxy.queryCarActivityListV2(CAR_SHOP_SOURCE, CAR_SHOP_PROMOTION_TYPE, CAR_SHOP_CHANNEL);
        List<ActivityConfig> activityConfigs2 = promotionAdminCustomServiceProxy.queryCarActivityList(CAR_SOURCE, CAR_PROMOTION_TYPE, CAR_CHANNEL);
        List<ActivityConfig> activityConfigs3 = promotionAdminCustomServiceProxy.queryCarActivityList(CAR_SHOP_SOURCE, CAR_SHOP_PROMOTION_TYPE, CAR_SHOP_CHANNEL);

        System.out.println("size1:" + activityConfigs.size());
        System.out.println("size2:" + activityConfigs1.size());
        System.out.println("size3:" + activityConfigs2.size());
        System.out.println("size4:" + activityConfigs3.size());
    }
}