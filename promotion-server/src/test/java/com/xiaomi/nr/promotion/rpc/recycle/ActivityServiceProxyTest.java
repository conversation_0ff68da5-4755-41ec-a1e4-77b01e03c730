package com.xiaomi.nr.promotion.rpc.recycle;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.recycle.api.activity.dto.ActivityQueryKeyDto;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2022/10/12
 */
public class ActivityServiceProxyTest extends BaseTest {
    @Autowired
    private ActivityServiceProxy activityServiceProxy;

    @Test
    public void testQueryCurrentRebate() throws BizError {
//        ActivityQueryKeyDto queryKeyDto = activityServiceProxy.queryCurrentRebate(221010272903958L);
//        Assert.assertNotNull(queryKeyDto);
//        System.out.println(GsonUtil.toJson(queryKeyDto));
    }
}