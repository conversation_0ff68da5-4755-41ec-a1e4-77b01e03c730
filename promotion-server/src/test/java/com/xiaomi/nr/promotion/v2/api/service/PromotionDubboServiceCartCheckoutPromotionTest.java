package com.xiaomi.nr.promotion.v2.api.service;

import com.xiaomi.nr.coupon.api.dto.trade.GetCheckoutCouponListV2Request;
import com.xiaomi.nr.coupon.api.dto.trade.GetCheckoutCouponListV2Response;
import com.xiaomi.nr.coupon.api.service.CouponTradeDubboService;
import com.xiaomi.nr.promotion.activity.pool.CarPromotionInstancePool;
import com.xiaomi.nr.promotion.api.dto.CartPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CartPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.enums.SubmitTypeEnum;
import com.xiaomi.nr.promotion.api.service.PromotionDubboService;
import com.xiaomi.nr.promotion.rpc.coupon.CouponTradeServiceProxy;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.Result;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;

import static org.mockito.ArgumentMatchers.any;

/**
 * cartCheckoutPromotion接口-购物车服务
 * 整车场景
 */
public class PromotionDubboServiceCartCheckoutPromotionTest extends BaseTestV2 {

    @Autowired
    private PromotionDubboService promotionDubboService;
    @Autowired
    private CarPromotionInstancePool carPromotionInstancePool;
    @Autowired
    private CouponTradeDubboService couponTradeDubboService;
    @Autowired
    private CouponTradeServiceProxy couponTradeServiceProxy;


    /**
     * 整车销售-常态改配-汽车定金券优惠
     */
    @Test
    public void test_cartCheckoutPromotion_1_payment_final_success(){

        carPromotionInstancePool.rebuildCacheTask();

        CouponTradeDubboService mockCouponTradeDubboService = PowerMockito.mock(CouponTradeDubboService.class);
        String str1 = "{\"noCodeCoupons\":{\"1011183162\":{\"couponBaseInfo\":{\"couponId\":1011183162,\"couponName\":\"SU7定金券\",\"configId\":198181,\"status\":\"unused\",\"startTime\":1739030400,\"endTime\":1774972799,\"couponType\":1,\"couponRangeDesc\":\"SU7定金券1\",\"couponRuleDesc\":\"满1件减1元优惠券\",\"promotionType\":4,\"promotionValue\":100,\"showUnit\":\"元\",\"maxReduce\":0,\"bottomType\":2,\"bottomPrice\":0,\"bottomCount\":1,\"postFree\":2,\"share\":2,\"type\":1,\"typeCode\":\"cash\",\"limitUseRegion\":[],\"sendChannel\":\"other\",\"useChannel\":\"mi_home\",\"useChannelDesc\":\"仅小米之家可用\",\"budgetApplyNo\":\"BR202403200004\",\"lineNum\":9013,\"bizPlatform\":3,\"serviceScene\":0,\"timesLimit\":1,\"workHourStandardPage\":10,\"sendScene\":\"11BFB15C0E2BA4D03DFF93D95C843D0E\",\"tags\":[],\"annualType\":0,\"checkoutStage\":1},\"validCode\":0,\"invalidReason\":\"不含可用优惠券商品\",\"couponGroupNo\":\"1_1_-1\",\"validSsuList\":[600035726],\"couponSsuExtInfo\":{}}}}";
        GetCheckoutCouponListV2Response response = GsonUtil.fromJson(str1, GetCheckoutCouponListV2Response.class);
        Mockito.when(mockCouponTradeDubboService.getCheckoutCouponListV2(any(GetCheckoutCouponListV2Request.class))).thenReturn(Result.success(response));
        Whitebox.setInternalState(couponTradeServiceProxy, "couponTradeDubboService", mockCouponTradeDubboService);

        // 整车
        String str="{\"cartList\":[{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600035726\",\"sku\":\"600035726\",\"packageId\":\"\",\"ssuId\":600035726,\"count\":1,\"standardPrice\":300000,\"cartPrice\":300000,\"prePrice\":0,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600035641\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":300000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,205,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,61,62,500,501,502,503,504,505,2002,29,55],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false,\"canAdjustPrice\":false,\"selected\":true,\"isDepositDiff\":false}],\"channel\":11,\"userId\":3150443380,\"couponIds\":[],\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"car\",\"getCouponList\":true,\"showType\":0,\"useDefaultCoupon\":true,\"usedCouponId\":1011183162,\"submitType\":1,\"orderTime\":1749453185}";
        CartPromotionRequest request = GsonUtil.fromJson(str, CartPromotionRequest.class);
        request.setChannel(ChannelEnum.CAR_VEHICLE.getValue());
        request.setSubmitType(SubmitTypeEnum.REPLACE.getCode());
        Result<CartPromotionResponse> result3 = promotionDubboService.cartCheckoutPromotion(request);
        Assertions.assertEquals(1, result3.getData().getCouponList().size());
        Whitebox.setInternalState(couponTradeServiceProxy, "couponTradeDubboService", couponTradeDubboService);
    }

    /**
     * 整车销售-常态改配-汽车尾款券优惠
     */
    @Test
    public void test_cartCheckoutPromotion_1_booking_success(){

        carPromotionInstancePool.rebuildCacheTask();

        CouponTradeDubboService mockCouponTradeDubboService = PowerMockito.mock(CouponTradeDubboService.class);
        String str1 = "{\"noCodeCoupons\":{\"1011145644\":{\"couponBaseInfo\":{\"couponId\":1011145644,\"couponName\":\"尾款券\",\"configId\":203233,\"status\":\"unused\",\"startTime\":1741190400,\"endTime\":1777564799,\"couponType\":1,\"couponRangeDesc\":\"测试\",\"couponRuleDesc\":\"满1件减1元优惠券\",\"promotionType\":4,\"promotionValue\":100,\"showUnit\":\"元\",\"maxReduce\":0,\"bottomType\":2,\"bottomPrice\":0,\"bottomCount\":1,\"postFree\":2,\"share\":2,\"type\":1,\"typeCode\":\"cash\",\"limitUseRegion\":[],\"sendChannel\":\"other\",\"useChannel\":\"mi_home\",\"useChannelDesc\":\"仅小米之家可用\",\"budgetApplyNo\":\"BR202403150014\",\"lineNum\":8960,\"bizPlatform\":3,\"serviceScene\":0,\"timesLimit\":1,\"workHourStandardPage\":10,\"sendScene\":\"11BFB15C0E2BA4D03DFF93D95C843D0E\",\"tags\":[],\"annualType\":0,\"checkoutStage\":2},\"validCode\":0,\"invalidReason\":\"不含可用优惠券商品\",\"couponGroupNo\":\"1_1_-1\",\"validSsuList\":[600035726],\"couponSsuExtInfo\":{}}}}";
        GetCheckoutCouponListV2Response response = GsonUtil.fromJson(str1, GetCheckoutCouponListV2Response.class);
        Mockito.when(mockCouponTradeDubboService.getCheckoutCouponListV2(any(GetCheckoutCouponListV2Request.class))).thenReturn(Result.success(response));
        Whitebox.setInternalState(couponTradeServiceProxy, "couponTradeDubboService", mockCouponTradeDubboService);

        // 整车
        String str="{\"cartList\":[{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600035726\",\"sku\":\"600035726\",\"packageId\":\"\",\"ssuId\":600035726,\"count\":1,\"standardPrice\":300000,\"cartPrice\":300000,\"prePrice\":0,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600035641\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":300000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,205,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,61,62,500,501,502,503,504,505,2002,29,55],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false,\"canAdjustPrice\":false,\"selected\":true,\"isDepositDiff\":false}],\"channel\":11,\"userId\":3150443380,\"couponIds\":[],\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"car\",\"getCouponList\":true,\"showType\":0,\"useDefaultCoupon\":true,\"usedCouponId\":1011145644,\"submitType\":1,\"orderTime\":1749453185}";
        CartPromotionRequest request = GsonUtil.fromJson(str, CartPromotionRequest.class);
        request.setChannel(ChannelEnum.CAR_VEHICLE.getValue());
        request.setSubmitType(SubmitTypeEnum.REPLACE.getCode());
        Result<CartPromotionResponse> result3 = promotionDubboService.cartCheckoutPromotion(request);
        Assertions.assertEquals(1, result3.getData().getCouponList().size());
        Whitebox.setInternalState(couponTradeServiceProxy, "couponTradeDubboService", couponTradeDubboService);
    }


    /**
     * 整车销售-常态销售-汽车尾款券优惠
     */
    @Test
    public void test_cartCheckoutPromotion_0_payment_final_success(){

        carPromotionInstancePool.rebuildCacheTask();

        CouponTradeDubboService mockCouponTradeDubboService = PowerMockito.mock(CouponTradeDubboService.class);
        String str1 = "{\"noCodeCoupons\":{\"1011145643\":{\"couponBaseInfo\":{\"couponId\":1011145643,\"couponName\":\"尾款券\",\"configId\":203233,\"status\":\"unused\",\"startTime\":1741190400,\"endTime\":1777564799,\"couponType\":1,\"couponRangeDesc\":\"测试\",\"couponRuleDesc\":\"满1件减1元优惠券\",\"promotionType\":4,\"promotionValue\":100,\"showUnit\":\"元\",\"maxReduce\":0,\"bottomType\":2,\"bottomPrice\":0,\"bottomCount\":1,\"postFree\":2,\"share\":2,\"type\":1,\"typeCode\":\"cash\",\"limitUseRegion\":[],\"sendChannel\":\"other\",\"useChannel\":\"mi_home\",\"useChannelDesc\":\"仅小米之家可用\",\"budgetApplyNo\":\"BR202403150014\",\"lineNum\":8960,\"bizPlatform\":3,\"serviceScene\":0,\"timesLimit\":1,\"workHourStandardPage\":10,\"sendScene\":\"11BFB15C0E2BA4D03DFF93D95C843D0E\",\"tags\":[],\"annualType\":0,\"checkoutStage\":2},\"validCode\":0,\"invalidReason\":\"不含可用优惠券商品\",\"couponGroupNo\":\"1_1_-1\",\"validSsuList\":[600003439],\"couponSsuExtInfo\":{}}},\"couponGroupInfoMap\":{}}";
        GetCheckoutCouponListV2Response response = GsonUtil.fromJson(str1, GetCheckoutCouponListV2Response.class);
        Mockito.when(mockCouponTradeDubboService.getCheckoutCouponListV2(any(GetCheckoutCouponListV2Request.class))).thenReturn(Result.success(response));
        Whitebox.setInternalState(couponTradeServiceProxy, "couponTradeDubboService", mockCouponTradeDubboService);

        // 整车
        String str="{\"cartList\":[{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600003439\",\"sku\":\"600003439\",\"packageId\":\"\",\"ssuId\":600003439,\"count\":1,\"standardPrice\":1000,\"cartPrice\":1000,\"prePrice\":100,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":1000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,205,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,61,62,500,501,502,503,504,505,2002,29,55],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false,\"canAdjustPrice\":false,\"selected\":true}],\"channel\":11,\"userId\":3150443380,\"couponIds\":[],\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"car\",\"getCouponList\":true,\"showType\":0,\"useDefaultCoupon\":true,\"submitType\":0}";
        CartPromotionRequest request = GsonUtil.fromJson(str, CartPromotionRequest.class);
        request.setChannel(ChannelEnum.CAR_VEHICLE.getValue());
        Result<CartPromotionResponse> result3 = promotionDubboService.cartCheckoutPromotion(request);
        Assertions.assertEquals(1, result3.getData().getCouponList().size());
        Whitebox.setInternalState(couponTradeServiceProxy, "couponTradeDubboService", couponTradeDubboService);
    }


    /**
     * 整车销售-常态销售-汽车定金券优惠
     */
    @Test
    public void test_cartCheckoutPromotion_0_booking_success(){

        carPromotionInstancePool.rebuildCacheTask();

        CouponTradeDubboService mockCouponTradeDubboService = PowerMockito.mock(CouponTradeDubboService.class);
        String str1 = "{\"noCodeCoupons\":{\"1011144392\":{\"couponBaseInfo\":{\"couponId\":1011144392,\"couponName\":\"SU7定金券\",\"configId\":198181,\"status\":\"unused\",\"startTime\":1739030400,\"endTime\":1774972799,\"couponType\":1,\"couponRangeDesc\":\"SU7定金券1\",\"couponRuleDesc\":\"满1件减1元优惠券\",\"promotionType\":4,\"promotionValue\":100,\"showUnit\":\"元\",\"maxReduce\":0,\"bottomType\":2,\"bottomPrice\":0,\"bottomCount\":1,\"postFree\":2,\"share\":2,\"type\":1,\"typeCode\":\"cash\",\"limitUseRegion\":[],\"sendChannel\":\"other\",\"useChannel\":\"mi_home\",\"useChannelDesc\":\"仅小米之家可用\",\"budgetApplyNo\":\"BR202403200004\",\"lineNum\":9013,\"bizPlatform\":3,\"serviceScene\":0,\"timesLimit\":1,\"workHourStandardPage\":10,\"sendScene\":\"11BFB15C0E2BA4D03DFF93D95C843D0E\",\"tags\":[],\"annualType\":0,\"checkoutStage\":1},\"validCode\":0,\"couponGroupNo\":\"1_1_-1\",\"validSsuList\":[600003439],\"couponSsuExtInfo\":{}}},\"couponGroupInfoMap\":{}}";
        GetCheckoutCouponListV2Response response = GsonUtil.fromJson(str1, GetCheckoutCouponListV2Response.class);
        Mockito.when(mockCouponTradeDubboService.getCheckoutCouponListV2(any(GetCheckoutCouponListV2Request.class))).thenReturn(Result.success(response));
        Whitebox.setInternalState(couponTradeServiceProxy, "couponTradeDubboService", mockCouponTradeDubboService);

        // 整车
        String str="{\"cartList\":[{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600003439\",\"sku\":\"600003439\",\"packageId\":\"\",\"ssuId\":600003439,\"count\":1,\"standardPrice\":1000,\"cartPrice\":1000,\"prePrice\":100,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":1000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,205,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,61,62,500,501,502,503,504,505,2002,29,55],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false,\"canAdjustPrice\":false,\"selected\":true}],\"channel\":11,\"userId\":3150443380,\"couponIds\":[],\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"car\",\"getCouponList\":true,\"showType\":0,\"useDefaultCoupon\":true,\"submitType\":0}";
        CartPromotionRequest request = GsonUtil.fromJson(str, CartPromotionRequest.class);
        request.setChannel(ChannelEnum.CAR_VEHICLE.getValue());
        Result<CartPromotionResponse> result3 = promotionDubboService.cartCheckoutPromotion(request);
        Assertions.assertEquals(1, result3.getData().getCouponList().size());
        Whitebox.setInternalState(couponTradeServiceProxy, "couponTradeDubboService", couponTradeDubboService);
    }

    /**
     * 整车销售-常态改配-选装基金活动优惠
     */
    @Test
    public void test_cartCheckoutPromotion_1_28_success(){

        carPromotionInstancePool.rebuildCacheTask();

        // 整车
        String str="{\"cartList\":[{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600016371\",\"sku\":\"600016371\",\"packageId\":\"\",\"ssuId\":600019901,\"count\":1,\"standardPrice\":700,\"cartPrice\":700,\"prePrice\":0,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600016359\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":700,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,205,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,61,62,500,501,502,503,504,505,2002,29,55],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false,\"canAdjustPrice\":false,\"selected\":true},{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600016359\",\"sku\":\"600016359\",\"packageId\":\"\",\"ssuId\":600016359,\"count\":1,\"standardPrice\":500,\"cartPrice\":500,\"prePrice\":200,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":500,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,205,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,61,62,500,501,502,503,504,505,2002,29,55],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false,\"canAdjustPrice\":false,\"selected\":true}],\"channel\":11,\"userId\":3150071208,\"couponIds\":[],\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"car\",\"getCouponList\":true,\"showType\":0,\"useDefaultCoupon\":true,\"submitType\":1,\"orderTime\":1749106110}";
        CartPromotionRequest request = GsonUtil.fromJson(str, CartPromotionRequest.class);
        request.setSubmitType(SubmitTypeEnum.REPLACE.getCode());
        request.setChannel(ChannelEnum.CAR_VEHICLE.getValue());
        Result<CartPromotionResponse> result3 = promotionDubboService.cartCheckoutPromotion(request);
        Assertions.assertEquals(2, result3.getData().getPromotions().size());
    }

    /**
     * 整车销售-常态改配-直降活动优惠
     */
    @Test
    public void test_cartCheckoutPromotion_1_20_success(){

        carPromotionInstancePool.rebuildCacheTask();

        // 整车
        String str="{\"cartList\":[{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600016371\",\"sku\":\"600016371\",\"packageId\":\"\",\"ssuId\":600016371,\"count\":1,\"standardPrice\":700,\"cartPrice\":700,\"prePrice\":0,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600016359\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":700,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,205,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,61,62,500,501,502,503,504,505,2002,29,55],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false,\"canAdjustPrice\":false,\"selected\":true},{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600016359\",\"sku\":\"600016359\",\"packageId\":\"\",\"ssuId\":600016359,\"count\":1,\"standardPrice\":500,\"cartPrice\":500,\"prePrice\":200,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":500,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,205,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,61,62,500,501,502,503,504,505,2002,29,55],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false,\"canAdjustPrice\":false,\"selected\":true}],\"channel\":11,\"userId\":3150071208,\"couponIds\":[],\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"car\",\"getCouponList\":true,\"showType\":0,\"useDefaultCoupon\":true,\"submitType\":1,\"orderTime\":1749106110}";
        CartPromotionRequest request = GsonUtil.fromJson(str, CartPromotionRequest.class);
        request.setSubmitType(SubmitTypeEnum.REPLACE.getCode());
        request.setChannel(ChannelEnum.CAR_VEHICLE.getValue());
        Result<CartPromotionResponse> result3 = promotionDubboService.cartCheckoutPromotion(request);
        Assertions.assertEquals(1, result3.getData().getPromotions().size());
    }

    /**
     * 整车销售-常态销售-选装基金活动优惠
     */
    @Test
    public void test_cartCheckoutPromotion_0_31_success(){

        carPromotionInstancePool.rebuildCacheTask();

        // 整车
        String str="{\"cartList\":[{\"reduceItemList\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"2221500235_0_buy\",\"ssuId\":\"600016401\",\"packageId\":\"\",\"count\":1,\"standardPrice\":3900,\"cartPrice\":3900,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":1703576077,\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":3900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":400,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"unitId\":\"\"},{\"reduceItemList\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"2221500235_1_buy\",\"ssuId\":\"600020446\",\"packageId\":\"\",\"count\":1,\"standardPrice\":3900,\"cartPrice\":3900,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":1703576077,\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":3900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":400,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"unitId\":\"\"}],\"clientId\":180100031052,\"userId\":1242170538,\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"mishop\",\"userIsFriend\":2,\"getCouponList\":true}";
        CartPromotionRequest request = GsonUtil.fromJson(str, CartPromotionRequest.class);
        request.setChannel(ChannelEnum.CAR_VEHICLE.getValue());
        Result<CartPromotionResponse> result3 = promotionDubboService.cartCheckoutPromotion(request);
        Assertions.assertEquals(1, result3.getData().getPromotions().size());
    }

    /**
     * 整车销售-常态销售-直降活动优惠
     */
    @Test
    public void test_cartCheckoutPromotion_0_20_success(){

        carPromotionInstancePool.rebuildCacheTask();

        // 整车
        String str="{\"cartList\":[{\"reduceItemList\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"2221500235_0_buy\",\"ssuId\":\"2230000702\",\"packageId\":\"\",\"count\":1,\"standardPrice\":3900,\"cartPrice\":3900,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":1703576077,\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":3900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":400,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"unitId\":\"\"}],\"clientId\":180100031052,\"userId\":1242170538,\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"mishop\",\"userIsFriend\":2,\"getCouponList\":true}";
        CartPromotionRequest request = GsonUtil.fromJson(str, CartPromotionRequest.class);
        request.setChannel(ChannelEnum.CAR_VEHICLE.getValue());
        Result<CartPromotionResponse> result3 = promotionDubboService.cartCheckoutPromotion(request);
        Assertions.assertEquals(1, result3.getData().getPromotions().size());
    }
}
