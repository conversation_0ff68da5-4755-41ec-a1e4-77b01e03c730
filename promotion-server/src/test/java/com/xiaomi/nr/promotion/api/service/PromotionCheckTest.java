package com.xiaomi.nr.promotion.api.service;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2023/11/28 15:32
 **/
public class PromotionCheckTest extends BaseTest {

    @Autowired
    public PromotionDubboService promotionDubboService;

    @Test
    public void checkoutPromotion_testCRMPrice() {
        String str = "{\n" +
                "    \"bargainSize\": 0,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": true,\n" +
                "            \"cannotUseEcard\": true,\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"cartPrice\": 1203,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"sku\": \"\",\n" +
                "            \"marketPrice\": 1203,\n" +
                "            \"ssuId\": 600000784\n,\n" +
                "            \"standardPrice\": 1203,\n" +
                "            \"itemId\": \"400000001\",\n" +
                "            \"count\": 1,\n" +
                "            \"childs\": [\n" +
                "            ],\n" +
                "            \"department\": 1,\n" +
                "            \"displayType\": 1,\n" +
                "            \"goodsType\": 2,\n" +
                "            \"groupId\": 0,\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"reduceList\": {},\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"source\": \"buy\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": true,\n" +
                "            \"cannotUseEcard\": true,\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"cartPrice\": 801,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"sku\": \"\",\n" +
                "            \"marketPrice\": 801,\n" +
                "            \"ssuId\": 600002303\n,\n" +
                "            \"standardPrice\": 801,\n" +
                "            \"itemId\": \"400000002\",\n" +
                "            \"count\": 1,\n" +
                "            \"childs\": [\n" +
                "            ],\n" +
                "            \"department\": 1,\n" +
                "            \"displayType\": 1,\n" +
                "            \"goodsType\": 2,\n" +
                "            \"groupId\": 0,\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"reduceList\": {},\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"source\": \"buy\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"channel\": 11,\n" +
                "    \"cityId\": 36,\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"\",\n" +
                "    \"noSaveDbSubmit\": false,\n" +
                "    \"orderId\": 5237211001999983,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"useRedPacket\": false,\n" +
                "    \"userId\": 1538121936,\n" +
                "    \"userIsFriend\": 0,\n" +
                "    \"userLevel\": \"vip\"\n" +
                "}";
        CheckoutPromotionRequest request = GsonUtil.fromJson(str, CheckoutPromotionRequest.class);
        long orderId = System.currentTimeMillis();
        request.setOrderId(orderId);

        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(">>>>>>>>>>>>>>>>>>>>>");
        System.out.println(GsonUtil.toJson(result));
    }
}
