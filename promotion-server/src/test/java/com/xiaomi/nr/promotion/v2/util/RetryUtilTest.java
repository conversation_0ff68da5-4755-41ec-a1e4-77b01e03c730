package com.xiaomi.nr.promotion.v2.util;

import com.xiaomi.nr.promotion.util.RetryUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.assertEquals;

/**
 * @author: zhangliwei6
 * @date: 2025/9/16 20:52
 * @description:
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
public class RetryUtilTest {

    private int index;

    @Test
    public void test() {
        try {
            RetryUtil.retryableRun(() -> testRetry(3), 3);
        } catch (Exception e) {
            log.info("test retry failed", e);
        }
        assertEquals(3, index);
    }

    private void testRetry(int count) {
        index++;
        if (index < count) {
            throw new RuntimeException("test retry");
        }
        log.info("test retry");
    }
}
