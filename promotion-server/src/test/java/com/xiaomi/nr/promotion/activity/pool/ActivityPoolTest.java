package com.xiaomi.nr.promotion.activity.pool;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import org.checkerframework.checker.units.qual.A;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 活动池缓存
 *
 * <AUTHOR>
 * @date 2021/6/9
 */
public class ActivityPoolTest extends BaseTest {
    @Autowired
    private ActivityPool activityPool;



    @Test
    public void testGetCheckoutPromotions() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        CartItem cartItem1 = new CartItem();
        cartItem1.setSku("22748");
        cartItemList.add(cartItem1);
//
//        CartItem cartItem2 = new CartItem();
//        cartItem2.setSku("15981");
//        cartItemList.add(cartItem2);
//
//        CartItem cartItem3 = new CartItem();
//        cartItem3.setSku("15570");
//        cartItemList.add(cartItem3);

        request.setCartList(cartItemList);
        request.setClientId(180100041075L);
        request.setOrgCode("MI0101");
        List<ActivityTool> toolList = activityPool.getCheckoutPromotions(request);
        Assert.assertNotNull(toolList);
    }

    @Test
    public void testGetPromotionById() {
        ActivityTool tool = activityPool.getPromotionById(207L);
        Assert.assertNotNull(tool);
    }

    @Test
    public void testGetCurrent() {
        List<ActivityTool> toolList = activityPool.getCurrent(Arrays.asList(207L));
        Assert.assertNotNull(toolList);
    }
}