package com.xiaomi.nr.promotion.rpc.promember;

import com.xiaomi.nr.promotion.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

public class MemberApiProxyTest extends BaseTest {

    @Autowired
    private MemberApiProxy proxy;

    @Test
    public void userProperty() throws ExecutionException, InterruptedException, TimeoutException {
        ListenableFuture<Boolean> future = proxy.checkIsMember(3150437322L);
        Boolean userPropertyResult = future.get(1000, TimeUnit.MILLISECONDS);
        System.out.println();
    }
}
