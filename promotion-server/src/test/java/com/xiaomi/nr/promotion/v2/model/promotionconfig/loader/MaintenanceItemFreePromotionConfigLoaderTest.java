package com.xiaomi.nr.promotion.v2.model.promotionconfig.loader;

import com.google.common.collect.Lists;
import com.xiaomi.nr.md.promotion.admin.api.constant.ScopeTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityScope;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ProductPolicy;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.MaintenanceItemFreeRule;
import com.xiaomi.nr.promotion.enums.CarIdentityTypeEnum;
import com.xiaomi.nr.promotion.model.promotionconfig.MaintenanceItemFreePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.loader.MaintenanceItemFreePromotionConfigLoader;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

//@ExtendWith(MockitoExtension.class)
public class MaintenanceItemFreePromotionConfigLoaderTest extends BaseTestV2 {

    @InjectMocks
    private MaintenanceItemFreePromotionConfigLoader loader;

    private MaintenanceItemFreePromotionConfig promotionConfig;

    private ActivityConfig activityConfig;

    @BeforeEach
    void setUp() {
        activityConfig = new ActivityConfig();
        activityConfig.setId(1L);
        promotionConfig = new MaintenanceItemFreePromotionConfig();
    }

    @Test
    public void test_load_success() throws BizError {
        // prepare test data
        MaintenanceItemFreePromotionConfig promotionConfig = new MaintenanceItemFreePromotionConfig();
        ActivityConfig activityConfig = new ActivityConfig();
        activityConfig.setId(1L);

        // mock rule
        MaintenanceItemFreeRule mockRule = MaintenanceItemFreeRule.builder()
                .description("desc")
                .carIdentityType(CarIdentityTypeEnum.CAR_SHOP_VIP.getCode())
                .carIdentityId("123")
                .promotionPrice(100L)
                .userLimitNum(5)
                .maxReduceAmount(50L)
                .build();

        // mock activity config
        ProductPolicy productPolicy = new ProductPolicy();
        productPolicy.setProductId(1L);
        activityConfig.setProductPolicyList(Arrays.asList(productPolicy));
        activityConfig.setRule(GsonUtil.toJson(mockRule));

        List<ActivityScope> activityScopeList = new ArrayList<>();
        ActivityScope scope = new ActivityScope();
        scope.setScopeType(ScopeTypeEnum.ORDER_FROM.code);
        scope.setScopeValue("1,2");
        activityScopeList.add(scope);
        activityConfig.setActivityScopeList(activityScopeList);

        // test
        loader.load(promotionConfig, activityConfig);

        // verify
        assert promotionConfig.getCarIdentityType().equals(CarIdentityTypeEnum.CAR_SHOP_VIP.getCode());
        assert promotionConfig.getPromotionPrice().equals(100L);
        assert promotionConfig.getMaxReduceAmount().equals(50L);
        assert promotionConfig.getCarIdentityId().equals("123");
        assert promotionConfig.getIdentityJoinLimitNum().equals(5);
        assert promotionConfig.getWorkOrderType().containsAll(Arrays.asList(1, 2));
    }

    @Test
    public void test_load_fail() throws BizError {
        // Case 1: activityConfig is null
        BizError bizError = Assertions.assertThrows(BizError.class, () -> loader.load(null, (ActivityConfig) null));
        Assertions.assertEquals("activityInfo is null", bizError.getMsg());

        // Case 2: invalid policy
        bizError = assertThrows(BizError.class, () ->
                loader.load(promotionConfig, activityConfig));
        assertEquals("policy is invalid", bizError.getMsg());

        // Case 3: invalid rule
        List<ProductPolicy> productPolicyList = Lists.newArrayList(new ProductPolicy());
        activityConfig.setProductPolicyList(productPolicyList);
        bizError = assertThrows(BizError.class, () ->
                loader.load(promotionConfig, activityConfig));
        assertEquals("rule is null", bizError.getMsg());
    }
}
