package com.xiaomi.nr.promotion.rpc.order;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.enums.TradeFromEnum;
import com.xiaomi.nr.promotion.resource.model.OrderStatus;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import groovy.util.logging.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2021/8/16
 */
@lombok.extern.slf4j.Slf4j
@Slf4j
public class SubmitOrderServiceProxyTest extends BaseTest {

    @Autowired
    private SubmitOrderServiceProxy submitOrderServiceProxy;

    @Test
    public void testGetOrderStatus() throws BizError {
        OrderStatus orderStatus = submitOrderServiceProxy.getOrderStatus(5256461039614035L);
        Assert.assertEquals(orderStatus, OrderStatus.NOT_FOUND);
    }

    @Test
    public void testGetOrderStatusV2ForCar() throws BizError {
        OrderStatus orderStatus = submitOrderServiceProxy.getOrderStatus(5256461028158289L, 3150458865L, TradeFromEnum.SHOP);
        log.info(orderStatus.toString());
        Assert.assertEquals(orderStatus, OrderStatus.COMMIT);
    }

    @Test
    public void testGetOrderStatusV2ForCarShop() throws BizError {
        OrderStatus orderStatus = submitOrderServiceProxy.getOrderStatus(5256461032008461L, 3150443410L, TradeFromEnum.SHOP);
        log.info(orderStatus.toString());
        Assert.assertEquals(orderStatus, OrderStatus.COMMIT);
    }

    @Test
    public void testGetOrderStatusV2ForCarMaintenance() throws BizError {
        OrderStatus orderStatus = submitOrderServiceProxy.getOrderStatus(5256461039614035L, 3150430536L, TradeFromEnum.SHOP);
        log.info(orderStatus.toString());
        Assert.assertEquals(orderStatus, OrderStatus.COMMIT);
    }
}