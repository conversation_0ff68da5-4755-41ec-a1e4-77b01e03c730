package com.xiaomi.nr.promotion.rpc.order;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.resource.model.OrderStatus;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2021/8/16
 */
public class SubmitOrderServiceProxyTest extends BaseTest {

    @Autowired
    private SubmitOrderServiceProxy submitOrderServiceProxy;

    @Test
    public void testGetOrderStatus() throws BizError {
        OrderStatus orderStatus = submitOrderServiceProxy.getOrderStatus(5210730376640065L);
        Assert.assertEquals(orderStatus, OrderStatus.NOT_FOUND);
    }
}