package com.xiaomi.nr.promotion.util;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * GsonUtil测试类
 *
 * <AUTHOR>
 * @date 2021/5/18
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class GsonUtilTest {

    @Test
    public void testToJson() {
        Map<String, Object> map = new HashMap<>();
        map.put("xiaomi", "yes");
        map.put("promotion", 100);
        System.out.println(GsonUtil.toJson(map));
        Assert.assertNotNull(GsonUtil.toJson(map));
    }

    @Test
    public void testFromJson() {
        String json = "{\"xiaomi\":\"yes\",\"promotion\":100}\n";
         Map map = GsonUtil.fromJson(json, Map.class);
         Assert.assertNotNull(map);
        Assert.assertEquals("yes", map.get("xiaomi"));
    }

    @Test
    public void testFromListJson() {
        String json = "[\"xiaomi\",\"yes\",\"promotion\"]";
        List<String> list = GsonUtil.fromListJson(json, String.class);
        Assert.assertNotNull(list);
        Assert.assertEquals("promotion", list.get(2));
    }

    @Test
    public void testFromMapJson() {
        String json = "{\"xiaomi\":\"yes\",\"promotion\":100}\n";
        Map<String, Object> map = GsonUtil.fromMapJson(json, Map.class);
        Assert.assertNotNull(map);
        Assert.assertEquals("yes", map.get("xiaomi"));
    }

    @Test
    public void testFromList(){
        List<BigInteger> activitiIds = new ArrayList<>();
        activitiIds.add(BigInteger.valueOf(100000));
        String json = GsonUtil.toJson(activitiIds);
        List<Long> list = GsonUtil.fromListJson(json, Long.class);
        Assert.assertNotNull(list);
        Assert.assertEquals(1, list.size());
    }
}