package com.xiaomi.nr.promotion.rpc.userProperty;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.resource.model.UserPropertyResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

public class UserPropertyProxyTest extends BaseTest {

    @Autowired
    private UserPropertyProxy proxy;

    @Test
    public void userProperty() throws ExecutionException, InterruptedException, TimeoutException {
        ListenableFuture<UserPropertyResult> future = proxy.userProperty(3150437322L);
        UserPropertyResult userPropertyResult = future.get(1000, TimeUnit.MILLISECONDS);
        System.out.println();
    }
}