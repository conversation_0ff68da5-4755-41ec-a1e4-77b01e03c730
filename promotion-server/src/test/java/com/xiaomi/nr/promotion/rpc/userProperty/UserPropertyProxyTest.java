package com.xiaomi.nr.promotion.rpc.userProperty;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.resource.model.UserPropertyResult;
import com.xiaomi.nr.promotion.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Slf4j
public class UserPropertyProxyTest extends BaseTest {

    @Autowired
    private UserPropertyProxy proxy;

    @Test
    public void userProperty() throws ExecutionException, InterruptedException, TimeoutException {
        ListenableFuture<UserPropertyResult> future = proxy.userProperty(3150437322L);
        UserPropertyResult userPropertyResult = future.get(1000, TimeUnit.MILLISECONDS);
        System.out.println();
    }

    @Test
    public void testUserPropertyV2() throws ExecutionException, InterruptedException, TimeoutException {
        ListenableFuture<UserPropertyResult> future = proxy.userPropertyV2(3150437322L, true, true, Lists.newArrayList("20250429001"));
        UserPropertyResult resp = future.get(1000, TimeUnit.MILLISECONDS);

        log.info("testUserPropertyV2 resp = {}", GsonUtil.toJson(resp));
    }
}