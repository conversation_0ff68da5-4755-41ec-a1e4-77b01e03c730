package com.xiaomi.nr.promotion.api.service;


import cn.hutool.crypto.digest.DigestUtil;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.dao.redis.ActivityRedisDao;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * PromotionDubboService 服务测试类
 *
 * <AUTHOR>
 * @date 2021/3/31
 */
@Slf4j
public class PromotionDubboServiceTest extends BaseTest {

    //@Reference(group = "staging", version = "1.0")
    @Autowired
    public PromotionDubboService promotionDubboService;

    @Autowired
    private ActivityRedisDao activityRedisDao;

    /**
     * 结算校验: 直降
     */
    @Test
    public void checkoutPromotion_test1() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("test_20853");
        cartItem1.setSku("20853");
        cartItem1.setPackageId("");
        cartItem1.setCount(1);
        cartItem1.setStandardPrice(180000L);
        cartItem1.setCartPrice(180000L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("easyrecycle");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1621846505L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(new ArrayList<>());
        cartItem1.setMarketPrice(180000L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("test2_18123");
        cartItem2.setSku("23569");
        cartItem2.setPackageId("");
        cartItem2.setCount(4);
        cartItem2.setStandardPrice(34900L);
        cartItem2.setCartPrice(34900L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setReduceList(new HashMap<>());
        cartItem2.setChilds(new ArrayList<>());
        cartItem2.setSource("");
        cartItem2.setSourceCode("");
        cartItem2.setProperties("");
        cartItem2.setParentItemId("");
        cartItem2.setUpdateTime(1610354801L);
        cartItem2.setDisplayType(1);
        cartItem2.setCannotJoinAct(false);
        cartItem2.setCannotUseCoupon(false);
        cartItem2.setAccessCode(new ArrayList<>());
        cartItem2.setMarketPrice(34900L);
        cartItem2.setCannotUseEcard(false);
        cartItem2.setMaxUseEcardAmount(0L);
        cartItem2.setCannotJoinActTypes(new ArrayList<>());
        cartItem2.setCannotUseCouponTypes(new ArrayList<>());
        cartItem2.setSaleSource("common");
        cartItem2.setCannotUseRedPacket(false);
        cartItem2.setGroupId(0L);
        cartItem2.setOnSaleBookingPrice(0L);
        cartItem2.setJoinOnsale(false);

        cartItemList.add(cartItem1);
        cartItemList.add(cartItem2);
        request.setEcardIds(Arrays.asList("20003536514207"));
        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setOrderId(2022110201L);
        request.setUserId(3150269625L);
        request.setOrgCode("3150269625");
        request.setUidType("");
        request.setSourceApi(SourceApi.SUBMIT);
        request.setNoSaveDbSubmit(false);
        request.setOrderTime(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()));
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 直降 套装
     */
    @Test
    public void checkoutPromotion_onsaleAct_test2() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("1211000001_buy");
        cartItem1.setPackageId("1211000001");
        cartItem1.setCount(1);

        List<CartItemChild> childs = new ArrayList<>();
        cartItem1.setChilds(childs);
        CartItemChild child1 = new CartItemChild();
        child1.setSku("19928");
        child1.setSellPrice(170000L);
        child1.setCount(1);
        child1.setOnSaleBookingPrice(0L);
        child1.setUnitId("");
        child1.setLowerPrice(0L);
        child1.setOriginalSellPrice(0L);
        childs.add(child1);

        CartItemChild child2 = new CartItemChild();
        child2.setSku("19927");
        child2.setSellPrice(200000L);
        child2.setCount(1);
        child2.setOnSaleBookingPrice(0L);
        child2.setUnitId("");
        child2.setLowerPrice(0L);
        child2.setOriginalSellPrice(0L);
        childs.add(child2);

        cartItem1.setStandardPrice(370000L);
        cartItem1.setCartPrice(370000L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());

        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1621846505L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(new ArrayList<>());
        cartItem1.setMarketPrice(370000L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150083511L);
        request.setOrgCode("MI0101");
        request.setUidType("");
        request.setSourceApi(SourceApi.CHECKOUT);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 结算校验: 满减13136,13745,13793
     */
    @Test
    public void checkoutPromotion_test2() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        //**************
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("MI0104");
        cartItem1.setSku("15668");
        cartItem1.setPackageId("");
        cartItem1.setCount(1);
        cartItem1.setStandardPrice(369900L);
        cartItem1.setCartPrice(369900L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1624344271L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(new ArrayList<>());
        cartItem1.setMarketPrice(399900L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        //***************
        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("8888888_0_gift_13896_2161200066");
        cartItem2.setSku("18123");
        cartItem2.setPackageId("");
        cartItem2.setCount(4);
        cartItem2.setStandardPrice(49800L);
        cartItem2.setCartPrice(49800L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setReduceList(new HashMap<>());
        cartItem2.setChilds(new ArrayList<>());
        cartItem2.setSource("bargain");
        cartItem2.setSourceCode("990523");
        cartItem2.setProperties("");
        cartItem2.setParentItemId("");
        cartItem2.setUpdateTime(1610354801L);
        cartItem2.setDisplayType(1);
        cartItem2.setCannotJoinAct(true);
        cartItem2.setCannotUseCoupon(true);
        cartItem2.setAccessCode(new ArrayList<>());
        cartItem2.setMarketPrice(49800L);
        cartItem2.setCannotUseEcard(false);
        cartItem2.setMaxUseEcardAmount(0L);
        cartItem2.setCannotJoinActTypes(new ArrayList<>());
        cartItem2.setCannotUseCouponTypes(new ArrayList<>());
        cartItem2.setSaleSource("common");
        cartItem2.setCannotUseRedPacket(false);
        cartItem2.setGroupId(1L);
        cartItem2.setOnSaleBookingPrice(0L);
        cartItem2.setJoinOnsale(false);

        cartItemList.add(cartItem1);
        cartItemList.add(cartItem2);
        request.setCartList(cartItemList);
        request.setOrderId(1423456L);
        request.setOrgCode("MI0104");
        request.setClientId(180100031052L);
        request.setUserId(3150269625L);
        request.setSourceApi(SourceApi.SUBMIT);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 结算校验: 赠品
     */
    @Test
    public void checkoutPromotion_test3() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2144600003_0_buy");
        cartItem1.setSku("8419");
        cartItem1.setPackageId("");
        cartItem1.setCount(2);
        cartItem1.setStandardPrice(59700L);
        cartItem1.setCartPrice(59700L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1610354801L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(new ArrayList<>());
        cartItem1.setMarketPrice(60000L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("2182100052_0_buy");
        cartItem2.setSku("15981");
        cartItem2.setPackageId("");
        cartItem2.setCount(5);
        cartItem2.setStandardPrice(289900L);
        cartItem2.setCartPrice(289900L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setReduceList(new HashMap<>());
        cartItem2.setChilds(new ArrayList<>());
        cartItem2.setSource("");
        cartItem2.setSourceCode("");
        cartItem2.setProperties("");
        cartItem2.setParentItemId("");
        cartItem2.setUpdateTime(1611038704L);
        cartItem2.setDisplayType(1);
        cartItem2.setCannotJoinAct(false);
        cartItem2.setCannotUseCoupon(false);
        cartItem2.setAccessCode(new ArrayList<>());
        cartItem2.setMarketPrice(289900L);
        cartItem2.setCannotUseEcard(false);
        cartItem2.setMaxUseEcardAmount(0L);
        cartItem2.setCannotJoinActTypes(new ArrayList<>());
        cartItem2.setCannotUseCouponTypes(new ArrayList<>());
        cartItem2.setSaleSource("common");
        cartItem2.setCannotUseRedPacket(false);
        cartItem2.setGroupId(0L);
        cartItem2.setOnSaleBookingPrice(0L);
        cartItem2.setJoinOnsale(false);
        cartItemList.add(cartItem2);

        CartItem cartItem3 = new CartItem();
        cartItem3.setItemId("8888888_0_gift_15570_2134700008");
        cartItem3.setSku("15570");
        cartItem3.setPackageId("");
        cartItem3.setCount(3);
        cartItem3.setStandardPrice(9800L);
        cartItem3.setCartPrice(8900L);
        cartItem3.setReduceAmount(0L);
        cartItem3.setReduceList(new HashMap<>());
        cartItem3.setChilds(new ArrayList<>());
        cartItem3.setSource("gift");
        cartItem3.setSourceCode("13825");
        cartItem3.setProperties("");
        cartItem3.setParentItemId("");
        cartItem3.setUpdateTime(0L);
        cartItem3.setDisplayType(1);
        cartItem3.setCannotJoinAct(true);
        cartItem3.setCannotUseCoupon(true);
        cartItem3.setAccessCode(new ArrayList<>());
        cartItem3.setMarketPrice(9800L);
        cartItem3.setCannotUseEcard(false);
        cartItem3.setMaxUseEcardAmount(0L);
        cartItem3.setCannotJoinActTypes(new ArrayList<>());
        cartItem3.setCannotUseCouponTypes(new ArrayList<>());
        cartItem3.setSaleSource("common");
        cartItem3.setCannotUseRedPacket(false);
        cartItem3.setGroupId(0L);
        cartItem3.setOnSaleBookingPrice(0L);
        cartItem3.setJoinOnsale(false);
        cartItemList.add(cartItem3);

        request.setCartList(cartItemList);
        request.setClientId(180100031055L);
        request.setUserId(3150075477L);
        request.setOrderId(0L);
        request.setBargainSize(10L);
        request.setNoSaveDbSubmit(false);
        request.setUseRedPacket(false);
        request.setCalculateRedpacket(false);
        request.setUseBeijingcoupon(false);
        request.setOrgCode("");
        request.setUidType("");
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 结算校验: 折扣13805
     */
    @Test
    public void checkoutPromotion_test4() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        //**************
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2182800003_0_buy");
        cartItem1.setSku("20211");
        cartItem1.setPackageId("");
        cartItem1.setCount(1);
        cartItem1.setStandardPrice(69900L);
        cartItem1.setCartPrice(69900L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1610354801L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(new ArrayList<>());
        cartItem1.setMarketPrice(69900L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);

        cartItemList.add(cartItem1);
        request.setCartList(cartItemList);
        request.setClientId(180100031055L);
        request.setUserId(3150075477L);
        request.setOrgCode("");
        request.setSourceApi(SourceApi.CHECKOUT);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        assert result.getCode() == 0;
    }

    @Test
    public void checkoutPromotion_ecard_test1() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2192100011_0_buy");
        cartItem1.setSku("23115");
        cartItem1.setPackageId("");
        cartItem1.setCount(1);
        cartItem1.setStandardPrice(99900L);
        cartItem1.setCartPrice(99900L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("base");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(0L);
        cartItem1.setDisplayType(0);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(new ArrayList<>());
        cartItem1.setMarketPrice(999L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItem1.setMaxUseEcardAmount(1L);

        //********************
        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("2144600003_0_buy");
        cartItem2.setSku("8419");
        cartItem2.setPackageId("");
        cartItem2.setCount(2);
        cartItem2.setStandardPrice(59700L);
        cartItem2.setCartPrice(59700L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setReduceList(new HashMap<>());
        cartItem2.setChilds(new ArrayList<>());
        cartItem2.setSource("");
        cartItem2.setSourceCode("");
        cartItem2.setProperties("");
        cartItem2.setParentItemId("");
        cartItem2.setUpdateTime(1610354801L);
        cartItem2.setDisplayType(1);
        cartItem2.setCannotJoinAct(false);
        cartItem2.setCannotUseCoupon(false);
        cartItem2.setAccessCode(new ArrayList<>());
        cartItem2.setMarketPrice(60000L);
        cartItem2.setCannotUseEcard(false);
        cartItem2.setMaxUseEcardAmount(0L);
        cartItem2.setCannotJoinActTypes(new ArrayList<>());
        cartItem2.setCannotUseCouponTypes(new ArrayList<>());
        cartItem2.setSaleSource("common");
        cartItem2.setCannotUseRedPacket(false);
        cartItem2.setGroupId(0L);
        cartItem2.setOnSaleBookingPrice(0L);
        cartItem2.setJoinOnsale(false);
        cartItem2.setMaxUseEcardAmount(1L);
        cartItemList.add(cartItem1);
        cartItemList.add(cartItem2);

        Map<String, EcardConsumeItemList> itemListMap = new HashMap<>();
        EcardConsumeItemList ecardConsumeItemList = new EcardConsumeItemList();
        List<EcardConsumeItem> ecardConsumeItems = new ArrayList<>();
        EcardConsumeItem ecardConsumeItem = new EcardConsumeItem();
        ecardConsumeItem.setSOrderId(1001L);
        ecardConsumeItem.setMoney(1L);
        ecardConsumeItems.add(ecardConsumeItem);
        ecardConsumeItemList.setConsumeList(ecardConsumeItems);
        ecardConsumeItemList.setTotalAmount(1L);

        EcardConsumeItemList ecardConsumeItemList2 = new EcardConsumeItemList();
        List<EcardConsumeItem> ecardConsumeItems2 = new ArrayList<>();
        EcardConsumeItem ecardConsumeItem2 = new EcardConsumeItem();
        ecardConsumeItem2.setSOrderId(1001L);
        ecardConsumeItem2.setMoney(1L);
        ecardConsumeItems2.add(ecardConsumeItem2);
        ecardConsumeItemList2.setConsumeList(ecardConsumeItems2);
        ecardConsumeItemList2.setTotalAmount(1L);

        itemListMap.put("20000520928686", ecardConsumeItemList);
        itemListMap.put("20030138228655", ecardConsumeItemList2);
        request.setEcardConsumeDetail(itemListMap);
        request.setEcardIds(Arrays.asList("20000520928686", "20030138228655"));
        request.setCartList(cartItemList);
        request.setClientId(180100041103L);
        request.setUserId(3150041197L);
        request.setOrderId(1001L);
        request.setSourceApi(SourceApi.CHECKOUT);
        request.setNoSaveDbSubmit(false);

        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 结算校验: 没有优惠
     */
    @Test
    public void checkoutPromotion_noPromotion_test() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        //**************
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("1182900005_1_buy");
        cartItem1.setSku("");
        cartItem1.setPackageId("1182900005");
        cartItem1.setCount(1);
        cartItem1.setStandardPrice(275000L);
        cartItem1.setCartPrice(275000L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        List<CartItemChild> childs = new ArrayList<>();
        cartItem1.setChilds(childs);
        CartItemChild child1 = new CartItemChild();
        child1.setSku("19928");
        child1.setSellPrice(90000L);
        child1.setCount(1);
        child1.setOnSaleBookingPrice(0L);
        child1.setUnitId("");
        child1.setLowerPrice(0L);
        child1.setOriginalSellPrice(0L);
        childs.add(child1);

        CartItemChild child2 = new CartItemChild();
        child2.setSku("19927");
        child2.setSellPrice(185000L);
        child2.setCount(1);
        child2.setOnSaleBookingPrice(0L);
        child2.setUnitId("");
        child2.setLowerPrice(0L);
        child2.setOriginalSellPrice(0L);
        childs.add(child2);

        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1623879228L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(new ArrayList<>());
        cartItem1.setMarketPrice(300000L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        cartItem1 = new CartItem();
        cartItem1.setItemId("2155100010_0_buy");
        cartItem1.setSku("12377");
        cartItem1.setPackageId("");
        cartItem1.setCount(1);
        cartItem1.setStandardPrice(34900L);
        cartItem1.setCartPrice(34900L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1623879228L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(new ArrayList<>());
        cartItem1.setMarketPrice(34900L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        request.setCartList(cartItemList);
        request.setClientId(180100031016L);
        request.setUserId(854007203L);
        request.setOrgCode("");
        request.setSourceApi(SourceApi.CHECKOUT);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 结算校验: 使用券ID 现金券
     */
    @Test
    public void checkoutPromotion_coupon_test() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        //**************
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2155100010_0_buy");
        cartItem1.setSku("20963");
        cartItem1.setPackageId("");
        cartItem1.setCount(1);
        cartItem1.setStandardPrice(34900L);
        cartItem1.setCartPrice(34900L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1623879198L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(new ArrayList<>());
        cartItem1.setMarketPrice(34900L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150063523L);
        request.setOrgCode("");
        request.setCouponIds(Arrays.asList(1009121506L));
        request.setSourceApi(SourceApi.CHECKOUT);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 结算校验: 使用券ID 抵扣券
     */
    @Test
    public void checkoutPromotion_coupon_test2() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        //**************
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2155100010_0_buy");
        cartItem1.setSku("12377");
        cartItem1.setPackageId("");
        cartItem1.setCount(1);
        cartItem1.setStandardPrice(34900L);
        cartItem1.setCartPrice(34900L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1623879062L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(new ArrayList<>());
        cartItem1.setMarketPrice(34900L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150015466L);
        request.setOrgCode("");
        request.setOrderId(6004L);
        request.setCouponCodes(Arrays.asList("****************"));
        request.setSourceApi(SourceApi.SUBMIT);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 提交
     */
    @Test
    public void testSubmitPromotion() {
        SubmitPromotionRequest request = new SubmitPromotionRequest();
        request.setOrderId(2022110201L);
        request.setUserId(3150269625L);
        Result<SubmitPromotionResponse> result = promotionDubboService.submitPromotion(request);
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(Boolean.TRUE, result.getData().getResult());
        Assert.assertEquals(0, result.getCode());
    }

    /**
     * 回滚
     */
    @Test
    public void testRollbackPromotion() {
        RollbackPromotionRequest request = new RollbackPromotionRequest();
        request.setOrderId(1711458278960L);
        request.setUserId(12345678L);
        Result<RollbackPromotionResponse> result = promotionDubboService.rollbackPromotion(request);
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(Boolean.TRUE, result.getData().getResult());
        Assert.assertEquals(0, result.getCode());
    }

    /**
     * 满减+红包 submit
     */
    @Test
    public void checkoutPromotion_test5() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2154300001_0_buy");
        cartItem1.setSku("11960");
        cartItem1.setPackageId("");
        cartItem1.setCount(2);
        cartItem1.setStandardPrice(180000L);
        cartItem1.setCartPrice(180000L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1610354801L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(new ArrayList<>());
        cartItem1.setMarketPrice(199900L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);
        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150063523L);
        request.setBargainSize(10L);
        request.setOrgCode("");
        request.setSourceApi(SourceApi.CHECKOUT);
        request.setCalculateRedpacket(true);
        request.setUseRedPacket(true);
        request.setOrderId(6003L);
        request.setSourceApi(SourceApi.SUBMIT);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 券 ID
     */
    @Test
    public void checkoutPromotion_couponId1() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2191200015_0_buy");
        cartItem1.setSku("22029");
        cartItem1.setPackageId("");
        cartItem1.setCount(2);
        cartItem1.setStandardPrice(199900L);
        cartItem1.setCartPrice(199900L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1623037292L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(Arrays.asList("doorstep_access_2017"));
        cartItem1.setMarketPrice(199900L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("2202300005_0_insurance_@2191200015_0_buy");
        cartItem2.setSku("29660");
        cartItem2.setPackageId("");
        cartItem2.setCount(2);
        cartItem2.setStandardPrice(6900L);
        cartItem2.setCartPrice(6900L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setReduceList(new HashMap<>());
        cartItem2.setChilds(new ArrayList<>());
        cartItem2.setSource("insurance");
        cartItem2.setSourceCode("");
        cartItem2.setProperties("");
        cartItem2.setParentItemId("");
        cartItem2.setUpdateTime(1623037292L);
        cartItem2.setDisplayType(1);
        cartItem2.setCannotJoinAct(false);
        cartItem2.setCannotUseCoupon(false);
        cartItem2.setAccessCode(Arrays.asList("doorstep_access_2017"));
        cartItem2.setMarketPrice(7000L);
        cartItem2.setCannotUseEcard(false);
        cartItem2.setMaxUseEcardAmount(0L);
        cartItem2.setCannotJoinActTypes(new ArrayList<>());
        cartItem2.setCannotUseCouponTypes(new ArrayList<>());
        cartItem2.setSaleSource("insurance");
        cartItem2.setCannotUseRedPacket(false);
        cartItem2.setGroupId(0L);
        cartItem2.setOnSaleBookingPrice(0L);
        cartItem2.setJoinOnsale(false);
        cartItemList.add(cartItem2);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150075418L);
        request.setBargainSize(10L);
        request.setOrgCode("");
        request.setCouponIds(Arrays.asList(1009103432L));
        request.setSourceApi(SourceApi.CHECKOUT);
        request.setCalculateRedpacket(true);
        request.setUseRedPacket(true);
//        request.setOrderId(21543000032L);
//        request.setSourceApi(SourceApi.SUBMIT);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 下单试算checkout
     */
    @Test
    public void checkoutPromotion_test6() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2174200042_0_buy");
        cartItem1.setSku("17389");
        cartItem1.setPackageId("");
        cartItem1.setCount(1);
        cartItem1.setStandardPrice(288800L);
        cartItem1.setCartPrice(15000L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1623037292L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(Arrays.asList("doorstep_access_2017"));
        cartItem1.setMarketPrice(288800L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150075418L);
        request.setBargainSize(10L);
        request.setOrgCode("");
        request.setCalculateRedpacket(true);
        request.setUseRedPacket(true);
        request.setOrderId(0L);
        request.setSourceApi(SourceApi.SUBMIT);
        request.setNoSaveDbSubmit(true);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 带礼品卡
     */
    @Test
    public void checkoutPromotion_test7() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2174200042_0_buy");
        cartItem1.setSku("17389");
        cartItem1.setPackageId("");
        cartItem1.setCount(1);
        cartItem1.setStandardPrice(288800L);
        cartItem1.setCartPrice(15000L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1623037292L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(Arrays.asList("doorstep_access_2017"));
        cartItem1.setMarketPrice(288800L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150063523L);
        request.setBargainSize(10L);
        request.setOrgCode("");
        request.setCalculateRedpacket(true);
        request.setUseRedPacket(true);
        request.setOrderId(6660001L);
        request.setSourceApi(SourceApi.SUBMIT);
        request.setNoSaveDbSubmit(false);
        request.setEcardIds(Arrays.asList("10010813615391"));
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 每满件减钱活动，活动id 990276
     * 测试商品
     * -单品 2184600004 数量1
     * -套装 1204900014 数量1
     */
    @Test
    public void checkoutPromotion_reduce() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        // 单品
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2184600004_0_buy");
        cartItem1.setSku("21777");
        cartItem1.setPackageId("");
        cartItem1.setCount(1);
        cartItem1.setStandardPrice(199900L);
        cartItem1.setCartPrice(199900L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1623037292L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setMarketPrice(199900L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        // 套装
        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("1204900014_1_buy");
        cartItem2.setPackageId("1204900014");
        cartItem2.setCount(1);
        cartItem2.setStandardPrice(299899L);
        cartItem2.setCartPrice(299899L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setReduceList(new HashMap<>());
        CartItemChild child1 = new CartItemChild();
        child1.setSku("16144");
        child1.setSellPrice(199911L);
        child1.setCount(1);
        CartItemChild child2 = new CartItemChild();
        child2.setSku("16144");
        child2.setSellPrice(99988L);
        child2.setCount(1);
        cartItem2.setChilds(Arrays.asList(child1, child2));
        cartItem2.setSource("insurance");
        cartItem2.setSourceCode("");
        cartItem2.setProperties("");
        cartItem2.setParentItemId("");
        cartItem2.setUpdateTime(1623037292L);
        cartItem2.setDisplayType(1);
        cartItem2.setCannotJoinAct(false);
        cartItem2.setCannotUseCoupon(false);
        cartItem2.setMarketPrice(299999L);
        cartItem2.setCannotUseEcard(false);
        cartItem2.setMaxUseEcardAmount(0L);
        cartItem2.setCannotJoinActTypes(new ArrayList<>());
        cartItem2.setCannotUseCouponTypes(new ArrayList<>());
        cartItem2.setSaleSource("common");
        cartItem2.setCannotUseRedPacket(false);
        cartItem2.setGroupId(0L);
        cartItem2.setOnSaleBookingPrice(0L);
        cartItem2.setJoinOnsale(false);
        cartItemList.add(cartItem2);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150269625L);
        request.setBargainSize(10L);
        request.setOrgCode("");
        //request.setCouponIds(Arrays.asList(1009103432L));
        request.setSourceApi(SourceApi.CHECKOUT);
        request.setCalculateRedpacket(true);
        request.setUseRedPacket(true);
//        request.setOrderId(21543000032L);
//        request.setSourceApi(SourceApi.SUBMIT);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 测试满减，活动99382配置了活动密码
     * 预期：不应该带出该活动
     */
    @Test
    public void checkoutPromotion_reduce2() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        // 单品
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2190800012_0_buy");
        cartItem1.setSku("22013");
        cartItem1.setPackageId("");
        cartItem1.setCount(2);
        cartItem1.setStandardPrice(329900L);
        cartItem1.setCartPrice(329900L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1630402482L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setMarketPrice(329900L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150070208L);
        request.setBargainSize(10L);
        request.setOrgCode("");
        request.setSourceApi(SourceApi.CHECKOUT);
        request.setCalculateRedpacket(false);
        request.setUseRedPacket(false);
//        request.setOrderId(21543000032L);
//        request.setSourceApi(SourceApi.SUBMIT);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 明码劵测试
     */
    @Test
    public void checkoutPromotion_codecouponTest1() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        // 单品
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2190800012_0_buy");
        cartItem1.setSku("22013");
        cartItem1.setPackageId("");
        cartItem1.setCount(2);
        cartItem1.setStandardPrice(329900L);
        cartItem1.setCartPrice(329900L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1630402482L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setMarketPrice(329900L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        request.setCartList(cartItemList);
        request.setClientId(180100031055L);
        request.setUserId(3150070208L);
        request.setBargainSize(10L);
        request.setOrgCode("");
        // 明码劵
        request.setCouponCodes(Arrays.asList("9408672823385668"));
        request.setSourceApi(SourceApi.CHECKOUT);
        request.setCalculateRedpacket(false);
        request.setUseRedPacket(false);
//        request.setOrderId(21543000032L);
//        request.setSourceApi(SourceApi.SUBMIT);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 下单试算，订单优惠分摊，单品
     */
    @Test
    public void checkoutPromotion_testSubmitShare1() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2174200042_0_buy");
        cartItem1.setSku("16196");
        cartItem1.setPackageId("");
        cartItem1.setCount(3);
        cartItem1.setStandardPrice(29900L);
        cartItem1.setCartPrice(29900L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1623037292L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(Arrays.asList("doorstep_access_2017"));
        cartItem1.setMarketPrice(29900L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150075418L);
        request.setBargainSize(10L);
        request.setOrgCode("");
        request.setCalculateRedpacket(true);
        request.setUseRedPacket(true);
        request.setOrderId(0L);
        request.setSourceApi(SourceApi.SUBMIT);
        request.setNoSaveDbSubmit(true);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 下单试算，订单优惠分摊，套装
     */
    @Test
    public void checkoutPromotion_testSubmitShare2() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        CartItem cartItem1 = new CartItem();
//        cartItem1.setItemId("1204900001_0_buy");
//        cartItem1.setPackageId("1204900005");
//        cartItem1.setCount(3);
//        cartItem1.setStandardPrice(204800L);
//        cartItem1.setCartPrice(204800L);
//        cartItem1.setReduceAmount(0L);
//        cartItem1.setReduceList(new HashMap<>());
//        cartItem1.setChilds(new ArrayList<>());
//        CartItemChild child1 = new CartItemChild();
//        child1.setSku("11961");
//        child1.setSellPrice(199900L);
//        child1.setCount(1);
//        CartItemChild child2 = new CartItemChild();
//        child2.setSku("11552");
//        child2.setSellPrice(4900L);
//        child2.setCount(1);
        cartItem1.setItemId("1194200001_1_buy");
        cartItem1.setPackageId("1194200001");
        cartItem1.setCount(1);
        cartItem1.setStandardPrice(130100L);
        cartItem1.setCartPrice(130100L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        CartItemChild child1 = new CartItemChild();
        child1.setSku("19360");
        child1.setSellPrice(130000L);
        child1.setCount(1);
        CartItemChild child2 = new CartItemChild();
        child2.setSku("13910");
        child2.setSellPrice(100L);
        child2.setCount(1);
        cartItem1.setChilds(Arrays.asList(child1, child2));
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1623037292L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(Arrays.asList("doorstep_access_2017"));
        cartItem1.setMarketPrice(204800L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150040115L);
        request.setBargainSize(10L);
        request.setOrgCode("");
        request.setCalculateRedpacket(true);
        request.setUseRedPacket(true);
        request.setOrderId(0L);
        request.setCouponIds(Arrays.asList(1009116069L));
        request.setSourceApi(SourceApi.SUBMIT);
        request.setNoSaveDbSubmit(true);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 订单优惠分摊，单品，直降
     */
    @Test
    public void checkoutPromotion_testSubmitShare3() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2174200042_0_buy");
        cartItem1.setSku("19360");
        cartItem1.setPackageId("");
        cartItem1.setCount(2);
        cartItem1.setStandardPrice(399900L);
        cartItem1.setCartPrice(399900L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1623037292L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(Arrays.asList("doorstep_access_2017"));
        cartItem1.setMarketPrice(409900L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150075418L);
        request.setBargainSize(10L);
        request.setOrgCode("");
        request.setCalculateRedpacket(true);
        request.setUseRedPacket(true);
        request.setOrderId(0L);
        request.setSourceApi(SourceApi.SUBMIT);
        request.setNoSaveDbSubmit(true);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 带劵
     */
    @Test
    public void checkoutPromotion_testSubmitShare4() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();
        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2190800008_0_buy");
        cartItem1.setSku("22006");
        cartItem1.setPackageId("");
        cartItem1.setCount(2);
        cartItem1.setStandardPrice(99900L);
        cartItem1.setCartPrice(99900L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setReduceList(new HashMap<>());
        cartItem1.setChilds(new ArrayList<>());
        cartItem1.setSource("");
        cartItem1.setSourceCode("");
        cartItem1.setProperties("");
        cartItem1.setParentItemId("");
        cartItem1.setUpdateTime(1623037292L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotJoinAct(false);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setAccessCode(Arrays.asList("doorstep_access_2017"));
        cartItem1.setMarketPrice(409900L);
        cartItem1.setCannotUseEcard(false);
        cartItem1.setMaxUseEcardAmount(0L);
        cartItem1.setCannotJoinActTypes(new ArrayList<>());
        cartItem1.setCannotUseCouponTypes(new ArrayList<>());
        cartItem1.setSaleSource("common");
        cartItem1.setCannotUseRedPacket(false);
        cartItem1.setGroupId(0L);
        cartItem1.setOnSaleBookingPrice(0L);
        cartItem1.setJoinOnsale(false);
        cartItemList.add(cartItem1);

        request.setCartList(cartItemList);
        request.setClientId(180100041075L);
        request.setUserId(3150040115L);
        request.setCouponIds(Arrays.asList(1009116039L));
        request.setBargainSize(10L);
        request.setOrgCode("MI0601");
        request.setCalculateRedpacket(true);
        request.setUseRedPacket(true);
        request.setOrderId(0L);
        request.setSourceApi(SourceApi.SUBMIT);
        request.setNoSaveDbSubmit(true);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 测试json
     */
    @Test
    public void checkoutPromotion_testCase() {
        CheckoutPromotionRequest request = GsonUtil.fromJson("{\"sourceApi\":1,\"cartList\":[{\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"2181500039_0_contract\",\"sku\":\"19171\",\"packageId\":\"\",\"count\":1,\"standardPrice\":200000,\"cartPrice\":200000,\"reduceAmount\":0,\"source\":\"contract\",\"sourceCode\":\"\",\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":1658482397,\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":200000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"saleSource\":\"contract\",\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[{\"typeCode\":\"contract\",\"maxAmount\":378000,\"amount\":0}]}],\"clientId\":180100031052,\"userId\":3150040115,\"bargainSize\":10,\"useRedPacket\":false,\"calculateRedpacket\":true,\"getCouponList\":true,\"orgCode\":\"\",\"uidType\":\"\",\"cityId\":36,\"shoppingMode\":0,\"showType\":1,\"shipmentId\":2,\"shipmentExpense\":0,\"useDefaultCoupon\":false,\"useBeijingcoupon\":false,\"globalBusinessPartner\":\"\",\"couponIds\":[1009330336]}", CheckoutPromotionRequest.class);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    @Test
    public void checkoutPromotionV2_testCase() {
        String str =
                "" +
                        "  {\n" +
                        "    \"bargainSize\": 10,\n" +
                        "    \"clientId\": 180100031055,\n" +
                        "    \"uidType\": \"\",\n" +
                        "    \"getCouponList\": true,\n" +
                        "    \"shipmentExpense\": 0,\n" +
                        "    \"cityId\": 205,\n" +
                        "    \"userId\": 3150058766,\n" +
                        "    \"useRedPacket\": true,\n" +
                        "    \"useDefaultCoupon\": true,\n" +
                        "    \"globalBusinessPartner\": \"\",\n" +
                        "    \"orgCode\": \"\",\n" +
                        "    \"shipmentId\": 2,\n" +
                        "    \"showType\": 1,\n" +
                        "    \"useBeijingcoupon\": false,\n" +
                        "    \"calculateRedpacket\": true,\n" +
                        "    \"cartList\": [\n" +
                        "      {\n" +
                        "        \"joinOnsale\": false,\n" +
                        "        \"marketPrice\": 10000,\n" +
                        "        \"groupId\": 0,\n" +
                        "        \"reduceAmount\": 0,\n" +
                        "        \"source\": \"\",\n" +
                        "        \"saleSources\": [],\n" +
                        "        \"childs\": [\n" +
                        "          {\n" +
                        "            \"onSaleBookingPrice\": 0,\n" +
                        "            \"lowerPrice\": 0,\n" +
                        "            \"cartPrice\": 0,\n" +
                        "            \"count\": 1,\n" +
                        "            \"storepriceReduce\": 0,\n" +
                        "            \"unitId\": \"\",\n" +
                        "            \"sellPrice\": 5000,\n" +
                        "            \"originalSellPrice\": 5000,\n" +
                        "            \"onsaleReduce\": 0,\n" +
                        "            \"sku\": \"60364\"\n" +
                        "          },\n" +
                        "          {\n" +
                        "            \"onSaleBookingPrice\": 0,\n" +
                        "            \"lowerPrice\": 0,\n" +
                        "            \"cartPrice\": 0,\n" +
                        "            \"count\": 1,\n" +
                        "            \"storepriceReduce\": 0,\n" +
                        "            \"unitId\": \"\",\n" +
                        "            \"sellPrice\": 5000,\n" +
                        "            \"originalSellPrice\": 5000,\n" +
                        "            \"onsaleReduce\": 0,\n" +
                        "            \"sku\": \"60364\"\n" +
                        "          }\n" +
                        "        ],\n" +
                        "        \"oriItemId\": \"\",\n" +
                        "        \"sourceCode\": \"\",\n" +
                        "        \"reduceDetailList\": {},\n" +
                        "        \"parentItemId\": \"\",\n" +
                        "        \"onSaleBookingPrice\": 0,\n" +
                        "        \"cartPrice\": 10000,\n" +
                        "        \"accessCode\": [],\n" +
                        "        \"cannotUseCoupon\": false,\n" +
                        "        \"unitId\": \"\",\n" +
                        "        \"orderItemReduceList\": {},\n" +
                        "        \"onsaleReduce\": 0,\n" +
                        "        \"sku\": \"\",\n" +
                        "        \"checkoutPrice\": 0,\n" +
                        "        \"originalCartPrice\": 0,\n" +
                        "        \"cannotUseRedPacket\": false,\n" +
                        "        \"packageId\": \"1222200140\",\n" +
                        "        \"count\": 1,\n" +
                        "        \"standardPrice\": 10000,\n" +
                        "        \"updateTime\": 1666097378,\n" +
                        "        \"changePriceActType\": 0,\n" +
                        "        \"itemId\": \"1222200140_1_buy\",\n" +
                        "        \"cannotJoinAct\": false,\n" +
                        "        \"displayType\": 1,\n" +
                        "        \"cannotUseEcard\": false,\n" +
                        "        \"saleSource\": \"common\",\n" +
                        "        \"storepriceReduce\": 0,\n" +
                        "        \"properties\": \"\",\n" +
                        "        \"maxUseEcardAmount\": 0\n" +
                        "      },\n" +
                        "      {\n" +
                        "        \"joinOnsale\": false,\n" +
                        "        \"marketPrice\": 68889,\n" +
                        "        \"groupId\": 0,\n" +
                        "        \"reduceAmount\": 0,\n" +
                        "        \"source\": \"\",\n" +
                        "        \"saleSources\": [],\n" +
                        "        \"childs\": [\n" +
                        "          {\n" +
                        "            \"onSaleBookingPrice\": 0,\n" +
                        "            \"lowerPrice\": 0,\n" +
                        "            \"cartPrice\": 0,\n" +
                        "            \"count\": 1,\n" +
                        "            \"storepriceReduce\": 0,\n" +
                        "            \"unitId\": \"\",\n" +
                        "            \"sellPrice\": 68888,\n" +
                        "            \"originalSellPrice\": 0,\n" +
                        "            \"onsaleReduce\": 0,\n" +
                        "            \"sku\": \"60379\"\n" +
                        "          },\n" +
                        "          {\n" +
                        "            \"onSaleBookingPrice\": 0,\n" +
                        "            \"lowerPrice\": 0,\n" +
                        "            \"cartPrice\": 0,\n" +
                        "            \"count\": 1,\n" +
                        "            \"storepriceReduce\": 0,\n" +
                        "            \"unitId\": \"\",\n" +
                        "            \"sellPrice\": 1,\n" +
                        "            \"originalSellPrice\": 0,\n" +
                        "            \"onsaleReduce\": 0,\n" +
                        "            \"sku\": \"60359\"\n" +
                        "          }\n" +
                        "        ],\n" +
                        "        \"oriItemId\": \"\",\n" +
                        "        \"sourceCode\": \"\",\n" +
                        "        \"reduceDetailList\": {},\n" +
                        "        \"parentItemId\": \"\",\n" +
                        "        \"onSaleBookingPrice\": 0,\n" +
                        "        \"cartPrice\": 68889,\n" +
                        "        \"accessCode\": [],\n" +
                        "        \"cannotUseCoupon\": false,\n" +
                        "        \"unitId\": \"\",\n" +
                        "        \"orderItemReduceList\": {},\n" +
                        "        \"onsaleReduce\": 0,\n" +
                        "        \"sku\": \"\",\n" +
                        "        \"checkoutPrice\": 0,\n" +
                        "        \"originalCartPrice\": 0,\n" +
                        "        \"cannotUseRedPacket\": false,\n" +
                        "        \"packageId\": \"1222200208\",\n" +
                        "        \"count\": 1,\n" +
                        "        \"standardPrice\": 68889,\n" +
                        "        \"updateTime\": 1666097406,\n" +
                        "        \"changePriceActType\": 0,\n" +
                        "        \"itemId\": \"1222200208_1_buy\",\n" +
                        "        \"cannotJoinAct\": false,\n" +
                        "        \"displayType\": 1,\n" +
                        "        \"cannotUseEcard\": false,\n" +
                        "        \"saleSource\": \"common\",\n" +
                        "        \"storepriceReduce\": 0,\n" +
                        "        \"properties\": \"\",\n" +
                        "        \"maxUseEcardAmount\": 0\n" +
                        "      },\n" +
                        "      {\n" +
                        "        \"joinOnsale\": false,\n" +
                        "        \"marketPrice\": 10000,\n" +
                        "        \"groupId\": 0,\n" +
                        "        \"reduceAmount\": 0,\n" +
                        "        \"source\": \"\",\n" +
                        "        \"saleSources\": [],\n" +
                        "        \"oriItemId\": \"\",\n" +
                        "        \"sourceCode\": \"\",\n" +
                        "        \"reduceDetailList\": {},\n" +
                        "        \"parentItemId\": \"\",\n" +
                        "        \"onSaleBookingPrice\": 0,\n" +
                        "        \"cartPrice\": 10000,\n" +
                        "        \"accessCode\": [],\n" +
                        "        \"cannotUseCoupon\": false,\n" +
                        "        \"unitId\": \"\",\n" +
                        "        \"orderItemReduceList\": {},\n" +
                        "        \"onsaleReduce\": 0,\n" +
                        "        \"sku\": \"60364\",\n" +
                        "        \"checkoutPrice\": 0,\n" +
                        "        \"originalCartPrice\": 0,\n" +
                        "        \"cannotUseRedPacket\": false,\n" +
                        "        \"packageId\": \"\",\n" +
                        "        \"count\": 1,\n" +
                        "        \"standardPrice\": 10000,\n" +
                        "        \"updateTime\": 1666097357,\n" +
                        "        \"changePriceActType\": 0,\n" +
                        "        \"itemId\": \"2221000323_0_buy\",\n" +
                        "        \"cannotJoinAct\": false,\n" +
                        "        \"displayType\": 1,\n" +
                        "        \"cannotUseEcard\": false,\n" +
                        "        \"saleSource\": \"common\",\n" +
                        "        \"storepriceReduce\": 0,\n" +
                        "        \"properties\": \"\",\n" +
                        "        \"maxUseEcardAmount\": 0\n" +
                        "      },\n" +
                        "      {\n" +
                        "        \"joinOnsale\": false,\n" +
                        "        \"marketPrice\": 5899,\n" +
                        "        \"groupId\": 0,\n" +
                        "        \"reduceAmount\": 0,\n" +
                        "        \"source\": \"\",\n" +
                        "        \"saleSources\": [],\n" +
                        "        \"oriItemId\": \"\",\n" +
                        "        \"sourceCode\": \"\",\n" +
                        "        \"reduceDetailList\": {},\n" +
                        "        \"parentItemId\": \"\",\n" +
                        "        \"onSaleBookingPrice\": 0,\n" +
                        "        \"cartPrice\": 5899,\n" +
                        "        \"accessCode\": [],\n" +
                        "        \"cannotUseCoupon\": false,\n" +
                        "        \"unitId\": \"\",\n" +
                        "        \"orderItemReduceList\": {},\n" +
                        "        \"onsaleReduce\": 0,\n" +
                        "        \"sku\": \"60359\",\n" +
                        "        \"checkoutPrice\": 0,\n" +
                        "        \"originalCartPrice\": 0,\n" +
                        "        \"cannotUseRedPacket\": false,\n" +
                        "        \"packageId\": \"\",\n" +
                        "        \"count\": 1,\n" +
                        "        \"standardPrice\": 5899,\n" +
                        "        \"updateTime\": 1666097337,\n" +
                        "        \"changePriceActType\": 0,\n" +
                        "        \"itemId\": \"2221000330_0_buy\",\n" +
                        "        \"cannotJoinAct\": false,\n" +
                        "        \"displayType\": 1,\n" +
                        "        \"cannotUseEcard\": false,\n" +
                        "        \"saleSource\": \"common\",\n" +
                        "        \"storepriceReduce\": 0,\n" +
                        "        \"properties\": \"\",\n" +
                        "        \"maxUseEcardAmount\": 0\n" +
                        "      }\n" +
                        "    ],\n" +
                        "    \"shoppingMode\": 0,\n" +
                        "    \"userIsFriend\": 2\n" +
                        "  }\n" +
                        "";
        CheckoutPromotionV2Request request = GsonUtil.fromJson(str, CheckoutPromotionV2Request.class);
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    @Test
    public void checkoutPromotion_testJson() {
        String str =
                "{\"sourceApi\":2,\"cartList\":[{\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"2154300020_0_buy\",\"sku\":\"11855\",\"packageId\":\"\",\"count\":2,\"standardPrice\":2002,\"cartPrice\":2002,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":1677741721,\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[\"doorstep_access_2017\"],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":2002,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"unitId\":\"\"},{\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"8888888_0_gift_9988813_1_2165100001\",\"sku\":\"14273\",\"packageId\":\"\",\"count\":4,\"standardPrice\":439900,\"cartPrice\":439900,\"reduceAmount\":0,\"source\":\"gift\",\"sourceCode\":\"9988813\",\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":1677741722,\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[\"doorstep_access_2017\"],\"cannotJoinAct\":true,\"cannotUseCoupon\":true,\"marketPrice\":439900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":1,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"unitId\":\"\"}],\"clientId\":180100031055,\"userId\":3150067909,\"couponIds\":[1010006282,1010002999],\"orderId\":0,\"bargainSize\":0,\"noSaveDbSubmit\":true,\"ecardConsumeDetail\":{},\"useRedPacket\":true,\"calculateRedpacket\":false,\"useBeijingcoupon\":false,\"orgCode\":\"\",\"uidType\":\"\",\"cityId\":36,\"shoppingMode\":0,\"shipmentId\":2,\"shipmentExpense\":1000,\"globalBusinessPartner\":\"\",\"fromPriceProtect\":false,\"userIsFriend\":1}";
        CheckoutPromotionRequest request = GsonUtil.fromJson(str, CheckoutPromotionRequest.class);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));


        assert result.getCode() == 0;
    }

    @Test
    public void checkoutPromotion_testAuto() {
        String str = "{\n" +
                "    \"bargainSize\": 10,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                1,\n" +
                "                2,\n" +
                "                3,\n" +
                "                4,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                21,\n" +
                "                -1,\n" +
                "                8,\n" +
                "                5,\n" +
                "                9,\n" +
                "                27,\n" +
                "                -1,\n" +
                "                7,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                10,\n" +
                "                23,\n" +
                "                26,\n" +
                "                22,\n" +
                "                25,\n" +
                "                500,\n" +
                "                501,\n" +
                "                502,\n" +
                "                503,\n" +
                "                504,\n" +
                "                505,\n" +
                "                -1\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": true,\n" +
                "            \"cannotUseEcard\": true,\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"cartPrice\": 44800,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"sku\": \"123\",\n" +
                "            \"packageId\": \"1223800040\",\n" +
                "            \"marketPrice\": 44800,\n" +
                "            \"ssuId\": 1223800040,\n" +
                "            \"standardPrice\": 44800,\n" +
                "            \"itemId\": \"400000250\",\n" +
                "            \"bindMainAccessory\": true,\n" +
                "            \"count\": 2,\n" +
                "            \"childs\": [\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 44400,\n" +
                "                    \"sku\": \"18853\",\n" +
                "                    \"ssuId\": 123,\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 400,\n" +
                "                    \"sku\": \"4090\",\n" +
                "                    \"ssuId\": 234,\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"department\": 2,\n" +
                "            \"displayType\": 1,\n" +
                "            \"goodsType\": 2,\n" +
                "            \"groupId\": 0,\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"reduceList\": {},\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"source\": \"buy\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"channel\": 20,\n" +
                "    \"cityId\": 36,\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"tuangou\",\n" +
                "    \"noSaveDbSubmit\": false,\n" +
                "    \"orderId\": 5237211001999983,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"useRedPacket\": false,\n" +
                "    \"userId\": 1538121936,\n" +
                "    \"userIsFriend\": 0,\n" +
                "    \"userLevel\": \"vip\"\n" +
                "}";
        CheckoutPromotionRequest request = GsonUtil.fromJson(str, CheckoutPromotionRequest.class);
        long orderId = System.currentTimeMillis();
        request.setOrderId(orderId);

        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);

        SubmitPromotionRequest submitPromotionRequest = new SubmitPromotionRequest();
        submitPromotionRequest.setOrderId(orderId);
        submitPromotionRequest.setUserId(3150000058L);
        promotionDubboService.submitPromotion(submitPromotionRequest);

        RollbackPromotionRequest rollbackCouponRequest = new RollbackPromotionRequest();
        rollbackCouponRequest.setOrderId(orderId);
        rollbackCouponRequest.setUserId(3150000058L);
        promotionDubboService.rollbackPromotion(rollbackCouponRequest);


    }

    @Test
    public void cartPromotionTest() {
        String input =
                "{\"cartList\":[{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"29322\",\"sku\":\"29322\",\"packageId\":\"\",\"ssuId\":29322,\"count\":5,\"standardPrice\":999,\"cartPrice\":999,\"reduceAmount\":0,\"source\":\"buy\",\"sourceCode\":\"\",\"properties\":\"\",\"parentItemId\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":true,\"marketPrice\":999,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,-1,-1,21,-1,8,5,9,27,-1,7,-1,-1,-1,-1,-1,-1,-1,-1,-1,10,23,26,22,25,500,501,502,503,504,505,-1],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":2}],\"channel\":21,\"userId\":1538122000,\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"tuangou\",\"getCouponList\":true,\"userLevel\":\"svip\"}";
        CartPromotionRequest request = GsonUtil.fromJson(input, CartPromotionRequest.class);


        Result<CartPromotionResponse> cartPromotionResponseResult = promotionDubboService.cartCheckoutPromotion(request);
        System.out.println(cartPromotionResponseResult);
    }

    @Test
    public void testBuyGiftCrowd() {
        String s = "{\n" +
                "    \"bargainSize\": 10,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"cartPrice\": 9900,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 1,\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"2200800002_0_buy\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 9900,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"prePrice\": 0,\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"12530\",\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"standardPrice\": 9900,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 1727425678\n" +
                "        }\n" +
                "    ],\n" +
                "    \"channel\": 1,\n" +
                "    \"clientId\": 180100041089,\n" +
                "    \"getCouponList\": true,\n" +
                "    \"globalBusinessPartner\": \"mishop\",\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"showType\": 1,\n" +
                "    \"submitType\": 0,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useDefaultCoupon\": true,\n" +
                "    \"userId\": 3150437322,\n" +
                "    \"userIsFriend\": 2\n" +
                "}";
        CartPromotionRequest request = GsonUtil.fromJson(s, CartPromotionRequest.class);
        Result<CartPromotionResponse> result = promotionDubboService.cartCheckoutPromotion(request);
        Assert.assertEquals(result.getCode(), 0);

    }

    @Test
    public void cartPromotionByGroupTest() {
        String input =
                "{\"cartList\":[{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600008371_600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"sku\":\"600008371\",\"packageId\":\"\",\"ssuId\":600008371,\"count\":1,\"standardPrice\":2000,\"cartPrice\":2000,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":2000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600008371_600003438_528bdf18dd88e2887138f15965eaa90e\",\"sku\":\"600008371\",\"packageId\":\"\",\"ssuId\":600008371,\"count\":1,\"standardPrice\":2000,\"cartPrice\":2000,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600003438_528bdf18dd88e2887138f15965eaa90e\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":2000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600005222_600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"sku\":\"600005222\",\"packageId\":\"\",\"ssuId\":600005222,\"count\":1,\"standardPrice\":10000,\"cartPrice\":10000,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":10000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600005222_600003438_528bdf18dd88e2887138f15965eaa90e\",\"sku\":\"600005222\",\"packageId\":\"\",\"ssuId\":600005222,\"count\":1,\"standardPrice\":10000,\"cartPrice\":10000,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600003438_528bdf18dd88e2887138f15965eaa90e\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":10000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600005210_600003438_528bdf18dd88e2887138f15965eaa90e\",\"sku\":\"600005210\",\"packageId\":\"\",\"ssuId\":600005210,\"count\":1,\"standardPrice\":1900,\"cartPrice\":1900,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600003438_528bdf18dd88e2887138f15965eaa90e\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":1900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600003499_600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"sku\":\"600003499\",\"packageId\":\"\",\"ssuId\":600003499,\"count\":1,\"standardPrice\":900,\"cartPrice\":900,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600003499_600003438_528bdf18dd88e2887138f15965eaa90e\",\"sku\":\"600003499\",\"packageId\":\"\",\"ssuId\":600003499,\"count\":1,\"standardPrice\":900,\"cartPrice\":900,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600003438_528bdf18dd88e2887138f15965eaa90e\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600003493_600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"sku\":\"600003493\",\"packageId\":\"\",\"ssuId\":600003493,\"count\":1,\"standardPrice\":9900,\"cartPrice\":9900,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":9900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"sku\":\"600003438\",\"packageId\":\"\",\"ssuId\":600003438,\"count\":1,\"standardPrice\":1000000,\"cartPrice\":1000000,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":1000000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600003438_528bdf18dd88e2887138f15965eaa90e\",\"sku\":\"600003438\",\"packageId\":\"\",\"ssuId\":600003438,\"count\":1,\"standardPrice\":1000000,\"cartPrice\":1000000,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":1000000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false}],\"channel\":11,\"userId\":3150075543,\"couponIds\":[],\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"car\",\"getCouponList\":true,\"showType\":0,\"useDefaultCoupon\":true}";
        CartPromotionRequest request = GsonUtil.fromJson(input, CartPromotionRequest.class);


        Result<CartPromotionResponse> cartPromotionResponseResult = promotionDubboService.cartCheckoutPromotionByGroup(request);
        System.out.println(cartPromotionResponseResult);
    }

    @Test
    public void protectTest() {
        String str =
                "{\n" +
                        "    \"userId\":3150000058,\n" +
                        "    \"clientId\":180100031052,\n" +
                        "    \"cartList\":[\n" +
                        "        {\n" +
                        "            \"childs\":[\n" +
                        "\n" +
                        "            ],\n" +
                        "            \"onsaleReduce\":0,\n" +
                        "            \"storepriceReduce\":0,\n" +
                        "            \"reduceDetailList\":{\n" +
                        "\n" +
                        "            },\n" +
                        "            \"checkoutPrice\":0,\n" +
                        "            \"itemId\":\"sku_21396_1\",\n" +
                        "            \"sku\":\"21396\",\n" +
                        "            \"packageId\":\"\",\n" +
                        "            \"count\":1,\n" +
                        "            \"standardPrice\":199900,\n" +
                        "            \"cartPrice\":199900,\n" +
                        "            \"reduceAmount\":0,\n" +
                        "            \"source\":\"\",\n" +
                        "            \"sourceCode\":\"\",\n" +
                        "            \"properties\":\"\",\n" +
                        "            \"parentItemId\":\"\",\n" +
                        "            \"updateTime\":0,\n" +
                        "            \"displayType\":0,\n" +
                        "            \"oriItemId\":\"\",\n" +
                        "            \"cannotJoinAct\":false,\n" +
                        "            \"cannotUseCoupon\":false,\n" +
                        "            \"marketPrice\":199900,\n" +
                        "            \"cannotUseEcard\":false,\n" +
                        "            \"maxUseEcardAmount\":0,\n" +
                        "            \"cannotJoinActTypes\":[\n" +
                        "\n" +
                        "            ],\n" +
                        "            \"cannotUseCouponTypes\":[\n" +
                        "\n" +
                        "            ],\n" +
                        "            \"saleSource\":\"common\",\n" +
                        "            \"saleSources\":[\n" +
                        "\n" +
                        "            ],\n" +
                        "            \"cannotUseRedPacket\":true,\n" +
                        "            \"groupId\":0,\n" +
                        "            \"onSaleBookingPrice\":0,\n" +
                        "            \"joinOnsale\":false,\n" +
                        "            \"changePriceActType\":0,\n" +
                        "            \"originalCartPrice\":0,\n" +
                        "            \"orderItemReduceList\":{\n" +
                        "\n" +
                        "            },\n" +
                        "            \"unitId\":\"\"\n" +
                        "        }\n" +
                        "    ],\n" +
                        "    \"couponId\":0,\n" +
                        "    \"couponIdList\":[\n" +
                        "        1010001483,\n" +
                        "        1010002501\n" +
                        "    ],\n" +
                        "    \"couponCode\":\"\",\n" +
                        "    \"redPacketUsedList\":[\n" +
                        "\n" +
                        "    ],\n" +
                        "    \"orderId\":****************\n" +
                        "}";
        GetProductCurrentPriceRequest request = GsonUtil.fromJson(str, GetProductCurrentPriceRequest.class);
        System.out.println(GsonUtil.toJson(request));
        Result<GetProductCurrentPriceResponse> result = promotionDubboService.getProductCurrentPrice(request);
        System.out.println(GsonUtil.toJson(result));

    }

    @Test
    public void testGetPromotionPrice() {
        String str =
                "{\n" +
                        "    \"channel\": 22,\n" +
                        "    \"goodsList\": [\n" +
                        "        {\n" +
                        "            \"bindMainAccessory\": false,\n" +
                        "            \"childs\": [\n" +
                        "                {\n" +
                        "                    \"count\": 2,\n" +
                        "                    \"sellPrice\": 289900,\n" +
                        "                    \"sku\": \"11860\",\n" +
                        "                    \"ssuId\": 21212\n" +
                        "                },\n" +
                        "                {\n" +
                        "                    \"count\": 2,\n" +
                        "                    \"sellPrice\": 29900,\n" +
                        "                    \"sku\": \"234\",\n" +
                        "                    \"ssuId\": 21212\n" +
                        "                }\n" +
                        "            ],\n" +
                        "            \"department\": 2,\n" +
                        "            \"goodsType\": 2,\n" +
                        "            \"id\": 5645,\n" +
                        "            \"price\": 10000000,\n" +
                        "            \"ssuId\": 5645\n" +
                        "        },\n" +
                        "        {\n" +
                        "            \"bindMainAccessory\": true,\n" +
                        "            \"childs\": [\n" +
                        "                {\n" +
                        "                    \"count\": 1,\n" +
                        "                    \"sellPrice\": 289900,\n" +
                        "                    \"sku\": \"5645\",\n" +
                        "                    \"ssuId\": 21212\n" +
                        "                },\n" +
                        "                {\n" +
                        "                    \"count\": 1,\n" +
                        "                    \"sellPrice\": 29900,\n" +
                        "                    \"sku\": \"11860\",\n" +
                        "                    \"ssuId\": 21212\n" +
                        "                }\n" +
                        "            ],\n" +
                        "            \"department\": 2,\n" +
                        "            \"goodsType\": 2,\n" +
                        "            \"id\": 1,\n" +
                        "            \"price\": 10000000,\n" +
                        "            \"ssuId\": 1\n" +
                        "        },\n" +
                        "        {\n" +
                        "            \"bindMainAccessory\": true,\n" +
                        "            \"childs\": [\n" +
                        "                {\n" +
                        "                    \"count\": 2,\n" +
                        "                    \"sellPrice\": 289900,\n" +
                        "                    \"sku\": \"5645\",\n" +
                        "                    \"ssuId\": 21212\n" +
                        "                },\n" +
                        "                {\n" +
                        "                    \"count\": 2,\n" +
                        "                    \"sellPrice\": 29900,\n" +
                        "                    \"sku\": \"11860\",\n" +
                        "                    \"ssuId\": 21212\n" +
                        "                }\n" +
                        "            ],\n" +
                        "            \"department\": 2,\n" +
                        "            \"goodsType\": 2,\n" +
                        "            \"id\": 2,\n" +
                        "            \"price\": 10000000,\n" +
                        "            \"ssuId\": 2\n" +
                        "        },\n" +
                        "        {\n" +
                        "            \"bindMainAccessory\": true,\n" +
                        "            \"childs\": [\n" +
                        "                {\n" +
                        "                    \"count\": 2,\n" +
                        "                    \"sellPrice\": 289900,\n" +
                        "                    \"sku\": \"5645\",\n" +
                        "                    \"ssuId\": 21212\n" +
                        "                },\n" +
                        "                {\n" +
                        "                    \"count\": 2,\n" +
                        "                    \"sellPrice\": 29900,\n" +
                        "                    \"sku\": \"234\",\n" +
                        "                    \"ssuId\": 21212\n" +
                        "                }\n" +
                        "            ],\n" +
                        "            \"department\": 2,\n" +
                        "            \"goodsType\": 2,\n" +
                        "            \"id\": 3,\n" +
                        "            \"price\": 10000000,\n" +
                        "            \"ssuId\": 3\n" +
                        "        },\n" +
                        "        {\n" +
                        "            \"bindMainAccessory\": true,\n" +
                        "            \"childs\": [\n" +
                        "                {\n" +
                        "                    \"count\": 2,\n" +
                        "                    \"sellPrice\": 289900,\n" +
                        "                    \"sku\": \"11860\",\n" +
                        "                    \"ssuId\": 21212\n" +
                        "                },\n" +
                        "                {\n" +
                        "                    \"count\": 2,\n" +
                        "                    \"sellPrice\": 29900,\n" +
                        "                    \"sku\": \"234\",\n" +
                        "                    \"ssuId\": 21212\n" +
                        "                }\n" +
                        "            ],\n" +
                        "            \"department\": 2,\n" +
                        "            \"goodsType\": 2,\n" +
                        "            \"id\": 4,\n" +
                        "            \"price\": 10000000,\n" +
                        "            \"ssuId\": 4\n" +
                        "        }\n" +
                        "    ],\n" +
                        "    \"userLevel\": \"svip\"\n" +
                        "}";
        GetPromotionPriceRequest request = GsonUtil.fromJson(str, GetPromotionPriceRequest.class);
        Result<GetPromotionPriceResponse> result = promotionDubboService.getPromotionPrice(request);
        System.out.println(GsonUtil.toJson(result));
    }

    @Test
    public void testGetCarPromotionPrice() {
        String str =
                "{\n" +
                        "    \"channel\": 11,\n" +
                        "    \"goodsList\": [\n" +
                        "        {\n" +
                        "            \"price\": 200000,\n" +
                        "            \"ssuId\": 600000861\n" +
                        "        },\n" +
                        "        {\n" +
                        "            \"price\": 20000,\n" +
                        "            \"ssuId\": 600000882\n" +
                        "        },\n" +
                        "        {\n" +
                        "            \"price\": 10,\n" +
                        "            \"ssuId\": 600000884\n" +
                        "        }\n" +
                        "    ]\n" +
                        "}";
        GetPromotionPriceRequest request = GsonUtil.fromJson(str, GetPromotionPriceRequest.class);
        Result<GetPromotionPriceResponse> result = promotionDubboService.getPromotionPrice(request);
        System.out.println(GsonUtil.toJson(result));
    }

    @Test
    public void checkoutPromotion_testCRMPrice() {
        String str = "{\n" +
                "    \"bargainSize\": 10,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                1,\n" +
                "                2,\n" +
                "                3,\n" +
                "                4,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                21,\n" +
                "                -1,\n" +
                "                8,\n" +
                "                5,\n" +
                "                9,\n" +
                "                27,\n" +
                "                -1,\n" +
                "                7,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                -1,\n" +
                "                10,\n" +
                "                23,\n" +
                "                26,\n" +
                "                22,\n" +
                "                25,\n" +
                "                500,\n" +
                "                501,\n" +
                "                502,\n" +
                "                503,\n" +
                "                504,\n" +
                "                505,\n" +
                "                -1\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": true,\n" +
                "            \"cannotUseEcard\": true,\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"cartPrice\": 501554,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"sku\": \"3400019\",\n" +
//                "            \"packageId\": \"1223800040\",\n" +
                "            \"marketPrice\": 501554,\n" +
                "            \"ssuId\": 3400019,\n" +
                "            \"standardPrice\": 501554,\n" +
                "            \"itemId\": \"400000250\",\n" +
//                "            \"bindMainAccessory\": true,\n" +
                "            \"count\": 1,\n" +
                "            \"childs\": [\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 500000,\n" +
//                "                    \"sku\": \"18853\",\n" +
                "                    \"ssuId\": 123,\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"cartPrice\": 0,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 1554,\n" +
//                "                    \"sku\": \"4090\",\n" +
                "                    \"ssuId\": 234,\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"department\": 2,\n" +
                "            \"displayType\": 1,\n" +
                "            \"goodsType\": 2,\n" +
                "            \"groupId\": 0,\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"reduceList\": {},\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"source\": \"buy\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"channel\": 21,\n" +
                "    \"cityId\": 36,\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"tuangou\",\n" +
                "    \"noSaveDbSubmit\": false,\n" +
                "    \"orderId\": 1696734037000,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"useRedPacket\": false,\n" +
                "    \"userId\": 1538121936,\n" +
                "    \"userIsFriend\": 0,\n" +
                "    \"userLevel\": \"vip\"\n" +
                "}";
        CheckoutPromotionRequest request = GsonUtil.fromJson(str, CheckoutPromotionRequest.class);
        long orderId = System.currentTimeMillis();
        request.setOrderId(orderId);

        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);

        SubmitPromotionRequest submitPromotionRequest = new SubmitPromotionRequest();
        submitPromotionRequest.setOrderId(orderId);
        submitPromotionRequest.setUserId(3150000058L);
        promotionDubboService.submitPromotion(submitPromotionRequest);

        RollbackPromotionRequest rollbackCouponRequest = new RollbackPromotionRequest();
        rollbackCouponRequest.setOrderId(orderId);
        rollbackCouponRequest.setUserId(3150000058L);
        promotionDubboService.rollbackPromotion(rollbackCouponRequest);


    }

    @Test
    public void checkoutPromotion_testCar() {
        /*String str = "{\"cartList\":[{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600008419_600008378_37f020c8f5c48748b44664f03fb8c08a\",\"sku\":\"600008419\",\"packageId\":\"\",\"ssuId\":600008419,\"count\":1,\"standardPrice\":10,\"cartPrice\":10,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":10,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505,2002],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600008418_600008378_37f020c8f5c48748b44664f03fb8c08a\",\"sku\":\"600008418\",\"packageId\":\"\",\"ssuId\":600008418,\"count\":1,\"standardPrice\":1000,\"cartPrice\":1000,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":1000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505,2002],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600008415_600008378_37f020c8f5c48748b44664f03fb8c08a\",\"sku\":\"600008415\",\"packageId\":\"\",\"ssuId\":600008415,\"count\":1,\"standardPrice\":10,\"cartPrice\":10,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":10,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505,2002],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600008410_600008378_37f020c8f5c48748b44664f03fb8c08a\",\"sku\":\"600008410\",\"packageId\":\"\",\"ssuId\":600008410,\"count\":1,\"standardPrice\":0,\"cartPrice\":0,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":0,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505,2002],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600008408_600008378_37f020c8f5c48748b44664f03fb8c08a\",\"sku\":\"600008408\",\"packageId\":\"\",\"ssuId\":600008408,\"count\":1,\"standardPrice\":0,\"cartPrice\":0,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":0,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505,2002],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600008407_600008378_37f020c8f5c48748b44664f03fb8c08a\",\"sku\":\"600008407\",\"packageId\":\"\",\"ssuId\":600008407,\"count\":1,\"standardPrice\":300,\"cartPrice\":300,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":300,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505,2002],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600008378_37f020c8f5c48748b44664f03fb8c08a\",\"sku\":\"600008378\",\"packageId\":\"\",\"ssuId\":600008378,\"count\":1,\"standardPrice\":1000000,\"cartPrice\":1000000,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":1000000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505,2002],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false}],\"channel\":11,\"userId\":3150368455,\"couponIds\":[1010547176],\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"car\",\"getCouponList\":true,\"showType\":0,\"useDefaultCoupon\":true,\"usedCouponId\":0,\"submitType\":1,\"sourceApi\":2,\"orderId\":****************,\"noSaveDbSubmit\":true}";
        CheckoutPromotionRequest request = GsonUtil.fromJson(str, CheckoutPromotionRequest.class);
        long orderId = System.currentTimeMillis();*/

        String s = "{\n" +
                "    \"bargainSize\": 10,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"bindMainAccessory\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                1,\n" +
                "                2,\n" +
                "                3,\n" +
                "                4,\n" +
                "                21,\n" +
                "                8,\n" +
                "                5,\n" +
                "                9,\n" +
                "                27,\n" +
                "                7,\n" +
                "                10,\n" +
                "                23,\n" +
                "                26,\n" +
                "                22,\n" +
                "                25,\n" +
                "                70,\n" +
                "                71,\n" +
                "                72,\n" +
                "                2001,\n" +
                "                500,\n" +
                "                501,\n" +
                "                502,\n" +
                "                503,\n" +
                "                504,\n" +
                "                505,\n" +
                "                2002\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseEcard\": true,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"cartPrice\": 3000000,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"childs\": [],\n" +
                "            \"count\": 1,\n" +
                "            \"department\": 0,\n" +
                "            \"displayType\": 1,\n" +
                "            \"goodsType\": 2,\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"600008379\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 3000000,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"reduceList\": {},\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"600008379\",\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"ssuId\": 600008379,\n" +
                "            \"standardPrice\": 3000000,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"channel\": 11,\n" +
                "    \"couponCodes\": [],\n" +
                "    \"couponIds\": [1010628386],\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"car\",\n" +
                "    \"noSaveDbSubmit\": false,\n" +
                "    \"orderId\": 5245822204688552,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"submitType\": 0,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"usePoint\": false,\n" +
                "    \"useRedPacket\": false,\n" +
                "    \"userId\": 12345678,\n" +
                "    \"userIsFriend\": 0\n" +
                "}";
        CheckoutPromotionRequest request = GsonUtil.fromJson(s, CheckoutPromotionRequest.class);
        long orderId = System.currentTimeMillis();
        request.setOrderId(orderId);
        /*CheckoutPromotionRequest request =new CheckoutPromotionRequest();
        request.setChannel(ChannelEnum.CAR_VEHICLE.getValue());
        request.setNoSaveDbSubmit(false);
        request.setSourceApi(SourceApi.SUBMIT);
        request.setClientId(123456789L);
        request.setOrderId(System.currentTimeMillis());
        request.setUserId(12345678L);
        List<CartItem> cartList=new ArrayList<>();
        CartItem cartItem=new CartItem();
        cartItem.setSsuId(600003440L);
        cartItem.setStandardPrice(2100L);
        cartItem.setCartPrice(2100L);
        cartItem.setMarketPrice(2100L);
        cartItem.setCount(1);
        cartItem.setItemId("9876");
        cartList.add(cartItem);
        request.setCartList(cartList);
        request.setCouponIds(List.of(139958L));*/
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));

    }

    @Test
    public void checkoutPromotion_testCheckoutAndSubmit() {
        String str = "{\"sourceApi\":2,\"cartList\":[{\"reduceList\":{},\"reduceItemList\":[{\"promotionId\":21487641,\"promotionType\":28,\"ssuId\":600008373,\"reduce\":25,\"reduceSingle\":25}],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600008373\",\"sku\":\"600008373\",\"packageId\":\"\",\"ssuId\":600008373,\"count\":1,\"standardPrice\":100,\"cartPrice\":100,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"displayType\":1,\"oriItemId\":\"600008373\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":100,\"cannotUseEcard\":true,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505,2002],\"cannotUseCouponTypes\":[],\"saleSource\":\"common\",\"saleSources\":[\"common\"],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":100,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600008372_600003438\",\"sku\":\"600008372\",\"packageId\":\"\",\"ssuId\":600008372,\"count\":1,\"standardPrice\":0,\"cartPrice\":0,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"displayType\":1,\"oriItemId\":\"600008372_600003438\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":0,\"cannotUseEcard\":true,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505,2002],\"cannotUseCouponTypes\":[],\"saleSource\":\"common\",\"saleSources\":[\"common\"],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[{\"promotionId\":21487641,\"promotionType\":28,\"ssuId\":600008371,\"reduce\":25,\"reduceSingle\":25}],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600008371\",\"sku\":\"600008371\",\"packageId\":\"\",\"ssuId\":600008371,\"count\":1,\"standardPrice\":100,\"cartPrice\":100,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"displayType\":1,\"oriItemId\":\"600008371\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":100,\"cannotUseEcard\":true,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505,2002],\"cannotUseCouponTypes\":[],\"saleSource\":\"common\",\"saleSources\":[\"common\"],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":100,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600008268_600003438\",\"sku\":\"600008268\",\"packageId\":\"\",\"ssuId\":600008268,\"count\":1,\"standardPrice\":0,\"cartPrice\":0,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"displayType\":1,\"oriItemId\":\"600008268_600003438\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":0,\"cannotUseEcard\":true,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505,2002],\"cannotUseCouponTypes\":[],\"saleSource\":\"common\",\"saleSources\":[\"common\"],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[{\"promotionId\":21487641,\"promotionType\":28,\"ssuId\":600003499,\"reduce\":25,\"reduceSingle\":25}],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600003499\",\"sku\":\"600003499\",\"packageId\":\"\",\"ssuId\":600003499,\"count\":1,\"standardPrice\":100,\"cartPrice\":100,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"displayType\":1,\"oriItemId\":\"600003499\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":100,\"cannotUseEcard\":true,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505,2002],\"cannotUseCouponTypes\":[],\"saleSource\":\"common\",\"saleSources\":[\"common\"],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":100,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[{\"promotionId\":21487641,\"promotionType\":28,\"ssuId\":600003493,\"reduce\":25,\"reduceSingle\":25}],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600003493\",\"sku\":\"600003493\",\"packageId\":\"\",\"ssuId\":600003493,\"count\":1,\"standardPrice\":100,\"cartPrice\":100,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"displayType\":1,\"oriItemId\":\"600003493\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":100,\"cannotUseEcard\":true,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505,2002],\"cannotUseCouponTypes\":[],\"saleSource\":\"common\",\"saleSources\":[\"common\"],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":100,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[{\"promotionId\":21487963,\"promotionType\":20,\"ssuId\":600003438,\"reduce\":1001700,\"reduceSingle\":1001700}],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600003438\",\"sku\":\"600003438\",\"packageId\":\"\",\"ssuId\":600003438,\"count\":1,\"standardPrice\":1002100,\"cartPrice\":400,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"displayType\":1,\"oriItemId\":\"600003438\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":1002100,\"cannotUseEcard\":true,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505,2002],\"cannotUseCouponTypes\":[],\"saleSource\":\"common\",\"saleSources\":[\"common\"],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":1002100,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false}],\"channel\":11,\"userId\":3150028131,\"couponIds\":[],\"couponCodes\":[],\"orderId\":****************,\"bargainSize\":10,\"noSaveDbSubmit\":false,\"useRedPacket\":false,\"calculateRedpacket\":false,\"useBeijingcoupon\":false,\"orgCode\":\"\",\"uidType\":\"\",\"shoppingMode\":0,\"shipmentId\":2,\"shipmentExpense\":0,\"globalBusinessPartner\":\"car\",\"fromPriceProtect\":false,\"userIsFriend\":0,\"usePoint\":false,\"pointReduceAmount\":0,\"usedCouponId\":1010548588,\"submitType\":1}";
        CheckoutPromotionRequest request = GsonUtil.fromJson(str, CheckoutPromotionRequest.class);

        request.setNoSaveDbSubmit(false);


        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
//        System.out.println(GsonUtil.toJson(result));
//        SubmitPromotionRequest submitPromotionRequest = new SubmitPromotionRequest();
//        submitPromotionRequest.setOrderId(5245541003054345L);
//        submitPromotionRequest.setUserId(3150077067L);
//        promotionDubboService.submitPromotion(submitPromotionRequest);

    }

    @Test
    public void testWorkHour() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        request.setChannel(ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue());
        request.setNoSaveDbSubmit(false);
        request.setSourceApi(SourceApi.SUBMIT);
        request.setClientId(123456789L);
        request.setOrderId(System.currentTimeMillis());
        List<CartItem> cartList = new ArrayList<>();
        CartItem cartItem = new CartItem();
        cartItem.setSsuId(600005245L);
        cartItem.setStandardPrice(600L);
        cartItem.setCartPrice(600L);
        cartItem.setMarketPrice(600L);
        cartItem.setBizSubType(13);
        cartItem.setPayType(1);
        cartItem.setWorkHour(BigDecimal.valueOf(6.0));
        cartItem.setUnitPrice(100L);
        cartItem.setCount(1);
        cartItem.setItemId("9876");
        cartList.add(cartItem);
        request.setCartList(cartList);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));

    }

    @Test
    public void checkoutPromotion_framework_upgrade() {
        // 3c
        String str = "{\"sourceApi\":1,\"cartList\":[{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"51495_normal_0_1_master\",\"sku\":\"51495\",\"packageId\":\"\",\"goodsName\":\"Redmi Watch 4 典雅黑\",\"count\":1,\"standardPrice\":49900,\"cartPrice\":49900,\"reduceAmount\":0,\"source\":\"normal\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":0,\"displayType\":0,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":49900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[],\"cannotUseCouponTypes\":[],\"saleSource\":\"\",\"saleSources\":[],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\"}],\"clientId\":180100041075,\"userId\":0,\"couponIds\":[],\"ecardIds\":[],\"orderId\":0,\"bargainSize\":100,\"noSaveDbSubmit\":true,\"useRedPacket\":false,\"calculateRedpacket\":false,\"useBeijingcoupon\":false,\"orgCode\":\"MI51168\",\"uidType\":\"\",\"cityId\":0,\"shoppingMode\":0,\"shipmentId\":-1,\"shipmentExpense\":0,\"globalBusinessPartner\":\"\",\"fromPriceProtect\":false}";
        CheckoutPromotionRequest request = GsonUtil.fromJson(str, CheckoutPromotionRequest.class);
        request.setOrderId(System.currentTimeMillis());
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);

        // 团购
        str = "{\"sourceApi\":2,\"cartList\":[{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[{\"sku\":\"\",\"ssuId\":123,\"sellPrice\":500000,\"count\":1,\"unitId\":\"\",\"onSaleBookingPrice\":0,\"lowerPrice\":0,\"originalSellPrice\":0,\"cartPrice\":0,\"checkoutPrice\":0,\"onsaleReduce\":0,\"storepriceReduce\":0},{\"sku\":\"\",\"ssuId\":234,\"sellPrice\":1554,\"count\":1,\"unitId\":\"\",\"onSaleBookingPrice\":0,\"lowerPrice\":0,\"originalSellPrice\":0,\"cartPrice\":0,\"checkoutPrice\":0,\"onsaleReduce\":0,\"storepriceReduce\":0}],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"400000250\",\"sku\":\"3400019\",\"packageId\":\"\",\"ssuId\":3400019,\"count\":1,\"standardPrice\":501554,\"cartPrice\":501554,\"reduceAmount\":0,\"source\":\"buy\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":0,\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":true,\"marketPrice\":501554,\"cannotUseEcard\":true,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,-1,-1,21,-1,8,5,9,27,-1,7,-1,-1,-1,-1,-1,-1,-1,-1,-1,10,23,26,22,25,500,501,502,503,504,505,-1],\"cannotUseCouponTypes\":[],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":2}],\"channel\":21,\"clientId\":0,\"userId\":1538121936,\"orderId\":1703577622130,\"bargainSize\":10,\"noSaveDbSubmit\":false,\"useRedPacket\":false,\"calculateRedpacket\":false,\"useBeijingcoupon\":false,\"orgCode\":\"\",\"uidType\":\"\",\"cityId\":36,\"shoppingMode\":0,\"shipmentId\":2,\"shipmentExpense\":0,\"globalBusinessPartner\":\"tuangou\",\"fromPriceProtect\":false,\"userIsFriend\":0,\"userLevel\":\"vip\"}";
        request = GsonUtil.fromJson(str, CheckoutPromotionRequest.class);
        request.setOrderId(System.currentTimeMillis());
        request.setChannel(ChannelEnum.B2T_GOV_BIG_CUSTOMER.getValue());
        Result<CheckoutPromotionResponse> result1 = promotionDubboService.checkoutPromotion(request);

        // 整车
        str = "{\"sourceApi\":1,\"cartList\":[{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"51495_normal_0_1_master\",\"sku\":\"51495\",\"packageId\":\"\",\"goodsName\":\"Redmi Watch 4 典雅黑\",\"count\":1,\"standardPrice\":49900,\"cartPrice\":49900,\"reduceAmount\":0,\"source\":\"normal\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":0,\"displayType\":0,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":49900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[],\"cannotUseCouponTypes\":[],\"saleSource\":\"\",\"saleSources\":[],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\"}],\"clientId\":180100041075,\"userId\":0,\"couponIds\":[],\"ecardIds\":[],\"orderId\":0,\"bargainSize\":100,\"noSaveDbSubmit\":true,\"useRedPacket\":false,\"calculateRedpacket\":false,\"useBeijingcoupon\":false,\"orgCode\":\"MI51168\",\"uidType\":\"\",\"cityId\":0,\"shoppingMode\":0,\"shipmentId\":-1,\"shipmentExpense\":0,\"globalBusinessPartner\":\"\",\"fromPriceProtect\":false}";
        request = GsonUtil.fromJson(str, CheckoutPromotionRequest.class);
        request.setOrderId(System.currentTimeMillis());
        request.setChannel(ChannelEnum.CAR_VEHICLE.getValue());
        Result<CheckoutPromotionResponse> result2 = promotionDubboService.checkoutPromotion(request);

        // 融合三方
        str = "{\"sourceApi\":1,\"cartList\":[{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"51495_normal_0_1_master\",\"sku\":\"51495\",\"packageId\":\"\",\"goodsName\":\"Redmi Watch 4 典雅黑\",\"count\":1,\"standardPrice\":49900,\"cartPrice\":49900,\"reduceAmount\":0,\"source\":\"normal\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":0,\"displayType\":0,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":49900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[],\"cannotUseCouponTypes\":[],\"saleSource\":\"\",\"saleSources\":[],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\"}],\"clientId\":180100041075,\"userId\":0,\"couponIds\":[],\"ecardIds\":[],\"orderId\":0,\"bargainSize\":100,\"noSaveDbSubmit\":true,\"useRedPacket\":false,\"calculateRedpacket\":false,\"useBeijingcoupon\":false,\"orgCode\":\"MI51168\",\"uidType\":\"\",\"cityId\":0,\"shoppingMode\":0,\"shipmentId\":-1,\"shipmentExpense\":0,\"globalBusinessPartner\":\"\",\"fromPriceProtect\":false}";
        request = GsonUtil.fromJson(str, CheckoutPromotionRequest.class);
        request.setOrderId(System.currentTimeMillis());
        request.setChannel(ChannelEnum.JD_HOME_DIRECT.getValue());
        Result<CheckoutPromotionResponse> result3 = promotionDubboService.checkoutPromotion(request);

        System.out.println();
    }

    @Test
    public void checkoutPromotionV2_framework_upgrade() {
        // 3c
        String str = "{\"sourceApi\":1,\"cartList\":[{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"51495_normal_0_1_master\",\"sku\":\"51495\",\"packageId\":\"\",\"goodsName\":\"Redmi Watch 4 典雅黑\",\"count\":1,\"standardPrice\":49900,\"cartPrice\":49900,\"reduceAmount\":0,\"source\":\"normal\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":0,\"displayType\":0,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":49900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[],\"cannotUseCouponTypes\":[],\"saleSource\":\"\",\"saleSources\":[],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\"}],\"clientId\":180100041075,\"userId\":0,\"couponIds\":[],\"ecardIds\":[],\"orderId\":0,\"bargainSize\":100,\"noSaveDbSubmit\":true,\"useRedPacket\":false,\"calculateRedpacket\":false,\"useBeijingcoupon\":false,\"orgCode\":\"MI51168\",\"uidType\":\"\",\"cityId\":0,\"shoppingMode\":0,\"shipmentId\":-1,\"shipmentExpense\":0,\"globalBusinessPartner\":\"\",\"fromPriceProtect\":false}";
        CheckoutPromotionV2Request request = GsonUtil.fromJson(str, CheckoutPromotionV2Request.class);
        request.setOrderId(System.currentTimeMillis());
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);

        // 团购
        str = "{\"sourceApi\":2,\"cartList\":[{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[{\"sku\":\"\",\"ssuId\":123,\"sellPrice\":500000,\"count\":1,\"unitId\":\"\",\"onSaleBookingPrice\":0,\"lowerPrice\":0,\"originalSellPrice\":0,\"cartPrice\":0,\"checkoutPrice\":0,\"onsaleReduce\":0,\"storepriceReduce\":0},{\"sku\":\"\",\"ssuId\":234,\"sellPrice\":1554,\"count\":1,\"unitId\":\"\",\"onSaleBookingPrice\":0,\"lowerPrice\":0,\"originalSellPrice\":0,\"cartPrice\":0,\"checkoutPrice\":0,\"onsaleReduce\":0,\"storepriceReduce\":0}],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"400000250\",\"sku\":\"3400019\",\"packageId\":\"\",\"ssuId\":3400019,\"count\":1,\"standardPrice\":501554,\"cartPrice\":501554,\"reduceAmount\":0,\"source\":\"buy\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":0,\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":true,\"marketPrice\":501554,\"cannotUseEcard\":true,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,-1,-1,21,-1,8,5,9,27,-1,7,-1,-1,-1,-1,-1,-1,-1,-1,-1,10,23,26,22,25,500,501,502,503,504,505,-1],\"cannotUseCouponTypes\":[],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":2}],\"channel\":21,\"clientId\":0,\"userId\":1538121936,\"orderId\":1703577622130,\"bargainSize\":10,\"noSaveDbSubmit\":false,\"useRedPacket\":false,\"calculateRedpacket\":false,\"useBeijingcoupon\":false,\"orgCode\":\"\",\"uidType\":\"\",\"cityId\":36,\"shoppingMode\":0,\"shipmentId\":2,\"shipmentExpense\":0,\"globalBusinessPartner\":\"tuangou\",\"fromPriceProtect\":false,\"userIsFriend\":0,\"userLevel\":\"vip\"}";
        request = GsonUtil.fromJson(str, CheckoutPromotionV2Request.class);
        request.setOrderId(System.currentTimeMillis());
        request.setChannel(ChannelEnum.B2T_GOV_BIG_CUSTOMER.getValue());
        Result<CheckoutPromotionV2Response> result1 = promotionDubboService.checkoutPromotionV2(request);

        // 整车
        str = "{\"sourceApi\":1,\"cartList\":[{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"51495_normal_0_1_master\",\"sku\":\"51495\",\"packageId\":\"\",\"goodsName\":\"Redmi Watch 4 典雅黑\",\"count\":1,\"standardPrice\":49900,\"cartPrice\":49900,\"reduceAmount\":0,\"source\":\"normal\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":0,\"displayType\":0,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":49900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[],\"cannotUseCouponTypes\":[],\"saleSource\":\"\",\"saleSources\":[],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\"}],\"clientId\":180100041075,\"userId\":0,\"couponIds\":[],\"ecardIds\":[],\"orderId\":0,\"bargainSize\":100,\"noSaveDbSubmit\":true,\"useRedPacket\":false,\"calculateRedpacket\":false,\"useBeijingcoupon\":false,\"orgCode\":\"MI51168\",\"uidType\":\"\",\"cityId\":0,\"shoppingMode\":0,\"shipmentId\":-1,\"shipmentExpense\":0,\"globalBusinessPartner\":\"\",\"fromPriceProtect\":false}";
        request = GsonUtil.fromJson(str, CheckoutPromotionV2Request.class);
        request.setOrderId(System.currentTimeMillis());
        request.setChannel(ChannelEnum.CAR_VEHICLE.getValue());
        Result<CheckoutPromotionV2Response> result2 = promotionDubboService.checkoutPromotionV2(request);

        // 融合三方
        str = "{\"sourceApi\":1,\"cartList\":[{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"51495_normal_0_1_master\",\"sku\":\"51495\",\"packageId\":\"\",\"goodsName\":\"Redmi Watch 4 典雅黑\",\"count\":1,\"standardPrice\":49900,\"cartPrice\":49900,\"reduceAmount\":0,\"source\":\"normal\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":0,\"displayType\":0,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":49900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[],\"cannotUseCouponTypes\":[],\"saleSource\":\"\",\"saleSources\":[],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\"}],\"clientId\":180100041075,\"userId\":0,\"couponIds\":[],\"ecardIds\":[],\"orderId\":0,\"bargainSize\":100,\"noSaveDbSubmit\":true,\"useRedPacket\":false,\"calculateRedpacket\":false,\"useBeijingcoupon\":false,\"orgCode\":\"MI51168\",\"uidType\":\"\",\"cityId\":0,\"shoppingMode\":0,\"shipmentId\":-1,\"shipmentExpense\":0,\"globalBusinessPartner\":\"\",\"fromPriceProtect\":false}";
        request = GsonUtil.fromJson(str, CheckoutPromotionV2Request.class);
        request.setOrderId(System.currentTimeMillis());
        request.setChannel(ChannelEnum.JD_HOME_DIRECT.getValue());
        Result<CheckoutPromotionV2Response> result3 = promotionDubboService.checkoutPromotionV2(request);

        System.out.println();

    }

    @Test
    public void cartCheckoutPromotion_framework_upgrade() {
        // 3c
        String str = "{\"cartList\":[{\"reduceItemList\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"2221500235_0_buy\",\"sku\":\"39671\",\"packageId\":\"\",\"count\":1,\"standardPrice\":3900,\"cartPrice\":3900,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":1703576077,\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":3900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"unitId\":\"\"}],\"clientId\":180100031052,\"userId\":1242170538,\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"mishop\",\"userIsFriend\":2,\"getCouponList\":true}";
        CartPromotionRequest request = GsonUtil.fromJson(str, CartPromotionRequest.class);
        Result<CartPromotionResponse> result = promotionDubboService.cartCheckoutPromotion(request);
        // 团购
        str = "{\"cartList\":[{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"29322\",\"sku\":\"29322\",\"packageId\":\"\",\"ssuId\":29322,\"count\":5,\"standardPrice\":999,\"cartPrice\":999,\"reduceAmount\":0,\"source\":\"buy\",\"sourceCode\":\"\",\"properties\":\"\",\"parentItemId\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":true,\"marketPrice\":999,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,-1,-1,21,-1,8,5,9,27,-1,7,-1,-1,-1,-1,-1,-1,-1,-1,-1,10,23,26,22,25,500,501,502,503,504,505,-1],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":2}],\"channel\":21,\"userId\":1538122000,\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"tuangou\",\"getCouponList\":true,\"userLevel\":\"svip\"}";
        request = GsonUtil.fromJson(str, CartPromotionRequest.class);
        request.setChannel(ChannelEnum.B2T_GOV_BIG_CUSTOMER.getValue());
        Result<CartPromotionResponse> result2 = promotionDubboService.cartCheckoutPromotion(request);
        // 整车
        str = "{\"cartList\":[{\"reduceItemList\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"2221500235_0_buy\",\"sku\":\"39671\",\"packageId\":\"\",\"count\":1,\"standardPrice\":3900,\"cartPrice\":3900,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":1703576077,\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":3900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"unitId\":\"\"}],\"clientId\":180100031052,\"userId\":1242170538,\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"mishop\",\"userIsFriend\":2,\"getCouponList\":true}";
        request = GsonUtil.fromJson(str, CartPromotionRequest.class);
        request.setChannel(ChannelEnum.CAR_VEHICLE.getValue());
        Result<CartPromotionResponse> result3 = promotionDubboService.cartCheckoutPromotion(request);
        // 融合三方
        str = "{\"cartList\":[{\"reduceItemList\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"2221500235_0_buy\",\"sku\":\"39671\",\"packageId\":\"\",\"count\":1,\"standardPrice\":3900,\"cartPrice\":3900,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":1703576077,\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":3900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"unitId\":\"\"}],\"clientId\":180100031052,\"userId\":1242170538,\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"mishop\",\"userIsFriend\":2,\"getCouponList\":true}";
        request = GsonUtil.fromJson(str, CartPromotionRequest.class);
        request.setChannel(ChannelEnum.JD_HOME_DIRECT.getValue());
        Result<CartPromotionResponse> result4 = promotionDubboService.cartCheckoutPromotion(request);
        System.out.println();

    }


    @Test
    public void test_maintenanceDeductCoupon() {
        String json = "{\n" + "    \"usePoint\": false,\n" + "    \"submitType\": 0,\n" + "    \"orderId\": 0,\n"
                + "    \"channel\": 12,\n" + "    \"getCouponList\": true,\n" + "    \"shipmentExpense\": 0,\n"
                + "    \"cityId\": 0,\n" + "    \"fromPriceProtect\": false,\n"
                + "    \"vid\": \"MOCKD000000000003\",\n" + "    \"useDefaultCoupon\": false,\n"
                + "    \"useRedPacket\": false,\n" + "    \"pointReduceAmount\": 0,\n" + "    \"orgCode\": \"\",\n"
                + "    \"showType\": 1,\n" + "    \"ecardIds\": [],\n" + "    \"useBeijingcoupon\": false,\n"
                + "    \"calculateRedpacket\": true,\n" + "    \"noSaveDbSubmit\": false,\n"
                + "    \"shoppingMode\": 0,\n" + "    \"couponCodes\": [],\n" + "    \"bargainSize\": 10,\n"
                + "    \"uidType\": \"\",\n" + "    \"userId\": 3150000058,\n" + "    \"couponIds\": [\n"
                + "        1010549423\n" + "    ],\n" + "    \"globalBusinessPartner\": \"car\",\n"
                + "    \"shipmentId\": 2,\n" + "    \"cartList\": [\n" + "        {\n"
                + "            \"joinOnsale\": false,\n" + "            \"marketPrice\": 500,\n"
                + "            \"preferentialInfos\": [],\n" + "            \"groupId\": 0,\n"
                + "            \"reduceAmount\": 0,\n" + "            \"source\": \"\",\n"
                + "            \"saleSources\": [],\n" + "            \"childs\": [],\n"
                + "            \"bizSubType\": 13,\n" + "            \"oriItemId\": \"\",\n"
                + "            \"sourceCode\": \"\",\n" + "            \"payType\": 1,\n"
                + "            \"reduceDetailList\": {},\n" + "            \"onSaleBookingPrice\": 0,\n"
                + "            \"cartPrice\": 500,\n" + "            \"accessCode\": [],\n"
                + "            \"cannotUseCoupon\": false,\n" + "            \"unitId\": \"\",\n"
                + "            \"ssuId\": 600005239,\n" + "            \"orderItemReduceList\": {},\n"
                + "            \"reduceItemList\": [],\n" + "            \"onsaleReduce\": 0,\n"
                + "            \"workHour\": 0.1,\n" + "            \"sku\": \"600005239\",\n"
                + "            \"department\": 0,\n" + "            \"checkoutPrice\": 0,\n"
                + "            \"unitPrice\": 5000,\n" + "            \"reduceList\": {},\n"
                + "            \"onSalePromotionIdMap\": {},\n" + "            \"originalCartPrice\": 0,\n"
                + "            \"bindMainAccessory\": false,\n" + "            \"cannotUseRedPacket\": false,\n"
                + "            \"packageId\": \"\",\n" + "            \"count\": 1,\n"
                + "            \"cannotUsePoint\": false,\n" + "            \"standardPrice\": 500,\n"
                + "            \"changePriceActType\": 0,\n" + "            \"goodsType\": 2,\n"
                + "            \"itemId\": \"600005239-1-1\",\n" + "            \"cannotJoinAct\": false,\n"
                + "            \"displayType\": 1,\n" + "            \"cannotUseEcard\": false,\n"
                + "            \"canAdjustPrice\": true,\n" + "            \"saleSource\": \"common\",\n"
                + "            \"storepriceReduce\": 0,\n" + "            \"properties\": \"\",\n"
                + "            \"maxUseEcardAmount\": 0\n" + "        },\n" + "        {\n"
                + "            \"joinOnsale\": false,\n" + "            \"marketPrice\": 500,\n"
                + "            \"preferentialInfos\": [],\n" + "            \"groupId\": 0,\n"
                + "            \"reduceAmount\": 0,\n" + "            \"source\": \"\",\n"
                + "            \"saleSources\": [],\n" + "            \"childs\": [],\n"
                + "            \"bizSubType\": 13,\n" + "            \"oriItemId\": \"\",\n"
                + "            \"sourceCode\": \"\",\n" + "            \"payType\": 1,\n"
                + "            \"reduceDetailList\": {},\n" + "            \"onSaleBookingPrice\": 0,\n"
                + "            \"cartPrice\": 500,\n" + "            \"accessCode\": [],\n"
                + "            \"cannotUseCoupon\": false,\n" + "            \"unitId\": \"\",\n"
                + "            \"ssuId\": 600005238,\n" + "            \"orderItemReduceList\": {},\n"
                + "            \"reduceItemList\": [],\n" + "            \"onsaleReduce\": 0,\n"
                + "            \"workHour\": 0.1,\n" + "            \"sku\": \"600005238\",\n"
                + "            \"department\": 0,\n" + "            \"checkoutPrice\": 0,\n"
                + "            \"unitPrice\": 5000,\n" + "            \"reduceList\": {},\n"
                + "            \"onSalePromotionIdMap\": {},\n" + "            \"originalCartPrice\": 0,\n"
                + "            \"bindMainAccessory\": false,\n" + "            \"cannotUseRedPacket\": false,\n"
                + "            \"packageId\": \"\",\n" + "            \"count\": 1,\n"
                + "            \"cannotUsePoint\": false,\n" + "            \"standardPrice\": 500,\n"
                + "            \"changePriceActType\": 0,\n" + "            \"goodsType\": 2,\n"
                + "            \"itemId\": \"600005238-1-1\",\n" + "            \"cannotJoinAct\": false,\n"
                + "            \"displayType\": 1,\n" + "            \"cannotUseEcard\": false,\n"
                + "            \"canAdjustPrice\": true,\n" + "            \"saleSource\": \"common\",\n"
                + "            \"storepriceReduce\": 0,\n" + "            \"properties\": \"\",\n"
                + "            \"maxUseEcardAmount\": 0\n" + "        },\n" + "        {\n"
                + "            \"joinOnsale\": false,\n" + "            \"marketPrice\": 100,\n"
                + "            \"preferentialInfos\": [],\n" + "            \"groupId\": 0,\n"
                + "            \"reduceAmount\": 0,\n" + "            \"source\": \"\",\n"
                + "            \"saleSources\": [],\n" + "            \"childs\": [],\n"
                + "            \"bizSubType\": 14,\n" + "            \"oriItemId\": \"\",\n"
                + "            \"sourceCode\": \"\",\n" + "            \"payType\": 5,\n"
                + "            \"reduceDetailList\": {},\n" + "            \"parentItemId\": \"600005239-1-1\",\n"
                + "            \"onSaleBookingPrice\": 0,\n" + "            \"cartPrice\": 100,\n"
                + "            \"accessCode\": [],\n" + "            \"cannotUseCoupon\": false,\n"
                + "            \"unitId\": \"\",\n" + "            \"ssuId\": 600000981,\n"
                + "            \"orderItemReduceList\": {},\n" + "            \"reduceItemList\": [],\n"
                + "            \"onsaleReduce\": 0,\n" + "            \"sku\": \"600000981\",\n"
                + "            \"department\": 0,\n" + "            \"checkoutPrice\": 0,\n"
                + "            \"reduceList\": {},\n" + "            \"onSalePromotionIdMap\": {},\n"
                + "            \"originalCartPrice\": 0,\n" + "            \"bindMainAccessory\": false,\n"
                + "            \"cannotUseRedPacket\": false,\n" + "            \"packageId\": \"\",\n"
                + "            \"count\": 4,\n" + "            \"cannotUsePoint\": false,\n"
                + "            \"standardPrice\": 100,\n" + "            \"changePriceActType\": 0,\n"
                + "            \"goodsType\": 2,\n" + "            \"itemId\": \"600000981-1-5\",\n"
                + "            \"cannotJoinAct\": false,\n" + "            \"displayType\": 1,\n"
                + "            \"cannotUseEcard\": false,\n" + "            \"canAdjustPrice\": true,\n"
                + "            \"saleSource\": \"common\",\n" + "            \"storepriceReduce\": 0,\n"
                + "            \"properties\": \"\",\n" + "            \"maxUseEcardAmount\": 0\n" + "        },\n"
                + "        {\n" + "            \"joinOnsale\": false,\n" + "            \"marketPrice\": 1,\n"
                + "            \"preferentialInfos\": [],\n" + "            \"groupId\": 0,\n"
                + "            \"reduceAmount\": 0,\n" + "            \"source\": \"\",\n"
                + "            \"saleSources\": [],\n" + "            \"childs\": [],\n"
                + "            \"bizSubType\": 14,\n" + "            \"oriItemId\": \"\",\n"
                + "            \"sourceCode\": \"\",\n" + "            \"payType\": 5,\n"
                + "            \"reduceDetailList\": {},\n" + "            \"parentItemId\": \"600005238-1-1\",\n"
                + "            \"onSaleBookingPrice\": 0,\n" + "            \"cartPrice\": 1,\n"
                + "            \"accessCode\": [],\n" + "            \"cannotUseCoupon\": false,\n"
                + "            \"unitId\": \"\",\n" + "            \"ssuId\": 600000977,\n"
                + "            \"orderItemReduceList\": {},\n" + "            \"reduceItemList\": [],\n"
                + "            \"onsaleReduce\": 0,\n" + "            \"sku\": \"600000977\",\n"
                + "            \"department\": 0,\n" + "            \"checkoutPrice\": 0,\n"
                + "            \"reduceList\": {},\n" + "            \"onSalePromotionIdMap\": {},\n"
                + "            \"originalCartPrice\": 0,\n" + "            \"bindMainAccessory\": false,\n"
                + "            \"cannotUseRedPacket\": false,\n" + "            \"packageId\": \"\",\n"
                + "            \"count\": 2,\n" + "            \"cannotUsePoint\": false,\n"
                + "            \"standardPrice\": 1,\n" + "            \"changePriceActType\": 0,\n"
                + "            \"goodsType\": 2,\n" + "            \"itemId\": \"600000977-1-5\",\n"
                + "            \"cannotJoinAct\": false,\n" + "            \"displayType\": 1,\n"
                + "            \"cannotUseEcard\": false,\n" + "            \"canAdjustPrice\": true,\n"
                + "            \"saleSource\": \"common\",\n" + "            \"storepriceReduce\": 0,\n"
                + "            \"properties\": \"\",\n" + "            \"maxUseEcardAmount\": 0\n" + "        }\n"
                + "    ],\n" + "    \"userIsFriend\": 0\n" + "}";


        String json2 = "{\n" + "\t\"usePoint\": false,\n" + "\t\"submitType\": 0,\n"
                + "\t\"orderId\": ****************,\n" + "\t\"channel\": 12,\n" + "\t\"shipmentExpense\": 0,\n"
                + "\t\"fromPriceProtect\": false,\n" + "\t\"vid\": \"MOCKD000000000003\",\n"
                + "\t\"useRedPacket\": false,\n" + "\t\"pointReduceAmount\": 0,\n" + "\t\"orgCode\": \"\",\n"
                + "\t\"ecardIds\": [],\n" + "\t\"useBeijingcoupon\": false,\n" + "\t\"calculateRedpacket\": true,\n"
                + "\t\"noSaveDbSubmit\": false,\n" + "\t\"shoppingMode\": 0,\n" + "\t\"couponCodes\": [],\n"
                + "\t\"bargainSize\": 10,\n" + "\t\"uidType\": \"\",\n" + "\t\"userId\": 3150000058,\n"
                + "\t\"couponIds\": [1010549423],\n" + "\t\"globalBusinessPartner\": \"car\",\n"
                + "\t\"shipmentId\": 2,\n" + "\t\"sourceApi\": 2,\n" + "\t\"cartList\": [{\n"
                + "\t\t\"joinOnsale\": false,\n" + "\t\t\"marketPrice\": 500,\n" + "\t\t\"preferentialInfos\": [],\n"
                + "\t\t\"groupId\": 0,\n" + "\t\t\"reduceAmount\": 0,\n" + "\t\t\"source\": \"\",\n"
                + "\t\t\"saleSources\": [],\n" + "\t\t\"childs\": [],\n" + "\t\t\"bizSubType\": 13,\n"
                + "\t\t\"oriItemId\": \"\",\n" + "\t\t\"sourceCode\": \"\",\n" + "\t\t\"payType\": 1,\n"
                + "\t\t\"reduceDetailList\": {},\n" + "\t\t\"onSaleBookingPrice\": 0,\n" + "\t\t\"cartPrice\": 500,\n"
                + "\t\t\"accessCode\": [],\n" + "\t\t\"cannotUseCoupon\": false,\n" + "\t\t\"unitId\": \"\",\n"
                + "\t\t\"ssuId\": 600005239,\n" + "\t\t\"orderItemReduceList\": {},\n" + "\t\t\"reduceItemList\": [],\n"
                + "\t\t\"onsaleReduce\": 0,\n" + "\t\t\"workHour\": 0.1,\n" + "\t\t\"sku\": \"600005239\",\n"
                + "\t\t\"department\": 0,\n" + "\t\t\"checkoutPrice\": 0,\n" + "\t\t\"unitPrice\": 5000,\n"
                + "\t\t\"reduceList\": {},\n" + "\t\t\"onSalePromotionIdMap\": {},\n"
                + "\t\t\"originalCartPrice\": 0,\n" + "\t\t\"bindMainAccessory\": false,\n"
                + "\t\t\"cannotUseRedPacket\": false,\n" + "\t\t\"packageId\": \"\",\n" + "\t\t\"count\": 1,\n"
                + "\t\t\"cannotUsePoint\": false,\n" + "\t\t\"standardPrice\": 500,\n"
                + "\t\t\"changePriceActType\": 0,\n" + "\t\t\"goodsType\": 2,\n"
                + "\t\t\"itemId\": \"600005239-1-1\",\n" + "\t\t\"cannotJoinAct\": false,\n"
                + "\t\t\"displayType\": 1,\n" + "\t\t\"cannotUseEcard\": false,\n" + "\t\t\"canAdjustPrice\": true,\n"
                + "\t\t\"saleSource\": \"common\",\n" + "\t\t\"storepriceReduce\": 0,\n" + "\t\t\"properties\": \"\",\n"
                + "\t\t\"maxUseEcardAmount\": 0\n" + "\t}, {\n" + "\t\t\"joinOnsale\": false,\n"
                + "\t\t\"marketPrice\": 500,\n" + "\t\t\"preferentialInfos\": [],\n" + "\t\t\"groupId\": 0,\n"
                + "\t\t\"reduceAmount\": 0,\n" + "\t\t\"source\": \"\",\n" + "\t\t\"saleSources\": [],\n"
                + "\t\t\"childs\": [],\n" + "\t\t\"bizSubType\": 13,\n" + "\t\t\"oriItemId\": \"\",\n"
                + "\t\t\"sourceCode\": \"\",\n" + "\t\t\"payType\": 1,\n" + "\t\t\"reduceDetailList\": {},\n"
                + "\t\t\"onSaleBookingPrice\": 0,\n" + "\t\t\"cartPrice\": 500,\n" + "\t\t\"accessCode\": [],\n"
                + "\t\t\"cannotUseCoupon\": false,\n" + "\t\t\"unitId\": \"\",\n" + "\t\t\"ssuId\": 600005238,\n"
                + "\t\t\"orderItemReduceList\": {},\n" + "\t\t\"reduceItemList\": [],\n" + "\t\t\"onsaleReduce\": 0,\n"
                + "\t\t\"workHour\": 0.1,\n" + "\t\t\"sku\": \"600005238\",\n" + "\t\t\"department\": 0,\n"
                + "\t\t\"checkoutPrice\": 0,\n" + "\t\t\"unitPrice\": 5000,\n" + "\t\t\"reduceList\": {},\n"
                + "\t\t\"onSalePromotionIdMap\": {},\n" + "\t\t\"originalCartPrice\": 0,\n"
                + "\t\t\"bindMainAccessory\": false,\n" + "\t\t\"cannotUseRedPacket\": false,\n"
                + "\t\t\"packageId\": \"\",\n" + "\t\t\"count\": 1,\n" + "\t\t\"cannotUsePoint\": false,\n"
                + "\t\t\"standardPrice\": 500,\n" + "\t\t\"changePriceActType\": 0,\n" + "\t\t\"goodsType\": 2,\n"
                + "\t\t\"itemId\": \"600005238-1-1\",\n" + "\t\t\"cannotJoinAct\": false,\n"
                + "\t\t\"displayType\": 1,\n" + "\t\t\"cannotUseEcard\": false,\n" + "\t\t\"canAdjustPrice\": true,\n"
                + "\t\t\"saleSource\": \"common\",\n" + "\t\t\"storepriceReduce\": 0,\n" + "\t\t\"properties\": \"\",\n"
                + "\t\t\"maxUseEcardAmount\": 0\n" + "\t}, {\n" + "\t\t\"joinOnsale\": false,\n"
                + "\t\t\"marketPrice\": 100,\n" + "\t\t\"preferentialInfos\": [],\n" + "\t\t\"groupId\": 0,\n"
                + "\t\t\"reduceAmount\": 0,\n" + "\t\t\"source\": \"\",\n" + "\t\t\"saleSources\": [],\n"
                + "\t\t\"childs\": [],\n" + "\t\t\"bizSubType\": 14,\n" + "\t\t\"oriItemId\": \"\",\n"
                + "\t\t\"sourceCode\": \"\",\n" + "\t\t\"payType\": 1,\n" + "\t\t\"reduceDetailList\": {},\n"
                + "\t\t\"parentItemId\": \"600005239-1-1\",\n" + "\t\t\"onSaleBookingPrice\": 0,\n"
                + "\t\t\"cartPrice\": 100,\n" + "\t\t\"accessCode\": [],\n" + "\t\t\"cannotUseCoupon\": false,\n"
                + "\t\t\"unitId\": \"\",\n" + "\t\t\"ssuId\": 600000981,\n" + "\t\t\"orderItemReduceList\": {},\n"
                + "\t\t\"reduceItemList\": [],\n" + "\t\t\"onsaleReduce\": 0,\n" + "\t\t\"sku\": \"600000981\",\n"
                + "\t\t\"department\": 0,\n" + "\t\t\"checkoutPrice\": 0,\n" + "\t\t\"reduceList\": {},\n"
                + "\t\t\"onSalePromotionIdMap\": {},\n" + "\t\t\"originalCartPrice\": 0,\n"
                + "\t\t\"bindMainAccessory\": false,\n" + "\t\t\"cannotUseRedPacket\": false,\n"
                + "\t\t\"packageId\": \"\",\n" + "\t\t\"count\": 4,\n" + "\t\t\"cannotUsePoint\": false,\n"
                + "\t\t\"standardPrice\": 100,\n" + "\t\t\"changePriceActType\": 0,\n" + "\t\t\"goodsType\": 2,\n"
                + "\t\t\"itemId\": \"600000981-1-1\",\n" + "\t\t\"cannotJoinAct\": false,\n"
                + "\t\t\"displayType\": 1,\n" + "\t\t\"cannotUseEcard\": false,\n" + "\t\t\"canAdjustPrice\": true,\n"
                + "\t\t\"saleSource\": \"common\",\n" + "\t\t\"storepriceReduce\": 0,\n" + "\t\t\"properties\": \"\",\n"
                + "\t\t\"maxUseEcardAmount\": 0\n" + "\t}, {\n" + "\t\t\"joinOnsale\": false,\n"
                + "\t\t\"marketPrice\": 1,\n" + "\t\t\"preferentialInfos\": [],\n" + "\t\t\"groupId\": 0,\n"
                + "\t\t\"reduceAmount\": 0,\n" + "\t\t\"source\": \"\",\n" + "\t\t\"saleSources\": [],\n"
                + "\t\t\"childs\": [],\n" + "\t\t\"bizSubType\": 14,\n" + "\t\t\"oriItemId\": \"\",\n"
                + "\t\t\"sourceCode\": \"\",\n" + "\t\t\"payType\": 1,\n" + "\t\t\"reduceDetailList\": {},\n"
                + "\t\t\"parentItemId\": \"600005238-1-1\",\n" + "\t\t\"onSaleBookingPrice\": 0,\n"
                + "\t\t\"cartPrice\": 1,\n" + "\t\t\"accessCode\": [],\n" + "\t\t\"cannotUseCoupon\": false,\n"
                + "\t\t\"unitId\": \"\",\n" + "\t\t\"ssuId\": 600000977,\n" + "\t\t\"orderItemReduceList\": {},\n"
                + "\t\t\"reduceItemList\": [],\n" + "\t\t\"onsaleReduce\": 0,\n" + "\t\t\"sku\": \"600000977\",\n"
                + "\t\t\"department\": 0,\n" + "\t\t\"checkoutPrice\": 0,\n" + "\t\t\"reduceList\": {},\n"
                + "\t\t\"onSalePromotionIdMap\": {},\n" + "\t\t\"originalCartPrice\": 0,\n"
                + "\t\t\"bindMainAccessory\": false,\n" + "\t\t\"cannotUseRedPacket\": false,\n"
                + "\t\t\"packageId\": \"\",\n" + "\t\t\"count\": 2,\n" + "\t\t\"cannotUsePoint\": false,\n"
                + "\t\t\"standardPrice\": 1,\n" + "\t\t\"changePriceActType\": 0,\n" + "\t\t\"goodsType\": 2,\n"
                + "\t\t\"itemId\": \"600000977-1-1\",\n" + "\t\t\"cannotJoinAct\": false,\n"
                + "\t\t\"displayType\": 1,\n" + "\t\t\"cannotUseEcard\": false,\n" + "\t\t\"canAdjustPrice\": true,\n"
                + "\t\t\"saleSource\": \"common\",\n" + "\t\t\"storepriceReduce\": 0,\n" + "\t\t\"properties\": \"\",\n"
                + "\t\t\"maxUseEcardAmount\": 0\n" + "\t}],\n" + "\t\"userIsFriend\": 0\n" + "}";

        CheckoutPromotionV2Request request = GsonUtil.fromJson(json,
                CheckoutPromotionV2Request.class);
//        request.setVid("MOCKD000000000003");
//        request.setGetCouponList(true);

        CheckoutPromotionRequest request1 = GsonUtil.fromJson(json2, CheckoutPromotionRequest.class);

//        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(
//                request);

        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(
                request1);

        log.info("{}", GsonUtil.toJson(result));
    }

    @Test
    public void testPurchaseSubsidyCheckoutV2() {
        String str = "{\n" +
                "    \"bargainSize\": 10,\n" +
                "    \"calculateRedpacket\": true,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"cartPrice\": 16900,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 1,\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"2221000323_0_buy\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 16900,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"23080\",\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"standardPrice\": 16900,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 1716514106\n" +
                "        }\n" +
                "    ],\n" +
                "    \"cityId\": 335,\n" +
                "    \"clientId\": 180100031055,\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"getCouponList\": true,\n" +
                "    \"globalBusinessPartner\": \"mishop\",\n" +
                "    \"noSaveDbSubmit\": false,\n" +
                "    \"orderId\": 0,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"showType\": 1,\n" +
                "    \"submitType\": 0,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"useDefaultCoupon\": true,\n" +
                "    \"usePoint\": false,\n" +
                "    \"useRedPacket\": true,\n" +
                "    \"userId\": 3150425861,\n" +
                "    \"userIsFriend\": 1\n" +
                "}";
        CheckoutPromotionV2Request request = GsonUtil.fromJson(str, CheckoutPromotionV2Request.class);
        //request.setIdCard("123");
        request.setUsePurchaseSubsidy(true);
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);
        System.out.println();

    }

    @Test
    public void testPurchaseSubsidyCheckout() {
        String str = "{\n" +
                "    \"bargainSize\": 0,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [\n" +
                "                \"doorstep_access_2017\"\n" +
                "            ],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"cartPrice\": 159900,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 1,\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"2230000355_0_buy\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 159900,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"6823\",\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"standardPrice\": 159900,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 1715582288\n" +
                "        }\n" +
                "    ],\n" +
                "    \"cityId\": 36,\n" +
                "    \"clientId\": 180100031052,\n" +
                "    \"couponIds\": [],\n" +
                "    \"ecardConsumeDetail\": {},\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"mishop\",\n" +
                "    \"noSaveDbSubmit\": false,\n" +
                "    \"orderId\": ****************,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"submitType\": 0,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"usePoint\": false,\n" +
                "    \"useRedPacket\": false,\n" +
                "    \"userId\": 3150425861,\n" +
                "    \"userIsFriend\": 1\n" +
                "}";
        CheckoutPromotionRequest request = GsonUtil.fromJson(str, CheckoutPromotionRequest.class);
        request.setOrderId(5240821881061732L);
        request.setIdCard("123");
        request.setUsePurchaseSubsidy(true);
        Result<CheckoutPromotionResponse> res = promotionDubboService.checkoutPromotion(request);
        System.out.println(res);


    }

    @Test
    public void testPurchaseSubsidyCheckoutForOffline() {
        String str = "{\n" +
                "    \"bargainSize\": 100,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                25\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseCouponTypes\": [],\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"cartPrice\": 250000,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"childs\": [],\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 0,\n" +
                "            \"goodsName\": \"自然风 3匹新二级能效空调（室内机） 白色\",\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"47195_binding_47195_2_P_0\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 250000,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"47195_binding_47195_2_P_0\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"reduceList\": {},\n" +
                "            \"saleSource\": \"\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"30289\",\n" +
                "            \"source\": \"base\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"standardPrice\": 250000,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 0\n" +
                "        },\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                25\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseCouponTypes\": [],\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"cartPrice\": 150000,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"childs\": [],\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 0,\n" +
                "            \"goodsName\": \"自然风 3匹新二级能效空调（室外机） 白色\",\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"1135000226_47194_binding_47194_2_P_47195\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 150000,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"47194_binding_47194_2_P_47195\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"packageId\": \"1135000231\",\n" +
                "            \"childs\": [\n" +
                "                {\n" +
                "                    \"cartPrice\": 100000,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 100000,\n" +
                "                    \"sku\": \"18853\",\n" +
//                "                    \"ssuId\": 123,\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"cartPrice\": 50000,\n" +
                "                    \"checkoutPrice\": 0,\n" +
                "                    \"count\": 1,\n" +
                "                    \"lowerPrice\": 0,\n" +
                "                    \"onSaleBookingPrice\": 0,\n" +
                "                    \"onsaleReduce\": 0,\n" +
                "                    \"originalSellPrice\": 0,\n" +
                "                    \"sellPrice\": 50000,\n" +
                "                    \"sku\": \"4090\",\n" +
//                "                    \"ssuId\": 234,\n" +
                "                    \"storepriceReduce\": 0,\n" +
                "                    \"unitId\": \"\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"reduceList\": {},\n" +
                "            \"saleSource\": \"\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"\",\n" +
                "            \"source\": \"base\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"standardPrice\": 150000,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 0\n" +
                "        },\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                8\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseCouponTypes\": [],\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"cartPrice\": 100000,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"childs\": [],\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 0,\n" +
                "            \"goodsName\": \"自然风 米家空调 1.5匹新1级能效 银色（室内机） 银色\",\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"4908_binding_4908_2_P_0\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 100000,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceList\": {},\n" +
                "            \"saleSource\": \"\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"4908\",\n" +
                "            \"source\": \"bind\",\n" +
                "            \"sourceCode\": \"3084086\",\n" +
                "            \"standardPrice\": 100000,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 0\n" +
                "        },\n" +
                "        \n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                8\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseCouponTypes\": [],\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"cartPrice\": 200000,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"childs\": [],\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 0,\n" +
                "            \"goodsName\": \"自然风 米家空调 1.5匹新1级能效（室外机） 白色\",\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"1586_binding_1586_2_P_4908\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 200000,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"parentItemId\": \"4908_binding_4908_2_P_0\",\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceList\": {},\n" +
                "            \"saleSource\": \"\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"1586\",\n" +
                "            \"source\": \"bind\",\n" +
                "            \"sourceCode\": \"3084086\",\n" +
                "            \"standardPrice\": 200000,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 0\n" +
                "        }" +
                "    ],\n" +
                "    \"cityId\": 36,\n" +
                "    \"clientId\": 180100041075,\n" +
                "    \"couponCodes\": [],\n" +
                "    \"couponIds\": [\n" +
                "    ],\n" +
                "    \"ecardIds\": [],\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"\",\n" +
                "    \"noSaveDbSubmit\": false,\n" +
                "    \"orderId\": 1246532093766130,\n" +
                "    \"orderTime\": 1717209636,\n" +
                "    \"orgCode\": \"JM73690\",\n" +
                "    \"payBarCode\": \"\",\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": 0,\n" +
                "    \"shoppingMode\": 1,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"submitType\": 0,\n" +
                "    \"thirdPromotions\": [\n" +
                "        {\n" +
                "            \"businessType\": 0,\n" +
                "            \"params\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"businessType\": 0,\n" +
                "            \"params\": {\n" +
                "                \"contractPeriod\": \"0\",\n" +
                "                \"discount\": \"0\",\n" +
                "                \"packageAmount\": \"0\",\n" +
                "                \"packageId\": \"\"\n" +
                "            }\n" +
                "        }\n" +
                "    ],\n" +
                "    \"uidType\": \"\",\n" +
                "    \"unicomHsBalance\": 0,\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"usePoint\": false,\n" +
                "    \"useRedPacket\": false,\n" +
                "    \"userId\": 2653947322,\n" +
                "    \"userIsFriend\": 0\n" +
                "}";
        CheckoutPromotionRequest request = GsonUtil.fromJson(str, CheckoutPromotionRequest.class);
        Long orderId = 9333334L;
        Long userId = 1234826L;
        request.setOrderId(orderId);
        request.setUserId(userId);
        request.setOrgCode("JM002");
        request.setIdCard(DigestUtil.md5Hex("identity_id_card" + "543721"));
        request.setUsePurchaseSubsidy(true);

        // 结算页添加 noSaveDbSubmit = true
        request.setNoSaveDbSubmit(true);

        Result<CheckoutPromotionResponse> res = promotionDubboService.checkoutPromotion(request);

        RollbackPromotionRequest rollbackPromotionRequest = new RollbackPromotionRequest();
        rollbackPromotionRequest.setOrderId(orderId);
        rollbackPromotionRequest.setUserId(userId);
        Result<RollbackPromotionResponse> rollbackResult = promotionDubboService.rollbackPromotion(rollbackPromotionRequest);
        System.out.println();


    }

    @Test
    public void testCheckoutV2() {
        String str = "{\"cartList\":[{\"itemId\":\"600013315_1_buy\",\"sku\":\"\",\"packageId\":\"600013315\",\"count\":1,\"standardPrice\":2400,\"cartPrice\":2400,\"prePrice\":0,\"reduceAmount\":0,\"reduceList\":null,\"childs\":[{\"sku\":\"11111\",\"ssuId\":2212700001,\"sellPrice\":1000,\"count\":2,\"onSaleBookingPrice\":0,\"unitId\":\"\",\"lowerPrice\":0,\"originalSellPrice\":0,\"groupId\":0},{\"sku\":\"15591\",\"ssuId\":2171500037,\"sellPrice\":400,\"count\":1,\"onSaleBookingPrice\":0,\"unitId\":\"\",\"lowerPrice\":0,\"originalSellPrice\":0,\"groupId\":1}],\"source\":\"\",\"sourceCode\":\"\",\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":1735110499,\"displayType\":1,\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"accessCode\":[\"doorstep_access_2017\"],\"marketPrice\":2400,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[40,41,42],\"cannotUseCouponTypes\":null,\"saleSource\":\"common\",\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"preferentialInfos\":null,\"checkoutPrice\":0,\"subItemList\":null,\"saleSources\":[],\"reduceShareMap\":null,\"ssuId\":600013315,\"ssuType\":1}],\"clientId\":180100031016,\"userId\":3150445185,\"couponIds\":null,\"couponCodes\":null,\"ecardIds\":null,\"bargainSize\":10,\"ecardConsumeDetail\":null,\"useRedPacket\":false,\"calculateRedpacket\":true,\"getCouponList\":true,\"useBeijingcoupon\":false,\"orgCode\":\"\",\"uidType\":\"\",\"cityId\":36,\"shoppingMode\":0,\"shipmentExpense\":0,\"shipmentId\":2,\"useDefaultCoupon\":false,\"globalBusinessPartner\":\"\",\"personalInfo\":\"\",\"usePurchaseSubsidy\":false,\"thirdPromotions\":null,\"region\":{\"province\":2,\"city\":36,\"district\":384,\"area\":384010}}";

        CheckoutPromotionV2Request request = GsonUtil.fromJson(str, CheckoutPromotionV2Request.class);

        Result<CheckoutPromotionV2Response> resp = promotionDubboService.checkoutPromotionV2(request);

        log.info("resp = {}", GsonUtil.toJson(resp));

    }


    @Test
    public void testGetProductCurrentPrice_NewPackage() {
        String s = "{\"userId\":3150000058,\"clientId\":180100031052,\"cartList\":[{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"ssu_600013293_1\",\"sku\":\"\",\"packageId\":\"\",\"ssuId\":600013293,\"ssuType\":1,\"count\":1,\"standardPrice\":210000,\"cartPrice\":210000,\"prePrice\":0,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":0,\"displayType\":0,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":210000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[],\"cannotUseCouponTypes\":[],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"cannotUsePoint\":false,\"canAdjustPrice\":false}],\"couponId\":0,\"couponCode\":\"\",\"redPacketUsedList\":[],\"orderId\":****************}";
        GetProductCurrentPriceRequest request = GsonUtil.fromJson(s, GetProductCurrentPriceRequest.class);
        request.setOrderId(System.currentTimeMillis());
        System.out.println(GsonUtil.toJson(request));
        Result<GetProductCurrentPriceResponse> result = promotionDubboService.getProductCurrentPrice(request);
        System.out.println(GsonUtil.toJson(result));
        Assert.assertEquals(0, result.getCode());
    }

    @Test
    public void testCartCheckoutPromotion_0508() {
        log.info("========================================================================");

        String str = "{\"cartList\":[{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"2183700036_0_buy\",\"sku\":\"18922\",\"packageId\":\"\",\"ssuId\":0,\"ssuType\":0,\"goodsName\":\"小米手环3 NFC版 黑色\",\"count\":1,\"standardPrice\":49900,\"cartPrice\":49900,\"prePrice\":0,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":1747126134,\"displayType\":1,\"oriItemId\":\"2183700036_0_buy\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":49900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[],\"cannotUseCouponTypes\":[],\"saleSource\":\"common\",\"saleSources\":[\"common\"],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":49900,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"cannotUsePoint\":false,\"canAdjustPrice\":false,\"maintenanceInfo\":{\"canAdjustPrice\":false,\"cannotUseCouponServiceTypes\":[]}}],\"channel\":1,\"clientId\":180100031016,\"userId\":3150425883,\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"mishop\",\"getCouponList\":false,\"showType\":1,\"useDefaultCoupon\":true,\"submitType\":0}";
        CartPromotionRequest req = GsonUtil.fromJson(str, CartPromotionRequest.class);

        Result<CartPromotionResponse> resp = promotionDubboService.cartCheckoutPromotion(req);

        log.info("testCartCheckoutPromotion_0508 req = {}, resp = {}", GsonUtil.toJson(req), GsonUtil.toJson(resp));
        log.info("========================================================================");
    }

}