package com.xiaomi.nr.promotion.rpc.mdpromotionadmin;

import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.params.PageResponse;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.util.DateTimeUtil;
import com.xiaomi.nr.promotion.util.GsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/2/9
 */
public class PromotionAdminCustomServiceProxyTest extends BaseTest {
    @Autowired
    private PromotionAdminCustomServiceProxy promotionAdminCustomServiceProxy;

    @Test
    public void testQueryActivityList() {
        List<ActivityConfig> configList = promotionAdminCustomServiceProxy.queryActivityList();
        System.out.println(GsonUtil.toJson(configList));
    }

    @Test
    public void testPageQueryActivityList() {
        Long timeNow = DateTimeUtil.getCurrentTimes(TimeUnit.SECONDS);
        PageResponse<ActivityConfig> pageResponse = promotionAdminCustomServiceProxy.pageQueryActivityList(timeNow, 1, 20);
        System.out.println(GsonUtil.toJson(pageResponse));
    }
}