package com.xiaomi.nr.promotion.rpc.mdpromotionadmin;

import com.google.common.collect.Lists;
import com.xiaomi.nr.md.promotion.admin.api.constant.ChannelEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.PromotionTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.SourceAppEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityFilterTip;
import com.xiaomi.nr.md.promotion.admin.api.dto.params.ActivityConfigForPromotionResponse;
import com.xiaomi.nr.md.promotion.admin.api.dto.params.ActivityListQueryRequest;
import com.xiaomi.nr.md.promotion.admin.api.dto.params.PageResponse;
import com.xiaomi.nr.md.promotion.admin.api.service.publish.PromotionAdminCustomService;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.util.DateTimeUtil;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/2/9
 */
public class PromotionAdminCustomServiceProxyTest extends BaseTest {
    @Autowired
    private PromotionAdminCustomServiceProxy promotionAdminCustomServiceProxy;
    @Autowired
    private PromotionAdminCustomService promotionAdminCustomService;

    @Test
    public void testQueryActivityList() {
        List<ActivityConfig> configList = promotionAdminCustomServiceProxy.queryActivityList();
        System.out.println(GsonUtil.toJson(configList));
    }

    @Test
    public void testPageQueryActivityList() {
        Long timeNow = DateTimeUtil.getCurrentTimes(TimeUnit.SECONDS);
        PageResponse<ActivityConfig> pageResponse = promotionAdminCustomServiceProxy.pageQueryActivityList(timeNow, 1, 20);
        System.out.println(GsonUtil.toJson(pageResponse));
    }

    private static final long SEVENS_DAYS_TO_SECONDS = 8 * 24 * 3600L;

    @Test
    public void test_queryActivityBySeqIdV2() {
        long startTime = System.currentTimeMillis();
        long now = startTime / 1000;
        ActivityListQueryRequest request = new ActivityListQueryRequest();
        request.setBeginTime(now);
        request.setEndTime(now + SEVENS_DAYS_TO_SECONDS);
        request.setSequenceId(560000000000348701L);
        Map<Integer, ActivityFilterTip> activityFilterTips = new HashMap<>();
        // CRM
        ActivityFilterTip crm = new ActivityFilterTip();
        crm.setPromotionTypes(Lists.newArrayList(
                PromotionTypeEnum.B2T_CHANNEL_PRICE.code,
                PromotionTypeEnum.B2T_STEP_PRICE.code,
                PromotionTypeEnum.B2T_VIP_DISCOUNT.code));
        crm.setChannels(Lists.newArrayList(
                ChannelEnum.B2T_C_CUSTOMER.value,
                ChannelEnum.B2T_GOV_BIG_CUSTOMER.value,
                ChannelEnum.B2T_MIJIA_BIG_CUSTOMER.value));
        activityFilterTips.put(SourceAppEnum.CRM.code, crm);

        // 团购
        ActivityFilterTip b2tAdmin = new ActivityFilterTip();
        b2tAdmin.setPromotionTypes(Lists.newArrayList(
                PromotionTypeEnum.B2T_CHANNEL_PRICE.code,
                PromotionTypeEnum.B2T_STEP_PRICE.code,
                PromotionTypeEnum.B2T_VIP_DISCOUNT.code));
        b2tAdmin.setChannels(Lists.newArrayList(
                ChannelEnum.B2T_C_CUSTOMER.value,
                ChannelEnum.B2T_GOV_BIG_CUSTOMER.value,
                ChannelEnum.B2T_MIJIA_BIG_CUSTOMER.value));
        activityFilterTips.put(SourceAppEnum.B2T_ADMIN.code, b2tAdmin);

        // 国补
        ActivityFilterTip govSubsidy = new ActivityFilterTip();
        govSubsidy.setPromotionTypes(Lists.newArrayList(
                PromotionTypeEnum.GOVERNMENT_SUBSIDY.code));
        govSubsidy.setChannels(Lists.newArrayList(
                ChannelEnum.MISHOP.value,
                ChannelEnum.DIRECT.value));
        request.setActivityFilterTips(activityFilterTips);

        Result<ActivityConfigForPromotionResponse> result = promotionAdminCustomService.queryActivityBySeqIdV2(request);
        System.out.println(GsonUtil.toJson(result));
    }
}