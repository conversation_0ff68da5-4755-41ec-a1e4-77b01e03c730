package com.xiaomi.nr.promotion.v2.activity.carmaintenance;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.activity.carmaintenance.MaintenanceDiscountActivity;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.enums.ActFrequencyEnum;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.MaintenanceDiscountPromotionConfig;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

@Slf4j
public class MaintenanceDiscountActivityTest extends BaseTestV2 {

    @InjectMocks
    private MaintenanceDiscountActivity activity;

    @Mock
    private DSLEngine dslEngine;

    @Mock
    private DSLStream dslStream;

    private MaintenanceDiscountPromotionConfig config;
    private LocalContext context;

    @BeforeEach
    public void setUp() {
        // 初始化配置
        config = new MaintenanceDiscountPromotionConfig();
        config.setDescription("测试维修保养折扣活动");
        config.setFrequency(ActFrequencyEnum.NONE);

        // 初始化上下文
        context = new LocalContext();
    }

    @Test
    public void test_getType() {
        // 执行测试
        PromotionToolType type = activity.getType();

        // 验证结果
        assertEquals(PromotionToolType.MAINTENANCE_REPAIR_DISCOUNT, type);
    }

    @Test
    public void test_getBizPlatform() {
        // 执行测试
        BizPlatformEnum platform = activity.getBizPlatform();

        // 验证结果
        assertEquals(BizPlatformEnum.MAINTENANCE_REPAIR, platform);
    }

    @Test
    public void test_getProductGoodsAct() throws BizError {
        // 准备测试数据
        Long clientId = 123L;
        String orgCode = "TEST_ORG";
        List<String> skuPackageList = Lists.newArrayList("SKU1", "SKU2");

        // 执行测试
        Object result = activity.getProductGoodsAct(clientId, orgCode, skuPackageList);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void test_getActivityDetail() {
        // 执行测试
        Object result = activity.getActivityDetail();

        // 验证结果
        assertNull(result);
    }
}