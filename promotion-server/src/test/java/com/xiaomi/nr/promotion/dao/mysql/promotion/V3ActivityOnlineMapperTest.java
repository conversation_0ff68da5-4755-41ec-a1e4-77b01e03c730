package com.xiaomi.nr.promotion.dao.mysql.promotion;

import com.xiaomi.nr.promotion.BaseTest;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 在线活动测试
 *
 * <AUTHOR>
 * @date 2021/5/17
 */
public class V3ActivityOnlineMapperTest extends BaseTest {

    @Autowired
    private V3ActivityOnlineMapper v3ActivityOnlineMapper;

    @Test
    public void testListIdApproved() {
        Long nowSeconds = TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis());
        List<Long> idList = v3ActivityOnlineMapper.listIdApproved(nowSeconds);
        System.out.println(idList);
        System.out.println(nowSeconds);
        Assert.assertNotNull(idList);
    }
}