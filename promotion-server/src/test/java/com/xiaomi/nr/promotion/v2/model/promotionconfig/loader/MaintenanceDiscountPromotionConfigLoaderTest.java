package com.xiaomi.nr.promotion.v2.model.promotionconfig.loader;

import com.xiaomi.nr.md.promotion.admin.api.constant.ProductWhileBlackEnum;
import com.xiaomi.nr.md.promotion.admin.api.constant.ScopeTypeEnum;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityScope;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ProductPolicy;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.MaintenanceDiscountRule;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.rule.specification.DiscountSpecification;
import com.xiaomi.nr.promotion.enums.ActFrequencyEnum;
import com.xiaomi.nr.promotion.model.common.QuotaLevel;
import com.xiaomi.nr.promotion.model.promotionconfig.MaintenanceDiscountPromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.loader.MaintenanceDiscountPromotionConfigLoader;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

//@ExtendWith(MockitoExtension.class)
public class MaintenanceDiscountPromotionConfigLoaderTest extends BaseTestV2 {

    @InjectMocks
    private MaintenanceDiscountPromotionConfigLoader loader;

    private ActivityConfig activityConfig;

    private MaintenanceDiscountPromotionConfig promotionConfig;

    @BeforeEach
    void setUp() {
        activityConfig = new ActivityConfig();
        activityConfig.setId(1L);
        promotionConfig = new MaintenanceDiscountPromotionConfig();
    }

    @Test
    void test_load_success() throws BizError {
        // Given
        // 设置商品策略
        List<ProductPolicy> productPolicyList = new ArrayList<>();
        ProductPolicy validPolicy = new ProductPolicy();
        validPolicy.setProductId(1L);

        MaintenanceDiscountRule.ProductRule validProductRule = MaintenanceDiscountRule.ProductRule.builder().limitType(ProductWhileBlackEnum.WHITE.value).build();
        validPolicy.setRule(GsonUtil.toJson(validProductRule));

        ProductPolicy invalidPolicy = new ProductPolicy();
        invalidPolicy.setProductId(2L);
        MaintenanceDiscountRule.ProductRule invalidProductRule = MaintenanceDiscountRule.ProductRule.builder().limitType(ProductWhileBlackEnum.BLACK.value).build();
        invalidPolicy.setRule(GsonUtil.toJson(invalidProductRule));

        productPolicyList.add(validPolicy);
        productPolicyList.add(invalidPolicy);
        activityConfig.setProductPolicyList(productPolicyList);

        // 设置活动范围
        List<ActivityScope> activityScopeList = new ArrayList<>();
        ActivityScope scope = new ActivityScope();
        scope.setScopeType(ScopeTypeEnum.ORDER_FROM.code);
        scope.setScopeValue("1,2,3");
        activityScopeList.add(scope);
        activityConfig.setActivityScopeList(activityScopeList);

        // 设置折扣规则
        MaintenanceDiscountRule discountRule = MaintenanceDiscountRule.builder()
                .description("desc")
                .carIdentityId("123")
                .userLimitNum(5)
                .build();
        List<DiscountSpecification> specificationList = new ArrayList<>();
        DiscountSpecification spec = new DiscountSpecification();
        spec.setReduce(80L);
        spec.setMaxReduce(100L);
        spec.setThreshold(1L);
        specificationList.add(spec);
        discountRule.setSpecificationList(specificationList);
        activityConfig.setRule(GsonUtil.toJson(discountRule));

        // When
        loader.load(promotionConfig, activityConfig);

        // Then
        assertNotNull(promotionConfig.getJoinGoods());
        assertEquals(1, promotionConfig.getJoinGoods().getSsuId().size());
        assertEquals(1L, promotionConfig.getJoinGoods().getSsuId().get(0));

        assertEquals(1, promotionConfig.getInvalidGoods().size());
        assertEquals(2L, promotionConfig.getInvalidGoods().get(0));

        assertEquals(3, promotionConfig.getSupportWorkOrderTypes().size());
        assertTrue(promotionConfig.getSupportWorkOrderTypes().contains(1));
        assertTrue(promotionConfig.getSupportWorkOrderTypes().contains(2));
        assertTrue(promotionConfig.getSupportWorkOrderTypes().contains(3));

        assertEquals(5, promotionConfig.getUserJoinNumLimit());
        assertEquals(123, promotionConfig.getCarIdentityId());
        assertEquals("desc", promotionConfig.getDescription());
        assertEquals(ActFrequencyEnum.NONE, promotionConfig.getFrequency());

        assertEquals(1, promotionConfig.getLevelList().size());
        QuotaLevel level = promotionConfig.getLevelList().get(0);
        assertEquals(80, level.getReduceDiscount());
        assertEquals(100.0, level.getMaxReducePrice());
        assertEquals(1, level.getQuotas().get(0).getCount());
    }

    @Test
    void test_load_fail() {
        // Case 1: activityConfig is null
        BizError exception = assertThrows(BizError.class, () ->
                loader.load(promotionConfig, (ActivityConfig) null));
        assertEquals("activityInfo is null", exception.getMessage());

        // Case 2: invalid rule
        exception = assertThrows(BizError.class, () ->
                loader.load(promotionConfig, activityConfig));
        assertEquals("maintenance discount rule is invalid", exception.getMessage());
    }
} 