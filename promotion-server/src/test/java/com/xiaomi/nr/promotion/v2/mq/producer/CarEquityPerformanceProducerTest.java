package com.xiaomi.nr.promotion.v2.mq.producer;

import com.xiaomi.nr.promotion.mq.producer.CarEquityPerformanceProducer;
import com.xiaomi.nr.promotion.mq.producer.entity.CarEquityPerformanceMessage;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

//@ExtendWith(MockitoExtension.class)
public class CarEquityPerformanceProducerTest extends BaseTestV2 {

    @Mock
    private RocketMQTemplate rocketMQTemplate;

    @InjectMocks
    private CarEquityPerformanceProducer carEquityPerformanceProducer;

    private final String topic = "test_topic";
    private final String tag = "test_tag";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(carEquityPerformanceProducer, "topic", topic);
        ReflectionTestUtils.setField(carEquityPerformanceProducer, "tag", tag);
        carEquityPerformanceProducer.setRocketMqTemplate(rocketMQTemplate);
    }

    @Test
    void test_sendMessage_success() {
        // Given
        CarEquityPerformanceMessage message = CarEquityPerformanceMessage.builder()
                .sourceSvc("ACTIVITY")
                .equityKey("test-equity-key")
                .vid("test-vid")
                .implAbilityId("123")
                .usageRecordId("456")
                .usageOrderId(789L)
                .deleted(false)
                .build();
        List<CarEquityPerformanceMessage> messages = Collections.singletonList(message);

        SendResult mockResult = new SendResult();
        when(rocketMQTemplate.syncSend(anyString(), any(Object.class))).thenReturn(mockResult);

        // When
        carEquityPerformanceProducer.sendMessage(messages);

        // Then
        verify(rocketMQTemplate).syncSend(eq(topic), any(Object.class));
    }

    @Test
    void test_sendMessageWithKey_success() {
        // Given
        CarEquityPerformanceMessage message = CarEquityPerformanceMessage.builder()
                .sourceSvc("ACTIVITY")
                .equityKey("test-equity-key")
                .vid("test-vid")
                .implAbilityId("123")
                .usageRecordId("456")
                .usageOrderId(789L)
                .deleted(false)
                .build();
        List<CarEquityPerformanceMessage> messages = Collections.singletonList(message);
        String key = "test-key";

        SendResult mockResult = new SendResult();
        when(rocketMQTemplate.syncSend(anyString(), any(Message.class))).thenReturn(mockResult);

        // When
        carEquityPerformanceProducer.sendMessage(messages, key);

        // Then
        verify(rocketMQTemplate).syncSend(eq(topic + ":" + tag), any(Message.class));
    }

    @Test
    void test_sendMessage_retryOnFailure() {
        // Given
        CarEquityPerformanceMessage message = CarEquityPerformanceMessage.builder()
                .sourceSvc("ACTIVITY")
                .equityKey("test-equity-key")
                .vid("test-vid")
                .implAbilityId("123")
                .usageRecordId("456")
                .usageOrderId(789L)
                .deleted(false)
                .build();
        List<CarEquityPerformanceMessage> messages = Collections.singletonList(message);

        // First call throws exception, second call succeeds
        SendResult mockResult = new SendResult();
        when(rocketMQTemplate.syncSend(anyString(), any(Object.class)))
                .thenThrow(new RuntimeException("Send failed"))
                .thenReturn(mockResult);

        // When
        carEquityPerformanceProducer.sendMessage(messages);

        // Then
        verify(rocketMQTemplate, times(2)).syncSend(eq(topic), any(Object.class));
    }

    @Test
    void test_sendMessageWithKey_retryOnFailure() {
        // Given
        CarEquityPerformanceMessage message = CarEquityPerformanceMessage.builder()
                .sourceSvc("ACTIVITY")
                .equityKey("test-equity-key")
                .vid("test-vid")
                .implAbilityId("123")
                .usageRecordId("456")
                .usageOrderId(789L)
                .deleted(false)
                .build();
        List<CarEquityPerformanceMessage> messages = Collections.singletonList(message);
        String key = "test-key";

        // First call throws exception, second call succeeds
        SendResult mockResult = new SendResult();
        when(rocketMQTemplate.syncSend(anyString(), any(Message.class)))
                .thenThrow(new RuntimeException("Send failed"))
                .thenReturn(mockResult);

        // When
        carEquityPerformanceProducer.sendMessage(messages, key);

        // Then
        verify(rocketMQTemplate, times(2)).syncSend(eq(topic + ":" + tag), any(Message.class));
    }
}
