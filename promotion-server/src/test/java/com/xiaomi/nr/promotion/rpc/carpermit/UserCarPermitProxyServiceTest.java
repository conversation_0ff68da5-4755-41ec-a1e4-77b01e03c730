package com.xiaomi.nr.promotion.rpc.carpermit;

import com.mi.car.iccc.user.permit.common.model.UserPermitResponse;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.enums.UserCarPermitEnum;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/14 00:30
 */
public class UserCarPermitProxyServiceTest extends BaseTest {

    @Autowired
    private UserCarPermitProxyService userCarPermitProxyService;

    @Test
    public void queryUserPermit(){
        UserCarPermitEnum userCarPermitEnum = userCarPermitProxyService.queryUserPermit(3150446069L);
        Assert.assertTrue(Objects.isNull(userCarPermitEnum));
    }

    @Test
    public void queryUserPermit_V2(){
        UserPermitResponse userPermitResponse = userCarPermitProxyService.queryCarPermit(3150446069L);
        Assert.assertTrue(Objects.isNull(userPermitResponse));
    }
}
