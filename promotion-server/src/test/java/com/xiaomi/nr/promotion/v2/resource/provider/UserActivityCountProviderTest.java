package com.xiaomi.nr.promotion.v2.resource.provider;

import com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionUserActivityCountMapper;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.UserActivityCount;
import com.xiaomi.nr.promotion.mq.producer.CarActivityUseProducer;
import com.xiaomi.nr.promotion.mq.producer.entity.CarActivityUseMessage;
import com.xiaomi.nr.promotion.resource.model.ResourceObject;
import com.xiaomi.nr.promotion.resource.provider.UserActivityCountProvider;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

//@ExtendWith(MockitoExtension.class)
public class UserActivityCountProviderTest extends BaseTestV2 {

    @InjectMocks
    private UserActivityCountProvider provider;

    @Mock
    private PromotionUserActivityCountMapper userActivityCountMapper;

    @Mock
    private CarActivityUseProducer carActivityUseProducer;

    private ResourceObject<UserActivityCountProvider.UserJoinActNum> resourceObject;
    private UserActivityCountProvider.UserJoinActNum userJoinActNum;

    @BeforeEach
    void setUp() {
        userJoinActNum = new UserActivityCountProvider.UserJoinActNum();
        userJoinActNum.setUid(1L);
        userJoinActNum.setActId(100L);
        userJoinActNum.setUserJoinNumLimit(5);

        resourceObject = new ResourceObject<>();
        resourceObject.setContent(userJoinActNum);
        provider.initResource(resourceObject);
    }

    @Test
    void test_lock_success() throws BizError {
        // Given
        when(userActivityCountMapper.getByUserIdAndPromotionId(anyLong(), anyLong())).thenReturn(null);

        // When
        provider.lock();

        // Then
        verify(userActivityCountMapper).insert(any(UserActivityCount.class));
        verify(carActivityUseProducer).sendMessage(any(CarActivityUseMessage.class));
    }

    @Test
    void test_lock_fail_userJoinNumOverLimit() {
        // Given
        UserActivityCount userActivityCount = new UserActivityCount();
        userActivityCount.setNum(5);
        when(userActivityCountMapper.getByUserIdAndPromotionId(anyLong(), anyLong())).thenReturn(userActivityCount);

        // When & Then
        BizError exception = assertThrows(BizError.class, () -> provider.lock());
        assertEquals("用户参与次数超过限制", exception.getMessage());
    }

    @Test
    void test_consume_success() throws BizError {
        // When
        provider.consume();

        // Then
        // No exception should be thrown
    }

    @Test
    void test_rollback_success() throws BizError {
        // Given
        UserActivityCount userActivityCount = new UserActivityCount();
        userActivityCount.setNum(2);
        when(userActivityCountMapper.getByUserIdAndPromotionId(anyLong(), anyLong())).thenReturn(userActivityCount);

        // When
        provider.rollback();

        // Then
        verify(userActivityCountMapper).updateCount(anyLong(), anyLong(), eq(1), eq(2), anyString(), anyLong());
        verify(carActivityUseProducer).sendMessage(any(CarActivityUseMessage.class));
    }

    @Test
    void test_rollback_fail_numLessThanOne() throws BizError {
        // Given
        UserActivityCount userActivityCount = new UserActivityCount();
        userActivityCount.setNum(0);
        when(userActivityCountMapper.getByUserIdAndPromotionId(anyLong(), anyLong())).thenReturn(userActivityCount);

        // When
        provider.rollback();

        // Then
        verify(userActivityCountMapper, never()).updateCount(anyLong(), anyLong(), anyInt(), anyInt(), anyString(), anyLong());
        verify(carActivityUseProducer, never()).sendMessage(any(CarActivityUseMessage.class));
    }
} 