package com.xiaomi.nr.promotion.v2.api.service;

import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.activity.pool.CarPromotionInstancePool;
import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.MaintenanceInfo;
import com.xiaomi.nr.promotion.api.service.PromotionDubboService;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;

import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;


@Slf4j
public class PromotionDubboServiceTest extends BaseTestV2 {

    @Autowired
    private PromotionDubboService promotionDubboService;
    @Autowired
    private CarPromotionInstancePool carPromotionInstancePool;
    private final static long USER_ID_TEST = 3150000058L;
    private final static long ORDER_ID_TEST = 1100010L;


    /**
     * 整车
     */
    @Test
    public void test_getPromotionPrice_car_vehicle_success() {

        carPromotionInstancePool.rebuildCacheTask();

        String str = "{\"goodsList\":[{\"ssuId\":124,\"childs\":[],\"price\":20000}],\"channel\":11}";
        GetPromotionPriceRequest request = GsonUtil.fromJson(str, GetPromotionPriceRequest.class);
        Result<GetPromotionPriceResponse> result = promotionDubboService.getPromotionPrice(request);
        Assert.assertEquals(1, result.getData().getPriceMap().size());
    }

    @Test
    public void cartPromotionByGroupTest() {
        String input =
                "{\"cartList\":[{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600008371_600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"sku\":\"600008371\",\"packageId\":\"\",\"ssuId\":600008371,\"count\":1,\"standardPrice\":2000,\"cartPrice\":2000,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":2000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600008371_600003438_528bdf18dd88e2887138f15965eaa90e\",\"sku\":\"600008371\",\"packageId\":\"\",\"ssuId\":600008371,\"count\":1,\"standardPrice\":2000,\"cartPrice\":2000,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600003438_528bdf18dd88e2887138f15965eaa90e\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":2000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600005222_600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"sku\":\"600005222\",\"packageId\":\"\",\"ssuId\":600005222,\"count\":1,\"standardPrice\":10000,\"cartPrice\":10000,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":10000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600005222_600003438_528bdf18dd88e2887138f15965eaa90e\",\"sku\":\"600005222\",\"packageId\":\"\",\"ssuId\":600005222,\"count\":1,\"standardPrice\":10000,\"cartPrice\":10000,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600003438_528bdf18dd88e2887138f15965eaa90e\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":10000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600005210_600003438_528bdf18dd88e2887138f15965eaa90e\",\"sku\":\"600005210\",\"packageId\":\"\",\"ssuId\":600005210,\"count\":1,\"standardPrice\":1900,\"cartPrice\":1900,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600003438_528bdf18dd88e2887138f15965eaa90e\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":1900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600003499_600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"sku\":\"600003499\",\"packageId\":\"\",\"ssuId\":600003499,\"count\":1,\"standardPrice\":900,\"cartPrice\":900,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600003499_600003438_528bdf18dd88e2887138f15965eaa90e\",\"sku\":\"600003499\",\"packageId\":\"\",\"ssuId\":600003499,\"count\":1,\"standardPrice\":900,\"cartPrice\":900,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600003438_528bdf18dd88e2887138f15965eaa90e\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600003493_600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"sku\":\"600003493\",\"packageId\":\"\",\"ssuId\":600003493,\"count\":1,\"standardPrice\":9900,\"cartPrice\":9900,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":9900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600003438_d7d4dee44ab574329f8f48b4ee4480f4\",\"sku\":\"600003438\",\"packageId\":\"\",\"ssuId\":600003438,\"count\":1,\"standardPrice\":1000000,\"cartPrice\":1000000,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":1000000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false},{\"reduceList\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"600003438_528bdf18dd88e2887138f15965eaa90e\",\"sku\":\"600003438\",\"packageId\":\"\",\"ssuId\":600003438,\"count\":1,\"standardPrice\":1000000,\"cartPrice\":1000000,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":1000000,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,21,8,5,9,27,7,10,23,26,22,25,70,71,72,2001,500,501,502,503,504,505],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":false,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false}],\"channel\":11,\"userId\":3150075543,\"couponIds\":[],\"bargainSize\":10,\"orgCode\":\"\",\"uidType\":\"\",\"globalBusinessPartner\":\"car\",\"getCouponList\":true,\"showType\":0,\"useDefaultCoupon\":true}";
        CartPromotionRequest request = GsonUtil.fromJson(input, CartPromotionRequest.class);


        Result<CartPromotionResponse> result = promotionDubboService.cartCheckoutPromotionByGroup(request);
        Assert.assertEquals(result.getCode(), 0);
    }

    @Test
    public void protectTest(){
        String str = "{\"userId\":3150000058,\"clientId\":180100031052,\"cartList\":[{\"childs\":[],\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"sku_21396_1\",\"sku\":\"21396\",\"packageId\":\"\",\"count\":1,\"standardPrice\":199900,\"cartPrice\":199900,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"properties\":\"\",\"parentItemId\":\"\",\"updateTime\":0,\"displayType\":0,\"oriItemId\":\"\",\"cannotJoinAct\":false,\"cannotUseCoupon\":false,\"marketPrice\":199900,\"cannotUseEcard\":false,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[],\"cannotUseCouponTypes\":[],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"unitId\":\"\"}],\"couponId\":0,\"couponIdList\":[1010001483,1010002501],\"couponCode\":\"\",\"redPacketUsedList\":[],\"orderId\":****************}";
        GetProductCurrentPriceRequest request = GsonUtil.fromJson(str, GetProductCurrentPriceRequest.class);
        Result<GetProductCurrentPriceResponse> result = promotionDubboService.getProductCurrentPrice(request);
        Assert.assertEquals(result.getCode(), 0);

    }

    /**
     * 整车销售-直降活动优惠
     */


    @Test
    public void checkoutNeedMaintenanceCouponTest() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        request.setSourceApi(1);
        request.setFromInterface(2);
        request.setChannel(ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue());
        request.setClientId(180100031052L);
        request.setUserId(USER_ID_TEST);
//        request.setCouponIds(Lists.newArrayList(1011106783L));
//        request.setCouponIds(Lists.newArrayList(1011088440L, 1011088441L));
        request.setCouponIds(Lists.newArrayList());
        request.setOrderId(ORDER_ID_TEST);
        request.setUsePoint(false);
//        request.setVid("LKBQRHRVZHMU6TJK1");
        request.setVid("1735890318928Tcgx");
        request.setWorkOrderType(1);
        request.setActivityIds(Lists.newArrayList());

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("600016462-1-1");
        cartItem1.setSsuId(600016462L);
        cartItem1.setCount(1);
        cartItem1.setBizSubType(13);
        cartItem1.setCartPrice(110L);
        cartItem1.setStandardPrice(110L);
        cartItem1.setMarketPrice(110L);
        MaintenanceInfo info1 = new MaintenanceInfo();
        info1.setPayType(1);
        info1.setWorkHour(new BigDecimal("1.1"));
        info1.setUnitPrice(100L);
        cartItem1.setMaintenanceInfo(info1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("600001042-1-3_600016462-1-1");
        cartItem2.setSsuId(600001042L);
        cartItem2.setCount(2);
        cartItem2.setBizSubType(14);
        cartItem2.setCartPrice(11400L);
        cartItem2.setStandardPrice(11400L);
        cartItem2.setMarketPrice(11400L);
        MaintenanceInfo info2 = new MaintenanceInfo();
        info2.setPayType(1);
        cartItem2.setMaintenanceInfo(info2);

        CartItem cartItem3 = new CartItem();
        cartItem3.setItemId("600001043-1-3_600016462-1-1");
        cartItem3.setSsuId(600001043L);
        cartItem3.setCount(1);
        cartItem3.setBizSubType(14);
        cartItem3.setCartPrice(11500L);
        cartItem3.setMarketPrice(11500L);
        cartItem3.setStandardPrice(11500L);
        cartItem3.setParentItemId("600016462-1-1");
        MaintenanceInfo info3 = new MaintenanceInfo();
        info3.setPayType(1);
        cartItem3.setMaintenanceInfo(info3);

        CartItem cartItem4 = new CartItem();
        cartItem4.setItemId("600001047");
        cartItem4.setSsuId(600001047L);
        cartItem4.setCount(1);
        cartItem4.setBizSubType(14);
        cartItem4.setCartPrice(11900L);
        cartItem4.setMarketPrice(11900L);
        cartItem4.setStandardPrice(11900L);
        MaintenanceInfo info4 = new MaintenanceInfo();
        info4.setPayType(1);
        cartItem4.setMaintenanceInfo(info4);

        request.setCartList(Lists.newArrayList(cartItem1, cartItem2, cartItem3, cartItem4));

        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);
        Assert.assertEquals(result.getCode(), 0);
    }

    @Test
    public void testRollbackPromotion() {
        RollbackPromotionRequest request = new RollbackPromotionRequest();
        request.setOrderId(ORDER_ID_TEST);
        request.setUserId(USER_ID_TEST);
        Result<RollbackPromotionResponse> result = promotionDubboService.rollbackPromotion(request);
        Assert.assertEquals(result.getCode(), 0);
    }

    @Test
    public void testSubmitPromotion() {
        SubmitPromotionRequest request = new SubmitPromotionRequest();
        request.setOrderId(ORDER_ID_TEST);
        request.setUserId(USER_ID_TEST);
        Result<SubmitPromotionResponse> result = promotionDubboService.submitPromotion(request);
        Assert.assertEquals(result.getCode(), 0);
    }

    @Test
    public void checkoutConsumablesCoupon_submit() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        request.setSourceApi(2);
        request.setFromInterface(1);
        request.setChannel(ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue());
        request.setClientId(180100031052L);
        request.setUserId(USER_ID_TEST);
        request.setCouponIds(Lists.newArrayList(1011079335L));
        request.setOrderId(ORDER_ID_TEST);
        request.setUsePoint(false);
//        request.setVid("LKBQRHRVZHMU6TJK1");
        request.setVid("1735890318928Tcgx");
        request.setWorkOrderType(1);
        request.setActivityIds(Lists.newArrayList());

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("600001030");
        cartItem1.setSsuId(600001030L);
        cartItem1.setCount(1);
        cartItem1.setBizSubType(14);
        cartItem1.setCartPrice(10200L);
        cartItem1.setStandardPrice(10200L);
        cartItem1.setMarketPrice(10200L);
        MaintenanceInfo info1 = new MaintenanceInfo();
        info1.setPayType(1);
        cartItem1.setMaintenanceInfo(info1);

        request.setCartList(Lists.newArrayList(cartItem1));
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        Assert.assertEquals(400030985, result.getCode());
    }
}
