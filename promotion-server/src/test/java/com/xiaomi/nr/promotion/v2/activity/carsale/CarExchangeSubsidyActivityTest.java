package com.xiaomi.nr.promotion.v2.activity.carsale;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.activity.carsale.CarExchangeSubsidyActivity;
import com.xiaomi.nr.promotion.api.dto.MultiProductGoodsActRequest;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.api.dto.model.car.ExchangeSubsidyRule;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.ProductDetailContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.CarExchangeSubsidyConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class CarExchangeSubsidyActivityTest extends BaseTestV2 {

    @InjectMocks
    private CarExchangeSubsidyActivity activity;

    @Mock
    private DSLEngine dslEngine;

    @Mock
    private DSLStream dslStream;

    private CarExchangeSubsidyConfig config;
    private LocalContext context;
    private Map<String, ActPriceInfo> exchangeSubsidyInfoMap;

    @BeforeEach
    public void setUp() {
        // 初始化配置
        config = new CarExchangeSubsidyConfig();
        config.setPromotionId(1L);
        config.setName("测试置换补贴活动");
        config.setUnixStartTime(1609459200L); // 2021-01-01 00:00:00
        config.setUnixEndTime(1640995199L);   // 2021-12-31 23:59:59

        // 初始化补贴信息
        exchangeSubsidyInfoMap = Maps.newHashMap();
        ActPriceInfo actPriceInfo1 = new ActPriceInfo();
        actPriceInfo1.setPrice(5000L);
        actPriceInfo1.setSsuId(1001L);
        actPriceInfo1.setBudgetApplyNo("BUDGET001");
        actPriceInfo1.setLineNum(111L);
        exchangeSubsidyInfoMap.put("1001", actPriceInfo1);

        ActPriceInfo actPriceInfo2 = new ActPriceInfo();
        actPriceInfo2.setPrice(3000L);
        actPriceInfo2.setSsuId(1002L);
        actPriceInfo2.setBudgetApplyNo("BUDGET002");
        actPriceInfo2.setLineNum(111L);
        exchangeSubsidyInfoMap.put("1002", actPriceInfo2);

        config.setExchangeSubsidyInfoMap(exchangeSubsidyInfoMap);

        // 初始化上下文
        context = new LocalContext();
        context.setBizPlatform(BizPlatformEnum.CAR);

        ReflectionTestUtils.setField(activity, "unixStartTime", System.currentTimeMillis() / 1000);
        ReflectionTestUtils.setField(activity, "unixEndTime", System.currentTimeMillis() / 1000 + 1000*3600*24*7);
        ReflectionTestUtils.setField(activity, "type", ActivityTypeEnum.BUY_GIFT);

    }

    @Test
    public void test_getType() {
        // 执行测试
        PromotionToolType type = activity.getType();

        // 验证结果
        assertEquals(PromotionToolType.EXCHANGE_SUBSIDY, type);
    }

    @Test
    public void test_getBizPlatform() {
        // 执行测试
        BizPlatformEnum platform = activity.getBizPlatform();

        // 验证结果
        assertEquals(BizPlatformEnum.CAR, platform);
    }

    @Test
    public void test_getActivityDetail() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "exchangeSubsidyInfoMap", exchangeSubsidyInfoMap);
        ReflectionTestUtils.setField(activity, "promotionConfig", config);

        // 执行测试
        ActivityDetail detail = activity.getActivityDetail();

        // 验证结果
        assertNotNull(detail);
        assertEquals(exchangeSubsidyInfoMap, detail.getPriceInfoMap());
    }

    @Test
    public void test_buildCartPromotionInfo() throws BizError {
        // 执行测试
        PromotionInfo result = activity.buildCartPromotionInfo(context);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void test_getProductGoodsAct() throws BizError {
        // 准备测试数据
        Long clientId = 123L;
        String orgCode = "TEST_ORG";
        List<String> skuPackageList = Lists.newArrayList("SKU1", "SKU2");

        // 执行测试
        Map<String, ProductActInfo> result = activity.getProductGoodsAct(clientId, orgCode, skuPackageList);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void test_checkUsableAddAct_true() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "exchangeSubsidyInfoMap", exchangeSubsidyInfoMap);

        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setSsuId(1001L);
        item.setReduce(5000L);

        // 执行测试
        Boolean result = activity.checkUsableAddAct(item);

        // 验证结果
        assertTrue(result);
    }

    @Test
    public void test_checkUsableAddAct_false_price_not_match() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "exchangeSubsidyInfoMap", exchangeSubsidyInfoMap);

        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setSsuId(1001L);
        item.setReduce(4000L); // 价格不匹配

        // 执行测试
        Boolean result = activity.checkUsableAddAct(item);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void test_checkUsableAddAct_false_ssu_not_exist() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "exchangeSubsidyInfoMap", exchangeSubsidyInfoMap);

        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setSsuId(9999L); // 不存在的SSU
        item.setReduce(5000L);

        // 执行测试
        Boolean result = activity.checkUsableAddAct(item);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void test_checkUsableAddAct_false_null_map() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "exchangeSubsidyInfoMap", null);

        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setSsuId(1001L);
        item.setReduce(5000L);

        // 执行测试
        Boolean result = activity.checkUsableAddAct(item);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void test_getMultiProductAct_success() throws BizError {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "exchangeSubsidyInfoMap", exchangeSubsidyInfoMap);
        ReflectionTestUtils.setField(activity, "promotionConfig", config);

        MultiGoodItem goodItem = new MultiGoodItem();
        goodItem.setSsuId(1001L);

        MultiProductGoodsActRequest request = new MultiProductGoodsActRequest();
        ProductDetailContext productDetailContext = new ProductDetailContext();

        // 执行测试
        PromotionInfoDTO result = activity.getMultiProductAct(goodItem, request, productDetailContext);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("测试置换补贴活动", result.getPromotionName());
        assertEquals(1609459200L, result.getStartTime());
        assertEquals(1640995199L, result.getEndTime());

        // 验证规则
        ExchangeSubsidyRule rule = GsonUtil.fromJson(result.getRule(), ExchangeSubsidyRule.class);
        assertNotNull(rule);
        assertEquals(5000L, rule.getReduceMoney());
    }

    @Test
    public void test_getMultiProductAct_null_price_info() throws BizError {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "exchangeSubsidyInfoMap", exchangeSubsidyInfoMap);
        ReflectionTestUtils.setField(activity, "promotionConfig", config);

        MultiGoodItem goodItem = new MultiGoodItem();
        goodItem.setSsuId(9999L); // 不存在的SSU

        MultiProductGoodsActRequest request = new MultiProductGoodsActRequest();
        ProductDetailContext productDetailContext = new ProductDetailContext();

        // 执行测试
        PromotionInfoDTO result = activity.getMultiProductAct(goodItem, request, productDetailContext);

        // 验证结果
        assertNull(result);
    }
}