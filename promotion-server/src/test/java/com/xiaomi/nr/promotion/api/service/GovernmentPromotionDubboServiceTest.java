package com.xiaomi.nr.promotion.api.service;

import cn.hutool.json.JSONUtil;
import com.xiaomi.nr.phoenix.api.dto.request.subsidyqualify.QuerySubsidyQualificationForTradeReq;
import com.xiaomi.nr.phoenix.api.dto.response.QualificationSubsidyResponse;
import com.xiaomi.nr.phoenix.api.dto.response.tradeInfo.TradeInfoQueryListResp;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.nr.promotion.rpc.phoenix.SubsidyPhoenixProxy;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
public class GovernmentPromotionDubboServiceTest extends BaseTest {
    @Autowired
    public PromotionDubboService promotionDubboService;
    @Resource
    private SubsidyPhoenixProxy subsidyPhoenixProxy;

    //获取
    @Test
    public void testGetQualification() throws BizError {
        QuerySubsidyQualificationForTradeReq qualification = QuerySubsidyQualificationForTradeReq.builder().build();
        qualification.setPersonalInfo("GHDsEdgdw9IhlmqqCZJG78wz3bypNpSTh++zeNHIKuYYMXgdBtSxGteN9q6zvymM6pSKz+sIMOkuk8tK2MqzRFJdtdi8SAAobjEsPhfstauWYjMtNl9vqMd3lV9kpDkL+ZnQGhnFO9sk1hcU+0RG7FqWGBJqXNH9VrJEx6q+PnGS+vnos/8YEGk43h/e0xKZvz8GVgtsRxoYFKBysFFearDhyThtlr3wyDQXloDNEwEVABIA");
        List<QualificationSubsidyResponse> qualificationSubsidyResponses = subsidyPhoenixProxy.queryPersonalInfoListForQualification(qualification);
        System.out.println("aa");
    }
    //锁定
    @Test
    public void testLockQualification() {
        try {
            subsidyPhoenixProxy.governmentLockSubsidyQualification(null,null,"",null,"");
        } catch (BizError e) {
            throw new RuntimeException(e);
        }
        System.out.println("aa");
    }
    //核销
    @Test
    public void testWriteOffQualification() {
        try {
            subsidyPhoenixProxy.governmentWriteOffSubsidyQualification(null,null);
        } catch (BizError e) {
            throw new RuntimeException(e);
        }
        System.out.println("aa");
    }

    //回滚
    @Test
    public void testRollbackQualification() {
        try {
            subsidyPhoenixProxy.governmentRollbackSubsidyQualification(null,null);
        } catch (BizError e) {
            throw new RuntimeException(e);
        }
        System.out.println("aa");
    }

    @Test
    public void testCheckoutPromotion(){

        String s="{\n" +
                "    \"bargainSize\": 0,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [],\n" +
                "            \"cannotUseCoupon\": true,\n" +
                "            \"cannotUseCouponTypes\": [],\n" +
                "            \"cannotUseEcard\": true,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"cartPrice\": 37900,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 0,\n" +
                "            \"goodsName\": \"米家智能空气炸锅P1 6.5L\",\n" +
                "            \"itemId\": \"1_57888\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 49900,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"prePrice\": 0,\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"reduceList\": {},\n" +
                "            \"reduceShareMap\": {},\n" +
                "            \"saleSource\": \"\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"12468\",\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"standardPrice\": 37900,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 0\n" +
                "        }\n" +
                "    ],\n" +
                "    \"channel\": 1,\n" +
                "    \"cityId\": 0,\n" +
                "    \"clientId\": 0,\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"\",\n" +
                "    \"orderId\": 1,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"region\": {\n" +
                "        \"area\": 0,\n" +
                "        \"city\": 0,\n" +
                "        \"district\": 0,\n" +
                "        \"province\": 20\n" +
                "    },\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": -1,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"submitType\": 0,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"usePoint\": false,\n" +
                "    \"usePurchaseSubsidy\": false,\n" +
                "    \"useRedPacket\": false,\n" +
                "    \"userId\": 3150433252,\n" +
                "    \"personalInfo\": \"GKABTTRIz41prkvcNgAuHpIGOsnxDrSoGyEmNWws10H9VBEOQ4SJSruqpIzJYGcVwIjDWao/76D91Zi71E7fdHyD80hgU4fI8IAYw53BKPmuWkmmhZd1CKXpzfPr6ghypJB1j8rFQv2Xp1HVFDeiwN8GmLFCbcqVlsUmt8evg8KZ1G8hz5R2xP5KdQJwcuwstwj2Wxv0MUA+XFWgKc+XssKR0hgSnTvKppzeTbusF7pN4eEJwvn/GBDBC8L+ayVkAmvUmKlWME0tGBSjEwrHVJT7yhooZcp2BYu7ywntSRMBAA==\"\n" +
                "}";
        // Lock
        CheckoutPromotionRequest request= GsonUtil.fromJson(s,CheckoutPromotionRequest.class);
        System.out.println("param:"+JSONUtil.parseObj(request));
        request.setOrderId(System.currentTimeMillis());
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println("result:"+JSONUtil.parseObj(result));

        // Commit
        SubmitPromotionRequest submitPromotionRequest=new SubmitPromotionRequest();
        submitPromotionRequest.setOrderId(request.getOrderId());
        submitPromotionRequest.setUserId(request.getUserId());
        Result<SubmitPromotionResponse> submitPromotionResponseResult = promotionDubboService.submitPromotion(submitPromotionRequest);

        // Rollback
        RollbackPromotionRequest rollbackPromotionRequest=new RollbackPromotionRequest();
        rollbackPromotionRequest.setOrderId(request.getOrderId());
        rollbackPromotionRequest.setUserId(request.getUserId());
        promotionDubboService.rollbackPromotion(rollbackPromotionRequest);

        System.out.println();

    }


    @Test
    public void testCheckoutPromotionV2(){

        String s="{\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"itemId\": \"2161000073_0_buy\",\n" +
                "            \"sku\": \"12468\",\n" +
                "            \"packageId\": \"\",\n" +
                "            \"count\": 1,\n" +
                "            \"standardPrice\": 3900,\n" +
                "            \"cartPrice\": 3900,\n" +
                "            \"prePrice\": 0,\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceList\": null,\n" +
                "            \"childs\": null,\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"properties\": \"\",\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"updateTime\": 1734957710,\n" +
                "            \"displayType\": 1,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"accessCode\": [],\n" +
                "            \"marketPrice\": 3900,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                40,\n" +
                "                41\n" +
                "            ],\n" +
                "            \"cannotUseCouponTypes\": null,\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"groupId\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"preferentialInfos\": null,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"subItemList\": null,\n" +
                "            \"saleSources\": [],\n" +
                "            \"reduceShareMap\": null\n" +
                "        }\n" +
                "    ],\n" +
                "    \"clientId\": 180100031052,\n" +
                "    \"userId\": 3150328838,\n" +
                "    \"couponIds\": null,\n" +
                "    \"couponCodes\": null,\n" +
                "    \"ecardIds\": null,\n" +
                "    \"bargainSize\": 10,\n" +
                "    \"ecardConsumeDetail\": null,\n" +
                "    \"useRedPacket\": false,\n" +
                "    \"calculateRedpacket\": true,\n" +
                "    \"getCouponList\": true,\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"uidType\": \"\",\n" +
                "    \"cityId\": 233,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"useDefaultCoupon\": false,\n" +
                "    \"globalBusinessPartner\": \"\",\n" +
                "    \"personalInfo\": \"GKAB18sl+7dY360AoRUsRvcXJlnvF8kYLAt7gdQwXIcKgd6XZ0VK5eNS1WrGbAwZWsV+UfAKMe3l1Znlw3IPh6404I3lrectwkwReqek0AQPHonXvG7TmzP7wDrhCH4D8RlCawihtU5zelpvsF45C4g7s+LJOGcRpQUdhen3gMPfyXFbXK7RCT3mvzKzyBJGKMErncWULiQwXVcFaAqEo7I2chgSBxzWv1sGRXiTikJ+H91C1qT/GBBOuDHIH81Dt+kd6//1QW6eGBSLBL+JiJbNPHMCIXXGYwFxc5h8yRMBAA==\",\n" +
                "    \"usePurchaseSubsidy\": false,\n" +
                "    \"thirdPromotions\": null,\n" +
                "    \"region\": {\n" +
                "        \"province\": 20,\n" +
                "        \"city\": 0,\n" +
                "        \"district\": 0,\n" +
                "        \"area\": 0\n" +
                "    }\n" +
                "}";
        // Lock
        CheckoutPromotionV2Request request= GsonUtil.fromJson(s,CheckoutPromotionV2Request.class);
        System.out.println("param:"+JSONUtil.parseObj(request));
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);
        System.out.println("result:"+JSONUtil.parseObj(result));
        System.out.println();

    }

    @Test
    public void testCheckoutPromotionV2_bj_qu() throws BizError {
        Long userId = 3150428707L;
        TradeInfoQueryListResp tradeInfoQueryListResp = subsidyPhoenixProxy.queryListForCoupon(userId);
        System.out.println("xiaomi");
    }

    @Test
    public void testCheckoutPromotionV2_bj_online(){
        //单品
        String s="{\n" +
                "    \"bargainSize\": 0,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [],\n" +
                "            \"cannotUseCoupon\": true,\n" +
                "            \"cannotUseCouponTypes\": [],\n" +
                "            \"cannotUseEcard\": true,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"cartPrice\": 3790000,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 0,\n" +
                "            \"goodsName\": \"米家智能空气炸锅P1 6.5L\",\n" +
                "            \"itemId\": \"1_57888\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 49900,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"prePrice\": 0,\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"reduceList\": {},\n" +
                "            \"reduceShareMap\": {},\n" +
                "            \"saleSource\": \"\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"21396\",\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"standardPrice\": 37900,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 0\n" +
                "        }\n" +
                "    ],\n" +
                "    \"channel\": 1,\n" +
                "    \"cityId\": 0,\n" +
                "    \"clientId\": 0,\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"\",\n" +
                "    \"orderId\": 1,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"region\": {\n" +
                "        \"area\": 0,\n" +
                "        \"city\": 0,\n" +
                "        \"district\": 0,\n" +
                "        \"province\": 2\n" +
                "    },\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": -1,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"submitType\": 0,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"usePoint\": false,\n" +
                "    \"usePurchaseSubsidy\": true,\n" +
                "    \"useRedPacket\": false,\n" +
                "    \"userId\": 3150428707,\n" +
                "    \"personalInfo\": \"GKABTTRIz41prkvcNgAuHpIGOsnxDrSoGyEmNWws10H9VBEOQ4SJSruqpIzJYGcVwIjDWao/76D91Zi71E7fdHyD80hgU4fI8IAYw53BKPmuWkmmhZd1CKXpzfPr6ghypJB1j8rFQv2Xp1HVFDeiwN8GmLFCbcqVlsUmt8evg8KZ1G8hz5R2xP5KdQJwcuwstwj2Wxv0MUA+XFWgKc+XssKR0hgSnTvKppzeTbusF7pN4eEJwvn/GBDBC8L+ayVkAmvUmKlWME0tGBSjEwrHVJT7yhooZcp2BYu7ywntSRMBAA==\"\n" +
                "}";;
        // Lock
        CheckoutPromotionV2Request request= GsonUtil.fromJson(s,CheckoutPromotionV2Request.class);
        System.out.println("param:"+JSONUtil.parseObj(request));
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);
        System.out.println("result:"+JSONUtil.parseObj(result));
        System.out.println();
    }
    @Test
    public void testCheckoutPromotionV2_bj_offline(){
        //单品
        String s="{\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"itemId\": \"2161000073_0_buy\",\n" +
                "            \"sku\": \"16173\",\n" +
                "            \"packageId\": \"\",\n" +
                "            \"count\": 1,\n" +
                "            \"standardPrice\": 3900,\n" +
                "            \"cartPrice\": 3900,\n" +
                "            \"prePrice\": 0,\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceList\": null,\n" +
                "            \"childs\": null,\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"properties\": \"\",\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"updateTime\": 1734957710,\n" +
                "            \"displayType\": 1,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"accessCode\": [],\n" +
                "            \"marketPrice\": 3900,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                40,\n" +
                "                41\n" +
                "            ],\n" +
                "            \"cannotUseCouponTypes\": null,\n" +
                "            \"saleSource\": \"common\",\n" +
                "            \"cannotUseRedPacket\": false,\n" +
                "            \"groupId\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"preferentialInfos\": null,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"subItemList\": null,\n" +
                "            \"saleSources\": [],\n" +
                "            \"reduceShareMap\": null\n" +
                "        }\n" +
                "    ],\n" +
                "    \"clientId\": 180100031052,\n" +
                "    \"userId\": 8944707901833198323,\n" +
                "    \"couponIds\": null,\n" +
                "    \"couponCodes\": null,\n" +
                "    \"ecardIds\": null,\n" +
                "    \"bargainSize\": 10,\n" +
                "    \"ecardConsumeDetail\": null,\n" +
                "    \"useRedPacket\": false,\n" +
                "    \"calculateRedpacket\": true,\n" +
                "    \"getCouponList\": true,\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"orgCode\": \"MI0101\",\n" +
                "    \"uidType\": \"\",\n" +
                "    \"channel\": 2,\n" +
                "    \"cityId\": 233,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": 2,\n" +
                "    \"useDefaultCoupon\": false,\n" +
                "    \"globalBusinessPartner\": \"\",\n" +
                "    \"usePurchaseSubsidy\": true,\n" +
                "    \"thirdPromotions\": null,\n" +
                "    \"region\": {\n" +
                "        \"province\": 0,\n" +
                "        \"city\": 0,\n" +
                "        \"district\": 0,\n" +
                "        \"area\": 0\n" +
                "    }\n" +
                "}";
        // Lock
        CheckoutPromotionV2Request request= GsonUtil.fromJson(s,CheckoutPromotionV2Request.class);
        System.out.println("param:"+JSONUtil.parseObj(request));
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);
        System.out.println("result:"+JSONUtil.parseObj(result));
        System.out.println();
    }

    @Test
    public void testCheckoutPromotionV2_bj_offline_bind(){
        //强绑定(2个)
        String s="{\n" +
                "    \"sourceApi\": 1,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"reduceList\": {\n" +
                "\n" +
                "            },\n" +
                "            \"reduceShareMap\": {\n" +
                "\n" +
                "            },\n" +
                "            \"reduceItemList\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"childs\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"reduceDetailList\": {\n" +
                "\n" +
                "            },\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"itemId\": \"21062_binding_21062_2_P_0\",\n" +
                "            \"sku\": \"21062\",\n" +
                "            \"packageId\": \"\",\n" +
                "            \"goodsName\": \"米家变频空调（室内机）(一级能效) 白色\",\n" +
                "            \"count\": 1,\n" +
                "            \"standardPrice\": 85800,\n" +
                "            \"cartPrice\": 85800,\n" +
                "            \"prePrice\": 0,\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"source\": \"bind\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"onSalePromotionIdMap\": {\n" +
                "\n" +
                "            },\n" +
                "            \"properties\": \"\",\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"updateTime\": 0,\n" +
                "            \"displayType\": 0,\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"accessCode\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"marketPrice\": 85800,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"cannotUseCouponTypes\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"saleSource\": \"\",\n" +
                "            \"saleSources\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"groupId\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"orderItemReduceList\": {\n" +
                "\n" +
                "            },\n" +
                "            \"preferentialInfos\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"unitId\": \"\",\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"canAdjustPrice\": false\n" +
                "        },\n" +
                "        {\n" +
                "            \"reduceList\": {\n" +
                "\n" +
                "            },\n" +
                "            \"reduceShareMap\": {\n" +
                "\n" +
                "            },\n" +
                "            \"reduceItemList\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"childs\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"reduceDetailList\": {\n" +
                "\n" +
                "            },\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"itemId\": \"21063_binding_21063_2_P_21062\",\n" +
                "            \"sku\": \"21063\",\n" +
                "            \"packageId\": \"\",\n" +
                "            \"goodsName\": \"米家变频空调（室外机）(一级能效) 白色\",\n" +
                "            \"count\": 1,\n" +
                "            \"standardPrice\": 99900,\n" +
                "            \"cartPrice\": 99900,\n" +
                "            \"prePrice\": 0,\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"source\": \"bind\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"onSalePromotionIdMap\": {\n" +
                "\n" +
                "            },\n" +
                "            \"properties\": \"\",\n" +
                "            \"parentItemId\": \"21062_binding_21062_2_P_0\",\n" +
                "            \"updateTime\": 0,\n" +
                "            \"displayType\": 0,\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"accessCode\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"marketPrice\": 99900,\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"cannotUseCouponTypes\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"saleSource\": \"\",\n" +
                "            \"saleSources\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"groupId\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"orderItemReduceList\": {\n" +
                "\n" +
                "            },\n" +
                "            \"preferentialInfos\": [\n" +
                "\n" +
                "            ],\n" +
                "            \"unitId\": \"\",\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"canAdjustPrice\": false\n" +
                "        }\n" +
                "    ],\n" +
                "    \"clientId\": 180100041075,\n" +
                "    \"userId\": 3150077480,\n" +
                "    \"couponIds\": [\n" +
                "\n" +
                "    ],\n" +
                "    \"ecardIds\": [\n" +
                "\n" +
                "    ],\n" +
                "    \"orderId\": 0,\n" +
                "    \"bargainSize\": 100,\n" +
                "    \"noSaveDbSubmit\": true,\n" +
                "    \"useRedPacket\": false,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"orgCode\": \"MI0101\",\n" +
                "    \"uidType\": \"\",\n" +
                "    \"cityId\": 0,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"shipmentId\": -1,\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"globalBusinessPartner\": \"\",\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"usePoint\": false,\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"submitType\": 0,\n" +
                "    \"usePurchaseSubsidy\": true,\n" +
                "    \"region\": {\n" +
                "        \"province\": 2,\n" +
                "        \"city\": 36,\n" +
                "        \"district\": 377,\n" +
                "        \"area\": 377001,\n" +
                "        \"address\": \"小米之家北京清河万象汇店\"\n" +
                "    },\n" +
                "    \"isExchange\": false\n" +
                "}";
        // Lock
        CheckoutPromotionV2Request request= GsonUtil.fromJson(s,CheckoutPromotionV2Request.class);
        System.out.println("param:"+JSONUtil.parseObj(request));
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);
        System.out.println("result:"+JSONUtil.parseObj(result));
        System.out.println();
    }

    @Test
    public void testCheckoutPromotion_bj_online(){
        //单品
        String s="{\n" +
                "    \"bargainSize\": 0,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"canAdjustPrice\": false,\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [],\n" +
                "            \"cannotUseCoupon\": true,\n" +
                "            \"cannotUseCouponTypes\": [],\n" +
                "            \"cannotUseEcard\": true,\n" +
                "            \"cannotUsePoint\": false,\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"cartPrice\": 37900,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 0,\n" +
                "            \"goodsName\": \"米家智能空气炸锅P1 6.5L\",\n" +
                "            \"itemId\": \"1_57888\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 49900,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onSalePromotionIdMap\": {},\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"prePrice\": 0,\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceItemList\": [],\n" +
                "            \"reduceList\": {},\n" +
                "            \"reduceShareMap\": {},\n" +
                "            \"saleSource\": \"\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"14590\",\n" +
                "            \"source\": \"\",\n" +
                "            \"sourceCode\": \"\",\n" +
                "            \"standardPrice\": 37900,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 0\n" +
                "        }\n" +
                "    ],\n" +
                "    \"channel\": 1,\n" +
                "    \"cityId\": 0,\n" +
                "    \"clientId\": 0,\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"\",\n" +
                "    \"orderId\": 1,\n" +
                "    \"orgCode\": \"\",\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"region\": {\n" +
                "        \"area\": 0,\n" +
                "        \"city\": 0,\n" +
                "        \"district\": 0,\n" +
                "        \"province\": 2\n" +
                "    },\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": -1,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"submitType\": 0,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"usePoint\": false,\n" +
                "    \"usePurchaseSubsidy\": true,\n" +
                "    \"useRedPacket\": false,\n" +
                "    \"userId\": 123456789123,\n" +
                "    \"personalInfo\": \"GKABTTRIz41prkvcNgAuHpIGOsnxDrSoGyEmNWws10H9VBEOQ4SJSruqpIzJYGcVwIjDWao/76D91Zi71E7fdHyD80hgU4fI8IAYw53BKPmuWkmmhZd1CKXpzfPr6ghypJB1j8rFQv2Xp1HVFDeiwN8GmLFCbcqVlsUmt8evg8KZ1G8hz5R2xP5KdQJwcuwstwj2Wxv0MUA+XFWgKc+XssKR0hgSnTvKppzeTbusF7pN4eEJwvn/GBDBC8L+ayVkAmvUmKlWME0tGBSjEwrHVJT7yhooZcp2BYu7ywntSRMBAA==\"\n" +
                "}";
        // Lock
        CheckoutPromotionRequest request= GsonUtil.fromJson(s,CheckoutPromotionRequest.class);
        request.setOrderId(System.currentTimeMillis());
        System.out.println("param:"+JSONUtil.parseObj(request));
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println("result:"+JSONUtil.parseObj(result));
        System.out.println();

        RollbackPromotionRequest rollbackPromotionRequest=new RollbackPromotionRequest();
        rollbackPromotionRequest.setOrderId(request.getOrderId());
        rollbackPromotionRequest.setUserId(request.getUserId());
        promotionDubboService.rollbackPromotion(rollbackPromotionRequest);

        System.out.println();
    }
    @Test
    public void testCheckoutPromotion_bj_offline(){
        //单品
        String s="{\n" +
                "        \"sourceApi\": 2,\n" +
                "        \"cartList\": [\n" +
                "            {\n" +
                "                \"itemId\": \"2174200042_0_buy\",\n" +
                "                \"sku\": \"17389\",\n" +
                "                \"packageId\": \"\",\n" +
                "                \"count\": 1,\n" +
                "                \"standardPrice\": 4900,\n" +
                "                \"cartPrice\": 4900,\n" +
                "                \"prePrice\": 0,\n" +
                "                \"reduceAmount\": 0,\n" +
                "                \"reduceList\": null,\n" +
                "                \"childs\": null,\n" +
                "                \"source\": \"\",\n" +
                "                \"sourceCode\": \"\",\n" +
                "                \"properties\": \"\",\n" +
                "                \"parentItemId\": \"\",\n" +
                "                \"updateTime\": 1735714988,\n" +
                "                \"displayType\": 1,\n" +
                "                \"cannotJoinAct\": false,\n" +
                "                \"cannotUseCoupon\": false,\n" +
                "                \"accessCode\": [\n" +
                "                    \"doorstep_access_2017\"\n" +
                "                ],\n" +
                "                \"marketPrice\": 4900,\n" +
                "                \"cannotUseEcard\": false,\n" +
                "                \"maxUseEcardAmount\": 0,\n" +
                "                \"cannotJoinActTypes\": null,\n" +
                "                \"cannotUseCouponTypes\": null,\n" +
                "                \"saleSource\": \"common\",\n" +
                "                \"cannotUseRedPacket\": false,\n" +
                "                \"groupId\": 0,\n" +
                "                \"onSaleBookingPrice\": 0,\n" +
                "                \"joinOnsale\": false,\n" +
                "                \"preferentialInfos\": null,\n" +
                "                \"checkoutPrice\": 0,\n" +
                "                \"subItemList\": null,\n" +
                "                \"saleSources\": [],\n" +
                "                \"reduceShareMap\": null,\n" +
                "                \"ssuId\": 0,\n" +
                "                \"ssuType\": 0,\n" +
                "                \"goodsName\": \"小米MIX 2 全陶瓷尊享版 8GB + 128GB 黑色\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"clientId\": 180100031052,\n" +
                "        \"userId\": 3150408521,\n" +
                "        \"couponIds\": null,\n" +
                "        \"couponCodes\": null,\n" +
                "        \"ecardIds\": null,\n" +
                "        \"orderId\": 0,\n" +
                "        \"bargainSize\": 0,\n" +
                "        \"noSaveDbSubmit\": true,\n" +
                "        \"ecardConsumeDetail\": {},\n" +
                "        \"useRedPacket\": true,\n" +
                "        \"calculateRedpacket\": false,\n" +
                "        \"useBeijingcoupon\": false,\n" +
                "        \"orgCode\": \"\",\n" +
                "        \"uidType\": \"\",\n" +
                "        \"cityId\": 36,\n" +
                "        \"shoppingMode\": 0,\n" +
                "        \"shipmentExpense\": 0,\n" +
                "        \"shipmentId\": 2,\n" +
                "        \"globalBusinessPartner\": \"mishop\",\n" +
                "        \"personalInfo\": \"GLAB+9cjRTGSaIde5HAMz70bbutXq38hF5roVgqzDaDZlCij0BpIyKU915I34UEugsq67lBdn+vYJxlgUze/p0JKaZfhaHbqboAJO/JKbt8mMSk/dRX8J71lBXAdSjVIrsfwrco5wnm6CTONpFYlXIIejTxwYGMTLeaCm8X+f66LBYPKpWsUufI7SdZv9NtQ44CVthE4+pOrMDat0hwEH/DuIxAh1lx9L+MWxKvB9S1KeG8YEmpc0f1WskTHqr4+cZL6+eiz/xgQFxfh9rLmlbZELBmcvzuK3hgUKW+38FrMDT2xNEV2oK/GJ1f3QRgTAQA=\",\n" +
                "        \"usePurchaseSubsidy\": true,\n" +
                "        \"thirdPromotions\": null,\n" +
                "        \"region\": {\n" +
                "            \"province\": 2,\n" +
                "            \"city\": 36,\n" +
                "            \"district\": 384,\n" +
                "            \"area\": 384010,\n" +
                "            \"address\": \"麻六记清河万象汇店\"\n" +
                "        }\n" +
                "    }";
        // Lock
        CheckoutPromotionRequest request= GsonUtil.fromJson(s,CheckoutPromotionRequest.class);
        System.out.println("param:"+JSONUtil.parseObj(request));
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println("result:"+JSONUtil.parseObj(result));
        System.out.println();
    }

    @Test
    public void testCheckoutPromotion_bj_offline_bind(){
        //强绑定(2个)
        String s="{\n" +
                "    \"bargainSize\": 0,\n" +
                "    \"calculateRedpacket\": false,\n" +
                "    \"cartList\": [\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                8\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseCouponTypes\": [],\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"cartPrice\": 100000,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"childs\": [],\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 0,\n" +
                "            \"goodsName\": \"自然风 米家空调 1.5匹新1级能效 银色（室内机） 银色\",\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"4908_binding_4908_2_P_0\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 100000,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"parentItemId\": \"\",\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceList\": {},\n" +
                "            \"saleSource\": \"\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"2643\",\n" +
                "            \"source\": \"bind\",\n" +
                "            \"sourceCode\": \"3084086\",\n" +
                "            \"standardPrice\": 100000,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 0\n" +
                "        },\n" +
                "        {\n" +
                "            \"accessCode\": [],\n" +
                "            \"cannotJoinAct\": false,\n" +
                "            \"cannotJoinActTypes\": [\n" +
                "                8\n" +
                "            ],\n" +
                "            \"cannotUseCoupon\": false,\n" +
                "            \"cannotUseCouponTypes\": [],\n" +
                "            \"cannotUseEcard\": false,\n" +
                "            \"cannotUseRedPacket\": true,\n" +
                "            \"cartPrice\": 200000,\n" +
                "            \"changePriceActType\": 0,\n" +
                "            \"checkoutPrice\": 0,\n" +
                "            \"childs\": [],\n" +
                "            \"count\": 1,\n" +
                "            \"displayType\": 0,\n" +
                "            \"goodsName\": \"自然风 米家空调 1.5匹新1级能效（室外机） 白色\",\n" +
                "            \"groupId\": 0,\n" +
                "            \"itemId\": \"1586_binding_1586_2_P_4908\",\n" +
                "            \"joinOnsale\": false,\n" +
                "            \"marketPrice\": 200000,\n" +
                "            \"maxUseEcardAmount\": 0,\n" +
                "            \"onSaleBookingPrice\": 0,\n" +
                "            \"onsaleReduce\": 0,\n" +
                "            \"orderItemReduceList\": {},\n" +
                "            \"oriItemId\": \"\",\n" +
                "            \"originalCartPrice\": 0,\n" +
                "            \"packageId\": \"\",\n" +
                "            \"parentItemId\": \"4908_binding_4908_2_P_0\",\n" +
                "            \"preferentialInfos\": [],\n" +
                "            \"properties\": \"\",\n" +
                "            \"reduceAmount\": 0,\n" +
                "            \"reduceDetailList\": {},\n" +
                "            \"reduceList\": {},\n" +
                "            \"saleSource\": \"\",\n" +
                "            \"saleSources\": [],\n" +
                "            \"sku\": \"16173\",\n" +
                "            \"source\": \"bind\",\n" +
                "            \"sourceCode\": \"3084086\",\n" +
                "            \"standardPrice\": 200000,\n" +
                "            \"storepriceReduce\": 0,\n" +
                "            \"unitId\": \"\",\n" +
                "            \"updateTime\": 0\n" +
                "        }"+
                "    ],\n" +
                "    \"channel\": 2,\n" +
                "    \"cityId\": 0,\n" +
                "    \"clientId\": 0,\n" +
                "    \"fromPriceProtect\": false,\n" +
                "    \"globalBusinessPartner\": \"\",\n" +
                "    \"orderId\": 1,\n" +
                "    \"orgCode\": \"MI0101\",\n" +
                "    \"pointReduceAmount\": 0,\n" +
                "    \"region\": {\n" +
                "        \"area\": 0,\n" +
                "        \"city\": 0,\n" +
                "        \"district\": 0,\n" +
                "        \"province\": 0\n" +
                "    },\n" +
                "    \"shipmentExpense\": 0,\n" +
                "    \"shipmentId\": -1,\n" +
                "    \"shoppingMode\": 0,\n" +
                "    \"sourceApi\": 2,\n" +
                "    \"submitType\": 0,\n" +
                "    \"uidType\": \"\",\n" +
                "    \"useBeijingcoupon\": false,\n" +
                "    \"usePoint\": false,\n" +
                "    \"usePurchaseSubsidy\": true,\n" +
                "    \"useRedPacket\": false,\n" +
                "    \"userId\": 123456789123,\n" +
                "    \"personalInfo\": \"GKABTTRIz41prkvcNgAuHpIGOsnxDrSoGyEmNWws10H9VBEOQ4SJSruqpIzJYGcVwIjDWao/76D91Zi71E7fdHyD80hgU4fI8IAYw53BKPmuWkmmhZd1CKXpzfPr6ghypJB1j8rFQv2Xp1HVFDeiwN8GmLFCbcqVlsUmt8evg8KZ1G8hz5R2xP5KdQJwcuwstwj2Wxv0MUA+XFWgKc+XssKR0hgSnTvKppzeTbusF7pN4eEJwvn/GBDBC8L+ayVkAmvUmKlWME0tGBSjEwrHVJT7yhooZcp2BYu7ywntSRMBAA==\"\n" +
                "}";

        // 核销
        CheckoutPromotionRequest request= GsonUtil.fromJson(s,CheckoutPromotionRequest.class);
        System.out.println("param:"+JSONUtil.parseObj(request));
        request.setOrderId(System.currentTimeMillis());
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println("result:"+JSONUtil.parseObj(result));

        // Rollback
        RollbackPromotionRequest rollbackPromotionRequest=new RollbackPromotionRequest();
        rollbackPromotionRequest.setOrderId(request.getOrderId());
        rollbackPromotionRequest.setUserId(request.getUserId());
        promotionDubboService.rollbackPromotion(rollbackPromotionRequest);
        System.out.println();
    }
}
