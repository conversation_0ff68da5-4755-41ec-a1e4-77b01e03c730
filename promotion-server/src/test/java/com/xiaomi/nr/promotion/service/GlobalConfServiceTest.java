package com.xiaomi.nr.promotion.service;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.domain.activity.service.common.GlobalConfService;
import com.xiaomi.nr.promotion.entity.redis.CompareItem;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 全局配置服务测试
 *
 * <AUTHOR>
 * @date 2021/5/18
 */
public class GlobalConfServiceTest extends BaseTest {
    @Autowired
    private GlobalConfService globalConfService;

    @Test
    public void testGetGlobalInExclude() {
        try {
            ListenableFuture<CompareItem> future = globalConfService.getGlobalInExclude();
            Assert.assertFalse(future.isDone());
            Assert.assertNotNull(future.get(1, TimeUnit.MINUTES));
            Assert.assertTrue(future.isDone());
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testGetGlobalActInExclude() {
        try {
            ListenableFuture<CompareItem> future = globalConfService.getGlobalActInExclude();
            Assert.assertFalse(future.isDone());
            Assert.assertNotNull(future.get(1, TimeUnit.MINUTES));
            Assert.assertTrue(future.isDone());
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testGetGlobalCouponInExclude() {
        try {
            ListenableFuture<CompareItem> future = globalConfService.getGlobalCouponInExclude();
            Assert.assertFalse(future.isDone());
            Assert.assertNotNull(future.get(1, TimeUnit.MINUTES));
            Assert.assertTrue(future.isDone());
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            e.printStackTrace();
        }
    }
}