package com.xiaomi.nr.promotion.api.service;


import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.SourceApi;
import com.xiaomi.nr.promotion.bootstrap.PromotionBootstrap;
import com.xiaomi.nr.promotion.domain.point.service.carshop.CarShopPointService;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * CarShopPointTest 服务测试类
 *
 * @date 2024/1/23
 */
@ActiveProfiles("dev")
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = {PromotionBootstrap.class, PromotionDubboServiceTest.class})
public class CarShopPointTest {
    @Resource
    public PromotionDubboService promotionDubboService;
    @Resource
    public CarShopPointService carShopPointService;

    private final static long USER_ID_TEST = 3150000058L;
    private final static long ORDER_ID_TEST = 100010L;

    /**
     * checkout页面
     */
    @Test
    public void checkoutPromotion_testCheckoutV2() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        List<CartItem> cartItemList = new ArrayList<>();

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2154300001_0_buy");
        cartItem1.setSsuId(11960L);
        cartItem1.setCount(3);
        cartItem1.setStandardPrice(1000L);
        cartItem1.setCartPrice(1000L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setCannotUsePoint(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("2154300002_0_buy");
        cartItem2.setSsuId(11962L);
        cartItem2.setCount(1);
        cartItem2.setStandardPrice(1000L);
        cartItem2.setCartPrice(1000L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setCannotUsePoint(false);
        cartItemList.add(cartItem2);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(USER_ID_TEST);
        request.setUsePoint(true);
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 下单试算
     */
    @Test
    public void checkoutPromotion_testSubmit() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2154300001_0_buy");
        cartItem1.setSsuId(11960L);
        cartItem1.setCount(3);
        cartItem1.setStandardPrice(100000L);
        cartItem1.setCartPrice(100000L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setCannotUsePoint(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("2154300002_0_buy");
        cartItem2.setSsuId(11962L);
        cartItem2.setCount(1);
        cartItem2.setStandardPrice(100000L);
        cartItem2.setCartPrice(100000L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setCannotUsePoint(false);
        cartItemList.add(cartItem2);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(USER_ID_TEST);
        request.setOrderId(ORDER_ID_TEST);
        request.setUsePoint(true);
        request.setNoSaveDbSubmit(false);
        request.setSourceApi(SourceApi.SUBMIT);
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 失败回滚
     */
    @Test
    public void testRollbackPromotion() {
        RollbackPromotionRequest request = new RollbackPromotionRequest();
        request.setOrderId(ORDER_ID_TEST);
        request.setUserId(USER_ID_TEST);
        Result<RollbackPromotionResponse> result = promotionDubboService.rollbackPromotion(request);
        System.out.println(GsonUtil.toJson(result));
    }

    /**
     * 下单提交
     */
    @Test
    public void testSubmitPromotion() {
        SubmitPromotionRequest request = new SubmitPromotionRequest();
        request.setOrderId(ORDER_ID_TEST);
        request.setUserId(USER_ID_TEST);
        Result<SubmitPromotionResponse> result = promotionDubboService.submitPromotion(request);
        System.out.println(GsonUtil.toJson(result));
    }


    /**
     * 分摊方法
     */
    @Test
    public void checkoutPromotion_share() throws BizError {
        List<CartItem> cartItemList = new ArrayList<>();

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("item_1000002_1");
        cartItem1.setSsuId(35313L);
        cartItem1.setCount(1);
        cartItem1.setCartPrice(10000L);
        cartItem1.setOriginalCartPrice(10000L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setCannotUsePoint(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("item_1000002_2");
        cartItem2.setSsuId(35314L);
        cartItem2.setCount(1);
        cartItem2.setCartPrice(10000L);
        cartItem2.setOriginalCartPrice(10000L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setCannotUsePoint(false);
        cartItemList.add(cartItem2);

        CartItem cartItem3 = new CartItem();
        cartItem3.setItemId("item_1000002_3");
        cartItem3.setSsuId(35315L);
        cartItem3.setCount(1);
        cartItem3.setOriginalCartPrice(10000L);
        cartItem3.setCartPrice(10000L);
        cartItem3.setReduceAmount(0L);
        cartItem3.setCannotUsePoint(false);
        cartItemList.add(cartItem3);

        List<GoodsIndex> goodsIndexList = new ArrayList<>();
        for (int i = 0; i < cartItemList.size(); i++) {
            CartItem item = cartItemList.get(i);
            if (item.getCannotUsePoint()) {
                continue;
            }
            GoodsIndex goodsIndex = new GoodsIndex();
            goodsIndex.setIndex(i);
            goodsIndex.setItemId(item.getItemId());
            goodsIndexList.add(goodsIndex);
        }

        carShopPointService.sharePointAmount(cartItemList, goodsIndexList, 2750L);
        System.out.println(GsonUtil.toJson(cartItemList));
    }

    /**
     * 分摊方法
     */
    @Test
    public void checkoutPromotion_share_2() throws BizError {
        List<CartItem> cartItemList = new ArrayList<>();

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("item_1000002_1");
        cartItem1.setSsuId(35313L);
        cartItem1.setCount(1);
        cartItem1.setCartPrice(10003L);
        cartItem1.setOriginalCartPrice(10003L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setCannotUsePoint(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("item_1000002_2");
        cartItem2.setSsuId(35314L);
        cartItem2.setCount(1);
        cartItem2.setCartPrice(10015L);
        cartItem2.setOriginalCartPrice(10015L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setCannotUsePoint(false);
        cartItemList.add(cartItem2);

        CartItem cartItem3 = new CartItem();
        cartItem3.setItemId("item_1000002_3");
        cartItem3.setSsuId(35315L);
        cartItem3.setCount(1);
        cartItem3.setOriginalCartPrice(10009L);
        cartItem3.setCartPrice(10009L);
        cartItem3.setReduceAmount(0L);
        cartItem3.setCannotUsePoint(false);
        cartItemList.add(cartItem3);

        List<GoodsIndex> goodsIndexList = new ArrayList<>();
        for (int i = 0; i < cartItemList.size(); i++) {
            CartItem item = cartItemList.get(i);
            if (item.getCannotUsePoint()) {
                continue;
            }
            GoodsIndex goodsIndex = new GoodsIndex();
            goodsIndex.setIndex(i);
            goodsIndex.setItemId(item.getItemId());
            goodsIndexList.add(goodsIndex);
        }

        carShopPointService.sharePointAmount(cartItemList, goodsIndexList, 2750L);
        System.out.println(GsonUtil.toJson(cartItemList));
    }

    @Test
    public void checkoutPromotion_share_3() throws BizError {
        List<CartItem> cartItemList = new ArrayList<>();

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("item_1000002_1");
        cartItem1.setSsuId(35313L);
        cartItem1.setCount(1);
        cartItem1.setCartPrice(915L);
        cartItem1.setOriginalCartPrice(915L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setCannotUsePoint(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("item_1000002_2");
        cartItem2.setSsuId(35314L);
        cartItem2.setCount(1);
        cartItem2.setCartPrice(919L);
        cartItem2.setOriginalCartPrice(919L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setCannotUsePoint(false);
        cartItemList.add(cartItem2);

        CartItem cartItem3 = new CartItem();
        cartItem3.setItemId("item_1000002_3");
        cartItem3.setSsuId(35315L);
        cartItem3.setCount(1);
        cartItem3.setOriginalCartPrice(917L);
        cartItem3.setCartPrice(917L);
        cartItem3.setReduceAmount(0L);
        cartItem3.setCannotUsePoint(false);
        cartItemList.add(cartItem3);

        List<GoodsIndex> goodsIndexList = new ArrayList<>();
        for (int i = 0; i < cartItemList.size(); i++) {
            CartItem item = cartItemList.get(i);
            if (item.getCannotUsePoint()) {
                continue;
            }
            GoodsIndex goodsIndex = new GoodsIndex();
            goodsIndex.setIndex(i);
            goodsIndex.setItemId(item.getItemId());
            goodsIndexList.add(goodsIndex);
        }

        carShopPointService.sharePointAmount(cartItemList, goodsIndexList, 2750L);
        System.out.println(GsonUtil.toJson(cartItemList));
    }

    @Test
    public void checkoutPromotion_share_4() throws BizError {
        List<CartItem> cartItemList = new ArrayList<>();

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("item_1000002_1");
        cartItem1.setSsuId(35313L);
        cartItem1.setCount(1);
        cartItem1.setCartPrice(5L);
        cartItem1.setOriginalCartPrice(5L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setCannotUsePoint(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("item_1000002_2");
        cartItem2.setSsuId(35314L);
        cartItem2.setCount(1);
        cartItem2.setCartPrice(5L);
        cartItem2.setOriginalCartPrice(5L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setCannotUsePoint(false);
        cartItemList.add(cartItem2);

        CartItem cartItem3 = new CartItem();
        cartItem3.setItemId("item_1000002_3");
        cartItem3.setSsuId(35315L);
        cartItem3.setCount(1);
        cartItem3.setOriginalCartPrice(5L);
        cartItem3.setCartPrice(5L);
        cartItem3.setReduceAmount(0L);
        cartItem3.setCannotUsePoint(false);
        cartItemList.add(cartItem3);

        List<GoodsIndex> goodsIndexList = new ArrayList<>();
        for (int i = 0; i < cartItemList.size(); i++) {
            CartItem item = cartItemList.get(i);
            if (item.getCannotUsePoint()) {
                continue;
            }
            GoodsIndex goodsIndex = new GoodsIndex();
            goodsIndex.setIndex(i);
            goodsIndex.setItemId(item.getItemId());
            goodsIndexList.add(goodsIndex);
        }

        carShopPointService.sharePointAmount(cartItemList, goodsIndexList, 2750L);
        System.out.println(GsonUtil.toJson(cartItemList));
    }

    @Test
    public void checkoutPromotion_testCheckoutV2_2() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        List<CartItem> cartItemList = new ArrayList<>();

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("item_1000002_1");
        cartItem1.setSsuId(35313L);
        cartItem1.setCount(1);
        cartItem1.setCartPrice(915L);
        cartItem1.setOriginalCartPrice(915L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setCannotUsePoint(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("item_1000002_2");
        cartItem2.setSsuId(35314L);
        cartItem2.setCount(1);
        cartItem2.setCartPrice(919L);
        cartItem2.setOriginalCartPrice(919L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setCannotUsePoint(false);
        cartItemList.add(cartItem2);

        CartItem cartItem3 = new CartItem();
        cartItem3.setItemId("item_1000002_3");
        cartItem3.setSsuId(35315L);
        cartItem3.setCount(1);
        cartItem3.setOriginalCartPrice(917L);
        cartItem3.setCartPrice(917L);
        cartItem3.setReduceAmount(0L);
        cartItem3.setCannotUsePoint(false);
        cartItemList.add(cartItem3);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150410272L);
        request.setUsePoint(true);
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    @Test
    public void checkoutPromotion_testCheckoutV2_3() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        List<CartItem> cartItemList = new ArrayList<>();

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("item_1000002_1");
        cartItem1.setSsuId(35313L);
        cartItem1.setCount(1);
        cartItem1.setCartPrice(10003L);
        cartItem1.setOriginalCartPrice(10003L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setCannotUsePoint(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("item_1000002_2");
        cartItem2.setSsuId(35314L);
        cartItem2.setCount(1);
        cartItem2.setCartPrice(10015L);
        cartItem2.setOriginalCartPrice(10015L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setCannotUsePoint(false);
        cartItemList.add(cartItem2);

        CartItem cartItem3 = new CartItem();
        cartItem3.setItemId("item_1000002_3");
        cartItem3.setSsuId(35315L);
        cartItem3.setCount(1);
        cartItem3.setOriginalCartPrice(10009L);
        cartItem3.setCartPrice(10009L);
        cartItem3.setReduceAmount(0L);
        cartItem3.setCannotUsePoint(false);
        cartItemList.add(cartItem3);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150410272L);
        request.setUsePoint(true);
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    @Test
    public void checkoutPromotion_testCheckoutV2_4() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        List<CartItem> cartItemList = new ArrayList<>();

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("item_1000002_1");
        cartItem1.setSsuId(35313L);
        cartItem1.setCount(1);
        cartItem1.setCartPrice(5L);
        cartItem1.setOriginalCartPrice(5L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setCannotUsePoint(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("item_1000002_2");
        cartItem2.setSsuId(35314L);
        cartItem2.setCount(1);
        cartItem2.setCartPrice(5L);
        cartItem2.setOriginalCartPrice(5L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setCannotUsePoint(false);
        cartItemList.add(cartItem2);

        CartItem cartItem3 = new CartItem();
        cartItem3.setItemId("item_1000002_3");
        cartItem3.setSsuId(35315L);
        cartItem3.setCount(1);
        cartItem3.setOriginalCartPrice(5L);
        cartItem3.setCartPrice(5L);
        cartItem3.setReduceAmount(0L);
        cartItem3.setCannotUsePoint(false);
        cartItemList.add(cartItem3);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150410272L);
        request.setUsePoint(true);
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

}