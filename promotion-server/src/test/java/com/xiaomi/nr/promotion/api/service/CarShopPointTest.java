package com.xiaomi.nr.promotion.api.service;


import com.google.common.collect.Lists;
import com.xiaomi.nr.promotion.api.dto.*;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.enums.SsuIdTypeEnum;
import com.xiaomi.nr.promotion.api.dto.model.*;
import com.xiaomi.nr.promotion.api.dto.model.carshop.Region;
import com.xiaomi.nr.promotion.bootstrap.PromotionBootstrap;
import com.xiaomi.nr.promotion.domain.point.service.carshop.CarShopPointService;
import com.xiaomi.nr.promotion.model.common.GoodsIndex;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * CarShopPointTest 服务测试类
 *
 * @date 2024/1/23
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = {PromotionBootstrap.class, PromotionDubboServiceTest.class})
public class CarShopPointTest {
    @Resource
    public PromotionDubboService promotionDubboService;
    @Resource
    public CarShopPointService carShopPointService;

    private final static long USER_ID_TEST = 3150000058L;
    private final static long ORDER_ID_TEST = 100010L;

    /**
     * checkout页面
     */
    @Test
    public void checkoutPromotion_testCheckoutV2() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        List<CartItem> cartItemList = new ArrayList<>();

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2154300001_0_buy");
        cartItem1.setSsuId(11960L);
        cartItem1.setCount(3);
        cartItem1.setStandardPrice(1000L);
        cartItem1.setCartPrice(1000L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setCannotUsePoint(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("2154300002_0_buy");
        cartItem2.setSsuId(11962L);
        cartItem2.setCount(1);
        cartItem2.setStandardPrice(1000L);
        cartItem2.setCartPrice(1000L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setCannotUsePoint(false);
        cartItemList.add(cartItem2);

        TestItem childItem1 = new TestItem(80001L, 5000L, 1, null);
        TestItem childItem2 = new TestItem(80002L, 4000L, 1, null);
        TestItem childItem3 = new TestItem(80003L, 1000L, 1, null);
        List<TestItem> childItemList = Arrays.asList(childItem1, childItem2, childItem3);
        TestItem item1 = new TestItem(10001L, 10000L, 1, childItemList);
        TestItem item2 = new TestItem(10002L, 10000L, 1, null);
        TestItem item3 = new TestItem(10003L, 10000L, 1, null);
        List<TestItem> itemList = Arrays.asList(item1, item2, item3);




        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(USER_ID_TEST);
        request.setUsePoint(true);
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 下单试算
     */
    @Test
    public void checkoutPromotion_testSubmit() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        List<CartItem> cartItemList = new ArrayList<>();

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("2154300001_0_buy");
        cartItem1.setSsuId(11960L);
        cartItem1.setCount(3);
        cartItem1.setStandardPrice(100000L);
        cartItem1.setCartPrice(100000L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setCannotUsePoint(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("2154300002_0_buy");
        cartItem2.setSsuId(11962L);
        cartItem2.setCount(1);
        cartItem2.setStandardPrice(100000L);
        cartItem2.setCartPrice(100000L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setCannotUsePoint(false);
        cartItemList.add(cartItem2);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(USER_ID_TEST);
        request.setOrderId(ORDER_ID_TEST);
        request.setUsePoint(true);
        request.setNoSaveDbSubmit(false);
        request.setSourceApi(SourceApi.SUBMIT);
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    /**
     * 失败回滚
     */
    @Test
    public void testRollbackPromotion() {
        RollbackPromotionRequest request = new RollbackPromotionRequest();
        request.setOrderId(ORDER_ID_TEST);
        request.setUserId(USER_ID_TEST);
        Result<RollbackPromotionResponse> result = promotionDubboService.rollbackPromotion(request);
        System.out.println(GsonUtil.toJson(result));
    }

    /**
     * 下单提交
     */
    @Test
    public void testSubmitPromotion() {
        SubmitPromotionRequest request = new SubmitPromotionRequest();
        request.setOrderId(ORDER_ID_TEST);
        request.setUserId(USER_ID_TEST);
        Result<SubmitPromotionResponse> result = promotionDubboService.submitPromotion(request);
        System.out.println(GsonUtil.toJson(result));
    }

    @Data
    @AllArgsConstructor
    class TestItem {
        private Long ssuId;
        private Long price;
        private Integer count;
        private List<TestItem> children;
    }

    private List<CartItem> getCartItemList(List<TestItem> testItems) {
        List<CartItem> cartItemList = new ArrayList<>();
        for (TestItem item : testItems) {
            CartItem cartItem = new CartItem();
            if (item.getChildren() == null || item.getChildren().isEmpty()) {

                cartItem.setItemId(String.valueOf(item.getSsuId()));
                cartItem.setSsuId(item.getSsuId());
                cartItem.setCount(item.getCount());
                cartItem.setOriginalCartPrice(item.getPrice());
                cartItem.setCartPrice(item.getPrice());
                cartItem.setReduceAmount(0L);
                cartItem.setCannotUsePoint(false);
                cartItem.setSsuType(SsuIdTypeEnum.SINGLE.code);
            } else {
                List<CartItemChild> childList = new ArrayList<>();
                long standardPrice = 0L;
                for (TestItem child : item.getChildren()) {
                    CartItemChild cartItemChild = new CartItemChild();
                    cartItemChild.setSsuId(child.getSsuId());
                    cartItemChild.setCount(child.getCount());
                    cartItemChild.setCartPrice(child.getPrice());
                    cartItemChild.setOriginalSellPrice(child.getPrice());
                    cartItemChild.setSellPrice(child.getPrice());
                    childList.add(cartItemChild);
                    standardPrice += child.getPrice() * child.getCount();
                }
                cartItem.setItemId(String.valueOf(item.getSsuId()));
                cartItem.setSsuId(item.getSsuId());
                cartItem.setPackageId(String.valueOf(item.getSsuId()));
                cartItem.setCount(item.getCount());
                cartItem.setOriginalCartPrice(item.getPrice());
                cartItem.setCartPrice(item.getPrice());
                cartItem.setStandardPrice(standardPrice);
                cartItem.setReduceAmount(0L);
                cartItem.setCannotUsePoint(false);
                cartItem.setSsuType(SsuIdTypeEnum.PACKAGE.code);
                cartItem.setChilds(childList);
            }
            cartItemList.add(cartItem);
        }
        return cartItemList;
    }


    /**
     * 分摊方法
     */
    @Test
    public void checkoutPromotion_share() throws BizError {
        TestItem childItem1 = new TestItem(80001L, 5000L, 1, null);
        TestItem childItem2 = new TestItem(80002L, 4000L, 1, null);
        TestItem childItem3 = new TestItem(80003L, 1000L, 1, null);
        List<TestItem> childItemList = Arrays.asList(childItem1, childItem2, childItem3);
        TestItem item1 = new TestItem(10001L, 10000L, 1, childItemList);
        TestItem item2 = new TestItem(10002L, 10000L, 1, null);
        TestItem item3 = new TestItem(10003L, 10000L, 1, null);
        List<TestItem> itemList = Arrays.asList(item1, item2, item3);

        List<CartItem> cartItemList = getCartItemList(itemList);
        long pointCanUseTotalAmount = 2750L;

        Set<Long> ssuIdList = new HashSet<>();
        Collections.addAll(ssuIdList, 80001L, 80002L, 80003L, 10001L, 10002L, 10003L);

        List<GoodsIndex> goodsIndexList = carShopPointService.fillValidGoodsList(cartItemList, ssuIdList);

        Map<Integer, List<GoodsIndex>> subGoodsIndexMap = carShopPointService.fillValidSubGoodsList(cartItemList, ssuIdList);

        carShopPointService.sharePointAmount(cartItemList, goodsIndexList, pointCanUseTotalAmount, subGoodsIndexMap);

        Map<String, List<ReduceDetailItem>> result = cartItemList.stream().collect(Collectors.toMap(OrderCartItem::getItemId, CartItem::getReduceItemList, (a, b) -> a));

        log.info("result: {}", GsonUtil.toJson(result));
    }

    /**
     * 分摊方法
     */
    @Test
    public void checkoutPromotion_share_2() throws BizError {

        TestItem childItem1 = new TestItem(80001L, 1L, 3, null);
        TestItem childItem2 = new TestItem(80002L, 1L, 2, null);
        TestItem childItem3 = new TestItem(80003L, 1L, 1, null);
        List<TestItem> childItemList = Arrays.asList(childItem1, childItem2, childItem3);
        TestItem item1 = new TestItem(10001L, 6L, 10, childItemList);
        TestItem item2 = new TestItem(10002L, 10000L, 1, null);
        TestItem item3 = new TestItem(10003L, 10000L, 1, null);
        List<TestItem> itemList = Arrays.asList(item1, item2, item3);

        List<CartItem> cartItemList = getCartItemList(itemList);
        long pointCanUseTotalAmount = 750L;

        Set<Long> ssuIdList = new HashSet<>();
        Collections.addAll(ssuIdList, 80001L, 80002L, 80003L, 10001L, 10002L, 10003L);

        List<GoodsIndex> goodsIndexList = carShopPointService.fillValidGoodsList(cartItemList, ssuIdList);

        Map<Integer, List<GoodsIndex>> subGoodsIndexMap = carShopPointService.fillValidSubGoodsList(cartItemList, ssuIdList);

        carShopPointService.sharePointAmount(cartItemList, goodsIndexList, pointCanUseTotalAmount, subGoodsIndexMap);

        Map<String, List<ReduceDetailItem>> result = cartItemList.stream().collect(Collectors.toMap(OrderCartItem::getItemId, CartItem::getReduceItemList, (a, b) -> a));

        log.info("result: {}", GsonUtil.toJson(result));
    }

    @Test
    public void checkoutPromotion_share_3() throws BizError {
        log.info("==========================================================================");
        TestItem childItem1 = new TestItem(80001L, 4999L, 1, null);
        TestItem childItem2 = new TestItem(80002L, 4999L, 1, null);
        TestItem childItem3 = new TestItem(80003L, 2L, 1, null);
        List<TestItem> childItemList = Arrays.asList(childItem1, childItem2, childItem3);
        TestItem item1 = new TestItem(10001L, 10000L, 1, childItemList);
        TestItem item2 = new TestItem(10002L, 10000L, 1, null);
        TestItem item3 = new TestItem(10003L, 10000L, 1, null);
        TestItem item4 = new TestItem(10004L, 10000L, 1, null);
        TestItem item5 = new TestItem(10005L, 10000L, 1, null);
        TestItem item6 = new TestItem(10006L, 10000L, 1, null);
        TestItem item7 = new TestItem(10007L, 10000L, 1, null);
        TestItem item8 = new TestItem(10008L, 10000L, 1, null);
        TestItem item9 = new TestItem(10009L, 10000L, 1, null);
        TestItem item10 = new TestItem(10010L, 10000L, 1, null);
        List<TestItem> itemList = Arrays.asList(item1, item2, item3, item4, item5, item6, item7, item8, item9, item10);

        List<CartItem> cartItemList = getCartItemList(itemList);
        long pointCanUseTotalAmount = 10110L;

        Set<Long> ssuIdList = new HashSet<>();
        Collections.addAll(ssuIdList, 80001L, 80002L, 80003L, 10001L, 10002L, 10003L, 10004L, 10005L, 10006L, 10007L, 10008L, 10009L, 10010L);

        List<GoodsIndex> goodsIndexList = carShopPointService.fillValidGoodsList(cartItemList, ssuIdList);

        Map<Integer, List<GoodsIndex>> subGoodsIndexMap = carShopPointService.fillValidSubGoodsList(cartItemList, ssuIdList);

        carShopPointService.sharePointAmount(cartItemList, goodsIndexList, pointCanUseTotalAmount, subGoodsIndexMap);

        Map<String, List<ReduceDetailItem>> result = cartItemList.stream().collect(Collectors.toMap(OrderCartItem::getItemId, CartItem::getReduceItemList, (a, b) -> a));

        log.info("result: {}", GsonUtil.toJson(result));
        log.info("==========================================================================");
    }

    @Test
    public void checkoutPromotion_share_4() throws BizError {
        List<TestItem> childItemList = new ArrayList<>();
        Set<Long> ssuIdList = new HashSet<>();
        for (int i = 0; i < 1000; i++) {
            TestItem childItem = new TestItem(80001L + i, 1999L, 1, null);
            childItemList.add(childItem);
            ssuIdList.add(80001L + i);
        }
        childItemList.add(new TestItem(80001L + 1000, 1000L, 1, null));
        TestItem item1 = new TestItem(10001L, 3L, 1, childItemList);
        TestItem item2 = new TestItem(10002L, 27L, 1, null);
//        TestItem item3 = new TestItem(10003L, 10000L, 1, null);
        List<TestItem> itemList = Arrays.asList(item1, item2);

        List<CartItem> cartItemList = getCartItemList(itemList);
        long pointCanUseTotalAmount = 30L; // 为了避免出现零元单，实际分摊积分金额总数为20

        Collections.addAll(ssuIdList, 10001L, 10002L, 10003L, 80001L, 80002L, 80003L, 80004L, 80005L, 80006L, 80007L, 80008L, 80009L, 80010L);

        List<GoodsIndex> goodsIndexList = carShopPointService.fillValidGoodsList(cartItemList, ssuIdList);

        Map<Integer, List<GoodsIndex>> subGoodsIndexMap = carShopPointService.fillValidSubGoodsList(cartItemList, ssuIdList);

        long startTime = System.currentTimeMillis();

        carShopPointService.sharePointAmount(cartItemList, goodsIndexList, pointCanUseTotalAmount, subGoodsIndexMap);

        log.info("cost time: {}", System.currentTimeMillis() - startTime);

        Map<String, List<ReduceDetailItem>> result = cartItemList.stream().collect(Collectors.toMap(OrderCartItem::getItemId, CartItem::getReduceItemList, (a, b) -> a));

        log.info("result: {}", GsonUtil.toJson(result));
    }

    @Test
    public void checkoutPromotion_testCheckoutV2_2() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        List<CartItem> cartItemList = new ArrayList<>();

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("item_1000002_1");
        cartItem1.setSsuId(35313L);
        cartItem1.setCount(1);
        cartItem1.setCartPrice(915L);
        cartItem1.setOriginalCartPrice(915L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setCannotUsePoint(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("item_1000002_2");
        cartItem2.setSsuId(35314L);
        cartItem2.setCount(1);
        cartItem2.setCartPrice(919L);
        cartItem2.setOriginalCartPrice(919L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setCannotUsePoint(false);
        cartItemList.add(cartItem2);

        CartItem cartItem3 = new CartItem();
        cartItem3.setItemId("item_1000002_3");
        cartItem3.setSsuId(35315L);
        cartItem3.setCount(1);
        cartItem3.setOriginalCartPrice(917L);
        cartItem3.setCartPrice(917L);
        cartItem3.setReduceAmount(0L);
        cartItem3.setCannotUsePoint(false);
        cartItemList.add(cartItem3);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150410272L);
        request.setUsePoint(true);
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    @Test
    public void checkoutPromotion_testCheckoutV2_3() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        List<CartItem> cartItemList = new ArrayList<>();

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("item_1000002_1");
        cartItem1.setSsuId(35313L);
        cartItem1.setCount(1);
        cartItem1.setCartPrice(10003L);
        cartItem1.setOriginalCartPrice(10003L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setCannotUsePoint(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("item_1000002_2");
        cartItem2.setSsuId(35314L);
        cartItem2.setCount(1);
        cartItem2.setCartPrice(10015L);
        cartItem2.setOriginalCartPrice(10015L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setCannotUsePoint(false);
        cartItemList.add(cartItem2);

        CartItem cartItem3 = new CartItem();
        cartItem3.setItemId("item_1000002_3");
        cartItem3.setSsuId(35315L);
        cartItem3.setCount(1);
        cartItem3.setOriginalCartPrice(10009L);
        cartItem3.setCartPrice(10009L);
        cartItem3.setReduceAmount(0L);
        cartItem3.setCannotUsePoint(false);
        cartItemList.add(cartItem3);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150410272L);
        request.setUsePoint(true);
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    @Test
    public void checkoutPromotion_testCheckoutV2_4() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        List<CartItem> cartItemList = new ArrayList<>();

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("item_1000002_1");
        cartItem1.setSsuId(35313L);
        cartItem1.setCount(1);
        cartItem1.setCartPrice(5L);
        cartItem1.setOriginalCartPrice(5L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setCannotUsePoint(false);
        cartItemList.add(cartItem1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("item_1000002_2");
        cartItem2.setSsuId(35314L);
        cartItem2.setCount(1);
        cartItem2.setCartPrice(5L);
        cartItem2.setOriginalCartPrice(5L);
        cartItem2.setReduceAmount(0L);
        cartItem2.setCannotUsePoint(false);
        cartItemList.add(cartItem2);

        CartItem cartItem3 = new CartItem();
        cartItem3.setItemId("item_1000002_3");
        cartItem3.setSsuId(35315L);
        cartItem3.setCount(1);
        cartItem3.setOriginalCartPrice(5L);
        cartItem3.setCartPrice(5L);
        cartItem3.setReduceAmount(0L);
        cartItem3.setCannotUsePoint(false);
        cartItemList.add(cartItem3);

        request.setCartList(cartItemList);
        request.setClientId(180100031052L);
        request.setUserId(3150410272L);
        request.setUsePoint(true);
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);
        System.out.println(GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }


    @Test
    public void test() {
        String json = "{\"getCouponList\":false,\"showType\":1,\"useDefaultCoupon\":true,\"cartList\":[{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"2192100012_gift_2182900005_21513686_2\",\"sku\":\"20433\",\"packageId\":\"\",\"ssuId\":2182900005,\"count\":50,\"standardPrice\":10000,\"cartPrice\":10000,\"prePrice\":0,\"reduceAmount\":0,\"source\":\"gift\",\"sourceCode\":\"21513686\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"2192100012\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":true,\"marketPrice\":10000,\"cannotUseEcard\":true,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,8,5,9,27,7,10,23,26,22,25,28,70,71,72,2001,500,501,502,503,504,505,2002,29],\"cannotUseCouponTypes\":[],\"saleSource\":\"gift\",\"saleSources\":[],\"cannotUseRedPacket\":true,\"groupId\":2,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false,\"canAdjustPrice\":false},{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"2192100012_gift_2182800037_21513686_1\",\"sku\":\"19360\",\"packageId\":\"\",\"ssuId\":2182800037,\"count\":50,\"standardPrice\":10000,\"cartPrice\":10000,\"prePrice\":0,\"reduceAmount\":0,\"source\":\"gift\",\"sourceCode\":\"21513686\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"parentItemId\":\"2192100012\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":true,\"marketPrice\":10000,\"cannotUseEcard\":true,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,8,5,9,27,7,10,23,26,22,25,28,70,71,72,2001,500,501,502,503,504,505,2002,29],\"cannotUseCouponTypes\":[],\"saleSource\":\"gift\",\"saleSources\":[],\"cannotUseRedPacket\":true,\"groupId\":1,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false,\"canAdjustPrice\":false},{\"reduceList\":{},\"reduceShareMap\":{},\"reduceItemList\":[],\"childs\":[],\"bindMainAccessory\":false,\"onsaleReduce\":0,\"storepriceReduce\":0,\"reduceDetailList\":{},\"checkoutPrice\":0,\"itemId\":\"2192100012\",\"sku\":\"23399\",\"packageId\":\"\",\"ssuId\":2192100012,\"count\":50,\"standardPrice\":100,\"cartPrice\":100,\"prePrice\":0,\"reduceAmount\":0,\"source\":\"\",\"sourceCode\":\"\",\"onSalePromotionIdMap\":{},\"properties\":\"\",\"displayType\":1,\"oriItemId\":\"\",\"accessCode\":[],\"cannotJoinAct\":false,\"cannotUseCoupon\":true,\"marketPrice\":100,\"cannotUseEcard\":true,\"maxUseEcardAmount\":0,\"cannotJoinActTypes\":[1,2,3,4,8,5,9,27,7,10,23,26,22,25,28,70,71,72,2001,500,501,502,503,504,505,2002,29],\"cannotUseCouponTypes\":[],\"saleSource\":\"common\",\"saleSources\":[],\"cannotUseRedPacket\":true,\"groupId\":0,\"onSaleBookingPrice\":0,\"joinOnsale\":false,\"changePriceActType\":0,\"originalCartPrice\":0,\"orderItemReduceList\":{},\"preferentialInfos\":[],\"unitId\":\"\",\"goodsType\":2,\"department\":0,\"cannotUsePoint\":false,\"canAdjustPrice\":false}],\"channel\":13,\"userId\":3150439050,\"orderId\":0,\"bargainSize\":10,\"noSaveDbSubmit\":false,\"useRedPacket\":false,\"calculateRedpacket\":false,\"useBeijingcoupon\":false,\"orgCode\":\"\",\"uidType\":\"\",\"cityId\":0,\"shoppingMode\":0,\"shipmentId\":2,\"shipmentExpense\":0,\"globalBusinessPartner\":\"car\",\"fromPriceProtect\":false,\"userIsFriend\":0,\"usePoint\":true,\"pointReduceAmount\":0,\"submitType\":0,\"usePurchaseSubsidy\":false,\"region\":{\"province\":2,\"city\":36,\"district\":377,\"area\":377001}}";

        CheckoutPromotionV2Request req = GsonUtil.fromJson(json,
                CheckoutPromotionV2Request.class);
        promotionDubboService.checkoutPromotionV2(req);


    }

    public List<CartItem> getCartItemList() {
        // 套装子品
        TestItem childItem1 = new TestItem(2192100012L, 100L, 1, null);
        TestItem childItem2 = new TestItem(2221000020L, 9900L, 1, null);
        TestItem childItem3 = new TestItem(2221000608L, 1900L, 1, null);
        List<TestItem> childItemList = Arrays.asList(childItem1, childItem2, childItem3);
        TestItem item1 = new TestItem(2230000294L, 11900L, 1, childItemList);
        TestItem item2 = new TestItem(2230000326L, 5000L, 1, null);
        TestItem item3 = new TestItem(2230000333L, 4500L, 1, null);
        TestItem item4 = new TestItem(2230000335L ,1000L, 1, null);
        List<TestItem> itemList = Arrays.asList(item1, item2, item3, item4);

        return getCartItemList(itemList);
    }

    @Test
    public void checkoutPromotion_testCheckoutV2_5() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        request.setCartList(getCartItemList());
        request.setClientId(180100031052L);
        request.setUserId(3150441360L);
        request.setUsePoint(true);
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);
        log.info("checkoutPromotionV2 result:{}", GsonUtil.toJson(result));
        assert result.getCode() == 0;
    }

    @Test
    public void checkoutPromotion_testCheckoutV2_6() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        request.setGetCouponList(false);
        request.setShowType(1L);
        request.setUseDefaultCoupon(true);
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        request.setUserId(3150075543L);
        request.setOrderId(0L);
        request.setBargainSize(10L);
        request.setNoSaveDbSubmit(false);
        request.setUseRedPacket(false);
        request.setCalculateRedpacket(false);
        request.setUseBeijingcoupon(false);
        request.setOrgCode("");
        request.setUidType("");
        request.setCityId(0L);
        request.setShoppingMode(0L);
        request.setShipmentId(2);
        request.setShipmentExpense(0L);
        request.setGlobalBusinessPartner("car");
        request.setFromPriceProtect(false);
        request.setUserIsFriend(0);
        request.setUsePoint(true);
        request.setPointReduceAmount(0L);
        request.setSubmitType(0);
        request.setUsePurchaseSubsidy(false);
        Region region = new Region();
        region.setProvince(2);
        region.setCity(36);
        region.setDistrict(378);
        region.setArea(378001);
        request.setRegion(region);
        CartItem cartItem = new CartItem();
        cartItem.setBindMainAccessory(false);
        cartItem.setOnsaleReduce(0L);
        cartItem.setStorepriceReduce(0L);
        cartItem.setCheckoutPrice(0L);
        cartItem.setItemId(String.valueOf(600013315L));
        cartItem.setPackageId(String.valueOf(600013315L));
        cartItem.setSsuId(600013315L);
        cartItem.setSsuType(1);
        cartItem.setStandardPrice(2400L);
        cartItem.setCartPrice(2400L);
        cartItem.setReduceAmount(0L);
        cartItem.setDisplayType(1);
        cartItem.setCannotUseCoupon(true);
        cartItem.setMarketPrice(2400L);
        cartItem.setCannotUsePoint(false);
        cartItem.setGoodsType(2);
        cartItem.setCount(1);

        CartItemChild child1 = new CartItemChild();
        child1.setSku("11111");
        child1.setSsuId(2212700001L);
        child1.setSellPrice(1000L);
        child1.setCount(2);
        child1.setOriginalSellPrice(0L);
        child1.setCartPrice(0L);

        CartItemChild child2 = new CartItemChild();
        child2.setSku("15591");
        child2.setSsuId(2171500037L);
        child2.setSellPrice(400L);
        child2.setCount(1);
        child2.setOriginalSellPrice(0L);
        child2.setCartPrice(0L);

        CartItem cartItem1 = new CartItem();
        cartItem1.setBindMainAccessory(false);
        cartItem1.setOnsaleReduce(0L);
        cartItem1.setStorepriceReduce(0L);
        cartItem1.setCheckoutPrice(0L);
        cartItem1.setItemId(String.valueOf(600013314L));
        cartItem1.setPackageId(String.valueOf(600013314L));
        cartItem1.setSsuId(600013314L);
        cartItem1.setSsuType(1);
        cartItem1.setStandardPrice(1300L);
        cartItem1.setCartPrice(1300L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotUseCoupon(true);
        cartItem1.setMarketPrice(1300L);
        cartItem1.setCannotUsePoint(false);
        cartItem1.setGoodsType(2);
        cartItem1.setCount(1);

        CartItemChild child3 = new CartItemChild();
        child3.setSku("11111");
        child3.setSsuId(2212700001L);
        child3.setSellPrice(1000L);
        child3.setCount(1);
        child3.setOriginalSellPrice(0L);
        child3.setCartPrice(0L);

        CartItemChild child4 = new CartItemChild();
        child4.setSku("15592");
        child4.setSsuId(2171500036L);
        child4.setSellPrice(300L);
        child4.setCount(1);
        child4.setOriginalSellPrice(0L);
        child4.setCartPrice(0L);

        cartItem.setChilds(Arrays.asList(child1, child2));
        cartItem1.setChilds(Arrays.asList(child3, child4));
        request.setCartList(Arrays.asList(cartItem, cartItem1));
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);

        log.info("checkoutPromotionV2 result:{}", GsonUtil.toJson(result));
    }

    @Test
    public void checkoutPromotion_testCheckoutV2_7() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        request.setGetCouponList(false);
        request.setShowType(1L);
        request.setUseDefaultCoupon(true);
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        request.setUserId(3150075543L);
        request.setOrderId(0L);
        request.setBargainSize(10L);
        request.setNoSaveDbSubmit(false);
        request.setUseRedPacket(false);
        request.setCalculateRedpacket(false);
        request.setUseBeijingcoupon(false);
        request.setOrgCode("");
        request.setUidType("");
        request.setCityId(0L);
        request.setShoppingMode(0L);
        request.setShipmentId(2);
        request.setShipmentExpense(0L);
        request.setGlobalBusinessPartner("car");
        request.setFromPriceProtect(false);
        request.setUserIsFriend(0);
        request.setUsePoint(true);
        request.setPointReduceAmount(0L);
        request.setSubmitType(0);
        request.setUsePurchaseSubsidy(false);
        Region region = new Region();
        region.setProvince(2);
        region.setCity(36);
        region.setDistrict(378);
        region.setArea(378001);
        request.setRegion(region);

        CartItem cartItem = new CartItem();
        cartItem.setBindMainAccessory(false);
        cartItem.setOnsaleReduce(0L);
        cartItem.setStorepriceReduce(0L);
        cartItem.setCheckoutPrice(0L);
        cartItem.setItemId(String.valueOf(600013314L));
        cartItem.setPackageId(String.valueOf(600013314L));
        cartItem.setSsuId(600013314L);
        cartItem.setSsuType(1);
        cartItem.setStandardPrice(1300L);
        cartItem.setCartPrice(1300L);
        cartItem.setReduceAmount(0L);
        cartItem.setDisplayType(1);
        cartItem.setCannotUseCoupon(false);
        cartItem.setMarketPrice(1300L);
        cartItem.setCannotUsePoint(false);
        cartItem.setGoodsType(2);
        cartItem.setCount(1);
        cartItem.setSaleSource("common");

        CartItemChild child1 = new CartItemChild();
        child1.setSku("11111");
        child1.setSsuId(2212700001L);
        child1.setSellPrice(10L);
        child1.setCount(1);
        child1.setOriginalSellPrice(0L);
        child1.setCartPrice(0L);

        CartItemChild child2 = new CartItemChild();
        child2.setSku("15592");
        child2.setSsuId(2171500036L);
        child2.setSellPrice(3L);
        child2.setCount(1);
        child2.setOriginalSellPrice(0L);
        child2.setCartPrice(0L);

        CartItem cartItem1 = new CartItem();
        cartItem1.setBindMainAccessory(false);
        cartItem1.setOnsaleReduce(0L);
        cartItem1.setStorepriceReduce(0L);
        cartItem1.setCheckoutPrice(0L);
        cartItem1.setItemId("600013314_gift_2182800037_21522553_1");
        cartItem1.setPackageId("");
        cartItem1.setSsuId(2182800037L);
        cartItem1.setSsuType(0);
        cartItem1.setStandardPrice(10000L);
        cartItem1.setCartPrice(10000L);
        cartItem1.setReduceAmount(0L);
        cartItem1.setDisplayType(1);
        cartItem1.setCannotUseCoupon(false);
        cartItem1.setMarketPrice(10000L);
        cartItem1.setCannotUsePoint(false);
        cartItem1.setGoodsType(2);
        cartItem1.setSource("gift");
        cartItem1.setSourceCode("600013314");
        cartItem1.setSaleSource("gift");
        cartItem1.setCount(1);



        cartItem.setChilds(Lists.newArrayList(child1, child2));

        request.setCartList(Lists.newArrayList(cartItem, cartItem1));
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);

        log.info("checkoutPromotionV2 result:{}", GsonUtil.toJson(result));
    }

    @Test
    public void checkoutPromotion_testCheckoutV2_8() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        request.setGetCouponList(false);
        request.setShowType(1L);
        request.setUseDefaultCoupon(true);
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        request.setUserId(3150075543L);
        request.setOrderId(0L);
        request.setBargainSize(10L);
        request.setNoSaveDbSubmit(false);
        request.setUseRedPacket(false);
        request.setCalculateRedpacket(false);
        request.setUseBeijingcoupon(false);
        request.setOrgCode("");
        request.setUidType("");
        request.setCityId(0L);
        request.setShoppingMode(0L);
        request.setShipmentId(2);
        request.setShipmentExpense(0L);
        request.setGlobalBusinessPartner("car");
        request.setFromPriceProtect(false);
        request.setUserIsFriend(0);
        request.setUsePoint(true);
        request.setPointReduceAmount(0L);
        request.setSubmitType(0);
        request.setUsePurchaseSubsidy(false);
        Region region = new Region();
        region.setProvince(2);
        region.setCity(36);
        region.setDistrict(378);
        region.setArea(378001);
        request.setRegion(region);

        CartItem cartItem = new CartItem();
        cartItem.setBindMainAccessory(false);
        cartItem.setOnsaleReduce(0L);
        cartItem.setStorepriceReduce(0L);
        cartItem.setCheckoutPrice(0L);
        cartItem.setItemId(String.valueOf(600013342L));
        cartItem.setPackageId(String.valueOf(600013342L));
        cartItem.setSsuId(600013342L);
        cartItem.setSsuType(1);
        cartItem.setStandardPrice(13000L);
        cartItem.setCartPrice(13000L);
        cartItem.setMarketPrice(13000L);
        cartItem.setCount(2);
        cartItem.setReduceAmount(0L);
        cartItem.setDisplayType(1);
        cartItem.setCannotUseCoupon(false);
        cartItem.setCannotUsePoint(false);
        cartItem.setGoodsType(2);
        cartItem.setSaleSource("common");

        CartItemChild child1 = new CartItemChild();
        child1.setSku("11111");
        child1.setSsuId(2182100017L);
        child1.setSellPrice(1500L);
        child1.setCount(2);
        child1.setOriginalSellPrice(0L);
        child1.setCartPrice(0L);

        CartItemChild child2 = new CartItemChild();
        child2.setSku("15592");
        child2.setSsuId(2230000702L);
        child2.setSellPrice(5000L);
        child2.setCount(2);
        child2.setOriginalSellPrice(0L);
        child2.setCartPrice(0L);

//        CartItem cartItem1 = new CartItem();
//        cartItem1.setBindMainAccessory(false);
//        cartItem1.setOnsaleReduce(0L);
//        cartItem1.setStorepriceReduce(0L);
//        cartItem1.setCheckoutPrice(0L);
//        cartItem1.setItemId("600013314_gift_2182800037_21522553_1");
//        cartItem1.setPackageId("");
//        cartItem1.setSsuId(2182800037L);
//        cartItem1.setSsuType(0);
//        cartItem1.setStandardPrice(10000L);
//        cartItem1.setCartPrice(10000L);
//        cartItem1.setReduceAmount(0L);
//        cartItem1.setDisplayType(1);
//        cartItem1.setCannotUseCoupon(false);
//        cartItem1.setMarketPrice(10000L);
//        cartItem1.setCannotUsePoint(false);
//        cartItem1.setGoodsType(2);
//        cartItem1.setSource("gift");
//        cartItem1.setSourceCode("600013314");
//        cartItem1.setSaleSource("gift");
//        cartItem1.setCount(1);



        cartItem.setChilds(Lists.newArrayList(child1, child2));

        request.setCartList(Lists.newArrayList(cartItem));
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);

        log.info("checkoutPromotionV2 result:{}", GsonUtil.toJson(result));
    }

    @Test
    public void checkoutPromotion_testCheckoutV2_9() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        request.setGetCouponList(true);
        request.setShowType(1L);
        request.setUseDefaultCoupon(true);
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        request.setUserId(3150428235L);
        request.setOrderId(0L);
        request.setBargainSize(10L);
        request.setNoSaveDbSubmit(false);
        request.setUseRedPacket(false);
        request.setCalculateRedpacket(false);
        request.setUseBeijingcoupon(false);
        request.setOrgCode("");
        request.setUidType("");
        request.setCityId(0L);
        request.setShoppingMode(0L);
        request.setShipmentId(2);
        request.setShipmentExpense(0L);
        request.setGlobalBusinessPartner("car");
        request.setFromPriceProtect(false);
        request.setUsePoint(true);
        request.setPointReduceAmount(0L);
        request.setSubmitType(0);
        request.setUsePurchaseSubsidy(false);
        Region region = new Region();
        region.setProvince(2);
        region.setCity(36);
        region.setDistrict(377);
        region.setArea(377001);
        request.setRegion(region);

        CartItem cartItem = new CartItem();
        cartItem.setBindMainAccessory(false);
        cartItem.setOnsaleReduce(0L);
        cartItem.setStorepriceReduce(0L);
        cartItem.setCheckoutPrice(0L);
        cartItem.setItemId(String.valueOf(600013315L));
        cartItem.setPackageId(String.valueOf(600013315L));
        cartItem.setSsuId(600013315L);
        cartItem.setSsuType(1);
        cartItem.setStandardPrice(2400L);
        cartItem.setCartPrice(2400L);
        cartItem.setMarketPrice(2400L);
        cartItem.setCount(1);
        cartItem.setReduceAmount(0L);
        cartItem.setDisplayType(1);
        cartItem.setCannotUseCoupon(false);
        cartItem.setCannotUsePoint(false);
        cartItem.setCannotUseEcard(true);
        cartItem.setGoodsType(2);
        cartItem.setSaleSource("common");
        cartItem.setJoinOnsale(false);
        cartItem.setChangePriceActType(0);
        cartItem.setCannotJoinActTypes(Lists.newArrayList(1L, 2L, 3L, 4L, 8L, 5L, 9L, 27L, 7L, 10L, 23L, 26L,
                22L, 25L, 28L, 70L, 71L, 72L, 2001L, 500L, 501L, 502L, 503L, 504L, 505L, 2002L, 29L));

        CartItemChild child1 = new CartItemChild();
        child1.setSku("11111");
        child1.setSsuId(2212700001L);
        child1.setSellPrice(1000L);
        child1.setCount(2);
        child1.setOriginalSellPrice(0L);
        child1.setCartPrice(0L);

        CartItemChild child2 = new CartItemChild();
        child2.setSku("15592");
        child2.setSsuId(2171500037L);
        child2.setSellPrice(400L);
        child2.setCount(1);
        child2.setOriginalSellPrice(0L);
        child2.setCartPrice(0L);

        cartItem.setChilds(Lists.newArrayList(child1, child2));

        request.setCartList(Lists.newArrayList(cartItem));
        Result<CheckoutPromotionV2Response> result = promotionDubboService.checkoutPromotionV2(request);

        log.info("checkoutPromotionV2 result:{}", GsonUtil.toJson(result));
    }

    @Test
    public void checkoutPromotion_testCheckoutV1_1() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        request.setSourceApi(2);
        request.setFromInterface(null);
        request.setChannel(13);
        request.setUserId(3150443207L);
        request.setCouponIds(Lists.newArrayList(1011085820L));
        request.setOrderId(5248191003035431L);
        request.setBargainSize(10L);
        request.setUsePoint(true);
        Region region = new Region();
        region.setProvince(2);
        region.setCity(36);
        region.setDistrict(384);
        region.setArea(384019);
        request.setRegion(region);

        List<CartItem> cartList = new ArrayList<>();

        // CartItem 1
        CartItem item1 = new CartItem();
        item1.setItemId(String.valueOf(600013388L));
        item1.setPackageId(String.valueOf(600013388L));
        item1.setSsuId(600013388L);
        item1.setSsuType(1);
        item1.setCount(1);
        item1.setStandardPrice(9000L);
        item1.setCartPrice(9000L);
        item1.setMarketPrice(9000L);
        item1.setCannotUseEcard(true);
        item1.setMaxUseEcardAmount(0L);
        item1.setCannotJoinActTypes(Lists.newArrayList(1L, 2L, 3L, 4L, 205L, 8L, 5L, 9L, 27L, 7L, 10L, 23L, 26L, 22L, 25L, 28L, 70L, 71L, 72L, 2001L, 500L, 501L, 502L, 503L, 504L, 505L, 2002L, 29L));
        item1.setSaleSource("common");
        item1.setSaleSources(Lists.newArrayList("common"));
        item1.setCannotUseRedPacket(true);
        item1.setGroupId(0L);
        item1.setOriginalCartPrice(9000L);

        List<CartItemChild> childs1 = new ArrayList<>();
        CartItemChild child1 = new CartItemChild();
        child1.setSku("11111");
        child1.setSsuId(2212700001L);
        child1.setSellPrice(4000L);
        child1.setCount(1);
        child1.setOriginalSellPrice(4000L);
        childs1.add(child1);

        CartItemChild child2 = new CartItemChild();
        child2.setSku("19927");
        child2.setSsuId(2182900001L);
        child2.setSellPrice(5000L);
        child2.setCount(1);
        child2.setOriginalSellPrice(5000L);
        childs1.add(child2);

        item1.setChilds(childs1);
        cartList.add(item1);

        // CartItem 2
        CartItem item2 = new CartItem();
        item2.setItemId("600013387_gift_2134700025_21528885_1");
        item2.setSku(String.valueOf(2015L));
        item2.setSsuId(2134700025L);
        item2.setCount(1);
        item2.setStandardPrice(1000L);
        item2.setCartPrice(1000L);
        item2.setSource("gift");
        item2.setSourceCode("21528885");
        item2.setMarketPrice(1000L);
        item2.setSaleSource("gift");
        item2.setSaleSources(Lists.newArrayList("gift"));
        item2.setGroupId(1L);
        item2.setOriginalCartPrice(1000L);
        cartList.add(item2);

        // CartItem 3
        CartItem item3 = new CartItem();
        item3.setItemId(String.valueOf(600013387L));
        item3.setPackageId(String.valueOf(600013387L));
        item3.setSsuId(600013387L);
        item3.setSsuType(1);
        item3.setCount(1);
        item3.setStandardPrice(11000L);
        item3.setCartPrice(11000L);
        item3.setMarketPrice(11000L);
        item3.setCannotUseEcard(true);
        item3.setMaxUseEcardAmount(0L);
        item3.setCannotJoinActTypes(Arrays.asList(1L, 2L, 3L, 4L, 205L, 8L, 5L, 9L, 27L, 7L, 10L, 23L, 26L, 22L, 25L, 28L, 70L, 71L, 72L, 2001L, 500L, 501L, 502L, 503L, 504L, 505L, 2002L, 29L));
        item3.setSaleSource("common");
        item3.setSaleSources(Lists.newArrayList("common"));
        item3.setGroupId(0L);
        item3.setOriginalCartPrice(11000L);

        List<CartItemChild> childs3 = new ArrayList<>();
        CartItemChild child3 = new CartItemChild();
        child3.setSku("8758");
        child3.setSsuId(2144900024L);
        child3.setSellPrice(2000L);
        child3.setCount(1);
        child3.setOriginalSellPrice(2000L);
        childs3.add(child3);

        CartItemChild child4 = new CartItemChild();
        child4.setSku("6868");
        child4.setSsuId(2230000702L);
        child4.setSellPrice(3000L);
        child4.setCount(1);
        child4.setOriginalSellPrice(3000L);
        childs3.add(child4);

        CartItemChild child5 = new CartItemChild();
        child5.setSku("6869");
        child5.setSsuId(2230000710L);
        child5.setSellPrice(6000L);
        child5.setCount(1);
        child5.setOriginalSellPrice(6000L);
        childs3.add(child5);

        item3.setChilds(childs3);
        cartList.add(item3);

        request.setCartList(cartList);
        Result<CheckoutPromotionResponse> result = promotionDubboService.checkoutPromotion(request);

        log.info("checkoutPromotionV1 result:{}", GsonUtil.toJson(result));
    }

}