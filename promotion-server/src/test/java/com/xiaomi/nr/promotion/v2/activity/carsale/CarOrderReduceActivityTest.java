package com.xiaomi.nr.promotion.v2.activity.carsale;

import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.activity.carsale.CarOrderReduceActivity;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionInfo;
import com.xiaomi.nr.promotion.api.dto.model.ReduceDetailAddItem;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.CarOrderReduceConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class CarOrderReduceActivityTest extends BaseTestV2 {

    @InjectMocks
    private CarOrderReduceActivity activity;

    @Mock
    private DSLEngine dslEngine;

    @Mock
    private DSLStream dslStream;

    private CarOrderReduceConfig config;
    private Map<String, ActPriceInfo> orderReduceMap;

    @BeforeEach
    public void setUp() {
        // 初始化配置
        config = new CarOrderReduceConfig();
        config.setPromotionId(1L);
        config.setName("测试汽车订单立减活动");
        
        // 初始化立减信息
        orderReduceMap = Maps.newHashMap();
        ActPriceInfo actPriceInfo1 = new ActPriceInfo();
        actPriceInfo1.setPrice(5000L);
        actPriceInfo1.setSsuId(1001L);
        orderReduceMap.put("1001", actPriceInfo1);
        
        ActPriceInfo actPriceInfo2 = new ActPriceInfo();
        actPriceInfo2.setPrice(3000L);
        actPriceInfo2.setSsuId(1002L);
        orderReduceMap.put("1002", actPriceInfo2);
        
        config.setOrderReduceMap(orderReduceMap);
    }

    @Test
    public void test_getType() {
        // 执行测试
        PromotionToolType type = activity.getType();

        // 验证结果
        assertEquals(PromotionToolType.ORDER_REDUCE, type);
    }

    @Test
    public void test_getBizPlatform() {
        // 执行测试
        BizPlatformEnum platform = activity.getBizPlatform();

        // 验证结果
        assertEquals(BizPlatformEnum.CAR, platform);
    }

    @Test
    public void test_getActivityDetail() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "orderReduceMap", orderReduceMap);
        ReflectionTestUtils.setField(activity, "promotionConfig", config);

        // 执行测试
        ActivityDetail detail = activity.getActivityDetail();

        // 验证结果
        assertNotNull(detail);
        assertEquals(orderReduceMap, detail.getPriceInfoMap());
    }

    @Test
    public void test_buildCartPromotionInfo() throws BizError {
        // 准备测试数据
        LocalContext context = new LocalContext();

        // 执行测试
        PromotionInfo result = activity.buildCartPromotionInfo(context);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void test_getProductGoodsAct() throws BizError {
        // 准备测试数据
        Long clientId = 123L;
        String orgCode = "TEST_ORG";
        List<String> skuPackageList = List.of("SKU1", "SKU2");

        // 执行测试
        Map<String, ProductActInfo> result = activity.getProductGoodsAct(clientId, orgCode, skuPackageList);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void test_checkUsableAddAct_success() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "orderReduceMap", orderReduceMap);
        
        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setSsuId(1001L);
        item.setReduce(4000L); // 小于配置的5000L

        // 执行测试
        Boolean result = activity.checkUsableAddAct(item);

        // 验证结果
        assertTrue(result);
    }

    @Test
    public void test_checkUsableAddAct_fail_price_too_high() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "orderReduceMap", orderReduceMap);
        
        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setSsuId(1001L);
        item.setReduce(6000L); // 大于配置的5000L

        // 执行测试
        Boolean result = activity.checkUsableAddAct(item);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void test_checkUsableAddAct_fail_no_matching_ssu() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "orderReduceMap", orderReduceMap);
        
        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setSsuId(9999L); // 不存在的SSU
        item.setReduce(1000L);

        // 执行测试
        Boolean result = activity.checkUsableAddAct(item);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void test_checkUsableAddAct_fail_null_map() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "orderReduceMap", null);
        
        ReduceDetailAddItem item = new ReduceDetailAddItem();
        item.setSsuId(1001L);
        item.setReduce(1000L);

        // 执行测试
        Boolean result = activity.checkUsableAddAct(item);

        // 验证结果
        assertFalse(result);
    }
}