package com.xiaomi.nr.promotion.v2.resource.external;

import com.xiaomi.micar.club.api.model.member.MemberInfo;
import com.xiaomi.nr.promotion.activity.carshop.CarShopVipDiscountActivity;
import com.xiaomi.nr.promotion.activity.pool.ActSearchParam;
import com.xiaomi.nr.promotion.activity.pool.CarShopActivitySearcher;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.domain.activity.service.common.VipMemberService;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.resource.external.CarUserVipExternalProvider;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.*;

//@ExtendWith(MockitoExtension.class)
public class CarUserVipExternalProviderTest extends BaseTestV2 {

    @Mock
    private VipMemberService vipMemberService;

    @Mock
    private CarShopActivitySearcher carShopActivitySearcher;

    @InjectMocks
    private CarUserVipExternalProvider provider;

    @Mock
    private ListenableFuture<MemberInfo> mockFuture;

    private CheckoutPromotionRequest request;
    private List<CartItem> cartList;
    private List<ActSearchParam.GoodsInSearch> goodsInSearchList;
    private List<ActivityTool> activityTools;

    @BeforeEach
    void setUp() {
        request = new CheckoutPromotionRequest();
        cartList = new ArrayList<>();
        request.setCartList(cartList);
        
        goodsInSearchList = new ArrayList<>();
        activityTools = new ArrayList<>();
        
        when(carShopActivitySearcher.createSearchGoods(any())).thenReturn(goodsInSearchList);
    }

    @Test
    void test_doPrepare_fail() {
        request.setChannel(ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue());
        provider.prepare(request);

        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        request.setUserId(-1L);
        provider.prepare(request);

        request.setUserId(123L);
        when(carShopActivitySearcher.searchCarActivity(any())).thenReturn(activityTools);
        provider.prepare(request);

        verify(vipMemberService, never()).getUserMemberInfoAsync(anyLong(), anyString());
    }

    @Test
    void test_doPrepare_success() {
        // Given
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        request.setUserId(123L);
        request.setVid("test-vid");
        
        ActivityTool mockTool = new CarShopVipDiscountActivity();
        activityTools.add(mockTool);
        
        when(carShopActivitySearcher.searchCarActivity(any())).thenReturn(activityTools);
        when(vipMemberService.getUserMemberInfoAsync(anyLong(), anyString())).thenReturn(mockFuture);

        // When
        provider.prepare(request);

        // Then
        verify(carShopActivitySearcher).searchCarActivity(any());
        verify(vipMemberService).getUserMemberInfoAsync(eq(123L), eq("test-vid"));
    }
}
