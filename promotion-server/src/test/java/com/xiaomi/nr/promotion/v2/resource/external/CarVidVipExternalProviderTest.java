package com.xiaomi.nr.promotion.v2.resource.external;

import com.xiaomi.micar.club.api.model.member.MemberInfo;
import com.xiaomi.nr.promotion.activity.pool.ActSearchParam;
import com.xiaomi.nr.promotion.activity.pool.MaintenanceActivitySearcher;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.domain.activity.service.common.VipMemberService;
import com.xiaomi.nr.promotion.engine.ActivityTool;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.resource.external.CarVidVipExternalProvider;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

//@ExtendWith(MockitoExtension.class)
public class CarVidVipExternalProviderTest extends BaseTestV2 {

    @Mock
    private VipMemberService vipMemberService;

    @Mock
    private MaintenanceActivitySearcher maintenanceActivitySearcher;

    @InjectMocks
    private CarVidVipExternalProvider provider;

    @Mock
    private ListenableFuture<MemberInfo> mockFuture;

    private CheckoutPromotionRequest request;
    private List<CartItem> cartList;
    private List<ActSearchParam.GoodsInSearch> goodsInSearchList;
    private List<ActivityTool> activityTools;

    @BeforeEach
    void setUp() {
        request = new CheckoutPromotionRequest();
        cartList = new ArrayList<>();
        request.setCartList(cartList);
        activityTools = new ArrayList<>();

        goodsInSearchList = new ArrayList<>();
        when(maintenanceActivitySearcher.createSearchGoods(any())).thenReturn(goodsInSearchList);
    }

    @Test
    void test_doPrepare_fail() {
        request.setChannel(ChannelEnum.CAR_SHOP.getValue());
        provider.prepare(request);

        request.setChannel(ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue());
        request.setVid("");
        provider.prepare(request);

        request.setVid("123");
        when(maintenanceActivitySearcher.searchCarActivity(any())).thenReturn(activityTools);
        provider.prepare(request);

        verify(maintenanceActivitySearcher).searchCarActivity(any());
        verify(vipMemberService, never()).getVidMemberInfoAsync(anyString());
    }

    @Test
    void test_doPrepare_success() {
        // Given
        request.setChannel(ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue());
        request.setVid("test-vid");
        
        ActivityTool mockTool = mock(ActivityTool.class);
        when(mockTool.getType()).thenReturn(PromotionToolType.MAINTENANCE_REPAIR_DISCOUNT);
        activityTools = Collections.singletonList(mockTool);
        
        when(maintenanceActivitySearcher.searchCarActivity(any())).thenReturn(activityTools);
        when(vipMemberService.getVidMemberInfoAsync(anyString())).thenReturn(mockFuture);

        // When
        provider.prepare(request);

        // Then
        verify(maintenanceActivitySearcher).searchCarActivity(any());
        verify(vipMemberService).getVidMemberInfoAsync(eq("test-vid"));
    }
}
