package com.xiaomi.nr.promotion.v2;

import com.google.common.collect.Lists;
import com.xiaomi.mit.unittest.configuration.DbMockConfiguration;
import com.xiaomi.nr.md.promotion.admin.api.dto.activity.ActivityConfig;
import com.xiaomi.nr.promotion.activity.pool.CarPromotionInstancePool;
import com.xiaomi.nr.promotion.activity.pool.PromotionInstancePool;
import com.xiaomi.nr.promotion.bootstrap.PromotionBootstrap;
import com.xiaomi.nr.promotion.rpc.mdpromotionadmin.PromotionAdminCustomServiceProxy;
import com.xiaomi.nr.promotion.util.GsonUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.*;

/**
 * @author: zhangliwei6
 * @date: 2025/2/25 15:39
 * @description:
 */
@ActiveProfiles("ut")
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {DbMockConfiguration.class, PromotionBootstrap.class})
public abstract class BaseTestV2 {

    @Autowired
    private PromotionInstancePool promotionInstancePool;
    @Autowired
    private CarPromotionInstancePool carPromotionInstancePool;

    @BeforeEach
    public void beforeAll(){
        PromotionAdminCustomServiceProxy mockPromotionAdminCustomServiceProxy = PowerMockito.mock(PromotionAdminCustomServiceProxy.class);

        ArrayList<ActivityConfig> carActivityConfigs = Lists.newArrayList();

        String carStr16 = "{\"id\":21551030,\"name\":\"权益中心选装基金活动-600016359-1\",\"channels\":[11],\"beginTime\":1746687835,\"endTime\":4102329600,\"sourceApp\":15,\"uniqId\":\"bd65afcc4b59476bb2d2c502c656a015\",\"status\":1,\"promotionType\":28,\"rule\":\"{\\\"reducePrice\\\":10000,\\\"budgetSpecification\\\":{\\\"budgetApplyNo\\\":\\\"BR202404020002\\\",\\\"lineNum\\\":9208},\\\"title\\\":\\\"这是一个选装基金活动权益名称\\\",\\\"description\\\":\\\"这是一个选装基金活动说明文案\\\",\\\"descriptionTitle\\\":\\\"这是一个选装基金活动文案标题\\\",\\\"benefitSpecification\\\":{\\\"startTime\\\":1743436800,\\\"endTime\\\":4102329600}}\",\"expand\":\"\",\"createTime\":\"May 8, 2025 3:02:27 PM\",\"updateTime\":\"May 8, 2025 3:02:27 PM\",\"creator\":\"v-liuqingqing1\",\"activityScopeList\":[{\"scopeType\":9,\"relation\":1,\"scopeValue\":\"1\"},{\"scopeType\":10,\"relation\":1,\"scopeValue\":\"600016359\"}],\"productPolicyList\":[{\"id\":243956327,\"productIdType\":5,\"productId\":600019901,\"ssuType\":0,\"productLevel\":2,\"productGroup\":0,\"promotionPrice\":0,\"stock\":0,\"userLimitNum\":0,\"validity\":1},{\"id\":243956328,\"productIdType\":5,\"productId\":600016359,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":0,\"stock\":0,\"userLimitNum\":0,\"validity\":1}],\"tradeType\":1}";
        ActivityConfig carActivityConfig16 = GsonUtil.fromJson(carStr16, ActivityConfig.class);
        carActivityConfigs.add(carActivityConfig16);


        String carStr15 = "{\"id\":21551031,\"name\":\"权益中心直降活动-600016359-1\",\"channels\":[11],\"beginTime\":1746374400,\"endTime\":4102329600,\"sourceApp\":15,\"uniqId\":\"6ec5171a355e425dbd4d22f48142b17c\",\"status\":1,\"promotionType\":20,\"rule\":\"{\\\"budgetSpecification\\\":{\\\"budgetApplyNo\\\":\\\"BR202403200002\\\",\\\"lineNum\\\":9003},\\\"benefitSpecification\\\":{\\\"startTime\\\":1743436800,\\\"endTime\\\":4102329600}}\",\"expand\":\"\",\"createTime\":\"May 8, 2025 4:22:54 PM\",\"updateTime\":\"May 8, 2025 4:22:54 PM\",\"creator\":\"v-liuqingqing1\",\"activityScopeList\":[{\"scopeType\":9,\"relation\":1,\"scopeValue\":\"1\"},{\"scopeType\":10,\"relation\":1,\"scopeValue\":\"600016359\"}],\"productPolicyList\":[{\"id\":243956329,\"productIdType\":5,\"productId\":600016359,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":300,\"stock\":0,\"userLimitNum\":0,\"validity\":1},{\"id\":243956330,\"productIdType\":5,\"productId\":600016371,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":300,\"stock\":0,\"userLimitNum\":0,\"validity\":1},{\"id\":243956331,\"productIdType\":5,\"productId\":600016372,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":300,\"stock\":0,\"userLimitNum\":0,\"validity\":1}],\"tradeType\":1}";
        ActivityConfig carActivityConfig15 = GsonUtil.fromJson(carStr15, ActivityConfig.class);
        carActivityConfigs.add(carActivityConfig15);

        String carStr14 = "{\"id\":21549760,\"name\":\"权益中心选装基金活动-600016401-1\",\"channels\":[11],\"beginTime\":1746374400,\"endTime\":4102329600,\"sourceApp\":15,\"uniqId\":\"d44b30e950e44a0abc9d026d011d9513\",\"status\":1,\"promotionType\":28,\"rule\":\"{\\\"reducePrice\\\":20000,\\\"budgetSpecification\\\":{\\\"budgetApplyNo\\\":\\\"BR202403200004\\\",\\\"lineNum\\\":9009},\\\"title\\\":\\\"测试\\\",\\\"description\\\":\\\"测试\\\",\\\"descriptionTitle\\\":\\\"测试\\\"}\",\"expand\":\"\",\"createTime\":\"Apr 30, 2025 3:39:26 PM\",\"updateTime\":\"Apr 30, 2025 3:40:16 PM\",\"creator\":\"p-chenshantao\",\"activityScopeList\":[{\"scopeType\":9,\"relation\":1,\"scopeValue\":\"1\"},{\"scopeType\":10,\"relation\":1,\"scopeValue\":\"600016401\"}],\"productPolicyList\":[{\"id\":243951637,\"productIdType\":5,\"productId\":600020446,\"ssuType\":0,\"productLevel\":2,\"productGroup\":0,\"promotionPrice\":0,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1},{\"id\":243951638,\"productIdType\":5,\"productId\":600016401,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":0,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1}],\"tradeType\":0}";
        ActivityConfig carActivityConfig14 = GsonUtil.fromJson(carStr14, ActivityConfig.class);
        carActivityConfigs.add(carActivityConfig14);

        //============================test_submitPromotion测试数据开始============================
        String carStr12 = "{\"id\":21507614,\"name\":\"订单立减测试活动11\",\"channels\":[11],\"beginTime\":1721725200,\"endTime\":4102329600,\"sourceApp\":15,\"uniqId\":\"bd03740ad3ec4a5a851817ceeda44396\",\"status\":1,\"promotionType\":31,\"rule\":\"{\\\"budgetSpecification\\\":{\\\"budgetApplyNo\\\":\\\"BR202404020002\\\",\\\"lineNum\\\":9204}}\",\"expand\":\"\",\"createTime\":\"Jul 12, 2024 4:20:47 PM\",\"updateTime\":\"Mar 25, 2025 7:22:07 PM\",\"creator\":\"caoxiaopeng1\",\"productPolicyList\":[{\"id\":14298656,\"productIdType\":5,\"productId\":600003441,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":10000,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1},{\"id\":14298657,\"productIdType\":5,\"productId\":600003440,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":10000,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1},{\"id\":14298658,\"productIdType\":5,\"productId\":600003439,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":10000,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1},{\"id\":14298659,\"productIdType\":5,\"productId\":600003438,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":10000,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1},{\"id\":14298660,\"productIdType\":5,\"productId\":600003437,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":10000,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1}],\"tradeType\":0}";
        ActivityConfig carActivityConfig12 = GsonUtil.fromJson(carStr12, ActivityConfig.class);
        carActivityConfigs.add(carActivityConfig12);
        String carStr11 = "{\"id\":21534572,\"name\":\"2025置换二期置换补贴\",\"channels\":[11],\"beginTime\":1738720800,\"endTime\":4102329600,\"sourceApp\":15,\"uniqId\":\"49a1bbbb944a40c497b596de82184fe1\",\"status\":1,\"promotionType\":29,\"rule\":\"{\\\"budgetSpecification\\\":{\\\"budgetApplyNo\\\":\\\"BR202404020002\\\",\\\"lineNum\\\":9204}}\",\"expand\":\"\",\"createTime\":\"Jan 25, 2025 4:21:54 PM\",\"updateTime\":\"Mar 25, 2025 7:25:12 PM\",\"creator\":\"zuoguoyi\",\"productPolicyList\":[{\"id\":243894327,\"productIdType\":5,\"productId\":600003438,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":1000,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1}],\"tradeType\":0}";
        ActivityConfig carActivityConfig11 = GsonUtil.fromJson(carStr11, ActivityConfig.class);
        carActivityConfigs.add(carActivityConfig11);
        //============================test_submitPromotion测试数据结束============================

        //============================test_cartCheckoutPromotion_car_vehicle_onSale-测试数据开始============================
        String carStr10 = "{\"id\":21528318,\"name\":\"小米汽车单品的直降活动\",\"channels\":[11],\"beginTime\":1734077400,\"endTime\":4102329600,\"sourceApp\":1,\"uniqId\":\"1734571105226\",\"status\":1,\"promotionType\":20,\"rule\":\"\",\"expand\":\"{\\\"extFiles\\\":[],\\\"workFlows\\\":[{\\\"type\\\":13,\\\"workflowId\\\":305}]}\",\"createTime\":\"Dec 13, 2024 4:06:24 PM\",\"updateTime\":\"Mar 25, 2025 7:24:29 PM\",\"creator\":\"p-yanghuixiang\",\"activityScopeList\":[{\"scopeType\":6,\"relation\":2,\"scopeValue\":\"*\"}],\"productPolicyList\":[{\"id\":14376828,\"productIdType\":5,\"productId\":2230000702,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":4000,\"stock\":0,\"userLimitNum\":0,\"rule\":\"{\\\"sku\\\":6868,\\\"ssuId\\\":2230000702}\",\"validity\":1}],\"tradeType\":0}";
        ActivityConfig carActivityConfig10 = GsonUtil.fromJson(carStr10, ActivityConfig.class);
        carActivityConfigs.add(carActivityConfig10);
        //============================test_cartCheckoutPromotion_car_vehicle_onSale-测试数据结束============================

        //============================test_getPromotionPrice_car_vehicle_success-测试数据开始============================
        String carStr5 = "{\"id\":21460863,\"name\":\"权益中心直降活动-124\",\"channels\":[11],\"beginTime\":1693986531,\"endTime\":4102329600,\"sourceApp\":15,\"uniqId\":\"6b29c39a3303454f8acb1637ff72818f\",\"status\":1,\"promotionType\":20,\"rule\":\"\",\"expand\":\"\",\"createTime\":\"Sep 7, 2023 7:56:40 PM\",\"updateTime\":\"Mar 25, 2025 7:17:14 PM\",\"creator\":\"zuopengpeng\",\"activityScopeList\":[{\"scopeType\":9,\"relation\":1,\"scopeValue\":\"null\"}],\"productPolicyList\":[{\"id\":14127396,\"productIdType\":5,\"productId\":124,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":10000,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1}],\"tradeType\":0}";
        ActivityConfig carActivityConfig5 = GsonUtil.fromJson(carStr5, ActivityConfig.class);
        String carStr6 = "{\"id\":21547341,\"name\":\"权益中心直降活动-600003441-1\",\"channels\":[11],\"beginTime\":1744699836,\"endTime\":4102329600,\"sourceApp\":15,\"uniqId\":\"c146871ac42e42d9add0aab31eef6b1a\",\"status\":1,\"promotionType\":20,\"rule\":\"{\\\"budgetSpecification\\\":{\\\"budgetApplyNo\\\":\\\"BR202403190002\\\",\\\"lineNum\\\":8983}}\",\"expand\":\"\",\"createTime\":\"Apr 15, 2025 2:48:53 PM\",\"updateTime\":\"Apr 15, 2025 2:48:53 PM\",\"creator\":\"genglinlin\",\"activityScopeList\":[{\"scopeType\":9,\"relation\":1,\"scopeValue\":\"1\"},{\"scopeType\":10,\"relation\":1,\"scopeValue\":\"600003441\"}],\"productPolicyList\":[{\"id\":243942841,\"productIdType\":5,\"productId\":600003441,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":100,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1}],\"tradeType\":0}";
        ActivityConfig carActivityConfig6 = GsonUtil.fromJson(carStr6, ActivityConfig.class);
        String carStr7 = "{\"id\":21547180,\"name\":\"权益中心直降活动-600000750-1\",\"channels\":[11],\"beginTime\":1744819200,\"endTime\":4102329600,\"sourceApp\":15,\"uniqId\":\"b77018d0a51a429f91c051ce688c9982\",\"status\":1,\"promotionType\":20,\"rule\":\"{\\\"budgetSpecification\\\":{\\\"budgetApplyNo\\\":\\\"BR202403190002\\\",\\\"lineNum\\\":8983}}\",\"expand\":\"\",\"createTime\":\"Apr 14, 2025 3:44:45 PM\",\"updateTime\":\"Apr 14, 2025 3:44:45 PM\",\"creator\":\"zhangtianji\",\"activityScopeList\":[{\"scopeType\":9,\"relation\":1,\"scopeValue\":\"1\"},{\"scopeType\":10,\"relation\":1,\"scopeValue\":\"600000750\"}],\"productPolicyList\":[{\"id\":243942249,\"productIdType\":5,\"productId\":600000753,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":0,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1}],\"tradeType\":0}";
        ActivityConfig carActivityConfig7 = GsonUtil.fromJson(carStr7, ActivityConfig.class);
        String carStr8 = "{\"id\":21547178,\"name\":\"权益中心直降活动-600000788-1\",\"channels\":[11],\"beginTime\":1746028800,\"endTime\":4102329600,\"sourceApp\":15,\"uniqId\":\"adfa0f87e1554f6f9a3c2702165a8ff6\",\"status\":1,\"promotionType\":20,\"rule\":\"{\\\"budgetSpecification\\\":{\\\"budgetApplyNo\\\":\\\"BR202403190002\\\",\\\"lineNum\\\":8983}}\",\"expand\":\"\",\"createTime\":\"Apr 14, 2025 3:39:26 PM\",\"updateTime\":\"Apr 14, 2025 3:41:41 PM\",\"creator\":\"zhangtianji\",\"activityScopeList\":[{\"scopeType\":9,\"relation\":1,\"scopeValue\":\"1\"},{\"scopeType\":10,\"relation\":1,\"scopeValue\":\"600000788\"}],\"productPolicyList\":[{\"id\":243942245,\"productIdType\":5,\"productId\":600000797,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":0,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1}],\"tradeType\":0}";
        ActivityConfig carActivityConfig8 = GsonUtil.fromJson(carStr8, ActivityConfig.class);
        String carStr9 = "{\"id\":21546532,\"name\":\"权益中心直降活动-600016359-1\",\"channels\":[11],\"beginTime\":1744254540,\"endTime\":4102329600,\"sourceApp\":15,\"uniqId\":\"9d2f10b1976c44c1955ccd6eea59c721\",\"status\":1,\"promotionType\":20,\"rule\":\"{\\\"budgetSpecification\\\":{\\\"budgetApplyNo\\\":\\\"BR202403190002\\\",\\\"lineNum\\\":8983},\\\"benefitSpecification\\\":{\\\"startTime\\\":1744254363,\\\"endTime\\\":1746028799}}\",\"expand\":\"\",\"createTime\":\"Apr 10, 2025 11:07:11 AM\",\"updateTime\":\"Apr 10, 2025 2:48:34 PM\",\"creator\":\"p-xianghan1\",\"activityScopeList\":[{\"scopeType\":9,\"relation\":1,\"scopeValue\":\"1\"},{\"scopeType\":10,\"relation\":1,\"scopeValue\":\"600016359\"}],\"productPolicyList\":[{\"id\":243939912,\"productIdType\":5,\"productId\":600020061,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":50,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1}],\"tradeType\":1}";
        ActivityConfig carActivityConfig9 = GsonUtil.fromJson(carStr9, ActivityConfig.class);
        carActivityConfigs.add(carActivityConfig5);
        carActivityConfigs.add(carActivityConfig6);
        carActivityConfigs.add(carActivityConfig7);
        carActivityConfigs.add(carActivityConfig8);
        carActivityConfigs.add(carActivityConfig9);
        //============================test_getPromotionPrice_car_vehicle_success-测试数据结束============================

        //============================test_getmultiproductgoodsact测试数据开始============================
        String carStr13 = "{\"id\":21548621,\"name\":\"测试买赠\",\"channels\":[13],\"beginTime\":1745376000,\"endTime\":4102329600,\"sourceApp\":1,\"uniqId\":\"1745375911669\",\"status\":1,\"promotionType\":21,\"rule\":\"{\\\"giftType\\\":2,\\\"ignoreStock\\\":true,\\\"isFMember\\\":2,\\\"configUrl\\\":\\\"测试\\\",\\\"crowdList\\\":[\\\"all\\\"]}\",\"expand\":\"{\\\"extFiles\\\":[],\\\"workFlows\\\":[{\\\"type\\\":13,\\\"workflowId\\\":300}]}\",\"createTime\":\"Apr 23, 2025 10:44:00 AM\",\"updateTime\":\"Apr 23, 2025 10:44:00 AM\",\"creator\":\"p-yeyang7\",\"activityScopeList\":[{\"scopeType\":5,\"relation\":2,\"scopeValue\":\"\"},{\"scopeType\":6,\"relation\":2,\"scopeValue\":\"*\"}],\"productPolicyList\":[{\"id\":243947521,\"productIdType\":5,\"productId\":2221000456,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":0,\"stock\":0,\"userLimitNum\":0,\"rule\":\"{\\\"sku\\\":40219,\\\"ssuId\\\":2221000456}\",\"validity\":1},{\"id\":243947522,\"productIdType\":5,\"productId\":2230000337,\"ssuType\":0,\"productLevel\":2,\"productGroup\":1,\"promotionPrice\":0,\"stock\":1000,\"userLimitNum\":0,\"rule\":\"{\\\"giftBaseNum\\\":1,\\\"sku\\\":55607,\\\"ssuId\\\":2230000337}\",\"validity\":1}],\"tradeType\":0}";
        ActivityConfig carActivityConfig13 = GsonUtil.fromJson(carStr13, ActivityConfig.class);
        carActivityConfigs.add(carActivityConfig13);
        String carStr3 = "{\"id\":21474199,\"name\":\"权益中心选装基金活动-[600002303, 600002305]\",\"channels\":[11],\"beginTime\":1702396800,\"endTime\":4102329600,\"sourceApp\":15,\"uniqId\":\"0608503dfba346239258082d9396bef2\",\"status\":1,\"promotionType\":28,\"rule\":\"{\\\"reducePrice\\\":100000}\",\"expand\":\"\",\"createTime\":\"Dec 11, 2023 7:03:18 PM\",\"updateTime\":\"Mar 25, 2025 7:18:34 PM\",\"creator\":\"wangweiyi1\",\"activityScopeList\":[{\"scopeType\":9,\"relation\":1,\"scopeValue\":\"1\"},{\"scopeType\":10,\"relation\":1,\"scopeValue\":\"600002292\"}],\"productPolicyList\":[{\"id\":14175466,\"productIdType\":5,\"productId\":600002303,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":0,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1},{\"id\":14175467,\"productIdType\":5,\"productId\":600002305,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":0,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1}],\"tradeType\":0}";
        ActivityConfig carActivityConfig3 = GsonUtil.fromJson(carStr3, ActivityConfig.class);
        String carStr4 = "{\"id\":21483502,\"name\":\"汽车置换补贴测试\",\"channels\":[11],\"beginTime\":1706803200,\"endTime\":4102329600,\"sourceApp\":15,\"uniqId\":\"e94e373be500513193b414089ad75cf8\",\"status\":1,\"promotionType\":29,\"rule\":\"{\\\"reduceMoney\\\":5000,\\\"ssuIdList\\\":[600000658,600000672,600002250,600002274,600002292,600003289]}\",\"expand\":\"\",\"createTime\":\"Jan 30, 2024 4:32:12 PM\",\"updateTime\":\"Mar 25, 2025 7:19:30 PM\",\"creator\":\"duanpengqi\",\"productPolicyList\":[{\"id\":14209433,\"productIdType\":5,\"productId\":600000658,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":5000,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1},{\"id\":14209434,\"productIdType\":5,\"productId\":600000672,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":5000,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1},{\"id\":14209435,\"productIdType\":5,\"productId\":600002250,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":5000,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1},{\"id\":14209436,\"productIdType\":5,\"productId\":600002274,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":5000,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1},{\"id\":14209437,\"productIdType\":5,\"productId\":600002292,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":5000,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1},{\"id\":14209438,\"productIdType\":5,\"productId\":600003289,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":5000,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1}],\"tradeType\":0}";
        ActivityConfig carActivityConfig4 = GsonUtil.fromJson(carStr4, ActivityConfig.class);
        carActivityConfigs.add(carActivityConfig3);
        carActivityConfigs.add(carActivityConfig4);
        String carStr1 = "{\"id\":3145887,\"name\":\"测试会员折扣活动-历史活动编辑\",\"channels\":[13],\"beginTime\":1743576000,\"endTime\":4102329600,\"sourceApp\":1,\"uniqId\":\"1744033918302\",\"status\":0,\"promotionType\":55,\"rule\":\"{\\\"ruleType\\\":4,\\\"specificationList\\\":[{\\\"reduce\\\":98,\\\"threshold\\\":1}],\\\"numLimit\\\":0,\\\"userLimitNum\\\":0,\\\"reduceLimit\\\":0,\\\"maxReducePrice\\\":0,\\\"identityLimit\\\":1,\\\"crowdCode\\\":\\\"ultra_svip\\\",\\\"vipLevel\\\":100000}\",\"expand\":\"{\\\"extFiles\\\":[],\\\"workFlows\\\":[{\\\"type\\\":13,\\\"workflowId\\\":300}],\\\"desc\\\":\\\"1历史活动编辑\\\"}\",\"createTime\":\"Apr 2, 2025 2:37:46 PM\",\"updateTime\":\"Apr 7, 2025 10:06:04 PM\",\"creator\":\"p-chenweiming7\",\"productPolicyList\":[{\"id\":27798010,\"productIdType\":5,\"productId\":2145000007,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":0,\"stock\":0,\"userLimitNum\":0,\"rule\":\"{\\\"sku\\\":9831,\\\"ssuId\\\":2145000007}\",\"validity\":1}],\"tradeType\":0}";
        ActivityConfig carActivityConfig1 = GsonUtil.fromJson(carStr1, ActivityConfig.class);
        String carStr2 = "{\"id\":21472415,\"name\":\"范围立减测试活动_1128\",\"channels\":[11],\"beginTime\":1701145638,\"endTime\":4102329600,\"sourceApp\":15,\"uniqId\":\"f157501a1795416f99ba01551b30syf\",\"status\":1,\"promotionType\":28,\"rule\":\"{\\\"reducePrice\\\": 1000}\",\"expand\":\"\",\"createTime\":\"Nov 28, 2023 4:30:50 PM\",\"updateTime\":\"Mar 25, 2025 7:18:23 PM\",\"creator\":\"shangyanfei\",\"productPolicyList\":[{\"id\":14169035,\"productIdType\":5,\"productId\":600000784,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":1200,\"stock\":10,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1},{\"id\":14169623,\"productIdType\":5,\"productId\":600002303,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":1500,\"stock\":10,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1}],\"tradeType\":0}";
        ActivityConfig carActivityConfig2 = GsonUtil.fromJson(carStr2, ActivityConfig.class);
        carActivityConfigs.add(carActivityConfig1);
        carActivityConfigs.add(carActivityConfig2);
        Mockito.when(mockPromotionAdminCustomServiceProxy.queryCarActivityList(anyList(), anyList(), anyList())).thenReturn(carActivityConfigs);
        //============================test_getmultiproductgoodsact测试数据结束============================

        // ============================test_getActivitysByGoods2_success-测试数据开始============================
        ArrayList<ActivityConfig> activityConfigs = Lists.newArrayList();
        String str = "{\"id\":21452135,\"name\":\"政策自动转化测试0811_01_更新测试02\",\"channels\":[1],\"beginTime\":1735574400,\"endTime\":4102329600,\"sourceApp\":13,\"uniqId\":\"211.069276221183E0300001211\",\"status\":1,\"promotionType\":20,\"rule\":\"\",\"expand\":\"{\\\"extFiles\\\":[{\\\"name\\\":\\\"MI_logo.png\\\",\\\"url\\\":\\\"https://staging-cnbj2-fds.api.xiaomi.net/nr-inner/202201202010_d1a19a730dfc9d30d9c970227e128dcc.png\\\"}],\\\"workFlows\\\":[{\\\"type\\\":1,\\\"workflowId\\\":1}]}\",\"createTime\":\"Aug 11, 2023 3:05:56 PM\",\"updateTime\":\"Mar 25, 2025 7:16:25 PM\",\"creator\":\"\",\"activityScopeList\":[{\"scopeType\":2,\"relation\":2,\"scopeValue\":\"*\"}],\"productPolicyList\":[{\"id\":13379989,\"productIdType\":1,\"productId\":20851,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":50000,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1},{\"id\":13379990,\"productIdType\":1,\"productId\":20858,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":500001,\"stock\":0,\"userLimitNum\":0,\"rule\":\"\",\"validity\":1}],\"tradeType\":0,\"compressProduct\":[]}";
        ActivityConfig activityConfig1 = GsonUtil.fromJson(str, ActivityConfig.class);
        String str2 = "{\"id\":21546539,\"name\":\"XH买赠活动\",\"channels\":[13],\"beginTime\":1735574400,\"endTime\":4102329600,\"sourceApp\":1,\"uniqId\":\"1744274809060\",\"status\":1,\"promotionType\":21,\"rule\":\"{\\\"giftType\\\":2,\\\"ignoreStock\\\":false,\\\"isFMember\\\":2,\\\"configUrl\\\":\\\"测试\\\",\\\"crowdList\\\":[\\\"all\\\"]}\",\"expand\":\"{\\\"extFiles\\\":[],\\\"workFlows\\\":[{\\\"type\\\":13,\\\"workflowId\\\":300}]}\",\"createTime\":\"Apr 10, 2025 4:40:00 PM\",\"updateTime\":\"Apr 10, 2025 4:47:17 PM\",\"creator\":\"p-chenweiming7\",\"activityScopeList\":[{\"scopeType\":5,\"relation\":2,\"scopeValue\":\"\"},{\"scopeType\":6,\"relation\":2,\"scopeValue\":\"*\"}],\"productPolicyList\":[{\"id\":243939922,\"productIdType\":5,\"productId\":2230000957,\"ssuType\":0,\"productLevel\":1,\"productGroup\":0,\"promotionPrice\":0,\"stock\":0,\"userLimitNum\":0,\"rule\":\"{\\\"sku\\\":36782,\\\"ssuId\\\":2230000957}\",\"validity\":1},{\"id\":243939924,\"productIdType\":5,\"productId\":2143100002,\"ssuType\":0,\"productLevel\":2,\"productGroup\":1,\"promotionPrice\":0,\"stock\":1,\"userLimitNum\":0,\"rule\":\"{\\\"giftBaseNum\\\":1,\\\"sku\\\":8431,\\\"ssuId\\\":2143100002}\",\"validity\":1}],\"tradeType\":0,\"compressProduct\":[1,0,0,0,120,1,-99,-112,-69,106,-61,64,16,69,127,69,76,-67,-59,-66,100,91,-37,37,4,-126,-64,54,46,-36,-59,41,-124,-76,9,-125,-91,72,-20,67,96,-116,-2,61,35,-55,40,110,-20,34,91,-51,-36,-71,59,-113,-13,113,-123,-94,12,-40,99,-72,-28,21,24,41,82,-67,74,85,-58,-96,115,109,21,-53,-112,87,-57,75,103,-63,-92,119,10,-39,-92,-30,-12,-78,116,-51,-64,-5,56,91,-8,98,-39,-38,-34,-42,96,-60,34,-68,-69,54,118,96,102,71,-45,6,108,127,14,14,75,-22,75,-110,15,109,121,-98,-94,-24,-83,-37,98,-125,97,31,-101,73,112,-79,38,15,92,79,-32,-49,-15,4,70,-83,-42,27,-55,40,-13,49,-81,40,-1,-37,99,0,6,125,81,99,69,-121,76,-109,113,-68,70,-85,76,101,-103,-108,12,74,103,-117,96,-113,-40,-116,-3,94,58,-105,8,-50,18,-55,101,-102,104,-93,-71,-31,60,57,-20,-88,5,-6,55,91,-37,96,-23,51,31,-40,-1,-40,8,-83,-60,72,-121,-90,62,102,67,-59,27,-31,27,-101,25,-42,3,54,84,124,-62,-26,27,-65,-62,107,-31,45,81,35,38,98,-28,51,-47,-38,-48,34,-9,-80,-106,-59,-98,-62,-46,-61,-25,47,-120,-32,-94,-76]}";
        ActivityConfig activityConfig2 = GsonUtil.fromJson(str2, ActivityConfig.class);
        String str3 = "{\"id\":21546197,\"name\":\"团购价格折扣2025-05\",\"channels\":[20],\"beginTime\":1735574400,\"endTime\":4102329600,\"sourceApp\":12,\"uniqId\":\"7266d503b2b7c0f9625a8438f32d31d6\",\"status\":1,\"promotionType\":70,\"rule\":\"{\\\"discountRate\\\":{\\\"normal\\\":80,\\\"vip\\\":70,\\\"svip\\\":60}}\",\"expand\":\"\",\"createTime\":\"Apr 8, 2025 3:34:03 PM\",\"updateTime\":\"Apr 8, 2025 3:34:03 PM\",\"creator\":\"张丹红\",\"activityScopeList\":[{\"scopeType\":8,\"relation\":1,\"scopeValue\":\"3\"}],\"tradeType\":0}";
        ActivityConfig activityConfig3 = GsonUtil.fromJson(str3, ActivityConfig.class);
        String str4 = "{\"id\":21545226,\"name\":\"团购价格折扣2025-04\",\"channels\":[20],\"beginTime\":1735574400,\"endTime\":4102329600,\"sourceApp\":12,\"uniqId\":\"43a238e5c93a43bb5c8e0880e6815b74\",\"status\":1,\"promotionType\":70,\"rule\":\"{\\\"discountRate\\\":{\\\"normal\\\":90,\\\"vip\\\":80,\\\"svip\\\":60}}\",\"expand\":\"\",\"createTime\":\"Apr 2, 2025 4:56:38 PM\",\"updateTime\":\"Apr 8, 2025 3:27:47 PM\",\"creator\":\"张丹红\",\"activityScopeList\":[{\"scopeType\":8,\"relation\":1,\"scopeValue\":\"3\"}],\"tradeType\":0}";
        ActivityConfig activityConfig4 = GsonUtil.fromJson(str4, ActivityConfig.class);
        String str5 = "{\"id\":21539828,\"name\":\"团购价格折扣2025-03\",\"channels\":[20],\"beginTime\":1735574400,\"endTime\":4102329600,\"sourceApp\":12,\"uniqId\":\"51a92e76f427c92c3d61859f1cbdf33d\",\"status\":1,\"promotionType\":70,\"rule\":\"{\\\"discountRate\\\":{\\\"normal\\\":90,\\\"vip\\\":80,\\\"svip\\\":70}}\",\"expand\":\"\",\"createTime\":\"Mar 1, 2025 12:00:06 AM\",\"updateTime\":\"Mar 31, 2025 4:51:53 PM\",\"creator\":\"system\",\"activityScopeList\":[{\"scopeType\":8,\"relation\":1,\"scopeValue\":\"3\"}],\"tradeType\":0}";
        ActivityConfig activityConfig5 = GsonUtil.fromJson(str5, ActivityConfig.class);
        String str6 = "{\"id\":21535555,\"name\":\"团购价格折扣2025-02\",\"channels\":[20],\"beginTime\":1735574400,\"endTime\":4102329600,\"sourceApp\":12,\"uniqId\":\"25192121ac714878cc75fa67784bb421\",\"status\":1,\"promotionType\":70,\"rule\":\"{\\\"discountRate\\\":{\\\"normal\\\":90,\\\"vip\\\":80,\\\"svip\\\":70}}\",\"expand\":\"\",\"createTime\":\"Feb 1, 2025 12:00:04 AM\",\"updateTime\":\"Feb 1, 2025 12:00:04 AM\",\"creator\":\"system\",\"activityScopeList\":[{\"scopeType\":8,\"relation\":1,\"scopeValue\":\"3\"}],\"tradeType\":0}";
        ActivityConfig activityConfig6 = GsonUtil.fromJson(str6, ActivityConfig.class);
        activityConfigs.add(activityConfig1);
        activityConfigs.add(activityConfig2);
        activityConfigs.add(activityConfig3);
        activityConfigs.add(activityConfig4);
        activityConfigs.add(activityConfig5);
        activityConfigs.add(activityConfig6);
        Mockito.when(mockPromotionAdminCustomServiceProxy.queryActivityList()).thenReturn(activityConfigs);
        Mockito.when(mockPromotionAdminCustomServiceProxy.pageQuerySubsityActivityList(anyLong(), anyInt(), anyInt())).thenReturn(activityConfigs);
        Mockito.when(mockPromotionAdminCustomServiceProxy.queryCarShopActivityList()).thenReturn(activityConfigs);
        // ============================test_getActivitysByGoods2_success-测试数据结束============================

        Whitebox.setInternalState(promotionInstancePool, "promotionAdminCustomServiceProxy", mockPromotionAdminCustomServiceProxy);
        Whitebox.setInternalState(carPromotionInstancePool, "promotionAdminCustomServiceProxy", mockPromotionAdminCustomServiceProxy);
    }
}
