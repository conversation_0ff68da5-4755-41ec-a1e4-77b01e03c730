package com.xiaomi.nr.promotion.v2.activity.carsale;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xiaomi.nr.promotion.activity.carsale.CarOnsaleActivity;
import com.xiaomi.nr.promotion.api.dto.enums.SubmitTypeEnum;
import com.xiaomi.nr.promotion.api.dto.model.GoodsDto;
import com.xiaomi.nr.promotion.api.dto.model.ProductActInfo;
import com.xiaomi.nr.promotion.api.dto.model.PromotionPriceDTO;
import com.xiaomi.nr.promotion.engine.dsl.DSLEngine;
import com.xiaomi.nr.promotion.engine.dsl.DSLStream;
import com.xiaomi.nr.promotion.enums.ActivityTypeEnum;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.ActivityDetail;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.OnsalePromotionConfig;
import com.xiaomi.nr.promotion.model.promotionconfig.common.ActPriceInfo;
import com.xiaomi.nr.promotion.model.promotionconfig.common.BenefitInfo;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class CarOnsaleActivityTest extends BaseTestV2 {

    @InjectMocks
    private CarOnsaleActivity activity;

    @Mock
    private DSLEngine dslEngine;

    @Mock
    private DSLStream dslStream;

    private OnsalePromotionConfig config;
    private Map<String, ActPriceInfo> onsaleInfoMap;
    private BenefitInfo benefitInfo;

    @BeforeEach
    public void setUp() {
        // 初始化配置
        config = new OnsalePromotionConfig();
        config.setPromotionId(1L);
        config.setName("测试汽车直降活动");
        config.setUnixStartTime(1609459200L); // 2021-01-01 00:00:00
        config.setUnixEndTime(1640995199L);   // 2021-12-31 23:59:59

        // 初始化直降信息
        onsaleInfoMap = Maps.newHashMap();
        ActPriceInfo actPriceInfo1 = new ActPriceInfo();
        actPriceInfo1.setPrice(5000L);
        actPriceInfo1.setSsuId(1001L);
        onsaleInfoMap.put("1001", actPriceInfo1);

        ActPriceInfo actPriceInfo2 = new ActPriceInfo();
        actPriceInfo2.setPrice(3000L);
        actPriceInfo2.setSsuId(1002L);
        onsaleInfoMap.put("1002", actPriceInfo2);

        config.setOnsaleInfoMap(onsaleInfoMap);

        // 初始化权益信息
        benefitInfo = new BenefitInfo();
        benefitInfo.setStartTime(1609459200L);
        benefitInfo.setEndTime(1640995199L);
        config.setBenefitInfo(benefitInfo);

        // 设置交易类型
        config.setTradeType(SubmitTypeEnum.REPLACE.getCode());

        ReflectionTestUtils.setField(activity, "unixStartTime", System.currentTimeMillis() / 1000);
        ReflectionTestUtils.setField(activity, "unixEndTime", System.currentTimeMillis() / 1000 + 1000*3600*24*7);
        ReflectionTestUtils.setField(activity, "type", ActivityTypeEnum.ONSALE);
    }

    @Test
    public void test_getType() {
        // 执行测试
        PromotionToolType type = activity.getType();

        // 验证结果
        assertEquals(PromotionToolType.ONSALE, type);
    }

    @Test
    public void test_getBizPlatform() {
        // 执行测试
        BizPlatformEnum platform = activity.getBizPlatform();

        // 验证结果
        assertEquals(BizPlatformEnum.CAR, platform);
    }

    @Test
    public void test_getActivityDetail() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "onsaleInfoMap", onsaleInfoMap);
        ReflectionTestUtils.setField(activity, "promotionConfig", config);

        // 执行测试
        ActivityDetail detail = activity.getActivityDetail();

        // 验证结果
        assertNull(detail);
    }

    @Test
    public void test_getProductGoodsAct() throws BizError {
        // 准备测试数据
        Long clientId = 123L;
        String orgCode = "TEST_ORG";
        List<String> skuPackageList = Lists.newArrayList("SKU1", "SKU2");

        // 执行测试
        Map<String, ProductActInfo> result = activity.getProductGoodsAct(clientId, orgCode, skuPackageList);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void test_checkCondition_success() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "tradeType", SubmitTypeEnum.REPLACE.getCode());
        ReflectionTestUtils.setField(activity, "benefitInfo", benefitInfo);

        Map<String, String> contextParams = new HashMap<>();
        contextParams.put("order_time", "1700000000"); // 2023-11-14 12:26:40，超出权益时间范围

        // 执行测试
        boolean result = activity.checkCondition(contextParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void test_checkCondition_replace_fail_no_order_time() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "tradeType", SubmitTypeEnum.REPLACE.getCode());
        ReflectionTestUtils.setField(activity, "benefitInfo", benefitInfo);

        Map<String, String> contextParams = new HashMap<>();
        // 不设置order_time

        // 执行测试
        boolean result = activity.checkCondition(contextParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void test_checkCondition_replace_fail_null_benefit_info() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "tradeType", SubmitTypeEnum.REPLACE.getCode());
        ReflectionTestUtils.setField(activity, "benefitInfo", null);

        Map<String, String> contextParams = new HashMap<>();
        contextParams.put("order_time", "1610000000");

        // 执行测试
        boolean result = activity.checkCondition(contextParams);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void test_checkCondition_non_replace() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "tradeType", SubmitTypeEnum.NORMAL.getCode());

        Map<String, String> contextParams = new HashMap<>();
        // 不设置order_time

        // 执行测试
        boolean result = activity.checkCondition(contextParams);

        // 验证结果
        assertTrue(result);
    }

    @Test
    public void test_getGoodsPromotionPrice_success() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "onsaleInfoMap", onsaleInfoMap);

        GoodsDto goodsDto1 = new GoodsDto();
        goodsDto1.setSsuId(1001L);
        goodsDto1.setPrice(6000L);

        GoodsDto goodsDto2 = new GoodsDto();
        goodsDto2.setSsuId(1002L);
        goodsDto2.setPrice(4000L);

        List<GoodsDto> goodsList = Lists.newArrayList(goodsDto1, goodsDto2);
        Map<String, String> contextParams = new HashMap<>();

        // 执行测试
        Map<Long, PromotionPriceDTO> result = activity.getGoodsPromotionPrice(goodsList, contextParams);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个商品
        PromotionPriceDTO priceDTO1 = result.get(1001L);
        assertNotNull(priceDTO1);

        // 验证第二个商品
        PromotionPriceDTO priceDTO2 = result.get(1002L);
        assertNotNull(priceDTO2);

        // 验证商品价格已更新
        assertEquals(5000L, goodsDto1.getPrice());
        assertEquals(3000L, goodsDto2.getPrice());
    }

    @Test
    public void test_getGoodsPromotionPrice_empty_goods_list() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "onsaleInfoMap", onsaleInfoMap);

        List<GoodsDto> goodsList = Collections.emptyList();
        Map<String, String> contextParams = new HashMap<>();

        // 执行测试
        Map<Long, PromotionPriceDTO> result = activity.getGoodsPromotionPrice(goodsList, contextParams);

        // 验证结果
        assertTrue(result.isEmpty());
    }

    @Test
    public void test_getGoodsPromotionPrice_no_matching_goods() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "onsaleInfoMap", onsaleInfoMap);

        GoodsDto goodsDto = new GoodsDto();
        goodsDto.setSsuId(9999L); // 不存在的SSU
        goodsDto.setPrice(6000L);

        List<GoodsDto> goodsList = Lists.newArrayList(goodsDto);
        Map<String, String> contextParams = new HashMap<>();

        // 执行测试
        Map<Long, PromotionPriceDTO> result = activity.getGoodsPromotionPrice(goodsList, contextParams);

        // 验证结果
        assertTrue(result.isEmpty());
        assertEquals(6000L, goodsDto.getPrice()); // 价格不应被修改
    }

    @Test
    public void test_getGoodsPromotionPrice_null_price() {
        // 准备测试数据
        ReflectionTestUtils.setField(activity, "onsaleInfoMap", onsaleInfoMap);

        GoodsDto goodsDto = new GoodsDto();
        goodsDto.setSsuId(1001L);
        goodsDto.setPrice(null);

        List<GoodsDto> goodsList = Lists.newArrayList(goodsDto);
        Map<String, String> contextParams = new HashMap<>();

        // 执行测试
        Map<Long, PromotionPriceDTO> result = activity.getGoodsPromotionPrice(goodsList, contextParams);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());

//        PromotionPriceDTO priceDTO = result.get(1001L);
//        assertNotNull(priceDTO);
//        assertEquals(5000L, priceDTO.getFinalPrice());

        // 验证商品价格已更新
        assertEquals(5000L, goodsDto.getPrice());
    }
}