package com.xiaomi.nr.promotion.api.service;

import com.google.common.collect.Lists;
import com.xiaomi.micar.club.api.resp.member.MemberInfoResp;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionResponse;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Request;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionV2Response;
import com.xiaomi.nr.promotion.api.dto.enums.ChannelEnum;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.MaintenanceInfo;
import com.xiaomi.nr.promotion.bootstrap.PromotionBootstrap;
import com.xiaomi.nr.promotion.rpc.club.VipClubProxyService;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-01-10 19:21
 */
@Slf4j
@ActiveProfiles("dev")
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = {PromotionBootstrap.class, PromotionDubboServiceTest.class})
public class CarServiceCouponTest {

    private final static long USER_ID_TEST = 3150000058L;
    private final static long ORDER_ID_TEST = 1100010L;

    @Resource
    public PromotionDubboService promotionDubboService;

    @Autowired
    private VipClubProxyService vipClubProxyService;

    /**
     * 按需保养券结算测试
     */
    @Test
    public void checkoutNeedMaintenanceCouponTest() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        request.setSourceApi(1);
        request.setFromInterface(2);
        request.setChannel(ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue());
        request.setClientId(180100031052L);
        request.setUserId(USER_ID_TEST);
//        request.setCouponIds(Lists.newArrayList(1011106783L));
//        request.setCouponIds(Lists.newArrayList(1011088440L, 1011088441L));
        request.setCouponIds(Lists.newArrayList());
        request.setOrderId(ORDER_ID_TEST);
        request.setUsePoint(false);
//        request.setVid("LKBQRHRVZHMU6TJK1");
        request.setVid("1735890318928Tcgx");
        request.setWorkOrderType(1);
        request.setActivityIds(Lists.newArrayList());

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("600016462-1-1");
        cartItem1.setSsuId(600016462L);
        cartItem1.setCount(1);
        cartItem1.setBizSubType(13);
        cartItem1.setCartPrice(110L);
        cartItem1.setStandardPrice(110L);
        cartItem1.setMarketPrice(110L);
        MaintenanceInfo info1 = new MaintenanceInfo();
        info1.setPayType(1);
        info1.setWorkHour(new BigDecimal("1.1"));
        info1.setUnitPrice(100L);
        cartItem1.setMaintenanceInfo(info1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("600001042-1-3_600016462-1-1");
        cartItem2.setSsuId(600001042L);
        cartItem2.setCount(2);
        cartItem2.setBizSubType(14);
        cartItem2.setCartPrice(11400L);
        cartItem2.setStandardPrice(11400L);
        cartItem2.setMarketPrice(11400L);
        MaintenanceInfo info2 = new MaintenanceInfo();
        info2.setPayType(1);
        cartItem2.setMaintenanceInfo(info2);

        CartItem cartItem3 = new CartItem();
        cartItem3.setItemId("600001043-1-3_600016462-1-1");
        cartItem3.setSsuId(600001043L);
        cartItem3.setCount(1);
        cartItem3.setBizSubType(14);
        cartItem3.setCartPrice(11500L);
        cartItem3.setMarketPrice(11500L);
        cartItem3.setStandardPrice(11500L);
        cartItem3.setParentItemId("600016462-1-1");
        MaintenanceInfo info3 = new MaintenanceInfo();
        info3.setPayType(1);
        cartItem3.setMaintenanceInfo(info3);

        CartItem cartItem4 = new CartItem();
        cartItem4.setItemId("600001047");
        cartItem4.setSsuId(600001047L);
        cartItem4.setCount(1);
        cartItem4.setBizSubType(14);
        cartItem4.setCartPrice(11900L);
        cartItem4.setMarketPrice(11900L);
        cartItem4.setStandardPrice(11900L);
        MaintenanceInfo info4 = new MaintenanceInfo();
        info4.setPayType(1);
        cartItem4.setMaintenanceInfo(info4);

        request.setCartList(Lists.newArrayList(cartItem1, cartItem2, cartItem3, cartItem4));

        Result<CheckoutPromotionV2Response> response = promotionDubboService.checkoutPromotionV2(request);
        log.info("response:{}", GsonUtil.toJson(response));
    }

    /**
     * 耗材券结算测试
     */
    @Test
    public void checkoutConsumablesCoupon() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        request.setSourceApi(1);
        request.setFromInterface(2);
        request.setChannel(ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue());
        request.setClientId(180100031052L);
        request.setUserId(USER_ID_TEST);
        request.setCouponIds(Lists.newArrayList());
        request.setOrderId(ORDER_ID_TEST);
        request.setUsePoint(false);
//        request.setVid("LKBQRHRVZHMU6TJK1");
        request.setVid("1735890318928Tcgx");
        request.setWorkOrderType(1);
        request.setActivityIds(Lists.newArrayList());

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("600016617");
        cartItem1.setSsuId(600016617L);
        cartItem1.setCount(1);
        cartItem1.setBizSubType(13);
        cartItem1.setCartPrice(40L);
        cartItem1.setStandardPrice(40L);
        cartItem1.setMarketPrice(40L);
        MaintenanceInfo info1 = new MaintenanceInfo();
        info1.setPayType(1);
        info1.setWorkHour(new BigDecimal("0.4"));
        info1.setUnitPrice(100L);
        cartItem1.setMaintenanceInfo(info1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("600007018");
        cartItem2.setSsuId(600007018L);
        cartItem2.setCount(1);
        cartItem2.setBizSubType(14);
        cartItem2.setCartPrice(155000L);
        cartItem2.setStandardPrice(155000L);
        cartItem2.setMarketPrice(155000L);
        MaintenanceInfo info2 = new MaintenanceInfo();
        info2.setPayType(1);
        cartItem2.setMaintenanceInfo(info2);

        request.setCartList(Lists.newArrayList(cartItem1, cartItem2));
        Result<CheckoutPromotionV2Response> response = promotionDubboService.checkoutPromotionV2(request);
        log.info("response:{}", GsonUtil.toJson(response));
    }

    @Test
    public void checkoutConsumablesCoupon_submit() {
        CheckoutPromotionRequest request = new CheckoutPromotionRequest();
        request.setSourceApi(2);
        request.setFromInterface(1);
        request.setChannel(ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue());
        request.setClientId(180100031052L);
        request.setUserId(USER_ID_TEST);
        request.setCouponIds(Lists.newArrayList(1011079335L));
        request.setOrderId(ORDER_ID_TEST);
        request.setUsePoint(false);
//        request.setVid("LKBQRHRVZHMU6TJK1");
        request.setVid("1735890318928Tcgx");
        request.setWorkOrderType(1);
        request.setActivityIds(Lists.newArrayList());

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("600001030");
        cartItem1.setSsuId(600001030L);
        cartItem1.setCount(1);
        cartItem1.setBizSubType(14);
        cartItem1.setCartPrice(10200L);
        cartItem1.setStandardPrice(10200L);
        cartItem1.setMarketPrice(10200L);
        MaintenanceInfo info1 = new MaintenanceInfo();
        info1.setPayType(1);
        cartItem1.setMaintenanceInfo(info1);

        request.setCartList(Lists.newArrayList(cartItem1));
        Result<CheckoutPromotionResponse> response = promotionDubboService.checkoutPromotion(request);
        log.info("response:{}", GsonUtil.toJson(response));
    }

    @Test
    public void getVipInfoTest() throws BizError {
        MemberInfoResp resp = vipClubProxyService.getUserMemberInfo(3150448375L, null);
        log.info("resp:{}", GsonUtil.toJson(resp));
    }

    @Test
    public void checkoutPromotionV2Test_maintenanceDiscount() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        request.setSourceApi(1);
        request.setFromInterface(2);
        request.setChannel(ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue());
        request.setClientId(180100031052L);
        request.setUserId(3150071208L);
        request.setCouponIds(Lists.newArrayList());
        request.setActivityIds(Lists.newArrayList(21534072L));
        request.setOrderId(0L);
        request.setUsePoint(false);
        request.setVid("LKBQLZAVPKCHVW6C3");
        request.setWorkOrderType(1);
        request.setIsExchange(false);

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("600016675-1-1");
        cartItem1.setSsuId(600016675L);
        cartItem1.setSku("600016675");
        cartItem1.setCount(1);
        cartItem1.setBizSubType(13);
        cartItem1.setCartPrice(80L);
        cartItem1.setStandardPrice(80L);
        cartItem1.setMarketPrice(80L);
        MaintenanceInfo info1 = new MaintenanceInfo();
        info1.setPayType(1);
        info1.setWorkHour(new BigDecimal("0.8"));
        info1.setUnitPrice(100L);
        cartItem1.setMaintenanceInfo(info1);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("600016458-1-1");
        cartItem2.setSsuId(600016458L);
        cartItem2.setSku("600016458");
        cartItem2.setCount(1);
        cartItem2.setBizSubType(13);
        cartItem2.setCartPrice(70L);
        cartItem2.setStandardPrice(70L);
        cartItem2.setMarketPrice(70L);
        MaintenanceInfo info2 = new MaintenanceInfo();
        info2.setPayType(1);
        info2.setUnitPrice(100L);
        info2.setWorkHour(new BigDecimal("0.7"));
        cartItem2.setMaintenanceInfo(info2);

        CartItem cartItem3 = new CartItem();
        cartItem3.setItemId("600003452-1-1");
        cartItem3.setSsuId(600003452L);
        cartItem3.setSku("600003452");
        cartItem3.setCount(1);
        cartItem3.setBizSubType(14);
        cartItem3.setCartPrice(0L);
        cartItem3.setMarketPrice(0L);
        cartItem3.setStandardPrice(0L);
        cartItem3.setParentItemId("600016675-1-1");
        MaintenanceInfo info3 = new MaintenanceInfo();
        info3.setPayType(1);
        cartItem3.setMaintenanceInfo(info3);

        CartItem cartItem4 = new CartItem();
        cartItem4.setItemId("600001047");
        cartItem4.setSsuId(600001047L);
        cartItem4.setCount(1);
        cartItem4.setBizSubType(14);
        cartItem4.setCartPrice(11900L);
        cartItem4.setMarketPrice(11900L);
        cartItem4.setStandardPrice(11900L);
        MaintenanceInfo info4 = new MaintenanceInfo();
        info4.setPayType(1);
        cartItem4.setMaintenanceInfo(info4);

        request.setCartList(Lists.newArrayList(cartItem1, cartItem2, cartItem3));

        Result<CheckoutPromotionV2Response> response = promotionDubboService.checkoutPromotionV2(request);
        log.info("response:{}", GsonUtil.toJson(response));
    }

    @Test
    public void checkoutPromotionV2Test_maintenanceDiscount1() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        request.setSourceApi(1);
        request.setFromInterface(2);
        request.setChannel(ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue());
        request.setClientId(180100031052L);
        request.setUserId(3150444793L);
        request.setCouponIds(Lists.newArrayList());
        request.setActivityIds(Lists.newArrayList());
        request.setOrderId(0L);
        request.setUsePoint(false);
        request.setVid("LKBQPFTWBRLBAXVH0");
        request.setWorkOrderType(1);
        request.setIsExchange(false);
        request.setWorkOrderType(1);
        request.setIsFirstCarOwner(true);

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("600020260-1-1");
        cartItem1.setSsuId(600020260L);
        cartItem1.setSku("600020260");
        cartItem1.setCount(1);
        cartItem1.setBizSubType(13);
        cartItem1.setCartPrice(50L);
        cartItem1.setStandardPrice(50L);
        cartItem1.setMarketPrice(50L);
        MaintenanceInfo info1 = new MaintenanceInfo();
        info1.setPayType(1);
        info1.setWorkHour(new BigDecimal("0.5"));
        info1.setUnitPrice(100L);
        cartItem1.setMaintenanceInfo(info1);

        request.setCartList(Lists.newArrayList(cartItem1));

        Result<CheckoutPromotionV2Response> response = promotionDubboService.checkoutPromotionV2(request);
        log.info("response:{}", GsonUtil.toJson(response));
    }

    /**
     * 耗材券结算测试
     */
    @Test
    public void checkoutConsumablesCoupon_v2() {
        CheckoutPromotionV2Request request = new CheckoutPromotionV2Request();
        request.setGetCouponList(true);
        request.setChannel(ChannelEnum.CAR_MAINTENANCE_REPAIR.getValue());
        request.setClientId(180100031052L);
        request.setUserId(USER_ID_TEST);
        request.setCouponIds(Lists.newArrayList(1011143885L, 1011182598L, 1011088440L));
        request.setOrderId(ORDER_ID_TEST);
        request.setUsePoint(false);
//        request.setVid("LKBQRHRVZHMU6TJK1");
        request.setVid("1735890318928Tcgx");
        request.setWorkOrderType(1);
        request.setActivityIds(Lists.newArrayList());

        CartItem cartItem1 = new CartItem();
        cartItem1.setItemId("600006536-1");
        cartItem1.setSsuId(600006536L);
        cartItem1.setCount(5);
        cartItem1.setBizSubType(14);
        cartItem1.setCartPrice(100L);
        cartItem1.setStandardPrice(100L);
        cartItem1.setMarketPrice(100L);
        MaintenanceInfo info1 = new MaintenanceInfo();
        info1.setPayType(1);
        cartItem1.setMaintenanceInfo(info1);

//        CartItem cartItem3 = new CartItem();
//        cartItem3.setItemId("600006536-2");
//        cartItem3.setSsuId(600006536L);
//        cartItem3.setCount(1);
//        cartItem3.setBizSubType(14);
//        cartItem3.setCartPrice(100L);
//        cartItem3.setStandardPrice(100L);
//        cartItem3.setMarketPrice(100L);
//        MaintenanceInfo info2 = new MaintenanceInfo();
//        info2.setPayType(1);
//        cartItem3.setMaintenanceInfo(info2);

        CartItem cartItem2 = new CartItem();
        cartItem2.setItemId("600007018");
        cartItem2.setSsuId(600007018L);
        cartItem2.setCount(1);
        cartItem2.setBizSubType(14);
        cartItem2.setCartPrice(200L);
        cartItem2.setStandardPrice(200L);
        cartItem2.setMarketPrice(200L);
        MaintenanceInfo info2 = new MaintenanceInfo();
        info2.setPayType(1);
        cartItem2.setMaintenanceInfo(info2);

        CartItem cartItem4 = new CartItem();
        cartItem4.setItemId("600008269");
        cartItem4.setSsuId(600008269L);
        cartItem4.setCount(1);
        cartItem4.setBizSubType(14);
        cartItem4.setCartPrice(100L);
        cartItem4.setStandardPrice(100L);
        cartItem4.setMarketPrice(100L);
        MaintenanceInfo info4 = new MaintenanceInfo();
        info4.setPayType(1);
        cartItem4.setMaintenanceInfo(info4);

        CartItem cartItem5 = new CartItem();
        cartItem5.setItemId("600016462");
        cartItem5.setSsuId(600016462L);
        cartItem5.setCount(1);
        cartItem5.setBizSubType(13);
        cartItem5.setCartPrice(110L);
        cartItem5.setStandardPrice(110L);
        cartItem5.setMarketPrice(110L);
        MaintenanceInfo info5 = new MaintenanceInfo();
        info5.setWorkHour(new BigDecimal("1.1"));
        info5.setUnitPrice(100L);
        info5.setPayType(1);
        cartItem5.setMaintenanceInfo(info5);

        request.setCartList(Lists.newArrayList(cartItem1, cartItem2, cartItem4, cartItem5));
        log.info("=======================================================================");
        Result<CheckoutPromotionV2Response> response = promotionDubboService.checkoutPromotionV2(request);
        log.info("response:{}", GsonUtil.toJson(response));
        log.info("=======================================================================");
    }
}
