package com.xiaomi.nr.promotion.v2.componet.condition.carmaintenance;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xiaomi.micar.club.api.model.member.MemberInfo;
import com.xiaomi.nr.promotion.api.dto.CheckoutPromotionRequest;
import com.xiaomi.nr.promotion.api.dto.model.CartItem;
import com.xiaomi.nr.promotion.api.dto.model.MaintenanceInfo;
import com.xiaomi.nr.promotion.componet.condition.carmaintenance.MaintenanceDiscountCondition;
import com.xiaomi.nr.promotion.dao.mysql.promotionuser.PromotionUserActivityCountMapper;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.UserActivityCount;
import com.xiaomi.nr.promotion.enums.BizPlatformEnum;
import com.xiaomi.nr.promotion.model.LocalContext;
import com.xiaomi.nr.promotion.model.PromotionToolType;
import com.xiaomi.nr.promotion.model.promotionconfig.MaintenanceDiscountPromotionConfig;
import com.xiaomi.nr.promotion.resource.external.CarVidVipExternalProvider;
import com.xiaomi.nr.promotion.resource.external.ExternalDataProvider;
import com.xiaomi.nr.promotion.resource.external.ResourceExtType;
import com.xiaomi.nr.promotion.v2.BaseTestV2;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

//@ExtendWith(MockitoExtension.class)
public class MaintenanceDiscountConditionTest extends BaseTestV2 {

    @InjectMocks
    private MaintenanceDiscountCondition condition;

    @Mock
    private PromotionUserActivityCountMapper userActivityCountMapper;

    private CheckoutPromotionRequest request;
    private LocalContext context;
    private MaintenanceDiscountPromotionConfig config;

    @BeforeEach
    public void setUp() {
        request = new CheckoutPromotionRequest();
        request.setUserId(123L);
        request.setWorkOrderType(1);
        request.setVid("test_vid");

        context = new LocalContext();
        context.setBizPlatform(BizPlatformEnum.MAINTENANCE_REPAIR);

        config = new MaintenanceDiscountPromotionConfig();
        config.setPromotionId(1L);
        config.setPromotionType(PromotionToolType.MAINTENANCE_REPAIR_DISCOUNT);
        config.setCarIdentityId(1);
        config.setUserJoinNumLimit(2);
        Set<Integer> supportWorkOrderTypes = Sets.newHashSet(1, 2);
        config.setSupportWorkOrderTypes(supportWorkOrderTypes);
        config.setInvalidGoods(Lists.newArrayList(999L));
    }

    @Test
    public void test_isSatisfied_success() throws BizError {
        // 准备测试数据
        CartItem cartItem = new CartItem();
        cartItem.setSsuId(1L);
        cartItem.setItemId("1");
        cartItem.setOriginalCartPrice(100L);
        MaintenanceInfo maintenanceInfo = new MaintenanceInfo();
        maintenanceInfo.setPayType(1);
        cartItem.setMaintenanceInfo(maintenanceInfo);
        request.setCartList(Lists.newArrayList(cartItem));

        // Mock VIP会员信息
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setLevel(1);
        Map<ResourceExtType, ExternalDataProvider<?>> externalDataMap = new HashMap<>();
        CarVidVipExternalProvider carVidVipExternalProvider = mock(CarVidVipExternalProvider.class);
        when(carVidVipExternalProvider.getData()).thenReturn(memberInfo);
        externalDataMap.put(ResourceExtType.VID_ULTRA_VIP_MEMBER, carVidVipExternalProvider);
        context.setExternalDataMap(externalDataMap);

        // Mock用户参与次数
        UserActivityCount userActivityCount = new UserActivityCount();
        userActivityCount.setNum(1);
        when(userActivityCountMapper.getByUserIdAndPromotionId(anyLong(), anyLong())).thenReturn(userActivityCount);

        condition.loadConfig(config);
        assertTrue(condition.isSatisfied(request, context));
    }

    @Test
    public void test_isSatisfied_fail() throws BizError {
        condition.loadConfig(config);

        // 1. 业务平台不匹配
        context.setBizPlatform(BizPlatformEnum.CAR);
        assertFalse(condition.isSatisfied(request, context));
        context.setBizPlatform(BizPlatformEnum.MAINTENANCE_REPAIR);

        // 2. 工单类型不匹配
        request.setWorkOrderType(999);
        assertFalse(condition.isSatisfied(request, context));
        request.setWorkOrderType(1);

        // 3. VIP身份不匹配
        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setLevel(2); // 不同的VIP等级
        Map<ResourceExtType, ExternalDataProvider<?>> externalDataMap = new HashMap<>();
        CarVidVipExternalProvider carVidVipExternalProvider = mock(CarVidVipExternalProvider.class);
        when(carVidVipExternalProvider.getData()).thenReturn(memberInfo);
        externalDataMap.put(ResourceExtType.VID_ULTRA_VIP_MEMBER, carVidVipExternalProvider);
        context.setExternalDataMap(externalDataMap);
        assertFalse(condition.isSatisfied(request, context));
        memberInfo.setLevel(1);

        // 4. 用户参与次数超限
        UserActivityCount userActivityCount = new UserActivityCount();
        userActivityCount.setNum(3); // 超过限制次数2
        when(userActivityCountMapper.getByUserIdAndPromotionId(anyLong(), anyLong())).thenReturn(userActivityCount);
        assertFalse(condition.isSatisfied(request, context));

        // 5. 商品不满足条件
        CartItem cartItem = new CartItem();
        cartItem.setSsuId(999L); // 黑名单商品
        cartItem.setItemId("999");
        cartItem.setOriginalCartPrice(100L);
        MaintenanceInfo maintenanceInfo = new MaintenanceInfo();
        maintenanceInfo.setPayType(1);
        cartItem.setMaintenanceInfo(maintenanceInfo);
        request.setCartList(Lists.newArrayList(cartItem));
        assertFalse(condition.isSatisfied(request, context));
    }

    @Test
    public void test_loadConfig_fail() {
        condition.loadConfig(null);
        assertNull(ReflectionTestUtils.getField(condition, "promotionId"));
    }
}
