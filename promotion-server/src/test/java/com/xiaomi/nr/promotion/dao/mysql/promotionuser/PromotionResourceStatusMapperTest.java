package com.xiaomi.nr.promotion.dao.mysql.promotionuser;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.PromotionResourceStatus;
import com.xiaomi.nr.promotion.resource.model.ResourceStatus;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Instant;
import java.util.List;

/**
 * 资源状态测试
 *
 * <AUTHOR>
 * @date 2021/5/6
 */
public class PromotionResourceStatusMapperTest extends BaseTest {

    @Autowired
    private PromotionResourceStatusMapper promotionResourceStatusMapper;

    @Test
    public void testInsertStatus() {
        PromotionResourceStatus status = new PromotionResourceStatus();
        status.setOrderId(187L);
        status.setUid(8989L);
        status.setStatus(ResourceStatus.INIT.getValue());
        status.setUpdateTime(Instant.now().toEpochMilli());
        status.setCreateTime(Instant.now().toEpochMilli());
        int count = promotionResourceStatusMapper.insertStatus(status);
        Assert.assertEquals(1, count);
    }

    @Test
    public void testUpdateStatus() {
        int count = promotionResourceStatusMapper.updateStatus(187L, ResourceStatus.LOCKED.getValue(), Instant.now().toEpochMilli());
        Assert.assertEquals(1, count);
    }

    @Test
    public void testGetStatusByOrderId() {
        PromotionResourceStatus status = promotionResourceStatusMapper.getStatusByOrderId(187L);
        Assert.assertNotNull(status);
    }

    @Test
    public void testDeletePreSaleStatus() {
        int count = promotionResourceStatusMapper.deletePreSaleStatus(187L);
        Assert.assertEquals(0, count);
    }

    @Test
    public void testDeleteStatusByOrderId() {
        int count = promotionResourceStatusMapper.deletePreSaleStatus(187L);
        Assert.assertEquals(0, count);
    }

    @Test
    public void testUpdateUnableRemedialRecord() {
        int count = promotionResourceStatusMapper.deletePreSaleStatus(187L);
        Assert.assertEquals(0, count);
    }

    @Test
    public void testGetTimeoutStatus() {
        List<PromotionResourceStatus> statusList = promotionResourceStatusMapper.getTimeoutStatus(Instant.now().getEpochSecond());
        Assert.assertNotEquals(0, statusList.size());
    }
}