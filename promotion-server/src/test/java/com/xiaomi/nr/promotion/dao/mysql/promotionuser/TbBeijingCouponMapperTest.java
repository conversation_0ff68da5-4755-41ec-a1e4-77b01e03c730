package com.xiaomi.nr.promotion.dao.mysql.promotionuser;

import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.entity.mysql.promotionuser.TbBeijingCoupon;
import com.xiaomi.nr.promotion.util.GsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

import static com.xiaomi.nr.promotion.constant.EcardConstant.TYPE_ID_BEIJING_COUPON;

/**
 * <AUTHOR>
 * @date
 */
public class TbBeijingCouponMapperTest extends BaseTest {

    @Autowired
    private TbBeijingCouponMapper tbBeijingCouponMapper;

    @Test
    public void testGetBeijingCouponIds() {
        List<TbBeijingCoupon> couponList = tbBeijingCouponMapper.getBeijingCouponIds(3150075477L, Arrays.asList(TYPE_ID_BEIJING_COUPON));
        System.out.println(GsonUtil.toJson(couponList));
    }
}