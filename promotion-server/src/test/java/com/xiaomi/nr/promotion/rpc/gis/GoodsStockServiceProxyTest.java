package com.xiaomi.nr.promotion.rpc.gis;

import com.google.common.collect.Sets;
import com.xiaomi.goods.gis.dto.stock.GiftStockRespParam;
import com.xiaomi.goods.gis.dto.stock.Region;
import com.xiaomi.nr.promotion.BaseTest;
import com.xiaomi.nr.promotion.util.GsonUtil;
import com.xiaomi.youpin.infra.rpc.errors.BizError;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 商品库存信息测试
 *
 * <AUTHOR>
 * @date 2021/9/9
 */
public class GoodsStockServiceProxyTest extends BaseTest {
    @Autowired
    private GoodsStockServiceProxy goodsStockServiceProxy;

    @Test
    public void getGiftStockAsyncTest() throws InterruptedException, ExecutionException, TimeoutException {
        Long masterId = 2183600016L;
        Set<Long> idList = Sets.newHashSet(2183600017L, 2183600018L, 2183600018L);
        Region region = new Region();
        region.setProvince(1);
        region.setCity(10);
        region.setDistrict(112);
        region.setArea(0);
        ListenableFuture<Map<Long, Long>> stockMapFuture = goodsStockServiceProxy.getGiftStockAsync(masterId, "goods", idList, "MI0101", region, false, 0);
        System.out.println(GsonUtil.toJson(stockMapFuture.get(3, TimeUnit.SECONDS)));
    }

    @Test
    public void getGiftStockTest() throws BizError {
        Long masterId = 2183600016L;
        Set<Long> idList = Sets.newHashSet(2183600017L, 2183600018L, 2183600018L);
        Region region = new Region();
        region.setProvince(1);
        region.setCity(10);
        region.setDistrict(112);
        region.setArea(0);
        List<GiftStockRespParam> respParamList = goodsStockServiceProxy.getGiftStock(masterId, "goods", idList, "MI0101", region, false, 0);
        System.out.println(GsonUtil.toJson(respParamList));
    }
}