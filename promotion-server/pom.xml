<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>promotion</artifactId>
        <groupId>com.xiaomi.nr</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>promotion-server</artifactId>

    <properties>
    	<maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <artifactId>promotion-service</artifactId>
            <groupId>com.xiaomi.nr</groupId>
            <version>${project.version}</version>
        </dependency>

        <!--阿里巴巴nacos-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo-registry-nacos</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.spring</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-spring-context</artifactId>
            <version>${com.alibaba.nacos.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-context</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-context-support</artifactId>
                    <groupId>com.alibaba.spring</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- spring boot test-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!--   rocketMq-->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.mit</groupId>
            <artifactId>unittest-tool</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>5.11.4</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.Filter</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources/META-INF</directory>
                <filtering>true</filtering>
                <includes>
                    <include>app.properties</include>
                </includes>
                <targetPath>META-INF/</targetPath>
            </resource>
        </resources>

        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.Filter</include>
                </includes>
            </testResource>
        </testResources>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.xiaomi.nr.promotion.bootstrap.PromotionBootstrap</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

    </build>

    <profiles>

        <profile>
            <id>dev</id>
            <activation>
                <property>
                    <name>dev</name>
                </property>
            </activation>
            <properties>
                <profile_name>dev</profile_name>
                <zookeeper_host>staging</zookeeper_host>
                <app_nacos>*************:80</app_nacos>
            </properties>
        </profile>

        <profile>
            <id>staging</id>
            <activation>
                <property>
                    <name>staging</name>
                    <value>true</value>
                </property>
            </activation>
            <properties>
                <profile_name>staging</profile_name>
                <zookeeper_host>staging</zookeeper_host>
                <app_nacos>*************:80</app_nacos>
            </properties>
        </profile>

        <profile>
            <id>trade</id>
            <activation>
                <property>
                    <name>trade</name>
                </property>
            </activation>
            <properties>
                <profile_name>trade</profile_name>
                <zookeeper_host>c4</zookeeper_host>
                <app_nacos>nacos.systech.b2c.srv:80</app_nacos>
            </properties>
        </profile>

        <profile>
            <id>car</id>
            <activation>
                <property>
                    <name>car</name>
                </property>
            </activation>
            <properties>
                <profile_name>car</profile_name>
                <zookeeper_host>c4</zookeeper_host>
                <app_nacos>nacos.systech.b2c.srv:80</app_nacos>
            </properties>
        </profile>

        <profile>
            <id>carshop</id>
            <activation>
                <property>
                    <name>carshop</name>
                </property>
            </activation>
            <properties>
                <profile_name>carshop</profile_name>
                <zookeeper_host>c4</zookeeper_host>
                <app_nacos>nacos.systech.b2c.srv:80</app_nacos>
            </properties>
        </profile>

        <profile>
            <id>c3</id>
            <activation>
                <property>
                    <name>c3</name>
                </property>
            </activation>
            <properties>
                <profile_name>c3</profile_name>
                <zookeeper_host>c4</zookeeper_host>
                <app_nacos>nacos.systech.b2c.srv:80</app_nacos>
            </properties>
        </profile>

        <profile>
            <id>c4</id>
            <activation>
                <property>
                    <name>c4</name>
                </property>
            </activation>
            <properties>
                <profile_name>c4</profile_name>
                <zookeeper_host>c4</zookeeper_host>
                <app_nacos>nacos.systech.b2c.srv:80</app_nacos>
            </properties>
        </profile>

        <profile>
            <id>offline</id>
            <activation>
                <property>
                    <name>offline</name>
                </property>
            </activation>
            <properties>
                <profile_name>offline</profile_name>
                <zookeeper_host>c4</zookeeper_host>
                <app_nacos>nacos.systech.b2c.srv:80</app_nacos>
            </properties>
        </profile>

        <profile>
            <id>preview</id>
            <activation>
                <property>
                    <name>preview</name>
                </property>
            </activation>
            <properties>
                <profile_name>preview</profile_name>
                <zookeeper_host>c4</zookeeper_host>
                <app_nacos>nacos.systech.b2c.srv:80</app_nacos>
            </properties>
        </profile>

        <profile>
            <id>ut</id>
            <activation>
                <property>
                    <name>ut</name>
                </property>
            </activation>
            <properties>
                <profile_name>ut</profile_name>
                <zookeeper_host>staging</zookeeper_host>
                <app_nacos>*************:80</app_nacos>
            </properties>
        </profile>
    </profiles>
</project>